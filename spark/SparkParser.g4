parser grammar SparkParser;

options { tokenVocab = SparkLexer; }

@parser::members {
  /**
   * When false, INTERSECT is given the greater precedence over the other set
   * operations (UNION, EXCEPT and MINUS) as per the SQL standard.
   */
  public boolean legacy_setops_precedence_enabled = false;

  /**
   * When false, a literal with an exponent would be converted into
   * double type rather than decimal type.
   */
  public boolean legacy_exponent_literal_as_decimal_enabled = false;

  /**
   * When true, the behavior of keywords follows ANSI SQL standard.
   */
  public boolean SQL_standard_keyword_behavior = false;

  /**
   * When true, double quoted literals are identifiers rather than STRINGs.
   */
  public boolean double_quoted_identifiers = false;
}

root
    : (
    //DDL Statements
      alterDatabase
    | alterTable
    | alterView
    | commentDatabase
    | commentTable
    | createDatabase
    | createFunction
    | createIndex
    | createTable
    | createView
    | declareVariable
    | dropDatabase
    | dropFunction
    | dropIndex
    | dropTable
    | dropVariable
    | dropView
    | repairTable
    | replaceTable
    | truncateTable
    | useDatabase
    //DML Statements
    | deleteFromTable
    | insertTable
    | insertOverwriteDirectory
    | load
    | mergeIntoTable
    | multiInsertQuery
    | updateTable
    //Data Retrieval Statements
    | query
    | explain
    //Auxiliary Statements
    | addFile
    | addJar
    | analyzeTable
    | cacheTable
    | clearCache
    | describeDatabase
    | describeFunction
    | describeQuery
    | describeTable
    | listFile
    | listJar
    | refresh
    | refreshTable
    | refreshFunction
    | setReset
    | showCatalogs
    | showColumns
    | showCreateTable
    | showCurrentNamespace
    | showDatabases
    | showFunctions
    | showPartitions
    | showProcedures
    | showTableExtended
    | showTables
    | showTblProperties
    | showViews
    | uncacheTable
    //other
    | executeImmediate
    | call
    | unsupportedHiveNativeCommands
    //compound
    | singleCompoundStatement
    ) SEMI_? SLASH_? EOF
    ;

alterDatabase
    : ALTER namespace db=identifierReference
         (
          SET (DBPROPERTIES | PROPERTIES) propertyList
         |SET locationSpec
         )
    ;

alterTable
    //rename table
    : ALTER TABLE tableIdentifier RENAME TO tableIdentifier                                                             #alterTableRenameTable
    | ALTER TABLE tableIdentifier partitionSpec RENAME TO partitionSpec                                                 #alterTableRenameTable
    //add columns
    | ALTER TABLE tableIdentifier ADD (COLUMNS|COLUMN) LP_ columns=qualifiedColTypeWithPositionList RP_                 #alterTableAddColumns
    | ALTER TABLE tableIdentifier ADD (COLUMNS|COLUMN) columns=qualifiedColTypeWithPositionList                         #alterTableAddColumns
    //drop columns
    | ALTER TABLE tableIdentifier DROP (COLUMNS|COLUMN) (IF EXISTS)? columns=multipartIdentifierList                    #alterTableDropColumns
    | ALTER TABLE tableIdentifier DROP (COLUMNS|COLUMN) (IF EXISTS)? LP_ columns=multipartIdentifierList RP_            #alterTableDropColumns
    //rename column
    | ALTER TABLE tableIdentifier RENAME COLUMN from=multipartIdentifier TO to=errorCapturingIdentifier                 #alterTableRenameColumn
    //alter or change column
    | ALTER TABLE tableIdentifier (ALTER | CHANGE) COLUMN? columns=alterColumnSpecList                                  #alterTableAlterOrChangeColumn
    //replace columns
    | ALTER TABLE tableIdentifier partitionSpec? REPLACE COLUMNS (LP_ columns=qualifiedColTypeWithPositionList RP_ | columns=qualifiedColTypeWithPositionList)       #alterTableReplaceColumns
    //add partition
    | ALTER TABLE tableIdentifier ADD (IF NOT EXISTS)? (LP_ partitionSpecLocation+ RP_ | partitionSpecLocation+)         #alterTableAddPartition
    //drop partition
    | ALTER TABLE tableIdentifier DROP (IF EXISTS)? partitionSpec (COMMA_ partitionSpec)* PURGE?                         #alterTableDropPartition
    //SET TABLE PROPERTIES
    | ALTER TABLE tableIdentifier SET TBLPROPERTIES propertyList                                                        #alterTableSetTableProperties
    | ALTER TABLE tableIdentifier UNSET TBLPROPERTIES (IF EXISTS)?  propertyList                                        #alterTableSetTableProperties
    //SET SERDE
    | ALTER TABLE tableIdentifier (partitionSpec)? SET SERDEPROPERTIES propertyList                                     #alterTableSetSerDe
    | ALTER TABLE tableIdentifier (partitionSpec)? SET SERDE stringLit (WITH SERDEPROPERTIES propertyList)?             #alterTableSetSerDe
    //SET LOCATION And SET FILE FORMAT
    | ALTER TABLE tableIdentifier (partitionSpec)? SET FILEFORMAT fileFormat                                            #alterTableSetFileFormat
    | ALTER TABLE tableIdentifier (partitionSpec)? SET locationSpec                                                     #alterTableSetLocation
    //RECOVER PARTITIONS
    | ALTER TABLE tableIdentifier RECOVER PARTITIONS                                                                    #alterTableRecoverPartitions
    //other
    | ALTER TABLE tableIdentifier (clusterBySpec | CLUSTER BY NONE)                                                     #alterTableClusterBy
    | ALTER TABLE tableIdentifier collationSpec                                                                         #alterTableCollation
    | ALTER TABLE tableIdentifier ADD tableConstraintDefinition                                                         #alterTableAddConstraint
    | ALTER TABLE tableIdentifier DROP CONSTRAINT (IF EXISTS)? name=identifier (RESTRICT | CASCADE)?                    #alterTableDropConstraint
    ;

alterView
    //rename view
    : ALTER VIEW from=identifierReference RENAME TO to=multipartIdentifier                                              #alterViewRenameView
    //SET View Properties
    | ALTER VIEW identifierReference SET TBLPROPERTIES propertyList                                                     #alterViewSetProperties
    //UNSET View Properties
    | ALTER VIEW identifierReference UNSET TBLPROPERTIES (IF EXISTS)? propertyList                                      #alterViewUnsetProperties
    //ALTER View AS SELECT
    | ALTER VIEW identifierReference AS query                                                      #alterViewAsSelect
    ;

createDatabase
    : CREATE namespace (IF NOT EXISTS)? identifierReference
        (commentSpec |
        locationSpec |
        (WITH (DBPROPERTIES | PROPERTIES) propertyList))*
    ;

createFunction
    : CREATE (OR REPLACE)? TEMPORARY? FUNCTION (IF errorCapturingNot EXISTS)?
              identifierReference AS className=stringLit
              (USING resource (COMMA_ resource)*)?                            #createFunctionCore
    | CREATE (OR REPLACE)? TEMPORARY? FUNCTION (IF errorCapturingNot EXISTS)?
              identifierReference LP_ parameters=colDefinitionList? RP_
              (RETURNS (dataType | TABLE LP_ returnParams=colTypeList RP_))?
              routineCharacteristics
              RETURN (query | expression)                                    #createUserDefinedFunction
    ;

createTable
    : createTableHeader (LP_ tableElementList RP_)? tableProvider?
              createTableClauses
              (AS? query)?                                                   #createTableUsing
    | CREATE TABLE (IF errorCapturingNot EXISTS)? target=tableIdentifier
              LIKE source=tableIdentifier
              (tableProvider |
              rowFormat |
              createFileFormat |
              locationSpec |
              (TBLPROPERTIES tableProps=propertyList))*                      #createTableLike
    ;

createView
    : CREATE (OR REPLACE)? (GLOBAL? TEMPORARY)?
              VIEW (IF errorCapturingNot EXISTS)? identifierReference
              identifierCommentList?
              (commentSpec |
               schemaBinding |
               collationSpec |
               (PARTITIONED ON identifierList) |
               (TBLPROPERTIES propertyList))*
              AS query                                                          #createViewCore
    | CREATE (OR REPLACE)? GLOBAL? TEMPORARY VIEW
              tableIdentifier (LP_ colTypeList RP_)? tableProvider
              (OPTIONS propertyList)?                                           #createTempViewUsing
    ;

dropDatabase
    : DROP namespace (IF EXISTS)? identifierReference (RESTRICT | CASCADE)?
    ;

dropFunction
    : DROP TEMPORARY? FUNCTION (IF EXISTS)? identifierReference
    ;

dropTable
    : DROP TABLE (IF EXISTS)? identifierReference PURGE?
    ;

dropView
    : DROP VIEW (IF EXISTS)? identifierReference
    ;

repairTable
    : (MSCK)? REPAIR TABLE identifierReference (option=(ADD|DROP|SYNC) PARTITIONS)?
    ;

truncateTable
    : TRUNCATE TABLE identifierReference partitionSpec?
    ;

useDatabase
    : USE namespace? identifierReference
    ;

insertTable
    : ctes? INSERT (INTO | OVERWRITE)? (TABLE)? tableIdentifier partitionSpec? (LP_ qualifiedNameList RP_)?
        (valuesClause | query)
    | ctes? INSERT INTO (TABLE)? tableIdentifier REPLACE WHERE booleanExpression query
    ;

insertOverwriteDirectory
    : ctes? INSERT OVERWRITE LOCAL? DIRECTORY path=stringLit rowFormat? createFileFormat?  (valuesClause | query)
    | ctes? INSERT OVERWRITE LOCAL? DIRECTORY (path=stringLit)? tableProvider (OPTIONS options=propertyList)? (valuesClause | query)
    ;

load
    : LOAD DATA LOCAL? INPATH path=stringLit OVERWRITE? INTO TABLE
              identifierReference partitionSpec?
    ;

explain
    : EXPLAIN (LOGICAL | FORMATTED | EXTENDED | CODEGEN | COST)?
              (root | setResetStatement)
    ;

addFile
    : ADD (FILE | FILES) (fileOrDirectoryName)+
    ;

addJar
    : ADD (JAR | JARS) (fileOrDirectoryName)+
    ;

analyzeTable
    : ANALYZE TABLE identifierReference partitionSpec? COMPUTE STATISTICS
              (identifier | FOR COLUMNS identifierSeq | FOR ALL COLUMNS)?
    | ANALYZE TABLES ((FROM | IN) identifierReference)? COMPUTE STATISTICS
              (identifier)?
    ;

cacheTable
    : CACHE LAZY? TABLE identifierReference
              (OPTIONS options=propertyList)? (AS? query)?
    ;

clearCache
    : CLEAR CACHE
    ;

describeDatabase
    : (DESC | DESCRIBE) namespace EXTENDED?
              identifierReference
    ;

describeFunction
    : (DESC | DESCRIBE) FUNCTION EXTENDED? describeFuncName
    ;

describeQuery
    : (DESC | DESCRIBE) QUERY? query
    ;

describeTable
    : (DESC | DESCRIBE) TABLE? option=(EXTENDED | FORMATTED)?
              identifierReference partitionSpec? describeColName? (AS JSON)?
    ;

listFile
    : LIST (FILE | FILES) (fileOrDirectoryName)*
    ;

listJar
    : LIST (JAR | JARS) (fileOrDirectoryName)*
    ;

refresh
    : REFRESH (identifier | stringLit)
    ;

refreshTable
    : REFRESH (TABLE)? tableIdentifier
    ;

refreshFunction
    : REFRESH FUNCTION identifierReference
    ;

//reset
//    : RESET configKey
//    | RESET .*?
//    ;
//
//set
//    : SET
//    | SET MINUS_ identifier
//    | SET propertyKey (EQ_ propertyValue)?
//    | SET CATALOG catalogIdentifierReference
//    ;

setReset
    : SET
    | SET MINUS_ identifier
    | SET propertyKey (EQ_ propertyValue)?
    | SET CATALOG catalogIdentifierReference
    | RESET configKey
    | RESET .*?
    ;

showColumns
    : SHOW COLUMNS (FROM | IN) table=identifierReference
              ((FROM | IN) ns=multipartIdentifier)?
    ;

showCreateTable
    : SHOW CREATE TABLE identifierReference (AS SERDE)?
    ;

showDatabases
    : SHOW namespaces ((FROM | IN) multipartIdentifier)?
              (LIKE? pattern=stringLit)?
    ;

showFunctions
    : SHOW identifier? FUNCTIONS ((FROM | IN) ns=identifierReference)?
              (LIKE? (legacy=multipartIdentifier | pattern=stringLit))?
    ;

showPartitions
    : SHOW PARTITIONS identifierReference partitionSpec?
    ;

showTableExtended
    : SHOW TABLE EXTENDED ((FROM | IN) ns=identifierReference)?
              LIKE pattern=stringLit partitionSpec?
    ;

showTables
    : SHOW TABLES ((FROM | IN) identifierReference)?
              (LIKE? pattern=stringLit)?
    ;

showTblProperties
    : SHOW TBLPROPERTIES table=identifierReference
              (LP_ key=propertyKey RP_)?
    ;

showViews
    : SHOW VIEWS ((FROM | IN) identifierReference)?
              (LIKE? pattern=stringLit)?
    ;

showProcedures
    : SHOW PROCEDURES ((FROM | IN) identifierReference)?
    ;
showCurrentNamespace
    : SHOW CURRENT namespace
    ;
showCatalogs
    :SHOW CATALOGS (LIKE? pattern=stringLit)?
    ;

uncacheTable
    : UNCACHE TABLE (IF EXISTS)? identifierReference
    ;

multiInsertQuery
    : (ctes)? fromClause multiInsertQueryBody+
    ;

deleteFromTable
    : DELETE FROM identifierReference tableAlias whereClause?
    ;

updateTable
    : UPDATE identifierReference tableAlias setClause whereClause?
    ;

mergeIntoTable
    : MERGE (WITH SCHEMA EVOLUTION)? INTO target=identifierReference targetAlias=tableAlias
              USING (source=identifierReference |
                LP_ sourceQuery=query RP_) sourceAlias=tableAlias
              ON mergeCondition=booleanExpression
              matchedClause*
              notMatchedClause*
              notMatchedBySourceClause*
    ;

replaceTable
    : replaceTableHeader (LP_ tableElementList RP_)? tableProvider?
              createTableClauses
              (AS? query)?
    ;

declareVariable
    : DECLARE (OR REPLACE)? variable?
              identifierReference dataType? variableDefaultExpression?
    ;

dropVariable
    : DROP TEMPORARY variable (IF EXISTS)? identifierReference
    ;

commentDatabase
    : COMMENT ON namespace identifierReference IS
              comment
    ;

commentTable
    : COMMENT ON TABLE identifierReference IS comment
    ;

createIndex
    : CREATE INDEX (IF errorCapturingNot EXISTS)? qualifiedName ON TABLE?
              identifierReference (USING indexType=identifier)?
              LP_ columns=multipartIdentifierPropertyList RP_
              (OPTIONS options=propertyList)?
    ;

dropIndex
    : DROP INDEX (IF EXISTS)? qualifiedName ON TABLE? identifierReference
    ;

call
    : CALL identifierReference
              LP_
              (functionArgument (COMMA_ functionArgument)*)?
              RP_
    ;






//TODO 以下均为原始语句，或者其它新增的小语法

fileOrDirectoryName
    : SLASH_? multipartIdentifier (SLASH_ multipartIdentifier)*
    | multipartIdentifier
    | stringLit
    ;

valuesClause
    : VALUES expression (COMMA_ expression)*
    ;

compoundOrSingleStatement
    : singleStatement
    | singleCompoundStatement
    ;

singleCompoundStatement
    : BEGIN compoundBody? END SEMI_? EOF
    ;

beginEndCompoundBlock
    : beginLabel? BEGIN compoundBody? END endLabel?
    ;

compoundBody
    : (compoundStatements+=compoundStatement SEMI_)+
    ;

compoundStatement
//    : statement
    : root
    | setStatementWithOptionalVarKeyword
    | beginEndCompoundBlock
    | declareConditionStatement
    | declareHandlerStatement
    | ifElseStatement
    | caseStatement
    | whileStatement
    | repeatStatement
    | leaveStatement
    | iterateStatement
    | loopStatement
    | forStatement
    ;

setStatementWithOptionalVarKeyword
    : SET variable? assignmentList                              #setVariableWithOptionalKeyword
    | SET variable? LP_ multipartIdentifierList RP_ EQ_
        LP_ query RP_                            #setVariableWithOptionalKeyword
    ;

sqlStateValue
    : stringLit
    ;

declareConditionStatement
    : DECLARE multipartIdentifier CONDITION (FOR SQLSTATE VALUE? sqlStateValue)?
    ;

conditionValue
    : SQLSTATE VALUE? sqlStateValue
    | SQLEXCEPTION
    | NOT FOUND
    | multipartIdentifier
    ;

conditionValues
    : cvList+=conditionValue (COMMA_ cvList+=conditionValue)*
    ;

declareHandlerStatement
//    : DECLARE (CONTINUE | EXIT) HANDLER FOR conditionValues (beginEndCompoundBlock | statement | setStatementWithOptionalVarKeyword)
    : DECLARE (CONTINUE | EXIT) HANDLER FOR conditionValues (beginEndCompoundBlock | root | setStatementWithOptionalVarKeyword)
    ;

whileStatement
    : beginLabel? WHILE booleanExpression DO compoundBody END WHILE endLabel?
    ;

ifElseStatement
    : IF booleanExpression THEN conditionalBodies+=compoundBody
        (ELSEIF booleanExpression THEN conditionalBodies+=compoundBody)*
        (ELSE elseBody=compoundBody)? END IF
    ;

repeatStatement
    : beginLabel? REPEAT compoundBody UNTIL booleanExpression END REPEAT endLabel?
    ;

leaveStatement
    : LEAVE multipartIdentifier
    ;

iterateStatement
    : ITERATE multipartIdentifier
    ;

caseStatement
    : CASE (WHEN conditions+=booleanExpression THEN conditionalBodies+=compoundBody)+
        (ELSE elseBody=compoundBody)? END CASE                #searchedCaseStatement
    | CASE caseVariable=expression (WHEN conditionExpressions+=expression THEN conditionalBodies+=compoundBody)+
        (ELSE elseBody=compoundBody)? END CASE                #simpleCaseStatement
    ;

loopStatement
    : beginLabel? LOOP compoundBody END LOOP endLabel?
    ;

forStatement
    : beginLabel? FOR (multipartIdentifier AS)? query DO compoundBody END FOR endLabel?
    ;

singleStatement
//    : (statement|setResetStatement) SEMI_* EOF
    : (root|setResetStatement) SEMI_* EOF
    ;

beginLabel
    : multipartIdentifier COLON_
    ;

endLabel
    : multipartIdentifier
    ;

singleExpression
    : namedExpression EOF
    ;

singleTableIdentifier
    : tableIdentifier EOF
    ;

singleMultipartIdentifier
    : multipartIdentifier EOF
    ;

singleFunctionIdentifier
    : functionIdentifier EOF
    ;

singleDataType
    : dataType EOF
    ;

singleTableSchema
    : colTypeList EOF
    ;

singleRoutineParamList
    : colDefinitionList EOF
    ;

//statement
//    : query                                                            #statementDefault
//    | executeImmediate                                                 #visitExecuteImmediate
//    | ctes? dmlStatementNoWith                                         #dmlStatement
//    | USE identifierReference                                          #use
//    | USE namespace identifierReference                                #useNamespace
//    | SET CATALOG catalogIdentifierReference                           #setCatalog
//    | CREATE namespace (IF errorCapturingNot EXISTS)? identifierReference
//        (commentSpec |
//         locationSpec |
//         (WITH (DBPROPERTIES | PROPERTIES) propertyList))*             #createNamespace
//    | ALTER namespace identifierReference
//        SET (DBPROPERTIES | PROPERTIES) propertyList                   #setNamespaceProperties
//    | ALTER namespace identifierReference
//        UNSET (DBPROPERTIES | PROPERTIES) propertyList                 #unsetNamespaceProperties
//    | ALTER namespace identifierReference
//        SET locationSpec                                               #setNamespaceLocation
//    | DROP namespace (IF EXISTS)? identifierReference
//        (RESTRICT | CASCADE)?                                          #dropNamespace
//    | SHOW namespaces ((FROM | IN) multipartIdentifier)?
//        (LIKE? pattern=stringLit)?                                        #showNamespaces
//    | createTableHeader (LP_ tableElementList RP_)? tableProvider?
//        createTableClauses
//        (AS? query)?                                                   #createTable
//    | CREATE TABLE (IF errorCapturingNot EXISTS)? target=tableIdentifier
//        LIKE source=tableIdentifier
//        (tableProvider |
//        rowFormat |
//        createFileFormat |
//        locationSpec |
//        (TBLPROPERTIES tableProps=propertyList))*                      #createTableLike
//    | replaceTableHeader (LP_ tableElementList RP_)? tableProvider?
//        createTableClauses
//        (AS? query)?                                                   #replaceTable
//    | ANALYZE TABLE identifierReference partitionSpec? COMPUTE STATISTICS
//        (identifier | FOR COLUMNS identifierSeq | FOR ALL COLUMNS)?    #analyze
//    | ANALYZE TABLES ((FROM | IN) identifierReference)? COMPUTE STATISTICS
//        (identifier)?                                                  #analyzeTables
//    | ALTER TABLE identifierReference
//        ADD (COLUMN | COLUMNS)
//        columns=qualifiedColTypeWithPositionList                       #addTableColumns
//    | ALTER TABLE identifierReference
//        ADD (COLUMN | COLUMNS)
//        LP_ columns=qualifiedColTypeWithPositionList RP_ #addTableColumns
//    | ALTER TABLE table=identifierReference
//        RENAME COLUMN
//        from=multipartIdentifier TO to=errorCapturingIdentifier        #renameTableColumn
//    | ALTER TABLE identifierReference
//        DROP (COLUMN | COLUMNS) (IF EXISTS)?
//        LP_ columns=multipartIdentifierList RP_         #dropTableColumns
//    | ALTER TABLE identifierReference
//        DROP (COLUMN | COLUMNS) (IF EXISTS)?
//        columns=multipartIdentifierList                                #dropTableColumns
//    | ALTER (TABLE | VIEW) from=identifierReference
//        RENAME TO to=multipartIdentifier                               #renameTable
//    | ALTER (TABLE | VIEW) identifierReference
//        SET TBLPROPERTIES propertyList                                 #setTableProperties
//    | ALTER (TABLE | VIEW) identifierReference
//        UNSET TBLPROPERTIES (IF EXISTS)? propertyList                  #unsetTableProperties
//    | ALTER TABLE table=identifierReference
//        (ALTER | CHANGE) COLUMN? columns=alterColumnSpecList           #alterTableAlterColumn
//    | ALTER TABLE table=identifierReference partitionSpec?
//        CHANGE COLUMN?
//        colName=multipartIdentifier colType colPosition?               #hiveChangeColumn
//    | ALTER TABLE table=identifierReference partitionSpec?
//        REPLACE COLUMNS
//        LP_ columns=qualifiedColTypeWithPositionList
//        RP_                                                    #hiveReplaceColumns
//    | ALTER TABLE identifierReference (partitionSpec)?
//        SET SERDE stringLit (WITH SERDEPROPERTIES propertyList)?       #setTableSerDe
//    | ALTER TABLE identifierReference (partitionSpec)?
//        SET SERDEPROPERTIES propertyList                               #setTableSerDe
//    | ALTER (TABLE | VIEW) identifierReference ADD (IF errorCapturingNot EXISTS)?
//        partitionSpecLocation+                                         #addTablePartition
//    | ALTER TABLE identifierReference
//        from=partitionSpec RENAME TO to=partitionSpec                  #renameTablePartition
//    | ALTER (TABLE | VIEW) identifierReference
//        DROP (IF EXISTS)? partitionSpec (COMMA_ partitionSpec)* PURGE?  #dropTablePartitions
//    | ALTER TABLE identifierReference
//        (partitionSpec)? SET locationSpec                              #setTableLocation
//    | ALTER TABLE identifierReference RECOVER PARTITIONS                 #recoverPartitions
//    | ALTER TABLE identifierReference
//        (clusterBySpec | CLUSTER BY NONE)                              #alterClusterBy
//    | ALTER TABLE identifierReference collationSpec                    #alterTableCollation
//    | ALTER TABLE identifierReference ADD tableConstraintDefinition    #addTableConstraint
//    | ALTER TABLE identifierReference
//        DROP CONSTRAINT (IF EXISTS)? name=identifier
//        (RESTRICT | CASCADE)?                                     #dropTableConstraint
//    | DROP TABLE (IF EXISTS)? identifierReference PURGE?               #dropTable
//    | DROP VIEW (IF EXISTS)? identifierReference                       #dropView
//    | CREATE (OR REPLACE)? (GLOBAL? TEMPORARY)?
//        VIEW (IF errorCapturingNot EXISTS)? identifierReference
//        identifierCommentList?
//        (commentSpec |
//         schemaBinding |
//         collationSpec |
//         (PARTITIONED ON identifierList) |
//         (TBLPROPERTIES propertyList))*
//        AS query                                                       #createView
//    | CREATE (OR REPLACE)? GLOBAL? TEMPORARY VIEW
//        tableIdentifier (LP_ colTypeList RP_)? tableProvider
//        (OPTIONS propertyList)?                                        #createTempViewUsing
//    | ALTER VIEW identifierReference AS? query                         #alterViewQuery
//    | ALTER VIEW identifierReference schemaBinding                     #alterViewSchemaBinding
//    | CREATE (OR REPLACE)? TEMPORARY? FUNCTION (IF errorCapturingNot EXISTS)?
//        identifierReference AS className=stringLit
//        (USING resource (COMMA_ resource)*)?                            #createFunction
//    | CREATE (OR REPLACE)? TEMPORARY? FUNCTION (IF errorCapturingNot EXISTS)?
//        identifierReference LP_ parameters=colDefinitionList? RP_
//        (RETURNS (dataType | TABLE LP_ returnParams=colTypeList RP_))?
//        routineCharacteristics
//        RETURN (query | expression)                                    #createUserDefinedFunction
//    | DROP TEMPORARY? FUNCTION (IF EXISTS)? identifierReference        #dropFunction
//    | DECLARE (OR REPLACE)? variable?
//        identifierReference dataType? variableDefaultExpression?       #createVariable
//    | DROP TEMPORARY variable (IF EXISTS)? identifierReference         #dropVariable
//    | EXPLAIN (LOGICAL | FORMATTED | EXTENDED | CODEGEN | COST)?
//        (statement|setResetStatement)                                  #explain
//    | SHOW TABLES ((FROM | IN) identifierReference)?
//        (LIKE? pattern=stringLit)?                                        #showTables
//    | SHOW TABLE EXTENDED ((FROM | IN) ns=identifierReference)?
//        LIKE pattern=stringLit partitionSpec?                             #showTableExtended
//    | SHOW TBLPROPERTIES table=identifierReference
//        (LP_ key=propertyKey RP_)?                      #showTblProperties
//    | SHOW COLUMNS (FROM | IN) table=identifierReference
//        ((FROM | IN) ns=multipartIdentifier)?                          #showColumns
//    | SHOW VIEWS ((FROM | IN) identifierReference)?
//        (LIKE? pattern=stringLit)?                                        #showViews
//    | SHOW PARTITIONS identifierReference partitionSpec?               #showPartitions
//    | SHOW identifier? FUNCTIONS ((FROM | IN) ns=identifierReference)?
//        (LIKE? (legacy=multipartIdentifier | pattern=stringLit))?      #showFunctions
//    | SHOW PROCEDURES ((FROM | IN) identifierReference)?               #showProcedures
//    | SHOW CREATE TABLE identifierReference (AS SERDE)?                #showCreateTable
//    | SHOW CURRENT namespace                                           #showCurrentNamespace
//    | SHOW CATALOGS (LIKE? pattern=stringLit)?                            #showCatalogs
//    | (DESC | DESCRIBE) FUNCTION EXTENDED? describeFuncName            #describeFunction
//    | (DESC | DESCRIBE) PROCEDURE identifierReference                  #describeProcedure
//    | (DESC | DESCRIBE) namespace EXTENDED?
//        identifierReference                                            #describeNamespace
//    | (DESC | DESCRIBE) TABLE? option=(EXTENDED | FORMATTED)?
//        identifierReference partitionSpec? describeColName? (AS JSON)? #describeRelation
//    | (DESC | DESCRIBE) QUERY? query                                   #describeQuery
//    | COMMENT ON namespace identifierReference IS
//        comment                                                        #commentNamespace
//    | COMMENT ON TABLE identifierReference IS comment                  #commentTable
//    | REFRESH TABLE identifierReference                                #refreshTable
//    | REFRESH FUNCTION identifierReference                             #refreshFunction
//    | REFRESH (stringLit | .*?)                                        #refreshResource
//    | CACHE LAZY? TABLE identifierReference
//        (OPTIONS options=propertyList)? (AS? query)?                   #cacheTable
//    | UNCACHE TABLE (IF EXISTS)? identifierReference                   #uncacheTable
//    | CLEAR CACHE                                                      #clearCache
//    | LOAD DATA LOCAL? INPATH path=stringLit OVERWRITE? INTO TABLE
//        identifierReference partitionSpec?                             #loadData
//    | TRUNCATE TABLE identifierReference partitionSpec?                #truncateTable
//    | (MSCK)? REPAIR TABLE identifierReference
//        (option=(ADD|DROP|SYNC) PARTITIONS)?                           #repairTable
//    | op=(ADD | LIST) identifier .*?                                   #manageResource
//    | CREATE INDEX (IF errorCapturingNot EXISTS)? identifier ON TABLE?
//        identifierReference (USING indexType=identifier)?
//        LP_ columns=multipartIdentifierPropertyList RP_
//        (OPTIONS options=propertyList)?                                #createIndex
//    | DROP INDEX (IF EXISTS)? identifier ON TABLE? identifierReference #dropIndex
//    | CALL identifierReference
//        LP_
//        (functionArgument (COMMA_ functionArgument)*)?
//        RP_                                                    #call
//    | unsupportedHiveNativeCommands .*?                                #failNativeCommand
//    ;

setResetStatement
    : SET ROLE .*?                                                     #failSetRole
    | SET TIME ZONE interval                                           #setTimeZone
    | SET TIME ZONE timezone                                           #setTimeZone
    | SET TIME ZONE .*?                                                #setTimeZone
    | SET variable assignmentList                                      #setVariable
    | SET variable LP_ multipartIdentifierList RP_ EQ_
        LP_ query RP_                                   #setVariable
    | SET configKey EQ_ configValue                                     #setQuotedConfiguration
    | SET configKey (EQ_ .*?)?                                          #setConfiguration
    | SET .*? EQ_ configValue                                           #setQuotedConfiguration
    | SET .*?                                                          #setConfiguration
    | RESET configKey                                                  #resetQuotedConfiguration
    | RESET .*?                                                        #resetConfiguration
    ;

executeImmediate
    : EXECUTE IMMEDIATE queryParam=executeImmediateQueryParam (INTO targetVariable=multipartIdentifierList)? executeImmediateUsing?
    ;

executeImmediateUsing
    : USING LP_ params=namedExpressionSeq RP_
    | USING params=namedExpressionSeq
    ;

executeImmediateQueryParam
    : stringLit
    | multipartIdentifier
    ;

executeImmediateArgument
    : (constant|multipartIdentifier) (AS name=errorCapturingIdentifier)?
    ;

executeImmediateArgumentSeq
    : executeImmediateArgument (COMMA_ executeImmediateArgument)*
    ;

timezone
    : stringLit
    | LOCAL
    ;

configKey
    : quotedIdentifier
    ;

configValue
    : backQuotedIdentifier
    ;

unsupportedHiveNativeCommands
    : kw1=CREATE kw2=ROLE
    | kw1=DROP kw2=ROLE
    | kw1=GRANT kw2=ROLE?
    | kw1=REVOKE kw2=ROLE?
    | kw1=SHOW kw2=GRANT
    | kw1=SHOW kw2=ROLE kw3=GRANT?
    | kw1=SHOW kw2=PRINCIPALS
    | kw1=SHOW kw2=ROLES
    | kw1=SHOW kw2=CURRENT kw3=ROLES
    | kw1=EXPORT kw2=TABLE
    | kw1=IMPORT kw2=TABLE
    | kw1=SHOW kw2=COMPACTIONS
    | kw1=SHOW kw2=CREATE kw3=TABLE
    | kw1=SHOW kw2=TRANSACTIONS
    | kw1=SHOW kw2=INDEXES
    | kw1=SHOW kw2=LOCKS
    | kw1=CREATE kw2=INDEX
    | kw1=DROP kw2=INDEX
    | kw1=ALTER kw2=INDEX
    | kw1=LOCK kw2=TABLE
    | kw1=LOCK kw2=DATABASE
    | kw1=UNLOCK kw2=TABLE
    | kw1=UNLOCK kw2=DATABASE
    | kw1=CREATE kw2=TEMPORARY kw3=MACRO
    | kw1=DROP kw2=TEMPORARY kw3=MACRO
    | kw1=ALTER kw2=TABLE tableIdentifier kw3=NOT kw4=CLUSTERED
    | kw1=ALTER kw2=TABLE tableIdentifier kw3=CLUSTERED kw4=BY
    | kw1=ALTER kw2=TABLE tableIdentifier kw3=NOT kw4=SORTED
    | kw1=ALTER kw2=TABLE tableIdentifier kw3=SKEWED kw4=BY
    | kw1=ALTER kw2=TABLE tableIdentifier kw3=NOT kw4=SKEWED
    | kw1=ALTER kw2=TABLE tableIdentifier kw3=NOT kw4=STORED kw5=AS kw6=DIRECTORIES
    | kw1=ALTER kw2=TABLE tableIdentifier kw3=SET kw4=SKEWED kw5=LOCATION
    | kw1=ALTER kw2=TABLE tableIdentifier kw3=EXCHANGE kw4=PARTITION
    | kw1=ALTER kw2=TABLE tableIdentifier kw3=ARCHIVE kw4=PARTITION
    | kw1=ALTER kw2=TABLE tableIdentifier kw3=UNARCHIVE kw4=PARTITION
    | kw1=ALTER kw2=TABLE tableIdentifier kw3=TOUCH
    | kw1=ALTER kw2=TABLE tableIdentifier partitionSpec? kw3=COMPACT
    | kw1=ALTER kw2=TABLE tableIdentifier partitionSpec? kw3=CONCATENATE
    | kw1=ALTER kw2=TABLE tableIdentifier partitionSpec? kw3=SET kw4=FILEFORMAT
    | kw1=ALTER kw2=TABLE tableIdentifier partitionSpec? kw3=REPLACE kw4=COLUMNS
    | kw1=START kw2=TRANSACTION
    | kw1=COMMIT
    | kw1=ROLLBACK
    | kw1=DFS
    ;

createTableHeader
    : CREATE TEMPORARY? EXTERNAL? TABLE (IF errorCapturingNot EXISTS)? identifierReference
    ;

replaceTableHeader
    : (CREATE OR)? REPLACE TABLE identifierReference
    ;

clusterBySpec
    : CLUSTER BY LP_ multipartIdentifierList RP_
    ;

bucketSpec
    : CLUSTERED BY identifierList
      (SORTED BY orderedIdentifierList)?
      INTO INTEGER_VALUE BUCKETS
    ;

skewSpec
    : SKEWED BY identifierList
      ON (constantList | nestedConstantList)
      (STORED AS DIRECTORIES)?
    ;

locationSpec
    : LOCATION stringLit
    ;

schemaBinding
    : WITH SCHEMA (BINDING | COMPENSATION | EVOLUTION | TYPE EVOLUTION)
    ;

commentSpec
    : COMMENT stringLit
    ;

singleQuery
    : query EOF
    ;

query
    : ctes? queryTerm queryOrganization
    ;

insertInto
    : INSERT OVERWRITE TABLE? identifierReference optionsClause? (partitionSpec (IF errorCapturingNot EXISTS)?)?  ((BY NAME) | identifierList)? #insertOverwriteTable
    | INSERT INTO TABLE? identifierReference optionsClause? partitionSpec? (IF errorCapturingNot EXISTS)? ((BY NAME) | identifierList)?   #insertIntoTable
    | INSERT INTO TABLE? identifierReference optionsClause? REPLACE whereClause                                             #insertIntoReplaceWhere
    | INSERT OVERWRITE LOCAL? DIRECTORY path=stringLit rowFormat? createFileFormat?                     #insertOverwriteHiveDir
    | INSERT OVERWRITE LOCAL? DIRECTORY (path=stringLit)? tableProvider (OPTIONS options=propertyList)? #insertOverwriteDir
    ;

partitionSpecLocation
    : partitionSpec locationSpec?
    ;

partitionSpec
    : PARTITION LP_ partitionVal (COMMA_ partitionVal)* RP_
    ;

partitionVal
    : identifier (EQ_ constant)?
    | identifier EQ_ DEFAULT
    ;

namespace
    : NAMESPACE
    | DATABASE
    | SCHEMA
    ;

namespaces
    : NAMESPACES
    | DATABASES
    | SCHEMAS
    ;

variable
    : VARIABLE
    | VAR
    ;

describeFuncName
    : identifierReference
    | stringLit
    | comparisonOperator
    | arithmeticOperator
    | predicateOperator
    | shiftOperator
    | BANG_
    ;

describeColName
    : nameParts+=errorCapturingIdentifier (DOT_ nameParts+=errorCapturingIdentifier)*
    ;

ctes
    : WITH RECURSIVE? namedQuery (COMMA_ namedQuery)*
    ;

namedQuery
    : name=errorCapturingIdentifier (columnAliases=identifierList)? AS? LP_ query RP_
    ;

tableProvider
    : USING multipartIdentifier
    ;

createTableClauses
    :((OPTIONS options=expressionPropertyList) |
     (PARTITIONED BY partitioning=partitionFieldList) |
     skewSpec |
     clusterBySpec |
     bucketSpec |
     rowFormat |
     createFileFormat |
     locationSpec |
     commentSpec |
     collationSpec |
     (TBLPROPERTIES tableProps=propertyList))*
    ;

propertyList
    : LP_ property (COMMA_ property)* RP_
    ;

property
    : key=propertyKey (EQ_? value=propertyValue)?
    ;

propertyKey
    : errorCapturingIdentifier (DOT_ errorCapturingIdentifier)*
    | stringLit
    ;

propertyValue
    : INTEGER_VALUE
    | DECIMAL_VALUE
    | booleanValue
    | stringLit
    ;

expressionPropertyList
    : LP_ expressionProperty (COMMA_ expressionProperty)* RP_
    ;

expressionProperty
    : key=propertyKey (EQ_? value=expression)?
    ;

constantList
    : LP_ constant (COMMA_ constant)* RP_
    ;

nestedConstantList
    : LP_ constantList (COMMA_ constantList)* RP_
    ;

createFileFormat
    : STORED AS fileFormat
    | STORED BY storageHandler
    ;

fileFormat
    : INPUTFORMAT inFmt=stringLit OUTPUTFORMAT outFmt=stringLit    #tableFileFormat
    | identifier                                             #genericFileFormat
    ;

storageHandler
    : stringLit (WITH SERDEPROPERTIES propertyList)?
    ;

resource
    : identifier stringLit
    ;

//dmlStatementNoWith
//    : insertInto query                                                             #singleInsertQuery
//    | fromClause multiInsertQueryBody+                                             #multiInsertQuery
//    | DELETE FROM identifierReference tableAlias whereClause?                      #deleteFromTable
//    | UPDATE identifierReference tableAlias setClause whereClause?                 #updateTable
//    | MERGE (WITH SCHEMA EVOLUTION)? INTO target=identifierReference targetAlias=tableAlias
//        USING (source=identifierReference |
//          LP_ sourceQuery=query RP_) sourceAlias=tableAlias
//        ON mergeCondition=booleanExpression
//        matchedClause*
//        notMatchedClause*
//        notMatchedBySourceClause*                                                  #mergeIntoTable
//    ;

identifierReference
    : IDENTIFIER_KW LP_ expression RP_
    | multipartIdentifier
    ;

catalogIdentifierReference
    : IDENTIFIER_KW LP_ expression RP_
    | errorCapturingIdentifier
    | stringLit
    ;

queryOrganization
    : (ORDER BY order+=sortItem (COMMA_ order+=sortItem)*)?
      (CLUSTER BY clusterBy+=expression (COMMA_ clusterBy+=expression)*)?
      (DISTRIBUTE BY distributeBy+=expression (COMMA_ distributeBy+=expression)*)?
      (SORT BY sort+=sortItem (COMMA_ sort+=sortItem)*)?
      windowClause?
      (LIMIT (ALL | limit=expression))?
      (OFFSET offset=expression)?
    ;

multiInsertQueryBody
    : insertInto fromStatementBody
    ;

queryTerm
    : queryPrimary                                                                       #queryTermDefault
    | left=queryTerm {legacy_setops_precedence_enabled}?
        operator=(INTERSECT | UNION | EXCEPT | SETMINUS) setQuantifier? right=queryTerm  #setOperation
    | left=queryTerm {!legacy_setops_precedence_enabled}?
        operator=INTERSECT setQuantifier? right=queryTerm                                #setOperation
    | left=queryTerm {!legacy_setops_precedence_enabled}?
        operator=(UNION | EXCEPT | SETMINUS) setQuantifier? right=queryTerm              #setOperation
    | left=queryTerm OPERATOR_PIPE_ operatorPipeRightSide                                 #operatorPipeStatement
    ;

queryPrimary
    : querySpecification                                                    #queryPrimaryDefault
    | fromStatement                                                         #fromStmt
    | TABLE identifierReference                                             #table
    | inlineTable                                                           #inlineTableDefault1
    | LP_ query RP_                                          #subquery
    ;

sortItem
    : expression ordering=(ASC | DESC)? (NULLS nullOrder=(LAST | FIRST))?
    ;

fromStatement
    : fromClause fromStatementBody*
    ;

fromStatementBody
    : transformClause
      whereClause?
      queryOrganization
    | selectClause
      lateralView*
      whereClause?
      aggregationClause?
      havingClause?
      windowClause?
      queryOrganization
    ;

querySpecification
    : transformClause
      fromClause?
      lateralView*
      whereClause?
      aggregationClause?
      havingClause?
      windowClause?                                                         #transformQuerySpecification
    | selectClause
      fromClause?
      lateralView*
      whereClause?
      aggregationClause?
      havingClause?
      windowClause?                                                         #regularQuerySpecification
    ;

transformClause
    : (SELECT kind=TRANSFORM LP_ setQuantifier? expressionSeq RP_
            | kind=MAP setQuantifier? expressionSeq
            | kind=REDUCE setQuantifier? expressionSeq)
      inRowFormat=rowFormat?
      (RECORDWRITER recordWriter=stringLit)?
      USING script=stringLit
      (AS (identifierSeq | colTypeList | (LP_ (identifierSeq | colTypeList) RP_)))?
      outRowFormat=rowFormat?
      (RECORDREADER recordReader=stringLit)?
    ;

selectClause
    : SELECT (hints+=hint)* setQuantifier? namedExpressionSeq
    ;

setClause
    : SET assignmentList
    ;

matchedClause
    : WHEN MATCHED (AND matchedCond=booleanExpression)? THEN matchedAction
    ;
notMatchedClause
    : WHEN errorCapturingNot MATCHED (BY TARGET)? (AND notMatchedCond=booleanExpression)? THEN notMatchedAction
    ;

notMatchedBySourceClause
    : WHEN errorCapturingNot MATCHED BY SOURCE (AND notMatchedBySourceCond=booleanExpression)? THEN notMatchedBySourceAction
    ;

matchedAction
    : DELETE
    | UPDATE SET ASTERISK_
    | UPDATE SET assignmentList
    ;

notMatchedAction
    : INSERT ASTERISK_
    | INSERT LP_ columns=multipartIdentifierList RP_
        VALUES LP_ expression (COMMA_ expression)* RP_
    ;

notMatchedBySourceAction
    : DELETE
    | UPDATE SET assignmentList
    ;

exceptClause
    : EXCEPT LP_ exceptCols=multipartIdentifierList RP_
    ;

assignmentList
    : assignment (COMMA_ assignment)*
    ;

assignment
    : key=multipartIdentifier EQ_ value=expression
    ;

whereClause
    : WHERE booleanExpression
    ;

havingClause
    : HAVING booleanExpression
    ;

hint
    : HENT_START_ hintStatements+=hintStatement (COMMA_? hintStatements+=hintStatement)* HENT_END_
    ;

hintStatement
    : hintName=identifier
    | hintName=identifier LP_ parameters+=primaryExpression (COMMA_ parameters+=primaryExpression)* RP_
    ;

fromClause
    : FROM relation (COMMA_ relation)* lateralView* pivotClause? unpivotClause?
    ;

temporalClause
    : FOR? (SYSTEM_VERSION | VERSION) AS OF version
    | FOR? (SYSTEM_TIME | TIMESTAMP) AS OF timestamp=valueExpression
    ;

aggregationClause
    : GROUP BY groupingExpressionsWithGroupingAnalytics+=groupByClause
        (COMMA_ groupingExpressionsWithGroupingAnalytics+=groupByClause)*
    | GROUP BY groupingExpressions+=namedExpression (COMMA_ groupingExpressions+=namedExpression)* (
      WITH kind=ROLLUP
    | WITH kind=CUBE
    | kind=GROUPING SETS LP_ groupingSet (COMMA_ groupingSet)* RP_)?
    ;

groupByClause
    : groupingAnalytics
    | expression
    ;

groupingAnalytics
    : (ROLLUP | CUBE) LP_ groupingSet (COMMA_ groupingSet)* RP_
    | GROUPING SETS LP_ groupingElement (COMMA_ groupingElement)* RP_
    ;

groupingElement
    : groupingAnalytics
    | groupingSet
    ;

groupingSet
    : LP_ (expression (COMMA_ expression)*)? RP_
    | expression
    ;

pivotClause
    : PIVOT LP_ aggregates=namedExpressionSeq FOR pivotColumn IN LP_ pivotValues+=pivotValue (COMMA_ pivotValues+=pivotValue)* RP_ RP_
    ;

pivotColumn
    : identifiers+=errorCapturingIdentifier
    | LP_ identifiers+=errorCapturingIdentifier (COMMA_ identifiers+=errorCapturingIdentifier)* RP_
    ;

pivotValue
    : expression (AS? errorCapturingIdentifier)?
    ;

unpivotClause
    : UNPIVOT nullOperator=unpivotNullClause? LP_
        operator=unpivotOperator
      RP_ (AS? errorCapturingIdentifier)?
    ;

unpivotNullClause
    : (INCLUDE | EXCLUDE) NULLS
    ;

unpivotOperator
    : (unpivotSingleValueColumnClause | unpivotMultiValueColumnClause)
    ;

unpivotSingleValueColumnClause
    : unpivotValueColumn FOR unpivotNameColumn IN LP_ unpivotColumns+=unpivotColumnAndAlias (COMMA_ unpivotColumns+=unpivotColumnAndAlias)* RP_
    ;

unpivotMultiValueColumnClause
    : LP_ unpivotValueColumns+=unpivotValueColumn (COMMA_ unpivotValueColumns+=unpivotValueColumn)* RP_
      FOR unpivotNameColumn
      IN LP_ unpivotColumnSets+=unpivotColumnSet (COMMA_ unpivotColumnSets+=unpivotColumnSet)* RP_
    ;

unpivotColumnSet
    : LP_ unpivotColumns+=unpivotColumn (COMMA_ unpivotColumns+=unpivotColumn)* RP_ unpivotAlias?
    ;

unpivotValueColumn
    : identifier
    ;

unpivotNameColumn
    : identifier
    ;

unpivotColumnAndAlias
    : unpivotColumn unpivotAlias?
    ;

unpivotColumn
    : multipartIdentifier
    ;

unpivotAlias
    : AS? errorCapturingIdentifier
    ;

lateralView
    : LATERAL VIEW (OUTER)? qualifiedName LP_ (expression (COMMA_ expression)*)? RP_ tblName=identifier (AS? colName+=identifier (COMMA_ colName+=identifier)*)?
    ;

setQuantifier
    : DISTINCT
    | ALL
    ;

relation
    : LATERAL? relationPrimary relationExtension*
    ;

relationExtension
    : joinRelation
    | pivotClause
    | unpivotClause
    ;

joinRelation
    : (joinType) JOIN LATERAL? right=relationPrimary joinCriteria?
    | NATURAL joinType JOIN LATERAL? right=relationPrimary
    ;

joinType
    : INNER?
    | CROSS
    | LEFT OUTER?
    | LEFT? SEMI
    | RIGHT OUTER?
    | FULL OUTER?
    | LEFT? ANTI
    ;

joinCriteria
    : ON booleanExpression
    | USING identifierList
    ;

sample
    : TABLESAMPLE LP_ sampleMethod? RP_ (REPEATABLE LP_ seed=INTEGER_VALUE RP_)?
    ;

sampleMethod
    : negativeSign=MINUS_? percentage=(INTEGER_VALUE | DECIMAL_VALUE) PERCENTLIT   #sampleByPercentile
    | expression ROWS                                                             #sampleByRows
    | sampleType=BUCKET numerator=INTEGER_VALUE OUT OF denominator=INTEGER_VALUE
        (ON (identifier | qualifiedName LP_ RP_))?                 #sampleByBucket
    | bytes=expression                                                            #sampleByBytes
    ;

identifierList
    : LP_ identifierSeq RP_
    ;

identifierSeq
    : ident+=errorCapturingIdentifier (COMMA_ ident+=errorCapturingIdentifier)*
    ;

orderedIdentifierList
    : LP_ orderedIdentifier (COMMA_ orderedIdentifier)* RP_
    ;

orderedIdentifier
    : ident=errorCapturingIdentifier ordering=(ASC | DESC)?
    ;

identifierCommentList
    : LP_ identifierComment (COMMA_ identifierComment)* RP_
    ;

identifierComment
    : identifier commentSpec?
    ;

relationPrimary
    : identifierReference temporalClause?
      optionsClause? sample? tableAlias                     #tableName
    | LP_ query RP_ sample? tableAlias       #aliasedQuery
    | LP_ relation RP_ sample? tableAlias    #aliasedRelation
    | inlineTable                                           #inlineTableDefault2
    | functionTable                                         #tableValuedFunction
    ;

optionsClause
    : WITH options=propertyList
    ;

inlineTable
    : VALUES expression (COMMA_ expression)* tableAlias
    ;

functionTableSubqueryArgument
    : TABLE identifierReference tableArgumentPartitioning?
    | TABLE LP_ identifierReference RP_ tableArgumentPartitioning?
    | TABLE LP_ query RP_ tableArgumentPartitioning?
    ;

tableArgumentPartitioning
    : ((WITH SINGLE PARTITION)
        | ((PARTITION | DISTRIBUTE) BY
            (((LP_ partition+=expression (COMMA_ partition+=expression)* RP_))
            | (expression (COMMA_ invalidMultiPartitionExpression=expression)+)
            | partition+=expression)))
      ((ORDER | SORT) BY
        (((LP_ sortItem (COMMA_ sortItem)* RP_)
        | (sortItem (COMMA_ invalidMultiSortItem=sortItem)+)
        | sortItem)))?
    ;

functionTableNamedArgumentExpression
    : key=identifier FAT_ARROW_ table=functionTableSubqueryArgument
    ;

functionTableReferenceArgument
    : functionTableSubqueryArgument
    | functionTableNamedArgumentExpression
    ;

functionTableArgument
    : functionTableReferenceArgument
    | functionArgument
    ;

functionTable
    : funcName=functionName LP_
      (functionTableArgument (COMMA_ functionTableArgument)*)?
      RP_ tableAlias
    ;

tableAlias
    : (AS? strictIdentifier identifierList?)?
    ;

rowFormat
    : ROW FORMAT SERDE name=stringLit (WITH SERDEPROPERTIES props=propertyList)?       #rowFormatSerde
    | ROW FORMAT DELIMITED
      (FIELDS TERMINATED BY fieldsTerminatedBy=stringLit (ESCAPED BY escapedBy=stringLit)?)?
      (COLLECTION ITEMS TERMINATED BY collectionItemsTerminatedBy=stringLit)?
      (MAP KEYS TERMINATED BY keysTerminatedBy=stringLit)?
      (LINES TERMINATED BY linesSeparatedBy=stringLit)?
      (NULL DEFINED AS nullDefinedAs=stringLit)?                                       #rowFormatDelimited
    ;

multipartIdentifierList
    : multipartIdentifier (COMMA_ multipartIdentifier)*
    ;

multipartIdentifier
    : parts+=errorCapturingIdentifier (DOT_ parts+=errorCapturingIdentifier)*
    ;

multipartIdentifierPropertyList
    : multipartIdentifierProperty (COMMA_ multipartIdentifierProperty)*
    ;

multipartIdentifierProperty
    : multipartIdentifier (OPTIONS options=propertyList)?
    ;

tableIdentifier
    : (db=errorCapturingIdentifier DOT_)? table=errorCapturingIdentifier
    ;

functionIdentifier
    : (db=errorCapturingIdentifier DOT_)? function=errorCapturingIdentifier
    ;

namedExpression
    : expression (AS? (name=errorCapturingIdentifier | identifierList))?
    ;

namedExpressionSeq
    : namedExpression (COMMA_ namedExpression)*
    ;

partitionFieldList
    : LP_ fields+=partitionField (COMMA_ fields+=partitionField)* RP_
    ;

partitionField
    : transform  #partitionTransform
    | colType    #partitionColumn
    ;

transform
    : qualifiedName                                                                             #identityTransform
    | transformName=identifier
      LP_ argument+=transformArgument (COMMA_ argument+=transformArgument)* RP_   #applyTransform
    ;

transformArgument
    : qualifiedName
    | constant
    ;

expression
    : booleanExpression
    ;

namedArgumentExpression
    : key=identifier FAT_ARROW_ value=expression
    ;

functionArgument
    : expression
    | namedArgumentExpression
    ;

expressionSeq
    : expression (COMMA_ expression)*
    ;

booleanExpression
    : (NOT | BANG_) booleanExpression                               #logicalNot
    | EXISTS LP_ query RP_                          #exists
    | valueExpression predicate?                                   #predicated
    | left=booleanExpression operator=AND right=booleanExpression  #logicalBinary
    | left=booleanExpression operator=OR right=booleanExpression   #logicalBinary
    ;

predicate
    : errorCapturingNot? kind=BETWEEN lower=valueExpression AND upper=valueExpression
    | errorCapturingNot? kind=IN LP_ expression (COMMA_ expression)* RP_
    | errorCapturingNot? kind=IN LP_ query RP_
    | errorCapturingNot? kind=RLIKE pattern=valueExpression
    | errorCapturingNot? kind=(LIKE | ILIKE) quantifier=(ANY | SOME | ALL) (LP_ RP_ | LP_ expression (COMMA_ expression)* RP_)
    | errorCapturingNot? kind=(LIKE | ILIKE) pattern=valueExpression (ESCAPE escapeChar=stringLit)?
    | IS errorCapturingNot? kind=NULL
    | IS errorCapturingNot? kind=(TRUE | FALSE | UNKNOWN)
    | IS errorCapturingNot? kind=DISTINCT FROM right=valueExpression
    ;

errorCapturingNot
    : NOT
    | BANG_
    ;

valueExpression
    : primaryExpression                                                                      #valueExpressionDefault
    | operator=(MINUS_ | PLUS_ | TILDE_) valueExpression                                        #arithmeticUnary
    | left=valueExpression operator=(ASTERISK_ | SLASH_ | PERCENT_ | DIV) right=valueExpression #arithmeticBinary
    | left=valueExpression operator=(PLUS_ | MINUS_ | CONCAT_PIPE_) right=valueExpression       #arithmeticBinary
    | left=valueExpression shiftOperator right=valueExpression                               #shiftExpression
    | left=valueExpression operator=AMPERSAND_ right=valueExpression                          #arithmeticBinary
    | left=valueExpression operator=HAT_ right=valueExpression                                #arithmeticBinary
    | left=valueExpression operator=PIPE_ right=valueExpression                               #arithmeticBinary
    | left=valueExpression comparisonOperator right=valueExpression                          #comparison
    ;

shiftOperator
    : SHIFT_LEFT_
    | SHIFT_RIGHT_
    | SHIFT_RIGHT_UNSIGNED_
    ;

datetimeUnit
    : YEAR | QUARTER | MONTH
    | WEEK | DAY | DAYOFYEAR
    | HOUR | MINUTE | SECOND | MILLISECOND | MICROSECOND
    ;

primaryExpression
    : name=(CURRENT_DATE | CURRENT_TIMESTAMP | CURRENT_USER | USER | SESSION_USER | CURRENT_TIME)             #currentLike
    | name=(TIMESTAMPADD | DATEADD | DATE_ADD) LP_ (unit=datetimeUnit | invalidUnit=stringLit) COMMA_ unitsAmount=valueExpression COMMA_ timestamp=valueExpression RP_             #timestampadd
    | name=(TIMESTAMPDIFF | DATEDIFF | DATE_DIFF | TIMEDIFF) LP_ (unit=datetimeUnit | invalidUnit=stringLit) COMMA_ startTimestamp=valueExpression COMMA_ endTimestamp=valueExpression RP_    #timestampdiff
    | CASE whenClause+ (ELSE elseExpression=expression)? END                                   #searchedCase
    | CASE value=expression whenClause+ (ELSE elseExpression=expression)? END                  #simpleCase
    | name=(CAST | TRY_CAST) LP_ expression AS dataType RP_                     #cast
    | primaryExpression collateClause                                                      #collate
    | primaryExpression DOUBLE_COLON_ dataType                                                  #castByColon
    | STRUCT LP_ (argument+=namedExpression (COMMA_ argument+=namedExpression)*)? RP_ #struct
    | FIRST LP_ expression (IGNORE NULLS)? RP_                                  #first
    | ANY_VALUE LP_ expression (IGNORE NULLS)? RP_                              #any_value
    | LAST LP_ expression (IGNORE NULLS)? RP_                                   #last
    | POSITION LP_ substr=valueExpression IN str=valueExpression RP_            #position
    | constant                                                                                 #constantDefault
    | ASTERISK_ exceptClause?                                                                   #star
    | qualifiedName DOT_ ASTERISK_ exceptClause?                                                 #star
    | LP_ namedExpression (COMMA_ namedExpression)+ RP_                          #rowConstructor
    | LP_ query RP_                                                             #subqueryExpression
    | functionName LP_ (setQuantifier? argument+=functionArgument
       (COMMA_ argument+=functionArgument)*)? RP_
       (WITHIN GROUP LP_ ORDER BY sortItem (COMMA_ sortItem)* RP_)?
       (FILTER LP_ WHERE where=booleanExpression RP_)?
       (nullsOption=(IGNORE | RESPECT) NULLS)? ( OVER windowSpec)?                             #functionCall
    | identifier ARROW_ expression                                                              #lambda
    | LP_ identifier (COMMA_ identifier)+ RP_ ARROW_ expression                   #lambda
    | value=primaryExpression LBT_ index=valueExpression RBT_                 #subscript
    | identifier                                                                               #columnReference
    | base=primaryExpression DOT_ fieldName=identifier                                          #dereference
    | LP_ expression RP_                                                        #parenthesizedExpression
    | EXTRACT LP_ field=identifier FROM source=valueExpression RP_              #extract
    | (SUBSTR | SUBSTRING) LP_ str=valueExpression (FROM | COMMA_) pos=valueExpression
      ((FOR | COMMA_) len=valueExpression)? RP_                                         #substring
    | TRIM LP_ trimOption=(BOTH | LEADING | TRAILING)? (trimStr=valueExpression)?
       FROM srcStr=valueExpression RP_                                                 #trim
    | OVERLAY LP_ input=valueExpression PLACING replace=valueExpression
      FROM position=valueExpression (FOR length=valueExpression)? RP_                  #overlay
    ;

literalType
    : DATE
    | TIME
    | TIMESTAMP | TIMESTAMP_LTZ | TIMESTAMP_NTZ
    | INTERVAL
    | BINARY_HEX
    | unsupportedType=identifier
    ;

constant
    : NULL                                                                                     #nullLiteral
    | QUESTION_                                                                                 #posParameterLiteral
    | COLON_ identifier                                                                         #namedParameterLiteral
    | interval                                                                                 #intervalLiteral
    | literalType stringLit                                                                    #typeConstructor
    | number                                                                                   #numericLiteral
    | booleanValue                                                                             #booleanLiteral
    | stringLit+                                                                               #stringLiteral
    ;

comparisonOperator
    : EQ_ | NEQ_ | NEQJ_ | LT_ | LTE_ | GT_ | GTE_ | NSEQ_
    ;

arithmeticOperator
    : PLUS_ | MINUS_ | ASTERISK_ | SLASH_ | PERCENT_ | DIV | TILDE_ | AMPERSAND_ | PIPE_ | CONCAT_PIPE_ | HAT_
    ;

predicateOperator
    : OR | AND | IN | NOT
    ;

booleanValue
    : TRUE | FALSE
    ;

interval
    : INTERVAL (errorCapturingMultiUnitsInterval | errorCapturingUnitToUnitInterval)
    ;

errorCapturingMultiUnitsInterval
    : body=multiUnitsInterval unitToUnitInterval?
    ;

multiUnitsInterval
    : (intervalValue unit+=unitInMultiUnits)+
    ;

errorCapturingUnitToUnitInterval
    : body=unitToUnitInterval (error1=multiUnitsInterval | error2=unitToUnitInterval)?
    ;

unitToUnitInterval
    : value=intervalValue from=unitInUnitToUnit TO to=unitInUnitToUnit
    ;

intervalValue
    : (PLUS_ | MINUS_)?
      (INTEGER_VALUE | DECIMAL_VALUE | stringLit)
    ;

unitInMultiUnits
    : NANOSECOND | NANOSECONDS | MICROSECOND | MICROSECONDS | MILLISECOND | MILLISECONDS
    | SECOND | SECONDS | MINUTE | MINUTES | HOUR | HOURS | DAY | DAYS | WEEK | WEEKS
    | MONTH | MONTHS | YEAR | YEARS
    ;

unitInUnitToUnit
    : SECOND | MINUTE | HOUR | DAY | MONTH | YEAR
    ;

colPosition
    : position=FIRST | position=AFTER afterCol=errorCapturingIdentifier
    ;

collationSpec
    : DEFAULT COLLATION collationName=identifier
    ;

collateClause
    : COLLATE collationName=multipartIdentifier
    ;

type
    : BOOLEAN
    | TINYINT | BYTE
    | SMALLINT | SHORT
    | INT | INTEGER
    | BIGINT | LONG
    | FLOAT | REAL
    | DOUBLE
    | DATE
    | TIME
    | TIMESTAMP | TIMESTAMP_NTZ | TIMESTAMP_LTZ
    | STRING collateClause?
    | CHARACTER | CHAR
    | VARCHAR
    | BINARY
    | DECIMAL | DEC | NUMERIC
    | VOID
    | INTERVAL
    | VARIANT
    | ARRAY | STRUCT | MAP
    | unsupportedType=identifier
    ;

dataType
    : complex=ARRAY LT_ dataType GT_                              #complexDataType
    | complex=MAP LT_ dataType COMMA_ dataType GT_                 #complexDataType
    | complex=STRUCT (LT_ complexColTypeList? GT_ | NEQ_)          #complexDataType
    | INTERVAL from=(YEAR | MONTH) (TO to=MONTH)?               #yearMonthIntervalDataType
    | INTERVAL from=(DAY | HOUR | MINUTE | SECOND)
      (TO to=(HOUR | MINUTE | SECOND))?                         #dayTimeIntervalDataType
    | type (LP_ INTEGER_VALUE
      (COMMA_ INTEGER_VALUE)* RP_)?                      #primitiveDataType
    ;

qualifiedColTypeWithPositionList
    : qualifiedColTypeWithPosition (COMMA_ qualifiedColTypeWithPosition)*
    ;

qualifiedColTypeWithPosition
    : name=multipartIdentifier dataType colDefinitionDescriptorWithPosition*
    ;

colDefinitionDescriptorWithPosition
    : errorCapturingNot NULL
    | defaultExpression
    | commentSpec
    | colPosition
    ;

defaultExpression
    : DEFAULT expression
    ;

variableDefaultExpression
    : (DEFAULT | EQ_) expression
    ;

colTypeList
    : colType (COMMA_ colType)*
    ;

colType
    : colName=errorCapturingIdentifier dataType (errorCapturingNot NULL)? commentSpec?
    ;

tableElementList
    : tableElement (COMMA_ tableElement)*
    ;

tableElement
    : tableConstraintDefinition
    | colDefinition
    ;

colDefinitionList
    : colDefinition (COMMA_ colDefinition)*
    ;

colDefinition
    : colName=errorCapturingIdentifier dataType colDefinitionOption*
    ;

colDefinitionOption
    : errorCapturingNot NULL
    | defaultExpression
    | generationExpression
    | commentSpec
    | columnConstraintDefinition
    ;

generationExpression
    : GENERATED ALWAYS AS LP_ expression RP_     #generatedColumn
    | GENERATED (ALWAYS | BY DEFAULT) AS IDENTITY identityColSpec? #identityColumn
    ;

identityColSpec
    : LP_ sequenceGeneratorOption* RP_
    ;

sequenceGeneratorOption
    : START WITH start=sequenceGeneratorStartOrStep
    | INCREMENT BY step=sequenceGeneratorStartOrStep
    ;

sequenceGeneratorStartOrStep
    : MINUS_? INTEGER_VALUE
    | MINUS_? BIGINT_LITERAL
    ;

complexColTypeList
    : complexColType (COMMA_ complexColType)*
    ;

complexColType
    : errorCapturingIdentifier COLON_? dataType (errorCapturingNot NULL)? commentSpec?
    ;

routineCharacteristics
    : (routineLanguage
    | specificName
    | deterministic
    | sqlDataAccess
    | nullCall
    | commentSpec
    | rightsClause)*
    ;

routineLanguage
    : LANGUAGE (SQL | IDENTIFIER)
    ;

specificName
    : SPECIFIC specific=errorCapturingIdentifier
    ;

deterministic
    : DETERMINISTIC
    | errorCapturingNot DETERMINISTIC
    ;

sqlDataAccess
    : access=NO SQL
    | access=CONTAINS SQL
    | access=READS SQL DATA
    | access=MODIFIES SQL DATA
    ;

nullCall
    : RETURNS NULL ON NULL INPUT
    | CALLED ON NULL INPUT
    ;

rightsClause
    : SQL SECURITY INVOKER
    | SQL SECURITY DEFINER
   ;

whenClause
    : WHEN condition=expression THEN result=expression
    ;

windowClause
    : WINDOW namedWindow (COMMA_ namedWindow)*
    ;

namedWindow
    : name=errorCapturingIdentifier AS windowSpec
    ;

windowSpec
    : name=errorCapturingIdentifier                         #windowRef
    | LP_ name=errorCapturingIdentifier RP_  #windowRef
    | LP_
      ( CLUSTER BY partition+=expression (COMMA_ partition+=expression)*
      | ((PARTITION | DISTRIBUTE) BY partition+=expression (COMMA_ partition+=expression)*)?
        ((ORDER | SORT) BY sortItem (COMMA_ sortItem)*)?)
      windowFrame?
      RP_                                           #windowDef
    ;

windowFrame
    : frameType=RANGE start=frameBound
    | frameType=ROWS start=frameBound
    | frameType=RANGE BETWEEN start=frameBound AND end=frameBound
    | frameType=ROWS BETWEEN start=frameBound AND end=frameBound
    ;

frameBound
    : UNBOUNDED boundType=(PRECEDING | FOLLOWING)
    | boundType=CURRENT ROW
    | expression boundType=(PRECEDING | FOLLOWING)
    ;

qualifiedNameList
    : qualifiedName (COMMA_ qualifiedName)*
    ;

functionName
    : IDENTIFIER_KW LP_ expression RP_
    | identFunc=IDENTIFIER_KW   // IDENTIFIER itself is also a valid function name.
    | qualifiedName
    | FILTER
    | LEFT
    | RIGHT
    ;

qualifiedName
    : identifier (DOT_ identifier)*
    ;

// this rule is used for explicitly capturing wrong identifiers such as test-table, which should actually be `test-table`
// replace identifier with errorCapturingIdentifier where the immediate follow symbol is not an expression, otherwise
// valid expressions such as "a-b" can be recognized as an identifier
errorCapturingIdentifier
    : identifier errorCapturingIdentifierExtra
    ;

// extra left-factoring grammar
errorCapturingIdentifierExtra
    : (MINUS_ identifier)+    #errorIdent
    |                        #realIdent
    ;

identifier
    : strictIdentifier
    | {!SQL_standard_keyword_behavior}? strictNonReserved
    ;

strictIdentifier
    : IDENTIFIER              #unquotedIdentifier
    | quotedIdentifier        #quotedIdentifierAlternative
    | {SQL_standard_keyword_behavior}? ansiNonReserved #unquotedIdentifier
    | {!SQL_standard_keyword_behavior}? nonReserved    #unquotedIdentifier
    ;

quotedIdentifier
    : BACKQUOTED_IDENTIFIER
    | {double_quoted_identifiers}? DOUBLEQUOTED_STRING
    ;

backQuotedIdentifier
    : BACKQUOTED_IDENTIFIER
    ;

number
    : {!legacy_exponent_literal_as_decimal_enabled}? MINUS_? EXPONENT_VALUE #exponentLiteral
    | {!legacy_exponent_literal_as_decimal_enabled}? MINUS_? DECIMAL_VALUE  #decimalLiteral
    | {legacy_exponent_literal_as_decimal_enabled}? MINUS_? (EXPONENT_VALUE | DECIMAL_VALUE) #legacyDecimalLiteral
    | MINUS_? INTEGER_VALUE            #integerLiteral
    | MINUS_? BIGINT_LITERAL           #bigIntLiteral
    | MINUS_? SMALLINT_LITERAL         #smallIntLiteral
    | MINUS_? TINYINT_LITERAL          #tinyIntLiteral
    | MINUS_? DOUBLE_LITERAL           #doubleLiteral
    | MINUS_? FLOAT_LITERAL            #floatLiteral
    | MINUS_? BIGDECIMAL_LITERAL       #bigDecimalLiteral
    ;

columnConstraintDefinition
    : (CONSTRAINT name=errorCapturingIdentifier)? columnConstraint constraintCharacteristic*
    ;

columnConstraint
    : checkConstraint
    | uniqueSpec
    | referenceSpec
    ;

tableConstraintDefinition
    : (CONSTRAINT name=errorCapturingIdentifier)? tableConstraint constraintCharacteristic*
    ;

tableConstraint
    : checkConstraint
    | uniqueConstraint
    | foreignKeyConstraint
    ;

checkConstraint
    : CHECK LP_ (expr=booleanExpression) RP_
    ;

uniqueSpec
    : UNIQUE
    | PRIMARY KEY
    ;

uniqueConstraint
    : uniqueSpec identifierList
    ;

referenceSpec
    : REFERENCES multipartIdentifier (parentColumns=identifierList)?
    ;

foreignKeyConstraint
    : FOREIGN KEY identifierList referenceSpec
    ;

constraintCharacteristic
    : enforcedCharacteristic
    | relyCharacteristic
    ;

enforcedCharacteristic
    : ENFORCED
    | NOT ENFORCED
    ;

relyCharacteristic
    : RELY
    | NORELY
    ;

alterColumnSpecList
    : alterColumnSpec (COMMA_ alterColumnSpec)*
    ;

alterColumnSpec
    : column=multipartIdentifier alterColumnAction?
    ;

alterColumnAction
    : TYPE dataType
    | commentSpec
    | colPosition
    | setOrDrop=(SET | DROP) errorCapturingNot NULL
    | SET defaultExpression
    | dropDefault=DROP DEFAULT
    ;

stringLit
    : STRING_LITERAL
    | {!double_quoted_identifiers}? DOUBLEQUOTED_STRING
    ;

comment
    : stringLit
    | NULL
    ;

version
    : INTEGER_VALUE
    | stringLit
    ;

operatorPipeRightSide
    : selectClause windowClause?
    | EXTEND extendList=namedExpressionSeq
    | SET operatorPipeSetAssignmentSeq
    | DROP identifierSeq
    | AS errorCapturingIdentifier
    // Note that the WINDOW clause is not allowed in the WHERE pipe operator, but we add it here in
    // the grammar simply for purposes of catching this invalid syntax and throwing a specific
    // dedicated error message.
    | whereClause windowClause?
    // The following two cases match the PIVOT or UNPIVOT clause, respectively.
    // For each one, we add the other clause as an option in order to return high-quality error
    // messages in the event that both are present (this is not allowed).
    | pivotClause unpivotClause?
    | unpivotClause pivotClause?
    | sample
    | joinRelation
    | operator=(UNION | EXCEPT | SETMINUS | INTERSECT) setQuantifier? right=queryPrimary
    | queryOrganization
    | AGGREGATE namedExpressionSeq? aggregationClause?
    ;

operatorPipeSetAssignmentSeq
    : ident+=errorCapturingIdentifier
        (DOT_ errorCapturingIdentifier)*  // This is invalid syntax; we just capture it here.
        EQ_ expression
        (COMMA_ ident+=errorCapturingIdentifier
          (DOT_ errorCapturingIdentifier)*  // This is invalid syntax; we just capture it here.
          EQ_ expression)*
    ;

// When `SQL_standard_keyword_behavior=true`, there are 2 kinds of keywords in Spark SQL.
// - Reserved keywords:
//     Keywords that are reserved and can't be used as identifiers for table, view, column,
//     function, alias, etc.
// - Non-reserved keywords:
//     Keywords that have a special meaning only in particular contexts and can be used as
//     identifiers in other contexts. For example, `EXPLAIN SELECT ...` is a command, but EXPLAIN
//     can be used as identifiers in other places.
// You can find the full keywords list by searching "Start of the keywords list" in this file.
// The non-reserved keywords are listed below. Keywords not in this list are reserved keywords.
ansiNonReserved
//--ANSI-NON-RESERVED-START
    : ADD
    | AFTER
    | AGGREGATE
    | ALTER
    | ALWAYS
    | ANALYZE
    | ANTI
    | ANY_VALUE
    | ARCHIVE
    | ARRAY
    | ASC
    | AT
    | BEGIN
    | BETWEEN
    | BIGINT
    | BINARY
    | BINARY_HEX
    | BINDING
    | BOOLEAN
    | BUCKET
    | BUCKETS
    | BY
    | BYTE
    | CACHE
    | CALLED
    | CASCADE
    | CATALOG
    | CATALOGS
    | CHANGE
    | CHAR
    | CHARACTER
    | CLEAR
    | CLUSTER
    | CLUSTERED
    | CODEGEN
    | COLLECTION
    | COLUMNS
    | COMMENT
    | COMMIT
    | COMPACT
    | COMPACTIONS
    | COMPENSATION
    | COMPUTE
    | CONCATENATE
    | CONDITION
    | CONTAINS
    | CONTINUE
    | COST
    | CUBE
    | CURRENT
    | DATA
    | DATABASE
    | DATABASES
    | DATE
    | DATEADD
    | DATE_ADD
    | DATEDIFF
    | DATE_DIFF
    | DAY
    | DAYS
    | DAYOFYEAR
    | DBPROPERTIES
    | DEC
    | DECIMAL
    | DECLARE
    | DEFAULT
    | DEFINED
    | DEFINER
    | DELETE
    | DELIMITED
    | DESC
    | DESCRIBE
    | DETERMINISTIC
    | DFS
    | DIRECTORIES
    | DIRECTORY
    | DISTRIBUTE
    | DIV
    | DO
    | DOUBLE
    | DROP
    | ELSEIF
    | ENFORCED
    | ESCAPED
    | EVOLUTION
    | EXCHANGE
    | EXCLUDE
    | EXISTS
    | EXIT
    | EXPLAIN
    | EXPORT
    | EXTEND
    | EXTENDED
    | EXTERNAL
    | EXTRACT
    | FIELDS
    | FILEFORMAT
    | FIRST
    | FLOAT
    | FOLLOWING
    | FORMAT
    | FORMATTED
    | FOUND
    | FUNCTION
    | FUNCTIONS
    | GENERATED
    | GLOBAL
    | GROUPING
    | HANDLER
    | HOUR
    | HOURS
    | IDENTIFIER_KW
    | IDENTITY
    | IF
    | IGNORE
    | IMMEDIATE
    | IMPORT
    | INCLUDE
    | INCREMENT
    | INDEX
    | INDEXES
    | INPATH
    | INPUT
    | INPUTFORMAT
    | INSERT
    | INT
    | INTEGER
    | INTERVAL
    | INVOKER
    | ITEMS
    | ITERATE
    | JSON
    | KEY
    | KEYS
    | LANGUAGE
    | LAST
    | LAZY
    | LEAVE
    | LIKE
    | ILIKE
    | LIMIT
    | LINES
    | LIST
    | LOAD
    | LOCAL
    | LOCATION
    | LOCK
    | LOCKS
    | LOGICAL
    | LONG
    | LOOP
    | MACRO
    | MAP
    | MATCHED
    | MERGE
    | MICROSECOND
    | MICROSECONDS
    | MILLISECOND
    | MILLISECONDS
    | MINUTE
    | MINUTES
    | MODIFIES
    | MONTH
    | MONTHS
    | MSCK
    | NAME
    | NAMESPACE
    | NAMESPACES
    | NANOSECOND
    | NANOSECONDS
    | NO
    | NONE
    | NORELY
    | NULLS
    | NUMERIC
    | OF
    | OPTION
    | OPTIONS
    | OUT
    | OUTPUTFORMAT
    | OVER
    | OVERLAY
    | OVERWRITE
    | PARTITION
    | PARTITIONED
    | PARTITIONS
    | PERCENTLIT
    | PIVOT
    | PLACING
    | POSITION
    | PRECEDING
    | PRINCIPALS
    | PROCEDURE
    | PROCEDURES
    | PROPERTIES
    | PURGE
    | QUARTER
    | QUERY
    | RANGE
    | READS
    | REAL
    | RECORDREADER
    | RECORDWRITER
    | RECOVER
    | REDUCE
    | REFRESH
    | RELY
    | RENAME
    | REPAIR
    | REPEAT
    | REPEATABLE
    | REPLACE
    | RESET
    | RESPECT
    | RESTRICT
    | RETURN
    | RETURNS
    | REVOKE
    | RLIKE
    | ROLE
    | ROLES
    | ROLLBACK
    | ROLLUP
    | ROW
    | ROWS
    | SCHEMA
    | SCHEMAS
    | SECOND
    | SECONDS
    | SECURITY
    | SEMI
    | SEPARATED
    | SERDE
    | SERDEPROPERTIES
    | SET
    | SETMINUS
    | SETS
    | SHORT
    | SHOW
    | SINGLE
    | SKEWED
    | SMALLINT
    | SORT
    | SORTED
    | SOURCE
    | SPECIFIC
    | SQLEXCEPTION
    | SQLSTATE
    | START
    | STATISTICS
    | STORED
    | STRATIFY
    | STRING
    | STRUCT
    | SUBSTR
    | SUBSTRING
    | SYNC
    | SYSTEM_TIME
    | SYSTEM_VERSION
    | TABLES
    | TABLESAMPLE
    | TARGET
    | TBLPROPERTIES
    | TEMPORARY
    | TERMINATED
    | TIMEDIFF
    | TIMESTAMP
    | TIMESTAMP_LTZ
    | TIMESTAMP_NTZ
    | TIMESTAMPADD
    | TIMESTAMPDIFF
    | TINYINT
    | TOUCH
    | TRANSACTION
    | TRANSACTIONS
    | TRANSFORM
    | TRIM
    | TRUE
    | TRUNCATE
    | TRY_CAST
    | TYPE
    | UNARCHIVE
    | UNBOUNDED
    | UNCACHE
    | UNLOCK
    | UNPIVOT
    | UNSET
    | UNTIL
    | UPDATE
    | USE
    | VALUE
    | VALUES
    | VARCHAR
    | VAR
    | VARIABLE
    | VARIANT
    | VERSION
    | VIEW
    | VIEWS
    | VOID
    | WEEK
    | WEEKS
    | WHILE
    | WINDOW
    | YEAR
    | YEARS
    | ZONE
//--ANSI-NON-RESERVED-END
    ;

// When `SQL_standard_keyword_behavior=false`, there are 2 kinds of keywords in Spark SQL.
// - Non-reserved keywords:
//     Same definition as the one when `SQL_standard_keyword_behavior=true`.
// - Strict-non-reserved keywords:
//     A strict version of non-reserved keywords, which can not be used as table alias.
// You can find the full keywords list by searching "Start of the keywords list" in this file.
// The strict-non-reserved keywords are listed in `strictNonReserved`.
// The non-reserved keywords are listed in `nonReserved`.
// These 2 together contain all the keywords.
strictNonReserved
    : ANTI
    | CROSS
    | EXCEPT
    | FULL
    | INNER
    | INTERSECT
    | JOIN
    | LATERAL
    | LEFT
    | NATURAL
    | ON
    | RIGHT
    | SEMI
    | SETMINUS
    | UNION
    | USING
    ;

nonReserved
//--DEFAULT-NON-RESERVED-START
    : ADD
    | AFTER
    | AGGREGATE
    | ALL
    | ALTER
    | ALWAYS
    | ANALYZE
    | AND
    | ANY
    | ANY_VALUE
    | ARCHIVE
    | ARRAY
    | AS
    | ASC
    | AT
    | AUTHORIZATION
    | BEGIN
    | BETWEEN
    | BIGINT
    | BINARY
    | BINARY_HEX
    | BINDING
    | BOOLEAN
    | BOTH
    | BUCKET
    | BUCKETS
    | BY
    | BYTE
    | CACHE
    | CALL
    | CALLED
    | CASCADE
    | CASE
    | CAST
    | CATALOG
    | CATALOGS
    | CHANGE
    | CHAR
    | CHARACTER
    | CHECK
    | CLEAR
    | CLUSTER
    | CLUSTERED
    | CODEGEN
    | COLLATE
    | COLLATION
    | COLLECTION
    | COLUMN
    | COLUMNS
    | COMMENT
    | COMMIT
    | COMPACT
    | COMPACTIONS
    | COMPENSATION
    | COMPUTE
    | CONCATENATE
    | CONDITION
    | CONSTRAINT
    | CONTAINS
    | CONTINUE
    | COST
    | CREATE
    | CUBE
    | CURRENT
    | CURRENT_DATE
    | CURRENT_TIME
    | CURRENT_TIMESTAMP
    | CURRENT_USER
    | DATA
    | DATABASE
    | DATABASES
    | DATE
    | DATEADD
    | DATE_ADD
    | DATEDIFF
    | DATE_DIFF
    | DAY
    | DAYS
    | DAYOFYEAR
    | DBPROPERTIES
    | DEC
    | DECIMAL
    | DECLARE
    | DEFAULT
    | DEFINED
    | DEFINER
    | DELETE
    | DELIMITED
    | DESC
    | DESCRIBE
    | DETERMINISTIC
    | DFS
    | DIRECTORIES
    | DIRECTORY
    | DISTINCT
    | DISTRIBUTE
    | DIV
    | DO
    | DOUBLE
    | DROP
    | ELSE
    | ELSEIF
    | END
    | ENFORCED
    | ESCAPE
    | ESCAPED
    | EVOLUTION
    | EXCHANGE
    | EXCLUDE
    | EXECUTE
    | EXISTS
    | EXIT
    | EXPLAIN
    | EXPORT
    | EXTEND
    | EXTENDED
    | EXTERNAL
    | EXTRACT
    | FALSE
    | FETCH
    | FILTER
    | FIELDS
    | FILEFORMAT
    | FIRST
    | FLOAT
    | FOLLOWING
    | FOR
    | FOREIGN
    | FORMAT
    | FORMATTED
    | FROM
    | FOUND
    | FUNCTION
    | FUNCTIONS
    | GENERATED
    | GLOBAL
    | GRANT
    | GROUP
    | GROUPING
    | HANDLER
    | HAVING
    | HOUR
    | HOURS
    | IDENTIFIER_KW
    | IDENTITY
    | IF
    | IGNORE
    | IMMEDIATE
    | IMPORT
    | IN
    | INCLUDE
    | INCREMENT
    | INDEX
    | INDEXES
    | INPATH
    | INPUT
    | INPUTFORMAT
    | INSERT
    | INT
    | INTEGER
    | INTERVAL
    | INTO
    | INVOKER
    | IS
    | ITEMS
    | ITERATE
    | JSON
    | JAR
    | KEY
    | KEYS
    | LANGUAGE
    | LAST
    | LAZY
    | LEADING
    | LEAVE
    | LIKE
    | LONG
    | ILIKE
    | LIMIT
    | LINES
    | LIST
    | LOAD
    | LOCAL
    | LOCATION
    | LOCK
    | LOCKS
    | LOGICAL
    | LONG
    | LOOP
    | MACRO
    | MAP
    | MATCHED
    | MERGE
    | MICROSECOND
    | MICROSECONDS
    | MILLISECOND
    | MILLISECONDS
    | MINUTE
    | MINUTES
    | MODIFIES
    | MONTH
    | MONTHS
    | MSCK
    | NAME
    | NAMESPACE
    | NAMESPACES
    | NANOSECOND
    | NANOSECONDS
    | NO
    | NONE
    | NORELY
    | NOT
    | NULL
    | NULLS
    | NUMERIC
    | OF
    | OFFSET
    | ONLY
    | OPTION
    | OPTIONS
    | OR
    | ORDER
    | OUT
    | OUTER
    | OUTPUTFORMAT
    | OVER
    | OVERLAPS
    | OVERLAY
    | OVERWRITE
    | PARTITION
    | PARTITIONED
    | PARTITIONS
    | PERCENTLIT
    | PIVOT
    | PLACING
    | POSITION
    | PRECEDING
    | PRIMARY
    | PRINCIPALS
    | PROCEDURE
    | PROCEDURES
    | PROPERTIES
    | PURGE
    | QUARTER
    | QUERY
    | RANGE
    | READS
    | REAL
    | RECORDREADER
    | RECORDWRITER
    | RECOVER
    | RECURSIVE
    | REDUCE
    | REFERENCES
    | REFRESH
    | RELY
    | RENAME
    | REPAIR
    | REPEAT
    | REPEATABLE
    | REPLACE
    | RESET
    | RESPECT
    | RESTRICT
    | RETURN
    | RETURNS
    | REVOKE
    | RLIKE
    | ROLE
    | ROLES
    | ROLLBACK
    | ROLLUP
    | ROW
    | ROWS
    | SCHEMA
    | SCHEMAS
    | SECOND
    | SECONDS
    | SECURITY
    | SELECT
    | SEPARATED
    | SERDE
    | SERDEPROPERTIES
    | SESSION_USER
    | SET
    | SETS
    | SHORT
    | SHOW
    | SINGLE
    | SKEWED
    | SMALLINT
    | SOME
    | SORT
    | SORTED
    | SOURCE
    | SPECIFIC
    | SQL
    | SQLEXCEPTION
    | SQLSTATE
    | START
    | STATISTICS
    | STORED
    | STRATIFY
    | STRING
    | STRUCT
    | SUBSTR
    | SUBSTRING
    | SYNC
    | SYSTEM_TIME
    | SYSTEM_VERSION
    | TABLE
    | TABLES
    | TABLESAMPLE
    | TARGET
    | TBLPROPERTIES
    | TEMPORARY
    | TERMINATED
    | THEN
    | TIME
    | TIMEDIFF
    | TIMESTAMP
    | TIMESTAMP_LTZ
    | TIMESTAMP_NTZ
    | TIMESTAMPADD
    | TIMESTAMPDIFF
    | TINYINT
    | TO
    | TOUCH
    | TRAILING
    | TRANSACTION
    | TRANSACTIONS
    | TRANSFORM
    | TRIM
    | TRUE
    | TRUNCATE
    | TRY_CAST
    | TYPE
    | UNARCHIVE
    | UNBOUNDED
    | UNCACHE
    | UNIQUE
    | UNKNOWN
    | UNLOCK
    | UNPIVOT
    | UNSET
    | UNTIL
    | UPDATE
    | USE
    | USER
    | VALUE
    | VALUES
    | VARCHAR
    | VAR
    | VARIABLE
    | VARIANT
    | VERSION
    | VIEW
    | VIEWS
    | VOID
    | WEEK
    | WEEKS
    | WHILE
    | WHEN
    | WHERE
    | WINDOW
    | WITH
    | WITHIN
    | YEAR
    | YEARS
    | ZONE
//--DEFAULT-NON-RESERVED-END
    ;
