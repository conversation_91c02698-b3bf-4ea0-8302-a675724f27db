/*
IBM Db2 SQL grammar.
The MIT License (MIT).

Copyright (c) 2023, <PERSON><PERSON><PERSON>.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:
The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.
THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/

parser grammar Db2Parser;

options { tokenVocab=Db2Lexer; }

//db2_file
//    : batch* EOF
//    ;
//
//batch
//    : sql_command SEMI?
//    ;


// https://www.ibm.com/docs/en/db2/12.1?topic=sql-statements

root
    : (
        alterAuditPolicy
        | alterBufferpool
        | alterDatabasePartitionGroup
        | alterDatabase
        | alterEventMonitor
        | alterFunction
        | alterHistogramTemplate
        | alterIndex
        | alterMask
        | alterMethod
        | alterModel
        | alterModule
        | alterNickname
        | alterPackage
        | alterPermission
        | alterProcedureExternal
        | alterProcedureSourced
        | alterProcedureSql
        | alterSchema
        | alterSecurityLabelComponent
        | alterSecurityPolicy
        | alterSequence
        | alterServer
        | alterServiceClass
        | alterStogroup
        | alterTable
        | alterTablespace
        | alterThreshold
        | alterTrigger
        | alterTrustedContext
        | alterType
        | alterUsageList
        | alterUserMapping
        | alterView
        | alterWorkActionSet
        | alterWorkClassSet
        | alterWorkload
        | alterWrapper
        | alterXsrobject
        | createAlias
        | createAuditPolicy
        | createBufferpool
        | createDatabasePartitionGroup
        | createEventMonitor
        | createEventMonitorActivities
        | createEventMonitorChangeHistory
        | createEventMonitorLocking
        | createEventMonitorPackageCache
        | createEventMonitorStatistics
        | createEventMonitorThreshold
        | createEventMonitorUnitOfWork
        | createExternalTable
        | createFunction
        | createFunctionMapping
        | createGlobalTemporaryTable
        | createHistogramTemplate
        | createIndex
        | createIndexExtension
        | createMask
        | createMethod
        | createModule
        | createNickname
        | createPermission
        | createProcedure
        | createRole
        | createSchema
        | createSecurityLabelComponent
        | createSecurityLabel
        | createSecurityPolicy
        | createSequence
        | createServiceClass
        | createServer
        | createStogroup
        | createSynonym
        | createTable
        | createTablespace
        | createTenant
        | createThreshold
        | createTransform
        | createTrigger
        | createTrustedContext
        | createType
        | createTypeMapping
        | createUsageList
        | createUserMapping
        | createVariable
        | createView
        | createWorkActionSet
        | createWorkClassSet
        | createWorkload
        | createWrapper
        | drop
        | select
        | insert
        | update
        | delete
        | merge
        | truncate
        | grant
        | revokeBufferPoolPrivileges
        | revokeDatabaseAuthorities
        | revokeExemption
        | revokeGlobalVariablePrivileges
        | revokeIndexPrivileges
        | revokeModulePrivileges
        | revokePackagePrivileges
        | revokeRole
        | revokeRoutinePrivileges
        | revokeSchemaPrivileges
        | revokeSecurityLabel
        | revokeSequencePrivileges
        | revokeServerPrivileges
        | revokeSetsessionuserPrivileges
        | revokeStorageGroupAuthority
        | revokeTablespacePrivileges
        | revokeTableViewOrNicknamePrivileges
        | revokeTenantPrivileges
        | revokeWorkloadPrivileges
        | revokeXsrObjectPrivileges
        | commit
        | rollback
        | savepoint
        | releaseSavepoint
        | allocate
        | analyze
        | associateLocators
        | audit
        | beginDeclareSection
        | call
        | close
        | comment
        | connectType1
        | connectType2
        | declareCursor
        | declareGlobalTemporaryTable
        | describe
        | disconnect
        | endDeclareSection
        | execute
        | executeImmediate
        | explain
        | fetch
        | flushBufferpools
        | flushEventMonitor
        | flushFederatedCache
        | flushOptimizationProfileCache
        | flushPackageCache
        | flushAuthenticationCache
        | freeLocator
        | getDiagnostics
        | lockTable
        | pipe
        | refreshTable
        | releaseConnection
        | rename
        | renameStogroup
        | renameTablespace
        | set)
     SEMI_? EOF
    ;

select
    : fullselect
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-delete
delete
    : DELETE FROM tableReference includeColumns? assignmentClause? whereClause? orderByClause? offsetClause? fetchClause? (WITH (RR | RS | CS | UR))?
    ((SKIP_ LOCKED DATA?)? | (WAIT FOR OUTCOME)? | NOWAIT? | (WAIT waitTime)? )
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-insert
insert
    : INSERT INTO tableReference insertColumns? (valuesClause | ((WITH tableName (COMMA_ tableName)*?)? | subselect )) (WITH (RR | RS | CS | UR))?
    ;

insertColumns
    : LP_ (columnName (COMMA_ columnName)*?) RP_ includeColumns?
    ;

includeColumns
    : INCLUDE (columnName dataType (COMMA_ columnName dataType)*?)
    ;

merge
    : MERGE INTO tableReference correlationClause? USING tableReference ON searchCondition caseWhen* (ELSE IGNORE)? (WITH (RR | RS | CS | UR))?
    ;

truncate
    : TRUNCATE TABLE? tableName ((DROP | REUSE) STORAGE)?
        ((IGNORE | RESTRICT WHEN) DELETE TRIGGERS)? (CONTINUE IDENTITY)?
        IMMEDIATE?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-update
update
    : UPDATE tableReference correlationClause? (searchedUpdate | positionedUpdate)
    ;

searchedUpdate
    : includeColumns? SET assignmentClause (FROM (tableReference (COMMA_ tableReference)*?))? whereClause? orderByClause? offsetClause? fetchClause?
    (WITH (RR | RS | CS | UR))? ((SKIP_ LOCKED DATA?)? | (WAIT FOR OUTCOME)? | NOWAIT? | (WAIT waitTime)? )
    ;

positionedUpdate
    : SET assignmentClause WHERE CURRENT OF cursorName
    ;

assignmentClause
    : assignmentWithoutBraces | assignmentWithBraces
    ;

columnAttribute
    : columnName (DOT_ attributeName)*?
    ;

assignmentExpr
    : expr | NULL_ | DEFAULT
    ;

assignmentWithoutBraces
    : (columnAttribute EQ_ assignmentExpr) (COMMA_ assignmentWithoutBraces)*?
    ;

assignmentWithBraces
    : LP_ (columnAttribute (COMMA_ columnAttribute)*?) RP_ EQ_ LP_ ((assignmentExpr (COMMA_ assignmentExpr)*?) | fullselect) RP_
    ;

grant
    : grantBufferPoolPrivileges
    | grantDatabaseAuthorities
    | grantExemption
    | grantGlobalVariablePrivileges
    | grantIndexPrivileges
    | grantModulePrivileges
    | grantPackagePrivileges
    | grantRole
    | grantRoutinePrivileges
    | grantSchemaPrivileges
    | grantSecurityLabel
    | grantSequencePrivileges
    | grantServerPrivileges
    | grantSetsessionuserPrivileges
    | grantStorageGroupAuthority
    | grantTablespacePrivileges
    | grantTableViewOrNicknamePrivileges
    | grantTenantPrivileges
    | grantWorkloadPrivileges
    | grantXsrObjectPrivileges
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-buffer-pool-privileges
grantBufferPoolPrivileges
    : GRANT USE ON BUFFERPOOL bufferpoolName TO granteeList
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-database-authorities
grantDatabaseAuthorities
    : GRANT dbPrivilegeList ON DATABASE TO granteeListPublic
    ;

dbPrivilegeList
    : dbPrivilege (COMMA_ dbPrivilege)*
    ;

dbPrivilege
    : ACCESSCTRL
    | BINDADD
    | CONNECT
    | CREATETAB
    | CREATE_EXTERNAL_ROUTINE
    | CREATE_NOT_FENCED_ROUTINE
    | CREATE_SOURCE_OBJECT
    | CREATE_SECURE_OBJECT
    | DATAACCESS
    | DBADM (WITH | WITHOUT)? (DATAACCESS | ACCESSCTRL)?
    | EXPLAIN
    | IMPLICIT_SCHEMA
    | LOAD
    | QUIESCE_CONNECT
    | SECADM
    | SQLADM
    | WLMADM
    ;

grantee
    : userGroupRole? authorizationName
    ;

granteeUserGroup
    : userGroup (authorizationName (COMMA_ authorizationName)*)
    ;

userGroup
    : USER
    | GROUP
    ;

granteeList
    : grantee (COMMA_ grantee)*
    ;

granteeListPublic
    : (granteeList | PUBLIC) (COMMA_ granteeListPublic)*
    ;

granteeListUserGroup
    : granteeUserGroup (COMMA_ granteeUserGroup)?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-exemption
grantExemption
    : GRANT EXEMPTION ON RULE exemptionPrivilege FOR policyName TO granteeList
    ;

exemptionPrivilege
    : DB2LBACREADARRAY
    | DB2LBACREADSET
    | DB2LBACREADTREE
    | DB2LBACWRITEARRAY (WRITEDOWN | WRITEUP)
    | DB2LBACWRITESET
    | DB2LBACWRITETREE
    | ALL
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-global-variable-privileges
grantGlobalVariablePrivileges
    : GRANT variablePrivilege ON VARIABLE variableName TO granteeListPublic withGrantOption?
    ;

variablePrivilege
    : ALL PRIVILEGES?
    | readWrite (COMMA_ readWrite)?
    ;

readWrite
    : READ
    | WRITE
    ;

withGrantOption
    : WITH GRANT OPTION
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-index-privileges
grantIndexPrivileges
    : GRANT CONTROL ON INDEX indexName TO granteeListPublic
    ;

// http://ibm.com/docs/en/db2/12.1.0?topic=statements-grant-module-privileges
grantModulePrivileges
    : GRANT EXECUTE ON MODULE moduleName TO granteeListPublic withGrantOption?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-package-privileges
grantPackagePrivileges
    : GRANT packagePrivilegeList ON (PACKAGE | PROGRAM) packageName TO granteeListPublic withGrantOption?
    ;

packagePrivilegeList
    : packagePivilege (COMMA_ packagePivilege)*
    ;

packagePivilege
    : BIND
    | CONTROL
    | EXECUTE
    | RUN
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-role
grantRole
    : GRANT ROLE? roleList TO granteeListPublic withGrantOption?
    ;

roleList
    : roleName (COMMA_ roleName)*
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-routine-privileges
grantRoutinePrivileges
    : GRANT EXECUTE ON ( functionDesignator
                       | FUNCTION (schema DOT_)? ASTERISK_
                       | methodDesignator
                       | METHOD ASTERISK_ FOR (typeName | (schema DOT_)? ASTERISK_)
                       | procedureDesignator
                       | PROCEDURE (schema DOT_)? ASTERISK_
                       ) TO granteeListPublic withGrantOption?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-schema-privileges-authorities
grantSchemaPrivileges
    : GRANT ( ALL PRIVILEGES?
            | schemaPrivilegeList
            ) ON (SCHEMA schemaName | CURRENT SCHEMA) TO granteeListPublic withGrantOption?
    ;

schemaPrivilegeList
    : schemaPrivilege (COMMA_ schemaPrivilege)*
    ;

schemaPrivilege
    : ACCESSCTRL
    | ALTERIN
    | CREATEIN
    | DATAACCESS
    | DELETEIN
    | DROPIN
    | EXECUTEIN
    | INSERTIN
    | LOAD
    | SCHEMAADM
    | SELECTIN
    | UPDATEIN
    | USAGE
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-security-label
grantSecurityLabel
    : GRANT SECURITY LABEL securityLabelName TO granteeList (FOR (ALL | READ | WRITE) ACCESS)?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-sequence-privileges
grantSequencePrivileges
    : GRANT sequencePrivilegeList ON SEQUENCE sequenceName TO granteeListPublic withGrantOption?
    ;

sequencePrivilegeList
    : sequencePrivilege (COMMA_ sequencePrivilege)?
    ;

sequencePrivilege
    : USAGE
    | ALTER
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-server-privileges
grantServerPrivileges
    : GRANT PASSTHRU ON SERVER serverName TO granteeListPublic
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-setsessionuser-privilege
grantSetsessionuserPrivileges
    : GRANT SETSESSIONUSER ON (userList | PUBLIC) TO granteeListUserGroup
    ;

userList
    : userAuth (COMMA_ userAuth)*
    ;

userAuth
    : USER sessionAuthorizationName
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-storage-group-authority
grantStorageGroupAuthority
    : GRANT TBSPACEADM ON STOGROUP storagegroupName TO (userGroupRole authorizationName (COMMA_ userGroupRole authorizationName)*?)
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-table-space-privileges
grantTablespacePrivileges
    : GRANT USE OF TABLESPACE tablespaceName TO granteeListPublic withGrantOption?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-table-view-nickname-privileges
grantTableViewOrNicknamePrivileges
    : GRANT ( ALL PRIVILEGES?
            | tvnPrivilegeList
            ) ON TABLE? (tableName | viewName | nickName) TO granteeListPublic withGrantOption?
    ;

tvnPrivilegeList
    : tvnPrivilege (COMMA_ tvnPrivilege)*
    ;

tvnPrivilege
    : ALTER
    | CONTROL
    | DELETE
    | INDEX
    | INSERT
    | REFERENCES columnListParen?
    | SELECT
    | UPDATE columnListParen?
    ;

columnListParen
    : LP_ columnList RP_
    ;

columnList
    : columnName (COMMA_ columnName)*
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-tenant-privileges
grantTenantPrivileges
    : GRANT (ALL PRIVILEGES? | schemaPrivilegeList) ON TENANT tenantName TO granteeListPublic
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-workload-privileges
grantWorkloadPrivileges
    : GRANT USAGE ON WORKLOAD workloadName TO granteeListPublic
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-grant-xsr-object-privileges
grantXsrObjectPrivileges
    : GRANT USAGE ON XSROBJECT xsrobjectName TO PUBLIC
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-buffer-pool-privileges
revokeBufferPoolPrivileges
    : REVOKE USE ON (BUFFERPOOL | BUFFER POOL) bufferpoolName FROM granteeList byALL?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-database-authorities
revokeDatabaseAuthorities
    : REVOKE dbPrivilegeList ON DATABASE FROM granteeListPublic byALL?
    ;

byALL
    : BY ALL
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-exemption
revokeExemption
    : REVOKE EXEMPTION ON RULE exemptionPrivilege FOR policyName FROM granteeList
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-global-variable-privileges
revokeGlobalVariablePrivileges
    : REVOKE variablePrivilege ON VARIABLE variableName FROM granteeListPublic byALL? RESTRICT?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-index-privileges
revokeIndexPrivileges
    : REVOKE CONTROL ON INDEX indexName FROM granteeListPublic byALL?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-module-privileges
revokeModulePrivileges
    : REVOKE EXECUTE ON MODULE moduleName FROM granteeListPublic
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-package-privileges
revokePackagePrivileges
    : REVOKE packagePivilege ON (PACKAGE | PROGRAM) packageName FROM granteeListPublic byALL?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-role
revokeRole
    : REVOKE (ADMIN OPTION FOR)? ROLE? roleList FROM granteeListPublic byALL?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-routine-privileges
revokeRoutinePrivileges
    : REVOKE EXECUTE ON ( functionDesignator
                        | FUNCTION (schema DOT_)? ASTERISK_
                        | methodDesignator
                        | METHOD ASTERISK_ FOR (typeName | (schema DOT_)? ASTERISK_)
                        | procedureDesignator
                        | PROCEDURE (schema DOT_)? ASTERISK_
                        ) FROM  granteeListPublic byALL? RESTRICT
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-schema-privileges-authorities
revokeSchemaPrivileges
    : REVOKE ( ALL PRIVILEGES?
             | schemaPrivilegeList
             ) ON (SCHEMA schemaName | CURRENT SCHEMA) FROM granteeListPublic byALL?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-security-label
revokeSecurityLabel
    : REVOKE SECURITY LABEL securityLabelName FROM granteeListPublic
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-sequence-privileges
revokeSequencePrivileges
    : REVOKE sequencePrivilegeList ON SEQUENCE sequenceName FROM granteeListPublic byALL? RESTRICT?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-server-privileges
revokeServerPrivileges
    : REVOKE PASSTHRU ON SERVER serverName FROM granteeListPublic byALL?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-setsessionuser-privilege
revokeSetsessionuserPrivileges
    : REVOKE SETSESSIONUSER ON userList FROM granteeListUserGroup
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-storage-group-authority
revokeStorageGroupAuthority
    : REVOKE TBSPACEADM ON STORAGE GROUP storagegroupName FROM granteeList byALL?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-table-space-privileges
revokeTablespacePrivileges
    : REVOKE USE OF TABLESPACE tablespaceName FROM granteeListPublic byALL?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-table-view-nickname-privileges
revokeTableViewOrNicknamePrivileges
    : REVOKE ( ALL PRIVILEGES?
             | tvnPrivilegeList
             ) ON TABLE? (tableName | viewName | nickName) FROM granteeListPublic byALL?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-tenant-privileges
revokeTenantPrivileges
    : REVOKE (ALL PRIVILEGES? | schemaPrivilegeList) ON TENANT tenantName FROM granteeListPublic byALL
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-workload-privileges
revokeWorkloadPrivileges
    : REVOKE USAGE ON WORKLOAD workloadName FROM granteeListPublic byALL?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-revoke-xsr-object-privileges
revokeXsrObjectPrivileges
    : REVOKE USAGE ON XSROBJECT xsrobjectName FROM PUBLIC byALL?
    ;

userGroupRole
    : USER
    | GROUP
    | ROLE
    ;


rollback
    : ROLLBACK WORK? (TO SAVEPOINT savepointName?)?
    ;

savepoint
    : SAVEPOINT savepointName UNIQUE? ON ROLLBACK RETAIN CURSORS (ON ROLLBACK RETAIN LOCKS)?
    ;

releaseSavepoint
    : RELEASE TO? SAVEPOINT savepointName
    ;


allocate
    : ALLOCATE cursorName CURSOR FOR RESULT SET resultsetName
    ;

resultsetName
    : identifier
    ;

//allocate_cursor
//    : ALLOCATE cursorName CURSOR FOR RESULT SET rsLocatorVariable
//    ;

//alter_audit_policy
//    : ALTER AUDIT POLICY policyName
//        ( CATEGORIES ( ALL
//                     | AUDIT
//                     | CHECKING
//                     | CONTEXT
//                     | EXECUTE (WITH DATA)?
//                     | OBJMAINT
//                     | SECMAINT
//                     | SYSADMIN
//                     | VALIDATE
//                     )
//                     STATUS ( BOTH
//                            | FAILURE
//                            | NONE
//                            | SUCCESS
//                            )
//
//        )
//        | ERROR TYPE (NORMAL | AUDIT)
//    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-audit-policy
alterAuditPolicy
    : ALTER AUDIT POLICY policyName (auditPolicyCategories | auditPolicyErrorType)
    ;


auditPolicyCategories
    : CATEGORIES categories (COMMA_ categories)*
    ;

categories
    : ( ALL
        | AUDIT
        | CHECKING
        | CONTEXT
        | EXECUTE (WITH DATA)?
        | OBJMAINT
        | SECMAINT
        | SYSADMIN
        | VALIDATE
        )
        STATUS ( BOTH
               | FAILURE
               | NONE
               | SUCCESS
               )

    ;


auditPolicyErrorType
    : ERROR TYPE (NORMAL | AUDIT)
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-bufferpool
alterBufferpool
    : ALTER BUFFERPOOL bufferpoolName
        ( (IMMEDIATE | DEFERRED)? (MEMBER memberNumber)? SIZE (numberOfPages | numberOfPages? AUTOMATIC)
        | addDbPartitionGroup
        | numBlockPagesClause
        | BLOCKSIZE numberOfPages
        )

    ;


memberNumber
    : intValue
    ;

addDbPartitionGroup
    : ADD DATABASE PARTITION GROUP dbPartitionGroupName
    ;

numBlockPagesClause
    : NUMBLOCKPAGES numberOfPages (BLOCKSIZE numberOfPages)?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-database-partition-group
alterDatabasePartitionGroup
    : ALTER DATABASE PARTITION GROUP dbPartitionName dbPartitionGroupListItem (COMMA_? dbPartitionGroupListItem)*
    ;

dbPartitionGroupListItem
    : ADD dbPartitionNumNums dbPartitionClause dbPartitionOptions?
    | DROP dbPartitionNumNums dbPartitionClause
    ;

dbPartitionNumNums
    : DBPARTITIONNUM
    | DBPARTITIONNUMS
    ;

dbPartitionClause
    : LP_ dbPartitionNumberList RP_
    ;

dbPartitionOptions
    : LIKE DBPARTITIONNUM dbPartitionNumber
    | WITHOUT TABLESPACES
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-database
alterDatabase
    : ALTER DATABASE databaseName?
        alterDatabaseOpts+
    ;

alterDatabaseOpts
    : (ADD | DROP) STORAGE ON stringList
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-event-monitor
alterEventMonitor
    : ALTER EVENT MONITOR eventMonitorName alterEventMonitorOpts+
    ;

alterEventMonitorOpts
    : ADD LOGICAL GROUP evmGroup ((LP_ targetTableOptions+ RP_) | (targetTableOptions))?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-function
alterFunction
    : ALTER functionDesignator alterFunctionOpts+
    ;

alterFunctionOpts
    : EXTERNAL NAME (string | identifier)
    | NOT? (FENCED | SECURED | THREADSAFE)
    ;

functionDesignator
    : FUNCTION functionName dataTypeListParen?
    | SPECIFIC FUNCTION specificName
    ;

dataTypeList
    : dataType (COMMA_ dataType)*
    ;

dataTypeListParen
    : LP_ dataTypeList? RP_
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-histogram-template
alterHistogramTemplate
    : ALTER HISTOGRAM TEMPLATE templateName HIGH BIN VALUE bigintConstant
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-index
alterIndex
    : ALTER INDEX indexName COMPRESS yesNo
    ;

yesNo
    : YES
    | NO
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-mask
alterMask
    : ALTER MASK maskName enableDisable
    ;

enableDisable
    : ENABLE
    | DISABLE
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-method
alterMethod
    : ALTER methodDesignator EXTERNAL NAME (string | identifier)
    ;

methodDesignator
    : METHOD methodName dataTypeListParen? FOR typeName
    | SPECIFIC METHOD specificName
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-model
alterModel
    : ALTER MODEL (modelName | (ON tableName)) (enableDisable | REVERT)
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-module
alterModule
    : ALTER MODULE moduleName
        ( ADD alterModuleOpts
        | DROP (BODY | moduleObjectIdentification)
        | PUBLISH alterModuleOpts
        )
    ;

alterModuleOpts
    : moduleConditionDefinition
    | moduleFunctionDefinition
    | moduleProcedureDefinition
    | moduleTypeDefinition
    | moduleVariableDefinition
    ;

moduleFunctionDefinition
    : (FUNCTION functionName (LP_ (dataType (COMMA_ dataType)*)? RP_)? )
    | (SPECIFIC FUNCTION functionName)
    | (FUNCTION functionName LP_ aggFnParamDecl (COMMA_ aggFnParamDecl)* RP_
               ( RETURNS (dataType | dataType CAST FROM dataType) aggFnOptionList?
               | AGGREGATE WITH LP_ stateVariableDeclaration (COMMA_ stateVariableDeclaration)* RP_ USING
                   (IN MODULE moduleName)? INITIALIZE procedureDesignator ACCUMULATE procedureDesignator
                       MERGE procedureDesignator FINALIZE functionDesignator
               ))
    | (FUNCTION functionName LP_ extScalarParamDecl (COMMA_ extScalarParamDecl)* RP_
               RETURNS (dataType asLocator? | dataType CAST FROM dataType asLocator?)
               extScalarOptionList)
    | (FUNCTION functionName LP_ extTableParamDeclList RP_
               RETURNS ((TABLE LP_ columnName dataType asLocator? (COMMA_ columnName dataType asLocator?)* RP_) | GENERIC TABLE)
               extTableOptionList)
    | (FUNCTION functionName LP_ paramDeclList3 RP_
               RETURNS TABLE LP_ columnName dataType (COMMA_ columnName dataType)* RP_ oledbOptionList)
    ;

moduleProcedureDefinition
    : (PROCEDURE functionName (LP_ (columnName dataType (COMMA_ columnName dataType)*)? RP_)? )
    | (SPECIFIC PROCEDURE functionName)
    ;

moduleTypeDefinition
    : TYPE (typeName AS dataType ARRAY LBT_ (INTEGER_ | intValue | dataType)? RBT_)
    | TYPE (typeName AS ROW (fieldDefinitionListParen | anchoredDataType))
    | TYPE (distinctTypeName AS sourceDataType (WITH STRONG TYPE RULES | WITH WEAK TYPE RULES dataTypeConstrainst)?)
    | TYPE (typeName AS (anchoredDataType | rowTypeName)? CURSOR)
    ;

moduleVariableDefinition
    : VARIABLE variableName dataType
                  ( (DEFAULT | CONSTANT)? NULL_
                  | (DEFAULT | CONSTANT) ( constant_
                                         | specialRegister
                                         | globalVariable
                                         | LP_ cursorValueConstructor RP_
                                         | LP_ expr RP_
                                         )
                  )?
    ;

moduleConditionDefinition
    : CONDITION conditionName (FOR (SQLSTATE VALUE?)? string)?
    ;

moduleObjectIdentification
    : moduleFunctionDesignator
    | moduleProcedureDesignator
    | CONDITION conditionName
    | TYPE typeName
    | VARIABLE variableName
    ;

moduleFunctionDesignator
    : FUNCTION unqualifiedFunctionName dataTypeListParen?
    | SPECIFIC FUNCTION unqualifiedSpecificName
    ;

moduleProcedureDesignator
    : PROCEDURE unqualifiedProcedureName dataTypeListParen?
    | SPECIFIC PROCEDURE unqualifiedSpecificName
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-nickname
alterNickname
    : ALTER NICKNAME nickName
        alterNicknameOpts1?
        alterNicknameOpts2*
    ;

alterNicknameOpts1
    : OPTIONS LP_ alterNicknameOpts1Item (COMMA_ alterNicknameOpts1Item)* RP_
    ;

alterNicknameOpts1Item
    : addSet? nickNameOptionName string
    | DROP nickNameOptionName
    ;

alterNicknameOpts2
    : ALTER COLUMN? columnName alterNicknameOpts2Item (COMMA_ alterNicknameOpts2Item)*
    | ADD (uniqueConstraint | referentialConstraint | checkConstraint)
    | ALTER (FOREIGN KEY | CHECK) constraintName constraintAlteration+
    | DROP (PRIMARY KEY | (FOREIGN KEY | UNIQUE | CHECK | CONSTRAINT) constraintName)
    | allowDisallow CACHING
    ;

alterNicknameOpts2Item
    : LOCAL NAME columnName
    | LOCAL TYPE localDataType
    | federatedColumnOptions
    ;

constraintAlteration
    : enableDisable QUERY OPTIMIZATION
    | NOT ENFORCED (NOT? TRUSTED)?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-package
alterPackage
    : ALTER PACKAGE packageName (VERSION? versionId)?
        alterPackageOpts*
    ;

alterPackageOpts
    : ACCESS PLAN REUSE yesNo
    | OPTIMIZATION PROFILE (NONE | optimizationProfileName)
    | KEEP DYNAMIC yesNo
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-permission
alterPermission
    : ALTER PERMISSION permissionName enableDisable
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-procedure-external
alterProcedureExternal
    : ALTER procedureDesignator alterProcedureExternalOpts+
    ;

alterProcedureExternalOpts
    : EXTERNAL NAME (string | identifier)
    | NOT? FENCED
    | NO? EXTERNAL ACTION
    | NOT? THREADSAFE
    | NEW SAVEPOINT LEVEL
    ;

procedureDesignator
    : PROCEDURE procedureName dataTypeListParen?
    | SPECIFIC PROCEDURE specificName
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-procedure-sourced
alterProcedureSourced
    : ALTER procedureDesignator (ALTER PARAMETER parameterAlteration)+
    ;

parameterAlteration
    : parameterName SET DATA TYPE dataType
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-procedure-sql
alterProcedureSql
    : ALTER procedureDesignator (NO? EXTERNAL ACTION | NEW SAVEPOINT LEVEL)+
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-schema
alterSchema
    : ALTER SCHEMA schemaName DATA CAPTURE (NONE | CHANGES) (ENABLE ROW MODIFICATION TRACKING)?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-security-label-component
alterSecurityLabelComponent
    : ALTER SECURITY LABEL COMPONENT componentName addElementClause
    ;

addElementClause
    : ADD ELEMENT string (arrayElementClause | treeElementClause)?
    ;

arrayElementClause
    : (BEFORE | AFTER) string
    ;

treeElementClause
    : ROOT
    | UNDER string (OVER string (COMMA_ OVER? string)*)?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-security-policy
alterSecurityPolicy
    : ALTER SECURITY POLICY securityPolicyName alterSecurityPolicyOpts+
    ;

alterSecurityPolicyOpts
    : ADD (SECURITY LABEL)? COMPONENT componentName
    | (OVERRIDE_ | RESTRICT) NOT AUTHORIZED WRITE SECURITY LABEL
    | (USE | IGNORE) (GROUP | ROLE) AUTHORIZATIONS
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-sequence
alterSequence
    : ALTER SEQUENCE sequenceName alterSequenceOpts+
    ;

alterSequenceOpts
    : RESTART (WITH intValue)?
    | INCREMENT BY intValue
    | (MINVALUE intValue | NO MINVALUE)
    | (MAXVALUE intValue | NO MAXVALUE)
    | NO? CYCLE
    | (CACHE intValue | NO CACHE)
    | NO? ORDER
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-server
alterServer
    : ALTER SERVER ( serverName (VERSION serverVersion)?
                   | TYPE serverType (VERSION serverVersion (WRAPPER wrapperName)?)?
                   )
        (OPTIONS LP_ alterServerOpts (COMMA_ alterServerOpts)* RP_)?
    ;

alterServerOpts
    : addSet? serverOptionName string
    | DROP serverOptionName
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-service-class
alterServiceClass
    : ALTER SERVICE CLASS_ serviceClassName (UNDER serviceSuperclassName)?
        alterServiceClassOpts+
    ;

alterServiceClassOpts
    : softHard? RESOURCE SHARES intValue
    | softHard? CPU SHARES intValue
    | CPU LIMIT (intValue | NONE)
    | ACTIVITY SORTMEM LIMIT (intValue | NONE)
    | MINIMUM RESOURCE SHARE intValue PERCENT
    | ADMISSION QUEUE ORDER (FIFO | LATENCY)
    | DEGREE_ SCALEBACK defaultOnOff?
    | MAXIMUM DEGREE_ defaultOnOff?
    | PREFETCH PRIORITY defaultHighMediumLow
    | OUTBOUND CORRELATOR (NONE | string)
    | BUFFERPOOL PRIORITY defaultHighMediumLow
    | COLLECT AGGREGATE ACTIVITY DATA extendedBaseNone?
    | COLLECT AGGREGATE REQUEST DATA baseNone?
    | COLLECT AGGREGATE UNIT OF WORK DATA baseNone?
    | COLLECT REQUEST METRICS extendedBaseNone?
    | ACTIVITY (LIFETIME | QUEUETIME | ESTIMATEDCOST | EXECUTETIME | INTERARRIVALTIME) HISTOGRAM TEMPLATE templateName
    | REQUEST EXECUTETIME HISTOGRAM TEMPLATE templateName
    | UOW LIFETIME HISTOGRAM TEMPLATE templateName
    | enableDisable
    ;

defaultOnOff
    : DEFAULT
    | onOff
    ;

defaultHighMediumLow
    : DEFAULT
    | highMediumLow
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-stogroup
alterStogroup
    : ALTER STOGROUP storagegroupName alterStogroupOpts+
    ;

alterStogroupOpts
    : (ADD | DROP) stringList
    | OVERHEAD numberOfMilliseconds
    | DEVICE READ RATE numberMegabytesPerSecond
    | DATA TAG (intValue | NONE)
    | SET AS DEFAULT
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-table
alterTable
    : ALTER DATALAKE? TABLE tableName
        ( alterTableOpts+
        | ADD PARTITION addPartition
        | ATTACH PARTITION attachPartition
        | DETACH PARTITION partitionName INTO tableName
        | ADD SECURITY POLICY policyName
        | DROP SECURITY POLICY
        | ADD VERSIONING USE HISTORY TABLE historyTableName
        | DROP VERSIONING
        )
    ;

alterTableOpts
    : ADD ( COLUMN? columnDefinition
          | uniqueConstraint
          | referentialConstraint
          | checkConstraint
          | distributionClause
          | RESTRICT ON DROP
          )
    | ADD (MATERIALIZED? QUERY)? materializedQueryDefinition
    | ALTER (FOREIGN KEY | CHECK) constraintName constraintAlteration
    | ALTER COLUMN? columnAlteration
    | activateDeactivate (ROW | COLUMN) ACCESS CONTROL
    | RENAME s=columnName? TO t=columnName
    | DROP ( PRIMARY KEY
           | (FOREIGN KEY | UNIQUE | CHECK | CONSTRAINT) constraintName
           | COLUMN? columnName cascadeRestrict?
           | RESTRICT ON DROP
           )
    | DROP DISTRIBUTION
    | DROP MATERIALIZED? QUERY
    | ADD PERIOD periodDefinitionAlter
    | DROP PERIOD periodName
    | DATA CAPTURE (NONE | (CHANGES | CHANGE) (INCLUDE LONGVAR COLUMNS)?)
    | ACTIVITY NOT LOGGED INITIALLY (WITH EMPTY TABLE)?
    | PCTFREE intValue
    | LOCKSIZE (ROW | BLOCKINSERT | TABLE)
    | APPEND onOff
    | NOT? VOLATILE CARDINALITY?
    | COMPRESS (YES (ADAPTIVE | STATIC) | NO)
    | activateDeactivate VALUE COMPRESSION
    | LOG INDEX BUILD nullOnOff
    ;

nullOnOff
    : NULL_
    | onOff
    ;

cascadeRestrict
    : CASCADE
    | RESTRICT
    ;

materializedQueryDefinition
    : LP_ fullselect RP_ refreshableTableOptions*
    ;

refreshableTableOptions
    : DATA INITIALLY DEFERRED
    | REFRESH (DEFERRED | IMMEDIATE)
    | enableDisable QUERY OPTIMIZATION
    | MAINTAINED BY (USER | REPLICATION | FEDERATED_TOOL | SYSTEM)
    ;

columnAlteration
    : columnName ( SET ( DATA TYPE
                        | NOT NULL_
                        | INLINE LENGTH intValue
                        | defaultClause
                        | EXPRESSION asGeneratedExpressionClause
                        | (NOT | IMPLICITLY) HIDDEN_
                        )
                  | SET generationAlteration
                  | (SET generationAlteration)? identityAlteration
                  | SET generationAttribute asIdentityClause
                  | SET GENERATE ALWAYS? (asGeneratedExpressionClause | asRowTransactionStartIdentifierclause | asRowTransactionTimestampClause)
                  | DROP (DEFAULT | GENERATED | NOT NULL_)
                  | ADD SCOPE (typedTableName | typedViewName)
                  | COMPRESS (SYSTEM DEFAULT | OFF)
                  | SECURED WITH securityLabelName
                  | DROP COLUMN SECURITY
                  )
    ;

generationAlteration
    : SET GENERATED (ALWAYS | BY DEFAULT)
    ;

identityAlteration
    : SET INCREMENT BY intValue
    | SET (NO MINVALUE | MINVALUE intValue)
    | SET (NO MAXVALUE | MAXVALUE intValue)
    | SET NO? CYCLE
    | SET (NO CACHE | CACHE intValue)
    | SET NO? ORDER
    | RESTART (WITH intValue)?
    ;

generationAttribute
    : GENERATED (ALWAYS | BY DEFAULT)?
    ;

asIdentityClause
    : AS IDENTITY (LP_ asIdentityClauseOpts+ RP_)?
    ;

asIdentityClauseOpts
    : START WITH intValue
    | INCREMENT BY intValue
    | (NO MINVALUE | MINVALUE intValue)
    | (NO MAXVALUE | MAXVALUE intValue)
    | NO? CYCLE
    | (NO CACHE | CACHE intValue)
    ;

periodDefinitionAlter
    : (SYSTEM_TIME | BUSINESS_TIME) LP_ b=columnName COMMA_ e=columnName RP_
    ;

addPartition
    : partitionName? boundarySpecAlter (IN tablespaceName)
        (INDEX IN tablespaceName (LONG IN tablespaceName)?)?
    ;

boundarySpecAlter
    : startingClause endingClause
    | endingClause
    ;

attachPartition
    : partitionName? boundarySpecAlter FROM tableName (REQUIRE MATCHING INDEXES)?
    ;

activateDeactivate
    : ACTIVATE
    | DEACTIVATE
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-tablespace
alterTablespace
    : ALTER TABLESPACE tablespaceName alterTablespaceOpts+
    ;

alterTablespaceOpts
    : ADD addClause
    | BEGIN NEW STRIPE SET dbContainerClause onDbPartitionsClause?
    | DROP dropContainerClause onDbPartitionsClause?
    | REDUCE (dbContainerClause | allContainersClause | MAX | STOP | intValue kmgPercent?)? onDbPartitionsClause?
    | (EXTEND | RESIZE) (dbContainerClause | allContainersClause) onDbPartitionsClause?
    | REBALANCE (SUSPEND | RESUME)?
    | PREFETCHSIZE (AUTOMATIC | numberOfPages | intValue km)
    | BUFFERPOOL bufferpoolName
    | OVERHEAD (numberOfMilliseconds | INHERIT)
    | TRANSFERRATE (numberOfMilliseconds | INHERIT)
    | NO? FILE SYSTEM CACHING
    | DROPPED TABLE RECOVERY yesNo
    | SWITCH ONLINE
    | AUTORESIZE yesNo
    | INCREASESIZE intValue kmgPercent
    | MAXSIZE (intValue kmg | NONE)
    | CONVERT TO LARGE
    | LOWER HIGH WATER MARK STOP?
    | USING STOGROUP storagegroupName
    | DATA TAG (intValue | INHERIT | NONE)
    | MANAGED BY AUTOMATIC STORAGE
    ;

addClause
    : (TO STRIPE SET stripeset)? dbContainerClause onDbPartitionsClause?
    | systemContainerClause onDbPartitionsClause
    ;

dbContainerClause
    : LP_ dbContainerClauseOpts (COMMA_ dbContainerClauseOpts)* RP_
    ;

dbContainerClauseOpts
    : fileDevice string (numberOfPages | intValue kmg)
    ;

dropContainerClause
    : LP_ fileDevice string (COMMA_ fileDevice string)* RP_
    ;

fileDevice
    : FILE
    | DEVICE
    ;

allContainersClause
    : LP_ ALL CONTAINERS? (numberOfPages | intValue kmg) RP_
    ;

systemContainerClause
    : LP_ stringList RP_
    ;

stripeset
    : identifier
    ;

km
    : K
    | M
    ;

kmgPercent
    : kmg
    | PERCENT
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-threshold
alterThreshold
    : ALTER THRESHOLD thresholdName alterThresholdOpts+
    ;

alterThresholdOpts
    : WHEN ( alterThresholdPredicate (PERFORM ACTION | alterThresholdExceededActions+)?
           | (EXCEEDED alterThresholdExceededActions+)?
           )
    | enableDisable
    ;

alterThresholdPredicate
    : TOTALMEMBERCONNECTIONS GT_ intValue
    | TOTALSCMEMBERCONNECTIONS GT_ intValue (AND QUEUEDCONNECTIONS (GT_ intValue | UNBOUNDED))?
    | CONNECTIONIDLETIME GT_ intValue dayToMinutes
    | (CONCURRENTWORKLOADOCCURRENCES | CONCURRENTWORKLOADACTIVITIES) GT_ intValue
    | CONCURRENTDBCOORDACTIVITIES GT_ intValue (AND QUEUEDCONNECTIONS (GT_ intValue | UNBOUNDED))?
    | ESTIMATEDSQLCOST GT_ bigintValue
    | SQLROWSRETURNED GT_ intValue
    | (ACTIVITYTOTALTIME | UOWTOTALTIME) GT_ intValue dayToSeconds
    | (SQLTEMPSPACE | AGGSQLTEMPSPACE) GT_ intValue kmg
    | (SQLROWSREAD | SQLROWSREADINSC) GT_ bigintValue checkingEvery?
    | (CPUTIME | CPUTIMEINSC) GT_ intValue hourToSeconds checkingEvery?
    | (ACTIVITYTOTALRUNTIME | ACTIVITYTOTALRUNTIMEINALLSC) GT_ intValue dayToSeconds
    | SORTSHRHEAPUTIL GT_ intValue PERCENT (AND BLOCKING ADMISSION FOR GT_ intValue dayToSeconds)?
    | DATATAGINSC NOT? IN LP_ integerConstantList RP_
    ;

alterThresholdExceededActions
    : COLLECT ACTIVITY DATA (alterCollectActivityDataClause | NONE)
    | (STOP EXECUTION | CONTINUE | FORCE APPLICATION | remapActivityAction)
    ;

dtUnits
    : DAY
    | DAYS
    | HOUR
    | HOURS
    | MINUTE
    | MINUTES
    ;

dtUnitsWithSeconds
    : dtUnits
    | SECONDS
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-trigger
alterTrigger
    : ALTER TRIGGER triggerName NOT? SECURED
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-trusted-context
alterTrustedContext
    : ALTER TRUSTED CONTEXT contextName alterTrustedContextOpts+
    ;

alterTrustedContextOpts
    : ALTER alterTrustedContextOptsAlterOpts+
    | ADD ATTRIBUTES LP_ addressClause (COMMA_ addressClause)* RP_
    | DROP ATTRIBUTES LP_ ADDRESS addressValue (COMMA_ ADDRESS addressValue)* RP_
    | DISABLE? userClause
    ;

alterTrustedContextOptsAlterOpts
    : SYSTEM AUTHID authorizationName
    | ATTRIBUTES LP_ addrClauseEncryptionVal (COMMA_ addrClauseEncryptionVal)* RP_
    | (NO DEFAULT ROLE | DEFAULT ROLE roleName)
    | enableDisable
    ;

addrClauseEncryptionVal
    : addressClause
    | ENCRYPTION encryptionValue
    ;

addressClause
    : ADDRESS addressValue (WITH ENCRYPTION encryptionValue)?
    ;

userClause
    : (ADD | REPLACE) USE FOR useForOpts (COMMA_ useForOpts)*
    | DROP USE FOR useForOpts2 (COMMA_ useForOpts2)*
    ;

useForOpts
    : (authorizationName (ROLE roleName)? | PUBLIC)
        (withWithout AUTHENTICATION)?
    ;

useForOpts2
    : authorizationName
    | PUBLIC
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-type-structured
alterType
    : ALTER TYPE typeName alterTypeOpts+
    ;

alterTypeOpts
    : ADD ATTRIBUTE attributeDefinition
    | DROP ATTRIBUTE attributeName RESTRICT?
    | ADD methodSpecification
    | ALTER methodIdentifier methodOptions
    | DROP methodIdentifier RESTRICT?
    ;

methodIdentifier
    : METHOD methodName (LP_ dataTypeListParen?  RP_)?
    | SPECIFIC METHOD specificName
    ;

methodOptions
    : NOT? FENCED
    | NOT? THREADSAFE
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-usage-list
alterUsageList
    : ALTER USAGE LIST usageListName alterUsageListOptsItem+
    ;

alterUsageListOptsItem
    : LIST SIZE intValue
    | WHEN FULL (WRAP | DEACTIVATE)
    | (INACTIVE | ACTIVE) ON START DATABASE
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-user-mapping
alterUserMapping
    : ALTER USER MAPPING FOR (authorizationName | USER | PUBLIC) SERVER serverName
        OPTIONS LP_ alterUserMappingOptsItem (COMMA_ alterUserMappingOptsItem)* RP_
    ;

alterUserMappingOptsItem
    : addSet? userMappingOptionName string
    | DROP userMappingOptionName
    ;

addSet
    : ADD
    | SET
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-view
alterView
    : ALTER VIEW viewName
        ( alterViewOpts+
        | enableDisable? QUERY OPTIMIZATION
        )
    ;

alterViewOpts
    : ALTER COLUMN? columnName ADD SCOPE (typedTableName | typedViewName)
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-work-action-set
alterWorkActionSet
    : ALTER WORK ACTION SET workActionSetName alterWorkActionSetOpts+
    ;

alterWorkActionSetOpts
    : ADD workActionDefinition
    | ALTER workActionAlteration
    | DROP (WORK ACTION)? workActionName
    | enableDisable
    ;

workActionAlteration
    : (WORK ACTION)? workActionName workActionAlterationOpts+
    ;

workActionAlterationOpts
    : SET WORK CLASS_ workClassName
    | alterActionTypesClause
    | ACTIVITY (LIFETIME | QUEUETIME | EXECUTETIME | ESTIMATEDCOST | INTERARRIVALTIME) HISTOGRAM TEMPLATE templateName
    | enableDisable
    ;

alterActionTypesClause
    : MAP ACTIVITY (withWithout NESTED)? TO serviceSubclassName
    | WHEN (thresholdPredicateClause (PERFORM ACTION | alterThresholdExceededActions)? | (EXCEEDED alterThresholdExceededActions)?)
    | PREVENT EXECUTION
    | COUNT ACTIVITY
    | COLLECT ACTIVITY DATA alterCollectActivityDataClause
    | COLLECT AGGREGATE ACTIVITY DATA (BASE | EXTENDED)?
    ;

thresholdPredicateClause
    : CONCURRENTDBCOORDACTIVITIES GT_ intValue (AND QUEUEDACTIVITIES (GT_ intValue | UNBOUNDED))?
    | SQLTEMPSPACE GT_ kmg
    | SQLROWSRETURNED GT_ intValue
    | ESTIMATEDSQLCOST GT_ bigintValue
    | CPUTIME GT_ intValue hoursMinutes checkingEvery?
    | SQLROWSREAD GT_ bigintValue checkingEvery?
    | SORTSHRHEAPUTIL GT_ intValue PERCENT (AND BLOCKING ADMISSION FOR GT_ intValue dayToSeconds)?
    | (ACTIVITYTOTALTIME | ACTIVITYTOTALRUNTIME) GT_ intValue dayToSeconds
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-work-class-set
alterWorkClassSet
    : ALTER WORK CLASS_ SET workClassSetName alterWorkClassSetOpts+
    ;

alterWorkClassSetOpts
    : ADD workClassDefinition
    | ALTER workClassAlteration
    | DROP (WORK CLASS_)? workClassName
    ;

workClassAlteration
    : (WORK CLASS_)? workClassName workClassAlterationOpts+
    ;

workClassAlterationOpts
    : forFromToAlterClause
    | schemaAlterClause
    | dataTagAlterClause
    | positionClause
    ;

forFromToAlterClause
    : FOR ( (TIMERONCOST | CARDINALITY) FROM fromValue (TO (UNBOUNDED | toValue))? | ALL UNITS UNBOUNDED)
    ;

schemaAlterClause
    : ROUTINES (IN SCHEMA schemaName | ALL)
    ;

dataTagAlterClause
    : DATA TAG LIST CONTAINS (intValue | ANY)
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-workload
alterWorkload
    : ALTER WORKLOAD workloadName alterWorkloadOptsItem*
    ;

alterWorkloadOptsItem
    : ADD connectionAttributes
    | DROP connectionAttributes
    | allowDisallow DB ACCESS
    | enableDisable
    | MAXIMUM DEGREE_ (DEFAULT | degree)
    | SERVICE CLASS_ serviceClassName (UNDER serviceSuperclassName)?
    | POSITION (CRITICAL | HIGH | MEDIUM | LOW | LAST | (BEFORE workloadName) | (AFTER workloadName) | (AT position_))
    | COLLECT ACTIVITY DATA (alterCollectActivityDataClause | NONE)
    | COLLECT ACTIVITY METRICS DATA extendedBaseNone?
    | COLLECT AGGREGATE ACTIVITY DATA extendedBaseNone?
    | COLLECT AGGREGATE UNIT OF WORK baseNone?
    | COLLECT LOCK TIMEOUT DATA (alterCollectHistoryClause | NONE)
    | COLLECT DEADLOCK DATA alterCollectHistoryClause
    | COLLECT LOCK WAIT DATA (alterCollectLockWaitDataClause | NONE)
    | COLLECT UNIT OF WORK DATA ( BASE (INCLUDE packageExecutable LIST (COMMA_ packageExecutable LIST)*)?
                                | NONE
                                )
    | ACTIVITY LIFETIME HISTOGRAM TEMPLATE templateName
    ;

packageExecutable
    : PACKAGE
    | EXECUTABLE
    ;

baseNone
    : BASE
    | NONE
    ;

extendedBaseNone
    : EXTENDED
    | baseNone
    ;

alterCollectActivityDataClause
    : (ON COORDINATOR MEMBER? | ON ALL MEMBERS?)
        (WITHOUT DETAILS | WITH withOpts (COMMA_ withOpts)* (AND VALUES)?)
    ;

withOpts
    : DETAILS
    | SECTION (INCLUDE ACTUALS BASE)?
    ;

alterCollectHistoryClause
    : WITHOUT HISTORY
    | WITH HISTORY (AND VALUES)?
    ;

alterCollectLockWaitDataClause
    : FOR LOCKS WAITING MORE_ THAN waitTime (SECONDS | MICROSECONDS)
      alterCollectHistoryClause? //todo dot
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-wrapper
alterWrapper
    : ALTER WRAPPER wrapperName OPTIONS LP_ alterWrapperOptsItem (COMMA_ alterWrapperOptsItem)* RP_
    ;

alterWrapperOptsItem
    : addSet? wrapperOptionName string
    | DROP wrapperOptionName
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-alter-xsrobject
alterXsrobject
    : ALTER XSROBJECT xsrobjectName enableDisable DECOMPOSITION
    ;

string
    : STRING_
    ;


dataType
    : builtInType
    | anchoredDataType
    | rowTypeName (dataLength | (LP_ identifier RP_))?
    ;

anchoredDataType
    : ANCHOR (DATA TYPE)? TO? ( variableName
                              | ROW OF? (tableName | viewName | cursorVariableName)
                              )
    ;

sourceDataType
    : builtInType
    | anchoredDataType
    ;

dataTypeConstrainst
    : (NOT NULL_)? (CHECK LP_ checkCondition RP_)?
    ;

checkCondition
    : expr
    ;

// https://www.ibm.com/docs/en/db2/12.1?topic=statements-create-type-array
builtInType
    : REF? dataTypeName dataLength?
    | (charCharacter VARYING? | VARCHAR) dataLength? (FOR BIT DATA)?
    ;

dataTypeName
    : SMALLINT | INT | INTEGER | BIGINT | DEC | DECIMAL | DECFLOAT | NUMERIC | NUM | FLOAT
    | REAL | DOUBLE PRECISION? | CLOB | charCharacter LARGE OBJECT
    | GRAPHIC | VARGRAPHIC | DBCLOB | NCHAR | NATIONAL charCharacter | NVARCHAR | NCHAR VARYING
    | NATIONAL charCharacter VARYING | NCLOB | NCHAR LARGE OBJECT | NATIONAL CHARACTER LARGE OBJECT
    | BINARY | VARBINARY | BINARY VARYING | DATE | TIME | TIMESTAMP | BOOLEAN | XML | CURSOR
    ;

dataLength
    : LP_ (intValue? (COMMA_ intValue)*) kmg? octetsCodeunits? RP_
    ;

integerParen
    : LP_ intValue RP_
    ;

charCharacter
    : CHAR
    | CHARACTER
    ;

octetsCodeunits
    : OCTETS
    | CODEUNITS16
    | CODEUNITS32
    ;


kmg
    : K
    | M
    | G
    ;

rsLocatorVariable
    : identifier
    ;

integerConstantList
    : intValue (COMMA_ intValue)*
    ;

intValue
    : INTEGER_
    ;


bigintValue
    : INTEGER_
    ;


bigintConstant
    : INTEGER_
    ;


versionId
    : identifier
    ;

// DROP  https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-drop
drop
    : DROP ( aliasDesignator
           | AUDIT POLICY policyName
           | BUFFERPOOL bufferpoolName
           | DATABASE PARTITION GROUP dbPartitionGroupName
           | EVENT MONITOR eventMonitorName
           | functionDesignator RESTRICT?
           | FUNCTION MAPPING functionMappingName
           | HISTOGRAM TEMPLATE templateName
           | INDEX indexName
           | INDEX EXTENSION indexExtensionName RESTRICT
           | MASK maskName
           | methodDesignator RESTRICT?
           | MODULE moduleName
           | NICKNAME nickName
           | PACKAGE packageName (VERSION? versionId)?
           | PERMISSION permissionName
           | procedureDesignator RESTRICT?
           | ROLE roleName
           | SCHEMA schemaName RESTRICT
           | SECURITY LABEL securityLabelName RESTRICT?
           | SECURITY LABEL COMPONENT secLabelCompName RESTRICT?
           | SECURITY POLICY securityPolicyName RESTRICT?
           | SEQUENCE sequenceName RESTRICT?
           | SERVER serverName
           | serviceClassDesignator RESTRICT?
           | STOGROUP storagegroupName RESTRICT?
           | TABLE (IF EXISTS)? tableName
           | TABLE HIERARCHY rootTableName
           | (TABLESPACE | TABLESPACES) tablespaceNameList
           | (TRANSFORM | TRANSFORMS) (ALL | groupName) FOR typeName
           | THRESHOLD thresholdName
           | TRIGGER triggerName
           | TRUSTED CONTEXT contextName
           | TYPE typeName RESTRICT?
           | TYPE MAPPING typeMappingName
           | USAGE LIST usageListName
           | USER MAPPING FOR (authorizationName | USER) SERVER serverName
           | VARIABLE variableName RESTRICT?
           | VIEW viewName
           | VIEW HIERARCHY rootViewName
           | WORK ACTION SET workActionSetName
           | WORK CLASS_ SET workClassSetName
           | WORKLOAD workloadName
           | WRAPPER wrapperName
           | XSROBJECT xsrobjectName
           | MODEL (modelName | ON tableName)
           | TENANT tenantName
           | DATALAKE TABLE (IF EXISTS)? tableName (DELETE DATA PURGE?)?
           )
    ;

aliasDesignator
    : PUBLIC? ALIAS aliasName (FOR (TABLE | MODULE | SEQUENCE))?
    ;

serviceClassDesignator
    : SERVICE CLASS_ serviceClassName (UNDER serviceSuperclassName)?
    ;

tablespaceNameList
    : tablespaceName (COMMA_ tablespaceName)*
    ;

analyze
    : ANALYZE TABLE tableName COMPUTE STATISTICS (analyzeCol | NOSCAN)? tablesampleClause?
    ;

analyzeCol
    : (INCREMENTAL | FULL)? (FOR ALL COLUMNS (colgroup (COMMA_ colgroup)*)?) | (FOR COLUMNS colobj (COMMA_ colobj)*)
    ;

colgroup
    : LP_ col (COMMA_ col)* RP_
    ;

colobj
    : col | colgroup
    ;

col
    : identifier intValue?
    ;

associateLocators
    : ASSOCIATE (RESULT SET)? (LOCATOR | LOCATORS) ((LP_ rsLocatorVariable (COMMA_ rsLocatorVariable)* RP_) | rsLocatorVariable) WITH PROCEDURE procedureName
    ;

audit
    : AUDIT ( ( (policyBlock (COMMA_ policyBlock)*)
        ( (USING | REPLACE) POLICY policyName
        | REMOVE POLICY) )
    | (ADD EXCEPTION FOR TRUSTED CONTEXT contextName)
    | (REMOVE EXCEPTION FOR TRUSTED CONTEXT contextName))
    ;

policyBlock
    : DATABASE
    | TABLE tableName
    | TRUSTED CONTEXT contextName
    | (USER | GROUP | ROLE) authorizationName
    | ACCESSCTRL
    | CREATE_SECURE_OBJECT
    | DATAACCESS
    | DBADM
    | SECADM
    | SQLADM
    | SYSADM
    | SYSCTRL
    | SYSMAINT
    | SYSMON
    | WLMADM
    ;

beginDeclareSection
    : BEGIN DECLARE SECTION .*?
    ;

call
    : CALL procedureName argListParen? (parameterName ARROW_)? (expr | DEFAULT | NULL_)
    ;

argListParen
    : LP_ argList? RP_
    ;

argList
    : argument (COMMA_ argument)*
    ;

argument
    : identifier
    ;

close
    : CLOSE (cursorName | cursorVariableName) (WITH RELEASE)?
    ;

comment
    : COMMENT ON ( commentObjects IS string
                 | (tableName | viewName) LP_ columnComment (COMMA_ columnComment)* RP_
                 )
    ;

columnComment
    : columnName IS string
    ;

commentObjects
    : aliasDesignator
    | AUDIT POLICY policyName
    | COLUMN (tableName | viewName) DOT_ columnName
    | CONSTRAINT tableName DOT_ constraintName
    | DATABASE PARTITION GROUP dbPartitionGroupName
    | functionDesignator
    | FUNCTION MAPPING functionMappingName
    | HISTOGRAM TEMPLATE templateName
    | INDEX indexName
    | MASK maskName
    | MODULE moduleName
    | NICKNAME nickName
    | PACKAGE packageName (VERSION? versionId)?
    | procedureDesignator
    | ROLE roleName
    | SCHEMA schemaName
    | SECURITY LABEL securityLabelName
    | SECURITY LABEL COMPONENT secLabelCompName
    | SECURITY POLICY securityPolicyName
    | SEQUENCE sequenceName
    | SERVER serverName
    | SERVER OPTION serverOptionName FOR remoteServer
    | serviceClassDesignator
    | TABLE (tableName | viewName)
    | TABLESPACE tablespaceName
    | THRESHOLD thresholdName
    | TRIGGER triggerName
    | TRUSTED CONTEXT contextName
    | TYPE typeName
    | TYPE MAPPING typeMappingName
    | USAGE LIST usageListName
    | VARIABLE variableName
    | WORK ACTION SET workActionSetName
    | WORK CLASS_ SET workClassSetName
    | WORKLOAD workloadName
    | WRAPPER wrapperName
    | XSROBJECT xsrobjectName
    ;

commit
    : COMMIT WORK?
    ;

connectType1
    : CONNECT (TO (serverName | hostVariable) lockBlock? authorization? | RESET | authorization)?
    ;

authorization
    : USER authorizationName passwords?
    | accesstoken
    | APIKEY apiKey
    ;

passwords
    : USING password_ (NEW password_ CONFIRM password_)?
    | CHANGE PASSWORD
    ;

lockBlock
    : IN SHARE MODE
    | IN EXCLUSIVE MODE (ON SINGLE MEMBER)?
    ;

accesstoken
    : ACCESSTOKEN token (ACCESSTOKENTYPE tokenType)
    ;

token
    : todo
    ;

apiKey
    : todo
    ;

tokenType
    : todo
    ;

connectType2
    : CONNECT todo
    ;

declareCursor
    : DECLARE cursorName cursorValueConstructor
    ;

declareGlobalTemporaryTable
    : DECLARE GLOBAL TEMPORARY TABLE tableName
                        ( LP_ columnDefinition (COMMA_ columnDefinition)* RP_
                        | LIKE (tableName | viewName) copyOptions?
                        | AS LP_ fullselect RP_ WITH NO DATA copyOptions? todo
                        )
                        createGlobalTemporaryTableOpts?
    ;

describe
    : describeInput | describeOutput
    ;

describeInput
    : DESCRIBE INPUT statementName INTO descriptorName
    ;

describeOutput
    : DESCRIBE OUTPUT? statementName INTO descriptorName
    ;

disconnect
    : DISCONNECT (serverName | hostVariable | CURRENT | ALL SQL?)
    ;

endDeclareSection
    : END DECLARE SECTION
    ;

execute
    : EXECUTE statementName
        (INTO (COLON_? assignmentTarget (COMMA_ COLON_? assignmentTarget)* | DESCRIPTOR descriptorName))?
        (USING (hostVariableExpression (COMMA_ hostVariableExpression)* | DESCRIPTOR inputDescriptorName))?
        (FOR (hostVariable | intValue) ROWS)?
    ;

hostVariableExpression
    : hostVariable
    | expr
    ;

assignmentTarget
    : globalVariableName
    | hostVariableName
    | sqlParameterName
    | transitionVariableName
    | arrayVariableName LBT_ arrayIndex RBT_
    | fieldReference
    ;

executeImmediate
    : EXECUTE IMMEDIATE COLON_? expr
    ;

explain
    : EXPLAIN
        (PLAN SELECTION | ALL | PLAN)
        ((FOR | WITH) SNAPSHOT)?
        (WITH REOPT ONCE)?
        (SET QUERYNO EQ_ intValue)?
        (SET QUERYTAG EQ_ string)? FOR
        (explainableSqlStatement | XQUERY string)
    ;

explainableSqlStatement
    : fullselect
    ;

fetch
    : FETCH FROM? (cursorName | cursorVariableName)
        (INTO assignmentTarget (COMMA_ assignmentTarget)* | USING DESCRIPTOR descriptorName)
    ;

flushBufferpools
    : FLUSH (BUFFERPOOL | BUFFERPOOLS) ALL
    ;

flushEventMonitor
    : FLUSH EVENT MONITOR eventMonitorName BUFFER?
    ;

flushFederatedCache
    : FLUSH FEDERATED CACHE
         ( FOR ( remoteObjectName
               | (dataSourceName DOT_ schemaName DOT_ASTERISK_)
               | (dataSourceName DOT_ASTERISK_ DOT_ASTERISK_)
               | (SERVER dataSourceName)
               | ALL
               )
         )?
    ;

flushOptimizationProfileCache
    : FLUSH OPTIMIZATION PROFILE CACHE (ALL | optimizationProfileName)
    ;

flushPackageCache
    : FLUSH PACKAGE CACHE DYNAMIC (FOR EXECUTABLE ID executableId USING HARD INVALIDATION)?
    ;

flushAuthenticationCache
    : FLUSH AUTHENTICATION CACHE (FOR ALL)?
    ;

freeLocator
    : FREE LOCATOR (COLON_? variableName) (COMMA_ (COLON_? variableName))*
    ;

getDiagnostics
    : GET DIAGNOSTICS (statementInformation | conditionInformation)
    ;

statementInformation
    : variable EQ_ (DB2_RETURN_STATUS | DB2_SQL_NESTING_LEVEL | ROW_COUNT)
    ;

conditionInformation
    : EXCEPTION conditionVarAssignment (COMMA_ conditionVarAssignment)*
    ;

conditionVarAssignment
    : variable EQ_ (DB2_TOKEN_STRING | MESSAGE_TEXT)
    ;

lockTable
    : LOCK TABLE (tableName | nickName) IN (SHARE | EXCLUSIVE) MODE
    ;

pipe
    : PIPE
     ((LP_ ((expr | NULL_) (COMMA_ (expr | NULL_))*) RP_)
    | exprNull
    | (LP_ rowFullselect RP_)
    | rowExpression)
    ;

refreshTable
    : REFRESH TABLE onlineOptions? queryOptimizationOptions? (NOT? INCREMENTAL)?
    ;

releaseConnection
    : RELEASE (serverName | hostVariable | CURRENT | ALL SQL?)
    ;

rename
    : RENAME (TABLE? sourceTableName | INDEX sourceIndexName) TO targetIdentifier
    ;

renameStogroup
    : RENAME STOGROUP sourceStoragegroupName TO targetStoragegroupName
    ;

renameTablespace
    : RENAME TABLESPACE sourceTablespaceName TO targetTablespaceName
    ;

set
    : SET ( COMPILATION ENVIRONMENT EQ_? hostVariable
          | CONNECTION (serverName | hostVariable)
          | setCurrent
          | ENCRYPTION PASSWORD EQ_? (hostVariable | string)
          | EVENT MONITOR eventMonitorName STATE EQ_? (hostVariable | INTEGER_)
          | INTEGRITY ( FOR tableName (COMMA_ tableName)* (OFF accessModeClause cascadeClause | FULL ACCESS | PRUNE)
                      | FOR tableName tableCheckedOptionsList (COMMA_ tableName tableCheckedOptionsList)* IMMEDIATE CHECKED checkOptions?
                      | FOR tableName tableUncheckedOptions (COMMA_ tableName tableUncheckedOptions)* IMMEDIATE UNCHECKED
                      )
          | PASSTHRU (serverName | RESET)
          | (CURRENT? PATH | CURRENT_PATH) EQ_? pathOptList
          | ROLE EQ_? roleName
          | CURRENT? SCHEMA EQ_? (schemaName | USER | SESSION_USER | SYSTEM_USER | CURRENT_USER | hostVariable | string)
          | SERVER OPTION serverOptionName TO string FOR SERVER serverName
          | (SESSION AUTHORIZATION | SESSION_USER) EQ_? (authorizationName | USER | CURRENT_USER | SYSTEM_USER | hostVariable | string)
          | USAGE LIST usageListName STATE EQ_? (ACTIVE | INACTIVE | RELEASED | hostVariable)
          | ( varDefList
            | booleanVariableName EQ_ (searchCondition | TRUE | FALSE | NULL_)
            | arrayVariableName LBT_ arrayIndex RBT_ EQ_ (expr | NULL_)
            | targetCursorVariable EQ_ (cursorVariableName | cursorValueConstructor | NULL_)
            | targetRowVariable EQ_ ( LP_ exprNull (COMMA_ exprNull)* RP_
                                     | LP_ rowFullselect RP_
                                     | rowExpression
                                     | NULL_
                                     )
            )
          )
    ;

setCurrent
    : decfloatRoundingMode
    | defaultTransformGroup
    | CURRENT DEGREE_ EQ_? (string | hostVariable)
    | explainMode
    | explainSnapshot
    | federatedAsyncharony
    | implicit
    | isolation
    | locale
    | lockTimeout
    | maintainedTable
    | mdcRolloutMode
    | opimizationProfile
    | packagePath
    | packageSet
    | queryOptimization
    | refreshAge
    | sqlCcflags
    | temporal
    ;

decfloatRoundingMode
    : CURRENT DECFLOAT ROUNDING MODE EQ_? (ROUND_CEILING | ROUND_DOWN | ROUND_FLOOR | ROUND_HALF_EVEN | ROUND_HALF_UP | string | hostVariable)
    ;

defaultTransformGroup
    : CURRENT? DEFAULT TRANSFORM GROUP EQ_? groupName
    ;

explainMode
    : CURRENT EXPLAIN MODE EQ_? (yesNo | EXPLAIN NORCAC? | REOPT | (RECOMMEND|EVALUATE) (INDEXES|PARTITIONINGS) | hostVariable)
    ;

explainSnapshot
    : CURRENT EXPLAIN SNAPSHOT EQ_? (yesNo | EXPLAIN | REOPT | hostVariable)
    ;

federatedAsyncharony
    : CURRENT FEDERATED ASYNCHRONY EQ_? (ANY | intValue | hostVariable)
    ;

implicit
    : CURRENT IMPLICIT XMLPARSE OPTION EQ_? (string | hostVariable)
    ;

isolation
    : CURRENT? ISOLATION EQ_? (UR | CS | RR | RS | RESET)
    ;

locale
    : CURRENT LOCALE (LC_MESSAGES_ | LC_TIME_) EQ_? (hostVariable | string)
    ;

lockTimeout
    : CURRENT? LOCK TIMEOUT EQ_? (NOT? WAIT | NULL_ | WAIT intValue | hostVariable)
    ;

maintainedTable
    : CURRENT MAINTAINED TABLE? TYPES (FOR OPTIMIZATION)? EQ_? (ALL | NONE | hostVariable | maintainOptList)
    ;

mdcRolloutMode
    : CURRENT MDC ROLLOUT MODE (NONE | IMMEDIATE | DEFERRED | hostVariable)
    ;

opimizationProfile
    : CURRENT OPTIMIZATION PROFILE EQ_? (optimizationProfileName | hostVariable | string | NULL_)
    ;

packagePath
    : CURRENT PACKAGE PATH EQ_? pkgOptList
    ;

packageSet
    : CURRENT PACKAGESET EQ_? (string | hostVariable)
    ;

queryOptimization
    : CURRENT QUERY OPTIMIZATION EQ_? (INTEGER_ | hostVariable)
    ;

refreshAge
    : CURRENT REFRESH AGE EQ_? (intValue | ANY | hostVariable)
    ;

sqlCcflags
    : CURRENT SQL_CCFLAGS EQ_?? (variable | string)
    ;

temporal
    : CURRENT TEMPORAL (BUSINESS_TIME | SYSTEM_TIME) EQ_? (NULL_ | expr)
    ;

accessModeClause
    : (NO | READ) ACCESS
    ;

cascadeClause
    : CASCADE (IMMEDIATE toDescendentTypes? | DEFERRED)
    ;

toDescendentTypes
    : TO ALL TABLES
    | TO tableTypeList
    ;

tableTypeList
    : tableType (COMMA_ tableType)*
    ;

tableType
    : MATERIALIZED QUERY TABLES
    | FOREIGN KEY TABLES
    | STAGING TABLES
    ;

tableCheckedOptionsList
    : tableCheckedOptions (COMMA_ tableCheckedOptions)*
    ;

tableCheckedOptions
    : onlineOptions
    | GENERATE IDENTITY
    | queryOptimizationOptions
    ;

onlineOptions
    : ALLOW (NO | READ | WRITE) ACCESS
    ;

queryOptimizationOptions
    : (ALLOW QUERY OPTIMIZATION)? USING REFRESH DEFERRED TABLES (WITH REFRESH AGE ANY)?
    ;

checkOptions
    : incrementalOptions exceptionClause
    ;

incrementalOptions
    : NOT? INCREMENTAL
    ;

exceptionClause
    : FOR EXCEPTION inTableUseClause (COMMA_ inTableUseClause)*
    ;

inTableUseClause
    : IN tableName USE tableName
    ;

tableUncheckedOptions
    : integrityOptions fullAccess? (COMMA_ integrityOptions fullAccess?)*
    ;

fullAccess
    : FULL ACCESS
    ;

integrityOptions
    : ALL
    | integrityOptionsItem (COMMA_ integrityOptionsItem)*
    ;

integrityOptionsItem
    : FOREIGN KEY
    | CHECK
    | MATERIALIZED QUERY
    | GENERATED COLUMN
    | STAGING
    ;

varDefList
    : varDef (COMMA_ varDef)*
    ;

varDef
    : targetVariable EQ_ exprNullDefault
    | LP_ targetVariable (COMMA_ targetVariable)* RP_ EQ_? (LP_ exprNullDefault (COMMA_ exprNullDefault)* RP_  | LP_ rowFullselect RP_)
    ;

exprNull
    : expr
    | NULL_
    ;

exprNullDefault
    : expr
    | NULL_
    | DEFAULT
    ;

arrayIndex
    : todo
    ;

rowFullselect
    : todo
    ;

targetVariable
    : globalVariableName
    | hostVariable
    | parameterMarker
    | sqlParameterName
    | fieldReference
    | (sqlVariableName | transitionVariableName) attributeName*
    ;

targetCursorVariable
    : todo
    ;

targetRowVariable
    : globalVariableName
    | parameterMarker
    | sqlParameterName
    | sqlVariableName
    | rowArrayElementSpecification
    | rowFieldReference
    ;

rowArrayElementSpecification
    : todo
    ;

rowFieldReference
    : todo
    ;

fieldReference
    : rowVariableName DOT_ fieldName
    ;

rowExpression
    : todo
    ;

pathOptList
    : pathOpt (COMMA_ pathOpt)*
    ;

pathOpt
    : schemaName
    | SYSTEM PATH
    | USER
    | (CURRENT PATH | CURRENT_PATH)
    | CURRENT PACKAGE PATH
    | hostVariable
    | string
    ;

pkgOptList
    : pkgOpt (COMMA_ pkgOpt)*
    ;

pkgOpt
    : schemaName
    | CURRENT ( PACKAGE? PATH
              | USER
              )
    | CURRENT_PATH
    | CURRENT_USER
    | SESSION_USER
    | SYSTEM_USER
    | USER
    | hostVariable
    | string
    ;

maintainOptList
    : maintainOpt (COMMA_ maintainOpt)*
    ;

maintainOpt
    : FEDERATED_TOOL
    | SYSTEM
    | USER
    | REPLICATION
    | CURRENT MAINTAINED TABLE? TYPES (FOR OPTIMIZATION)?
    ;

variable
    : todo
    ;

hostVariable
    : todo // :hv1
    ;

transferOwnership
    : TRANSFER OWNERSHIP OF objects TO newOwner ((REVOKE | PRESERVE) PRIVILEGES)?
    ;

objects
    : aliasDesignator
    | CONSTRAINT tableName DOT_ constraintName
    | DATABASE PARTITION GROUP dbPartitionGroupName
    | EVENT MONITOR eventMonitorName
    | functionDesignator
    | FUNCTION MAPPING functionMappingName
    | INDEX indexName
    | INDEX EXTENSION indexExtensionName
    | methodDesignator
    | NICKNAME nickName
    | PACKAGE packageName (VERSION? versionId)?
    | procedureDesignator
    | SCHEMA schemaName
    | SEQUENCE sequenceName
    | TABLE tableName
    | TABLE HIERARCHY rootTableName
    | TABLESPACE tablespaceName
    | TRIGGER triggerName
    | DISTINCT? TYPE typeName
    | TYPE MAPPING typeMappingName
    | VARIABLE variableName
    | VIEW viewName
    | VIEW HIERARCHY rootViewName
    | XSROBJECT xsrobjectName
    ;

whenever
    : ( NOT FOUND
      | SQLERROR
      | SQLWARNING) ( CONTINUE
                    | (GOTO | GO TO) COLON_ hostLabel
                    | DO ( functionName
                         | BREAK
                         | CONTINUE
                         )
                    )
    ;

for
    : FOR
    ;

goto
    : GOTO label
    ;

if
    : IF searchCondition THEN sqlRoutineStatement
        (ELSEIF searchCondition THEN sqlRoutineStatement)*
        (ELSE sqlRoutineStatement)?
        END IF
    ;

include
    : INCLUDE (SQLCA | SQLDA | name)
    ;

iterate
    : ITERATE label
    ;

leave
    : LEAVE label
    ;

loop
    : (label COLON_)? LOOP sqlRoutineStatement END LOOP label?
    ;

open
    : OPEN .*?
    ;

prepare
    : PREPARE .*?
    ;

repeat
    : REPEAT .*?
    ;

return
    : RETURN (expr | NULL_ | (WITH commonTableExpression (COMMA_ commonTableExpression)*)? fullselect)?
    ;

while
    : WHILE
    ;

sqlRoutineStatement
    : todo
    ;

commonTableExpression
    : todo
    ;

createAlias
    : CREATE orReplace? PUBLIC? ALIAS (tableAlias | moduleAlias | sequenceAlias)
    ;

tableAlias
    : aliasName FOR TABLE? tableName
    ;

moduleAlias
    : aliasName FOR MODULE moduleName
    ;

sequenceAlias
    : aliasName FOR SEQUENCE sequenceName
    ;

orReplace
    : OR REPLACE
    ;

createAuditPolicy
    : CREATE AUDIT POLICY policyName auditPolicyOpts+
    ;

auditPolicyOpts
    : CATEGORIES auditPolicyCategoriesOpts (COMMA_ auditPolicyCategoriesOpts)*
    | ERROR TYPE (NORMAL | AUDIT)
    ;

auditPolicyCategoriesOpts
    : ( ALL
      | AUDIT
      | CHECKING
      | CONTEXT
      | EXECUTE (withWithout DATA)?
      | OBJMAINT
      | SECMAINT
      | SYSADMIN
      | VALIDATE
      ) STATUS (BOTH | FAILURE | NONE | SUCCESS)
    ;

createBufferpool
    : CREATE BUFFERPOOL bufferpoolName
        (IMMEDIATE | DEFERRED)?
        (ALL DBPARTITIONNUMS | DATABASE PARTITION GROUP dbPartitionGroupName (COMMA_ dbPartitionGroupName)*)?
        (SIZE numberOfPages? AUTOMATIC?)?
        bufferpoolOpts
    ;

bufferpoolOpts
    : exceptClause
    | NUMBLOCKPAGES numberOfPages (BLOCKSIZE numberOfPages)?
    | PAGESIZE intValue K?
    ;

exceptClause
    : EXCEPT ON (MEMBER | MEMBERS) LP_ memberList RP_
    ;

memberList
    : memberListItem (COMMA_ memberListItem)*
    ;

memberListItem
    : memberNumber (TO memberNumber)? SIZE numberOfPages
    ;

createDatabasePartitionGroup
    : CREATE DATABASE PARTITION GROUP dbPartitionGroupName
        ( ON ALL DBPARTITIONNUMS
        | ON (DBPARTITIONNUMS | DBPARTITIONNUM) LP_ dbPartitionNumberList RP_
        )?
    ;

createEventMonitor
    : CREATE EVENT MONITOR eventMonitorName FOR (DATABASE | TABLES | ((DEDLOCKS | DEADLOCKS) (WITH DETAILS (HISTORY VALUES?)?)?)
    | TABLESPACES | BUFFERPOOLS | ((CONNECTIONS | STATEMENTS | TRANSACTIONS) (WHERE eventCondition2)?))
    (COMMA_ (DATABASE | TABLES | (DEDLOCKS (WITH DETAILS (HISTORY VALUES?)?)?)
    | TABLESPACES | BUFFERPOOLS | ((CONNECTIONS | STATEMENTS | TRANSACTIONS) (WHERE eventCondition2)?)))*
        WRITE TO (TABLE (formattedEventTableInfo3 (COMMA_ formattedEventTableInfo3)*?)? | PIPE pipeName | FILE pathName fileOptions* AUTOSTART?)
    ;

createEventMonitorActivities
    : CREATE EVENT MONITOR eventMonitorName FOR ACTIVITIES
        WRITE TO (TABLE (formattedEventTableInfo3 (COMMA_ formattedEventTableInfo3)*?)? | PIPE pipeName | FILE pathName fileOptions)
    ;

formattedEventTableInfo3
    : evmGroup (LP_ targetTableOptions (COMMA_? targetTableOptions)* RP_)?
    | BLOCKED
    ;

createEventMonitorChangeHistory
    : CREATE EVENT MONITOR eventMonitorName FOR CHANGE HISTORY WHERE EVENT IN LP_ eventControlList? RP_
        WRITE TO TABLE (formattedEventTableInfo (COMMA_ formattedEventTableInfo)*)?
        autostartManualstart?
    ;

eventControlList
    : eventControl (COMMA_ eventControl)*
    ;

eventControl
    : ALL
    | ADC
    | BACKUP
    | CFGALL
    | DBCFG
    | DBCFGVALUES
    | DBMCFG
    | CBMCFGVALUES
    | DDLALL
    | DDLDATA
    | DDLFEDERATED
    | DDLMONITOR
    | DDLSECURITY
    | DDLSQL
    | DDLSTORAGE
    | DDLWLM
    | DDLXML
    | LOAD
    | MOVETABLE
    | ONLINERECOVERY
    | REDISTRIBUTE
    | REGVAR
    | REGVARVALUES
    | REORG
    | RESTORE
    | ROLLFORWARD
    | RUNSTATS
    | UTILALL
    ;

createEventMonitorLocking
    : CREATE EVENT MONITOR eventMonitorName FOR LOCKING
        WRITE TO (TABLE formattedEventTableInfo? | UNFORMATTED EVENT TABLE (LP_ targetTableOptions* RP_)?)
        autostartManualstart?
    ;

createEventMonitorPackageCache
    : CREATE EVENT MONITOR eventMonitorName FOR PACKAGE CACHE filterAndCollectionOptions
        WRITE TO (TABLE formattedEventTableInfo? | UNFORMATTED EVENT TABLE (LP_ targetTableOptions* RP_)?)
        autostartManualstart?
    ;

filterAndCollectionOptions
    : (WHERE eventCondition)? (COLLECT (BASE | DETAILED)? DATA)?
    ;



eventCondition
    : eventConditionItem (AND eventConditionItem)*
    ;

eventConditionItem
    : UPDATED_SINCE_BOUNDARY_TIME
    | (NUM_EXECUTIONS | STMT_EXEC_TIME) (GT_ | LT_ | LTE_ | EQ_ | GTE_) intValue
    ;

eventCondition2
    : NOT? primaryCondition ( (AND | OR) eventCondition2 )?
    ;

primaryCondition
    : (APPL_ID | AUTH_ID | APPL_NAME) comparisonOperator string
    | LP_ eventCondition2 RP_
    ;

comparisonOperator
    : EQ_
    | NEQ_
    | GT_
    | GTE_
    | LT_
    | LTE_
    | LIKE
    | NOT? LIKE
    ;

createEventMonitorStatistics
    : CREATE EVENT MONITOR eventMonitorName FOR STATISTICS
        WRITE TO (TABLE (formattedEventTableInfo2 (COMMA_ formattedEventTableInfo2)*?)? | PIPE pipeName | FILE pathName fileOptions)
            eventMonitorStatisticsOpts
    ;

eventMonitorStatisticsOpts
    : (AUTOSTART | MANUALSTART)
    | ON MEMBER memberNumber
    | LOCAL
    ;

createEventMonitorThreshold
    : CREATE EVENT MONITOR eventMonitorName FOR THRESHOLD VIOLATIONS
        WRITE TO (TABLE (formattedEventTableInfo2 (COMMA_ formattedEventTableInfo2)*) | PIPE pipeName | FILE pathName fileOptions)
        eventMonitorThresholdOpts*
    ;

formattedEventTableInfo2
    : evmGroup (LP_ targetTableOptions (COMMA_? targetTableOptions)* RP_)?
    | BUFFERSIZE pages
    | (BLOCKED | NONBLOCKED)
    ;

fileOptions
    : MAXFILES (NONE | numberOfFiles)
    | MAXFILESIZE (pages | NONE)
    | BUFFERSIZE pages
    | (BLOCKED | NONBLOCKED)
    | (APPEND | REPLACE)
    ;

eventMonitorThresholdOpts
    : autostartManualstart
    | ON MEMBER memberNumber
    | LOCAL
    ;

pages
    : intValue
    ;

createEventMonitorUnitOfWork
    : CREATE EVENT MONITOR eventMonitorName FOR UNIT OF WORK
        WRITE TO (TABLE formattedEventTableInfo? | UNFORMATTED EVENT TABLE (LP_ targetTableOptions* RP_)?)
        autostartManualstart?
    ;

formattedEventTableInfo
    : evmGroup (LP_ targetTableOptions*  RP_)?
    ;

autostartManualstart
    : AUTOSTART
    | MANUALSTART
    ;

evmGroup
    : CONTROL | evmValueActivities | evmValueChageHistory | evmValueLocking | evmValuePackageCache
    | evmValueStatitics | evmValueThresholdViolations | evmValueUnitofWork
    ;

evmValueActivities
    : ACTIVITY | ACTIVITYMETRICS | ACTIVITYSTMT | ACTIVITYVALS
    ;

evmValueChageHistory
    : CHANGESUMMARY | EVMONSTART
    | TXNCOMPLETION | DDLSTMTEXEC | DBDBMCFG | REGVAR | UTILSTART | UTILSTOP | UTILPHASE | UTILLOCATION
    ;

evmValueLocking
    : LOCK | LOCK_PARTICIPANTS | LOCK_PARTICIPANT_ACTIVITIES | LOCK_ACTIVITY_VALUES
    ;

evmValuePackageCache
    : PKGCACHE | PKGCACHE_METRICS | PKGCACHE_STMT_ARGS
    ;

evmValueStatitics
    : HISTOGRAMBIN | OSMETRICS | QSTATS | SCMETRICS | SCSTATS | SUPERCLASSMETRICS | SUPERCLASSSTATS | WCSTATS
    | WLMETRICS | WLSTATS
    ;

evmValueThresholdViolations
    : THRESHOLDVIOLATIONS
    ;

evmValueUnitofWork
    : UOW | UOW_METRICS | UOW_PACKAGE_LIST | UOW_EXECUTABLE_LIST
    ;

targetTableOptions
    : TABLE tableName
    | IN tablespaceName
    | PCTDEACTIVATE intValue
    ;

createExternalTable
    : (CREATE EXTERNAL TABLE tableName (LP_ columnDefinition (COMMA_ columnDefinition)* RP_ | LIKE (tableName | viewName | nickName))
        USING LP_ extTableOption extTableOptionValue (COMMA_ extTableOption extTableOptionValue)* RP_) | createExternalTableSelect
    ;

createExternalTableSelect
    : CREATE EXTERNAL TABLE tableName? fileName USING LP_ extTableOption extTableOptionValue (COMMA_ extTableOption extTableOptionValue)* RP_ AS fullselect
    ;

extTableOption
    : identifier
    ;

extTableOptionValue
    : identifier
    | string
    | todo
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-function
createFunction
    : createFunctionAggregateInterface
      | createFunctionExternalScalar
      | createFunctionExternalTable
      | createFunctionOldDbExternalFunction
      | createFunctionSourcedOrTemplate
      | createFunctionSqlScalarTableOrRow
    ;

createFunctionAggregateInterface
    : CREATE orReplace? FUNCTION functionName LP_ aggFnParamDecl (COMMA_ aggFnParamDecl)* RP_
        ( RETURNS (dataType | dataType CAST FROM dataType) aggFnOptionList?
         AGGREGATE WITH LP_ stateVariableDeclaration (COMMA_ stateVariableDeclaration)* RP_ USING
            (IN MODULE moduleName)? INITIALIZE procedureDesignator ACCUMULATE procedureDesignator
                MERGE procedureDesignator FINALIZE functionDesignator
        )
    ;

aggFnParamDecl
    : (IN? parameterName)? dataType defaultClause?
    ;

aggFnOptionList
    : SPECIFIC specificName (NOT? DETERMINISTIC)? (CALLED ON NULL_ INPUT)?
        (NO? EXTERNAL ACTION)? (NO SCRATCHPAD)? (NO FINAL CALL)? (ALLOW PARALLEL)? (NOT? SECURED)?
    ;

stateVariableDeclaration
    : stateVariableName? dataType
    ;

stateVariableName
    : identifier
    ;

createFunctionExternalScalar
    : CREATE orReplace? FUNCTION functionName LP_ extScalarParamDecl (COMMA_ extScalarParamDecl)* RP_
        RETURNS (dataType asLocator? | dataType CAST FROM dataType asLocator?)
        extScalarOptionList
    ;

extScalarParamDecl
    : (inOutInout? parameterName)? dataType defaultClause?
    ;

extScalarOptionList
    : extScalarOptionListItem+
    ;

extScalarOptionListItem
    : LANGUAGE (C_ | JAVA | CLR | OLE | CPP | PYTHON)
    | SPECIFIC specificName
    | EXTERNAL (NAME (string | identifier))?
    | PARAMETER STYLE (DB2GENERAL | JAVA | SQL | NPSGENERIC)
    | PARAMETER CCSID asciiUnicode
    | NOT? DETERMINISTIC
    | FENCED (NOT? THREADSAFE)?
    | NOT FENCED THREADSAFE?
    | RETURNS NULL_ ON NULL_ INPUT
    | CALLED ON NULL_ INPUT
    | READS SQL DATA
    | NO SQL
    | CONTAINS SQL
    | STATIC DISPATCH
    | NO? EXTERNAL ACTION
    | NO SCRATCHPAD
    | SCRATCHPAD length?
    | NO? FINAL CALL
    | NOT? NULL_ CALL
    | allowDisallow PARALLEL
    | NO? DBINFO
    | TRANSFORM GROUP groupName
    | PREDICATES LP_ predicateSpecification RP_
    | INHERIT SPECIAL REGISTERS
    | NOT? SECURED
    | STAY RESIDENT NO
    ;

predicateSpecification
    : WHEN (EQ_ | NEQ_ | LT_ | GT_ | LTE_ | GTE_) (constant_ | EXPRESSION AS expressionName)
        (dataFilter indexExploitation? | indexExploitation dataFilter?)
    ;

dataFilter
    : FILTER USING (functionInvocation | caseExpression)
    ;

functionInvocation
    : todo
    ;

indexExploitation
    : SEARCH BY EXACT? INDEX EXTENSION indexExtensionName exploitationRule+
    ;

exploitationRule
    : WHEN KEY LP_ parameterName RP_ USE searchMethodName LP_ parameterName (COMMA_ parameterName)* RP_
    ;

createFunctionExternalTable
    : CREATE orReplace? FUNCTION functionName LP_ extTableParamDeclList RP_
        RETURNS ((TABLE LP_ columnName dataType asLocator? (COMMA_ columnName dataType asLocator?)* RP_) | GENERIC TABLE)
        extTableOptionList
    ;

extTableParamDeclList
    : extTableParamDecl (COMMA_ extTableParamDecl)*
    ;

extTableParamDecl
    : parameterName? dataType? defaultClause? asLocator?
    ;

extTableOptionList
    : extTableOptionListItem+
    ;

extTableOptionListItem
    : LANGUAGE (C_ | JAVA | CLR | OLE | CPP | OLEDB)
    | SPECIFIC specificName
    | EXTERNAL (NAME (string | identifier))?
    | PARAMETER STYLE (DB2GENERAL | SQL | NPSGENERIC)
    | PARAMETER CCSID asciiUnicode
    | NOT? DETERMINISTIC
    | FENCED (NOT? THREADSAFE)?
    | NOT FENCED THREADSAFE?
    | RETURNS NULL_ ON NULL_ INPUT
    | CALLED ON NULL_ INPUT
    | READS SQL DATA
    | NO SQL
    | CONTAINS SQL
    | STATIC DISPATCH
    | NO? EXTERNAL ACTION
    | NO SCRATCHPAD
    | SCRATCHPAD length?
    | NO? FINAL CALL
    | DISALLOW PARALLEL
    | ALLOW PARALLEL EXECUTE ON ALL (DATABASE PARTITIONS)? RESULT TABLE DISTRIBUTED
    | NO? DBINFO
    | CARDINALITY intValue
    | TRANSFORM GROUP groupName
    | INHERIT SPECIAL REGISTERS
    | NOT? SECURED
    | STAY RESIDENT NO
    ;

createFunctionOldDbExternalFunction
    : CREATE FUNCTION functionName LP_ paramDeclList3 RP_
        RETURNS TABLE LP_ columnName dataType (COMMA_ columnName dataType)* RP_ oledbOptionList
    ;

oledbOptionList
    : oledbOptionListItem+
    ;

oledbOptionListItem
    : LANGUAGE OLEDB
    | SPECIFIC specificName
    | EXTERNAL NAME string
    | NOT? DETERMINISTIC
    | STATIC DISPATCH
    | RETURNS NULL_ ON NULL_ INPUT
    | CALLED ON NULL_ INPUT
    | CARDINALITY intValue
    | NOT? SECURED
    | NO? EXTERNAL ACTION
    ;

createFunctionSourcedOrTemplate
    : CREATE FUNCTION functionName LP_ paramDeclList3 RP_
        fnReturnOpts?
    ;

fnReturnOpts
    : fnReturnOptsItem+
    ;

fnReturnOptsItem
    : RETURNS dataType
    | SPECIFIC specificName
    | ( SOURCE (functionName | SPECIFIC specificName | functionName LP_ dataTypeList? RP_) (PARAMETER CCSID asciiUnicode)?
      | AS TEMPLATE templateOpts?
      )
    ;

templateOpts
    : templateOptsItem+
    ;

templateOptsItem
    : NOT? DETERMINISTIC
    | NO? EXTERNAL ACTION
    ;

asciiUnicode
    : ASCII
    | UNICODE
    ;

paramDeclList3
    : paramDecl3 (COMMA_ paramDecl3)*
    ;

paramDecl3
    : parameterName? dataType defaultClause?
    ;

createFunctionSqlScalarTableOrRow
    : CREATE orReplace? FUNCTION functionName LP_ paramDeclList2 RP_
        RETURNS ( dataType
                | ROW columnList
                | TABLE (columnList | rowTypeName | anchoredDataType | ELEMENT OF rowTypeName)
                ) optionList sqlFunctionBody
    ;

paramDeclList2
    : paramDecl2 (COMMA_ paramDecl2)*
    ;

paramDecl2
    : inOutInout? parameterName dataType defaultClause?
    ;

sqlFunctionBody
    : RETURN todo
    | compoundSqlCompiled
    | compoundSqlInlined
    ;

createFunctionMapping
    : CREATE FUNCTION MAPPING functionMappingName? FOR (functionName LP_ dataTypeList RP_ | SPECIFIC specificName)
        (SERVER serverName | SERVER TYPE serverType (VERSION serverVersion (WRAPPER wrapperName)?)?)
        functionOptions? (WITH INFIX)?
    ;

functionOptions
    : OPTIONS LP_ functionOptionName string (COMMA_ functionOptionName string)* RP_
    ;

functionOptionName
    : identifier
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-global-temporary-table
createGlobalTemporaryTable
    : CREATE GLOBAL TEMPORARY TABLE tableName
          ( LP_ columnDefinition (COMMA_ columnDefinition)* RP_
          | LIKE (tableName | viewName) copyOptions?
          | AS LP_ fullselect RP_ WITH NO DATA copyOptions? todo
          )
          createGlobalTemporaryTableOpts?
    ;

createGlobalTemporaryTableOpts
    : createGlobalTemporaryTableItem+
    ;

createGlobalTemporaryTableItem
    : ON COMMIT deletePreserve ROWS
    | NOT LOGGED ON ROLLBACK deletePreserve ROWS
    | LOGGED
    | IN tablespaceName
    | distributionClause
    ;

deletePreserve
    : DELETE
    | PRESERVE
    ;

createHistogramTemplate
    : CREATE HISTOGRAM TEMPLATE templateName HIGH BIN VALUE bigintConstant
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-index
createIndex
    : CREATE UNIQUE? INDEX indexName ON (tableName | nickName) LP_ indexColOpts RP_
        (NOT? PARTITIONED)? (IN tablespaceName)? (SPECIFICATION ONLY)? (INCLUDE LP_ (tableName | nickName) RP_)?
    ;

indexColOpts
    : indexColOptsItem (COMMA_ indexColOptsItem)*
    ;

indexColOptsItem
    : (columnName | expr) (ASC | DESC | RANDOM)?
    | (BUSINESS_TIME WITHOUT OVERLAPS)?
    ;


//key_expression
//    : todo
//    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-index-extension
createIndexExtension
    : CREATE INDEX EXTENSION indexExtensionName paramList? indexMaintenance indexSearch?
    ;

paramList
    : LP_ parameterName dataType (COMMA_ parameterName dataType)* RP_
    ;

indexMaintenance
    : FROM SOURCE KEY LP_ parameterName dataType RP_ GENERATE KEY USING tableFunctionInvocation
    ;

tableFunctionInvocation
    : todo
    ;

indexSearch
    : WITH TARGET KEY LP_ paramList RP_ SEARCH METHOD searchMethodDefinition (COMMA_ searchMethodDefinition)*
    ;

searchMethodDefinition
    : WHEN methodName LP_ paramList RP_ RANGE THROUGH rangeProducingFuncitonInvocation
        (FILTER USING (indexFilteringFunctionInvocation | caseExpression))?
    ;

createMask
    : CREATE orReplace? MASK maskName ON tableName (AS? correlationName)? FOR COLUMN columnName RETURN caseExpression enableDisable?
    ;


rangeProducingFuncitonInvocation
    : todo
    ;

indexFilteringFunctionInvocation
    : todo
    ;

createMethod
    : CREATE ( METHOD (methodName | methodSignature) FOR typeName
             | SPECIFIC METHOD specificName
             )
             ( methodOpts?
             | (INHERIT ISOLATION LEVEL withWithout LOCK REQUEST)? sqlMethodBody
             )
    ;

methodOpts
    : methodOptsItem+
    ;

methodOptsItem
    : EXTERNAL (NAME (string | identifier))?
    | TRANSFORM GROUP groupName
    ;

methodSignature
    : methodName LP_ methodParamList? RP_ (RETURNS (dataType asLocator? | dataType CAST FROM dataType asLocator?))?
    ;

methodParamList
    : methodParam (COMMA_ methodParam)*
    ;

methodParam
    : parameterName? dataType asLocator?
    ;

sqlMethodBody
    : RETURN
    | compoundSqlInlined
    ;

compoundSqlInlined
    : (label COLON_)? BEGIN (NOT? ATOMIC)?

todo
        END (label COLON_)?
    ;

sqlStatementInlined
    : call
    | for
    | (WITH cte (COMMA_ cte)*)? fullselect
    | getDiagnostics
    | if
    | insert
    | iterate
    | leave
    | merge
    | return
//    | searched_delete
//    | searched_update
    | set
//    | signal
    | while
    ;

compoundSqlCompiled
    : BEGIN COMPOUND NOT? ATOMIC STATIC (STOP AFTER FIRST hostVariable STATEMENTS)?
        (sqlStatementCompiled SEMI_)* END COMPOUND
    ;

sqlStatementCompiled
    : todo //all except
    ;
/*
  CALL                FETCH
  CLOSE               OPEN
  CONNECT             PREPARE
  Compound SQL        RELEASE (Connection)
  DESCRIBE            ROLLBACK
  DISCONNECT          SET CONNECTION
  EXECUTE IMMEDIATE   SET variable
*/

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-module
createModule
    : CREATE orReplace? MODULE moduleName
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-nickname
createNickname
    : CREATE orReplace? NICKNAME nickName (FOR remoteObjectName | nonRelationalDataDefinition)
        (OPTIONS LP_ nickNameOptionName string (COMMA_ nickNameOptionName string)* RP_)?
    ;

nickNameOptionName
    : identifier
    ;

remoteObjectName
    : (dataSourceName DOT_)? (remoteCatalogName DOT_)? remoteSchemaName DOT_ remoteTableName
    ;

nonRelationalDataDefinition
    : nickNameColumnList FOR SERVER serverName
    ;

nickNameColumnList
    : LP_ nickNameColumnListItem (COMMA_ nickNameColumnListItem)* RP_
    ;

nickNameColumnListItem
    : nickNameColumnDefinition
    | uniqueConstraint
    | referentialConstraint
    | checkConstraint
    ;

nickNameColumnDefinition
    : columnName localDataType nickNameColumnOptions*
    ;

nickNameColumnOptions
    : NOT NULL_
    | (CONSTRAINT constraintName)? ( (PRIMARY KEY | UNIQUE) constraintAttributes
                                    | referencesClause
                                    | CHECK LP_ checkCondition RP_ constraintAttributes
                                    )
    | federatedColumnOptions
    ;

federatedColumnOptions
    : OPTIONS LP_ addSet? columnOptionName string (COMMA_ columnOptionName string)* RP_
    ;

columnOptionName
    : identifier
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-permission
createPermission
    : CREATE orReplace? PERMISSION permissionName ON tableName (AS? correlationName)?
        FOR ROWS WHERE searchCondition ENFORCED FOR ALL ACCESS enableDisable?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-procedure
createProcedure
    : createProcedureExternal | createProcedureSourced | createProcedureSql
    ;

createProcedureExternal
    : CREATE orReplace? PROCEDURE procedureName procExtParamList? optionList2?
    ;

procExtParamList
    : LP_ procExtParam (COMMA_ procExtParam)* RP_
    ;

procExtParam
    : inOutInout parameterName? dataType defaultClause?
    ;

optionList2
    : optionList2Item+
    ;

optionList2Item
    : LANGUAGE (C_ | JAVA | COBOL | CLR | OLE)
    | SPECIFIC specificName
    | DYNAMIC RESULT SETS intValue
    | (MODIFIES | READS) SQL DATA
    | (NO | CONTAINS) SQL
    | NOT? DETERMINISTIC
    | CALLED ON NULL_ INPUT
    | oldNew SAVEPOINT LEVEL
    | EXTERNAL (NAME (string | identifier))?
    | FENCED NOT? THREADSAFE
    | NOT FENCED THREADSAFE?
    | COMMIT ON RETURN yesNo
    | AUTONOMOUS
    | NO? EXTERNAL ACTION
    | INHERIT SPECIAL REGISTERS
    | PARAMETER STYLE (DB2GENERAL | DB2SQL | GENERAL | GENERAL WITH RULES | JAVA | SQL)
    | PARAMETER CCSID (ASCII | UNICODE)
    | PROGRAM TYPE (SUB | MAIN)
    | NO? DBINFO
    | STAY RESIDENT NO
    ;

createProcedureSourced
    : CREATE orReplace? PROCEDURE procedureName sourceProcedureClause optionList1?
    ;

sourceProcedureClause
    : SOURCE sourceObjectName (LP_ RP_ | NUMBER OF PARAMETERS intValue)?
        (UNIQUE ID uniqueId)? FOR SERVER serverName
    ;

sourceObjectName
    : sourceSchemaName DOT_ (sourcePackageName DOT_)? sourceProcedureName
    ;

optionList1
    : optionList1Item+
    ;

optionList1Item
    : SPECIFIC specificName
    | WITH RETURN TO CALLER ALL
    | WITH RETURN TO CLIENT (LP_ resultSetElementNumber (COMMA_ resultSetElementNumber)* RP_ | ALL)
    | (NO SQL | CONTAINS SQL | (MODIFIES | READS) SQL DATA)
    | NOT? DETERMINISTIC
    | NO? EXTERNAL ACTION
    ;

resultSetElementNumber
    : intValue
    ;

uniqueId
    : string
    ;

createProcedureSql
    : CREATE orReplace? PROCEDURE procedureName (LP_ procParameterList? RP_)? optionList? sqlProcedureBody
    ;

procParameterList
    : procParameterListItem (COMMA_ procParameterListItem)*
    ;

procParameterListItem
    : inOutInout? parameterName dataType defaultClause?
    ;

inOutInout
    : IN
    | OUT
    | INOUT
    ;

optionList
    : optionListItem+
    ;

optionListItem
    : LANGUAGE SQL
    | SPECIFIC specificName
    | DYNAMIC RESULT SETS intValue
    | (MODIFIES | READS) SQL DATA
    | CONTAINS SQL
    | NOT? DETERMINISTIC
    | CALLED ON NULL_ INPUT
    | COMMIT ON RETURN yesNo
    | AUTONOMOUS
    | INHERIT SPECIAL REGISTERS
    | oldNew SAVEPOINT LEVEL
    | NO? EXTERNAL ACTION
    | PARAMETER CCSID (ASCII | UNICODE)
    | RESULT SETS intValue
    ;

sqlProcedureBody
    : BEGIN .*? END
    ;

createRole
    : CREATE ROLE roleName
    ;

createSchema
    : CREATE SCHEMA (schemaName | AUTHORIZATION authorizationName | schemaName AUTHORIZATION authorizationName)
            (DATA CAPTURE (NONE | CHANGES))?
            schemaSqlStatement*
    ;

schemaSqlStatement
    : createTable
    | createView
    | createIndex
    | comment
    | grant
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-security-label-component
createSecurityLabelComponent
    : CREATE SECURITY LABEL COMPONENT componentName (arrayClause | setClause | treeClause)
    ;

arrayClause
    : ARRAY LBT_ string (COMMA_ string)* RBT_
    ;

setClause
    : SET LBE_ string (COMMA_ string)* RBE_
    ;

treeClause
    : TREE LP_ string ROOT treeClauseItem* RP_
    ;

treeClauseItem
    : COMMA_ string UNDER string
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-security-label
createSecurityLabel
    : CREATE SECURITY LABEL securityLabelName createSecurityLabelItem (COMMA_ createSecurityLabelItem)*
    ;

createSecurityLabelItem
    : COMPONENT componentName string (COMMA_ string)*
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-security-policy
createSecurityPolicy
    : CREATE SECURITY POLICY securityPolicyName COMPONENTS  componentName (COMMA_ componentName)*
        WITH DB2LBACRULES ((OVERRIDE_ | RESTRICT) NOT AUTHORIZED WRITE SECURITY LABEL)?
    ;

createSequence
    : CREATE orReplace? SEQUENCE sequenceName (AS (INTEGER | dataType))?
        createSequenceOpts?
    ;

createSequenceOpts
    : createSequenceOptsItem+
    ;

createSequenceOptsItem
    : START WITH intValue
    | INCREMENT BY intValue
    | (MINVALUE intValue | NO MINVALUE)
    | (MAXVALUE intValue | NO MAXVALUE)
    | NO? CYCLE
    | (CACHE intValue | NO CACHE)
    | NO? ORDER
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-service-class
createServiceClass
    : CREATE SERVICE CLASS_ serviceClassName (UNDER serviceSuperclassName)?
        (FOR WORKLOAD TYPE (CUSTOM | BATCH | INTERACTIVE | MIXED))?
        (softHard? RESOURCE SHARES intValue)?
        (softHard? CPU SHARES intValue)?
        (CPU LIMIT (intValue | NONE))?
        (MINIMUM RESOURCE SHARE intValue PERCENT)?
        (ADMISSION QUEUE ORDER (FIFO | LATENCY))?
        (DEGREE_ SCALEBACK (DEFAULT | onOff))?
        (MAXIMUM DEGREE_ (DEFAULT | NONE | degree))?
        (PREFETCH PRIORITY (DEFAULT | highMediumLow))?
        (OUTBOUND CORRELATOR (NONE | string))?
        (BUFFERPOOL PRIORITY (DEFAULT | highMediumLow))?
        (COLLECT AGGREGATE ACTIVITY DATA (NONE | BASE | EXTENDED)?)? //todo TEST
        (COLLECT AGGREGATE REQUEST DATA (NONE | BASE)?)? //todo TEST
        (COLLECT AGGREGATE UNIT OF WORK DATA (NONE | BASE)?)? //todo TEST
        (COLLECT REQUEST METRICS (NONE | BASE | EXTENDED)?)?
        (histogramTemplaceClause? enableDisable)?
    ;

highMediumLow
    : HIGH
    | MEDIUM
    | LOW
    ;

onOff
    : ON
    | OFF
    ;

softHard
    : SOFT
    | HARD
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-server
createServer
    : CREATE SERVER serverName (TYPE serverType)? (VERSION serverVersion)? (WRAPPER wrapperName)?
        (AUTHORIZATION authorizationName PASSWORD password_)?
        (OPTIONS LP_ serverOptionName string (COMMA_ serverOptionName string)* RP_)?
    ;

password_
    : string | identifier // "pass"
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-stogroup
createStogroup
    : CREATE STOGROUP storagegroupName ON string (COMMA_ string)* createStogroupOpts*
    ;

createStogroupOpts
    : OVERHEAD numberOfMilliseconds
    | DEVICE READ RATE numberMegabytesPerSecond
    | DATA TAG (NUMBER_ | NONE)
    | SET AS DEFAULT
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-synonym
createSynonym
    : CREATE //todo as alias
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-table
createTable
    : CREATE TABLE ifNotExists? tableName
        LP_ ( elementList
        | OF typeName typedTableOptions
        | LIKE (tableName | viewName | nickName) copyOptions?
        | asResultTable copyOptions?
        | materializedQueryOptions
        | stagingTableDefinition
        ) RP_
        (ORGANIZED BY (ROW | COLUMN | (ROW USING)? (dimensionsClause | KEY SEQUENCE sequenceKeySpec | INSERT TIME)))?
        createTableOpts*
    ;

createTableOpts
    : DATA CAPTURE (NONE | CHANGES)
    | tablespaceClauses
    | distributionClause
    | partitioningClause
    | COMPRESS (YES ADAPTIVE? | YES STATIC | NO)
    | VALUE COMPRESSION
    | WITH RESTRICT ON DROP
    | NOT LOGGED INITIALLY
    | CCSID (ASCII | UNICODE)
    | SECURITY POLICY policyName
    | OPTIONS LP_ tableOptionList RP_
    ;

tableOptionList
    : tableOptionListItem (COMMA_ tableOptionListItem)*
    ;

tableOptionListItem
    : tableOptionName string
    ;

tableOptionName
    : string
    ;

elementList
    : elementListItem (COMMA_ elementListItem)*
    ;

elementListItem
    : columnDefinition
    | periodDefinition
    | uniqueConstraint
    | referentialConstraint
    | checkConstraint
    ;

columnDefinition
    : columnName dataType? columnOptions //todo dt
    ;

periodDefinition
    : PERIOD (SYSTEM_TIME | BUSINESS_TIME) LP_ columnName COMMA_ columnName RP_
    ;

uniqueConstraint
    : (CONSTRAINT constraintName)? (UNIQUE | PRIMARY KEY) LP_ columnList (COMMA_ BUSINESS_TIME WITHOUT OVERLAPS)? RP_ constraintAttributes?
    ;

referentialConstraint
    : (CONSTRAINT constraintName)? FOREIGN KEY LP_ columnList RP_ referencesClause
    ;

checkConstraint
    : (CONSTRAINT constraintName)? CHECK LP_ checkCondition RP_ constraintAttributes?
    ;

columnOptions
    : columnOptionsItem* //todo ?
    ;

columnOptionsItem
    : NOT NULL_
    | lobOptions
    | SCOPE (typedTableName | typedViewName)
    | (CONSTRAINT constraintName)? ( (PRIMARY KEY | UNIQUE)
                                    | referencesClause
                                    | CHECK LP_ checkCondition RP_
                                    ) constraintAttributes?
    | (defaultClause | generatedClause)
    | INLINE LENGTH intValue
    | COMPRESS SYSTEM DEFAULT
    | COLUMN? SECURED WITH securityLabelName
    | (NOT | IMPLICITLY) HIDDEN_
    ;

//dataType

//builtInType

referencesClause
    : REFERENCES (tableName | nickName) (columnListParen)? ruleClause constraintAttributes?
    ;

ruleClause
    : (ON DELETE (NO ACTION | RESTRICT | CASCADE | SET NULL_))? (ON UPDATE (NO ACTION | RESTRICT))? //todo bullet
    ;

constraintAttributes
    : (ENFORCED | NOT ENFORCED (NOT? TRUSTED)?)
    | enableDisable QUERY OPTIMIZATION //TODO bullet
    ;

defaultClause
    : WITH? DEFAULT defaultValues?
    ;

defaultValues
    : constant_
    | datetimeSpecialRegister
    | userSpecialRegister
    | CURRENT (SCHEMA | MEMBER)
    | NULL_
    | castFunction LP_ (constant_ | datetimeSpecialRegister | userSpecialRegister) RP_
    | (EMPTY_CLOB | EMPTY_DBCLOB | EMPTY_NCLOB | EMPTY_BLOB) LP_ RP_
    ;

generatedClause
    : GENERATED (ALWAYS | BY DEFAULT)? (identityOptions | asRowChangeTimestampClause)
    | GENERATED ALWAYS? (asGeneratedExpressionClause | asRowTransactionTimestampClause | asRowTransactionStartIdentifierclause)
    ;

datetimeSpecialRegister
    : CURRENT (DATE | TIME | TIMESTAMP)
    ;

userSpecialRegister
    : CURRENT USER
    | SESSION_USER
    | SYSTEM_USER
    ;

castFunction
    : BLOB | DATE | MEASURE
    ;

identityOptions
    : AS IDENTITY (LP_ identityOptionsItem+ RP_)?
    ;

identityOptionsItem
    : START WITH intValue
    | INCREMENT BY intValue
    | (MINVALUE intValue | NO MINVALUE)
    | (MAXVALUE intValue | NO MAXVALUE)
    | NO? CYCLE
    | (CACHE intValue | NO CACHE)
    | NO? ORDER
    ;

asRowChangeTimestampClause
    : FOR EACH ROW ON UPDATE AS ROW CHANGE TIMESTAMP
    ;

asGeneratedExpressionClause
    : AS LP_ generationExpression RP_
    ;

generationExpression
    : todo
    ;

asRowTransactionTimestampClause
    : AS ROW (BEGIN | END)
    ;

asRowTransactionStartIdentifierclause
    : AS TRANSACTION START ID
    ;

oidentifiercolumnDefinition
    : REF IS oidentifiercolumnName USE GENERATED
    ;

rangePartitionSpec
    : LP_ partitionExpressionList RP_ LP_ partitionElementList RP_
    ;

partitionExpressionList
    : partitionExpression (COMMA_ partitionExpression)*
    ;

partitionExpression
    : columnName (NULLS firstLast)?
    ;

partitionElementList
    : partitionElement (COMMA_ partitionElement)*
    ;

partitionElement
    : (PARTITION partitionName)? boundarySpec partitionTablespaceOptions
    | boundarySpec EVERY ( LP_ constant_ durationLabel? RP_
                          | constant_ durationLabel?
                          )
    ;

boundarySpec
    : startingClause endingClause
    ;

partitionTablespaceOptions
    : (IN tablespaceName)? (INDEX IN tablespaceName)? (LONG IN tablespaceName)?
    ;

durationLabel
    : YEAR
    | YEARS
    | MONTH
    | MONTHS
    | DAY
    | DAYS
    | HOUR
    | HOURS
    | MINUTE
    | MINUTES
    | SECOND
    | SECONDS
    | MICROSECOND
    | MICROSECONDS
    ;

startingClause
    : STARTING FROM? (LP_ constMinMaxList RP_ | constMinMax)
    ;

constMinMaxList
    : constMinMax (COMMA_ constMinMax)*
    ;

constMinMax
    : constant_
    | MINVALUE
    | MAXVALUE
    ;

endingClause
    : ENDING AT? (LP_ constMinMaxList RP_ | constMinMax) (INCLUSIVE | EXCLUSIVE)?
    ;

typedTableOptions
    : (HIERARCHY hierarchyName | underClause)? typedElementList?
    ;

typedElementList
    : LP_ typedElementListItem (COMMA_ typedElementListItem)* RP_
    ;

typedElementListItem
    : oidentifiercolumnName
    | withOptions
    | uniqueConstraint
    | checkConstraint
    ;

asResultTable
    : columnListParen? AS LP_ fullselect RP_ WITH NO? DATA
    ;

copyOptions
    : ((INCLUDING | EXCLUDING) COLUMN? DEFAULTS) | ((EXCLUDING | INCLUDING) IDENTITY (COLUMN ATTRIBUTES)?)
    ;

materializedQueryOptions
    : columnListParen? AS LP_ fullselect RP_ WITH NO? DATA
    ;

stagingTableDefinition
    : columnListParen? FOR tableName PROPAGATE IMMEDIATE
    ;

dimensionsClause
    : DIMENSIONS? LP_ colNames (COMMA_ colNames)* RP_
    ;

colNames
    : columnName
    | columnListParen
    ;

sequenceKeySpec
    : LP_ sequenceKeySpecList RP_ (ALLOW | DISALLOW) OVERFLOW (PCTFREE intValue)?
    ;

sequenceKeySpecList
    : sequenceKeySpecListItem (COMMA_ sequenceKeySpecListItem)*
    ;

sequenceKeySpecListItem
    : columnName (STARTING FROM? constant_)? ENDING AT? constant_
    ;

tablespaceClauses
    : IN? tablespaceNameList NO? CYCLE? (INDEX IN tablespaceName)? (LONG IN tablespaceNameList)?
    ;

distributionClause
    : DISTRIBUTE BY (HASH LP_ columnList RP_ | REPLICATION | RANDOM)
    ;

partitioningClause
    : PARTITION BY RANGE? rangePartitionSpec
    ;

ifNotExists
    : IF NOT EXISTS
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-tablespace
createTablespace
    : CREATE (LARGE | REGULAR | (SYSTEM | USER)? TEMPORARY)? TABLESPACE tablespaceName
        (IN (DATABASE PARTITION GROUP)? dbPartitionGroupName)?
        (PAGESIZE intValue K?)?
        ( MANAGED BY ( AUTOMATIC STORAGE storageGroup? sizeAttributes
                     | SYSTEM systemContainers+
                     | DATABASE databaseContainers+ sizeAttributes
                     )
        )?
        ( EXTENTSIZE ( numberOfPages
                     | intValue (K | M)
                     )
        )?
        ( PREFETCHSIZE ( AUTOMATIC
                       | numberOfPages
                       | intValue (K | M)
                       )?
        )?
        (BUFFERPOOL bufferpoolName)?
        (OVERHEAD (numberOfMilliseconds | INHERIT))?
        (NO? FILE SYSTEM CACHING)?
        (TRANSFERRATE (numberOfMilliseconds | INHERIT))?
        (DATA TAG (intValue | INHERIT | NONE))?
        (DROPPED TABLE RECOVERY (ON | OFF))?
    ;

storageGroup
    : USING STOGROUP storagegroupName
    ;

sizeAttributes
    : (AUTORESIZE yesNo)?
      (INITIALSIZE intValue kmg)?
      (INCREASESIZE intValue (PERCENT | kmg))?
      (MAXSIZE (NONE | intValue kmg))?
    ;

systemContainers
    : USING LP_ containerStringList RP_ onDbPartitionsClause?
    ;

containerStringList
    : string (COMMA_ string)*
    ;

databaseContainers
    : USING containerClause onDbPartitionsClause?
    ;

containerClause
    : LP_ containerClauseList RP_
    ;

containerClauseList
    : containerClauseListItem (COMMA_ containerClauseListItem)*
    ;

containerClauseListItem
    : fileDevice string (numberOfPages | intValue kmg)
    ;

onDbPartitionsClause
    : ON dbPartitionNumNums LP_ dbPartitionNumberList RP_
    ;

dbPartitionNumberList
    : dbPartitionNumberListItem (COMMA_ dbPartitionNumberListItem)*
    ;

dbPartitionNumberListItem
    : dbPartitionNumber (TO dbPartitionNumber)?
    ;

dbPartitionNumber
    : intValue
    ;

numberOfPages
    : intValue
    ;

numberOfFiles
    : intValue
    ;

numberOfMilliseconds
    : NUMBER_
    ;

numberMegabytesPerSecond
    : intValue
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-tenant
createTenant
    : CREATE TENANT tenantName
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-threshold
createThreshold
    : CREATE THRESHOLD thresholdName FOR thresholdDomain ACTIVITIES?
     (ENFORCEMENT DEFAULT | ENFORCEMENT enforcementScope*)? enableDisable? WHEN thresholdPredicate thresholdExceededActions2
    ;

thresholdDomain
    : DATABASE
    | SERVICE CLASS_ serviceClassName (UNDER serviceClassName)?
    | STATEMENT (TEXT statementText | REFERENCE executableId)
    | WORKLOAD workloadName
    ;

statementText
    : string
    ;

executableId
    : identifier
    ;

enforcementScope
    : DATABASE
    | MEMBER
    | WORKLOAD OCCURENCE
    ;

thresholdPredicate
    : TOTALMEMBERCONNECTIONS GT_ intValue
    | TOTALSCMEMBERCONNECTIONS GT_ intValue (AND QUEUEDCONNECTIONS (GT_ intValue | UNBOUNDED))?
    | CONNECTIONIDLETIME GT_ intValue dayToMinutes
    | CONCURRENTWORKLOADOCCURRENCES GT_ intValue
    | CONCURRENTWORKLOADACTIVITIES GT_ intValue
    | CONCURRENTDBCOORDACTIVITIES GT_ intValue (AND QUEUEDACTIVITIES (GT_ intValue | UNBOUNDED))?
    | ESTIMATEDSQLCOST GT_ intValue
    | SQLROWSRETURNED GT_ intValue
    | (ACTIVITYTOTALTIME | UOWTOTALTIME) GT_ intValue dayToSeconds
    | (SQLTEMPSPACE | AGGSQLTEMPSPACE) GT_ intValue kmg
    | (SQLROWSREAD | SQLROWSREADINSC) GT_ bigintValue checkingEvery?
    | (CPUTIME | CPUTIMEINSC) GT_ hourToSeconds intValue checkingEvery?
    | (ACTIVITYTOTALRUNTIME | ACTIVITYTOTALRUNTIMEINALLSC) GT_ intValue dayToSeconds
    | SORTSHRHEAPUTIL GT_ intValue PERCENT (AND BLOCKING ADMISSION FOR GT_ intValue dayToSeconds)?
    | DATATAGINSC (NOT? IN) LP_ integerConstantList RP_
    ;

checkingEvery
    : CHECKING EVERY intValue secondSeconds
    ;

hourToSeconds
    : HOUR
    | HOURS
    | MINUTE
    | MINUTES
    | SECOND
    | SECONDS
    ;

dayToMinutes
    : DAY
    | DAYS
    | HOUR
    | HOURS
    | MINUTE
    | MINUTES
    ;

dayToSeconds
    : DAY
    | DAYS
    | HOUR
    | HOURS
    | MINUTE
    | MINUTES
    | SECONDS
    ;

thresholdExceededActions2
    : (COLLECT ACTIVITY DATA ((ON (COORDINATOR|ALL) MEMBER?)?) )?
        (WITHOUT DETAILS | WITH detailsSection (AND VALUES)?)?
        (STOP EXECUTION | CONTINUE | FOR APPLICATION | remapActivityAction)
    ;

detailsSection
    : DETAILS (COMMA_ SECTION)?
    ;

remapActivityAction
    : REMAP ACTIVITY TO serviceSubclassName ((NO | LOG) EVENT MONITOR RECORD)?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-transform
createTransform
    : CREATE (TRANSFORM | TRANSFORMS) FOR typeName tranformList
    ;

tranformList
    : tranformListItem+
    ;

tranformListItem
    : groupName LP_ transformGroupList RP_
    ;

transformGroupList
    : transformGroupListItem (COMMA_ transformGroupListItem)*
    ;

transformGroupListItem
    : (TO | FROM) SQL WITH functionDesignator
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-trigger
createTrigger
    : CREATE orReplace? TRIGGER triggerName ((NO CASCADE)? BEFORE | AFTER | INSTEAD OF) triggerEvent* ON (tableName | viewName)
    (
        REFERENCING refList
    )?
    FOR EACH (ROW | STATEMENT) (NOT? SECURED)? triggeredAction
    ;

refList
    : refListItem+
    ;

refListItem
    : oldNew AS? correlationName
    | oldNew TABLE AS? identifier
    ;

oldNew
    : OLD
    | NEW
    ;

correlationName
    : identifier
    ;

triggerEvent
    : OR
    | INSERT
    | DELETE
    | UPDATE (OF columnList)?
    ;

triggeredAction
    : (WHEN LP_ searchCondition RP_)? label? sqlProcedureStatement
    ;

sqlProcedureStatement
    : compound
    | singleStatement
    | CALL
    | compoundSqlInlined
    | compoundSqlCompiled
    | FOR
    | WITH commonTableExpression
    | IF


//    | todo
    ;
compound
     : BEGIN ATOMIC? .*? END
     ;
singleStatement
    : .*?
    ;
//compound
//    : BEGIN ATOMIC? sqlStatement* END
//    ;
//singleStatement
//    : sqlStatement
//    ;
sqlStatement
  : (declaration | controlStatement | dmlStatement) SEMI_?
  ;
controlStatement
     : IF LP_ expr RP_ sqlStatement (ELSE sqlStatement)?
     | WHILE LP_ expr RP_ sqlStatement
     | SIGNAL SQLSTATE literals (SET assignmentClause)?
     ;
declaration
    : DECLARE identifier dataType SEMI_
    ;
dmlStatement
    : (insert | update | delete | merge | call) .*? SEMI_?
    ;
createTrustedContext
    : CREATE TRUSTED CONTEXT contextName (BASED | BASE) UPON CONNECTION USING SYSTEM AUTHID authorizationName
        ( ATTRIBUTES LP_ attrList RP_
        | (NO DEFAULT ROLE | DEFAULT ROLE roleName)?
        | enableDisable?
        )
        (WITH USE FOR authList)?
    ;

attrList
    : attrListItem (COMMA_ attrListItem)*
    ;

attrListItem
    : ADDRESS addressValue (WITH ENCRYPTION encryptionValue)?
    | ENCRYPTION encryptionValue
    ;

authList
    : authListItem (COMMA_ authListItem)*
    ;

authListItem
    : ( authorizationName (ROLE roleName)?
      | PUBLIC
      ) (withWithout AUTHENTICATION)?
    ;

addressValue
    : string
    ;

encryptionValue
    : string
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-type
createType
    : createTypeArray
      | createTypeCursor
      | createTypeDistinct
      | createTypeRow
      | createTypeStructured
    ;

createTypeArray
    : CREATE orReplace? TYPE typeName AS dataType ARRAY LBT_ (intValue | dataType)? RBT_
    ;

createTypeCursor
    : CREATE orReplace? TYPE typeName AS (anchoredDataType | rowTypeName)? CURSOR
    ;

createTypeDistinct
    : CREATE TYPE distinctTypeName AS sourceDataType (WITH STRONG TYPE RULES | WITH WEAK TYPE RULES dataTypeConstrainst)?
    ;

createTypeRow
    : CREATE orReplace? TYPE typeName AS ROW (fieldDefinitionListParen | anchoredDataType)
    ;

fieldDefinitionListParen
    : LP_ fieldDefinitionList RP_
    ;

fieldDefinitionList
    : fieldDefinition (COMMA_ fieldDefinition)*
    ;

fieldDefinition
    : fieldName dataType
    ;

createTypeStructured
    : CREATE TYPE typeName (UNDER supertypeName)? (AS attributeDefinitionListParen)?
        structuredTypeSeq+
        methodSpecificationList?
    ;

structuredTypeSeq
    : NOT? INSTANTIABLE
    | CAST LP_ SOURCE AS REF RP_ WITH functionName
    | CAST LP_ REF AS SOURCE RP_ WITH functionName
    | INLINE LENGTH intValue
    | WITHOUT COMPARISONS
    | NOT FINAL
    | MODE DB2SQL
    | WITH FUNCTION ACCESS
    | REF USING repType
    ;

attributeDefinitionListParen
    : LP_ attributeDefinitionList RP_
    ;

attributeDefinitionList
    : attributeDefinition (COMMA_ attributeDefinition)*
    ;

attributeDefinition
    : attributeName dataType lobOptions?
    ;

methodSpecificationList
    : methodSpecification (COMMA_ methodSpecification)*
    ;

methodSpecification
    : OVERRIDING? METHOD methodName paramDeclListParen methodSpecificationSeq+
    ;

methodSpecificationSeq
    : RETURNS (dataType asLocator? | dataType CAST FROM dataType asLocator?)
    | SPECIFIC specificName
    | SELF AS RESULT
    | sqlRoutineCharacteristics
    | externalRoutineCharacteristics
    ;

asLocator
    : AS LOCATOR
    ;

paramDeclListParen
    : LP_ paramDeclList RP_
    ;

paramDeclList
    : paramDecl (COMMA_ paramDecl)*
    ;

paramDecl
    : parameterName? dataType asLocator?
    ;

sqlRoutineCharacteristics
    : LANGUAGE SQL
    | PARAMETER CCSID (ASCII | UNICODE)
    | NOT? DETERMINISTIC
    | NO? EXTERNAL ACTION
    | READS SQL DATA
    | CONTAINS SQL
    | CALLED ON NULL_ INPUT
    | INHERIT SPECIAL REGISTERS
    ;

externalRoutineCharacteristics
    : LANGUAGE (C_ | JAVA | OLE)
    | PARAMETER STYLE (DB2GENERAL | SQL)
    | PARAMETER CCSID (ASCII | UNICODE)
    | NOT? DETERMINISTIC
    | FENCED (NOT? THREADSAFE)?
    | NOT FENCED THREADSAFE?
    | CALLED ON NULL_ INPUT
    | RETURNS NULL_ ON NULL_ INPUT
    | READS SQL DATA
    | NO SQL
    | CONTAINS SQL
    | NO? EXTERNAL ACTION
    | NO SCRATCHPAD
    | SCRATCHPAD length?
    | NO? FINAL CALL
    | (ALLOW | DISALLOW) PARALLEL
    | NO? DBINFO
    | INHERIT SPECIAL REGISTERS
    ;

length
    : intValue
    ;

repType
    : (SMALLINT | INTEGER | INT | BIGINT)
    | (DECIMAL | DEC | NUMERIC | NUM) (LP_ intValue (COMMA_ intValue)? RP_)?
    | DECFLOAT (LP_ intValue RP_)?
    | charCharacter integerParen? forBitData?
    | varchars integerParen forBitData?
    | BINARY integerParen?
    | varbinaries integerParen
    | GRAPHIC integerParen?
    | VARGRAPHIC integerParen
    ;

varchars
    : VARCHAR
    | charCharacter VARYING
    ;

varbinaries
    : VARBINARY
    | BINARY VARYING
    ;

forBitData
    : FOR BIT DATA
    ;

lobOptions
    : NOT? LOGGED
    | NOT? COMPACT //TODO DOT
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-type-mapping
createTypeMapping
    : CREATE TYPE MAPPING typeMappingName?
         fromTo (LOCAL TYPE)? (SYSIBM DOT_)? localDataType
         fromTo remoteServer REMOTE? TYPE dataSourceDataType forBitDataPrecision
    ;

forBitDataPrecision
    : forBitData
    | LP_ ((precision | LBT_ precision RANGE_OPERATOR_ precision RBT_) (COMMA_ (scale | LBT_ scale RANGE_OPERATOR_ scale RBT_))?)? RP_ precisionScaleComp?
    ;

precision
    : intValue
    ;

scale
    : intValue
    ;

precisionScaleComp
    : P_ (EQ_|GT_|LT_|GTE_|LTE_|NEQ_) S_
    ;

fromTo
    : FROM
    | TO
    ;

dataSourceDataType
    : todo
    ;

localDataType
    : builtInType
    ;

remoteServer
    : SERVER serverName
    | SERVER TYPE serverType (VERSION serverVersion (WRAPPER wrapperName)?)?
    ;

serverVersion
    : version
    | string
    | identifier
    ;

serverType
    : identifier (SLASH_ identifier)?
    ;

version
    : intValue | VERSION_ | NUMBER_
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-usage-list
createUsageList
    : CREATE USAGE LIST usageListName FOR (TABLE | INDEX) objectName
        (LIST SIZE intValue)?
        (WHEN FULL (WRAP | DEACTIVATE))?
        ((INACTIVE | ACTIVE) ON START DATABASE)?
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-user-mapping
createUserMapping
    : CREATE USER MAPPING FOR (LT_? authorizationName GT_? | USER | PUBLIC) SERVER LT_? serverName GT_? OPTIONS userMappingOptionsParen
    ;

userMappingOptionsParen
    : LP_ userMappingOptions (COMMA_ userMappingOptions)* RP_
    ;

userMappingOptions
    : userMappingOptionName string
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-variable
createVariable
    : CREATE orReplace? VARIABLE variableName dataType
            ( (DEFAULT | CONSTANT)? NULL_
            | (DEFAULT | CONSTANT) ( constant_
                                   | specialRegister
                                   | globalVariable
                                   | LP_ cursorValueConstructor RP_
                                   | LP_ expr RP_
                                   )
            )?
    ;

constant_
    : intValue
    | bigintConstant
    | string
    | HEX_DIGIT_
    | todo
    ;

specialRegister
    : identifier
    ;

globalVariable
    : identifier
    ;



cursorValueConstructor
    : (ASENSITIVE | INSENSITIVE)? CURSOR paramDeclListParen? holdability? FOR select
    ;

anchoredVariableDataType
    : ANCHOR (DATA TYPE)? TO? ( variableName
                              | tableName DOT_ columnName
                              | ROW OF? (tableName | viewName | cursorVariableName)
                              )
    ;

holdability
    : (WITHOUT | WITH) HOLD
    ;

returnability
    : (WITHOUT RETURN) | (WITH RETURN (TO (CALLER | CLIENT))?)
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-view
createView
    : CREATE orReplace? VIEW viewName ( columnListParen
                                        | OF typeName (rootViewDefinition | subviewDefinition)
                                        )?
            (WITH cteList)? AS? fullselect
            createViewSeq*
    ;

createViewSeq
    : WITH (CASCADED | LOCAL)? CHECK OPTION
    | WITH NO? ROW MOVEMENT
    ;

cteList
    : cte (COMMA_ cte)*
    ;

cte
    : todo
    ;

fullselect
    : (subselect | LP_ fullselect RP_ | valuesClause)
    ;

combineType
    : UNION ALL? | EXCEPT ALL? | INTERSECT ALL?
    ;

subselect
    :  subselect  combineType subselect | (SELECT (ALL | DISTINCT)? selectList fromClause whereClause? groupByClause? havingClause?
        orderByClause? offsetClause? fetchClause? isolationClause?)
    ;


selectList
    : (unqualifiedShorthand | selectProjection) (COMMA_ selectProjection)*
    ;

unqualifiedShorthand
    : ASTERISK_
    ;

selectProjection
    : (queryName | tableName | alias) DOT_ASTERISK_
    | selectProjectionExprClause
    ;

selectProjectionExprClause
    : (columnName | expr) (AS? alias)?
    ;

fromClause
    : FROM (tableReference | joinedTable)  (COMMA_ tableReference)*
    ;

tableReference
    : singlesTableReference
    | analyzeTableReference
    | nestedTableReference
    | dataChangeTableReference
    | tableFunctionReference
    | collectionDerivedTable
    | xmltableExpression
    //| joined_table
    | externalTableReference
    ;

singlesTableReference
    : tableName periodSpecification* correlationClause? tablesampleClause?
    | (ONLY | OUTER) LP_ tableName RP_ correlationClause?
    ;

periodSpecification
    : FOR (SYSTEM_TIME | BUSINESS_TIME) (AS OF value | FROM value TO value | BETWEEN value AND value)
    ;

value
    : string | intValue
    ;

correlationClause
    : AS? correlationName columnListParen?
    ;

tablesampleClause
    : TABLESAMPLE (BERNOULLI | SYSTEM) LP_ numberLiterals RP_ (REPEATABLE numberLiterals)?
    ;


analyzeTableReference
    : tableName ANALYZE TABLE  LP_  implementationClause RP_
    ;

implementationClause
    : IMPLEMENTATION string
    ;

nestedTableReference
    : (LATERAL (continueHandler WITHIN)?)? LP_ (WITH cteList)? subselect RP_ correlationClause?
    ;

continueHandler
    : RETURN DATA UNTIL specificConditionValue (COMMA_ specificConditionValue)*
    ;

specificConditionValue
    : FEDERATED SQLSTATE VALUE? string (SQLCODE intValue (COMMA_ intValue)*)?
    ;

dataChangeTableReference
    : ( finalNewOld TABLE LP_ insert RP_
      | finalNewOld TABLE LP_ searchedUpdateStatement RP_
      | OLD TABLE LP_ seachedDeleteStatement RP_
      ) correlationClause?
    ;


searchedUpdateStatement
    : todo
    ;

seachedDeleteStatement
    : todo
    ;


finalNewOld
    : FINAL
    | NEW
    | OLD
    ;

tableFunctionReference
    : TABLE LP_ functionName LP_ expr (COMMA_ expr)* RP_ tableUdfCardinalityClause? RP_
        (correlationClause | typedCorrelationClause)?
    ;

tableUdfCardinalityClause
    : CARDINALITY intValue
    | CARDINALITY MULTIPLIER intValue
    ;

typedCorrelationClause
    : AS? correlationName (LP_ columnNameDataType (COMMA_ columnNameDataType)* RP_)?
    ;

columnNameDataType
    : columnName dataType
    ;

collectionDerivedTable
    : UNNEST tableFunction (WITH ORDINALITY)? correlationClause?
    ;


xmltableExpression
    : functionCall correlationClause?
    ;

joinedTable
    : tableReference (INNER | outer)? JOIN tableReference (ON joinCondition | USING LP_ columnList RP_)
    | tableReference CROSS JOIN tableReference
    | LP_ joinedTable RP_
    ;

joinCondition
    : expr
    ;

outer
    : (LEFT | RIGHT | FULL) OUTER?
    ;

externalTableReference
    : EXTERNAL fileName (AS? correlationName)?
        ( LP_ columnDefinition2 (COMMA_ columnDefinition2)* RP_
        | LIKE (tableName | viewName | nickName)
        )
    ;

columnDefinition2
    : columnName builtInType (NOT NULL_)?
    ;

fileName
    : name
    ;

whereClause
    : WHERE expr
    ;

groupByClause
    : GROUP BY groupByClauseOpts (COMMA_ groupByClauseOpts)*
    ;

rollupCubeClause
    : (ROLLUP | CUBE) LP_ groupingExprList RP_
    ;

groupingSetsClause
    : GROUPING SETS LP_ (rollupCubeClause | groupingExprList) (COMMA_ (rollupCubeClause | groupingExprList))* RP_
    ;

groupingExprList
    : expressionList (COMMA_ expressionList)*
    ;

expressionList
    : exprs | LP_ expr? (COMMA_ expr?)* RP_
    ;

groupByClauseOpts
    : groupingExpression
    | groupingSetsClause
    | superGroups
    ;

groupingExpression
    : expr
    ;

superGroups
    : rollupCubeClause
    | grantTotal
    ;

grantTotal
    : LP_ RP_
    ;

havingClause
    : HAVING expr
    ;

orderByClause
    : ORDER BY ((orderByClauseOpts (COMMA_ orderByClauseOpts)*) | INPUT SEQUENCE)

    ;

orderByClauseOpts
    : sortKey ((ASC | DESC) (NULLS firstLast)?)?
    | ORDER OF expr
    ;

firstLast
    : FIRST
    | LAST
    ;

sortKey
    : columnName
    | numberLiterals
    | expr
    ;

offsetClause
    : OFFSET numberLiterals rowRows
    ;

fetchClause
    : FETCH NEXT numberLiterals? rowRows ONLY
    ;

rowRows
    : ROW
    | ROWS
    ;

isolationClause
    : WITH lockType lockRequestClause?
    ;

lockType
    : RR | RS | CS | UR
    ;

lockRequestClause
    : USE AND KEEP (SHARE | UPDATE | EXCLUSIVE) LOCKS
    ;

valuesClause
    : VALUES valuesRow (COMMA_ valuesRow)*
    ;

valuesRow
    : (expr | NULL_ | rowExpression)
    | LP_ exprNull (COMMA_ exprNull)* RP_
    ;

rootViewDefinition
    : MODE DB2SQL LP_ oidentifiercolumn (COMMA_ withOptions)? RP_
    ;

subviewDefinition
    : MODE DB2SQL underClause (LP_ withOptions RP_)? EXTEND?
    ;

oidentifiercolumn
    : REF IS oidentifiercolumnName USER GENERATED UNCHECKED?
    ;

withOptions
    : withOptionDef (COMMA_ withOptionDef)*
    ;

withOptionDef
    : columnName WITH OPTIONS (withOptionScopeDef+ | READ ONLY)
    ;

withOptionScopeDef
    : SCOPE (typedTableName | typedViewName)
    ;

underClause
    : UNDER superviewName INHERIT SELECT PRIVILEGES
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-work-action-set
createWorkActionSet
    : CREATE WORK ACTION SET workActionSetName FOR (DATABASE | SERVICE CLASS_ serviceSuperclassName | WORKLOAD workloadName)
        USING WORK CLASS_ SET workClassSetName (workActionDefinitionListParen)?
    ;

workActionDefinitionListParen
    : LP_ workActionDefinitionList RP_
    ;

workActionDefinitionList
    : workActionDefinition (COMMA_ workActionDefinition)*
    ;

workActionDefinition
    : WORK ACTION workActionName ON WORK CLASS_ workClassName actionTypesClause histogramTemplaceClause* enableDisable?
    ;

actionTypesClause
    : MAP ACTIVITY (withWithout NESTED)? TO serviceSubclassName
    | WHEN thresholdTypesClause thresholdExceededActions
    | PREVENT EXECUTION
    | COLLECT ACTIVITY DATA collectActivityDataClause
    | COLLECT AGGREGATE ACTIVITY DATA (BASE | EXTENDED)?
    ;

thresholdTypesClause
    : CONCURRENTDBCOORDACTIVITIES GT_ intValue (AND QUEUEDACTIVITIES (GT_ intValue | UNBOUNDED))?
    | SQLTEMPSPACE GT_ intValue kmg
    | SQLROWSRETURNED GT_ intValue
    | ESTIMATEDSQLCOST GT_ bigintValue
    | CPUTIME GT_ intValue hoursMinutes (CHECKING EVERY intValue secondSeconds)?
    | SQLROWSREAD GT_ bigintValue (CHECKING EVERY intValue secondSeconds)?
    | SORTSHRHEAPUTIL GT_ intValue PERCENT (AND BLOCKING ADMISSION FOR GT_ intValue dtUnitsWithSeconds)?
    | ACTIVITYTOTALTIME GT_ intValue dtUnitsWithSeconds
    | ACTIVITYTOTALRUNTIME GT_ intValue dtUnitsWithSeconds
    ;

secondSeconds
    : SECOND
    | SECONDS
    ;

hoursMinutes
    : HOUR
    | HOURS
    | MINUTE
    | MINUTES
    ;

thresholdExceededActions
    : (COLLECT ACTIVITY DATA (NONE | collectActivityDataClause))? (STOP EXECUTION | CONTINUE)
    ;

collectActivityDataClause
    : (ON COORDINATOR MEMBER? | ON ALL MEMBERS?)?
        ( WITHOUT DETAILS
        | WITH ( DETAILS
               | SECTION (INCLUDE ACTUALS BASE)?
               ) (AND VALUES)?
        )?
    ;

withWithout
    : WITH
    | WITHOUT
    ;

histogramTemplaceClause
    : ACTIVITY (LIFETIME | QUEUETIME | EXECUTETIME | ESTIMATEDCOST | INTERARRIVALTIME)
        HISTOGRAM TEMPLATE (SYSDEFAULTHISTOGRAM | templateName)
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-work-class-set
createWorkClassSet
    : CREATE WORK CLASS_ SET workClassSetName workClassDefinitionListParen?
    ;

workClassDefinitionListParen
    : LP_ workClassDefinitionList RP_
    ;

workClassDefinitionList
    : workClassDefinition (COMMA_ workClassDefinition)*
    ;

workClassDefinition
    : (WORK CLASS_)? workClassName workAttributes positionClause?
    ;

workAttributes
    : WORK TYPE ( CALL schemaClause?
                | (READ | WRITE | DML) forFromToClause? dataTagClause?
                | DDL
                | LOAD
                | ALL forFromToClause? schemaClause? dataTagClause?
                )
    ;

positionClause
    : POSITION LAST
    | POSITION (BEFORE | AFTER) workClassName
    | POSITION AT position_
    ;

position_
    : INTEGER_
    ;

forFromToClause
    : FOR (TIMERONCOST | CARDINALITY) FROM fromValue (TO (UNBOUNDED | toValue))?
    ;

fromValue
    : identifier | intValue
    ;

toValue
    : todo
    ;

dataTagClause
    : DATA TAG LIST CONTAINS intValue
    ;

schemaClause
    : ROUTINES IN SCHEMA schemaName
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-workload
createWorkload
    : CREATE WORKLOAD workloadName connectionAttributes+ workloadAttributes
        positionClause2?
        collects*
        histogramTemplaceClause?
    ;

collects
    : (PRIORITY (CRITICAL | HIGH | MEDIUM | LOW))
    | (COLLECT ACTIVITY METRICS (NONE | BASE | EXTENDED)?)
    | (COLLECT ACTIVITY DATA (NONE | collectOnClause collectDetailsClause))
    | (COLLECT AGGREGATE ACTIVITY DATA (BASE | EXTENDED)?)
    | (COLLECT AGGREGATE UNIT OF WORK DATA BASE?)
    | (COLLECT LOCK TIMEOUT DATA (NONE | WITH HISTORY (AND VALUES)?)?)
    | (COLLECT DEADLOCK DATA (WITH HISTORY (AND VALUES)?)?)
    | (COLLECT LOCK WAIT DATA collectLockWaitOptions)
    | (COLLECT UNIT OF WORK DATA (BASE (INCLUDE pkgExecSeq)?)?)
    ;

pkgExecSeq
    : PACKAGE LIST (COMMA_ EXECUTABLE LIST)?
    | EXECUTABLE LIST (COMMA_ PACKAGE LIST)?
    ;

positionClause2
    : POSITION LAST
    | POSITION (BEFORE | AFTER) workloadName
    | POSITION AT position_
    ;

connectionAttributes
    : ADDRESS stringListParen
    | APPLNAME stringListParen
    | SYSTEM_USER stringListParen
    | SESSION_USER (GROUP | ROLE)? stringListParen
    | CURRENT (CLIENT_USERID | CLIENT_APPLNAME | CLIENT_WRKSTNNAME | CLIENT_ACCTNG) stringListParen
    ;

stringList
    : string (COMMA_ string)*
    ;


stringListParen
    : LP_ string (COMMA_ string)* RP_
    ;

workloadAttributes
    : enableDisable? (allowDisallow DB ACCESS)? (MAXIMUM DEGREE_ (DEFAULT | degree))?
        (SERVICE CLASS_ (SYSDEFAULTUSERCLASS | serviceClassName (UNDER serviceSuperclassName)?))?
    ;

degree
    : todo
    ;

allowDisallow
    : ALLOW
    | DISALLOW
    ;

collectOnClause
    : ON COORDINATOR MEMBER?
    | ON ALL MEMBERS?
    ;

collectDetailsClause
    : WITHOUT DETAILS
    | WITH  (DETAILS | SECTION (INCLUDE ACTUALS BASE)?)? (AND VALUES)?
    ;

collectLockWaitOptions
    : FOR LOCKS WAITING MORE_ THAN (waitTime (SECONDS | MICROSECONDS) | INTEGER_ SECOND) (WITHOUT HISTORY | WITH HISTORY (AND VALUES)?)?
    ;

waitTime
    : intValue
    ;

// https://www.ibm.com/docs/en/db2/12.1.0?topic=statements-create-wrapper
createWrapper
    : CREATE WRAPPER wrapperName (LIBRARY libraryName)? (OPTIONS LP_ wrapperOptionList RP_)?
    ;

wrapperOptionList
    : wrapperOption (COMMA_ wrapperOption)*
    ;

wrapperOption
    : wrapperOptionName string
    ;


queryName
    : identifier | STRING_
    ;

tableName
    : (owner DOT_)? name
    ;

viewName
    : (owner DOT_)? name
    ;

triggerName
    : (owner DOT_)? name
    ;


columnName
    : (owner DOT_)* name
    ;

objectName
    : (owner DOT_)? name
    ;

clusterName
    : (owner DOT_)? name
    ;

indexName
    : (owner DOT_)? name
    ;

statisticsTypeName
    : (owner DOT_)? name
    ;

functionName
    : (owner DOT_)? name | STRING_
    ;

tenantName
    : identifier
    ;

owner
    : identifier | STRING_
    ;


name
    : identifier | STRING_
    ;

alias
    : identifier | STRING_
    ;

exprs
    : expr (COMMA_ expr)*
    ;

exprList
    : LP_ exprs RP_
    ;

expr
    : expr andOperator expr
    | expr orOperator expr
    | notOperator expr
    | LP_ expr RP_
    | booleanPrimary
    | searchCondition
    ;

andOperator
    : AND | AND_
    ;

orOperator
    : OR | OR_
    ;

notOperator
    : NOT | NOT_
    ;

booleanPrimary
    : booleanPrimary IS NOT? (TRUE | FALSE | UNKNOWN | NULL_)
    | (PRIOR | DISTINCT) predicate
    | CONNECT_BY_ROOT predicate
    | booleanPrimary SAFE_EQ_ predicate
    | booleanPrimary comparisonOperator (ALL | ANY | SOME) subquery
    | booleanPrimary comparisonOperator predicate
//    | predicate
    ;

// https://www.ibm.com/docs/en/db2/12.1?topic=predicates-search-conditions
searchCondition
    : NOT? predicate (SELECTIVITY numberLiterals)?// ((AND | OR) NOT? predicate (SELECTIVITY numberLiterals)?)*
    ;

// https://www.ibm.com/docs/en/db2/12.1?topic=predicates-basic-predicate
predicate
    : bitExpr NOT? IN subquery
    | PRIOR predicate
    | bitExpr NOT? IN LP_ expr (COMMA_ expr)* RP_
    | bitExpr NOT? IN LP_ expr (COMMA_ expr)* RP_ AND predicate
    | bitExpr NOT? BETWEEN bitExpr AND predicate
    | ARRAY_EXISTS LP_ expr COMMA_ intValue RP_
    | bitExpr NOT? LIKE simpleExpr (ESCAPE simpleExpr)?
    | cursorName IS NOT? (FOUND | OPEN)
    | bitExpr IS NOT? DISTINCT FROM bitExpr
    | JSON_EXISTS LP_ bitExpr (FORMAT (JSON | BSON))? bitExpr (AS pathName)? ((FALSE | TRUE | UNKNOWN | ERROR ) ON ERROR) RP_
    | bitExpr IS NOT? NULL_
    | LP_ bitExpr COMMA_ bitExpr RP_ OVERLAPS LP_ bitExpr COMMA_ bitExpr RP_
    | REGEXP_LIKE LP_ bitExpr COMMA_ bitExpr (COMMA_ intValue)? (COMMA_ string)? (COMMA_ octetsCodeunits)? RP_
    | (UPDATING | INSERTING | DELETING)
    | bitExpr IS? NOT? OF (DYNAMIC TYPE)? LP_ (ONLY? typeName) (COMMA_ (ONLY? typeName))* RP_
    | bitExpr IS NOT? VALIDATED accrdingToClause?
    | bitExpr
    ;

accrdingToClause
    : ACCORDING TO XMLSCHEMA (xmlSchemaIdentification | IN? LP_ xmlSchemaIdentification (COMMA_ xmlSchemaIdentification)* RP_)
    ;

xmlSchemaIdentification
    : ID name
    | (URI string | NO NAMESPACE) (LOCATION string)?
    ;

bitExpr
    : bitExpr VERTICAL_BAR_ bitExpr
    | bitExpr AMPERSAND_ bitExpr
    | bitExpr SIGNED_LEFT_SHIFT_ bitExpr
    | bitExpr SIGNED_RIGHT_SHIFT_ bitExpr
    | bitExpr PLUS_ bitExpr
    | simpleExpr
    | bitExpr MINUS_ bitExpr
    | bitExpr ASTERISK_ bitExpr
    | bitExpr SLASH_ bitExpr
    | bitExpr MOD_ bitExpr
    | bitExpr MOD bitExpr
    | bitExpr CARET_ bitExpr
    | bitExpr DOT_ bitExpr
    | bitExpr ARROW_ bitExpr
    | bitExpr comparisonOperator bitExpr
    ;

simpleExpr
    : functionCall #functionExpr
    | literals #literalsExpr
//    | simpleExpr OR_ simpleExpr #orExpr
    | (PLUS_ | MINUS_ | TILDE_ | NOT_ | BINARY) simpleExpr #unaryExpr
    | ROW? LP_ expr (COMMA_ expr)* RP_ #rowExpr
    | EXISTS? subquery #subqueryExpr
    | LBE_ identifier expr RBE_ #objectAccessExpr
    | caseExpression #caseExpr
    | ROW CHANGE (TOKEN | TIMESTAMP) FOR tableName #rowChangeExpr
    | columnName #columnExpr
    // | ...
    ;

functionCall
    : aggregationFunction | scalarFunction | tableFunction | datalakeFunction | userDefinedFunction
    ;

aggregationFunction
    : aggregationFunctionName LP_ (((DISTINCT | ALL)? expr (COMMA_ expr)*) | ASTERISK_)? (COMMA_ stringLiterals)?
     listaggOverflowClause? orderByClause? RP_ (WITHIN GROUP LP_ orderByClause RP_)? keepClause? overClause? overClause?
    ;

keepClause
    : KEEP LP_ DENSE_RANK (FIRST | LAST) orderByClause RP_ overClause?
    ;

aggregationFunctionName
    : MAX | MIN | SUM | COUNT | AVG | GROUPING | LISTAGG | PERCENT_RANK | PERCENTILE_CONT | PERCENTILE_DISC | CUME_DIST
    | COLLECT | DENSE_RANK | FIRST| LAST | MEDIAN  | STDDEV | STDDEV_SAMP | VARIANCE | ARRAY_AGG | CORRELATION | COUNT_BIG
    | COVARIANCE | COVARIANCE_SAMP | MIN | VARIANCE_SAMP | XMLAGG | XMLGROUP
    ;

listaggOverflowClause
    : ON OVERFLOW (ERROR | (TRUNCATE stringLiterals? ((WITH | WITHOUT) COUNT)?))
    ;

overClause
    : OVER LP_ analyticClause RP_
    ;

analyticClause
    : queryPartitionClause? (orderByClause windowingClause?)?
    ;

queryPartitionClause
    : PARTITION BY (exprs | exprList)
    ;


scalarFunction
    : scalarFunctionName LP_ (expr (COMMA_ expr)*) RP_
    ;

scalarFunctionName
    : ABS | ABSVAL | ACOS | ADD_DAYS | ADD_HOURS | ADD_MINUTES | ADD_MONTHS | ADD_SECONDS | ADD_YEARS | AGE | ARRAY_DELETE | ARRAY_FIRST
    | ARRAY_LAST | ARRAY_NEXT | ARRAY_PRIOR | ASCII | ASCII_STR | ASIN | ATAN | ATAN2 | ATANH | BIGINT | BITARY | BITAND | BITANDNOT
    | BITOR | BITXOR | BITNOT | BLOB | BOOLEAN | BPCHAR | BSON_TO_JSON | BTRIM | CARDINALITY | CEILING | CEIL | CHAR | CHARACTER_LENGTH
    | CHR | CLOB | COALESCE | COLLATION_KEY | COLLATION_KEY_BIT | COMPARE_DECFLOAT | CONCAT | COS | COSH | COT | CURSOR_ROWCOUNT | DATAPARTITIONNUM
    | DATA_MASK | DATE | DATETIME | DATE_PART | DATE_TRUNC | DAY | DAYNAME | DAYOFMONTH | DAYOFWEEK | DAYOFWEEK_ISO | DAYOFYEAR | DAYS | DAYS_BETWEEN
    | DAYS_TO_END_OF_MONTH | DBCLOB | DBPARTITIONNUM | DECFLOAT | DECFLOAT_FORMAT | DECIMAL | DEC | DECODE | DECRYPT_BIN | DECRYPT_CHAR | DEGREES
    | DEREF | DIFFERENCE | DIGITS | DOUBLE_PRECISION | DOUBLE| EMPTY_BLOB | EMPTY_CLOB | EMPTY_DBCLOB | EMPTY_NCLOB | ENCRYPT | EVENT_MON_STATE
    | EXP | EXTRACT | FIRST_DAY | FLOAT | FLOAT4 | FLOAT8 | FLOOR | FROM_UTC_TIMESTAMP | GENERATE_UNIQUE | GETHINT | GRAPHIC | GREATEST | HASH
    | HASH4 | HASH8 | HASHEDVALUE | HEX | HEXTORAW | HOUR | HOURS_BETWEEN | IDENTITY_VAL_LOCAL | IFNULL | INITCAP | INSERT | INSTR | INSTR2
    | INSTR4 | INSTRB | INT | INTERVAL | INTEGER | INT2 | INT4 | INT8 | INTNAND | INTNOR | INTNXOR | INTNNOT | ISNULL | JSON_ARRAY | JSON_OBJECT
    | JSON_QUERY | JSON_TO_BSON | JSON_VALUE | JULIAN_DAY | LAST_DAY | LCASE | LEAST | LEFT | LENGTH | LENGTH2 | LENGTH4 | LENGTHB | LN | LOCATE
    | LOCATE_IN_STRING | LOG10 | LONG_VARCHAR | LONG_VARGRAPHIC | LOWER | LPAD | LTRIM | MAX | MAX_CARDINALITY | MICROSECOND | MIDNIGHT_SECONDS
    | MIN | MINUTE | MINUTES_BETWEEN | MOD | MONTH | MONTHNAME | MONTHS_BETWEEN | MULTIPLY_ALT | NCHAR | NCHR | NCLOB | NVARCHAR | NEXT_DAY
    | NEXT_MONTH | NEXT_QUARTER | NEXT_WEEK | NEXT_YEAR | NORMALIZE_DECFLOAT | NOW | NULLIF | NUMERIC | NVL | NVL2 | OCTET_LENGTH | OVERLAY
    | PARAMETER | POSITION | POSSTR | POW | POWER | QUANTIZE | QUARTER | QUOTE_IDENT | QUOTE_LITERAL | RADIANS | RAISE_ERROR | RAND | RANDOM
    | RAWTOHEX | REAL | REC2XML | REGEXP_COUNT | REGEXP_EXTRACT | REGEXP_INSTR | REGEXP_LIKE | REGEXP_MATCH_COUNT | REGEXP_REPLACE | REGEXP_SUBSTR
    | REPEAT | REPLACE | RID | RID_BIT | RIGHT | ROUND | ROUND_TIMESTAMP | RPAD | RTRIM | SECLABEL | SECLABEL_BY_NAME | SECLABEL_TO_CHAR | SECOND
    | SECONDS_BETWEEN | SIGN | SIN | SINH | SMALLINT | SOUNDEX | SPACE | SQRT | STRIP | STRLEFT | STRPOS | STRRIGHT | SUBSTR | SUBSTR2 | SUBSTR4
    | SUBSTRB | SUBSTRING | TABLE_NAME | TABLE_SCHEMA | TAN | TANH | THIS_MONTH | THIS_QUARTER | THIS_WEEK | THIS_YEAR | TIME | TIMESTAMP | TIMESTAMP_FORMAT
    | TIMESTAMP_ISO | TIMESTAMPDIFF | TIMEZONE | TO_CHAR | TO_DATE | TO_HEX | TO_MULTI_BYTE | TO_NCHAR | TO_NCLOB | TO_NUMBER | TO_SINGLE_BYTE | TO_TIMESTAMP
    | TO_UTC_TIMESTAMP | TOTALORDER | TRANSLATE | TRIM | TRIM_ARRAY | TRUNC_TIMESTAMP | TRUNCATE | TRUNC | TYPE_ID | TYPE_NAME | TYPE_SCHEMA | UCASE
    | UNICODE_STR | UPPER | VALUE | VARBINARY | VARCHAR | VARCHAR_BIT_FORMAT | VARCHAR_FORMAT | VARCHAR_FORMAT_BIT | VARGRAPHIC | VERIFY_GROUP_FOR_USER
    | VERIFY_ROLE_FOR_USER | VERIFY_TRUSTED_CONTEXT_ROLE_FOR_USER | WEEK | WEEK_ISO | WEEKS_BETWEEN | WIDTH_BUCKET | XMLATTRIBUTES | XMLCOMMENT
    | XMLCONCAT | XMLDOCUMENT | XMLELEMENT | XMLFOREST | XMLNAMESPACES | XMLPARSE | XMLPI | XMLQUERY | XMLROW | XMLSERIALIZE | XMLTEXT | XMLVALIDATE
    | XMLXSROBJECTID | XSLTRANSFORM | YEAR | YEARS_BETWEEN | YMD_BETWEEN
    ;


tableFunction
    : tableFunctionName LP_ (expr ((COMMA_ expr)*)) RP_
    ;

tableFunctionName
    : BASE_TABLE | JSON_TABLE | UNNEST | XMLTABLE | SYSPROC DOT_ MEMORY_TABLE
    ;

datalakeFunction
    : datalakeFunctionName LP_ (expr ((COMMA_ expr)*)) RP_
    ;

datalakeFunctionName
    : GET_CACHE_FILE_INFO | GET_CACHE_TABLE_INFO | GET_DATALAKE_CONFIG | HCAT_DESCRIBESCHEMA | HCAT_DESCRIBETAB | LOG_ENTRY
    | TABLE_FILES | TABLE_PARTITIONS | TABLE_SNAPSHOTS | TABLE_SNAPSHOT_REFS
    ;

userDefinedFunction
    : functionName LP_ (expr ((COMMA_ expr)*)) RP_
    ;

//windowExpression
//    : aggregationFunction OVER LP_ windowClause RP_
//    ;
//
//windowClause
//    : HIERARCHY hierarchyRef BETWEEN (precedingBoundary | followingBoundary)
//    (WITHIN (LEVEL | PARENT | ANCESTOR AT LEVEL levelRef))?
//    ;
//
//precedingBoundary
//    : (UNBOUNDED PRECEDING | offsetExpr PRECEDING) AND (CURRENT MEMBER | offsetExpr (PRECEDING | FOLLOWING) | UNBOUNDED FOLLOWING)
//    ;
//
//followingBoundary
//    : (CURRENT MEMBER | offsetExpr FOLLOWING) AND (offsetExpr FOLLOWING | UNBOUNDED FOLLOWING)
//    ;
//
//offsetExpr
//    : expr | numberLiterals
//    ;
//levelRef
//    : identifier
//    ;
//
//hierarchyRef
//    : identifier
//    ;


windowingClause
    : (ROWS | RANGE) ((BETWEEN (UNBOUNDED PRECEDING | CURRENT ROW | expr (PRECEDING | FOLLOWING)) AND (UNBOUNDED FOLLOWING | CURRENT ROW | expr (PRECEDING | FOLLOWING)))
    | (UNBOUNDED PRECEDING | CURRENT ROW | expr PRECEDING))
    ;

caseExpression
    : CASE simpleExpr? caseWhen+ caseElse? END
    ;

caseWhen
    : WHEN expr THEN (updateOpt | deleteOpt | insertOpt)
    ;

updateOpt
    : UPDATE periodSpecification? SET assignmentClause
    ;

deleteOpt
    : DELETE periodSpecification
    ;

insertOpt
    : INSERT insertColumns? valuesClause
    ;

caseElse
    : ELSE expr
    ;

subquery
    : LP_ subselect RP_
    ;

literals
    : stringLiterals
    | numberLiterals
    | dateTimeLiterals
    | hexadecimalLiterals
    | bitValueLiterals
    | booleanLiterals
    | nullValueLiterals
    ;


stringLiterals
    : STRING_
    | NCHAR_TEXT
    | UCHAR_TEXT
    ;

numberLiterals
   : (PLUS_ | MINUS_)? (INTEGER_ | NUMBER_)
   ;

dateTimeLiterals
    : (DATE | TIME | TIMESTAMP) stringLiterals
    | LBE_ identifier stringLiterals RBE_
    ;

hexadecimalLiterals
    : HEX_DIGIT_
    ;

bitValueLiterals
    : BIT_NUM_
    ;

booleanLiterals
    : TRUE | FALSE
    ;

nullValueLiterals
    : NULL_
    ;

todo
    : .*?
    ;

// ID

label
    : identifier
    ;

hostLabel
    : identifier
    ;

libraryName
    : identifier | string
    ;

rowTypeName
    : (owner DOT_)? name
    ;

attributeName
    : identifier
    ;


authorizationName
    : identifier | string
    ;

booleanVariableName
    : identifier
    ;

arrayVariableName
    : identifier
    ;


constraintName
    : identifier
    ;

descriptorName
    : COLON_? identifier
    ;

distinctTypeName
    : identifier
    ;

cursorName
    : identifier
    ;

cursorTypeName
    : identifier
    ;

conditionName
    : identifier
    ;

dataSourceName
    : identifier | string
    ;

remoteCatalogName
    : identifier | string
    ;

remoteSchemaName
    : identifier | string
    ;

remoteTableName
    : identifier | string
    ;

expressionName
    : identifier
    ;

groupName
    : identifier
    ;

policyName
    : identifier
    ;

bufferpoolName
    : identifier
    ;

dbPartitionName
    : identifier
    ;

databaseName
    : identifier
    ;

eventMonitorName
    : identifier
    ;

fieldName
    : identifier
    ;

functionMappingName
    : identifier
    ;

globalVariableName
    : identifier
    ;

hierarchyName
    : identifier
    ;

hostVariableName
    : identifier
    ;

parameterMarker
    : identifier
    ;

templateName
    : identifier
    ;


indexExtensionName
    : identifier
    ;

inputDescriptorName
    : identifier
    ;

maskName
    : identifier
    ;

methodName
    : identifier
    ;

modelName
    : identifier
    ;

moduleName
    : (owner DOT_)? name
    ;

newOwner
    : identifier
    ;

nickName
    : identifier
    ;


oidentifiercolumnName
    : identifier
    ;

optimizationProfileName
    : (owner DOT_)? name
    ;

packageName
    : (owner DOT_)? name
    ;

partitionName
    : identifier
    ;

pathName
    : string
    ;

permissionName
    : identifier
    ;

pipeName
    : string
    ;

procedureName
    : (owner DOT_)? name
    ;

roleName
    : identifier
    ;

rootTableName
    : identifier
    ;

rootViewName
    : identifier
    ;

rowVariableName
    : identifier
    ;

sourceSchemaName
    : identifier
    ;

sourcePackageName
    : identifier
    ;

sourceProcedureName
    : identifier
    ;

sqlParameterName
    : identifier
    ;

sqlVariableName
    : identifier
    ;

transitionVariableName
    : identifier
    ;

savepointName
    : identifier
    ;

specificName
    : identifier
    ;

schema
    : schemaName
    ;

schemaName
    : identifier
    ;

searchMethodName
    : identifier
    ;

serverName
    : identifier
    ;

serverOptionName
    : identifier
    ;

sessionAuthorizationName
    : identifier
    ;

componentName
    : identifier
    ;

secLabelCompName
    : identifier
    ;

securityPolicyName
    : identifier
    ;

securityLabelName
    : (owner DOT_)? name
    ;

sequenceName
    : identifier
    ;

serviceClassName
    : identifier
    ;

serviceSuperclassName
    : identifier
    ;

storagegroupName
    : identifier
    ;

supertypeName
    : identifier
    ;

superviewName
    : identifier
    ;

serviceSubclassName
    : identifier
    ;

statementName
    : identifier
    ;


tablespaceName
    : identifier
    ;

targetIdentifier
    : (owner DOT_)? name
    ;

thresholdName
    : identifier
    ;

contextName
    : identifier
    ;

usageListName
    : identifier
    ;

typeName
    : (owner DOT_)? name
    ;

typeMappingName
    : identifier
    ;

typedTableName
    : identifier
    ;

typedViewName
    : identifier
    ;

userMappingOptionName
    : identifier
    ;

variableName
    : (owner DOT_)? name
    ;

workActionSetName
    : identifier
    ;

workClassSetName
    : identifier
    ;

workloadName
    : identifier
    ;

workActionName
    : identifier
    ;

workClassName
    : identifier
    ;

wrapperName
    : identifier
    ;

wrapperOptionName
    : identifier
    ;

xsrobjectName
    : (owner DOT_)? name
    ;

parameterName
    : identifier
    ;

cursorVariableName
    : identifier
    ;

aliasName
    : identifier
    ;


dbPartitionGroupName
    : identifier
    ;


sourceIndexName
    : (owner DOT_)? name
    ;

sourceTableName
    : (owner DOT_)? name
    ;

sourceStoragegroupName
    : (owner DOT_)? name
    ;

targetStoragegroupName
    : (owner DOT_)? name
    ;

sourceTablespaceName
    : (owner DOT_)? name
    ;

targetTablespaceName
    : (owner DOT_)? name
    ;

unqualifiedFunctionName
    : identifier
    ;

unqualifiedProcedureName
    : identifier
    ;

unqualifiedSpecificName
    : identifier
    ;

periodName
    : identifier
    ;

historyTableName
    : identifier
    ;

identifier
    : IDENTIFIER_ | DOUBLE_QUOTED_TEXT | I_CURSOR | QUESTION_ | unreservedWord
    ;

unreservedWord
    : MAX | MIN | SUM | COUNT | AVG | GROUPING | LISTAGG | PERCENT_RANK | PERCENTILE_CONT | PERCENTILE_DISC | CUME_DIST
    | COLLECT | DENSE_RANK | FIRST| LAST | MEDIAN  | STDDEV | STDDEV_SAMP | VARIANCE | ARRAY_AGG | CORRELATION | COUNT_BIG
    | COVARIANCE | COVARIANCE_SAMP | MIN | VARIANCE_SAMP | XMLAGG | XMLGROUP ABS | ABSVAL | ACOS | ADD_DAYS | ADD_HOURS | ADD_MINUTES | ADD_MONTHS
    | ADD_SECONDS | ADD_YEARS | AGE | ARRAY_DELETE | ARRAY_FIRST GET_CACHE_FILE_INFO | GET_CACHE_TABLE_INFO | GET_DATALAKE_CONFIG | HCAT_DESCRIBESCHEMA
    | HCAT_DESCRIBETAB | LOG_ENTRY | TABLE_FILES | TABLE_PARTITIONS | TABLE_SNAPSHOTS | TABLE_SNAPSHOT_REFS
    | ARRAY_LAST | ARRAY_NEXT | ARRAY_PRIOR | ASCII | ASCII_STR | ASIN | ATAN | ATAN2 | ATANH | BIGINT | BITARY | BITAND | BITANDNOT
    | BITOR | BITXOR | BITNOT | BLOB | BOOLEAN | BPCHAR | BSON_TO_JSON | BTRIM | CARDINALITY | CEILING | CEIL | CHAR | CHARACTER_LENGTH
    | CHR | CLOB | COALESCE | COLLATION_KEY | COLLATION_KEY_BIT | COMPARE_DECFLOAT | CONCAT | COS | COSH | COT | CURSOR_ROWCOUNT | DATAPARTITIONNUM
    | DATA_MASK | DATE | DATETIME | DATE_PART | DATE_TRUNC | DAY | DAYNAME | DAYOFMONTH | DAYOFWEEK | DAYOFWEEK_ISO | DAYOFYEAR | DAYS | DAYS_BETWEEN
    | DAYS_TO_END_OF_MONTH | DBCLOB | DBPARTITIONNUM | DECFLOAT | DECFLOAT_FORMAT | DECIMAL | DEC | DECODE | DECRYPT_BIN | DECRYPT_CHAR | DEGREES
    | DEREF | DIFFERENCE | DIGITS | DOUBLE_PRECISION | DOUBLE| EMPTY_BLOB | EMPTY_CLOB | EMPTY_DBCLOB | EMPTY_NCLOB | ENCRYPT | EVENT_MON_STATE
    | EXP | EXTRACT | FIRST_DAY | FLOAT | FLOAT4 | FLOAT8 | FLOOR | FROM_UTC_TIMESTAMP | GENERATE_UNIQUE | GETHINT | GRAPHIC | GREATEST | HASH
    | HASH4 | HASH8 | HASHEDVALUE | HEX | HEXTORAW | HOUR | HOURS_BETWEEN | IDENTITY_VAL_LOCAL | IFNULL | INITCAP | INSERT | INSTR | INSTR2
    | INSTR4 | INSTRB | INT | INTERVAL | INTEGER | INT2 | INT4 | INT8 | INTNAND | INTNOR | INTNXOR | INTNNOT | ISNULL | JSON_ARRAY | JSON_OBJECT
    | JSON_QUERY | JSON_TO_BSON | JSON_VALUE | JULIAN_DAY | LAST_DAY | LCASE | LEAST | LEFT | LENGTH | LENGTH2 | LENGTH4 | LENGTHB | LN | LOCATE
    | LOCATE_IN_STRING | LOG10 | LONG_VARCHAR | LONG_VARGRAPHIC | LOWER | LPAD | LTRIM | MAX | MAX_CARDINALITY | MICROSECOND | MIDNIGHT_SECONDS
    | MIN | MINUTE | MINUTES_BETWEEN | MOD | MONTH | MONTHNAME | MONTHS_BETWEEN | MULTIPLY_ALT | NCHAR | NCHR | NCLOB | NVARCHAR | NEXT_DAY
    | NEXT_MONTH | NEXT_QUARTER | NEXT_WEEK | NEXT_YEAR | NORMALIZE_DECFLOAT | NOW | NULLIF | NUMERIC | NVL | NVL2 | OCTET_LENGTH | OVERLAY
    | PARAMETER | POSITION | POSSTR | POW | POWER | QUANTIZE | QUARTER | QUOTE_IDENT | QUOTE_LITERAL | RADIANS | RAISE_ERROR | RAND | RANDOM
    | RAWTOHEX | REAL | REC2XML | REGEXP_COUNT | REGEXP_EXTRACT | REGEXP_INSTR | REGEXP_LIKE | REGEXP_MATCH_COUNT | REGEXP_REPLACE | REGEXP_SUBSTR
    | REPEAT | REPLACE | RID | RID_BIT | RIGHT | ROUND | ROUND_TIMESTAMP | RPAD | RTRIM | SECLABEL | SECLABEL_BY_NAME | SECLABEL_TO_CHAR | SECOND
    | SECONDS_BETWEEN | SIGN | SIN | SINH | SMALLINT | SOUNDEX | SPACE | SQRT | STRIP | STRLEFT | STRPOS | STRRIGHT | SUBSTR | SUBSTR2 | SUBSTR4
    | SUBSTRB | SUBSTRING | TABLE_NAME | TABLE_SCHEMA | TAN | TANH | THIS_MONTH | THIS_QUARTER | THIS_WEEK | THIS_YEAR | TIME | TIMESTAMP | TIMESTAMP_FORMAT
    | TIMESTAMP_ISO | TIMESTAMPDIFF | TIMEZONE | TO_CHAR | TO_DATE | TO_HEX | TO_MULTI_BYTE | TO_NCHAR | TO_NCLOB | TO_NUMBER | TO_SINGLE_BYTE | TO_TIMESTAMP
    | TO_UTC_TIMESTAMP | TOTALORDER | TRANSLATE | TRIM | TRIM_ARRAY | TRUNC_TIMESTAMP | TRUNCATE | TRUNC | TYPE_ID | TYPE_NAME | TYPE_SCHEMA | UCASE
    | UNICODE_STR | UPPER | VALUE | VARBINARY | VARCHAR | VARCHAR_BIT_FORMAT | VARCHAR_FORMAT | VARCHAR_FORMAT_BIT | VARGRAPHIC | VERIFY_GROUP_FOR_USER
    | VERIFY_ROLE_FOR_USER | VERIFY_TRUSTED_CONTEXT_ROLE_FOR_USER | WEEK | WEEK_ISO | WEEKS_BETWEEN | WIDTH_BUCKET | XMLATTRIBUTES | XMLCOMMENT
    | XMLCONCAT | XMLDOCUMENT | XMLELEMENT | XMLFOREST | XMLNAMESPACES | XMLPARSE | XMLPI | XMLQUERY | XMLROW | XMLSERIALIZE | XMLTEXT | XMLVALIDATE
    | XMLXSROBJECTID | XSLTRANSFORM | YEAR | YEARS_BETWEEN | YMD_BETWEEN | BASE_TABLE | JSON_TABLE | UNNEST | XMLTABLE | SYSPROC | MEMORY_TABLE
    | RR | RS | CS | UR | ACTIVITY | GROUP | MODULE | TBSPACEADM | TENANT | MODEL | REVERT | LEVEL | SPECIAL | BONUS | NULL_ | ACTIVITYMETRICS
    | ACTIVITYSTMT | ACTIVITYVALS | CONTROL | CHANGESUMMARY | EVMONSTART| TXNCOMPLETION | DDLSTMTEXEC | DBDBMCFG | REGVAR | UTILSTART | UTILSTOP
    | UTILPHASE | UTILLOCATION | LOCK_PARTICIPANTS | LOCK_PARTICIPANT_ACTIVITIES | LOCK_ACTIVITY_VALUES | IN | PKGCACHE | PKGCACHE_METRICS | PKGCACHE_STMT_ARGS
    | THRESHOLDVIOLATIONS | UOW | UOW_METRICS | UOW_PACKAGE_LIST | UOW_EXECUTABLE_LIST | DEDLOCKS | APPL_ID | AUTH_ID | APPL_NAME | DENSE_RANK | SIZE | TEXT
    | SESSION_USER | RATE | CLIENT | BEGIN | END | NAME | YEARS | MEMBER | VERSION | PASSWORD | OR | ATOMIC | GENERIC | CURSOR | REF | NUMBER | STATE
    | SYSIBM | CURSOR | PURGE | DATALAKE | NOSCAN | K | M | G | SYSADM | SYSCTRL | SYSMAINT | SYSMON | SESSION | SQLDA | CHECK | ID | NEW | TYPE
    | AS | ARRAY | OPTIMIZATION | TABLES | HEX_DIGIT_ | MEASURE | CHANGES | CHANGE | COLUMN | CONNECT_BY_ROOT | PRIOR | FOLLOWING | PRECEDING | ESCAPE
    | BUFFERPOOL | BUFFER | POOL | SOME | SELECTIVITY | TOKEN | ARRAY_EXISTS | JSON_EXISTS | FORMAT | JSON | BSON | UPDATING | INSERTING | DELETING
    | VALIDATED | ACCORDING | XMLSCHEMA | URI | LOCATION | DECLARE | DEFAULT | DOUBLE | FOR | DEADLOCKS | DISABLE | ENABLE | ACTIVITIES
    ;
