/*
IBM Db2 SQL grammar.
The MIT License (MIT).

Copyright (c) 2023, <PERSON><PERSON><PERSON>.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:
The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.
THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/

lexer grammar Db2Lexer;

options { caseInsensitive = true; }

ACCESS                              : 'ACCESS';
ACCESSCTRL                          : 'ACCESSCTRL';
ACCESSTOKEN                         : 'ACCESSTOKEN';
ACCESSTOKENTYPE                     : 'ACCESSTOKENTYPE';
ACCUMULATE                          : 'ACCUMULATE';
ACTION                              : 'ACTION';
ACTIVATE                            : 'ACTIVATE';
ACTIVE                              : 'ACTIVE';
ACTIVITIES                          : 'ACTIVITIES';
ACTIVITY                            : 'ACTIVITY';
ACTIVITYTOTALRUNTIME                : 'ACTIVITYTOTALRUNTIME';
ACTIVITYTOTALRUNTIMEINALLSC         : 'ACTIVITYTOTALRUNTIMEINALLSC';
ACTIVITYTOTALTIME                   : 'ACTIVITYTOTALTIME';
ACTUALS                             : 'ACTUALS';
ADAPTIVE                            : 'ADAPTIVE';
ADC                                 : 'ADC';
ADD                                 : 'ADD';
ADDRESS                             : 'ADDRESS';
ADMIN                               : 'ADMIN';
ADMISSION                           : 'ADMISSION';
AFTER                               : 'AFTER';
AGE                                 : 'AGE';
AGGREGATE                           : 'AGGREGATE';
AGGSQLTEMPSPACE                     : 'AGGSQLTEMPSPACE';
ALIAS                               : 'ALIAS';
ALL                                 : 'ALL';
ALLOCATE                            : 'ALLOCATE';
ALLOW                               : 'ALLOW';
ALTER                               : 'ALTER';
ALTERIN                             : 'ALTERIN';
ALWAYS                              : 'ALWAYS';
ANALYZE                             : 'ANALYZE';
ANCHOR                              : 'ANCHOR';
AND                                 : 'AND';
ANY                                 : 'ANY';
APIKEY                              : 'APIKEY';
APPEND                              : 'APPEND';
APPLICATION                         : 'APPLICATION';
APPLNAME                            : 'APPLNAME';
ARRAY                               : 'ARRAY';
AS                                  : 'AS';
ASC                                 : 'ASC';
ASCII                               : 'ASCII';
ASENSITIVE                          : 'ASENSITIVE';
ASSOCIATE                           : 'ASSOCIATE';
ASYNCHRONY                          : 'ASYNCHRONY';
AT                                  : 'AT';
ATOMIC                              : 'ATOMIC';
ATTACH                              : 'ATTACH';
ATTRIBUTE                           : 'ATTRIBUTE';
ATTRIBUTES                          : 'ATTRIBUTES';
AUDIT                               : 'AUDIT';
AUTHENTICATION                      : 'AUTHENTICATION';
AUTHID                              : 'AUTHID';
AUTHORIZATION                       : 'AUTHORIZATION';
AUTHORIZATIONS                      : 'AUTHORIZATIONS';
AUTHORIZED                          : 'AUTHORIZED';
AUTOMATIC                           : 'AUTOMATIC';
AUTONOMOUS                          : 'AUTONOMOUS';
AUTORESIZE                          : 'AUTORESIZE';
AUTOSTART                           : 'AUTOSTART';
BACKUP                              : 'BACKUP';
BASE                                : 'BASE';
BATCH                               : 'BATCH';
BEFORE                              : 'BEFORE';
BEGIN                               : 'BEGIN';
BERNOULLI                           : 'BERNOULLI';
BETWEEN                             : 'BETWEEN';
BIGINT                              : 'BIGINT';
BIN                                 : 'BIN';
BINARY                              : 'BINARY';
BIND                                : 'BIND';
BINDADD                             : 'BINDADD';
BIT                                 : 'BIT';
BLOB                                : 'BLOB';
BLOCKED                             : 'BLOCKED';
BLOCKING                            : 'BLOCKING';
BLOCKINSERT                         : 'BLOCKINSERT';
BLOCKSIZE                           : 'BLOCKSIZE';
BODY                                : 'BODY';
BOOLEAN                             : 'BOOLEAN';
BOTH                                : 'BOTH';
BREAK                               : 'BREAK';
BUFFER                              : 'BUFFER';
BUFFERPOOL                          : 'BUFFERPOOL';
BUFFERPOOLS                         : 'BUFFERPOOLS';
BUFFERSIZE                          : 'BUFFERSIZE';
BUILD                               : 'BUILD';
BUSINESS_TIME                       : 'BUSINESS_TIME';
BY                                  : 'BY';
CACHE                               : 'CACHE';
CACHING                             : 'CACHING';
CALL                                : 'CALL';
CALLED                              : 'CALLED';
CALLER                              : 'CALLER';
CAPTURE                             : 'CAPTURE';
CARDINALITY                         : 'CARDINALITY';
CASCADE                             : 'CASCADE';
CASCADED                            : 'CASCADED';
CAST                                : 'CAST';
CATEGORIES                          : 'CATEGORIES';
CBMCFGVALUES                        : 'CBMCFGVALUES';
CCSID                               : 'CCSID';
CFGALL                              : 'CFGALL';
CHANGE                              : 'CHANGE';
CHANGES                             : 'CHANGES';
CHAR                                : 'CHAR';
CHARACTER                           : 'CHARACTER';
CHECK                               : 'CHECK';
CHECKING                            : 'CHECKING';
CLASS_                              : 'CLASS';
CLIENT_ACCTNG                       : 'CLIENT_ACCTNG';
CLIENT_APPLNAME                     : 'CLIENT_APPLNAME';
CLIENT_USERID                       : 'CLIENT_USERID';
CLIENT_WRKSTNNAME                   : 'CLIENT_WRKSTNNAME';
CLOB                                : 'CLOB';
CLOSE                               : 'CLOSE';
CLR                                 : 'CLR';
COBOL                               : 'COBOL';
CODEUNITS16                         : 'CODEUNITS16';
CODEUNITS32                         : 'CODEUNITS32';
COLLECT                             : 'COLLECT';
COLUMN                              : 'COLUMN';
COLUMNS                             : 'COLUMNS';
COMMENT                             : 'COMMENT';
COMMIT                              : 'COMMIT';
COMPACT                             : 'COMPACT';
COMPARISONS                         : 'COMPARISONS';
COMPILATION                         : 'COMPILATION';
COMPONENT                           : 'COMPONENT';
COMPONENTS                          : 'COMPONENTS';
COMPOUND                            : 'COMPOUND';
COMPRESS                            : 'COMPRESS';
COMPRESSION                         : 'COMPRESSION';
CONCURRENTDBCOORDACTIVITIES         : 'CONCURRENTDBCOORDACTIVITIES';
CONCURRENTWORKLOADACTIVITIES        : 'CONCURRENTWORKLOADACTIVITIES';
CONCURRENTWORKLOADOCCURRENCES       : 'CONCURRENTWORKLOADOCCURRENCES';
CONDITION                           : 'CONDITION';
CONFIRM                             : 'CONFIRM';
CONNECT                             : 'CONNECT';
CONNECTION                          : 'CONNECTION';
CONNECTIONIDLETIME                  : 'CONNECTIONIDLETIME';
CONSTANT                            : 'CONSTANT';
CONSTRAINT                          : 'CONSTRAINT';
CONTAINERS                          : 'CONTAINERS';
CONTAINS                            : 'CONTAINS';
CONTEXT                             : 'CONTEXT';
CONTINUE                            : 'CONTINUE';
CONTROL                             : 'CONTROL';
CONVERT                             : 'CONVERT';
COORDINATOR                         : 'COORDINATOR';
CORRELATOR                          : 'CORRELATOR';
COUNT                               : 'COUNT';
CPP                                 : 'CPP';
CPU                                 : 'CPU';
CPUTIME                             : 'CPUTIME';
CPUTIMEINSC                         : 'CPUTIMEINSC';
CREATE                              : 'CREATE';
CREATEIN                            : 'CREATEIN';
CREATETAB                           : 'CREATETAB';
CREATE_EXTERNAL_ROUTINE             : 'CREATE_EXTERNAL_ROUTINE';
CREATE_NOT_FENCED_ROUTINE           : 'CREATE_NOT_FENCED_ROUTINE';
CREATE_SECURE_OBJECT                : 'CREATE_SECURE_OBJECT';
CREATE_SOURCE_OBJECT                : 'CREATE_SOURCE_OBJECT';
CRITICAL                            : 'CRITICAL';
CROSS                               : 'CROSS';
CS                                  : 'CS';
CUBE                                : 'CUBE';
CURRENT                             : 'CURRENT';
CURRENT_PATH                        : 'CURRENT_PATH';
CURRENT_USER                        : 'CURRENT_USER';
CURSOR                              : 'CURSOR';
CURSORS                             : 'CURSORS';
CUSTOM                              : 'CUSTOM';
CYCLE                               : 'CYCLE';
C_                                  : 'C';
DATA                                : 'DATA';
DATAACCESS                          : 'DATAACCESS';
DATABASE                            : 'DATABASE';
DATATAGINSC                         : 'DATATAGINSC';
DATE                                : 'DATE';
DAY                                 : 'DAY';
DAYS                                : 'DAYS';
DB                                  : 'DB';
DB2GENERAL                          : 'DB2GENERAL';
DB2LBACREADARRAY                    : 'DB2LBACREADARRAY';
DB2LBACREADSET                      : 'DB2LBACREADSET';
DB2LBACREADTREE                     : 'DB2LBACREADTREE';
DB2LBACRULES                        : 'DB2LBACRULES';
DB2LBACWRITEARRAY                   : 'DB2LBACWRITEARRAY';
DB2LBACWRITESET                     : 'DB2LBACWRITESET';
DB2LBACWRITETREE                    : 'DB2LBACWRITETREE';
DB2SQL                              : 'DB2SQL';
DB2_RETURN_STATUS                   : 'DB2_RETURN_STATUS';
DB2_SQL_NESTING_LEVEL               : 'DB2_SQL_NESTING_LEVEL';
DB2_TOKEN_STRING                    : 'DB2_TOKEN_STRING';
DBADM                               : 'DBADM';
DBCFG                               : 'DBCFG';
DBCFGVALUES                         : 'DBCFGVALUES';
DBCLOB                              : 'DBCLOB';
DBINFO                              : 'DBINFO';
DBMCFG                              : 'DBMCFG';
DBPARTITIONNUM                      : 'DBPARTITIONNUM';
DBPARTITIONNUMS                     : 'DBPARTITIONNUMS';
DDL                                 : 'DDL';
DDLALL                              : 'DDLALL';
DDLDATA                             : 'DDLDATA';
DDLFEDERATED                        : 'DDLFEDERATED';
DDLMONITOR                          : 'DDLMONITOR';
DDLSECURITY                         : 'DDLSECURITY';
DDLSQL                              : 'DDLSQL';
DDLSTORAGE                          : 'DDLSTORAGE';
DDLWLM                              : 'DDLWLM';
DDLXML                              : 'DDLXML';
DEACTIVATE                          : 'DEACTIVATE';
DEADLOCK                            : 'DEADLOCK';
DEC                                 : 'DEC';
DECFLOAT                            : 'DECFLOAT';
DECIMAL                             : 'DECIMAL';
DECLARE                             : 'DECLARE';
DECOMPOSITION                       : 'DECOMPOSITION';
DEFAULT                             : 'DEFAULT';
DEFAULTS                            : 'DEFAULTS';
DEFERRED                            : 'DEFERRED';
DEGREE_                             : 'DEGREE';
DELETE                              : 'DELETE';
DELETEIN                            : 'DELETEIN';
DESC                                : 'DESC';
DESCRIBE                            : 'DESCRIBE';
DESCRIPTOR                          : 'DESCRIPTOR';
DETACH                              : 'DETACH';
DETAILED                            : 'DETAILED';
DETAILS                             : 'DETAILS';
DETERMINISTIC                       : 'DETERMINISTIC';
DEVICE                              : 'DEVICE';
DIAGNOSTICS                         : 'DIAGNOSTICS';
DIMENSIONS                          : 'DIMENSIONS';
DISABLE                             : 'DISABLE';
DISALLOW                            : 'DISALLOW';
DISCONNECT                          : 'DISCONNECT';
DISPATCH                            : 'DISPATCH';
DISTINCT                            : 'DISTINCT';
DISTRIBUTE                          : 'DISTRIBUTE';
DISTRIBUTED                         : 'DISTRIBUTED';
DISTRIBUTION                        : 'DISTRIBUTION';
DML                                 : 'DML';
DO                                  : 'DO';
DOUBLE                              : 'DOUBLE';
DROP                                : 'DROP';
DROPIN                              : 'DROPIN';
DROPPED                             : 'DROPPED';
DYNAMIC                             : 'DYNAMIC';
EACH                                : 'EACH';
ELEMENT                             : 'ELEMENT';
ELSE                                : 'ELSE';
ELSEIF                              : 'ELSEIF';
EMPTY                               : 'EMPTY';
EMPTY_BLOB                          : 'EMPTY_BLOB';
EMPTY_CLOB                          : 'EMPTY_CLOB';
EMPTY_DBCLOB                        : 'EMPTY_DBCLOB';
EMPTY_NCLOB                         : 'EMPTY_NCLOB';
ENABLE                              : 'ENABLE';
ENCRYPTION                          : 'ENCRYPTION';
END                                 : 'END';
ENDING                              : 'ENDING';
ENFORCED                            : 'ENFORCED';
ENFORCEMENT                         : 'ENFORCEMENT';
ENVIRONMENT                         : 'ENVIRONMENT';
ERROR                               : 'ERROR';
ESTIMATEDCOST                       : 'ESTIMATEDCOST';
ESTIMATEDSQLCOST                    : 'ESTIMATEDSQLCOST';
EVALUATE                            : 'EVALUATE';
EVENT                               : 'EVENT';
EVERY                               : 'EVERY';
EXACT                               : 'EXACT';
EXCEEDED                            : 'EXCEEDED';
EXCEPT                              : 'EXCEPT';
EXCEPTION                           : 'EXCEPTION';
EXCLUDING                           : 'EXCLUDING';
EXCLUSIVE                           : 'EXCLUSIVE';
EXECUTABLE                          : 'EXECUTABLE';
EXECUTE                             : 'EXECUTE';
EXECUTEIN                           : 'EXECUTEIN';
EXECUTETIME                         : 'EXECUTETIME';
EXECUTION                           : 'EXECUTION';
EXEMPTION                           : 'EXEMPTION';
EXISTS                              : 'EXISTS';
EXPLAIN                             : 'EXPLAIN';
EXPRESSION                          : 'EXPRESSION';
EXTEND                              : 'EXTEND';
EXTENDED                            : 'EXTENDED';
EXTENSION                           : 'EXTENSION';
EXTENTSIZE                          : 'EXTENTSIZE';
EXTERNAL                            : 'EXTERNAL';
FAILURE                             : 'FAILURE';
FALSE                               : 'FALSE';
FEDERATED                           : 'FEDERATED';
FEDERATED_TOOL                      : 'FEDERATED_TOOL';
FENCED                              : 'FENCED';
FETCH                               : 'FETCH';
FIFO                                : 'FIFO';
FILE                                : 'FILE';
FILTER                              : 'FILTER';
FINAL                               : 'FINAL';
FINALIZE                            : 'FINALIZE';
FIRST                               : 'FIRST';
FLOAT                               : 'FLOAT';
FLUSH                               : 'FLUSH';
FOR                                 : 'FOR';
FORCE                               : 'FORCE';
FOREIGN                             : 'FOREIGN';
FOUND                               : 'FOUND';
FREE                                : 'FREE';
FROM                                : 'FROM';
FULL                                : 'FULL';
FUNCTION                            : 'FUNCTION';
G                                   : 'G';
GENERAL                             : 'GENERAL';
GENERATE                            : 'GENERATE';
GENERATED                           : 'GENERATED';
GENERIC                             : 'GENERIC';
GET                                 : 'GET';
GLOBAL                              : 'GLOBAL';
GO                                  : 'GO';
GOTO                                : 'GOTO';
GRANT                               : 'GRANT';
GRAPHIC                             : 'GRAPHIC';
GROUP                               : 'GROUP';
GROUPING                            : 'GROUPING';
HARD                                : 'HARD';
HASH                                : 'HASH';
HIDDEN_                             : 'HIDDEN';
HIERARCHY                           : 'HIERARCHY';
HIGH                                : 'HIGH';
HISTOGRAM                           : 'HISTOGRAM';
HISTORY                             : 'HISTORY';
HOLD                                : 'HOLD';
HOUR                                : 'HOUR';
HOURS                               : 'HOURS';
IDENTITY                            : 'IDENTITY';
IF                                  : 'IF';
IGNORE                              : 'IGNORE';
IMMEDIATE                           : 'IMMEDIATE';
IMPLEMENTATION                      : 'IMPLEMENTATION';
IMPLICIT                            : 'IMPLICIT';
IMPLICITLY                          : 'IMPLICITLY';
IMPLICIT_SCHEMA                     : 'IMPLICIT_SCHEMA';
IN                                  : 'IN';
INACTIVE                            : 'INACTIVE';
INCLUDE                             : 'INCLUDE';
INCLUDING                           : 'INCLUDING';
INCLUSIVE                           : 'INCLUSIVE';
INCREASESIZE                        : 'INCREASESIZE';
INCREMENT                           : 'INCREMENT';
INCREMENTAL                         : 'INCREMENTAL';
INDEX                               : 'INDEX';
INDEXES                             : 'INDEXES';
INFIX                               : 'INFIX';
INHERIT                             : 'INHERIT';
INITIALIZE                          : 'INITIALIZE';
INITIALLY                           : 'INITIALLY';
INITIALSIZE                         : 'INITIALSIZE';
INLINE                              : 'INLINE';
INNER                               : 'INNER';
INOUT                               : 'INOUT';
INPUT                               : 'INPUT';
INSENSITIVE                         : 'INSENSITIVE';
INSERT                              : 'INSERT';
INSERTIN                            : 'INSERTIN';
INSTANTIABLE                        : 'INSTANTIABLE';
INSTEAD                             : 'INSTEAD';
INT                                 : 'INT';
INTEGER                             : 'INTEGER';
INTEGRITY                           : 'INTEGRITY';
INTERACTIVE                         : 'INTERACTIVE';
INTERARRIVALTIME                    : 'INTERARRIVALTIME';
INTO                                : 'INTO';
INVALIDATION                        : 'INVALIDATION';
IS                                  : 'IS';
ISOLATION                           : 'ISOLATION';
ITERATE                             : 'ITERATE';
JAVA                                : 'JAVA';
JOIN                                : 'JOIN';
K                                   : 'K';
KEEP                                : 'KEEP';
KEY                                 : 'KEY';
LABEL                               : 'LABEL';
LANGUAGE                            : 'LANGUAGE';
LARGE                               : 'LARGE';
LAST                                : 'LAST';
LATENCY                             : 'LATENCY';
LATERAL                             : 'LATERAL';
LC_MESSAGES_                        : 'LC_MESSAGES';
LC_TIME_                            : 'LC_TIME';
LEAVE                               : 'LEAVE';
LEFT                                : 'LEFT';
LENGTH                              : 'LENGTH';
LEVEL                               : 'LEVEL';
LIBRARY                             : 'LIBRARY';
LIFETIME                            : 'LIFETIME';
LIKE                                : 'LIKE';
LIMIT                               : 'LIMIT';
LIST                                : 'LIST';
LOAD                                : 'LOAD';
LOCAL                               : 'LOCAL';
LOCALE                              : 'LOCALE';
LOCATOR                             : 'LOCATOR';
LOCATORS                            : 'LOCATORS';
LOCK                                : 'LOCK';
LOCKING                             : 'LOCKING';
LOCKS                               : 'LOCKS';
LOCKSIZE                            : 'LOCKSIZE';
LOG                                 : 'LOG';
LOGGED                              : 'LOGGED';
LOGICAL                             : 'LOGICAL';
LONG                                : 'LONG';
LONGVAR                             : 'LONGVAR';
LOOP                                : 'LOOP';
LOW                                 : 'LOW';
LOWER                               : 'LOWER';
M                                   : 'M';
MAIN                                : 'MAIN';
MAINTAINED                          : 'MAINTAINED';
MANAGED                             : 'MANAGED';
MANUALSTART                         : 'MANUALSTART';
MAP                                 : 'MAP';
MAPPING                             : 'MAPPING';
MARK                                : 'MARK';
MASK                                : 'MASK';
MATCHING                            : 'MATCHING';
MATERIALIZED                        : 'MATERIALIZED';
MAX                                 : 'MAX';
MAXFILES                            : 'MAXFILES';
MAXFILESIZE                         : 'MAXFILESIZE';
MAXIMUM                             : 'MAXIMUM';
MAXSIZE                             : 'MAXSIZE';
MAXVALUE                            : 'MAXVALUE';
MDC                                 : 'MDC';
MEDIUM                              : 'MEDIUM';
MEMBER                              : 'MEMBER';
MEMBERS                             : 'MEMBERS';
MERGE                               : 'MERGE';
MESSAGE_TEXT                        : 'MESSAGE_TEXT';
METHOD                              : 'METHOD';
METRICS                             : 'METRICS';
MICROSECOND                         : 'MICROSECOND';
MICROSECONDS                        : 'MICROSECONDS';
MINIMUM                             : 'MINIMUM';
MINUTE                              : 'MINUTE';
MINUTES                             : 'MINUTES';
MINVALUE                            : 'MINVALUE';
MIXED                               : 'MIXED';
MODE                                : 'MODE';
MODIFICATION                        : 'MODIFICATION';
MODIFIES                            : 'MODIFIES';
MONITOR                             : 'MONITOR';
MONTH                               : 'MONTH';
MONTHS                              : 'MONTHS';
MORE_                               : 'MORE';
MOVEMENT                            : 'MOVEMENT';
MOVETABLE                           : 'MOVETABLE';
MULTIPLIER                          : 'MULTIPLIER';
NAME                                : 'NAME';
NATIONAL                            : 'NATIONAL';
NCHAR                               : 'NCHAR';
NCLOB                               : 'NCLOB';
NESTED                              : 'NESTED';
NEW                                 : 'NEW';
NEXT                                : 'NEXT';
NICKNAME                            : 'NICKNAME';
NO                                  : 'NO';
NONBLOCKED                          : 'NONBLOCKED';
NONE                                : 'NONE';
NORCAC                              : 'NORCAC';
NORMAL                              : 'NORMAL';
NOT                                 : 'NOT';
NPSGENERIC                          : 'NPSGENERIC';
NULLS                               : 'NULLS';
NULL_                               : 'NULL';
NUM                                 : 'NUM';
NUMBER                              : 'NUMBER';
NUMBLOCKPAGES                       : 'NUMBLOCKPAGES';
NUMERIC                             : 'NUMERIC';
NUM_EXECUTIONS                      : 'NUM_EXECUTIONS';
NVARCHAR                            : 'NVARCHAR';
OBJECT                              : 'OBJECT';
OBJMAINT                            : 'OBJMAINT';
OCCURENCE                           : 'OCCURENCE';
OCTETS                              : 'OCTETS';
OF                                  : 'OF';
OFF                                 : 'OFF';
OFFSET                              : 'OFFSET';
OLD                                 : 'OLD';
OLE                                 : 'OLE';
OLEDB                               : 'OLEDB';
ON                                  : 'ON';
ONCE                                : 'ONCE';
ONLINE                              : 'ONLINE';
ONLINERECOVERY                      : 'ONLINERECOVERY';
ONLY                                : 'ONLY';
OPEN                                : 'OPEN';
OPTIMIZATION                        : 'OPTIMIZATION';
OPTIMIZE                            : 'OPTIMIZE';
OPTION                              : 'OPTION';
OPTIONS                             : 'OPTIONS';
OR                                  : 'OR';
ORDER                               : 'ORDER';
ORDINALITY                          : 'ORDINALITY';
ORGANIZED                           : 'ORGANIZED';
OUT                                 : 'OUT';
OUTBOUND                            : 'OUTBOUND';
OUTER                               : 'OUTER';
OUTPUT                              : 'OUTPUT';
OVER                                : 'OVER';
OVERFLOW                            : 'OVERFLOW';
OVERHEAD                            : 'OVERHEAD';
OVERLAPS                            : 'OVERLAPS';
OVERRIDE_                           : 'OVERRIDE';
OVERRIDING                          : 'OVERRIDING';
OWNERSHIP                           : 'OWNERSHIP';
PACKAGE                             : 'PACKAGE';
PACKAGESET                          : 'PACKAGESET';
PAGESIZE                            : 'PAGESIZE';
PARALLEL                            : 'PARALLEL';
PARAMETER                           : 'PARAMETER';
PARAMETERS                          : 'PARAMETERS';
PARTITION                           : 'PARTITION';
PARTITIONINGS                       : 'PARTITIONINGS';
PARTITIONS                          : 'PARTITIONS';
PASSTHRU                            : 'PASSTHRU';
PASSWORD                            : 'PASSWORD';
PATH                                : 'PATH';
PCTDEACTIVATE                       : 'PCTDEACTIVATE';
PCTFREE                             : 'PCTFREE';
PERCENT                             : 'PERCENT';
PERFORM                             : 'PERFORM';
PERIOD                              : 'PERIOD';
PERMISSION                          : 'PERMISSION';
PIPE                                : 'PIPE';
PLAN                                : 'PLAN';
POLICY                              : 'POLICY';
POSITION                            : 'POSITION';
PRECISION                           : 'PRECISION';
PREDICATES                          : 'PREDICATES';
PREFETCH                            : 'PREFETCH';
PREFETCHSIZE                        : 'PREFETCHSIZE';
PREPARE                             : 'PREPARE';
PRESERVE                            : 'PRESERVE';
PREVENT                             : 'PREVENT';
PRIMARY                             : 'PRIMARY';
PRIORITY                            : 'PRIORITY';
PRIVILEGES                          : 'PRIVILEGES';
PROCEDURE                           : 'PROCEDURE';
PROFILE                             : 'PROFILE';
PROGRAM                             : 'PROGRAM';
PROPAGATE                           : 'PROPAGATE';
PUBLIC                              : 'PUBLIC';
PUBLISH                             : 'PUBLISH';
PYTHON                              : 'PYTHON';
P_                                  : 'P';
QUERY                               : 'QUERY';
QUERYNO                             : 'QUERYNO';
QUERYTAG                            : 'QUERYTAG';
QUEUE                               : 'QUEUE';
QUEUEDACTIVITIES                    : 'QUEUEDACTIVITIES';
QUEUEDCONNECTIONS                   : 'QUEUEDCONNECTIONS';
QUEUETIME                           : 'QUEUETIME';
QUIESCE_CONNECT                     : 'QUIESCE_CONNECT';
RANDOM                              : 'RANDOM';
RATE                                : 'RATE';
READ                                : 'READ';
READS                               : 'READS';
REAL                                : 'REAL';
REBALANCE                           : 'REBALANCE';
RECOMMEND                           : 'RECOMMEND';
RECORD                              : 'RECORD';
RECOVERY                            : 'RECOVERY';
REDISTRIBUTE                        : 'REDISTRIBUTE';
REDUCE                              : 'REDUCE';
REF                                 : 'REF';
REFERENCE                           : 'REFERENCE';
REFERENCES                          : 'REFERENCES';
REFERENCING                         : 'REFERENCING';
REFRESH                             : 'REFRESH';
REGISTERS                           : 'REGISTERS';
REGULAR                             : 'REGULAR';
REGVAR                              : 'REGVAR';
REGVARVALUES                        : 'REGVARVALUES';
RELEASE                             : 'RELEASE';
RELEASED                            : 'RELEASED';
REMAP                               : 'REMAP';
REMOTE                              : 'REMOTE';
REMOVE                              : 'REMOVE';
RENAME                              : 'RENAME';
REOPT                               : 'REOPT';
REORG                               : 'REORG';
REPEAT                              : 'REPEAT';
REPEATABLE                          : 'REPEATABLE';
REPLACE                             : 'REPLACE';
REPLICATION                         : 'REPLICATION';
REQUEST                             : 'REQUEST';
REQUIRE                             : 'REQUIRE';
RESET                               : 'RESET';
RESIDENT                            : 'RESIDENT';
RESIZE                              : 'RESIZE';
RESOURCE                            : 'RESOURCE';
RESTART                             : 'RESTART';
RESTORE                             : 'RESTORE';
RESTRICT                            : 'RESTRICT';
RESULT                              : 'RESULT';
RESUME                              : 'RESUME';
RETAIN                              : 'RETAIN';
RETURN                              : 'RETURN';
RETURNS                             : 'RETURNS';
REUSE                               : 'REUSE';
REVOKE                              : 'REVOKE';
RIGHT                               : 'RIGHT';
ROLE                                : 'ROLE';
ROLLBACK                            : 'ROLLBACK';
ROLLFORWARD                         : 'ROLLFORWARD';
ROLLOUT                             : 'ROLLOUT';
ROLLUP                              : 'ROLLUP';
ROOT                                : 'ROOT';
ROUNDING                            : 'ROUNDING';
ROUND_CEILING                       : 'ROUND_CEILING';
ROUND_DOWN                          : 'ROUND_DOWN';
ROUND_FLOOR                         : 'ROUND_FLOOR';
ROUND_HALF_EVEN                     : 'ROUND_HALF_EVEN';
ROUND_HALF_UP                       : 'ROUND_HALF_UP';
ROUTINES                            : 'ROUTINES';
ROW                                 : 'ROW';
ROWS                                : 'ROWS';
ROW_COUNT                           : 'ROW_COUNT';
RR                                  : 'RR';
RS                                  : 'RS';
RULE                                : 'RULE';
RULES                               : 'RULES';
RUN                                 : 'RUN';
RUNSTATS                            : 'RUNSTATS';
SAVEPOINT                           : 'SAVEPOINT';
SCALEBACK                           : 'SCALEBACK';
SCHEMA                              : 'SCHEMA';
SCHEMAADM                           : 'SCHEMAADM';
SCOPE                               : 'SCOPE';
SCRATCHPAD                          : 'SCRATCHPAD';
SEARCH                              : 'SEARCH';
SECADM                              : 'SECADM';
SECMAINT                            : 'SECMAINT';
SECOND                              : 'SECOND';
SECONDS                             : 'SECONDS';
SECTION                             : 'SECTION';
SECURED                             : 'SECURED';
SECURITY                            : 'SECURITY';
SELECT                              : 'SELECT';
SELECTIN                            : 'SELECTIN';
SELECTION                           : 'SELECTION';
SELF                                : 'SELF';
SEQUENCE                            : 'SEQUENCE';
SERVER                              : 'SERVER';
SERVICE                             : 'SERVICE';
SESSION                             : 'SESSION';
SESSION_USER                        : 'SESSION_USER';
SET                                 : 'SET';
SETS                                : 'SETS';
SETSESSIONUSER                      : 'SETSESSIONUSER';
SHARE                               : 'SHARE';
SHARES                              : 'SHARES';
SINGLE                              : 'SINGLE';
SIZE                                : 'SIZE';
SMALLINT                            : 'SMALLINT';
SNAPSHOT                            : 'SNAPSHOT';
SOFT                                : 'SOFT';
SORTMEM                             : 'SORTMEM';
SORTSHRHEAPUTIL                     : 'SORTSHRHEAPUTIL';
SOURCE                              : 'SOURCE';
SPECIAL                             : 'SPECIAL';
SPECIFIC                            : 'SPECIFIC';
SQL                                 : 'SQL';
SQLADM                              : 'SQLADM';
SQLCA                               : 'SQLCA';
SQLCODE                             : 'SQLCODE';
SQLDA                               : 'SQLDA';
SQLERROR                            : 'SQLERROR';
SQLROWSREAD                         : 'SQLROWSREAD';
SQLROWSREADINSC                     : 'SQLROWSREADINSC';
SQLROWSRETURNED                     : 'SQLROWSRETURNED';
SQLSTATE                            : 'SQLSTATE';
SQLTEMPSPACE                        : 'SQLTEMPSPACE';
SQLWARNING                          : 'SQLWARNING';
SQL_CCFLAGS                         : 'SQL_CCFLAGS';
STAGING                             : 'STAGING';
START                               : 'START';
STARTING                            : 'STARTING';
STATE                               : 'STATE';
STATEMENT                           : 'STATEMENT';
STATEMENTS                          : 'STATEMENTS';
STATIC                              : 'STATIC';
STATISTICS                          : 'STATISTICS';
STATUS                              : 'STATUS';
STAY                                : 'STAY';
STMT_EXEC_TIME                      : 'STMT_EXEC_TIME';
STOGROUP                            : 'STOGROUP';
STOP                                : 'STOP';
STORAGE                             : 'STORAGE';
STRIPE                              : 'STRIPE';
STRONG                              : 'STRONG';
STYLE                               : 'STYLE';
SUB                                 : 'SUB';
SUCCESS                             : 'SUCCESS';
SUSPEND                             : 'SUSPEND';
SWITCH                              : 'SWITCH';
SYSADMIN                            : 'SYSADMIN';
SYSDEFAULTHISTOGRAM                 : 'SYSDEFAULTHISTOGRAM';
SYSDEFAULTUSERCLASS                 : 'SYSDEFAULTUSERCLASS';
SYSTEM                              : 'SYSTEM';
SYSTEM_TIME                         : 'SYSTEM_TIME';
SYSTEM_USER                         : 'SYSTEM_USER';
S_                                  : 'S';
TABLE                               : 'TABLE';
TABLES                              : 'TABLES';
TABLESAMPLE                         : 'TABLESAMPLE';
TABLESPACE                          : 'TABLESPACE';
TABLESPACES                         : 'TABLESPACES';
TAG                                 : 'TAG';
TARGET                              : 'TARGET';
TEMPLATE                            : 'TEMPLATE';
TEMPORAL                            : 'TEMPORAL';
TEMPORARY                           : 'TEMPORARY';
TEXT                                : 'TEXT';
THAN                                : 'THAN';
THEN                                : 'THEN';
THREADSAFE                          : 'THREADSAFE';
THRESHOLD                           : 'THRESHOLD';
THROUGH                             : 'THROUGH';
TIME                                : 'TIME';
TIMEOUT                             : 'TIMEOUT';
TIMERONCOST                         : 'TIMERONCOST';
TIMESTAMP                           : 'TIMESTAMP';
TO                                  : 'TO';
TOTALMEMBERCONNECTIONS              : 'TOTALMEMBERCONNECTIONS';
TOTALSCMEMBERCONNECTIONS            : 'TOTALSCMEMBERCONNECTIONS';
TRACKING                            : 'TRACKING';
TRANSACTION                         : 'TRANSACTION';
TRANSFER                            : 'TRANSFER';
TRANSFERRATE                        : 'TRANSFERRATE';
TRANSFORM                           : 'TRANSFORM';
TRANSFORMS                          : 'TRANSFORMS';
TREE                                : 'TREE';
TRIGGER                             : 'TRIGGER';
TRIGGERS                            : 'TRIGGERS';
TRUE                                : 'TRUE';
TRUNCATE                            : 'TRUNCATE';
TRUSTED                             : 'TRUSTED';
TYPE                                : 'TYPE';
TYPES                               : 'TYPES';
UNBOUNDED                           : 'UNBOUNDED';
UNCHECKED                           : 'UNCHECKED';
UNDER                               : 'UNDER';
UNFORMATTED                         : 'UNFORMATTED';
UNICODE                             : 'UNICODE';
UNIQUE                              : 'UNIQUE';
UNIT                                : 'UNIT';
UNITS                               : 'UNITS';
UNNEST                              : 'UNNEST';
UNTIL                               : 'UNTIL';
UOW                                 : 'UOW';
UOWTOTALTIME                        : 'UOWTOTALTIME';
UPDATE                              : 'UPDATE';
UPDATED_SINCE_BOUNDARY_TIME         : 'UPDATED_SINCE_BOUNDARY_TIME';
UPDATEIN                            : 'UPDATEIN';
UPON                                : 'UPON';
UR                                  : 'UR';
USAGE                               : 'USAGE';
USE                                 : 'USE';
USER                                : 'USER';
USING                               : 'USING';
UTILALL                             : 'UTILALL';
VALIDATE                            : 'VALIDATE';
VALUE                               : 'VALUE';
VALUES                              : 'VALUES';
VARBINARY                           : 'VARBINARY';
VARCHAR                             : 'VARCHAR';
VARGRAPHIC                          : 'VARGRAPHIC';
VARIABLE                            : 'VARIABLE';
VARYING                             : 'VARYING';
VERSION                             : 'VERSION';
VERSIONING                          : 'VERSIONING';
VIEW                                : 'VIEW';
VIOLATIONS                          : 'VIOLATIONS';
VOLATILE                            : 'VOLATILE';
WAIT                                : 'WAIT';
WAITING                             : 'WAITING';
WATER                               : 'WATER';
WEAK                                : 'WEAK';
WHEN                                : 'WHEN';
WHERE                               : 'WHERE';
WHILE                               : 'WHILE';
WITH                                : 'WITH';
WITHIN                              : 'WITHIN';
WITHOUT                             : 'WITHOUT';
WLMADM                              : 'WLMADM';
WORK                                : 'WORK';
WORKLOAD                            : 'WORKLOAD';
WRAP                                : 'WRAP';
WRAPPER                             : 'WRAPPER';
WRITE                               : 'WRITE';
WRITEDOWN                           : 'WRITEDOWN';
WRITEUP                             : 'WRITEUP';
XML                                 : 'XML';
XMLPARSE                            : 'XMLPARSE';
XQUERY                              : 'XQUERY';
XSROBJECT                           : 'XSROBJECT';
YEAR                                : 'YEAR';
YEARS                               : 'YEARS';
YES                                 : 'YES';
CHECKED: 'CHECKED';
PRUNE: 'PRUNE';

AVG                                 : 'AVG';
ARRAY_AGG                           : 'ARRAY_AGG';
CORRELATION                         : 'CORRELATION';
COUNT_BIG : 'COUNT_BIG';
COVARIANCE  : 'COVARIANCE';
COVARIANCE_SAMP : 'COVARIANCE_SAMP';
CUME_DIST : 'CUME_DIST';
LISTAGG : 'LISTAGG';
MEDIAN : 'MEDIAN';
MIN : 'MIN';
PERCENTILE_CONT : 'PERCENTILE_CONT';
PERCENTILE_DISC : 'PERCENTILE_DISC';
PERCENT_RANK    :   'PERCENT_RANK';
STDDEV  : 'STDDEV';
STDDEV_SAMP : 'STDDEV_SAMP';
SUM : 'SUM';
VARIANCE : 'VARIANCE';
VARIANCE_SAMP : 'VARIANCE_SAMP';
XMLAGG : 'XMLAGG';
XMLGROUP : 'XMLGROUP';

ABS : 'ABS';
ABSVAL : 'ABSVAL';
ACOS : 'ACOS';
ADD_DAYS : 'ADD_DAYS';
ADD_HOURS : 'ADD_HOURS';
ADD_MINUTES : 'ADD_MINUTES';
ADD_MONTHS : 'ADD_MONTHS';
ADD_SECONDS : 'ADD_SECONDS';
ADD_YEARS : 'ADD_YEARS';
ARRAY_DELETE : 'ARRAY_DELETE';
ARRAY_FIRST : 'ARRAY_FIRST';
ARRAY_LAST : 'ARRAY_LAST';
ARRAY_NEXT : 'ARRAY_NEXT';
ARRAY_PRIOR : 'ARRAY_PRIOR';
ASCII_STR : 'ASCII_STR';
ASIN : 'ASIN';
ATAN : 'ATAN';
ATAN2 : 'ATAN2';
ATANH : 'ATANH';
BITAND : 'BITAND';
BITANDNOT : 'BITANDNOT';
BITOR : 'BITOR';
BITXOR : 'BITXOR';
BITNOT : 'BITNOT';
BPCHAR : 'BPCHAR';
BSON_TO_JSON : 'BSON_TO_JSON';
BTRIM : 'BTRIM';
CEILING : 'CEILING';
CEIL : 'CEIL';
CHARACTER_LENGTH : 'CHARACTER_LENGTH';
CHR : 'CHR';
COALESCE : 'COALESCE';
COLLATION_KEY : 'COLLATION_KEY';
COLLATION_KEY_BIT : 'COLLATION_KEY_BIT';
COMPARE_DECFLOAT : 'COMPARE_DECFLOAT';
CONCAT : 'CONCAT';
COS : 'COS';
COSH : 'COSH';
COT : 'COT';
CURSOR_ROWCOUNT : 'CURSOR_ROWCOUNT';
DATAPARTITIONNUM : 'DATAPARTITIONNUM';

DATA_MASK: 'DATA_MASK';
DATETIME: 'DATETIME';
DATE_PART: 'DATE_PART';
DATE_TRUNC: 'DATE_TRUNC';
DAYNAME: 'DAYNAME';
DAYOFMONTH: 'DAYOFMONTH';
DAYOFWEEK: 'DAYOFWEEK';
DAYOFWEEK_ISO: 'DAYOFWEEK_ISO';
DAYOFYEAR: 'DAYOFYEAR';
DAYS_BETWEEN: 'DAYS_BETWEEN';
DAYS_TO_END_OF_MONTH: 'DAYS_TO_END_OF_MONTH';
DECFLOAT_FORMAT: 'DECFLOAT_FORMAT';
DECODE: 'DECODE';
DECRYPT_BIN: 'DECRYPT_BIN';
DECRYPT_CHAR: 'DECRYPT_CHAR';
DEGREES: 'DEGREES';
DEREF: 'DEREF';
DIFFERENCE: 'DIFFERENCE';
DIGITS: 'DIGITS';
DOUBLE_PRECISION: 'DOUBLE_PRECISION';
ENCRYPT: 'ENCRYPT';
EVENT_MON_STATE: 'EVENT_MON_STATE';
EXP: 'EXP';
EXTRACT: 'EXTRACT';
FIRST_DAY: 'FIRST_DAY';
FLOAT4: 'FLOAT4';
FLOAT8: 'FLOAT8';
FLOOR: 'FLOOR';
FROM_UTC_TIMESTAMP: 'FROM_UTC_TIMESTAMP';
GENERATE_UNIQUE: 'GENERATE_UNIQUE';
GETHINT: 'GETHINT';
GREATEST: 'GREATEST';
HASH4: 'HASH4';
HASH8: 'HASH8';
HASHEDVALUE: 'HASHEDVALUE';
HEX: 'HEX';
HEXTORAW: 'HEXTORAW';
HOURS_BETWEEN: 'HOURS_BETWEEN';
IDENTITY_VAL_LOCAL: 'IDENTITY_VAL_LOCAL';
IFNULL: 'IFNULL';
INITCAP: 'INITCAP';
INSTR: 'INSTR';
INSTR2: 'INSTR2';
INSTR4: 'INSTR4';
INSTRB: 'INSTRB';
INTERVAL: 'INTERVAL';
INT2: 'INT2';
INT4: 'INT4';
INT8: 'INT8';
INTNAND: 'INTNAND';
INTNOR: 'INTNOR';
INTNXOR: 'INTNXOR';
INTNNOT: 'INTNNOT';
ISNULL: 'ISNULL';
JSON_ARRAY: 'JSON_ARRAY';
JSON_OBJECT: 'JSON_OBJECT';
JSON_QUERY: 'JSON_QUERY';
JSON_TO_BSON: 'JSON_TO_BSON';
JSON_VALUE: 'JSON_VALUE';
JULIAN_DAY: 'JULIAN_DAY';
LAST_DAY: 'LAST_DAY';
LCASE: 'LCASE';
LEAST: 'LEAST';
LENGTH2: 'LENGTH2';
LENGTH4: 'LENGTH4';
LENGTHB: 'LENGTHB';
LN: 'LN';
LOCATE: 'LOCATE';
LOCATE_IN_STRING: 'LOCATE_IN_STRING';
LOG10: 'LOG10';
LONG_VARCHAR: 'LONG_VARCHAR';
LONG_VARGRAPHIC: 'LONG_VARGRAPHIC';
LPAD: 'LPAD';
LTRIM: 'LTRIM';
MAX_CARDINALITY: 'MAX_CARDINALITY';
MIDNIGHT_SECONDS: 'MIDNIGHT_SECONDS';
MINUTES_BETWEEN: 'MINUTES_BETWEEN';
MOD: 'MOD';
MONTHNAME: 'MONTHNAME';
MONTHS_BETWEEN: 'MONTHS_BETWEEN';
MULTIPLY_ALT: 'MULTIPLY_ALT';
NCHR: 'NCHR';
NEXT_DAY: 'NEXT_DAY';
NEXT_MONTH: 'NEXT_MONTH';
NEXT_QUARTER: 'NEXT_QUARTER';
NEXT_WEEK: 'NEXT_WEEK';
NEXT_YEAR: 'NEXT_YEAR';
NORMALIZE_DECFLOAT: 'NORMALIZE_DECFLOAT';
NOW: 'NOW';
NULLIF: 'NULLIF';
NVL: 'NVL';
NVL2: 'NVL2';
OCTET_LENGTH: 'OCTET_LENGTH';
OVERLAY: 'OVERLAY';
POSSTR: 'POSSTR';
POW: 'POW';
POWER: 'POWER';
QUANTIZE: 'QUANTIZE';
QUARTER: 'QUARTER';
QUOTE_IDENT: 'QUOTE_IDENT';
QUOTE_LITERAL: 'QUOTE_LITERAL';
RADIANS: 'RADIANS';
RAISE_ERROR: 'RAISE_ERROR';
RAND: 'RAND';
RAWTOHEX: 'RAWTOHEX';
REC2XML: 'REC2XML';
REGEXP_COUNT: 'REGEXP_COUNT';
REGEXP_EXTRACT: 'REGEXP_EXTRACT';
REGEXP_INSTR: 'REGEXP_INSTR';
REGEXP_LIKE: 'REGEXP_LIKE';
REGEXP_MATCH_COUNT: 'REGEXP_MATCH_COUNT';
REGEXP_REPLACE: 'REGEXP_REPLACE';
REGEXP_SUBSTR: 'REGEXP_SUBSTR';
RID: 'RID';
RID_BIT: 'RID_BIT';
ROUND: 'ROUND';
ROUND_TIMESTAMP: 'ROUND_TIMESTAMP';
RPAD: 'RPAD';
RTRIM: 'RTRIM';
SECLABEL: 'SECLABEL';
SECLABEL_BY_NAME: 'SECLABEL_BY_NAME';
SECLABEL_TO_CHAR: 'SECLABEL_TO_CHAR';
SECONDS_BETWEEN: 'SECONDS_BETWEEN';
SIGN: 'SIGN';
SIN: 'SIN';
SINH: 'SINH';
SOUNDEX: 'SOUNDEX';
SPACE: 'SPACE';
SQRT: 'SQRT';
STRIP: 'STRIP';
STRLEFT: 'STRLEFT';
STRPOS: 'STRPOS';
STRRIGHT: 'STRRIGHT';
SUBSTR: 'SUBSTR';
SUBSTR2: 'SUBSTR2';
SUBSTR4: 'SUBSTR4';
SUBSTRB: 'SUBSTRB';
SUBSTRING: 'SUBSTRING';
TABLE_NAME: 'TABLE_NAME';
TABLE_SCHEMA: 'TABLE_SCHEMA';
TAN: 'TAN';
TANH: 'TANH';
THIS_MONTH: 'THIS_MONTH';
THIS_QUARTER: 'THIS_QUARTER';
THIS_WEEK: 'THIS_WEEK';
THIS_YEAR: 'THIS_YEAR';
TIMESTAMP_FORMAT: 'TIMESTAMP_FORMAT';
TIMESTAMP_ISO: 'TIMESTAMP_ISO';
TIMESTAMPDIFF: 'TIMESTAMPDIFF';
TIMEZONE: 'TIMEZONE';
TO_CHAR: 'TO_CHAR';
TO_DATE: 'TO_DATE';
TO_HEX: 'TO_HEX';
TO_MULTI_BYTE: 'TO_MULTI_BYTE';
TO_NCHAR: 'TO_NCHAR';
TO_NCLOB: 'TO_NCLOB';
TO_NUMBER: 'TO_NUMBER';
TO_SINGLE_BYTE: 'TO_SINGLE_BYTE';
TO_TIMESTAMP: 'TO_TIMESTAMP';
TO_UTC_TIMESTAMP: 'TO_UTC_TIMESTAMP';
TOTALORDER: 'TOTALORDER';
TRANSLATE: 'TRANSLATE';
TRIM: 'TRIM';
TRIM_ARRAY: 'TRIM_ARRAY';
TRUNC_TIMESTAMP: 'TRUNC_TIMESTAMP';
TRUNC: 'TRUNC';
TYPE_ID: 'TYPE_ID';
TYPE_NAME: 'TYPE_NAME';
TYPE_SCHEMA: 'TYPE_SCHEMA';
UCASE: 'UCASE';
UNICODE_STR: 'UNICODE_STR';
UPPER: 'UPPER';
VARCHAR_BIT_FORMAT: 'VARCHAR_BIT_FORMAT';
VARCHAR_FORMAT: 'VARCHAR_FORMAT';
VARCHAR_FORMAT_BIT: 'VARCHAR_FORMAT_BIT';
VERIFY_GROUP_FOR_USER: 'VERIFY_GROUP_FOR_USER';
VERIFY_ROLE_FOR_USER: 'VERIFY_ROLE_FOR_USER';
VERIFY_TRUSTED_CONTEXT_ROLE_FOR_USER: 'VERIFY_TRUSTED_CONTEXT_ROLE_FOR_USER';
WEEK: 'WEEK';
WEEK_ISO: 'WEEK_ISO';
WEEKS_BETWEEN: 'WEEKS_BETWEEN';
WIDTH_BUCKET: 'WIDTH_BUCKET';
XMLATTRIBUTES: 'XMLATTRIBUTES';
XMLCOMMENT: 'XMLCOMMENT';
XMLCONCAT: 'XMLCONCAT';
XMLDOCUMENT: 'XMLDOCUMENT';
XMLELEMENT: 'XMLELEMENT';
XMLFOREST: 'XMLFOREST';
XMLNAMESPACES: 'XMLNAMESPACES';
XMLPI: 'XMLPI';
XMLQUERY: 'XMLQUERY';
XMLROW: 'XMLROW';
XMLSERIALIZE: 'XMLSERIALIZE';
XMLTEXT: 'XMLTEXT';
XMLVALIDATE: 'XMLVALIDATE';
XMLXSROBJECTID: 'XMLXSROBJECTID';
XSLTRANSFORM: 'XSLTRANSFORM';
YEARS_BETWEEN: 'YEARS_BETWEEN';
YMD_BETWEEN : 'YMD_BETWEEN';
BITARY : 'BITARY';


BASE_TABLE : 'BASE_TABLE';
JSON_TABLE : 'JSON_TABLE';
XMLTABLE : 'XMLTABLE';
SYSPROC : 'SYSPROC';
MEMORY_TABLE : 'MEMORY_TABLE';

GET_CACHE_FILE_INFO: 'GET_CACHE_FILE_INFO';
GET_CACHE_TABLE_INFO: 'GET_CACHE_TABLE_INFO';
GET_DATALAKE_CONFIG: 'GET_DATALAKE_CONFIG';
HCAT_DESCRIBESCHEMA: 'HCAT_DESCRIBESCHEMA';
HCAT_DESCRIBETAB: 'HCAT_DESCRIBETAB';
LOG_ENTRY: 'LOG_ENTRY';
TABLE_FILES: 'TABLE_FILES';
TABLE_PARTITIONS: 'TABLE_PARTITIONS';
TABLE_SNAPSHOTS: 'TABLE_SNAPSHOTS';
TABLE_SNAPSHOT_REFS: 'TABLE_SNAPSHOT_REFS';

HAVING : 'HAVING';
UNION : 'UNION';
INTERSECT : 'INTERSECT';
UNKNOWN : 'UNKNOWN';
SKIP_ : 'SKIP';
LOCKED : 'LOCKED';
NOWAIT : 'NOWAIT';
OUTCOME : 'OUTCOME';
MODULE : 'MODULE';
TBSPACEADM : 'TBSPACEADM';
TENANT : 'TENANT';
POOL : 'POOL';
MODEL : 'MODEL';
REVERT : 'REVERT';
BONUS : 'BONUS';

ACTIVITYVALS : 'ACTIVITYVALS';
ACTIVITYSTMT : 'ACTIVITYSTMT';
ACTIVITYMETRICS : 'ACTIVITYMETRICS';

CHANGESUMMARY : 'CHANGESUMMARY';
EVMONSTART : 'EVMONSTART';
TXNCOMPLETION : 'TXNCOMPLETION';
DDLSTMTEXEC : 'DDLSTMTEXEC';
DBDBMCFG : 'DBDBMCFG';
UTILSTART : 'UTILSTART';
UTILSTOP : 'UTILSTOP';
UTILPHASE : 'UTILPHASE';
UTILLOCATION : 'UTILLOCATION';
LOCK_PARTICIPANTS : 'LOCK_PARTICIPANTS';
LOCK_PARTICIPANT_ACTIVITIES : 'LOCK_PARTICIPANT_ACTIVITIES';
LOCK_ACTIVITY_VALUES : 'LOCK_ACTIVITY_VALUES';
PKGCACHE : 'PKGCACHE';
PKGCACHE_STMT_ARGS : 'PKGCACHE_STMT_ARGS';
PKGCACHE_METRICS : 'PKGCACHE_METRICS';
HISTOGRAMBIN : 'HISTOGRAMBIN';
OSMETRICS : 'OSMETRICS';
QSTATS : 'QSTATS';
SCMETRICS : 'SCMETRICS';
SCSTATS : 'SCSTATS';
SUPERCLASSMETRICS : 'SUPERCLASSMETRICS';
SUPERCLASSSTATS : 'SUPERCLASSSTATS';
WCSTATS : 'WCSTATS';
WLMETRICS : 'WLMETRICS';
WLSTATS : 'WLSTATS';
THRESHOLDVIOLATIONS : 'THRESHOLDVIOLATIONS';
UOW_METRICS : 'UOW_METRICS';
UOW_PACKAGE_LIST : 'UOW_PACKAGE_LIST';
UOW_EXECUTABLE_LIST : 'UOW_EXECUTABLE_LIST';
CONNECTIONS : 'CONNECTIONS';
TRANSACTIONS : 'TRANSACTIONS';
DEDLOCKS : 'DEDLOCKS';
APPL_ID : 'APPL_ID';
AUTH_ID : 'AUTH_ID';
APPL_NAME : 'APPL_NAME';
DENSE_RANK : 'DENSE_RANK';
RANGE : 'RANGE';
PARTITIONED : 'PARTITIONED';
SPECIFICATION : 'SPECIFICATION';
CASE : 'CASE';
CLIENT : 'CLIENT';
BASED : 'BASED';
SYSIBM : 'SYSIBM';
DATALAKE : 'DATALAKE';
PURGE : 'PURGE';
COMPUTE : 'COMPUTE';
NOSCAN : 'NOSCAN';
SYSADM : 'SYSADM';
SYSCTRL : 'SYSCTRL';
SYSMAINT : 'SYSMAINT';
SYSMON  : 'SYSMON';
ID : 'ID';
MEASURE : 'MEASURE';
PRIOR : 'PRIOR';
CONNECT_BY_ROOT : 'CONNECT_BY_ROOT';
PRECEDING : 'PRECEDING';
FOLLOWING : 'FOLLOWING';
ESCAPE : 'ESCAPE';
SELECTIVITY : 'SELECTIVITY';
SOME : 'SOME';
TOKEN : 'TOKEN';
ARRAY_EXISTS : 'ARRAY_EXISTS';
JSON_EXISTS : 'JSON_EXISTS';
BSON : 'BSON';
JSON : 'JSON';
FORMAT : 'FORMAT';
DELETING : 'DELETING';
INSERTING : 'INSERTING';
UPDATING : 'UPDATING';
LOCATION : 'LOCATION';
NAMESPACE : 'NAMESPACE';
URI : 'URI';
XMLSCHEMA : 'XMLSCHEMA';
ACCORDING : 'ACCORDING';
VALIDATED : 'VALIDATED';
DEADLOCKS : 'DEADLOCKS';

SIGNAL : 'SIGNAL';

WHITE_SPACE                         : [ \t\r\n\u3000] + ->skip;
BLOCK_COMMENT:  '/*' .*? '*/'                           -> channel(HIDDEN);
INLINE_COMMENT: '--' ~[\r\n]* ('\r'? '\n' | EOF)        -> channel(HIDDEN);

I_CURSOR : '[CURSOR]' ;

STRING_: SINGLE_QUOTED_TEXT | DOUBLE_QUOTED_TEXT;
//SINGLE_QUOTED_TEXT: (SQ_ ('\\'. | '\'\'' | ~('\'' | '\\'))* SQ_);
SINGLE_QUOTED_TEXT: SQ_ (~('\'' | '\r' | '\n') | '\'' '\'' | '\r'? '\n')* SQ_;
DOUBLE_QUOTED_TEXT: (DQ_ ( '\\'. | '""' | ~('"'| '\\') )* DQ_);

NCHAR_TEXT: 'N' STRING_;
UCHAR_TEXT: 'U' STRING_;


INTEGER_: INT_;
NUMBER_: INTEGER_? DOT_? INTEGER_ ('E' (PLUS_ | MINUS_)? INTEGER_)?;
VERSION_ : INT_ (DOT_ INT_)*;

HEX_DIGIT_: '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_;
BIT_NUM_: '0b' ('0' | '1')+ | 'B' SQ_ ('0' | '1')+ SQ_;

MAX_INT                             : '2147483647';

fragment INT_: [0-9]+;
fragment HEX_: [0-9A-F];

fragment EscapeSequence
    : '\\' [btnfr"'\\]
    | '\\' ([0-3]? [0-7])? [0-7]
    | '\\' 'u'+ HEX_ HEX_ HEX_ HEX_
    ;

AMPERSAND_           : '&';
AND_                 : '&&';
ARROW_               : '=>';
ASSIGNMENT_OPERATOR_ : ':=';
ASTERISK_            : '*';
AT_                  : '@';
BACKSLASH_           : '\\';
BQ_                  : '`';
CARET_               : '^';
COLON_               : ':';
COMMA_               : ',';
DEQ_                 : '==';
DOLLAR_              : '$';
DOT_                 : '.';
DOT_ASTERISK_        : '.*';
DQ_                  : '"';
EQ_                  : '=';
EXPONENT_            : '**';
GT_                  : '>';
GTE_                 : '>=';
LBE_                 : '{';
LBT_                 : '[';
LP_                  : '(';
LT_                  : '<';
LTE_                 : '<=';
MINUS_               : '-';
MOD_                 : '%';
NEQ_                 : '<>' | '!=' | '^=';
NOT_                 : '!';
OR_                  : '||';
PLUS_                : '+';
POUND_               : '#';
QUESTION_            : '?';
RANGE_OPERATOR_      : '..';
RBE_                 : '}';
RBT_                 : ']';
RP_                  : ')';
SAFE_EQ_             : '<=>';
SEMI_                : ';';
SIGNED_LEFT_SHIFT_   : '<<';
SIGNED_RIGHT_SHIFT_  : '>>';
SLASH_               : '/';
SQ_                  : '\'';
TILDE_               : '~';
VERTICAL_BAR_        : '|';
UL_                  : '_';

fragment LETTER:       [A-Z_];
fragment DEC_DOT_DEC:  (DEC_DIGIT+ '.' DEC_DIGIT+ |  DEC_DIGIT+ '.' | '.' DEC_DIGIT+);
fragment DEC_DIGIT:    [0-9];

IDENTIFIER_: [A-Z\u0080-\u2FFF\u3001-\uFF0B\uFF0D-\uFFFF]+[A-Z_$#0-9\u0080-\u2FFF\u3001-\uFF0B\uFF0D-\uFFFF]*;
