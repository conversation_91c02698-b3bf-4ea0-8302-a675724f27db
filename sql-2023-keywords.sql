sql2023 保留字
    ABS
    ABSENT
    ACOS
    ALL
    ALLOCATE
    ALTER
    AND
    ANY
    ANY_VALUE
    ARE
    ARRAY
    ARRAY_AGG
    ARRAY_MAX_CARDINALITY
    AS
    ASENSITIVE
    ASIN
    ASYMMETRIC
    AT
    ATAN
    ATOMIC
    AUTHORIZATION
    AVG
    BEGIN
    BEGIN_FRAME
    BEGIN_PARTITION
    BETWEEN
    BIGINT
    BINARY
    BLOB
    BOOLEAN
    BOTH
    BTRIM
    BY
    CALL
    CALLED
    CARDINALITY
    CASCADED
    CASE
    CAST
    CEIL
    CEILING
    CHAR
    CHARACTER
    CHARACTER_LENGTH
    CHAR_LENGTH
    CHECK
    CLASSIFIER
    CLOB
    CLOSE
    COALESCE
    COLLATE
    COLLECT
    COLUMN
    COMMIT
    CONDITION
    CONNECT
    CONSTRAINT
    CONTAINS
    CONVERT
    COPY
    CORR
    CORRESPONDING
    COS
    COSH
    COUNT
    COVAR_POP
    COVAR_SAMP
    CREATE
    CROSS
    CUBE
    CUME_DIST
    CURRENT
    CURRENT_CATALOG
    CURRENT_DATE
    CURRENT_DEFAULT_TRANSFORM_GROUP
    CURRENT_PATH
    CURRENT_ROLE
    CURRENT_ROW
    CURRENT_SCHEMA
    CURRENT_TIME
    CURRENT_TIMESTAMP
    CURRENT_TRANSFORM_GROUP_FOR_TYPE
    CURRENT_USER
    CURSOR
    CYCLE
    DATE
    DAY
    DEALLOCATE
    DEC
    DECFLOAT
    DECIMAL
    DECLARE
    DEFAULT
    DEFINE
    DELETE
    DENSE_RANK
    DEREF
    DESCRIBE
    DETERMINISTIC
    DISCONNECT
    DISTINCT
    DOUBLE
    DROP
    DYNAMIC
    EACH
    ELEMENT
    ELSE
    EMPTY
    END
    END-EXEC
    END_FRAME
    END_PARTITION
    EQUALS
    ESCAPE
    EVERY
    EXCEPT
    EXEC
    EXECUTE
    EXISTS
    EXP
    EXTERNAL
    EXTRACT
    FALSE
    FETCH
    FILTER
    FIRST_VALUE
    FLOAT
    FLOOR
    FOR
    FOREIGN
    FRAME_ROW
    FREE
    FROM
    FULL
    FUNCTION
    FUSION
    GET
    GLOBAL
    GRANT
    GREATEST
    GROUP
    GROUPING
    GROUPS
    HAVING
    HOLD
    HOUR
    IDENTITY
    IN
    INDICATOR
    INITIAL
    INNER
    INOUT
    INSENSITIVE
    INSERT
    INT
    INTEGER
    INTERSECT
    INTERSECTION
    INTERVAL
    INTO
    IS
    JOIN
    JSON
    JSON_ARRAY
    JSON_ARRAYAGG
    JSON_EXISTS
    JSON_OBJECT
    JSON_OBJECTAGG
    JSON_QUERY
    JSON_SCALAR
    JSON_SERIALIZE
    JSON_TABLE
    JSON_TABLE_PRIMITIVE
    JSON_VALUE
    LAG
    LANGUAGE
    LARGE
    LAST_VALUE
    LATERAL
    LEAD
    LEADING
    LEAST
    LEFT
    LIKE
    LIKE_REGEX
    LISTAGG
    LN
    LOCAL
    LOCALTIME
    LOCALTIMESTAMP
    LOG
    LOG10
    LOWER
    LPAD
    LTRIM
    MATCH
    MATCHES
    MATCH_NUMBER
    MATCH_RECOGNIZE
    MAX
    MEMBER
    MERGE
    METHOD
    MIN
    MINUTE
    MOD
    MODIFIES
    MODULE
    MONTH
    MULTISET
    NATIONAL
    NATURAL
    NCHAR
    NCLOB
    NEW
    NO
    NONE
    NORMALIZE
    NOT
    NTH_VALUE
    NTILE
    NULL
    NULLIF
    NUMERIC
    OCCURRENCES_REGEX
    OCTET_LENGTH
    OF
    OFFSET
    OLD
    OMIT
    ON
    ONE
    ONLY
    OPEN
    OR
    ORDER
    OUT
    OUTER
    OVER
    OVERLAPS
    OVERLAY
    PARAMETER
    PARTITION
    PATTERN
    PER
    PERCENT
    PERCENTILE_CONT
    PERCENTILE_DISC
    PERCENT_RANK
    PERIOD
    PORTION
    POSITION
    POSITION_REGEX
    POWER
    PRECEDES
    PRECISION
    PREPARE
    PRIMARY
    PROCEDURE
    PTF
    RANGE
    RANK
    READS
    REAL
    RECURSIVE
    REF
    REFERENCES
    REFERENCING
    REGR_AVGX
    REGR_AVGY
    REGR_COUNT
    REGR_INTERCEPT
    REGR_R2
    REGR_SLOPE
    REGR_SXX
    REGR_SXY
    REGR_SYY
    RELEASE
    RESULT
    RETURN
    RETURNS
    REVOKE
    RIGHT
    ROLLBACK
    ROLLUP
    ROW
    ROWS
    ROW_NUMBER
    RPAD
    RUNNING
    SAVEPOINT
    SCOPE
    SCROLL
    SEARCH
    SECOND
    SEEK
    SELECT
    SENSITIVE
    SESSION_USER
    SET
    SHOW
    SIMILAR
    SIN
    SINH
    SKIP
    SMALLINT
    SOME
    SPECIFIC
    SPECIFICTYPE
    SQL
    SQLEXCEPTION
    SQLSTATE
    SQLWARNING
    SQRT
    START
    STATIC
    STDDEV_POP
    STDDEV_SAMP
    SUBMULTISET
    SUBSET
    SUBSTRING
    SUBSTRING_REGEX
    SUCCEEDS
    SUM
    SYMMETRIC
    SYSTEM
    SYSTEM_TIME
    SYSTEM_USER
    TABLE
    TABLESAMPLE
    TAN
    TANH
    THEN
    TIME
    TIMESTAMP
    TIMEZONE_HOUR
    TIMEZONE_MINUTE
    TO
    TRAILING
    TRANSLATE
    TRANSLATE_REGEX
    TRANSLATION
    TREAT
    TRIGGER
    TRIM
    TRIM_ARRAY
    TRUE
    TRUNCATE
    UESCAPE
    UNION
    UNIQUE
    UNKNOWN
    UNNEST
    UPDATE
    UPPER
    USER
    USING
    VALUE
    VALUES
    VALUE_OF
    VARBINARY
    VARCHAR
    VARYING
    VAR_POP
    VAR_SAMP
    VERSIONING
    WHEN
    WHENEVER
    WHERE
    WIDTH_BUCKET
    WINDOW
    WITH
    WITHIN
    WITHOUT
    YEAR
    DO
    ELSEIF
    HANDLER
    IF
    ITERATE
    LEAVE
    LOOP
    REPEAT
    RESIGNAL
    SIGNAL
    UNTIL
    WHILE

sql2023 非保留字
    ABORT
    ABORTSESSION
    ABSOLUTE
    ACCESS
    ACCESSIBLE
    ACCESS_LOCK
    ACCOUNT
    ACOSH
    ACTION
    ADD
    ADD_MONTHS
    ADMIN
    AFTER
    AGGREGATE
    ALIAS
    ALLOW
    ALTERAND
    AMP
    ANALYSE
    ANALYZE
    ANSIDATE
    ARRAY_EXISTS
    ASC
    ASINH
    ASSERTION
    ASSOCIATE
    ASUTIME
    ATAN2
    ATANH
    AUDIT
    AUX
    AUXILIARY
    AVE
    AVERAGE
    BACKUP
    BEFORE
    BIT
    BREADTH
    BREAK
    BROWSE
    BT
    BUFFERPOOL
    BULK
    BUT
    BYTE
    BYTEINT
    BYTES
    CAPTURE
    CASCADE
    CASESPECIFIC
    CASE_N
    CATALOG
    CCSID
    CD
    CHANGE
    CHAR2HEXINT
    CHARACTERS
    CHARS
    CHECKPOINT
    CLASS
    CLONE
    CLUSTER
    CLUSTERED
    CM
    COLLATION
    COLLECTION
    COLLID
    COLUMN_VALUE
    COMMENT
    COMPLETION
    COMPRESS
    COMPUTE
    CONCAT
    CONCURRENTLY
    CONNECTION
    CONSTRAINTS
    CONSTRUCTOR
    CONTAINSTABLE
    CONTENT
    CONTINUE
    CONVERT_TABLE_HEADER
    CS
    CSUM
    CT
    CURRENT_LC_CTYPE
    CURRENT_SERVER
    CURRENT_TIMEZONE
    CURRVAL
    CV
    DATA
    DATABASE
    DATABASES
    DATABLOCKSIZE
    DATEFORM
    DAYS
    DAY_HOUR
    DAY_MICROSECOND
    DAY_MINUTE
    DAY_SECOND
    DBCC
    DBINFO
    DEFERRABLE
    DEFERRED
    DEGREES
    DEL
    DELAYED
    DENY
    DEPTH
    DESC
    DESCRIPTOR
    DESTROY
    DESTRUCTOR
    DIAGNOSTIC
    DIAGNOSTICS
    DICTIONARY
    DISABLE
    DISABLED
    DISALLOW
    DISK
    DISTINCTROW
    DISTRIBUTED
    DIV
    DOCUMENT
    DOMAIN
    DSSIZE
    DUAL
    DUMP
    ECHO
    EDITPROC
    ENABLED
    ENCLOSED
    ENCODING
    ENCRYPTION
    ENDING
    EQ
    ERASE
    ERRLVL
    ERROR
    ERRORFILES
    ERRORTABLES
    ESCAPED
    ET
    EXCEPTION
    EXCLUSIVE
    EXIT
    EXPLAIN
    FALLBACK
    FASTEXPORT
    FENCED
    FIELDPROC
    FILE
    FILLFACTOR
    FINAL
    FIRST
    FLOAT4
    FLOAT8
    FORCE
    FORMAT
    FOUND
    FREESPACE
    FREETEXT
    FREETEXTTABLE
    FREEZE
    FULLTEXT
    GE
    GENERAL
    GENERATED
    GIVE
    GO
    GOTO
    GRAPHIC
    GT
    HASH
    HASHAMP
    HASHBAKAMP
    HASHBUCKET
    HASHROW
    HELP
    HIGH_PRIORITY
    HOLDLOCK
    HOST
    HOURS
    HOUR_MICROSECOND
    HOUR_MINUTE
    HOUR_SECOND
    IDENTIFIED
    IDENTITYCOL
    IDENTITY_INSERT
    IGNORE
    ILIKE
    IMMEDIATE
    INCLUSIVE
    INCONSISTENT
    INCREMENT
    INDEX
    INFILE
    INHERIT
    INITIALIZE
    INITIALLY
    INITIATE
    INPUT
    INS
    INSTEAD
    INT1
    INT2
    INT3
    INT4
    INT8
    INTEGERDATE
    IO_AFTER_GTIDS
    IO_BEFORE_GTIDS
    ISNULL
    ISOBID
    ISOLATION
    JAR
    JOURNAL
    KEEP
    KEY
    KEYS
    KILL
    KURTOSIS
    LABEL
    LAST
    LC_CTYPE
    LE
    LESS
    LEVEL
    LIMIT
    LINEAR
    LINENO
    LINES
    LOAD
    LOADING
    LOCALE
    LOCATOR
    LOCATORS
    LOCK
    LOCKING
    LOCKMAX
    LOCKSIZE
    LOGGING
    LOGON
    LONG
    LONGBLOB
    LONGTEXT
    LOW_PRIORITY
    LT
    MACRO
    MAINTAINED
    MAP
    MASTER_BIND
    MASTER_SSL_VERIFY_SERVER_CERT
    MATERIALIZED
    MAVG
    MAXEXTENTS
    MAXIMUM
    MAXVALUE
    MCHARACTERS
    MDIFF
    MEDIUMBLOB
    MEDIUMINT
    MEDIUMTEXT
    MICROSECOND
    MICROSECONDS
    MIDDLEINT
    MINDEX
    MINIMUM
    MINUS
    MINUTES
    MINUTE_MICROSECOND
    MINUTE_SECOND
    MLINREG
    MLOAD
    MLSLABEL
    MODE
    MODIFY
    MONITOR
    MONRESOURCE
    MONSESSION
    MONTHS
    MSUBSTR
    MSUM
    NAMED
    NAMES
    NE
    NESTED_TABLE_ID
    NEW_TABLE
    NEXT
    NEXTVAL
    NOAUDIT
    NOCHECK
    NOCOMPRESS
    NONCLUSTERED
    NOTNULL
    NOWAIT
    NO_WRITE_TO_BINLOG
    NULLIFZERO
    NULLS
    NUMBER
    NUMPARTS
    OBID
    OBJECT
    OBJECTS
    OFF
    OFFLINE
    OFFSETS
    OLD_TABLE
    ONLINE
    OPENDATASOURCE
    OPENQUERY
    OPENROWSET
    OPENXML
    OPERATION
    OPTIMIZATION
    OPTIMIZE
    OPTIMIZER_COSTS
    OPTION
    OPTIONALLY
    ORDINALITY
    ORGANIZATION
    OUTFILE
    OUTPUT
    OVERRIDE
    PACKAGE
    PAD
    PADDED
    PARAMETERS
    PART
    PARTIAL
    PARTITIONED
    PARTITIONING
    PASSWORD
    PATH
    PCTFREE
    PERM
    PERMANENT
    PIECESIZE
    PIVOT
    PLACING
    PLAN
    POSTFIX
    PREFIX
    PREORDER
    PRESERVE
    PREVVAL
    PRINT
    PRIOR
    PRIQTY
    PRIVATE
    PRIVILEGES
    PROC
    PROFILE
    PROGRAM
    PROPORTIONAL
    PROTECTION
    PSID
    PUBLIC
    PURGE
    QUALIFIED
    QUALIFY
    QUANTILE
    QUERY
    QUERYNO
    RADIANS
    RAISERROR
    RANDOM
    RANGE_N
    RAW
    READ
    READTEXT
    READ_WRITE
    RECONFIGURE
    REFRESH
    REGEXP
    RELATIVE
    RENAME
    REPLACE
    REPLICATION
    REPOVERRIDE
    REQUEST
    REQUIRE
    RESOURCE
    RESTART
    RESTORE
    RESTRICT
    RESULT_SET_LOCATOR
    RESUME
    RET
    RETRIEVE
    RETURNING
    REVALIDATE
    REVERT
    RIGHTS
    RLIKE
    ROLE
    ROLLFORWARD
    ROUND_CEILING
    ROUND_DOWN
    ROUND_FLOOR
    ROUND_HALF_DOWN
    ROUND_HALF_EVEN
    ROUND_HALF_UP
    ROUND_UP
    ROUTINE
    ROWCOUNT
    ROWGUIDCOL
    ROWID
    ROWNUM
    ROWSET
    RULE
    RUN
    SAMPLE
    SAMPLEID
    SAVE
    SCHEMA
    SCHEMAS
    SCRATCHPAD
    SECONDS
    SECOND_MICROSECOND
    SECQTY
    SECTION
    SECURITY
    SECURITYAUDIT
    SEL
    SEMANTICKEYPHRASETABLE
    SEMANTICSIMILARITYDETAILSTABLE
    SEMANTICSIMILARITYTABLE
    SEPARATOR
    SEQUENCE
    SESSION
    SETRESRATE
    SETS
    SETSESSRATE
    SETUSER
    SHARE
    SHUTDOWN
    SIMPLE
    SIZE
    SKEW
    SOUNDEX
    SOURCE
    SPACE
    SPATIAL
    SPOOL
    SQLTEXT
    SQL_BIG_RESULT
    SQL_CALC_FOUND_ROWS
    SQL_SMALL_RESULT
    SS
    SSL
    STANDARD
    STARTING
    STARTUP
    STATE
    STATEMENT
    STATISTICS
    STAY
    STEPINFO
    STOGROUP
    STORED
    STORES
    STRAIGHT_JOIN
    STRING_CS
    STRUCTURE
    STYLE
    SUBSCRIBER
    SUBSTR
    SUCCESSFUL
    SUMMARY
    SUSPEND
    SYNONYM
    SYSDATE
    SYSTIMESTAMP
    TABLESPACE
    TBL_CS
    TEMPORARY
    TERMINATE
    TERMINATED
    TEXTSIZE
    THAN
    THRESHOLD
    TINYBLOB
    TINYINT
    TINYTEXT
    TITLE
    TOP
    TRACE
    TRAN
    TRANSACTION
    TRANSLATE_CHK
    TRY_CONVERT
    TSEQUAL
    TYPE
    UC
    UID
    UNDEFINED
    UNDER
    UNDO
    UNLOCK
    UNPIVOT
    UNSIGNED
    UPD
    UPDATETEXT
    UPPERCASE
    USAGE
    USE
    UTC_DATE
    UTC_TIME
    UTC_TIMESTAMP
    VALIDATE
    VALIDPROC
    VARBYTE
    VARCHAR2
    VARCHARACTER
    VARGRAPHIC
    VARIABLE
    VARIADIC
    VARIANT
    VCAT
    VERBOSE
    VIEW
    VIRTUAL
    VOLATILE
    VOLUMES
    WAIT
    WAITFOR
    WITHIN_GROUP
    WLM
    WORK
    WRITE
    WRITETEXT
    XMLCAST
    XMLEXISTS
    XMLNAMESPACES
    XOR
    YEARS
    YEAR_MONTH
    ZEROFILL
    ZEROIFNULL
    ZONE