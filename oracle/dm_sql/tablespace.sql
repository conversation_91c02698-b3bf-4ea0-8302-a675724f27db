-- CREATE
CREATE TABLESPACE TS1 DATAFILE 'd:\TS1.dbf' SIZE 128;

-- ALTER
ALTER TABLESPACE TS1 RENAME TO TS2;
ALTER TABLESPACE TS1 ADD DATAFILE 'd:\TS1_1.dbf' SIZE 128;
ALTER TABLESPACE TS1 RESIZE DATAFILE 'd:\TS1.dbf' TO 200;
ALTER TABLESPACE TS1 OFFLINE;
ALTER TABLESPACE TS1 RENAME DATAFILE 'd:\TS1.dbf' TO 'e:\TS1_0.dbf';
ALTER TABLESPACE TS1 ONLINE;
ALTER TABLESPACE TS1 DATAFILE 'd:\TS1.dbf' AUTOEXTEND ON NEXT 10 MAXSIZE 1000;
ALTER TABLESPACE TS1 CACHE="KEEP";
ALTER TABLESPACE TS1 CORRUPT;
ALTER TABLESPACE TS1 ADD HUGE PATH 'D:\dmdbms\data\DAMENG\TS1\HUGE2';

-- DROP
DROP TABLESPACE TS1;