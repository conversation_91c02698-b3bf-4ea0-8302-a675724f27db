-- backup database
BACKUP DATABASE BACKUPSET '/home/<USER>/db_bak_3_01';
BACKUP DATABASE FORMAT '/home/<USER>/%R_%d_%T';
BAC<PERSON>UP DATABASE TO WEEKLY_FULL_BAK BACKUPSET '/home/<USER>/db_bak_3_02';
BACKUP DATABASE BACKUPSET '/home/<USER>/db_bak_3_04' BACKUPINFO '完全备份';
BACKUP DATABASE BACKUPSET '/home/<USER>/db_bak_3_05' MAXPIECESIZE 300;
BACKUP DATABASE BACKUPSET '/home/<USER>/db_bak_3_06' COMPRESSED LEVEL 5;
BACKUP DATABASE BACKUPSET '/home/<USER>/db_bak_3_07' PARALLEL 8;
BACKUP DATABASE FULL BACKUPSET '/home/<USER>/db_full_bak_01';
BACKUP DATABASE INCREMENT WITH BACKUPDIR '/home/<USER>' BACKUPSET '/home/<USER>/db_increment_bak_02';


BACKUP TABLESPACE MAIN BACKUPSET 'ts_full_bak_01';
BACKUP TABLESPACE MAIN INCREMENT BACKUPSET 'ts_increment_bak_01';
BACKUP TABLESPACE MAIN INCREMENT BASE ON BACKUPSET'ts_full_bak_01'


BACKUP TABLE TAB_01 BACKUPSET '/home/<USER>/tab_bak_01';

BACKUP ARCHIVELOG LSN BETWEEN 50414 AND 50478 BACKUPSET '/home/<USER>/arch_bak_time_14-78';

RESTORE DATABASE '/opt/dmdbms/data/DAMENG_FOR_RESTORE/dm.ini' FROM BACKUPSET '/home/<USER>/db_full_bak_for_restore';

RESTORE TABLE TAB_FOR_RES_01 FROM BACKUPSET '/home/<USER>/tab_bak_for_res_01';

RESTORE DATABASE '/opt/dmdbms/data/DAMENG_FOR_RESTORE/dm.ini' TABLESPACE MAIN FROM BACKUPSET  '/home/<USER>/ts_full_bak_for_restore';

RESTORE ARCHIVE LOG FROM BACKUPSET '/home/<USER>/arch_all_for_restore' TO ARCHIVEDIR'/opt/dmdbms/data/DAMENG_FOR_RESTORE/arch_dest' OVERWRITE 2;