-- package
CREATE OR R<PERSON>LACE PACKAGE PersonPackage AS
    E_NoPerson EXCEPTION;
    PersonCount INT;
    Pcur CURSOR;
    PROCEDURE AddPerson(Pname VARCHAR(100), Pcity varchar(100));
    PROCEDURE RemovePerson(Pname VARCHAR(100), Pcity varchar(100));
    PROCEDURE RemovePerson(Pid INT);
    FUNCTION GetPersonCount RETURN INT;
    PROCEDURE PersonList;
END PersonPackage;



CREATE OR REPLACE PACKAGE BODY PersonPackage AS
  PROCEDURE AddPerson(Pname VARCHAR(100), Pcity varchar(100) )AS
    BEGIN
      INSERT INTO Person(Name, City) VALUES(Pname, Pcity); 
      PersonCount = PersonCount + SQL%ROWCOUNT; 
    END AddPerson; 
  PROCEDURE RemovePerson(Pname VARCHAR(100), Pcity varchar(100)) AS
    BEGIN
      DELETE FROM Person WHERE NAME LIKE Pname AND City like Pcity; 
        IF SQL%ROWCOUNT=0 THEN
        RAISE E_NoPerson; 
        END IF;
      PersonCount = PersonCount - SQL%ROWCOUNT;  
    END RemovePerson; 
  PROCEDURE RemovePerson(Pid INT) AS
    BEGIN
      DELETE FROM Person WHERE Id = Pid; 
        IF SQL%ROWCOUNT=0 THEN
        RAISE E_NoPerson; 
        END IF;
      PersonCount = PersonCount - SQL%ROWCOUNT; 
    END RemovePerson; 
  FUNCTION GetPersonCount RETURN INT AS
  BEGIN
    RETURN PersonCount; 
  END GetPersonCount; 
  PROCEDURE PersonList AS
  DECLARE
    V_id INT; 
    V_name VARCHAR(100); 
    V_city VARCHAR(100); 
  BEGIN
    IF PersonCount = 0 THEN
       RAISE E_NoPerson; 
    END IF; 
    OPEN Pcur FOR SELECT Id, Name, City FROM Person;  
    LOOP
      FETCH Pcur INTO V_id,V_name,V_city; 
      EXIT WHEN Pcur%NOTFOUND; 
      PRINT ('No.' || (cast (V_id as varchar(100))) || '  ' || V_name || '来自' || V_city ); 
    END LOOP; 
    CLOSE Pcur; 
  END PersonList; 
BEGIN
    SELECT COUNT(*) INTO PersonCount FROM Person; 
END PersonPackage;


ALTER PACKAGE PersonPackage COMPILE;


-- class
create class mycls
	as
	type rec_type is  record (c1 int, c2  int);
	id    int;
	r    rec_type;
	function f1(a int, b int) return rec_type;
	function mycls(id int , r_c1 int, r_c2 int) return mycls;
    --用户自定义构造函数
end;
/

//类体创建
create or replace class body mycls
  as
  function f1(a int, b int) return rec_type
  as
  begin
	r.c1 = a;
	r.c2 = b;
	return r;
  end;
  function mycls(id int, r_c1 int, r_c2 int) return mycls
  as
  begin
	this.id = id;			//可以使用this.来访问自身的成员
	r.c1 = r_c1;			//this也可以省略
	r.c2 = r_c2;
	return this;			//使用return this 返回本对象
  end;
end;
/


create or replace java class jcls
{

	int a;

	public static int testAdd2(int a, int b) {

		return a + b;

	}

	public int testAdd3(int a, int b, int c) {

		return a + b +c;

	}
}


ALTER CLASS mycls COMPILE;



-- type
  CREATE TYPE COMPLEX AS OBJECT(
	RPART REAL,
	IPART REAL,
	FUNCTION PLUS(X COMPLEX) RETURN COMPLEX,
	FUNCTION LES(X COMPLEX) RETURN COMPLEX
  );
  /

  CREATE TYPE BODY COMPLEX AS
	FUNCTION PLUS(X COMPLEX) RETURN COMPLEX IS
	BEGIN
	RETURN COMPLEX(RPART+X.RPART, IPART+X.IPART);
	END;

	FUNCTION LES(X COMPLEX) RETURN COMPLEX IS
	BEGIN
	  RETURN COMPLEX(RPART-X.RPART, IPART-X.IPART);
	END;
  END;



