-- CREATE
CREATE  TABLE PRODUCTION.PRODUCT_REVIEW
( 
PRODUCT_REVIEWID INT IDENTITY(1,1), 
 PRODUCTID INT NOT NULL,
 NAME VARCHAR(50) NOT NULL,
 REVIEWDATE DATE NOT NULL,
 EMAIL VARCHAR(50) NOT NULL,
 RATING INT NOT NULL,
 COMMENTS TEXT,
 PRIMARY KEY(PRODUCT_REVIEWID),
 FOREIGN   KEY(PRODUCTID)  REFERENCES  PRODUCTION.PRODUCT(PRODUCTID),
 CHECK(RATING IN(1,2,3,4,5))
);

CREATE TABLE PERSON.PERSON
( PERSONID  INT IDENTITY(1,1) CLUSTER PRIMARY KEY,
SEX CHAR(1) NOT NULL,
NAME VARCHAR(50) NOT NULL,
EMAIL VARCHAR(50),
PHONE VARCHAR(25))
STORAGE
( INITIAL	   5,
<PERSON><PERSON><PERSON>ENTS   5,
NEXT	2,
ON		FG_PERSON,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>   85);

CREATE TABLE PRODUCTION.PRODUCT_INVENTORY
(PRODUCTID INT NOT NULL REFERENCES PRODUCTION.PRODUCT(PRODUCTID),
LOCATIONID INT NOT NULL REFERENCES PRODUCTION.LOCATION(LOCATIONID),
QUANTITY INT NOT NULL)
DISTRIBUTED BY RANGE (QUANTITY)
(
	VALUES EQU OR LESS THAN (100) ON EP01,
	VALUES EQU OR LESS THAN (MAXVALUE) ON EP02
);

CREATE TABLE PRODUCTION.PRODUCT_INVENTORY
(PRODUCTID INT NOT NULL REFERENCES PRODUCTION.PRODUCT(PRODUCTID),
LOCATIONID INT NOT NULL REFERENCES PRODUCTION.LOCATION(LOCATIONID),
QUANTITY INT NOT NULL)
DISTRIBUTED BY LIST (LOCATIONID)
(
	VALUES (1,2,3,4) ON EP01,
	VALUES (5,6,7,8) ON EP02
);

CREATE TABLE PRODUCTION.LOCATION
(LOCATIONID INT IDENTITY(1,1) PRIMARY KEY,
PRODUCT_SUBCATEGORYID INT NOT NULL,
NAME VARCHAR(50) NOT NULL)
DISTRIBUTED  FULLY;

create table test(c1 int);

CREATE TABLE T1(id int PRIMARY KEY AUTO_INCREMENT,name varchar(100)) AUTO_INCREMENT=20;

CREATE TABLE T(C1 INT ENCRYPT MANUAL USER (USER01,USER02));

CREATE EXTERNAL TABLE EXT (
L_CHAR CHAR(1),
L_CHARACTER CHARACTER(3),
L_VARCHAR VARCHAR(20),
L_NUMERIC NUMERIC(6,2),
L_DECIMAL DECIMAL(6,2),
L_DEC DEC(6,2),
L_MONEY DECIMAL(19,4),
L_BIT BIT,
L_BOOL BIT,
L_BOOLEAN BIT,
L_INTEGER INTEGER,
L_INT INT,
L_BIGINT BIGINT,
L_TINYINT TINYINT,
L_BYTE BYTE,
L_SMALLINT SMALLINT,
L_BINARY BINARY,
L_VARBINARY VARBINARY,
L_FLOAT FLOAT,
L_DOUBLE DOUBLE,
L_REAL REAL,
L_DATE DATE,
L_TIME TIME,
L_TIMESTAMP TIMESTAMP,
L_INTERVAL INTERVAL YEAR
)FROM DEFAULT DIRECTORY EXTDIR LOCATION ('ctrl.txt');

CREATE EXTERNAL TABLE EXT_TABLE2(
C1 INT,
C2 INT,
C3 INT
) FROM DATAFILE DEFAULT DIRECTORY EXTDIR_2 LOCATION ('data.txt') PARMS(FIELDS DELIMITED BY '|', RECORDS DELIMITED BY 0x0d0a);

CREATE EXTERNAL TABLE  fldr1(
"C1" NUMBER(2,1),
"C2" VARCHAR(4),
"C3" NUMBER(2,0)
) FROM DEFAULT DIRECTORY EXTDIR_3 LOCATION ('quan.ctrl');

CREATE HUGE TABLE orders
	(
		o_orderkey           	INT, 
		o_custkey            	INT,
		o_orderstatus	        	CHAR(1), 
		o_totalprice	        	FLOAT,
		o_orderdate          	DATE,
		o_orderpriority	    		CHAR(15),
		o_clerk	            	CHAR(15),
		o_shippriority		    	INT,
		o_comment           	VARCHAR(79) STORAGE(stat none)
	)STORAGE(SECTION(65536) , FILESIZE(64), WITH DELTA, ON TS1) COMPRESS LEVEL 9 FOR 'QUERY HIGH'  (o_comment);

CREATE  TABLE  callinfo( 
caller 	CHAR(15), 
callee 	CHAR(15),
time  DATETIME,
duration  INT
)
PARTITION BY RANGE(time)(
PARTITION p1 VALUES LESS THAN ('2018-04-01'),
PARTITION p2 VALUES LESS THAN ('2018-07-01'),
PARTITION p3 VALUES LESS THAN ('2018-10-01'),
PARTITION p4 VALUES EQU OR LESS THAN ('2018-12-31'));

CREATE  TABLE  ages( 
name  VARCHAR(30), 
age   INT
)
PARTITION BY RANGE(age) INTERVAL(10)(
PARTITION p1 VALUES EQU OR LESS THAN (18),
PARTITION p2 VALUES EQU OR LESS THAN (35),
PARTITION p3 VALUES EQU OR LESS THAN (60),
PARTITION p4 VALUES EQU OR LESS THAN (100));

CREATE  TABLE  callinfo( 
caller  CHAR(15), 
callee  CHAR(15),
time  DATETIME,
duration  INT
)
PARTITION BY RANGE(time, duration)(
PARTITION p1 VALUES LESS THAN ('2018-04-01',10),
PARTITION p2 VALUES LESS THAN ('2018-07-01',20),
PARTITION p3 VALUES LESS THAN ('2018-10-01',30),
PARTITION p4 VALUES EQU OR LESS THAN ('2018-12-31', 40) );

CREATE  TABLE  sales( 
sales_id  INT,
saleman	  CHAR(20),
saledate  DATETIME,
city       CHAR(10)
)
PARTITION BY LIST(city)(
PARTITION p1 VALUES ('北京', '天津'),
PARTITION p2 VALUES ('上海', '南京', '杭州'),
PARTITION p3 VALUES ('武汉', '长沙'),
PARTITION p4 VALUES ('广州', '深圳'),
PARTITION p5 VALUES (default)
);

CREATE  TABLE  sales01( 
sales_id  INT,
saleman   CHAR(20),
saledate  DATETIME,
city       CHAR(10)
)
PARTITION BY HASH(city)(
PARTITION p1,
PARTITION p2,
PARTITION p3,
PARTITION p4
);

CREATE  TABLE  sales( 
sales_id	INT,
saleman		CHAR(20),
saledate	DATETIME,
city		CHAR(10)
)
PARTITION BY LIST(city)(
PARTITION p1 VALUES ('北京', '天津'),
PARTITION p2 VALUES ('上海', '南京', '杭州'),
PARTITION p3 VALUES ('武汉', '长沙'),
PARTITION p4 VALUES ('广州', '深圳'),
PARTITION p5 VALUES (default)
);

CREATE  TABLE  sales01( 
sales_id	INT,
saleman		CHAR(20),
saledate	DATETIME,
city		CHAR(10)
)
PARTITION BY HASH(city)(
PARTITION p1,
PARTITION p2,
PARTITION p3,
PARTITION p4
);

CREATE  TABLE  SALES( 
SALES_ID	 INT,
SALEMAN		CHAR(20),
SALEDATE	DATETIME,
CITY		  CHAR(10)
)
PARTITION BY LIST(CITY)
 SUBPARTITION BY RANGE(SALEDATE) SUBPARTITION TEMPLATE(
	SUBPARTITION P11 VALUES LESS THAN ('2012-04-01'),
	SUBPARTITION P12 VALUES LESS THAN ('2012-07-01'),
	SUBPARTITION P13 VALUES LESS THAN ('2012-10-01'),
	SUBPARTITION P14 VALUES EQU OR LESS THAN (MAXVALUE))
(
	PARTITION P1 VALUES ('北京', '天津')
	(
	  SUBPARTITION P11_1 VALUES LESS THAN ('2012-10-01'),
	  SUBPARTITION P11_2 VALUES EQU OR LESS THAN (MAXVALUE)
	),
	PARTITION P2 VALUES ('上海', '南京', '杭州'),
	PARTITION P3 VALUES (DEFAULT)
);

CREATE TABLE STUDENT(
NAME VARCHAR(20), 
AGE INT, 
SEX VARCHAR(10) CHECK (SEX IN ('MALE','FEMALE')), 
GRADE INT CHECK (GRADE IN (7,8,9))
)
PARTITION BY LIST(GRADE)
  SUBPARTITION BY LIST(SEX) SUBPARTITION TEMPLATE
  (
     SUBPARTITION Q1 VALUES('MALE'),
     SUBPARTITION Q2 VALUES('FEMALE')
  ),
  SUBPARTITION BY RANGE(AGE) SUBPARTITION TEMPLATE
  (
     SUBPARTITION R1 VALUES LESS THAN (12),
     SUBPARTITION R2 VALUES LESS THAN (15),
     SUBPARTITION R3 VALUES LESS THAN (MAXVALUE)
   )
(
  PARTITION P1 VALUES (7),
  PARTITION P2 VALUES (8),
  PARTITION P3 VALUES (9)
);



-- ALTER
ALTER TABLE PRODUCTION.PRODUCT_REVIEW MODIFY NAME VARCHAR(8) DEFAULT '刘青' NOT NULL;
ALTER TABLE RESOURCES.EMPLOYEE_ADDRESS ADD ID INT PRIMARY KEY CHECK (ID<10000);
ALTER TABLE PERSON.ADDRESS ADD PERSONID INT DEFAULT 10 NOT NULL;
ALTER TABLE PRODUCTION.PRODUCT DROP PRODUCT_SUBCATEGORYID CASCADE;
ALTER TABLE PRODUCTION.PRODUCT ADD CONSTRAINT CONS_PRODUCTNAME UNIQUE(NAME);
ALTER TABLE PRODUCTION.PRODUCT DROP CONSTRAINT CONS_PRODUCTNAME;
ALTER TABLE PERSON.PERSON ADD CONSTRAINT unq UNIQUE(PHONE);
ALTER TABLE PERSON.PERSON ENABLE CONSTRAINT unq;
ALTER TABLE PERSON.PERSON DISABLE CONSTRAINT unq;
ALTER TABLE PERSON.PERSON DROP CONSTRAINT unq;
ALTER TABLE T ALTER COLUMN C1 DROP USER (USER02);
ALTER TABLE T ALTER COLUMN C1 ADD USER (USER03);
ALTER TABLE PRODUCTION.PRODUCT_INVENTORY MERGE PARTITIONS P1,P2 INTO PARTITION P5;
ALTER TABLE T1 SPLIT PARTITION P1 AT(5,5) INTO(PARTITION P5,PARTITION P6);
ALTER TABLE T1 SPLIT PARTITION P3 INTO(PARTITION p7 VALUES LESS THAN(25,25),PARTITION p8 VALUES LESS THAN (28,28),PARTITION p9);
ALTER TABLE T1 MODIFY PARTITION FP1 ADD SUBPARTITION SP3 VALUES LESS THAN(300) ;
ALTER TABLE T1 MODIFY PARTITION FP2 ADD VALUES(7);
ALTER TABLE T1 DROP SUBPARTITION FOR(2,60);
ALTER TABLE T1 DROP SUBPARTITION FP1_SP3;
ALTER TABLE PARTITION_T1 EXCHANGE PARTITION PAR1 WITH TABLE PARTITION_T2;
ALTER TABLE STUDENT ALTER COLUMN (S_COMMENT) SET STAT;
ALTER TABLE STUDENT SET STAT NONE;
ALTER TABLE STUDENT SET STAT SYNCHRONOUS ON (s_no, s_class, s_comment);
ALTER TABLE ORDERS REBUILD SECTION;

-- DROP
DROP TABLE PERSON.PERSON CASCADE;


-- TRUNCATE
TRUNCATE TABLE PRODUCTION.PRODUCT_REVIEW;