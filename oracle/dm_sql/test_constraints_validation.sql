-- Oracle ALTER TABLE 约束测试文件
-- 测试修复后的 addConstraintSpecification 规则

-- 1. 原始问题：括号内多个约束（现在应该能正确解析）
ALTER TABLE employees ADD (
    CONSTRAINT emp_emp_id_pk PRIMARY KEY (employee_id),
    CONSTRAINT emp_dept_fk FOREIGN KEY (department_id) REFERENCES departments,
    CONSTRAINT emp_job_fk FOREIGN KEY (job_id) REFERENCES jobs (job_id),
    CONSTRAINT emp_manager_fk FOREIGN KEY (manager_id) REFERENCES employees
);

-- 2. 测试单个约束（保持向后兼容）
ALTER TABLE employees ADD CONSTRAINT emp_salary_check CHECK (salary > 0);

-- 3. 测试多个约束不使用括号（保持向后兼容）
ALTER TABLE employees ADD 
    CONSTRAINT emp_email_unique UNIQUE (email)
    CONSTRAINT emp_hire_date_check CHECK (hire_date <= SYSDATE);

-- 4. 测试混合约束类型在括号内
ALTER TABLE departments ADD (
    CONSTRAINT dept_id_pk PRIMARY KEY (department_id),
    CONSTRAINT dept_name_unique UNIQUE (department_name),
    CONSTRAINT dept_manager_fk FOREIGN KEY (manager_id) REFERENCES employees (employee_id)
);

-- 5. 测试NOT NULL约束
ALTER TABLE employees ADD (
    CONSTRAINT emp_first_name_nn CHECK (first_name IS NOT NULL),
    CONSTRAINT emp_last_name_nn CHECK (last_name IS NOT NULL)
);