ALTER DATABASE ADD LOGFILE 'C:\DMDBMS\data\dmlog_0.log' SIZE 200;
ALTER DATABASE RESIZE LOGFILE 'C:\DMDBMS\data\dmlog_0.log' TO 300;
ALTER DATABASE MOUNT;
ALTER DATABASE RENAME LOGFILE 'C:\DMDBMS\data\dmlog_0.log' TO 'd:\dmlog_1.log';
ALTER DATABASE OPEN;
ALTER DATABASE MOUNT;
ALTER DATABASE NORMAL;
ALTER DATABASE ARCHIVELOG;
ALTER DATABASE ADD ARCHIVELOG 'TYPE = local, DEST = c:\arch_local, FILE_SIZE = 128, SPACE_LIMIT = 1024';
ALTER DATABASE ADD ARCHIVELOG ' TYPE = REALTIME, DEST = realtime';
ALTER DATABASE ADD ARCHIVELOG 'TYPE = ASYNC, DEST = asyn1, ARCH_TIMER_NAME = timer1';
ALTER DATABASE ADD ARCHIVELOG 'TYPE=ASYNC, DEST=asyn2, ARCH_TIMER_NAME=timer2, ARCH_SEND_DELAY=10';
ALTER DATABASE OPEN;
ALTER DATABASE PRIMARY;
ALTER DATABASE OPEN FORCE;
ALTER DATABASE STANDBY;