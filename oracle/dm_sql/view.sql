-- 视图
CREATE  VIEW  PURCHASING.VEND<PERSON>_EXCELLENT  AS
SELECT  VENDORID, ACCOUNTNO, NAME, ACTIVEFLAG, CREDIT
FROM  PURCHASING.VENDOR
WHERE  CREDIT = 1;

CREATE VIEW SALES.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_INFO AS
SELECT T1.SALESPERSONID, T2.TITLE, T3.NAME, T1.SALESLASTYEAR
FROM SALES.SALESPERSON T1, RESOURCES.EMPLOYEE T2, PERSON.PERSON T3
WHERE T1.EMPLOYEEID = T2.EMPLOYEEID AND T2.PERSONID = T3.PERSONID;

CREATE  VIEW  PRODUCTION.VENDOR_STATIS(VENDORID, PRODUCT_COUNT)  AS
SELECT  VENDORID, COUNT(PRODUCTID)
FROM   PRODUCTION.PRODUCT_VENDOR
GROUP  BY  VENDORID
ORDER  BY  VENDORID;

DROP VIEW PURCHASING.VENDOR_EXCELLENT;

DROP VIEW SALES.SALESPERSON_INFO CASCADE;

ALTER VIEW PURCHASING.VENDOR_EXCELLENT COMPILE;


-- 物化视图
CREATE MATERIALIZED VIEW  PURCHASING.MV_VENDOR_EXCELLENT  
REFRESH WITH ROWID START WITH SYSDATE NEXT SYSDATE + 1 AS
SELECT  VENDORID, ACCOUNTNO, NAME, ACTIVEFLAG, CREDIT
FROM  PURCHASING.VENDOR
WHERE  CREDIT = 1;

ALTER MATERIALIZED VIEW PURCHASING.MV_VENDOR_EXCELLENT ENABLE QUERY REWRITE;
ALTER MATERIALIZED VIEW PURCHASING.MV_VENDOR_EXCELLENT REFRESH COMPLETE;
ALTER MATERIALIZED VIEW VIEW1 COMPILE;

DROP MATERIALIZED VIEW PURCHASING.MV_VENDOR_EXCELLENT;

REFRESH MATERIALIZED VIEW PURCHASING.MV_VENDOR_EXCELLENT FAST;


-- 物化视图日志
CREATE MATERIALIZED VIEW LOG ON PURCHASING.VENDOR WITH ROWID(ACCOUNTNO,NAME,ACTIVEFLAG,WEBURL,CREDIT) PURGE START WITH SYSDATE + 5  REPEAT INTERVAL '1' DAY;

DROP MATERIALIZED VIEW LOG ON log1;
