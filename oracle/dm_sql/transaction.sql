COMMIT WORK;

<PERSON><PERSON><PERSON> WORK IMMEDIATE NOWAIT;

<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> WORK;

SAVEPOINT A;

<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> TO SAVEPOINT A;

<PERSON><PERSON><PERSON><PERSON> SAVEPOINT C;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

SET TRANSACTION READ ONLY;

LOCK TABLE tablename IN EXCLUSIVE MODE;

LOCK TABLE tablename IN SHARE MODE;
LOCK TABLE tablename IN EXCLUSIVE MODE;
LOCK TABLE tablename IN SHARE INTENT EXCLUSIVE MODE;

LOCK TABLE tablename IN INTENT EXCLUSIVE MODE;
LOCK TABLE tablename IN EXCLUSIVE MODE;
LOCK TABLE tablename IN SHARE INTENT EXCLUSIVE MODE;


