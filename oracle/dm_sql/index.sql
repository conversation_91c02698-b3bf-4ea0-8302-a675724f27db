-- CREATE
CREATE INDEX S1 ON PURCHASING.VENDOR (VENDORID);
CREATE UNIQUE INDEX S2 ON PURCHASING.VENDOR (ACCOUNTNO, NAME);
CREATE INDEX INDEX_FBI ON SALES.SALESPERSON(SALESTHISYEAR-SALESLASTYEAR);
CREATE SPATIAL INDEX spidx ON testgeo (geo);
CREATE INDEX i1 ON t1(c1) REVERSE; 
CREATE INDEX uidx ON t2(c1) UNUSABLE; 

create bitmap index SALES_CUSTOMER_NAME_IDX
on SALES.SALESORDER_HEADER(SALES.CUSTOMER.PERSONID)
from   SALES.CUSTOMER, SALES.SALESORDER_HEADER
where  SALES.CUSTOMER.CUSTOMERID = SALES.SALESORDER_HEADER.CUSTOMERID;

CREATE CONTEXT INDEX INDEX0001 ON PERSON.ADDRESS(ADDRESS1) LEXER CHINESE_LEXER;

create spatial index spidx on testgeo(geom);

CREATE ARRAY INDEX IDX ON TEST(C1)

-- ALTER
ALTER INDEX PURCHASING.S1 RENAME TO PURCHASING.S2;
alter index  index_C1 INVISIBLE; 
ALTER  INDEX  INDEX_C1  UNUSABLE;
ALTER INDEX INDEX_C1 REBUILD SHARE;
ALTER INDEX INDEX_C1 REBUILD EXCLUSIVE;
ALTER CONTEXT INDEX INDEX0001 ON PERSON.ADDRESS REBUILD;

-- DROP
DROP INDEX sales.SALES_CUSTOMER_NAME_IDX;
DROP CONTEXT INDEX INDEX0001 ON PERSON.ADDRESS;