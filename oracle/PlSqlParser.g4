
parser grammar PlSqlParser;

options {
    tokenVocab = PlSqlLexer;
}

root
    : (select
    | insert
    | update
    | delete
    | createView
    | createTable
    | alterTable
    | dropTable
    | truncateTable
    | lockTable
    | createIndex
    | dropIndex
    | alterIndex
    | commit
    | rollback
    | setTransaction
    | savepoint
    | grant
    | revoke
    | createUser
    | dropUser
    | alterUser
    | createRole
    | dropRole
    | alterRole
    | setRole
    | call
    | merge
    | alterSynonym
    | alterSession
    | alterDatabase
    | alterSystem
    | setConstraints
    | analyze
    | associateStatistics
    | disassociateStatistics
    | audit
    | noAudit
    | comment
    | flashbackDatabase
    | flashbackTable
    | purge
    | rename
    | createDatabase
    | createDatabaseLink
    | createDimension
    | alterDimension
    | dropDimension
    | createFunction
    | dropDatabaseLink
    | dropDirectory
    | dropView
    | dropTrigger
    | alterView
    | alterTrigger
    | createEdition
    | alterDatabaseLink
    | alterDatabaseDictionary
    | createSynonym
    | createDirectory
    | dropSynonym
    | dropPackage
    | dropEdition
    | dropTableSpace
    | dropOutline
    | dropDatabase
    | alterOutline
    | alterAnalyticView
    | alterAttributeDimension
    | createSequence
    | alterSequence
    | alterPackage
    | createContext
    | createSPFile
    | createPFile
    | createControlFile
    | createFlashbackArchive
    | alterFlashbackArchive
    | dropFlashbackArchive
    | createDiskgroup
    | dropDiskgroup
    | createRollbackSegment
    | dropRollbackSegment
    | createLockdownProfile
    | dropLockdownProfile
    | createInmemoryJoinGroup
    | alterInmemoryJoinGroup
    | dropInmemoryJoinGroup
    | createRestorePoint
    | dropRestorePoint
    | dropOperator
    | dropType
    | dropTypeBody
    | alterLibrary
    | alterMaterializedZonemap
    | alterJava
    | alterAuditPolicy
    | alterCluster
    | alterOperator
    | alterProfile
    | alterRollbackSegment
    | alterDiskgroup
    | alterIndexType
    | createMaterializedView
    | createMaterializedViewLog
    | alterMaterializedView
    | alterMaterializedViewLog
    | alterFunction
    | alterHierarchy
    | alterLockdownProfile
    | alterPluggableDatabase
    | explain
    | createProcedure
    | dropProcedure
    | alterProcedure
    | dropIndexType
    | dropPluggableDatabase
    | dropJava
    | dropLibrary
    | dropMaterializedView
    | dropMaterializedViewLog
    | dropMaterializedZonemap
    | dropContext
    | alterResourceCost
    | alterRole
    | createTablespace
    | alterTablespace
    | dropSequence
    | dropProfile
    | dropFunction
    | dropCluster
    | systemAction
    | alterType
    | createType
    | createTypeBody
    | createCluster
    | createJava
    | plsqlBlock
    | createLibrary
    | switch
    | createProfile
    | createTrigger
    | show
    | spool
    | createOperator
    | createPackage
    | createPackageBody
    | createSchema
    | createOutline
    ) SEMI_? SLASH_? EOF
    ;

alterResourceCost
    : ALTER RESOURCE COST ((CPU_PER_SESSION | CONNECT_TIME | LOGICAL_READS_PER_SESSION | PRIVATE_SGA) INTEGER_)+
    ;

dbLinkInfo
    : databaseName (DOT_ domain)* (AT_ connectionQualifier)?
    ;

explain
    : EXPLAIN PLAN (SET STATEMENT_ID EQ_ stringLiterals)? (INTO tableName (AT_ dbLinkInfo)? )? FOR (insert | delete | update | select)
    ;

schema
    : identifier
    ;

parameterName
    : identifier | STRING_
    ;

originalName
    : identifier
    ;

showOptions
    : systemVariable
    | ALL
    | CON_ID
    | CON_NAME
    | EDITION
    | (BTI | BTITLE)
    | (ERR | ERRORS) ((ANALYTIC VIEW | ATTRIBUTE DIMENSION | HIERARCHY | FUNCTION | PROCEDURE | PACKAGE | PACKAGE BODY | TRIGGER  | VIEW | TYPE | TYPE BODY | DIMENSION | JAVA CLASS) (schema DOT_)? name)?
    | HISTORY
    | LNO
    | LOBPREFETCH
    | (PARAMETER | PARAMETERS) parameterName?
    | PDBS
    | PNO
    | (RECYC | RECYCLEBIN) originalName?
    | (REL | RELEASE)
    | (REPF | REPFOOTER)
    | (REPH | REPHEADER)
    | (ROWPREF | ROWPREFETCH)
    | SGA
    | (SPOO | SPOOL)
    | (SPPARAMETER | SPPARAMETERS) parameterName?
    | SQLCODE
    | (STATEMENTC | STATEMENTCACHE)
    | (TTI | TLE)
    | USER
    | XQUERY
    ;

//    https://docs.oracle.com/en/database/oracle/oracle-database/23/sqpug/SET-system-variable-summary.html
systemVariable
    : identifier (ON | OFF | numberLiterals | stringLiterals | identifier)
    ;
show
    : (SHO | SHOW) showOptions
    ;

fileExt
    : DOT_ identifier
    ;

spoolFileName
    : identifier fileExt?
    ;

spool
    : (SPOOL | SPO) (spoolFileName (CRE | CREATE | REP | REPLACE | APP | APPEND)?) | OFF | OUT
    ;


grant
    : GRANT ((objectPrivilegeClause grantObjectTo) | (systemPrivilegeClause grantSystemTo) | (roleClause grantRoleTo))
    ;

grantObjectTo
    : TO revokeeGranteeClause (WITH HIERARCHY OPTION)? (WITH GRANT OPTION)?
    ;

grantRoleTo
    : TO roleClauseFrom
    ;

granteeIdentifiedBy
    : name (COMMA_ name)* IDENTIFIED BY name (COMMA_ name)*
    ;

revoke
    : REVOKE (((objectPrivilegeClause | systemPrivilegeClause) objectPrivilegeFrom) | roleClause FROM roleClauseFrom)
    ;

objectPrivilegeClause
    : (objectPrivileges | objectPrivilegesWithColumn ) (COMMA_  (objectPrivileges | objectPrivilegesWithColumn ))* ON onObjectClause
    ;

objectPrivilegeFrom
    : FROM revokeeGranteeClause ((CASCADE CONSTRAINTS) | FORCE)?
    ;

revokeeGranteeClause
    : (name | PUBLIC) (COMMA_ (name | PUBLIC))*
    ;

systemPrivilegeClause
    : systemPrivilege (COMMA_ systemPrivilege)*
    ;

grantSystemTo
    : TO (revokeeGranteeClause | granteeIdentifiedBy) (WITH (ADMIN | DELEGATE) OPTION)?
    ;

roleClause
    : ignoredIdentifiers
    ;

roleClauseFrom
    : programUnit (COMMA_ programUnit)*
    ;

programUnit
    : FUNCTION functionName | PROCEDURE procedureName | PACKAGE packageName
    ;

objectPrivileges
    : objectPrivilegeType
    ;

objectPrivilegesWithColumn
    : objectPrivilegeType columnNames
    ;

objectPrivilegeType
    : ALL PRIVILEGES?
    | SELECT
    | INSERT
    | DELETE
    | UPDATE
    | ALTER
    | READ
    | WRITE
    | EXECUTE
    | USE
    | INDEX
    | REFERENCES
    | DEBUG
    | UNDER
    | FLASHBACK
    | FLASHBACK ARCHIVE
    | ON COMMIT REFRESH
    | QUERY REWRITE
    | KEEP SEQUENCE
    | INHERIT PRIVILEGES
    | TRANSLATE SQL
    | MERGE VIEW
    ;

onObjectClause
    : USER username (COMMA_ username)*
    | DIRECTORY directoryName
    | EDITION editionName
    | MINING MODEL modelName (schemaName DOT_)? name
    | SQL TRANSLATION PROFILE profileName
    | JAVA (SOURCE | RESOURCE) tableName
    | tableName
    ;

systemPrivilege
    : ALL PRIVILEGES
    | advisorFrameworkSystemPrivilege
    | clustersSystemPrivilege
    | contextsSystemPrivilege
    | dataRedactionSystemPrivilege
    | databaseSystemPrivilege
    | databaseLinksSystemPrivilege
    | debuggingSystemPrivilege
    | dictionariesSystemPrivilege
    | dimensionsSystemPrivilege
    | directoriesSystemPrivilege
    | editionsSystemPrivilege
    | flashbackDataArchivesPrivilege
    | indexesSystemPrivilege
    | indexTypesSystemPrivilege
    | jobSchedulerObjectsSystemPrivilege
    | keyManagementFrameworkSystemPrivilege
    | librariesFrameworkSystemPrivilege
    | logminerFrameworkSystemPrivilege
    | materizlizedViewsSystemPrivilege
    | miningModelsSystemPrivilege
    | olapCubesSystemPrivilege
    | olapCubeMeasureFoldersSystemPrivilege
    | olapCubeDiminsionsSystemPrivilege
    | olapCubeBuildProcessesSystemPrivilege
    | operatorsSystemPrivilege
    | outlinesSystemPrivilege
    | planManagementSystemPrivilege
    | pluggableDatabasesSystemPrivilege
    | proceduresSystemPrivilege
    | profilesSystemPrivilege
    | rolesSystemPrivilege
    | rollbackSegmentsSystemPrivilege
    | sequencesSystemPrivilege
    | sessionsSystemPrivilege
    | sqlTranslationProfilesSystemPrivilege
    | synonymsSystemPrivilege
    | tablesSystemPrivilege
    | tablespacesSystemPrivilege
    | triggersSystemPrivilege
    | typesSystemPrivilege
    | usersSystemPrivilege
    | viewsSystemPrivilege
    | miscellaneousSystemPrivilege
    | ruleSystemPrivilege
    | name
    ;

systemPrivilegeOperation
    : (CREATE | ALTER | DROP | SELECT | INSERT | UPDATE | DELETE | EXECUTE) ANY?
    ;

advisorFrameworkSystemPrivilege
    : systemPrivilegeOperation? SQL PROFILE | ADVISOR | ADMINISTER ANY? SQL (TUNING SET | MANAGEMENT OBJECT)
    ;

clustersSystemPrivilege
    : systemPrivilegeOperation CLUSTER
    ;

contextsSystemPrivilege
    : systemPrivilegeOperation CONTEXT
    ;

dataRedactionSystemPrivilege
    : EXEMPT REDACTION POLICY
    ;

databaseSystemPrivilege
    : ALTER (DATABASE | SYSTEM) | AUDIT SYSTEM
    ;

databaseLinksSystemPrivilege
    : (CREATE | ALTER | DROP) PUBLIC? DATABASE LINK
    ;

debuggingSystemPrivilege
    : DEBUG (CONNECT SESSION | ANY PROCEDURE)
    ;

dictionariesSystemPrivilege
    : ANALYZE ANY DICTIONARY
    ;

dimensionsSystemPrivilege
    : systemPrivilegeOperation DIMENSION
    ;

directoriesSystemPrivilege
    : systemPrivilegeOperation DIRECTORY
    ;

editionsSystemPrivilege
    : systemPrivilegeOperation EDITION
    ;

flashbackDataArchivesPrivilege
    : FLASHBACK ARCHIVE ADMINISTER
    ;

indexesSystemPrivilege
    : systemPrivilegeOperation INDEX
    ;

indexTypesSystemPrivilege
    : systemPrivilegeOperation INDEXTYPE
    ;

jobSchedulerObjectsSystemPrivilege
    : CREATE (ANY | EXTERNAL)? JOB | EXECUTE ANY (CLASS | PROGRAM) | MANAGE SCHEDULER
    ;

keyManagementFrameworkSystemPrivilege
    : ADMINISTER KEY MANAGEMENT
    ;

librariesFrameworkSystemPrivilege
    : systemPrivilegeOperation LIBRARY
    ;

logminerFrameworkSystemPrivilege
    : LOGMINING
    ;

materizlizedViewsSystemPrivilege
    : systemPrivilegeOperation MATERIALIZED VIEW | GLOBAL? QUERY REWRITE | ON COMMIT REFRESH | FLASHBACK ANY TABLE
    ;

miningModelsSystemPrivilege
    : (systemPrivilegeOperation | COMMENT ANY) MINING MODEL
    ;

olapCubesSystemPrivilege
    : systemPrivilegeOperation CUBE
    ;

olapCubeMeasureFoldersSystemPrivilege
    : systemPrivilegeOperation MEASURE FOLDER
    ;

olapCubeDiminsionsSystemPrivilege
    : systemPrivilegeOperation CUBE DIMENSION
    ;

olapCubeBuildProcessesSystemPrivilege
    : systemPrivilegeOperation CUBE BUILD PROCESS
    ;

operatorsSystemPrivilege
    : systemPrivilegeOperation OPERATOR
    ;

outlinesSystemPrivilege
    : systemPrivilegeOperation OUTLINE
    ;

planManagementSystemPrivilege
    : ADMINISTER SQL MANAGEMENT OBJECT
    ;

pluggableDatabasesSystemPrivilege
    : CREATE PLUGGABLE DATABASE | SET CONTAINER
    ;

proceduresSystemPrivilege
    : systemPrivilegeOperation PROCEDURE
    ;

profilesSystemPrivilege
    : systemPrivilegeOperation PROFILE
    ;

rolesSystemPrivilege
    : (systemPrivilegeOperation | GRANT ANY) ROLE
    ;

rollbackSegmentsSystemPrivilege
    : systemPrivilegeOperation ROLLBACK SEGMENT
    ;

sequencesSystemPrivilege
    : systemPrivilegeOperation SEQUENCE
    ;

sessionsSystemPrivilege
    : (CREATE | ALTER | RESTRICTED) SESSION | ALTER RESOURCE COST
    ;

sqlTranslationProfilesSystemPrivilege
    : (systemPrivilegeOperation | USE ANY) SQL TRANSLATION PROFILE | TRANSLATE ANY SQL
    ;

synonymsSystemPrivilege
    : systemPrivilegeOperation SYNONYM | DROP PUBLIC SYNONYM
    ;

tablesSystemPrivilege
    : (systemPrivilegeOperation | (BACKUP | LOCK | READ | FLASHBACK) ANY) TABLE
    ;

tablespacesSystemPrivilege
    : (systemPrivilegeOperation | MANAGE | UNLIMITED) TABLESPACE
    ;

triggersSystemPrivilege
    : systemPrivilegeOperation TRIGGER | ADMINISTER DATABASE TRIGGER
    ;

typesSystemPrivilege
    : (systemPrivilegeOperation | UNDER ANY) TYPE
    ;

usersSystemPrivilege
    : systemPrivilegeOperation USER
    ;

ruleSystemPrivilege
    : createOperation* (TO username)?
    ;

createOperation
    : systemPrivilegeOperation (RULE SET? | EVALUATION CONTEXT) COMMA_?
    ;

viewsSystemPrivilege
    : (systemPrivilegeOperation | (UNDER | MERGE) ANY) VIEW
    ;

miscellaneousSystemPrivilege
    : ANALYZE ANY | AUDIT ANY | BECOME USER | CHANGE NOTIFICATION | COMMENT ANY TABLE | EXEMPT ACCESS POLICY | FORCE ANY? TRANSACTION
    | GRANT ANY OBJECT? PRIVILEGE | INHERIT ANY PRIVILEGES | KEEP DATE TIME | KEEP SYSGUID | PURGE DBA_RECYCLEBIN | RESUMABLE
    | SELECT ANY (DICTIONARY | TRANSACTION) | SYSBACKUP | SYSDBA | SYSDG | SYSKM | SYSOPER
    ;

createUser
    : CREATE USER username createUserIdentifiedClause createUserOption*
    ;

createUserIdentifiedClause
    : IDENTIFIED createUseridentifiedSegment
    | noAuthOption
    ;

createUseridentifiedSegment
    : BY password HTTP? DIGEST? (ENABLE | DISABLE)?
    | identifiedExternallyOption
    | identifiedGloballyOption
    ;

identifiedExternallyOption
    : EXTERNALLY (AS SQ_ name SQ_)?
    ;

identifiedGloballyOption
    : GLOBALLY (AS SQ_ (name | (AZURE_ROLE | AZURE_USER | IAM_GROUP_NAME | IAM_PRINCIPAL_NAME) EQ_ name) SQ_)?
    ;

noAuthOption
    : NO AUTHENTICATION
    ;

createUserOption
    : collationOption
    | tablespaceOption
    | temporaryOption
    | quotaOption
    | profileOption
    | passwordOption
    | accountOption
    | ENABLE EDITIONS
    | containerOption
    ;

collationOption
    : DEFAULT COLLATION collationName
    ;

tablespaceOption
    : DEFAULT TABLESPACE tablespaceName
    ;

temporaryOption
    : LOCAL? TEMPORARY TABLESPACE (tablespaceName | tablespaceGroupName)
    ;

quotaOption
    : QUOTA (sizeClause | UNLIMITED) ON tablespaceName
    ;

profileOption
    : PROFILE profileName
    ;

passwordOption
    : PASSWORD EXPIRE
    ;

accountOption
    : ACCOUNT (LOCK | UNLOCK)
    ;

containerOption
    : CONTAINER EQ_ (CURRENT | ALL)
    ;

dropUser
    : DROP USER ifExists? username cascadeOrRestrict?
    ;

// DM
ifExists
    : IF EXISTS
    ;

// DM
ifNotExists
    : IF NOT EXISTS
    ;

alterUser
    : ALTER USER ((username (IDENTIFIED (BY password (REPLACE password)?
    | EXTERNALLY (AS CERTIFICATE_DN | AS KERBEROS_PRINCIPAL_NAME)?
    | GLOBALLY AS (STRING_ | SQ_ AZURE_ROLE EQ_ identifier SQ_ | SQ_ IAM_GROUP_NAME EQ_ identifier SQ_))
    | NO AUTHENTICATION
    | DEFAULT COLLATION collationName
    | DEFAULT TABLESPACE tablespaceName
    | LOCAL? TEMPORARY TABLESPACE tablespaceName tablespaceGroupName
    | QUOTA (sizeClause | UNLIMITED) ON tablespaceName
    | PROFILE profileName
    | DEFAULT ROLE (NONE | roleName (COMMA_ roleName)* |  allClause )
    | PASSWORD EXPIRE
    | ACCOUNT (LOCK | UNLOCK)
    | ENABLE EDITIONS (FOR editionType (COMMA_ editionType)*)? FORCE?
    | HTTP? DIGEST (ENABLE | DISABLE)
    | CONTAINER EQ_ (CURRENT | ALL)
    | containerDataClause)*) | username (COMMA_ username)* proxyClause*)
    ;

createRole
    : CREATE ROLE roleName ( NOT IDENTIFIED | identifiedCluase)? (CONTAINER EQ_ (CURRENT | ALL))?
    ;

dropRole
    : DROP ROLE roleName
    ;

alterRole
    : ALTER ROLE roleName ( NOT IDENTIFIED | identifiedCluase  ) (CONTAINER EQ_ (CURRENT | ALL))?
    ;

identifiedCluase
    : IDENTIFIED (
    | BY password
    | USING packageName
    | EXTERNALLY
    | GLOBALLY AS (STRING_ | SQ_ AZURE_ROLE EQ_ identifier SQ_ | SQ_ IAM_GROUP_NAME EQ_ identifier SQ_)
    | GLOBALLY)
    ;

setRole
    : SET ROLE (roleAssignment | allClause | NONE)
    ;

roleAssignment
    : roleName (IDENTIFIED BY password)? (COMMA_ roleName (IDENTIFIED BY password)? )*
    ;

allClause
    : ALL (EXCEPT roleName (COMMA_ roleName)*)?
    ;

insert
    : INSERT hint? (insertSingleTable | insertMultiTable)
    ;

insertSingleTable
    : insertIntoClause (insertValuesClause | selectSubquery) returningClause? errorLoggingClause?
    ;

insertMultiTable
    : (ALL multiTableElement+ | conditionalInsertClause) selectSubquery
    ;

multiTableElement
    : insertIntoClause insertValuesClause? errorLoggingClause?
    ;

conditionalInsertClause
    : (ALL | FIRST)? conditionalInsertWhenPart+ conditionalInsertElsePart?
    ;

conditionalInsertWhenPart
    : WHEN expr THEN multiTableElement+
    ;

conditionalInsertElsePart
    : ELSE multiTableElement+
    ;

insertIntoClause
    : INTO dmlTableExprClause aliasClause? columnNames?
    ;

insertValuesClause
    : VALUES assignmentValues
    ;

returningClause
    : (RETURN | RETURNING) exprs INTO dataItem (COMMA_ dataItem)*
    ;

dmlTableExprClause
    : dmlTableClause | dmlSubqueryClause | tableCollectionExpr
    ;

dmlTableClause
    : tableName (partitionExtClause | dbLinkClause)?
    | (viewName | materializedViewName) dbLinkClause?
    ;

dbLinkClause
    : AT_ dbLink
    ;

partitionExtClause
    : PARTITION (LP_ partitionName RP_ | FOR LP_ partitionKeyValue (COMMA_ partitionKeyValue)? RP_)
    | SUBPARTITION (LP_ subpartitionName RP_ | FOR LP_ subpartitionKeyValue (COMMA_ subpartitionKeyValue) RP_)
    ;

dmlSubqueryClause
    : LP_ selectSubquery subqueryRestrictionClause? RP_
    ;

subqueryRestrictionClause
    : WITH (READ ONLY | CHECK OPTION) (CONSTRAINT constraintName)?
    ;

tableCollectionExpr
    : TABLE LP_ collectionExpr RP_ (LP_ PLUS_ RP_)?
    ;

collectionExpr
    : selectSubquery | columnName | functionCall | expr
    ;

update
    : UPDATE hint? updateSpecification aliasClause? updateSetClause whereClause? returningClause? errorLoggingClause?
    ;

aliasClause
    : alias
    | AS alias
    ;

updateSpecification
    : dmlTableExprClause | (ONLY LP_ dmlTableExprClause RP_)
    ;

updateSetClause
    : SET (updateSetColumnList | updateSetValueClause)
    ;

updateSetColumnList
    : updateSetColumnClause (COMMA_ updateSetColumnClause)*
    ;

updateSetColumnClause
    : (LP_ columnName (COMMA_ columnName)* RP_ EQ_ LP_ selectSubquery RP_ aliasClause?)
    | (columnName EQ_ (expr | LP_ selectSubquery RP_ | DEFAULT))
    ;

updateSetValueClause
    : VALUE LP_ columnName RP_ EQ_ (expr | LP_ selectSubquery RP_)
    ;

assignmentValues
    : LP_ assignmentValue (COMMA_ assignmentValue)* RP_
    ;

assignmentValue
    : expr | DEFAULT | sequenceName
    ;

delete
    : DELETE hint? FROM? deleteSpecification aliasClause? whereClause? returningClause? errorLoggingClause?
    ;

deleteSpecification
    : dmlTableExprClause
    | ONLY LP_ dmlTableExprClause RP_
    ;

// DM https://eco.dameng.com/document/dm/zh-cn/pm/check-phrases.html#
select
    : selectSubquery forUpdateClause?
    ;

selectSubquery
    : selectSubquery combineType selectSubquery
    | ((queryBlock | parenthesisSelectSubquery) pivotClause? orderByClause? rowLimitingClause?)
    ;

combineType
    : (UNION ALL? | INTERSECT | MINUS)
    ;

parenthesisSelectSubquery
    : LP_ selectSubquery RP_
    ;

// selectFromClause? 适配DM的json运算和函数select
queryBlock
    : withClause? SELECT hint? duplicateSpecification? selectList (intoClause | bulkCollectIntoClause)? selectFromClause? whereClause? hierarchicalQueryClause? groupByClause? modelClause?
    ;

// https://docs.oracle.com/en/database/oracle/oracle-database/21/sqlrf/Comments.html
hint
    : INLINE_HINT | BLOCK_HINT
    ;

// DM https://eco.dameng.com/document/dm/zh-cn/pm/check-phrases.html#4.4%20WITH%20%E5%AD%90%E5%8F%A5
withClause
    : WITH plsqlDeclarations? ((subqueryFactoringClause | subavFactoringClause) (COMMA_ (subqueryFactoringClause | subavFactoringClause))*)?
    ;

plsqlDeclarations
    :  (functionDeclaration | procedureDeclaration)+
    ;

functionDeclaration
    : functionHeading ((DETERMINISTIC | PIPELINED | PARALLEL_ENABLE | RESULT_CACHE)+)? SEMI_
    ;

functionHeading
    : FUNCTION funName parameterDeclarationList? RETURN dataType
    ;
// 系统包中的函数名有单引号包裹的字符串 STANDARD
funName
    : (owner DOT_)? name | STRING_
    ;
procedureDeclaration
    : procedureHeading procedureProperties* SEMI_
    ;

procedureHeading
    : PROCEDURE procedureName parameterDeclarationList?
    ;

procedureProperties
    : accessibleByClause | defaultCollationClause | invokerRightsClause
    ;

accessibleByClause
    : ACCESSIBLE BY LP_ accessor (COMMA_ accessor)* RP_
    ;

accessor
    : unitKind? unitName
    ;

unitKind
    : FUNCTION
    | PROCEDURE
    | PACKAGE
    | TRIGGER
    | TYPE
    ;

defaultCollationClause
    : DEFAULT COLLATION USING_NLS_COMP
    ;

invokerRightsClause
    : AUTHID (CURRENT_USER | DEFINER)
    ;

subqueryFactoringClause
    : queryName (LP_ alias (COMMA_ alias)* RP_)? AS subquery searchClause? cycleClause?
    ;

searchClause
    : SEARCH (DEPTH | BREADTH) FIRST BY (alias (ASC | DESC)? (NULLS FIRST | NULLS LAST)?) (COMMA_ (alias (ASC | DESC)? (NULLS FIRST | NULLS LAST)?))*
    SET orderingColumn
    ;

cycleClause
    : CYCLE alias (COMMA_ alias)* SET alias TO cycleValue DEFAULT noCycleValue
    ;

subavFactoringClause
    : subavName ANALYTIC VIEW AS LP_ subavClause RP_
    ;

subavClause
    : USING baseAvName hierarchiesClause? filterClauses? addCalcsClause?
    ;

hierarchiesClause
    : HIERARCHIES LP_ ((alias DOT_)? alias (COMMA_ (alias DOT_)? alias)*)? RP_
    ;

filterClauses
    : FILTER FACT LP_ filterClause (COMMA_ filterClause)* RP_
    ;

filterClause
    : (MEASURES | (alias DOT_)? alias) TO predicate
    ;

addCalcsClause
    : ADD MEASURES LP_ calcMeasClause (COMMA_ calcMeasClause)* RP_
    ;

calcMeasClause
    : measName AS LP_ calcMeasExpression RP_
    ;

calcMeasExpression
    : avExpression | expr
    ;

avExpression
    : avMeasExpression | avHierExpression
    ;

avMeasExpression
    : leadLagExpression
    | windowExpression
    | rankExpression
    | shareOfExpression
    | qdrExpression
    ;

leadLagExpression
    : leadLagFunctionName LP_ calcMeasExpression RP_ OVER LP_ leadLagClause RP_
    ;

leadLagFunctionName
    : LAG
    | LAG_DIFF
    | LAG_DIF_PERCENT
    | LEAD
    | LEAD_DIFF
    | LEAD_DIFF_PERCENT
    ;

leadLagClause
    : HIERARCHY hierarchyRef OFFSET offsetExpr
    ((WITHIN (LEVEL | PARENT)) | (ACROSS ANCESTOR AT LEVEL levelRef (POSITION FROM (BEGINNING | END))?))?
    ;

hierarchyRef
    : (alias DOT_)? alias
    ;

windowExpression
    : aggregationFunction OVER LP_ windowClause RP_
    ;

windowClause
    : HIERARCHY hierarchyRef BETWEEN (precedingBoundary | followingBoundary)
    (WITHIN (LEVEL | PARENT | ANCESTOR AT LEVEL levelRef))?
    ;

precedingBoundary
    : (UNBOUNDED PRECEDING | offsetExpr PRECEDING) AND (CURRENT MEMBER | offsetExpr (PRECEDING | FOLLOWING) | UNBOUNDED FOLLOWING)
    ;

followingBoundary
    : (CURRENT MEMBER | offsetExpr FOLLOWING) AND (offsetExpr FOLLOWING | UNBOUNDED FOLLOWING)
    ;

rankExpression
    : rankFunctionName LP_ RP_ OVER LP_ rankClause RP_
    ;

rankFunctionName
    : RANK
    | DENSE_RANK
    | AVERAGE_RANK
    | ROW_NUMBER
    ;

rankClause
    : HIERARCHY hierarchyRef ORDER BY calcMeasOrderByClause (COMMA_ calcMeasOrderByClause)*
    (WITHIN (LEVEL | PARENT | ANCESTOR AT LEVEL levelRef))?
    ;

calcMeasOrderByClause
    : calcMeasExpression (ASC | DESC)? (NULLS (FIRST | LAST))?
    ;

shareOfExpression
    : SHARE_OF LP_ calcMeasExpression shareClause RP_
    ;

shareClause
    : HIERARCHY hierarchyRef (PARENT | LEVEL levelRef | MEMBER memberExpression)
    ;

memberExpression
    : levelMemberLiteral
    | hierNavigationExpression
    | CURRENT MEMBER
    | NULL
    | ALL
    ;

levelMemberLiteral
    : levelRef (posMemberKeys | namedMemberKeys)
    ;

posMemberKeys
    : SQ_ LBT_ SQ_ memberKeyExpr (COMMA_ memberKeyExpr)* SQ_ RBT_ SQ_
    ;

namedMemberKeys
    : SQ_ LBT_ SQ_ (attributeName EQ_ memberKeyExpr) (COMMA_ (attributeName EQ_ memberKeyExpr))* SQ_ RBT_ SQ_
    ;

hierNavigationExpression
    : hierAncestorExpression | hierParentExpression | hierLeadLagExpression
    ;

hierAncestorExpression
    : HIER_ANCESTOR LP_ memberExpression AT (LEVEL levelRef | DEPTH depthExpression) RP_
    ;

hierParentExpression
    : HIER_PARENT LP_ memberExpression RP_
    ;

hierLeadLagExpression
    : (HIER_LEAD | HIER_LAG) LP_ hierLeadLagClause RP_
    ;

hierLeadLagClause
    : memberExpression OFFSET offsetExpr
    (WITHIN ((LEVEL | PARENT) | (ACROSS ANCESTOR AT LEVEL levelRef (POSITION FROM (BEGINNING | END))?)))?
    ;

qdrExpression
    : QUALIFY LP_ calcMeasExpression COMMA_ qualifier RP_
    ;

qualifier
    : hierarchyRef EQ_ memberExpression
    ;

avHierExpression
    : hierFunctionName LP_ memberExpression WITHIN HIERARCHY hierarchyRef RP_
    ;

hierFunctionName
    : HIER_CAPTION
    | HIER_DEPTH
    | HIER_DESCRIPTION
    | HIER_LEVEL
    | HIER_MEMBER_NAME
    | HIER_MEMBER_UNIQUE_NAME
    ;

duplicateSpecification
    : (DISTINCT | UNIQUE) | ALL
    ;

unqualifiedShorthand
    : ASTERISK_
    ;

selectList
    : (unqualifiedShorthand | selectProjection) (COMMA_ selectProjection)*
    ;

selectProjection
    : (queryName | tableName | viewName | materializedViewName | sequenceName | alias) DOT_ASTERISK_
    | selectProjectionExprClause
    ;

selectProjectionExprClause
    : (columnName | expr) aliasClause?
    ;

selectFromClause
    : FROM fromClauseList
    ;

fromClauseList
    : fromClauseOption (COMMA_ fromClauseOption)*
    ;

fromClauseOption
    : selectTableReference
//    | joinClause
//    | LP_ joinClause RP_
    | inlineAnalyticView
    | (regularFunction | xmlTableFunction) alias?
    ;

selectTableReference
    : (queryTableExprClause | containersClause | shardsClause) alias? selectJoinOption*
    | LP_ selectTableReference RP_
    ;

queryTableExprClause
    : (ONLY LP_ queryTableExpr RP_ | queryTableExpr) flashbackQueryClause? (pivotClause | unpivotClause | rowPatternClause)?
    ;

flashbackQueryClause
    : VERSIONS (BETWEEN scnTimestampLsn | PERIOD FOR validTimeColumn BETWEEN) (expr | MINVALUE) AND (expr | MAXVALUE)
    | AS OF (scnTimestampLsn (expr | intervalExprClause) | PERIOD FOR validTimeColumn expr)
    ;

intervalExprClause
    : LP_ SYSTIMESTAMP  (PLUS_ | MINUS_)  INTERVAL (INTEGER_ | STRING_) (HOUR | MINUTE | SECOND) RP_
    ;

queryTableExpr
    : queryTableExprSampleClause
    | lateralClause
    | tableCollectionExpr
    ;

lateralClause
    : LATERAL? LP_ selectSubquery subqueryRestrictionClause? RP_
    ;

queryTableExprSampleClause
    : queryTableExprTableClause sampleClause?
    ;

queryTableExprTableClause
    : tableName (mofifiedExternalTable | partitionExtClause | dbLinkClause | queryTableExprAnalyticClause)?
    ;

queryTableExprAnalyticClause
    : HIERARCHIES LP_ ((owner DOT_)* name (COMMA_ (owner DOT_)* name)*)? RP_
    ;


mofifiedExternalTable
    : EXTERNAL MODIFY modifyExternalTableProperties
    ;

modifyExternalTableProperties
    : (DEFAULT DIRECTORY directoryName)? (LOCATION LP_ (directoryName COLON_)? SQ_ locationSpecifier SQ_ (COMMA_ (directoryName COLON_)? SQ_ locationSpecifier SQ_)* RP_)?
    (ACCESS PARAMETERS (BADFILE fileName | LOGFILE fileName | DISCARDFILE fileName))? (REJECT LIMIT (INTEGER_ | UNLIMITED))?
    ;

pivotClause
    : PIVOT XML?
    LP_ aggregationFunction aliasClause? (COMMA_ aggregationFunction aliasClause?)* pivotForClause pivotInClause RP_
    ;

pivotForClause
    : FOR columnNames
    ;

pivotInClause
    : IN LP_ (pivotInClauseExpr (COMMA_ pivotInClauseExpr)*
    | selectSubquery
    | ANY (COMMA_ ANY)*) RP_
    ;

pivotInClauseExpr
    : (expr | exprList) aliasClause?
    ;

unpivotClause
    : UNPIVOT ((INCLUDE | EXCLUDE) NULLS)? LP_ columnNames pivotForClause unpivotInClause RP_
    ;

unpivotInClause
    : IN LP_ unpivotInClauseExpr (COMMA_ unpivotInClauseExpr)* RP_
    ;

unpivotInClauseExpr
    : columnNames (AS (literals | LP_ literals (COMMA_ literals)* RP_))?
    ;

sampleClause
    : SAMPLE BLOCK? LP_ samplePercent RP_ (SEED LP_ seedValue RP_)?
    ;

containersClause
    : CONTAINERS LP_ (tableName | viewName) RP_
    ;

shardsClause
    : SHARDS LP_ (tableName | viewName) RP_
    ;


selectJoinOption
    : innerCrossJoinClause
    | outerJoinClause
    | crossOuterApplyClause
    ;

innerCrossJoinClause
    : INNER? JOIN selectTableReference selectJoinSpecification
    | (CROSS | NATURAL INNER?) JOIN selectTableReference
    ;

selectJoinSpecification
    : ON expr | USING columnNames
    ;

outerJoinClause
    : queryPartitionClause? NATURAL? outerJoinType JOIN
    selectTableReference queryPartitionClause? selectJoinSpecification?
    ;

queryPartitionClause
    : PARTITION BY (exprs | exprList)
    ;

outerJoinType
    : (FULL | LEFT | RIGHT) OUTER?
    ;

crossOuterApplyClause
    : (CROSS | OUTER) APPLY (selectTableReference | collectionExpr)
    ;

inlineAnalyticView
    : ANALYTIC VIEW LP_ subavClause RP_ aliasClause?
    ;

whereClause
    : WHERE expr
    ;

hierarchicalQueryClause
    : CONNECT BY NOCYCLE? expr (START WITH expr)?
    | START WITH expr CONNECT BY NOCYCLE? expr
    ;

groupByClause
    : GROUP BY groupByItem (COMMA_ groupByItem)* havingClause?
    ;

groupByItem
    : rollupCubeClause | groupingSetsClause | expr
    ;

rollupCubeClause
    : (ROLLUP | CUBE) LP_ groupingExprList RP_
    ;

groupingSetsClause
    : GROUPING SETS LP_ (rollupCubeClause | groupingExprList) (COMMA_ (rollupCubeClause | groupingExprList))* RP_
    ;

groupingExprList
    : expressionList (COMMA_ expressionList)*
    ;

havingClause
    : HAVING expr
    ;

modelClause
    : MODEL cellReferenceOptions? returnRowsClause? referenceModel* mainModel
    ;

cellReferenceOptions
    : (IGNORE | KEEP) NAV | ((IGNORE | KEEP) NAV)? UNIQUE (DIMENSION | SINGLE REFERENCE)
    ;

returnRowsClause
    : RETURN (UPDATED | ALL) ROWS
    ;

referenceModel
    : REFERENCE referenceModelName ON LP_ selectSubquery RP_ modelColumnClauses cellReferenceOptions?
    ;

mainModel
    : (MAIN mainModelName)? modelColumnClauses cellReferenceOptions? modelRulesClause
    ;

modelColumnClauses
    : (PARTITION BY LP_ expr alias? (COMMA_ expr alias?)* RP_)?
    DIMENSION BY LP_ expr alias? (COMMA_ expr alias?)* RP_ MEASURES LP_ expr alias? (COMMA_ expr alias?)* RP_
    ;

modelRulesClause
    : (RULES (UPDATE | UPSERT ALL?)? ((AUTOMATIC | SEQUENTIAL) ORDER)? modelIterateClause?)?
    LP_ (UPDATE | UPSERT ALL?)? cellAssignment orderByClause? EQ_ modelExpr (COMMA_ (UPDATE | UPSERT ALL?)? cellAssignment orderByClause? EQ_ modelExpr)* RP_
    ;

modelIterateClause
    : ITERATE LP_ numberLiterals RP_ (UNTIL LP_ condition RP_)?
    ;

cellAssignment
    : measureColumn LBT_ (((condition | expr | singleColumnForLoop) (COMMA_ (condition | expr | singleColumnForLoop))*) | multiColumnForLoop) RBT_
    ;

singleColumnForLoop
    : FOR dimensionColumn ((IN LP_ ((literals (COMMA_ literals)*) | selectSubquery) RP_)
    | ((LIKE pattern)? FROM literals TO literals (INCREMENT | DECREMENT) literals))
    ;

multiColumnForLoop
    : FOR LP_ dimensionColumn (COMMA_ dimensionColumn)* RP_ IN LP_ (selectSubquery
    | LP_ literals (COMMA_ literals)* RP_ (COMMA_ LP_ literals (COMMA_ literals)* RP_)*) RP_
    ;

subquery
    : LP_ selectSubquery RP_
    ;

modelExpr
    : (numberLiterals ASTERISK_)? ((measureColumn LBT_ (condition | expr) (COMMA_ (condition | expr))* RBT_)
    | (aggregationFunction LBT_ (((condition | expr) (COMMA_ (condition | expr))*) | (singleColumnForLoop (COMMA_ singleColumnForLoop)*) | multiColumnForLoop) RBT_)
    | analyticFunction) ((PLUS_ | SLASH_) LP_? modelExpr* RP_? | ASTERISK_ numberLiterals (ASTERISK_ modelExpr)?)?
    | expr
    ;

forUpdateClause
    : FOR UPDATE (OF forUpdateClauseList)? ((NOWAIT | WAIT INTEGER_) | SKIP_SYMBOL LOCKED)?
    ;

forUpdateClauseList
    : forUpdateClauseOption (COMMA_ forUpdateClauseOption)*
    ;

forUpdateClauseOption
    : ((tableName | viewName) DOT_)? columnName
    ;

rowLimitingClause
    : rowOffsetClause rowFetchClause?
    | rowFetchClause
    ;

rowOffsetClause
    : OFFSET offset (ROW | ROWS)
    ;

rowFetchClause
    : FETCH (FIRST | NEXT) (rowcount PERCENT?)? (ROW | ROWS) (ONLY | WITH TIES)
    ;

merge
    : MERGE hint? mergeIntoClause usingClause (mergeUpdateClause? mergeInsertClause? | mergeInsertClause? mergeUpdateClause?) errorLoggingClause?
    ;

mergeIntoClause
    : INTO (tableName | viewName | subquery) aliasClause?
    ;

usingClause
    : USING ((tableName | viewName) | subquery) alias? ON LP_ expr RP_
    ;

mergeUpdateClause
    : WHEN MATCHED THEN UPDATE SET mergeSetAssignmentsClause whereClause? deleteWhereClause?
    ;

mergeSetAssignmentsClause
    : mergeAssignment (COMMA_ mergeAssignment)*
    ;

mergeAssignment
    : columnName EQ_ mergeAssignmentValue
    ;

mergeAssignmentValue
    : expr | DEFAULT
    ;

deleteWhereClause
    : DELETE whereClause
    ;

mergeInsertClause
    : WHEN NOT MATCHED THEN INSERT mergeInsertColumn? mergeColumnValue whereClause?
    ;

mergeInsertColumn
    : LP_ columnName (COMMA_ columnName)* RP_
    ;

mergeColumnValue
    : VALUES LP_ (expr | DEFAULT) (COMMA_ (expr | DEFAULT))* RP_
    ;

errorLoggingClause
    : LOG ERRORS (INTO tableName)? (LP_ simpleExpr RP_)? (REJECT LIMIT (INTEGER_ | UNLIMITED))?
    ;

rowPatternClause
    : MATCH_RECOGNIZE LP_ rowPatternPartitionBy? rowPatternOrderBy? rowPatternMeasures?
    rowPatternRowsPerMatch? rowPatternSkipTo? PATTERN LP_ rowPattern RP_
    rowPatternSubsetClause? DEFINE rowPatternDefinitionList RP_
    ;

rowPatternPartitionBy
    : PARTITION BY columnNames
    ;

rowPatternOrderBy
    : ORDER BY columnNames
    ;

rowPatternMeasures
    : MEASURES rowPatternMeasureColumn (COMMA_ rowPatternMeasureColumn)*
    ;

rowPatternMeasureColumn
    : patternMeasExpression AS alias
    ;

rowPatternRowsPerMatch
    : (ONE ROW | ALL ROWS) PER MATCH
    ;

rowPatternSkipTo
    : AFTER MATCH SKIP_SYMBOL ((TO NEXT | PAST LAST) ROW
    | TO (FIRST | LAST)? variableName)
    ;

rowPattern
    : rowPatternTerm
    ;

rowPatternTerm
    : rowPatternFactor
    ;

rowPatternFactor
    : rowPatternPrimary rowPatternQuantifier?
    ;

rowPatternPrimary
    : variableName
    | DOLLAR_
    | CARET_
    | LP_ rowPattern? RP_
    | LBE_ MINUS_ rowPattern MINUS_ RBE_
    | rowPatternPermute
    ;

rowPatternPermute
    : PERMUTE LP_ rowPattern (COMMA_ rowPattern)* RP_
    ;

rowPatternQuantifier
    : ASTERISK_ QUESTION_?
    | PLUS_ QUESTION_?
    | QUESTION_ QUESTION_?
    | (LBE_ INTEGER_? COMMA_ INTEGER_? RBE_ QUESTION_?
    | LBE_ INTEGER_ RBE_)
    ;

rowPatternSubsetClause
    : SUBSET rowPatternSubsetItem (COMMA_ rowPatternSubsetItem)*
    ;

rowPatternSubsetItem
    : variableName EQ_ LP_ variableName (COMMA_ variableName)* RP_
    ;

rowPatternDefinitionList
    : rowPatternDefinition (COMMA_ rowPatternDefinition)*
    ;

rowPatternDefinition
    : variableName AS expr
    ;

rowPatternRecFunc
    : rowPatternClassifierFunc
    | rowPatternMatchNumFunc
    | rowPatternNavigationFunc
    | rowPatternAggregateFunc
    ;

patternMeasExpression
    : stringLiterals
    | numberLiterals
    | columnName
    | rowPatternRecFunc
    ;

rowPatternClassifierFunc
    : CLASSIFIER LP_ RP_
    ;

rowPatternMatchNumFunc
    : MATCH_NUMBER LP_ RP_
    ;

rowPatternNavigationFunc
    : rowPatternNavLogical
    | rowPatternNavPhysical
    | rowPatternNavCompound
    ;

rowPatternNavLogical
    : (RUNNING | FINAL)? (FIRST | LAST) LP_ expr (COMMA_ offset)? RP_
    ;

rowPatternNavPhysical
    : (PREV | NEXT) LP_ expr (COMMA_ offset)? RP_
    ;

rowPatternNavCompound
    : (PREV | NEXT) LP_ (RUNNING | FINAL)? (FIRST | LAST) LP_ expr (COMMA_ offset)? RP_ (COMMA_ offset)? RP_
    ;

rowPatternAggregateFunc
    : (RUNNING | FINAL)? aggregationFunction
    ;

lockTable
    : LOCK TABLE (tableName | viewName) (partitionExtensionClause | dbLinkClause)? (COMMA_ (tableName | viewName) (partitionExtensionClause | dbLinkClause)? )* IN lockmodeClause MODE ( NOWAIT | WAIT INTEGER_)?
    ;

partitionExtensionClause
    : PARTITION LP_ partitionName RP_
    | PARTITION FOR LP_ partitionKeyValue (COMMA_ partitionKeyValue)* RP_
    | SUBPARTITION LP_ subpartitionName RP_
    | SUBPARTITION FOR LP_ subpartitionKeyValue (COMMA_ subpartitionKeyValue)* RP_
    ;

lockmodeClause
    : ROW (SHARE | EXCLUSIVE)
    | SHARE (UPDATE | ROW EXCLUSIVE)?
    | EXCLUSIVE
    ;

createSchema
    : CREATE SCHEMA AUTHORIZATION schemaName (createTable | createView | grant)*
    ;

createOutline
    : CREATE (OR REPLACE)? (PUBLIC | PRIVATE)? OUTLINE outlineName?
    (FROM (PUBLIC | PRIVATE)? outlineName)? (FOR CATEGORY categoryName)? (ON (select | delete | update | insert | createTable))?
    ;

/**
 * DM
 * https://eco.dameng.com/document/dm/zh-cn/pm/view.html#6.2%20%E8%A7%86%E5%9B%BE%E7%9A%84%E5%AE%9A%E4%B9%89
 */
createView
    : CREATE (OR REPLACE)? (NO? FORCE)? (EDITIONING | EDITIONABLE EDITIONING? | NONEDITIONABLE)? VIEW ifNotExists? viewName createSharingClause
    ( LP_ ((alias (VISIBLE | INVISIBLE)? inlineConstraint* | outOfLineConstraint) (COMMA_ (alias (VISIBLE | INVISIBLE)? inlineConstraint* | outOfLineConstraint))*) RP_
    | objectViewClause | xmlTypeViewClause)?
    ( DEFAULT COLLATION collationName)? (BEQUEATH (CURRENT_USER | DEFINER))? AS select subqueryRestrictionClause?
    ( CONTAINER_MAP | CONTAINERS_DEFAULT)?
    ;

createTable
    : CREATE createTableSpecification ifNotExists? TABLE tableName createSharingClause createDefinitionClause memOptimizeClause createParentClause createQueueClause
    ;

createEdition
    : CREATE EDITION editionName (AS CHILD OF editionName)?
    ;

createIndex
    : CREATE createIndexSpecification? INDEX ifNotExists? indexName ON createIndexDefinitionClause usableSpecification? invalidationSpecification?
    ;

// DM https://eco.dameng.com/document/dm/zh-cn/pm/user-defined-operators.html#20.1%20%E5%88%9B%E5%BB%BA%E8%87%AA%E5%AE%9A%E4%B9%89%E8%BF%90%E7%AE%97%E7%AC%A6
createOperator
    : CREATE OPERATOR operatorName bindDefinitionClause
    ;

bindDefinitionClause
    : BINDING bindDefinition (COMMA_ bindDefinition)*
    ;

bindDefinition
    : LP_ dataType (COMMA_ dataType)* RP_ returnDefinition
    ;

returnDefinition
    : RETURN returnTypeDef operatorBindClause? implementsOperator
    ;

implementsOperator
    : USING functionName
    ;

operatorBindClause
    : withIndexContextDef COMMA_ scanContextDef primaryBindingDef?
    ;

primaryBindingDef
    : COMPUTE ANCILLARY DATA
    ;

scanContextDef
    : SCAN CONTEXT identifier
    ;

withIndexContextDef
   : WITH INDEX CONTEXT
   ;

returnTypeDef
    : dataType
    ;

// DM https://eco.dameng.com/document/dm/zh-cn/pm/custom-type.html
// ORACLE https://docs.oracle.com/en/database/oracle/oracle-database/23/lnpls/CREATE-TYPE-statement.html#LNPLS1619
createType
    : CREATE (OR REPLACE)? (EDITIONABLE | NONEDITIONABLE)? TYPE ifNotExists? plsqlTypeSource
    ;

plsqlTypeSource
    : typeName (FORCE?) (OID objectIdentifier=stringLiterals)? sharingClause? defaultCollationClause?
     (invokerRightsClause | accessibleByClause)* (objectBaseTypeDef | objectSubTypeDef)
    ;

objectBaseTypeDef
    : (IS | AS) (objectTypeDef | varrayTypeSpec | nestedTableTypeSpec)
    ;

objectTypeDef
    : OBJECT (LP_ typeDefinitionItem (COMMA_ typeDefinitionItem)* RP_)? (finalClause | instantiableClause | persistableClause)*
    ;

objectViewClause
    : OF typeName (WITH OBJECT (IDENTIFIER | ID) (DEFAULT | LP_ attribute (COMMA_ attribute)* RP_) | UNDER (schemaName DOT_)? superview)
    ( LP_ outOfLineConstraint | attribute inlineConstraint* (COMMA_ outOfLineConstraint | attribute inlineConstraint*)* RP_)?
    ;

finalClause
    : NOT? FINAL
    ;

instantiableClause
    : NOT? INSTANTIABLE
    ;

persistableClause
    : NOT? PERSISTABLE
    ;

varrayTypeSpec
    : (VARYING ARRAY | VARRAY) (LP_ INTEGER_ RP_)? OF typeSpec
    ;

nestedTableTypeSpec
    : TABLE OF typeSpec
    ;

typeSpec
    : ((LP_ dataType RP_) | dataType) (NOT NULL)? persistableClause?
    ;

dataTypeDefinition
    : name dataType
    ;

objectSubTypeDef
    : UNDER typeName (LP_ typeDefinitionItem (COMMA_ typeDefinitionItem)* RP_)? (finalClause | instantiableClause)?
    ;

typeDefinitionItem
    : dataTypeDefinition | elementSpecification
    ;

createTypeBody
    : CREATE (OR REPLACE)? (EDITIONABLE | NONEDITIONABLE)? TYPE BODY plsqlTypeBodySource
    ;

plsqlTypeBodySource
    : typeName sharingClause? (IS | AS) typeBodyDecl (COMMA_? typeBodyDecl)* END
    ;

typeBodyDecl
    : subprogDeclInType | mapOrderFuncDeclaration
    ;

subprogDeclInType
    : procDeclInType | funcDeclInType | constructorDeclaration | elementSpecification
    ;

procDeclInType
    : MEMBER? PROCEDURE name parameterDeclarationList? (IS | AS) (declareSection? body | callSpec)
    ;

funcDeclInType
    : MEMBER? FUNCTION name parameterDeclarationList? RETURN dataType
    (invokerRightsClause | accessibleByClause | deterministicClause | parallelEnableClause | resultCacheClause)*
    PIPELINED? (IS | AS) (callSpec | declareSection? body)
    ;

constructorDeclaration
    : FINAL? INSTANTIABLE? CONSTRUCTOR FUNCTION typeName
    (LP_ (SELF IN OUT dataType COMMA_)? parameterDeclaration (COMMA_ parameterDeclaration)* RP_)?
    RETURN SELF AS RESULT (AS | IS) (callSpec | declareSection? body)
    ;

mapOrderFuncDeclaration
    : (MAP | ORDER) funcDeclInType
    ;

alterTable
    : ALTER TABLE tableName memOptimizeClause alterDefinitionClause enableDisableClauses?
    ;

alterIndex
    : ALTER INDEX indexName alterIndexInformationClause
    ;

alterTrigger
    : ALTER TRIGGER triggerName (
    | triggerCompileClause
    | ( ENABLE | DISABLE)
    | RENAME TO name
    | (EDITIONABLE | NONEDITIONABLE)
    )
    ;

triggerCompileClause
    : COMPILE (
    (DEBUG compilerParametersClause* (REUSE SETTINGS)?)
    | (compilerParametersClause+ (REUSE SETTINGS)?)
    | (REUSE SETTINGS)
    | (DEBUG REUSE SETTINGS)
    )?
    ;

compilerParametersClause
    : parameterName EQ_ parameterValue
    ;

dropContext
    : DROP CONTEXT ifExists? contextName;

dropTable
    : DROP TABLE ifExists? tableName (CASCADE CONSTRAINTS | cascadeOrRestrict)? (PURGE)?
    ;

dropTableSpace
    : DROP TABLESPACE ifExists? tablespaceName (INCLUDING CONTENTS ((AND | KEEP) DATAFILES)? (CASCADE CONSTRAINTS)? )?
    ;

dropPackage
    : DROP PACKAGE BODY? packageName
    ;

dropTrigger
    : DROP TRIGGER ifExists? triggerName
    ;

dropIndex
    : DROP INDEX ifExists? indexName ONLINE? FORCE? invalidationSpecification? (ON tableName)?
    ;

/**
 * DM
 * https://eco.dameng.com/document/dm/zh-cn/pm/view.html#6.3%20%E8%A7%86%E5%9B%BE%E7%9A%84%E5%88%A0%E9%99%A4
 */
dropView
    : DROP VIEW ifExists viewName (CASCADE CONSTRAINTS | cascadeOrRestrict)?
    ;

dropEdition
    : DROP EDITION editionName CASCADE?
    ;

dropOutline
    : DROP OUTLINE outlineName
    ;

dropCluster
    : DROP CLUSTER (schemaName DOT_)? clusterName (INCLUDING TABLES (CASCADE CONSTRAINTS)?)?
    ;

alterOutline
    : ALTER OUTLINE (PUBLIC | PRIVATE)? outlineName
    ( REBUILD
    | RENAME TO outlineName
    | CHANGE CATEGORY TO categoryName
    | (ENABLE | DISABLE)
    )+
    ;

truncateTable
    : TRUNCATE TABLE tableName materializedViewLogClause? dropReuseClause? CASCADE?
    ;

createTableSpecification
    : ((GLOBAL | PRIVATE) TEMPORARY | SHARDED | DUPLICATED | IMMUTABLE? BLOCKCHAIN | IMMUTABLE)?
    ;


tablespaceClause
    : TABLESPACE ignoredIdentifier
    ;

createSharingClause
    : (SHARING EQ_ (METADATA | DATA | EXTENDED DATA | NONE))?
    ;

createDefinitionClause
    : createRelationalTableClause | createObjectTableClause | createXMLTypeTableClause
    ;

createXMLTypeTableClause
    : OF? XMLTYPE
      (LP_ (objectProperties) RP_)?
      (XMLTYPE xmlTypeStorageClause)?
      (xmlSchemaSpecClause)?
      (xmlTypeVirtualColumnsClause)?
      (ON COMMIT (DELETE | PRESERVE) ROWS)?
      (oidClause)?
      (oidIndexClause)?
      (physicalProperties)?
      tableProperties
    ;

xmlTypeStorageClause
    : STORE
      (AS ( OBJECT RELATIONAL | ((SECUREFILE | BASICFILE)? (CLOB | BINARY XML) (lobSegname (LP_ lobParameters RP_)? | (LP_ lobParameters RP_))?)))
      | (ALL VARRAYS AS (LOBS | TABLES ))
    ;

xmlSchemaSpecClause
    : (XMLSCHEMA xmlSchemaURLName)? ELEMENT (elementName | xmlSchemaURLName POUND_ elementName)?
      (STORE ALL VARRAYS AS (LOBS | TABLES))?
      ((ALLOW | DISALLOW) NONSCHEMA)?
      ((ALLOW | DISALLOW) ANYSCHEMA)?
    ;

xmlTypeVirtualColumnsClause
    : VIRTUAL COLUMNS LP_ (columnName AS LP_ expr RP_ (COMMA_ columnName AS LP_ expr RP_)+) RP_
    ;

xmlTypeViewClause
    : OF XMLTYPE xmlSchemaSpec? WITH OBJECT (IDENTIFIER | ID) (DEFAULT | LP_ expr (COMMA_ expr)* RP_)
    ;

xmlSchemaSpec
    : (XMLSCHEMA xmlSchemaURLName)? ELEMENT (elementName | xmlSchemaURLName POUND_ elementName)
    ( STORE ALL VARRAYS AS (LOBS | TABLES))? ((ALLOW | DISALLOW) NONSCHEMA)? ((ALLOW | DISALLOW) ANYSCHEMA)?
    ;

oidClause
    : OBJECT IDENTIFIER IS (SYSTEM GENERATED | PRIMARY KEY)
    ;

oidIndexClause
    : OIDINDEX indexName? LP_ (physicalAttributesClause | TABLESPACE tablespaceName)* RP_
    ;

createRelationalTableClause
    : (LP_ relationalProperties RP_)? immutableTableClauses blockchainTableClauses? collationClause? commitClause parallelClause? physicalProperties? tableProperties
    ;

createParentClause
    : (PARENT tableName)?
    ;

createObjectTableClause
    : OF objectName objectTableSubstitution?
    (LP_ objectProperties RP_)? (ON COMMIT (DELETE | PRESERVE) ROWS)?
    oidClause? oidIndexClause? physicalProperties? tableProperties
    ;

createQueueClause
    : (USAGE QUEUE)?
    ;

relationalProperties
    : relationalProperty (COMMA_ relationalProperty)*
    ;

immutableTableClauses
    : immutableTableNoDropClause? immutableTableNoDeleteClause?
    ;

immutableTableNoDropClause
    : NO DROP (UNTIL INTEGER_ DAYS IDLE)?
    ;

immutableTableNoDeleteClause
    : NO DELETE (LOCKED? | UNTIL INTEGER_ DAYS AFTER INSERT LOCKED?)
    ;

blockchainTableClauses
    : blockchainDropTableClause | blockchainRowRetentionClause | blockchainHashAndDataFormatClause
    ;

blockchainDropTableClause
    : NO DROP (UNTIL INTEGER_ DAYS IDLE)?
    ;

blockchainRowRetentionClause
    : NO DELETE (LOCKED? | UNTIL INTEGER_ DAYS AFTER INSERT LOCKED?)
    ;

blockchainHashAndDataFormatClause
    : HASHING USING SHA2_512_Q VERSION V1_Q
    ;

relationalProperty
    : columnDefinition | virtualColumnDefinition | outOfLineConstraint | outOfLineRefConstraint
    ;

columnDefinition
    : columnName REF? (dataType (COLLATE columnCollationName)?)? SORT? visibleClause (DEFAULT (ON NULL)? expr | identityClause)?
    ( ENCRYPT encryptionSpecification)? (inlineConstraint+ | inlineRefConstraint)?
    | REF LP_ columnName RP_ WITH ROWID
    | SCOPE FOR LP_ columnName RP_ IS identifier
    ;

uniqueSpec
    : UNIQUE | primaryKey
    ;

visibleClause
    : (VISIBLE | INVISIBLE)?
    ;

identityClause
    : GENERATED (ALWAYS | BY DEFAULT (ON NULL)?) AS IDENTITY identifyOptions
    ;

identifyOptions
    : LP_? (identityOption+)? RP_?
    ;

identityOption
    : START WITH (INTEGER_ | LIMIT VALUE)
    | INCREMENT BY INTEGER_
    | MAXVALUE INTEGER_
    | NOMAXVALUE
    | MINVALUE INTEGER_
    | NOMINVALUE
    | CYCLE
    | NOCYCLE
    | CACHE INTEGER_
    | NOCACHE
    | ORDER
    | NOORDER
    ;

encryptionSpecification
    : (USING STRING_)? (IDENTIFIED BY (STRING_ | IDENTIFIER_))? (integrityAlgorithm? (NO? SALT)? | (NO? SALT)? integrityAlgorithm?)
    ;

inlineConstraint
    : (CONSTRAINT ignoredIdentifier)? (NOT? NULL | UNIQUE | primaryKey | referencesClause | CHECK LP_ expr RP_) constraintState
    ;

referencesClause
    : REFERENCES tableName columnNames? (WITH INDEX)? referentialAction*
    ;

referentialAction
    : ON DELETE (CASCADE | SET NULL | RESTRICT)
    | ON UPDATE (CASCADE | SET NULL | RESTRICT)
    ;

constraintState
    : (NOT? DEFERRABLE (INITIALLY (DEFERRED | IMMEDIATE))? | INITIALLY (DEFERRED | IMMEDIATE) (NOT? DEFERRABLE)?)? (RELY | NORELY)? usingIndexClause? (ENABLE | DISABLE)? (VALIDATE | NOVALIDATE)? exceptionsClause?
    ;

exceptionsClause
    : EXCEPTIONS INTO tableName
    ;

usingIndexClause
    : USING INDEX (indexName | createIndexClause | indexProperties)?
    ;

createIndexClause
    :  LP_ createIndex RP_
    ;

inlineRefConstraint
    : SCOPE IS tableName | WITH ROWID | (CONSTRAINT ignoredIdentifier)? referencesClause constraintState
    ;

virtualColumnDefinition
    : columnName dataType? (GENERATED ALWAYS)? AS LP_ expr RP_ VIRTUAL? inlineConstraint*
    ;

outOfLineConstraint
    : (CONSTRAINT constraintName)?
    (uniqueSpec columnNames
    | FOREIGN KEY columnNames referencesClause
    | CHECK LP_ expr RP_
    ) constraintState
    ;

outOfLineRefConstraint
    : SCOPE FOR LP_ lobItem RP_ IS tableName
    | REF LP_ lobItem RP_ WITH ROWID
    | (CONSTRAINT constraintName)? FOREIGN KEY lobItemList referencesClause constraintState
    ;

createIndexSpecification
    : UNIQUE | BITMAP | MULTIVALUE
    ;

clusterIndexClause
    : CLUSTER clusterName indexAttributes*
    ;
// 原为可以选循环，改为单项非循环，使用时后面拼'*'
indexAttributes
    : physicalAttributesClause | loggingClause | ONLINE | TABLESPACE (tablespaceName | DEFAULT) | indexCompression
    | (SORT | NOSORT) | REVERSE | (VISIBLE | INVISIBLE) | partialIndexClause | parallelClause | GLOBAL
    ;

tableIndexClause
    : tableName alias? indexExpressions indexProperties?
    ;

indexExpressions
    : LP_? indexExpression (COMMA_ indexExpression)* RP_?
    ;

indexExpression
    : (columnName | expr) (ASC | DESC)?
    ;

indexProperties
    : (globalPartitionedIndex | localPartitionedIndex | indexAttributes)+ | INDEXTYPE IS (domainIndexClause | xmlIndexClause)
    ;

globalPartitionedIndex
    : GLOBAL PARTITION BY (RANGE LP_ columnOrColumnList RP_ LP_ indexPartitioningClause RP_ | HASH LP_ columnOrColumnList RP_ (individualHashPartitions | hashPartitionsByQuantity))
    ;

indexPartitioningClause
    : PARTITION partitionName? VALUES LESS THAN LP_ literals (COMMA_ literals) RP_ segmentAttributesClause?
    ;

localPartitionedIndex
    : LOCAL (onRangePartitionedTable | onListPartitionedTable | onHashPartitionedTable | onCompPartitionedTable)?
    ;

onRangePartitionedTable
    : LP_ PARTITION partitionName? (segmentAttributesClause | indexCompression)* usableSpecification? (COMMA_ PARTITION partitionName? (segmentAttributesClause | indexCompression)* usableSpecification?)* RP_
    ;

onListPartitionedTable
    : LP_ PARTITION partitionName? (segmentAttributesClause | indexCompression)* usableSpecification? (COMMA_ PARTITION partitionName? (segmentAttributesClause | indexCompression)* usableSpecification?)* RP_
    ;

onHashPartitionedTable
    : (STORE IN LP_ tablespaceName (COMMA_ tablespaceName) RP_ | LP_ PARTITION partitionName? (TABLESPACE tablespaceName)? indexCompression? usableSpecification? RP_)
    ;

onCompPartitionedTable
    : (STORE IN LP_ tablespaceName (COMMA_ tablespaceName) RP_)? LP_ PARTITION partitionName? (segmentAttributesClause | indexCompression)* usableSpecification? indexSubpartitionClause RP_
    ;

domainIndexClause
    : indexTypeName localDomainIndexClause? parallelClause? (PARAMETERS LP_ SQ_ odciParameters SQ_ RP_)?
    ;

localDomainIndexClause
    : LOCAL (LP_ PARTITION partitionName? (PARAMETERS LP_ SQ_  SQ_ odciParameters RP_)? (COMMA_ PARTITION partitionName? (PARAMETERS LP_ SQ_  SQ_ odciParameters RP_)?)* RP_)?
    ;

xmlIndexClause
    : (XDB COMMA_)? XMLINDEX localXmlIndexClause? parallelClause? xmlIndexParametersClause?
    ;

localXmlIndexClause
    : LOCAL (LP_ PARTITION partitionName xmlIndexParametersClause? (COMMA_ PARTITION partitionName xmlIndexParametersClause?)* RP_)?
    ;

// https://docs.oracle.com/en/database/oracle/oracle-database/21/adxdb/indexes-for-XMLType-data.html#GUID-BCC7D521-E6B9-4636-98E1-25628BA0FEFC
xmlIndexParametersClause
    : PARAMETERS LP_ SQ_ (xmlIndexParameters | PARAM identifier) SQ_ RP_
    ;

xmlIndexParameters
    : (xmlIndexParameterClause)* (TABLESPACE identifier)?
    ;

xmlIndexParameterClause
    : unstructuredClause | structuredClause | asyncClause
    ;

unstructuredClause
    : (PATHS (createIndexPathsClause | alterIndexPathsClause) | (pathTableClause | pikeyClause | pathIdClause | orderKeyClause | valueClause | dropPathTableClause) parallelClause?)
    ;

createIndexPathsClause
    : LP_ (INCLUDE LP_ xPathsList RP_ | EXCLUDE LP_ xPathsList RP_) namespaceMappingClause RP_
    ;

alterIndexPathsClause
    : LP_ (INDEX_ALL_PATHS | (INCLUDE | EXCLUDE) (ADD | REMOVE) LP_ xPathsList RP_ namespaceMappingClause?) RP_
    ;

namespaceMappingClause
    : NAMESPACE MAPPING LP_ namespace+ RP_
    ;

pathTableClause
    : PATH TABLE identifier? (LP_ segmentAttributesClause tableProperties RP_)?
    ;

pikeyClause
    : PIKEY (INDEX identifier? (LP_ indexAttributes* RP_)?)?
    ;

pathIdClause
    : PATH ID (INDEX identifier? (LP_ indexAttributes* RP_)?)?
    ;

orderKeyClause
    : ORDER KEY (INDEX identifier? (LP_ indexAttributes* RP_)?)?
    ;

valueClause
    : VALUE (INDEX identifier? (LP_ indexAttributes* RP_)?)?
    ;

dropPathTableClause
    : DROP PATH TABLE
    ;

structuredClause
    : groupsClause | alterIndexGroupClause
    ;

asyncClause
    : ASYNC LP_ SYNC (ALWAYS | MANUAL | EVERY repeatInterval=STRING_ | ON COMMIT) (STALE LP_ (FALSE | TRUE) RP_)? RP_
    ;

groupsClause
    : ((GROUP identifier)? xmlIndexXmltableClause)+
    ;

xmlIndexXmltableClause
    : XMLTABLE identifier (LP_ segmentAttributesClause tableCompression? inmemoryTableClause? tableProperties RP_)?
    ( xmlNamespacesClause COMMA_)? xQueryString=STRING_ (PASSING identifier)? COLUMNS columnClause (COMMA_ columnClause)*
    ;

columnClause
    : columnName (FOR ORDINALITY | dataType PATH STRING_ VIRTUAL?)
    ;

alterIndexGroupClause
    : (NONBLOCKING? ADD_GROUP groupsClause | DROP_GROUP (GROUP identifier (COMMA_ identifier))?
    | NONBLOCKING? ADD_COLUMN addColumnOptions | DROP_COLUMN dropColumnOptions
    | NONBLOCKING ABORT | NONBLOCKING COMPLETE | MODIFY_COLUMN_TYPE modifyColumnTypeOptions)
    ;

addColumnOptions
    : (GROUP identifier)? XMLTABLE identifier (xmlNamespacesClause COMMA_)? COLUMNS columnClause (COMMA_ columnClause)*
    ;

dropColumnOptions
    : (GROUP identifier)? XMLTABLE identifier COLUMNS identifier (COMMA_ identifier)
    ;

modifyColumnTypeOptions
    : (GROUP identifier)? XMLTABLE identifier COLUMNS identifier identifier (COMMA_ identifier identifier)
    ;

bitmapJoinIndexClause
    : tableName columnSortsClause_ FROM tableAlias WHERE expr localPartitionedIndex? indexAttributes*
    ;

columnSortsClause_
    : LP_? columnSortClause_ (COMMA_ columnSortClause_)* RP_?
    ;

columnSortClause_
    : (tableName DOT_)? columnName (ASC | DESC)?
    ;

createIndexDefinitionClause
    : clusterIndexClause | tableIndexClause | bitmapJoinIndexClause
    ;

tableAlias
    : tableName alias? (COMMA_ tableName alias?)*
    ;

alterDefinitionClause
    : (alterTableProperties
    | columnClauses
    | moveTableClause
    | constraintClauses
    | alterTablePartitioning invalidationSpecification?
    | alterExternalTable)?
    ;

alterTableProperties
    : ((physicalAttributesClause
    | loggingClause
    | tableCompression
    | inmemoryTableClause
    | ilmClause
    | supplementalTableLogging
    | allocateExtentClause
    | deallocateUnusedClause
    | (CACHE | NOCACHE)
    | upgradeTableClause
    | recordsPerBlockClause
    | parallelClause
    | rowMovementClause
    | logicalReplicationClause
    | flashbackArchiveClause)+ | renameTableSpecification)? alterIotClauses? alterXMLSchemaClause?
    | shrinkClause
    | READ ONLY
    | READ WRITE
    | REKEY encryptionSpecification
    | DEFAULT COLLATION collationName
    | NO? ROW ARCHIVAL
    | ADD attributeClusteringClause
    | MODIFY CLUSTERING clusteringWhen zonemapClause?
    | DROP CLUSTERING
    ;

renameTableSpecification
    : RENAME TO identifier
    ;

supplementalTableLogging
    : ADD SUPPLEMENTAL LOG (supplementalLogGrpClause | supplementalIdKeyClause) (COMMA_ SUPPLEMENTAL LOG (supplementalLogGrpClause | supplementalIdKeyClause))*
    | DROP SUPPLEMENTAL LOG (supplementalIdKeyClause | GROUP logGroupName) (COMMA_ SUPPLEMENTAL LOG (supplementalIdKeyClause | GROUP logGroupName))*
    ;

dropSynonym
    : DROP PUBLIC? SYNONYM ifExists? synonymName FORCE?
    ;

columnClauses
    : operateColumnClause+ | renameColumnClause | modifyCollectionRetrieval | modifylobStorageClause
    ;

operateColumnClause
    : addColumnSpecification | modifyColumnSpecification | dropColumnClause | (REBUILD COLUMNS)
    ;

addColumnSpecification
    : ADD (LP_ columnOrVirtualDefinitions RP_ | columnOrVirtualDefinitions) columnProperties?
    ;

columnOrVirtualDefinitions
    : columnOrVirtualDefinition (COMMA_ columnOrVirtualDefinition)*
    ;

columnOrVirtualDefinition
    : columnDefinition | virtualColumnDefinition
    ;

columnProperties
    : columnProperty+
    ;

columnProperty
    : objectTypeColProperties
    | xmlTypeColProperties
    | varrayColProperties // (LP_ lobPartitionStorage (',' lobPartitionStorage)* RP_)?
    | lobStorageClause // (LP_ lobPartitionStorage (',' lobPartitionStorage)* RP_)?
    | nestedTableColProperties
//    | jsonStorageClause
    ;

xmlTypeColProperties
    : XMLTYPE COLUMN? columnName xmlTypeStorageClause?
    ;

objectTypeColProperties
    : COLUMN columnName substitutableColumnClause
    ;

substitutableColumnClause
    : ELEMENT? IS OF TYPE? LP_ ONLY? dataTypeName RP_ | NOT? SUBSTITUTABLE AT ALL LEVELS
    ;

modifyColumnSpecification
    : MODIFY (LP_? modifyColProperties (COMMA_ modifyColProperties)* RP_? | modifyColSubstitutable)
    ;

modifyColProperties
    : columnName dataType? (DEFAULT expr)? (ENCRYPT encryptionSpecification | DECRYPT)? inlineConstraint*
    ;

modifyColSubstitutable
    : COLUMN columnName NOT? SUBSTITUTABLE AT ALL LEVELS FORCE?
    ;

dropColumnClause
    : SET UNUSED columnOrColumnList cascadeOrInvalidate* | dropColumnSpecification
    ;

dropColumnSpecification
    : DROP columnOrColumnList ifExists? cascadeOrInvalidate* checkpointNumber?
    ;

columnOrColumnList
    : (COLUMN columnName) | columnNames
    ;

cascadeOrInvalidate
    : CASCADE CONSTRAINTS | INVALIDATE | cascadeOrRestrict
    ;

checkpointNumber
    : CHECKPOINT numberValue
    ;

renameColumnClause
    : RENAME COLUMN columnName TO columnName
    ;

modifyCollectionRetrieval
    : MODIFY NESTED TABLE tableName RETURN AS (LOCATOR | VALUE)
    ;

moveTableClause
    : MOVE filterCondition? ONLINE? segmentAttributesClause? tableCompression? indexOrgTableClause (lobStorageClause | varrayColProperties)* parallelClause? allowDisallowClustering?
    ( UPDATE INDEXES (LP_ indexName (segmentAttributesClause | updateIndexPartition) RP_ (COMMA_ indexName (segmentAttributesClause | updateIndexPartition))*)?)?
    ;

constraintClauses
    : addConstraintSpecification | modifyConstraintClause | renameConstraintClause | dropConstraintClause+
    ;

addConstraintSpecification
    : ADD (LP_ outOfLineConstraint (COMMA_ outOfLineConstraint)* RP_ | outOfLineConstraint* | outOfLineRefConstraint)
    ;

modifyConstraintClause
    : MODIFY constraintOption constraintState cascadeOrRestrict?
    ;

constraintWithName
    : CONSTRAINT constraintName
    ;

constraintOption
    : constraintWithName | constraintPrimaryOrUnique
    ;

constraintPrimaryOrUnique
    : primaryKey | UNIQUE columnNames
    ;

renameConstraintClause
    : RENAME constraintWithName TO ignoredIdentifier
    ;

dropConstraintClause
    : DROP
    (
    constraintPrimaryOrUnique cascadeOrRestrict? ((KEEP | DROP) INDEX)? | (CONSTRAINT constraintName cascadeOrRestrict?)
    )
    ;

cascadeOrRestrict
    : CASCADE
    ;

alterExternalTable
    : (addColumnSpecification | modifyColumnSpecification | dropColumnSpecification)+
    ;

objectProperties
    : ((columnName | attributeName) (DEFAULT expr))
    | ((columnName | attributeName) (inlineConstraint* | inlineRefConstraint))
    | ((columnName | attributeName) (DEFAULT expr) (inlineConstraint* | inlineRefConstraint))
    | outOfLineConstraint
    | outOfLineRefConstraint
    | supplementalLoggingProps
    ;

alterIndexInformationClause
    : (deallocateUnusedClause
    | allocateExtentClause
    | shrinkClause
    | parallelClause
    | physicalAttributesClause
    | loggingClause
    | partialIndexClause)+
    | rebuildClause invalidationSpecification?
    | parallelClause
    | PARAMETERS LP_ odciParameters RP_
    | COMPILE
    | (ENABLE | DISABLE)
    | UNUSABLE ONLINE? invalidationSpecification?
    | (VISIBLE | INVISIBLE)
    | renameIndexClause
    | COALESCE CLEANUP? ONLY? parallelClause?
    | ((MONITORING | NOMONITORING) USAGE)
    | UPDATE BLOCK REFERENCES
    | alterIndexPartitioning
    ;

renameIndexClause
    : (RENAME TO indexName)?
    ;

alterIndexPartitioning
    : modifyIndexDefaultAttrs
    | addHashIndexPartition
    | modifyIndexPartition
    | renameIndexPartition
    | dropIndexPartition
    | splitIndexPartition
    | coalesceIndexPartition
    | modifyIndexsubPartition
    ;

modifyIndexDefaultAttrs
    : MODIFY DEFAULT ATTRIBUTES (FOR PARTITION partitionName)? (physicalAttributesClause | TABLESPACE (tablespaceName | DEFAULT) | loggingClause)+
    ;

addHashIndexPartition
    : ADD PARTITION partitionName? (TABLESPACE tablespaceName)? indexCompression? parallelClause?
    ;

modifyIndexPartition
    : MODIFY PARTITION partitionName
    ( (deallocateUnusedClause | allocateExtentClause | physicalAttributesClause | loggingClause | indexCompression)+
    | PARAMETERS LP_ odciParameters RP_
    | COALESCE (CLEANUP | ONLY | parallelClause)?
    | UPDATE BLOCK REFERENCES
    | UNUSABLE
    )
    ;

renameIndexPartition
    : RENAME (PARTITION partitionName | SUBPARTITION subpartitionName) TO newName
    ;

dropIndexPartition
    : DROP PARTITION partitionName
    ;

splitIndexPartition
    : SPLIT PARTITION partitionName AT LP_ literals (COMMA_ literals)* RP_ (INTO LP_ indexPartitionDescription COMMA_ indexPartitionDescription RP_)? (parallelClause)?
    ;

indexPartitionDescription
    : PARTITION (partitionName ((segmentAttributesClause | indexCompression)+ | PARAMETERS LP_ odciParameters RP_)? usableSpecification?)?
    ;

coalesceIndexPartition
    : COALESCE PARTITION parallelClause?
    ;

modifyIndexsubPartition
    : MODIFY SUBPARTITION subpartitionName (UNUSABLE | allocateExtentClause | deallocateUnusedClause)
    ;

objectTableSubstitution
    : NOT? SUBSTITUTABLE AT ALL LEVELS
    ;

memOptimizeClause
    : memOptimizeReadClause? memOptimizeWriteClause?
    ;

memOptimizeReadClause
    : (MEMOPTIMIZE FOR READ | NO MEMOPTIMIZE FOR READ)
    ;

memOptimizeWriteClause
    : (MEMOPTIMIZE FOR WRITE | NO MEMOPTIMIZE FOR WRITE)
    ;

enableDisableClauses
    : (enableDisableClause | enableDisableOthers)+
    ;

enableDisableClause
    : (ENABLE | DISABLE) (VALIDATE | NOVALIDATE)? ((UNIQUE columnName (COMMA_ columnName)*) | PRIMARY KEY | constraintWithName) usingIndexClause? exceptionsClause? CASCADE? ((KEEP | DROP) INDEX)?
    ;

enableDisableOthers
    : (ENABLE | DISABLE) (TABLE LOCK | ALL TRIGGERS | CONTAINER_MAP | CONTAINERS_DEFAULT)
    ;

rebuildClause
    : (
        REBUILD (PARTITION partitionName | SUBPARTITION subpartitionName | REVERSE | NOREVERSE | NOSORT)?
    ) ( parallelClause
    | TABLESPACE tablespaceName
    | PARAMETERS LP_ odciParameters RP_
    | ONLINE
    | physicalAttributesClause
    | indexCompression
    | loggingClause
    | partialIndexClause)*
    ;

parallelClause
    : NOPARALLEL | PARALLEL (INTEGER_ | LP_ DEGREE INTEGER_ (INSTANCES INTEGER_)? RP_)?
    ;

usableSpecification
    : (USABLE | UNUSABLE)
    ;

invalidationSpecification
    : (DEFERRED | IMMEDIATE) INVALIDATION
    ;

materializedViewLogClause
    : (PRESERVE | PURGE) MATERIALIZED VIEW LOG
    ;

dropReuseClause
    : (DROP (ALL)? | REUSE) STORAGE
    ;

collationClause
    : DEFAULT COLLATION collationName
    ;

// DM https://eco.dameng.com/document/dm/zh-cn/pm/synonym.html
createSynonym
    : CREATE (OR REPLACE)? (EDITIONABLE | NONEDITIONABLE)? (PUBLIC)? SYNONYM ifNotExists? synonymName (SHARING EQ_ (METADATA | NONE))? FOR objectName dbLinkClause?
    ;

commitClause
    : (ON COMMIT (DROP | PRESERVE) DEFINITION)? (ON COMMIT (DELETE | PRESERVE) ROWS)?
    ;

physicalProperties
    : (deferredSegmentCreation? segmentAttributesClause tableCompression? inmemoryTableClause? ilmClause?
    | deferredSegmentCreation? (organizationClause | externalPartitionClause) | CLUSTER clusterName columnNames)
    ;

deferredSegmentCreation
    : SEGMENT CREATION (IMMEDIATE|DEFERRED)
    ;

segmentAttributesClause
    : segmentAttributesClauseItem+
    ;
segmentAttributesClauseItem
    : physicalAttributesClause
    | TABLESPACE SET? tablespaceName
    | loggingClause
    | tableCompression // fix: #27336
    ;
// * => +
physicalAttributesClause
    : physicalAttributesClauseItem+
    ;
physicalAttributesClauseItem
    : PCTFREE INTEGER_ | PCTUSED INTEGER_ | INITRANS INTEGER_ | MAXTRANS INTEGER_ | COMPUTE STATISTICS | storageClause
    ;
loggingClause
    : LOGGING | NOLOGGING |  FILESYSTEM_LIKE_LOGGING
    ;

partialIndexClause
    : INDEXING (PARTIAL | FULL)
    ;

storageClause
    : STORAGE LP_
    ((INITIAL sizeClause
    | NEXT sizeClause
    | MINEXTENTS INTEGER_
    | MAXEXTENTS (INTEGER_ | UNLIMITED)
    | maxsizeClause
    | PCTINCREASE INTEGER_
    | FREELISTS INTEGER_
    | FREELIST GROUPS INTEGER_
    | OPTIMAL (sizeClause | NULL)?
    | BUFFER_POOL (KEEP | RECYCLE | DEFAULT)
    | FLASH_CACHE (KEEP | NONE | DEFAULT)
    | CELL_FLASH_CACHE (KEEP | NONE | DEFAULT)
    | ENCRYPT
    ))+ RP_
    ;

tableCompression
    : COMPRESS
    | ROW STORE COMPRESS (BASIC | ADVANCED)?
    | COLUMN STORE COMPRESS (FOR (QUERY | ARCHIVE) (LOW | HIGH)?)? (NO? ROW LEVEL LOCKING)?
    | COMPRESS FOR OLTP
    | COMPRESS FOR ARCHIVE (LOW | HIGH)
    | NOCOMPRESS
    ;

inmemoryTableClause
    : ((INMEMORY inmemoryAttributes) | NO INMEMORY)
    | (inmemoryColumnClause)
    | ((INMEMORY inmemoryAttributes) | NO INMEMORY) (inmemoryColumnClause)
    ;

inmemoryAttributes
    : inmemoryMemcompress? inmemoryPriority? inmemoryDistribute? inmemoryDuplicate?
    ;

inmemoryColumnClause
    : (INMEMORY inmemoryMemcompress? | NO INMEMORY) columnNames
    ;

inmemoryMemcompress
    : MEMCOMPRESS FOR ( DML | (QUERY | CAPACITY) (LOW | HIGH)? ) | NO MEMCOMPRESS
    ;

inmemoryPriority
    : PRIORITY (NONE | LOW | MEDIUM | HIGH | CRITICAL)
    ;

inmemoryDistribute
    : DISTRIBUTE (AUTO | BY (ROWID RANGE | PARTITION | SUBPARTITION))? (FOR SERVICE (DEFAULT | ALL | serviceName | NONE))?
    ;

inmemoryDuplicate
    : DUPLICATE | DUPLICATE ALL | NO DUPLICATE
    ;

ilmClause
    : ILM (ADD POLICY ilmPolicyClause
    | (DELETE | ENABLE | DISABLE) POLICY ilmPolicyName
    | (DELETE_ALL | ENABLE_ALL | DISABLE_ALL))
    ;

ilmPolicyClause
    : ilmCompressionPolicy | ilmTieringPolicy | ilmInmemoryPolicy
    ;

ilmCompressionPolicy
    : tableCompression (SEGMENT | GROUP) ( AFTER ilmTimePeriod OF ( NO ACCESS | NO MODIFICATION | CREATION ) | ON functionName)
    | (ROW STORE COMPRESS ADVANCED | COLUMN STORE COMPRESS FOR QUERY) ROW AFTER ilmTimePeriod OF NO MODIFICATION
    ;

ilmTimePeriod
    : numberValue ((DAY | DAYS) | (MONTH | MONTHS) | (YEAR | YEARS))
    ;

ilmTieringPolicy
    : TIER TO tablespaceName (SEGMENT | GROUP)? (ON functionName)?
    | TIER TO tablespaceName READ ONLY (SEGMENT | GROUP)? (AFTER ilmTimePeriod OF (NO ACCESS | NO MODIFICATION | CREATION) | ON functionName)
    ;

ilmInmemoryPolicy
    : (SET INMEMORY inmemoryAttributes | MODIFY INMEMORY inmemoryMemcompress | NO INMEMORY) SEGMENT (AFTER ilmTimePeriod OF (NO ACCESS | NO MODIFICATION | CREATION) | ON functionName)
    ;

organizationClause
    : ORGANIZATION
    ( HEAP segmentAttributesClause? heapOrgTableClause
    | INDEX segmentAttributesClause? indexOrgTableClause
    | EXTERNAL externalTableClause)
    ;

heapOrgTableClause
    : tableCompression? inmemoryTableClause? ilmClause?
    ;

indexOrgTableClause
    : (mappingTableClause | PCTTHRESHOLD INTEGER_ | prefixCompression)* indexOrgOverflowClause?
    ;

externalTableClause
    : LP_ (TYPE accessDriverType)? externalTableDataProps RP_ (REJECT LIMIT (numberValue | UNLIMITED))? inmemoryTableClause?
    ;

externalTableDataProps
    : (DEFAULT DIRECTORY directoryName)? (ACCESS PARAMETERS ((opaqueFormatSpec delimSpec)? | USING CLOB subquery))? (LOCATION LP_ (directoryName | (directoryName COLON_)? locationSpecifier (COMMA_ (directoryName COLON_)? locationSpecifier)+) RP_)?
    ;

mappingTableClause
    : MAPPING TABLE | NOMAPPING
    ;

prefixCompression
    : COMPRESS numberValue? | NOCOMPRESS
    ;

indexOrgOverflowClause
    :  (INCLUDING columnName)? OVERFLOW segmentAttributesClause?
    ;

externalPartitionClause
    : EXTERNAL PARTITION ATTRIBUTES externalTableClause (REJECT LIMIT)?
    ;

tableProperties
    : columnProperties? readOnlyClause? indexingClause? tablePartitioningClauses? attributeClusteringClause?
     (CACHE | NOCACHE)? parallelClause? (RESULT_CACHE (LP_ MODE (DEFAULT | FORCE) RP_))?
     (ROWDEPENDENCIES | NOROWDEPENDENCIES)? enableDisableClause* rowMovementClause? logicalReplicationClause?
     flashbackArchiveClause? (ROW ARCHIVAL)? (AS selectSubquery | FOR EXCHANGE WITH TABLE tableName)?
    ;

readOnlyClause
    : READ ONLY | READ WRITE
    ;

indexingClause
    : INDEXING (ON | OFF)
    ;

tablePartitioningClauses
    : rangePartitions
    | listPartitions
    | hashPartitions
    | compositeRangePartitions
    | compositeListPartitions
    | compositeHashPartitions
    | referencePartitioning
    | systemPartitioning
    | consistentHashPartitions
    | consistentHashWithSubpartitions
    | partitionsetClauses
    ;

rangePartitions
    : PARTITION BY RANGE columnNames
      (INTERVAL LP_ expr RP_ (STORE IN LP_ tablespaceName (COMMA_ tablespaceName)* RP_)?)?
      LP_ PARTITION partitionName? rangeValuesClause tablePartitionDescription (COMMA_ PARTITION partitionName? rangeValuesClause tablePartitionDescription externalPartSubpartDataProps?)* RP_
    ;

rangeValuesClause
    : VALUES LESS THAN LP_? (literals | MAXVALUE | toDateFunction) (COMMA_ (literals | MAXVALUE | toDateFunction))* RP_?
    ;

tablePartitionDescription
    : (INTERNAL | EXTERNAL)?
      deferredSegmentCreation?
      readOnlyClause?
      indexingClause?
      segmentAttributesClause?
      (tableCompression | prefixCompression)?
      inmemoryClause?
      ilmClause?
      (OVERFLOW segmentAttributesClause?)?
      (lobStorageClause | varrayColProperties | nestedTableColProperties)*
    ;

inmemoryClause
    : INMEMORY inmemoryAttributes
    | INMEMORY
    | NO INMEMORY
    ;

varrayColProperties
    : VARRAY varrayItem (substitutableColumnClause? varrayStorageClause | substitutableColumnClause)
    ;

nestedTableColProperties
    : NESTED TABLE (nestedItem | COLUMN_VALUE) substitutableColumnClause? (LOCAL | GLOBAL)? STORE AS storageTable
    ( LP_ (LP_ objectProperties RP_ | physicalProperties | columnProperties)+ RP_)? (RETURN AS? (LOCATOR | VALUE))?
    ;

lobStorageClause
    : LOB
    ( LP_ lobItem (COMMA_ lobItem)* RP_ STORE AS ((SECUREFILE | BASICFILE) | LP_ lobStorageParameters RP_)+
    | LP_ lobItem RP_ STORE AS ((SECUREFILE | BASICFILE) | lobSegname | LP_ lobStorageParameters RP_)+
    )
    ;

varrayStorageClause
    : STORE AS (SECUREFILE | BASICFILE)? LOB (lobSegname? LP_ lobStorageParameters RP_ | lobSegname)
    ;

lobStorageParameters
    : ((TABLESPACE tablespaceName | TABLESPACE SET tablespaceSetName) | lobParameters storageClause?)+ | storageClause
    ;

lobParameters
    : ( (ENABLE | DISABLE) STORAGE IN ROW
        | CHUNK numberValue
        | PCTVERSION numberValue
        | FREEPOOLS numberValue
        | lobRetentionClause
        | lobDeduplicateClause
        | lobCompressionClause
        | (ENCRYPT encryptionSpecification | DECRYPT)
        | (CACHE | NOCACHE | CACHE READS) loggingClause?
      )+
    ;

lobRetentionClause
    : RETENTION (MAX | MIN numberValue | AUTO | NONE)?
    ;

lobDeduplicateClause
    : DEDUPLICATE | KEEP_DUPLICATES
    ;

lobCompressionClause
    : (COMPRESS (HIGH | MEDIUM | LOW)? | NOCOMPRESS)
    ;

externalPartSubpartDataProps
    : (DEFAULT DIRECTORY directoryName) (LOCATION LP_ (directoryName COLON_)? locationSpecifier (COMMA_ (directoryName COLON_)? locationSpecifier)* RP_)?
    ;

listPartitions
    : PARTITION BY LIST columnNames
      (AUTOMATIC (STORE IN LP_? tablespaceName (COMMA_ tablespaceName)* RP_?))?
      LP_ PARTITION partitionName? listValuesClause tablePartitionDescription (COMMA_ PARTITION partitionName? listValuesClause tablePartitionDescription externalPartSubpartDataProps?)* RP_
    ;

listValuesClause
    : VALUES LP_ (listValues | DEFAULT) RP_
    ;

listValues
    : (literals | NULL) (COMMA_ (literals | NULL))*
    | (LP_? ( (literals | NULL) (COMMA_ (literals | NULL))* ) RP_?) (COMMA_ LP_? ( (literals | NULL) (COMMA_ (literals | NULL))* ) RP_?)*
    ;

hashPartitions
    : PARTITION BY HASH columnNames (individualHashPartitions | hashPartitionsByQuantity)
    ;

hashPartitionsByQuantity
    : PARTITIONS INTEGER_ (STORE IN (tablespaceName (COMMA_ tablespaceName)*))? (tableCompression | indexCompression)? (OVERFLOW STORE IN (tablespaceName (COMMA_ tablespaceName)*))?
    ;

indexCompression
    : prefixCompression | advancedIndexCompression
    ;

advancedIndexCompression
    : COMPRESS ADVANCED (LOW | HIGH)? | NOCOMPRESS
    ;

individualHashPartitions
    : LP_? (PARTITION partitionName? readOnlyClause? indexingClause? partitioningStorageClause*) (COMMA_ PARTITION partitionName? readOnlyClause? indexingClause? partitioningStorageClause*)* RP_?
    ;

partitioningStorageClause
    : (TABLESPACE tablespaceName | TABLESPACE SET tablespaceSetName)
    | OVERFLOW (TABLESPACE tablespaceName | TABLESPACE SET tablespaceSetName)?
    | tableCompression
    | indexCompression
    | inmemoryClause
    | ilmClause
    | lobPartitioningStorage
    | VARRAY varrayItem STORE AS (SECUREFILE | BASICFILE)? LOB lobSegname
    ;

lobPartitioningStorage
    :LOB LP_ lobItem RP_ STORE AS (BASICFILE | SECUREFILE)?
    (lobSegname (LP_ TABLESPACE tablespaceName | TABLESPACE SET tablespaceSetName RP_)?
    | LP_ TABLESPACE tablespaceName | TABLESPACE SET tablespaceSetName RP_
    )?
    ;

compositeRangePartitions
    : PARTITION BY RANGE columnNames
      (INTERVAL LP_ expr RP_ (STORE IN LP_? tablespaceName (COMMA_ tablespaceName)* RP_?)?)?
      (subpartitionByRange | subpartitionByList | subpartitionByHash)
      LP_? rangePartitionDesc (COMMA_ rangePartitionDesc)* RP_?
    ;

subpartitionByRange
    : SUBPARTITION BY RANGE columnNames subpartitionTemplate?
    ;

subpartitionByList
    : SUBPARTITION BY LIST columnNames subpartitionTemplate?
    ;

subpartitionByHash
    : SUBPARTITION BY HASH columnNames (SUBPARTITIONS INTEGER_ (STORE IN LP_ tablespaceName (COMMA_ tablespaceName)? RP_)? | subpartitionTemplate)?
    ;

subpartitionTemplate
    : SUBPARTITION TEMPLATE (LP_ (rangeSubpartitionDesc (COMMA_ rangeSubpartitionDesc)*
    | listSubpartitionDesc (COMMA_ listSubpartitionDesc)* | individualHashSubparts (COMMA_ individualHashSubparts)*) RP_) | hashSubpartitionQuantity
    ;

rangeSubpartitionDesc
    : SUBPARTITION subpartitionName? rangeValuesClause readOnlyClause? indexingClause? partitioningStorageClause* externalPartSubpartDataProps?
    ;

listSubpartitionDesc
    : SUBPARTITION subpartitionName? listValuesClause readOnlyClause? indexingClause? partitioningStorageClause* externalPartSubpartDataProps?
    ;

individualHashSubparts
    : SUBPARTITION subpartitionName? readOnlyClause? indexingClause? partitioningStorageClause*
    ;

hashSubpartitionQuantity
    : SUBPARTITIONS INTEGER_ (STORE IN LP_ tablespaceName (COMMA_ tablespaceName)* RP_)?
    ;

rangePartitionDesc
    : PARTITION partitionName? rangeValuesClause tablePartitionDescription (LP_ (rangeSubpartitionDesc (COMMA_ rangeSubpartitionDesc)*
    | listSubpartitionDesc (COMMA_ listSubpartitionDesc)* | individualHashSubparts (COMMA_ individualHashSubparts)*) RP_ | hashSubpartitionQuantity)?
    ;

compositeListPartitions
    : PARTITION BY LIST columnNames
      (AUTOMATIC (STORE IN LP_? tablespaceName (COMMA_ tablespaceName)* RP_?)?)?
      (subpartitionByRange | subpartitionByList | subpartitionByHash)
      LP_? listPartitionDesc (COMMA_ listPartitionDesc)* RP_?
    ;

listPartitionDesc
    : PARTITION partitionName? listValuesClause tablePartitionDescription (LP_ (rangeSubpartitionDesc (COMMA_ rangeSubpartitionDesc)*
    | listSubpartitionDesc (COMMA_ listSubpartitionDesc)* | individualHashSubparts (COMMA_ individualHashSubparts)*) RP_ | hashSubpartsByQuantity)?
    ;

compositeHashPartitions
    : PARTITION BY HASH columnNames (subpartitionByRange | subpartitionByList | subpartitionByHash) (individualHashPartitions | hashPartitionsByQuantity)
    ;

referencePartitioning
    :PARTITION BY REFERENCE LP_ constraint RP_ (LP_? referencePartitionDesc (COMMA_ referencePartitionDesc)* RP_?)?
    ;

referencePartitionDesc
    : PARTITION
    | PARTITION partitionName
    | PARTITION tablePartitionDescription
    | PARTITION partitionName tablePartitionDescription
    ;

constraint
    : inlineConstraint | outOfLineConstraint | inlineRefConstraint | outOfLineRefConstraint
    ;

systemPartitioning
    : PARTITION BY SYSTEM (PARTITIONS numberValue | referencePartitionDesc (COMMA_ referencePartitionDesc)*)?
    | PARTITION BY SYSTEM
    ;

consistentHashPartitions
    : PARTITION BY CONSISTENT HASH columnNames (PARTITIONS AUTO)? TABLESPACE SET tablespaceSetName
    ;

consistentHashWithSubpartitions
    : PARTITION BY CONSISTENT HASH columnNames (subpartitionByRange | subpartitionByList | subpartitionByHash)  (PARTITIONS AUTO)?
    ;

partitionsetClauses
    : rangePartitionsetClause | listPartitionsetClause
    ;

rangePartitionsetClause
    : PARTITIONSET BY RANGE columnNames PARTITION BY CONSISTENT HASH columnNames
      (SUBPARTITION BY ((RANGE | HASH) columnNames | LIST LP_ columnName LP_) subpartitionTemplate?)?
      PARTITIONS AUTO LP_ rangePartitionsetDesc (COMMA_ rangePartitionsetDesc)* RP_
    ;

rangePartitionsetDesc
    : PARTITIONSET partitionSetName rangeValuesClause (TABLESPACE SET tablespaceSetName)? (lobStorageClause)? (SUBPARTITIONS STORE IN tablespaceSetName?)?
    ;

listPartitionsetClause
    : PARTITIONSET BY RANGE LP_ columnName RP_ PARTITION BY CONSISTENT HASH columnNames
      (SUBPARTITION BY ((RANGE | HASH) columnNames | LIST LP_ columnName LP_) subpartitionTemplate?)?
      PARTITIONS AUTO LP_ rangePartitionsetDesc (COMMA_ rangePartitionsetDesc)* RP_
    ;

attributeClusteringClause
    : CLUSTERING clusteringJoin? clusterClause clusteringWhen zonemapClause?
    ;

clusteringJoin
    : tableName (JOIN tableName ON LP_ expr RP_)+
    ;

clusterClause
    : BY (LINEAR | INTERLEAVED)? ORDER clusteringColumns
    ;

createDirectory
    : CREATE (OR REPLACE)? DIRECTORY directoryName (SHARING EQ_ (METADATA | NONE))? AS pathString
    ;

clusteringColumns
    : LP_? clusteringColumnGroup (COMMA_ clusteringColumnGroup)* RP_?
    ;

clusteringColumnGroup
    : columnNames
    ;

clusteringWhen
    : ((YES | NO) ON LOAD)? ((YES | NO) ON DATA MOVEMENT)?
    ;

zonemapClause
    : (WITH MATERIALIZED ZONEMAP (LP_ zonemapName RP_)?) | (WITHOUT MATERIALIZED ZONEMAP)
    ;

rowMovementClause
    : (ENABLE | DISABLE) ROW MOVEMENT
    ;

logicalReplicationClause
    : (ENABLE | DISABLE) LOGICAL REPLICATION
    ;

flashbackArchiveClause
    : FLASHBACK ARCHIVE flashbackArchiveName? | NO FLASHBACK ARCHIVE
    ;

alterPackage
    : ALTER PACKAGE packageName (
    | packageCompileClause
    | (EDITIONABLE | NONEDITIONABLE)
    )
    ;

alterProfile
    : ALTER PROFILE profileName LIMIT (resourceParameters | passwordParameters)+ (CONTAINER EQ_ (CURRENT | ALL))?
    ;

resourceParameters
    : (SESSIONS_PER_USER
    | CPU_PER_SESSION
    | CPU_PER_CALL
    | CONNECT_TIME
    | IDLE_TIME
    | LOGICAL_READS_PER_SESSION
    | LOGICAL_READS_PER_CALL
    | COMPOSITE_LIMIT
    ) (INTEGER_ | UNLIMITED | DEFAULT)
    | PRIVATE_SGA (sizeClause | UNLIMITED | DEFAULT)
    ;

passwordParameters
    : (FAILED_LOGIN_ATTEMPTS
    | PASSWORD_LIFE_TIME
    | PASSWORD_REUSE_TIME
    | PASSWORD_REUSE_MAX
    | PASSWORD_LOCK_TIME
    | PASSWORD_GRACE_TIME
    | INACTIVE_ACCOUNT_TIME
    ) (expr | UNLIMITED | DEFAULT)
    | PASSWORD_VERIFY_FUNCTION (functionName | NULL | DEFAULT)
    | PASSWORD_ROLLOVER_TIME (expr | DEFAULT)
    ;

alterRollbackSegment
    : ALTER ROLLBACK SEGMENT rollbackSegmentName (ONLINE | OFFLINE | storageClause | SHRINK (TO sizeClause)?)
    ;

packageCompileClause
    : COMPILE DEBUG? (PACKAGE | SPECIFICATION | BODY)? compilerParametersClause* (REUSE SETTINGS)?
    ;

alterSynonym
    : ALTER PUBLIC? SYNONYM synonymName (COMPILE | EDITIONABLE | NONEDITIONABLE)
    ;

alterTablePartitioning
    : modifyTableDefaultAttrs
    | setSubpartitionTemplate
    | modifyTablePartition
    | modifyTableSubpartition
    | moveTablePartition
    | moveTableSubPartition
    | addTablePartition
    | coalesceTablePartition
    | dropTablePartition
    | renamePartitionSubpart
    | alterIntervalPartitioning
    ;

modifyTableDefaultAttrs
    : MODIFY DEFAULT ATTRIBUTES (FOR partitionExtendedName)? (DEFAULT DIRECTORY directoryName)? deferredSegmentCreation? readOnlyClause? indexingClause? segmentAttributesClause? alterOverflowClause?
    ( ((LOB LP_ lobItem RP_ | VARRAY varrayType) LP_ lobParameters RP_)+)?
    ;

setSubpartitionTemplate
    : SET SUBPARTITION TEMPLATE (LP_ (rangeSubpartitionDesc (COMMA_ rangeSubpartitionDesc)* | listSubpartitionDesc (COMMA_ listSubpartitionDesc)* | individualHashSubparts (COMMA_ individualHashSubparts)*)? RP_ | hashSubpartitionQuantity)
    ;

modifyTablePartition
    : modifyRangePartition
    | modifyHashPartition
    | modifyListPartition
    ;

modifyRangePartition
    : MODIFY partitionExtendedName (partitionAttributes
    | (addRangeSubpartition | addHashSubpartition | addListSubpartition)
    | coalesceTableSubpartition | alterMappingTableClauses | REBUILD? UNUSABLE LOCAL INDEXES
    | readOnlyClause | indexingClause)
    ;

modifyHashPartition
    : MODIFY partitionExtendedName (partitionAttributes | coalesceTableSubpartition
    | alterMappingTableClauses | REBUILD? UNUSABLE LOCAL INDEXES | readOnlyClause | indexingClause)
    ;

modifyListPartition
    : MODIFY partitionExtendedName (partitionAttributes
    | (ADD | DROP) VALUES LP_ listValues RP_
    | (addRangeSubpartition | addHashSubpartition | addListSubpartition)
    | coalesceTableSubpartition | REBUILD? UNUSABLE LOCAL INDEXES | readOnlyClause | indexingClause)
    ;

modifyTableSubpartition
    : MODIFY subpartitionExtendedName (allocateExtentClause
    | deallocateUnusedClause | shrinkClause | ((LOB lobItem | VARRAY varrayType) LP_ modifylobParameters RP_)+ | REBUILD? UNUSABLE LOCAL INDEXES
    | (ADD | DROP) VALUES LP_ listValues RP_ | readOnlyClause | indexingClause)
    ;

subpartitionExtendedName
    : SUBPARTITION (subpartitionName | FOR LP_ subpartitionKeyValue (COMMA_ subpartitionKeyValue)* RP_)
    ;

partitionExtendedName
    : PARTITION partitionName
    | PARTITION FOR LP_ partitionKeyValue (COMMA_ partitionKeyValue)* RP_
    ;

addRangeSubpartition
    : ADD rangeSubpartitionDesc (COMMA_ rangeSubpartitionDesc)* dependentTablesClause? updateIndexClauses?
    ;

dependentTablesClause
    : DEPENDENT TABLES LP_ tableName LP_ partitionSpec (COMMA_ partitionSpec)* RP_
    (COMMA_ tableName LP_ partitionSpec (COMMA_ partitionSpec)* RP_)* RP_
    ;

addHashSubpartition
    : ADD individualHashSubparts dependentTablesClause? updateIndexClauses? parallelClause?
    ;

addListSubpartition
    : ADD listSubpartitionDesc (COMMA_ listSubpartitionDesc)* dependentTablesClause? updateIndexClauses?
    ;

coalesceTableSubpartition
    : COALESCE SUBPARTITION subpartitionName? updateIndexClauses? parallelClause? allowDisallowClustering?
    ;

allowDisallowClustering
    : (ALLOW | DISALLOW) CLUSTERING
    ;

alterMappingTableClauses
    : MAPPING TABLE (allocateExtentClause | deallocateUnusedClause)
    ;

// DM https://eco.dameng.com/document/dm/zh-cn/pm/view.html#6.5%20%E8%A7%86%E5%9B%BE%E7%9A%84%E7%BC%96%E8%AF%91
alterView
    : ALTER VIEW viewName (
    | ADD LP_? outOfLineConstraint RP_?
    | MODIFY CONSTRAINT constraintName (RELY | NORELY)
    | DROP (CONSTRAINT constraintName | PRIMARY KEY | UNIQUE columnNames)
    | COMPILE
    | READ (ONLY | WRITE)
    | (EDITIONABLE | NONEDITIONABLE)
    )
    ;

deallocateUnusedClause
    : DEALLOCATE UNUSED (KEEP sizeClause)?
    ;

allocateExtentClause
    : ALLOCATE EXTENT (LP_ (SIZE sizeClause | DATAFILE SQ_ fileName SQ_ | INSTANCE numberValue)* RP_)?
    ;

partitionSpec
    : PARTITION partitionName? tablePartitionDescription
    ;

upgradeTableClause
    : UPGRADE (NOT? INCLUDING DATA)? columnProperties?
    ;

recordsPerBlockClause
    : (MINIMIZE | NOMINIMIZE) RECORDS_PER_BLOCK
    ;

partitionAttributes
    : (physicalAttributesClause | loggingClause | allocateExtentClause | deallocateUnusedClause | shrinkClause)*
      (OVERFLOW (physicalAttributesClause | loggingClause | allocateExtentClause | deallocateUnusedClause)*)?
      tableCompression? inmemoryClause?
    ;

shrinkClause
    : SHRINK SPACE COMPACT? CASCADE?
    ;

moveTablePartition
    : MOVE partitionExtendedName (MAPPING TABLE)? tablePartitionDescription filterCondition? updateAllIndexesClause? parallelClause? allowDisallowClustering? ONLINE?
    ;

moveTableSubPartition
	: MOVE subpartitionExtendedName indexingClause? partitioningStorageClause* updateIndexClauses? filterCondition? parallelClause? allowDisallowClustering? ONLINE?
	;

filterCondition
    : INCLUDING ROWS whereClause
    ;

coalesceTablePartition
    : COALESCE PARTITION updateIndexClauses? parallelClause? allowDisallowClustering?
    ;

addTablePartition
    : ADD (PARTITION partitionName? addRangePartitionClause (COMMA_ PARTITION partitionName? addRangePartitionClause)*
    | PARTITION partitionName? addListPartitionClause (COMMA_ PARTITION partitionName? addListPartitionClause)*
    | PARTITION partitionName? addSystemPartitionClause (COMMA_ PARTITION partitionName? addSystemPartitionClause)* (BEFORE? (partitionName | numberValue)?)
    | PARTITION partitionName? addHashPartitionClause) dependentTablesClause?
    ;

addRangePartitionClause
    : rangeValuesClause tablePartitionDescription externalPartSubpartDataProps?
    ((LP_? (rangeSubpartitionDesc (COMMA_ rangeSubpartitionDesc)* | listSubpartitionDesc (COMMA_ listSubpartitionDesc)* | individualHashSubparts (COMMA_ individualHashSubparts)*) RP_?)
    | hashSubpartsByQuantity)? updateIndexClauses?
    ;

addListPartitionClause
    : listValuesClause tablePartitionDescription externalPartSubpartDataProps?
    ((LP_? (rangeSubpartitionDesc (COMMA_ rangeSubpartitionDesc)* | listSubpartitionDesc (COMMA_ listSubpartitionDesc)* | individualHashSubparts (COMMA_ individualHashSubparts)*) RP_?)
    | hashSubpartsByQuantity)? updateIndexClauses?
    ;

hashSubpartsByQuantity
    : SUBPARTITIONS numberValue (STORE IN LP_ tablespaceName (COMMA_ tablespaceName)* RP_)?
    ;

addSystemPartitionClause
    : tablePartitionDescription updateIndexClauses?
    ;

addHashPartitionClause
    : partitioningStorageClause* updateIndexClauses? parallelClause? readOnlyClause? indexingClause?
    ;

dropTablePartition
    : DROP partitionExtendedNames (updateIndexClauses parallelClause?)?
    ;

renamePartitionSubpart
    : RENAME (partitionExtendedName | subpartitionExtendedName) TO newName
    ;

alterIntervalPartitioning
    : SET INTERVAL LP_ expr? RP_ | SET STORE IN LP_ tablespaceName (COMMA_ tablespaceName)* RP_
    ;

partitionExtendedNames
    : (PARTITION | PARTITIONS) (partitionName | partitionForClauses) (COMMA_ (partitionName | partitionForClauses))*
    ;

partitionForClauses
    : FOR LP_ partitionKeyValue (COMMA_ partitionKeyValue)* RP_
    ;

updateIndexClauses
    : updateGlobalIndexClause | updateAllIndexesClause
    ;

updateGlobalIndexClause
    : (UPDATE | INVALIDATE) GLOBAL INDEXES
    ;

updateAllIndexesClause
    : UPDATE INDEXES
    (LP_ indexName LP_ (updateIndexPartition | updateIndexSubpartition) RP_
    (COMMA_ indexName LP_ (updateIndexPartition | updateIndexSubpartition) RP_)* RP_)?
    ;

updateIndexPartition
    : indexPartitionDesc indexSubpartitionClause?
    (COMMA_ indexPartitionDesc indexSubpartitionClause?)*
    ;

indexPartitionDesc
    : PARTITION
    (partitionName
    ((segmentAttributesClause | indexCompression)+ | PARAMETERS LP_ SQ_ odciParameters SQ_ RP_ )?
    usableSpecification?
    )?
    ;

indexSubpartitionClause
    : STORE IN LP_ tablespaceName (COMMA_ tablespaceName)* RP_
    | LP_ SUBPARTITION subpartitionName? (TABLESPACE tablespaceName)? indexCompression? usableSpecification?
    (COMMA_ SUBPARTITION subpartitionName? (TABLESPACE tablespaceName)? indexCompression? usableSpecification?)* RP_
    ;

updateIndexSubpartition
    : SUBPARTITION subpartitionName? (TABLESPACE tablespaceName)?
    (COMMA_ SUBPARTITION subpartitionName? (TABLESPACE tablespaceName)?)*
    ;

supplementalLoggingProps
    : SUPPLEMENTAL LOG supplementalLogGrpClause|supplementalIdKeyClause
    ;

supplementalLogGrpClause
    : GROUP logGroupName LP_ columnName (NO LOG)? (COMMA_ columnName (NO LOG)?)* RP_ ALWAYS?
    ;

supplementalIdKeyClause
    : DATA LP_ (ALL | PRIMARY KEY | UNIQUE INDEX? | FOREIGN KEY) (COMMA_ (ALL | PRIMARY KEY | UNIQUE INDEX? | FOREIGN KEY))* RP_ COLUMNS
    ;

alterSession
    : ALTER SESSION alterSessionOption
    ;

alterSessionOption
    : adviseClause
    | closeDatabaseLinkClause
    | commitInProcedureClause
    | securiyClause
    | parallelExecutionClause
    | resumableClause
    | shardDdlClause
    | syncWithPrimaryClause
    | alterSessionSetClause
    ;

adviseClause
    : ADVISE (COMMIT | ROLLBACK | NOTHING)
    ;

closeDatabaseLinkClause
    : CLOSE DATABASE LINK dbLink
    ;

commitInProcedureClause
    : (ENABLE | DISABLE) COMMIT IN PROCEDURE
    ;

securiyClause
    : (ENABLE | DISABLE) GUARD
    ;

parallelExecutionClause
    : (ENABLE | DISABLE | FORCE) PARALLEL (DML | DDL | QUERY) (PARALLEL numberLiterals)?
    ;

resumableClause
    : enableResumableClause | disableResumableClause
    ;

enableResumableClause
    : ENABLE RESUMABLE (TIMEOUT numberLiterals)? (NAME stringLiterals)?
    ;

disableResumableClause
    : DISABLE RESUMABLE
    ;

shardDdlClause
    : (ENABLE | DISABLE) SHARD DDL
    ;

syncWithPrimaryClause
    : SYNC WITH PRIMARY
    ;

alterSessionSetClause
    : SET alterSessionSetClauseOption
    ;

alterSessionSetClauseOption
    : parameterClause
    | editionClause
    | containerClause
    | rowArchivalVisibilityClause
    | setDefaultCollationClause
    | eventsClause
    ;

parameterClause
    : (parameter)+
    ;

parameter
    : parameterName EQ_ parameterValue
    ;

editionClause
    : EDITION EQ_ editionName
    ;

containerClause
    : CONTAINER EQ_ containerName (SERVICE EQ_ serviceName)?
    ;

rowArchivalVisibilityClause
    : ROW ARCHIVAL VISIBILITY EQ_ (ACTIVE | ALL)
    ;

setDefaultCollationClause
    : DEFAULT_COLLATION EQ_ (collationName | NONE)
    ;

eventsClause
    : (EVENTS EQ_? STRING_)+
    ;

alterDatabaseDictionary
    : ALTER DATABASE DICTIONARY (
    | ENCRYPT CREDENTIALS
    | REKEY CREDENTIALS
    | DELETE CREDENTIALS KEY
    )
    ;

alterDatabase
    : ALTER databaseClauses
    (startupClauses
    | recoveryClauses
    | databaseFileClauses
    | logfileClauses
    | controlfileClauses
    | standbyDatabaseClauses
    | defaultSettingsClauses
    | instanceClauses
    | securityClause
    | prepareClause
    | dropMirrorCopy
    | lostWriteProtection
    | cdbFleetClauses
    | propertyClause
    )
    ;

databaseClauses
    : DATABASE databaseName? | PLUGGABLE DATABASE pdbName?
    ;

startupClauses
    : MOUNT ((STANDBY | CLONE) DATABASE)?
    | OPEN ((READ WRITE)? (RESETLOGS | NORESETLOGS)? (UPGRADE | DOWNGRADE)? | READ ONLY)
    ;

recoveryClauses
    : generalRecovery | managedStandbyRecovery | BEGIN BACKUP | END BACKUP
    ;

generalRecovery
    : RECOVER (AUTOMATIC)? (FROM locationName)? (
      (fullDatabaseRecovery | partialDatabaseRecovery | LOGFILE fileName)
      ((TEST | ALLOW numberValue CORRUPTION | parallelClause)+)?
    | CONTINUE DEFAULT?
    | CANCEL
    )
    ;

fullDatabaseRecovery
    : STANDBY? DATABASE?
    ((UNTIL (CANCEL | TIME dateValue | CHANGE numberValue | CONSISTENT)
    | USING BACKUP CONTROLFILE
    | SNAPSHOT TIME dateValue
    )+)?
    ;

partialDatabaseRecovery
    : TABLESPACE tablespaceName (COMMA_ tablespaceName)*
    | DATAFILE (fileName | fileNumber) (COMMA_ (fileName | fileNumber))*
    ;

managedStandbyRecovery
    : RECOVER (MANAGED STANDBY DATABASE
    ((USING (ARCHIVED | CURRENT) LOGFILE | DISCONNECT (FROM SESSION)?
    | NODELAY
    | UNTIL CHANGE numberValue
    | UNTIL CONSISTENT | USING INSTANCES (ALL | numberValue) | parallelClause)+
    | FINISH | CANCEL)?
    | TO LOGICAL STANDBY (databaseName | KEEP IDENTITY))
    ;

databaseFileClauses
    : RENAME FILE fileName (COMMA_ fileName)* TO fileName
    | createDatafileClause
    | alterDatafileClause
    | alterTempfileClause
    | moveDatafileClause
    ;

createDatafileClause
    : CREATE DATAFILE (fileName | fileNumber) (COMMA_ (fileName | fileNumber))*
    ( AS (fileSpecifications | NEW))?
    ;

fileSpecifications
    : fileSpecification (COMMA_ fileSpecification)*
    ;

fileSpecification
    : datafileTempfileSpec | redoLogFileSpec
    ;

datafileTempfileSpec
    : (fileName | asmFileName )? (SIZE sizeClause)? REUSE? autoextendClause?
    ;

autoextendClause
    : AUTOEXTEND (OFF | ON (NEXT sizeClause)? maxsizeClause?)
    ;

redoLogFileSpec
    : ((fileName | asmFileName)
    | LP_ (fileName | asmFileName) (COMMA_ (fileName | asmFileName))* RP_)?
    (SIZE sizeClause)? (BLOCKSIZE sizeClause)? REUSE?
    ;

alterDatafileClause
    : DATAFILE (fileName | numberValue) (COMMA_ (fileName | numberValue))*
    (ONLINE | OFFLINE (FOR DROP)? | RESIZE sizeClause | autoextendClause | END BACKUP | ENCRYPT | DECRYPT)
    ;

alterTempfileClause
    : TEMPFILE (fileName | numberValue) (COMMA_ (fileName | numberValue))*
    (RESIZE sizeClause | autoextendClause | DROP (INCLUDING DATAFILES)? | ONLINE | OFFLINE)
    ;

logfileClauses
    : ((ARCHIVELOG MANUAL? | NOARCHIVELOG )
    | NO? FORCE LOGGING
    | SET STANDBY NOLOGGING FOR (DATA AVAILABILITY | LOAD PERFORMANCE)
    | RENAME (FILE | LOGFILE) fileName (COMMA_ fileName)* TO fileName
    | CLEAR UNARCHIVED? LOGFILE logfileDescriptor (COMMA_ logfileDescriptor)* (UNRECOVERABLE DATAFILE)?
    | addLogfileClauses
    | dropLogfileClauses
    | switchLogfileClause
    | supplementalDbLogging
    )
    ;

logfileDescriptor
    : GROUP INTEGER_ | LP_ fileName (COMMA_ fileName)* RP_ | fileName
    ;

addLogfileClauses
    : ADD STANDBY? LOGFILE
    (((INSTANCE SQ_ instanceName SQ_)? | (THREAD INTEGER_)?)
    (GROUP INTEGER_)? redoLogFileSpec (COMMA_ (GROUP INTEGER_)? redoLogFileSpec)*
    | MEMBER fileName REUSE? (COMMA_ fileName REUSE?)* TO logfileDescriptor (COMMA_ logfileDescriptor)*)
    ;

controlfileClauses
    : CREATE ((LOGICAL | PHYSICAL)? STANDBY | FAR SYNC INSTANCE) CONTROLFILE AS fileName REUSE?
    | BACKUP CONTROLFILE TO (fileName REUSE? | traceFileClause)
    ;

traceFileClause
    : TRACE (AS fileName REUSE?)? (RESETLOGS | NORESETLOGS)?
    ;

dropLogfileClauses
    : DROP STANDBY? LOGFILE
    (logfileDescriptor (COMMA_ logfileDescriptor)*
    | MEMBER fileName (COMMA_ fileName)*)
    ;

switchLogfileClause
    : SWITCH ALL LOGFILES TO BLOCKSIZE numberValue
    ;

supplementalDbLogging
    : (ADD | DROP) SUPPLEMENTAL LOG
    ( DATA
    | supplementalIdKeyClause
    | supplementalPlsqlClause
    | supplementalSubsetReplicationClause)
    ;

supplementalPlsqlClause
    : DATA FOR PROCEDURAL REPLICATION
    ;

supplementalSubsetReplicationClause
    : DATA SUBSET DATABASE REPLICATION
    ;

standbyDatabaseClauses
    : ((activateStandbyDbClause
    | maximizeStandbyDbClause
    | registerLogfileClause
    | commitSwitchoverClause
    | startStandbyClause
    | stopStandbyClause
    | convertDatabaseClause) parallelClause?)
    | (switchoverClause | failoverClause)
    ;

activateStandbyDbClause
    : ACTIVATE (PHYSICAL | LOGICAL)? STANDBY DATABASE (FINISH APPLY)?
    ;

maximizeStandbyDbClause
    : SET STANDBY DATABASE TO MAXIMIZE (PROTECTION | AVAILABILITY | PERFORMANCE)
    ;

registerLogfileClause
    : REGISTER (OR REPLACE)? (PHYSICAL | LOGICAL)? LOGFILE fileSpecifications (FOR logminerSessionName)?
    ;

commitSwitchoverClause
    : (PREPARE | COMMIT) TO SWITCHOVER
    ( TO (((PHYSICAL | LOGICAL)? PRIMARY | PHYSICAL? STANDBY) ((WITH | WITHOUT) SESSION SHUTDOWN (WAIT | NOWAIT)?)?
    | LOGICAL STANDBY)
    | CANCEL
    )?
    ;

startStandbyClause
    : START LOGICAL STANDBY APPLY IMMEDIATE? NODELAY? (NEW PRIMARY dbLink | INITIAL scnValue? | (SKIP_SYMBOL FAILED TRANSACTION | FINISH))?
    ;

stopStandbyClause
    : (STOP | ABORT) LOGICAL STANDBY APPLY
    ;

switchoverClause
    : SWITCHOVER TO databaseName (VERIFY | FORCE)?
    ;

convertDatabaseClause
    : CONVERT TO (PHYSICAL | SNAPSHOT) STANDBY
    ;

failoverClause
    : FAILOVER TO databaseName FORCE?
    ;

defaultSettingsClauses
    : DEFAULT EDITION EQ_ editionName
    | SET DEFAULT bigOrSmallFiles TABLESPACE
    | DEFAULT TABLESPACE tablespaceName
    | DEFAULT LOCAL? TEMPORARY TABLESPACE (tablespaceName | tablespaceGroupName)
    | RENAME GLOBAL_NAME TO databaseName DOT_ domain (DOT_ domain)*
    | ENABLE BLOCK CHANGE TRACKING (USING FILE fileName REUSE?)?
    | DISABLE BLOCK CHANGE TRACKING
    | NO? FORCE FULL DATABASE CACHING
    | CONTAINERS DEFAULT TARGET EQ_ (LP_ containerName RP_ | NONE)
    | flashbackModeClause
    | undoModeClause
    | setTimeZoneClause
    ;

setTimeZoneClause
    : SET TIME_ZONE EQ_ ((PLUS_ | MINUS_) dateValue | timeZoneRegion)
    ;

timeZoneRegion
    : STRING_
    ;

flashbackModeClause
    : FLASHBACK (ON | OFF)
    ;

undoModeClause
    : LOCAL UNDO (ON | OFF)
    ;

moveDatafileClause
    : MOVE DATAFILE LP_ (fileName | asmFileName | fileNumber) RP_
    (TO LP_ (fileName | asmFileName) RP_ )? REUSE? KEEP?
    ;

instanceClauses
    : (ENABLE | DISABLE) INSTANCE instanceName
    ;

securityClause
    : GUARD (ALL | STANDBY | NONE)
    ;

prepareClause
    : PREPARE MIRROR COPY copyName (WITH (UNPROTECTED | MIRROR | HIGH) REDUNDANCY)?
    ;

dropMirrorCopy
    : DROP MIRROR COPY mirrorName
    ;

lostWriteProtection
    : (ENABLE | DISABLE | REMOVE | SUSPEND)? LOST WRITE PROTECTION
    ;

cdbFleetClauses
    : leadCdbClause | leadCdbUriClause
    ;

leadCdbClause
    : SET LEAD_CDB EQ_  (TRUE | FALSE)
    ;

leadCdbUriClause
    : SET LEAD_CDB_URI EQ_ uriString
    ;

propertyClause
    : PROPERTY (SET | REMOVE) DEFAULT_CREDENTIAL EQ_ qualifiedCredentialName
    ;

alterSystem
    : ALTER SYSTEM alterSystemOption
    ;

alterSystemOption
    : archiveLogClause
    | checkpointClause
    | checkDatafilesClause
    | distributedRecovClauses
    | flushClause
    | endSessionClauses
    | alterSystemSwitchLogfileClause
    | suspendResumeClause
    | quiesceClauses
    | rollingMigrationClauses
    | rollingPatchClauses
    | alterSystemSecurityClauses
    | affinityClauses
    | shutdownDispatcherClause
    | registerClause
    | setClause
    | resetClause
    | relocateClientClause
    | cancelSqlClause
    | flushPasswordfileMetadataCacheClause
    ;

archiveLogClause
    : ARCHIVE LOG instanceClause? (sequenceClause | changeClause | currentClause | groupClause | logfileClause | nextClause = NEXT | logAllClause = ALL) toLocationClause?
    ;

checkpointClause
    : CHECKPOINT (GLOBAL | LOCAL)?
    ;

checkDatafilesClause
    : CHECK DATAFILES (GLOBAL | LOCAL)?
    ;

distributedRecovClauses
    : (ENABLE | DISABLE) DISTRIBUTED RECOVERY
    ;

flushClause
    : FLUSH flushClauseOption
    ;

endSessionClauses
    : (disconnectSessionClause | killSessionClause) (IMMEDIATE | NOREPLY)?
    ;

alterSystemSwitchLogfileClause
    : SWITCH LOGFILE
    ;

suspendResumeClause
    : SUSPEND | RESUME
    ;

quiesceClauses
    : QUIESCE RESTRICTED | UNQUIESCE
    ;

rollingMigrationClauses
    : startRollingMigrationClause | stopRollingMigrationClause
    ;

rollingPatchClauses
    : startRollingPatchClause | stopRollingPatchClause
    ;

alterSystemSecurityClauses
    : restrictedSessionClause | setEncryptionWalletOpenClause | setEncryptionWalletCloseClause | setEncryptionKeyClause
    ;

affinityClauses
    : enableAffinityClause | disableAffinityClause
    ;

shutdownDispatcherClause
    : SHUTDOWN IMMEDIATE? dispatcherName
    ;

registerClause
    : REGISTER
    ;

setClause
    : SET alterSystemSetClause+
    ;

resetClause
    : RESET alterSystemResetClause+
    ;

relocateClientClause
    : RELOCATE CLIENT clientId
    ;

cancelSqlClause
    : CANCEL SQL SQ_ sessionId serialNumber (AT_ instanceId)? sqlId? SQ_
    ;

flushPasswordfileMetadataCacheClause
    : FLUSH PASSWORDFILE_METADATA_CACHE
    ;

instanceClause
    : INSTANCE instanceName
    ;

sequenceClause
    : SEQUENCE INTEGER_
    ;

changeClause
    : CHANGE INTEGER_
    ;

currentClause
    : CURRENT NOSWITCH?
    ;

groupClause
    : GROUP INTEGER_
    ;

logfileClause
    : LOGFILE logFileName (USING BACKUP CONTROLFILE)?
    ;

toLocationClause
    : TO logFileGroupsArchivedLocationName
    ;

flushClauseOption
    : sharedPoolClause | globalContextClause | bufferCacheClause | flashCacheClause | redoToClause
    ;

disconnectSessionClause
    : DISCONNECT SESSION STRING_ POST_TRANSACTION?
    ;

killSessionClause
    : KILL SESSION STRING_
    ;

startRollingMigrationClause
    : START ROLLING MIGRATION TO asmVersion
    ;

stopRollingMigrationClause
    : STOP ROLLING MIGRATION
    ;

startRollingPatchClause
    : START ROLLING PATCH
    ;

stopRollingPatchClause
    : STOP ROLLING PATCH
    ;

restrictedSessionClause
    : (ENABLE | DISABLE) RESTRICTED SESSION
    ;

setEncryptionWalletOpenClause
    : SET ENCRYPTION WALLET OPEN IDENTIFIED BY (walletPassword | hsmAuthString)
    ;

setEncryptionWalletCloseClause
    : SET ENCRYPTION WALLET CLOSE (IDENTIFIED BY (walletPassword | hsmAuthString))?
    ;

setEncryptionKeyClause
    : SET ENCRYPTION KEY (identifiedByWalletPassword | identifiedByHsmAuthString)
    ;

enableAffinityClause
    : ENABLE AFFINITY tableName (SERVICE serviceName)?
    ;

disableAffinityClause
    : DISABLE AFFINITY tableName
    ;

alterSystemSetClause
    : setParameterClause | useStoredOutlinesClause | globalTopicEnabledClause | dbRecoveryFileDestSizeClause | eventsClause
    ;

alterSystemResetClause
    : parameterName scopeClause*
    ;

sharedPoolClause
    : SHARED_POOL
    ;

globalContextClause
    : GLOBAL CONTEXT
    ;

bufferCacheClause
    : BUFFER_CACHE
    ;

flashCacheClause
    : FLASH_CACHE
    ;

redoToClause
    : REDO TO targetDbName (NO? CONFIRM APPLY)?
    ;

identifiedByWalletPassword
    : certificateId? IDENTIFIED BY walletPassword
    ;

identifiedByHsmAuthString
    : IDENTIFIED BY hsmAuthString (MIGRATE USING walletPassword)?
    ;

setParameterClause
    : parameterName EQ_ parameterValue (COMMA_ parameterValue)* alterSystemCommentClause? (DEFERRED)? containerCurrentAllClause? scopeClause*
    ;

useStoredOutlinesClause
    : USE_STORED_OUTLINES EQ_ (TRUE | FALSE | categoryName)
    ;

globalTopicEnabledClause
    : GLOBAL_TOPIC_ENABLED EQ_ (TRUE | FALSE)
    ;

dbRecoveryFileDestSizeClause
    : DB_RECOVERY_FILE_DEST_SIZE EQ_ INTEGER_ capacityUnit?
    ;

alterSystemCommentClause
    : COMMENT EQ_ stringLiterals
    ;

containerCurrentAllClause
    : CONTAINER EQ_ (CURRENT | ALL)
    ;

scopeClause
    : (SCOPE EQ_)? (MEMORY | SPFILE | BOTH) | SID EQ_ (sessionId | SQ_ ASTERISK_ SQ_)
    ;

analyze
    : (ANALYZE ((TABLE tableName| INDEX indexName) partitionExtensionClause? | CLUSTER clusterName))
    (validationClauses | LIST CHAINED ROWS intoTableClause? | DELETE SYSTEM? STATISTICS)
    ;

validationClauses
    : VALIDATE REF UPDATE (SET DANGLING TO NULL)?
    | VALIDATE STRUCTURE (CASCADE (FAST | COMPLETE? (OFFLINE | ONLINE) intoTableClause?)?)?
    ;

intoTableClause
    : INTO tableName
    ;

associateStatistics
    : ASSOCIATE STATISTICS WITH (columnAssociation | functionAssociation) storageTableClause?
    ;

columnAssociation
    : COLUMNS tableName DOT_ columnName (COMMA_ tableName DOT_ columnName)* usingStatisticsType
    ;

functionAssociation
    : (FUNCTIONS functionName (COMMA_ functionName)*
    | PACKAGES packageName (COMMA_ packageName)*
    | TYPES typeName (COMMA_ typeName)*
    | INDEXES indexName (COMMA_ indexName)*
    | INDEXTYPES indexTypeName (COMMA_ indexTypeName)*)
    (usingStatisticsType | defaultCostClause (COMMA_ defaultSelectivityClause)? | defaultSelectivityClause (COMMA_ defaultCostClause)?)
    ;

storageTableClause
    : WITH (SYSTEM | USER) MANAGED STORAGE TABLES
    ;

usingStatisticsType
    : USING (statisticsTypeName | NULL)
    ;

defaultCostClause
    : DEFAULT COST LP_ cpuCost COMMA_ ioCost COMMA_ networkCost RP_
    ;

defaultSelectivityClause
    : DEFAULT SELECTIVITY defaultSelectivity
    ;

disassociateStatistics
    : DISASSOCIATE STATISTICS FROM
    (COLUMNS tableName DOT_ columnName (COMMA_ tableName DOT_ columnName)*
    | FUNCTIONS functionName (COMMA_ functionName)*
    | PACKAGES packageName (COMMA_ packageName)*
    | TYPES typeName (COMMA_ typeName)*
    | INDEXES indexName (COMMA_ indexName)*
    | INDEXTYPES indexTypeName (COMMA_ indexTypeName)*) FORCE?
    ;

audit
    : auditTraditional | auditUnified
    ;

auditTraditional
    : AUDIT (auditOperationClause (auditingByClause | IN SESSION CURRENT)? | auditSchemaObjectClause | NETWORK | DIRECT_PATH LOAD auditingByClause?)
    ( BY (SESSION | ACCESS))? (WHENEVER NOT? SUCCESSFUL)? (CONTAINER EQ_ (CURRENT | ALL))?
    ;

auditingByClause
    : BY username (COMMA_ username)*
    ;

auditOperationClause
    : (sqlStatementShortcut | ALL | ALL STATEMENTS) (COMMA_ sqlStatementShortcut | ALL | ALL STATEMENTS)*
    | (systemPrivilege | ALL PRIVILEGES) (COMMA_ systemPrivilege | ALL PRIVILEGES)*
    ;

sqlStatementShortcut
    : ALTER SYSTEM | CLUSTER | CREATE CLUSTER | ALTER CLUSTER | DROP CLUSTER | TRUNCATE CLUSTER | CONTEXT | CREATE CONTEXT | DROP CONTEXT
    | DATABASE LINK | CREATE DATABASE LINK | ALTER DATABASE LINK | DROP DATABASE LINK | DIMENSION | CREATE DIMENSION | ALTER DIMENSION | DROP DIMENSION
    | DIRECTORY | CREATE DIRECTORY | DROP DIRECTORY | INDEX | CREATE INDEX | ALTER INDEX | ANALYZE INDEX | DROP INDEX
    | MATERIALIZED VIEW | CREATE MATERIALIZED VIEW | ALTER MATERIALIZED VIEW | DROP MATERIALIZED VIEW | NOT EXISTS | OUTLINE | CREATE OUTLINE | ALTER OUTLINE | DROP OUTLINE
    | PLUGGABLE DATABASE | CREATE PLUGGABLE DATABASE | ALTER PLUGGABLE DATABASE | DROP PLUGGABLE DATABASE
    | PROCEDURE | CREATE FUNCTION | CREATE LIBRARY | CREATE PACKAGE | CREATE PACKAGE BODY | CREATE PROCEDURE | DROP FUNCTION | DROP LIBRARY | DROP PACKAGE | DROP PROCEDURE
    | PROFILE | CREATE PROFILE | ALTER PROFILE | DROP PROFILE | PUBLIC DATABASE LINK | CREATE PUBLIC DATABASE LINK | ALTER PUBLIC DATABASE LINK | DROP PUBLIC DATABASE LINK
    | PUBLIC SYNONYM | CREATE PUBLIC SYNONYM | DROP PUBLIC SYNONYM | ROLE | CREATE ROLE | ALTER ROLE | DROP ROLE | SET ROLE
    | ROLLBACK SEGMENT | CREATE ROLLBACK SEGMENT | ALTER ROLLBACK SEGMENT | DROP ROLLBACK SEGMENT | SEQUENCE | CREATE SEQUENCE | DROP SEQUENCE | SESSION | SYNONYM | CREATE SYNONYM | DROP SYNONYM
    | SYSTEM AUDIT | SYSTEM GRANT | TABLE | CREATE TABLE | DROP TABLE | TRUNCATE TABLE | TABLESPACE | CREATE TABLESPACE | ALTER TABLESPACE | DROP TABLESPACE
    | TRIGGER | CREATE TRIGGER | ALTER TRIGGER | DROP TRIGGER | ALTER TABLE | TYPE | CREATE TYPE | CREATE TYPE BODY | ALTER TYPE | DROP TYPE | DROP TYPE BODY
    | USER | CREATE USER | ALTER USER | DROP USER | VIEW | CREATE VIEW | DROP VIEW
    | ALTER SEQUENCE | COMMENT TABLE | DELETE TABLE | EXECUTE DIRECTORY | EXECUTE PROCEDURE | GRANT DIRECTORY | GRANT PROCEDURE | GRANT SEQUENCE | GRANT TABLE | GRANT TYPE
    | INSERT TABLE | LOCK TABLE | READ DIRECTORY | SELECT SEQUENCE | SELECT TABLE | UPDATE TABLE | WRITE DIRECTORY
    ;

auditSchemaObjectClause
    : (sqlOperation (COMMA_ sqlOperation)* | ALL) auditingOnClause
    ;

auditingOnClause
    : ON (DEFAULT | objectName | DIRECTORY directoryName | MINING MODEL modelName | SQL TRANSLATION PROFILE profileName)
    ;

sqlOperation
    : ALTER | AUDIT | COMMENT | DELETE | FLASHBACK | GRANT | INDEX | INSERT | LOCK | RENAME | SELECT | UPDATE | EXECUTE | READ
    ;

auditUnified
    : AUDIT (auditPolicyClause | contextClause)
    ;

noAuditUnified
    : NOAUDIT (noAuditPolicyClause | contextClause)
    ;

auditPolicyClause
    : POLICY policyName (byUsersWithRoles | (BY | EXCEPT) username (COMMA_ username)*)? (WHENEVER NOT? SUCCESSFUL)?
    ;

noAuditPolicyClause
    : POLICY policyName (byUsersWithRoles | BY username (COMMA_ username)*)? (WHENEVER NOT? SUCCESSFUL)?
    ;

byUsersWithRoles
    : BY USERS WITH GRANTED ROLES roleName (COMMA_ roleName)*
    ;

contextClause
    : contextNamespaceAttributesClause (COMMA_ contextNamespaceAttributesClause)* (BY username (COMMA_ username)*)?
    ;

contextNamespaceAttributesClause
    : CONTEXT NAMESPACE namespace ATTRIBUTES attributeName (COMMA_ attributeName)*
    ;

comment
    : COMMENT ON (
    | AUDIT POLICY policyName
    | COLUMN (tableName | viewName | materializedViewName) DOT_ columnName
    | EDITION editionName
    | INDEXTYPE indexTypeName
    | MATERIALIZED VIEW materializedViewName
    | MINING MODEL modelName
    | OPERATOR operatorName
    | TABLE (tableName | viewName)
    ) IS STRING_
    ;

// DM https://eco.dameng.com/document/dm/zh-cn/pm/flashback-query.html
flashbackDatabase
    : FLASHBACK STANDBY? PLUGGABLE? DATABASE databaseName?
    ( TO (scnTimestampClause | restorePointClause)
    | TO BEFORE (scnTimestampClause | RESETLOGS))
    ;

scnTimestampLsn
    : SCN | TIMESTAMP
    ;

scnTimestampClause
    : scnTimestampLsn scnTimestampExpr
    ;

restorePointClause
    : RESTORE POINT restorePoint
    ;

flashbackTable
    : FLASHBACK TABLE tableName (COMMA_ tableName)* TO (
    (scnTimestampClause | restorePointClause) ((ENABLE | DISABLE) TRIGGERS)?
    | BEFORE DROP renameToTable?
    )
    ;

renameToTable
    : RENAME TO tableName
    ;

purge
    : PURGE (TABLE tableName
    | INDEX indexName
    | TABLESPACE tablespaceName (USER username)?
    | TABLESPACE SET tablespaceSetName (USER username)?
    | RECYCLEBIN
    | DBA_RECYCLEBIN)
    ;

// ORACLE https://docs.oracle.com/en/database/oracle/oracle-database/12.2/sqlrf/RENAME.html
rename
    : RENAME name TO name
    ;

createDatabase
    : CREATE DATABASE databaseName? createDatabaseClauses+
    ;

createDatabaseClauses
    : USER SYS IDENTIFIED BY password
    | USER SYSTEM IDENTIFIED BY password
    | CONTROLFILE REUSE
    | MAXDATAFILES INTEGER_
    | MAXINSTANCES INTEGER_
    | CHARACTER SET databaseCharset
    | NATIONAL CHARACTER SET nationalCharset
    | SET DEFAULT bigOrSmallFiles TABLESPACE
    | databaseLoggingClauses
    | tablespaceClauses
    | setTimeZoneClause
    | bigOrSmallFiles? USER_DATA TABLESPACE tablespaceName DATAFILE datafileTempfileSpec (COMMA_ datafileTempfileSpec)*
    | enablePluggableDatabase
    | databaseName USING MIRROR COPY mirrorName
    ;

databaseLoggingClauses
    : LOGFILE (GROUP INTEGER_)? fileSpecification (COMMA_ (GROUP INTEGER_)? fileSpecification)*
    | MAXLOGFILES INTEGER_
    | MAXLOGMEMBERS INTEGER_
    | MAXLOGHISTORY INTEGER_
    | (ARCHIVELOG | NOARCHIVELOG)
    | FORCE LOGGING
    | SET STANDBY NOLOGGING FOR (DATA AVAILABILITY | LOAD PERFORMANCE)
    ;

tablespaceClauses
    : EXTENT MANAGEMENT LOCAL
    | DATAFILE fileSpecifications
    | SYSAUX DATAFILE fileSpecifications
    | defaultTablespace
    | defaultTempTablespace
    | undoTablespace
    ;

defaultTablespace
    : DEFAULT TABLESPACE tablespaceName (DATAFILE datafileTempfileSpec)? extentManagementClause?
    ;

defaultTempTablespace
    : bigOrSmallFiles? DEFAULT
    (TEMPORARY TABLESPACE | LOCAL TEMPORARY TABLESPACE FOR (ALL | LEAF)) tablespaceName
    (TEMPFILE fileSpecifications)? extentManagementClause?
    ;

undoTablespace
    : bigOrSmallFiles? UNDO TABLESPACE tablespaceName (DATAFILE fileSpecifications)?
    ;

bigOrSmallFiles
    : BIGFILE | SMALLFILE
    ;

extentManagementClause
    : EXTENT MANAGEMENT LOCAL (AUTOALLOCATE | UNIFORM (SIZE sizeClause)?)?
    ;

enablePluggableDatabase
    : ENABLE PLUGGABLE DATABASE
    (SEED fileNameConvert? (SYSTEM tablespaceDatafileClauses)? (SYSAUX tablespaceDatafileClauses)?)? undoModeClause?
    ;

fileNameConvert
    : FILE_NAME_CONVERT EQ_ (LP_ replaceFileNamePattern (COMMA_ replaceFileNamePattern)* RP_| NONE)
    ;

replaceFileNamePattern
    : filenamePattern COMMA_ filenamePattern
    ;

tablespaceDatafileClauses
    : DATAFILES (SIZE sizeClause | autoextendClause)+
    ;

createDatabaseLink
    : CREATE SHARED? PUBLIC? DATABASE LINK dbLink (connectToClause | dbLinkAuthentication)* (USING connectString)?
    ;

alterDatabaseLink
    : ALTER SHARED? PUBLIC? DATABASE LINK dbLink (
    | CONNECT TO username IDENTIFIED BY password dbLinkAuthentication?
    | dbLinkAuthentication
    )
    ;

dropDatabaseLink
    : DROP PUBLIC? DATABASE LINK dbLink
    ;

connectToClause
    : CONNECT TO (CURRENT_USER | username IDENTIFIED BY password dbLinkAuthentication?)
    ;

dbLinkAuthentication
    : AUTHENTICATED BY username IDENTIFIED BY password
    ;

createDimension
    : CREATE DIMENSION dimensionName levelClause+ (hierarchyClause | attributeClause+ | extendedAttrbuteClause)+
    ;

levelClause
    : LEVEL level IS (columnName | LP_ columnName (COMMA_ columnName)* RP_) (SKIP_SYMBOL WHEN NULL)?
    ;

hierarchyClause
    : HIERARCHY hierarchyName LP_ level (CHILD OF level)+ dimensionJoinClause* RP_
    ;

dimensionJoinClause
    : JOIN KEY (columnName | LP_ columnName (COMMA_ columnName)* RP_) REFERENCES level
    ;

attributeClause
    : ATTRIBUTE level DETERMINES (columnName | LP_ columnName (COMMA_ columnName)* RP_)
    ;

extendedAttrbuteClause
    : ATTRIBUTE attributeName (LEVEL level DETERMINES (columnName | LP_ columnName (COMMA_ columnName)* RP_))+
    ;

alterDimension
    : ALTER DIMENSION dimensionName (alterDimensionAddClause* | alterDimensionDropClause* | COMPILE)
    ;

alterDimensionAddClause
    : ADD (levelClause | hierarchyClause | attributeClause | extendedAttrbuteClause)
    ;

alterDimensionDropClause
    : DROP (LEVEL level cascadeOrRestrict?
    | HIERARCHY hierarchyName
    | ATTRIBUTE attributeName (LEVEL level (COLUMN columnName (COMMA_ COLUMN columnName)*)?)?)
    ;

dropDimension
    : DROP DIMENSION dimensionName
    ;

dropDirectory
    : DROP DIRECTORY ifExists? directoryName
    ;

dropType
    : DROP TYPE ifExists? typeName (FORCE | VALIDATE | cascadeOrRestrict)?
    ;

dropTypeBody
    : DROP TYPE BODY ifExists? typeName cascadeOrRestrict?
    ;

parameterDeclarationList
    : LP_ (parameterDeclaration (COMMA_ parameterDeclaration)*)? RP_
    ;

parameterDeclaration
    : parameterName (IN? dataType ((ASSIGNMENT_OPERATOR_ | DEFAULT) expr)? | IN? OUT NOCOPY? dataType)?
    ;

sharingClause
    : SHARING EQ_ (METADATA | NONE)
    ;

deterministicClause
    : DETERMINISTIC
    ;

parallelEnableClause
    : PARALLEL_ENABLE (LP_ PARTITION argument BY (ANY
    | (HASH | RANGE) LP_ columnName (COMMA_ columnName)* RP_ streamingCluase?
    | VALUE LP_ columnName RP_) RP_)?
    ;

streamingCluase
    : (ORDER | CLUSTER) expr BY LP_ columnName (COMMA_ columnName)* RP_
    ;

resultCacheClause
    : RESULT_CACHE (RELIES_ON LP_ (dataSource (COMMA_ dataSource)*)? RP_)?
    ;

aggregateClause
    : AGGREGATE USING implementationType
    ;

pipelinedClause
    : PIPELINED ((USING implementationType)?
    | (ROW | TABLE) POLYMORPHIC (USING implementationPackage)?)
    ;

sqlMacroClause
    : SQL_MARCO
    ;

callSpec
    : javaDeclaration | cDeclaration
    ;

javaDeclaration
    : LANGUAGE JAVA NAME STRING_
    ;

cDeclaration
    : (LANGUAGE SINGLE_C | EXTERNAL)
    ((NAME name)? LIBRARY libName| LIBRARY libName (NAME name)?)
    (AGENT IN RP_ argument (COMMA_ argument)* LP_)?
    (WITH CONTEXT)?
    (PARAMETERS LP_ externalParameter (COMMA_ externalParameter)* RP_)?
    ;

externalParameter
    : (CONTEXT
    | SELF (TDO | property)?
    | (parameterName | RETURN) property? (BY REFERENCE)? externalDatatype)
    ;

property
    : (INDICATOR (STRUCT | TDO)? | LENGTH | DURATION | MAXLEN | CHARSETID | CHARSETFORM)
    ;

alterAnalyticView
    : ALTER ANALYTIC VIEW analyticViewName (RENAME TO analyticViewName | COMPILE)
    ;

alterAttributeDimension
    : ALTER ATTRIBUTE DIMENSION (schemaName DOT_)? attributeDimensionName (RENAME TO attributeDimensionName | COMPILE)
    ;

createSequence
    : CREATE SEQUENCE ifNotExists? sequenceName (SHARING EQ_ (METADATA | DATA | NONE))? createSequenceClause*
    ;

createSequenceClause
    : (INCREMENT BY | START WITH) INTEGER_
    | (MAXVALUE INTEGER_ | NOMAXVALUE)
    | (MINVALUE INTEGER_ | NOMINVALUE)
    | (CYCLE | NOCYCLE)
    | (CACHE INTEGER_ | NOCACHE)
    | (ORDER | NOORDER)
    | (KEEP | NOKEEP)
    | (SCALE (EXTEND | NOEXTEND) | NOSCALE)
    | (SHARD (EXTEND | NOEXTEND) | NOSHARD)
    | (SESSION | GLOBAL | LOCAL)
    ;

alterSequence
    : ALTER SEQUENCE sequenceName alterSequenceClause+
    ;

alterSequenceClause
   : (INCREMENT BY | START WITH | CURRENT VALUE) INTEGER_
   | MAXVALUE INTEGER_
   | NOMAXVALUE
   | MINVALUE INTEGER_
   | NOMINVALUE
   | RESTART
   | CYCLE
   | NOCYCLE
   | CACHE INTEGER_
   | NOCACHE
   | ORDER
   | NOORDER
   | KEEP
   | NOKEEP
   | SCALE (EXTEND | NOEXTEND)
   | NOSCALE
   | SHARD (EXTEND | NOEXTEND)
   | NOSHARD
   | SESSION
   | GLOBAL
   ;

createContext
    : CREATE (OR REPLACE)? CONTEXT ifNotExists? namespace USING packageName sharingClause? (initializedClause | accessedClause)?
    ;

initializedClause
    : INITIALIZED (EXTERNALLY | GLOBALLY)
    ;

accessedClause
    : ACCESSED GLOBALLY
    ;

createSPFile
    : CREATE SPFILE (EQ_ spfileName)? FROM (PFILE (EQ_ pfileName)? (AS COPY)? | MEMORY)
    ;

createPFile
    : CREATE PFILE (EQ_ pfileName)? FROM (SPFILE (EQ_ spfileName)? (AS COPY)? | MEMORY)
    ;

createControlFile
    : CREATE CONTROLFILE REUSE? SET? DATABASE databaseName (logfileForControlClause | RESETLOGS | NORESETLOGS | DATAFILE fileSpecifications
    |( MAXLOGFILES INTEGER_
    | MAXLOGMEMBERS INTEGER_
    | MAXLOGHISTORY INTEGER_
    | MAXDATAFILES INTEGER_
    | MAXINSTANCES INTEGER_
    | ARCHIVELOG
    | NOARCHIVELOG
    | FORCE LOGGING
    | SET STANDBY NOLOGGING FOR (DATA AVAILABILITY | LOAD PERFORMANCE)))+
    characterSetClause?
    ;

logfileForControlClause
    : LOGFILE (GROUP INTEGER_)? fileSpecification (COMMA_ (GROUP INTEGER_)? fileSpecification)*
    ;

characterSetClause
    : CHARACTER SET characterSetName
    ;

createFlashbackArchive
   : CREATE FLASHBACK ARCHIVE DEFAULT? flashbackArchiveName tablespaceClause
     flashbackArchiveQuota? (NO? OPTIMIZE DATA)? flashbackArchiveRetention
   ;

flashbackArchiveQuota
    : QUOTA INTEGER_ quotaUnit
    ;

flashbackArchiveRetention
    : RETENTION INTEGER_ (YEAR | MONTH | DAY)
    ;

alterFlashbackArchive
    : ALTER FLASHBACK ARCHIVE flashbackArchiveName
    ( SET DEFAULT
    | (ADD | MODIFY) TABLESPACE tablespaceName flashbackArchiveQuota?
    | REMOVE TABLESPACE tablespaceName
    | MODIFY RETENTION? flashbackArchiveRetention
    | PURGE purgeClause
    | NO? OPTIMIZE DATA)
    ;

purgeClause
    : ALL
    | BEFORE scnTimestampClause
    ;

dropFlashbackArchive
    : DROP FLASHBACK ARCHIVE flashbackArchiveName
    ;

createDiskgroup
    : CREATE DISKGROUP diskgroupName ((HIGH | NORMAL | FLEX | EXTENDED (SITE siteName)? | EXTERNAL) REDUNDANCY)? diskClause+
    ( ATTRIBUTE attributeName EQ_ attributeValue (COMMA_ attributeName EQ_ attributeValue)*)?
    ;

diskClause
    : (QUORUM | REGULAR)? (FAILGROUP diskgroupName)? DISK qualifieDiskClause (COMMA_ qualifieDiskClause)*
    ;

qualifieDiskClause
    : searchString (NAME diskName)? (SIZE sizeClause)? (FORCE | NOFORCE)?
    ;

dropDiskgroup
    : DROP DISKGROUP diskgroupName contentsClause?
    ;

contentsClause
    : ((FORCE? INCLUDING) | EXCLUDING) CONTENTS
    ;

createRollbackSegment
    : CREATE PUBLIC? ROLLBACK SEGMENT rollbackSegment ((TABLESPACE tablespaceName) | storageClause)*
    ;

dropRollbackSegment
    : DROP ROLLBACK SEGMENT rollbackSegment
    ;

createLockdownProfile
    : CREATE LOCKDOWN PROFILE profileName (staticBaseProfile | dynamicBaseProfile)?
    ;

staticBaseProfile
    : FROM profileName
    ;

dynamicBaseProfile
    : INCLUDING profileName
    ;

dropLockdownProfile
    : DROP LOCKDOWN PROFILE profileName
    ;

createInmemoryJoinGroup
    : CREATE INMEMORY JOIN GROUP (schemaName DOT_)? joinGroupName
     LP_ tableColumnClause COMMA_ tableColumnClause (COMMA_ tableColumnClause)* RP_
    ;

tableColumnClause
    : (schemaName DOT_)? tableName LP_ columnName RP_
    ;

alterInmemoryJoinGroup
    : ALTER INMEMORY JOIN GROUP (schemaName DOT_)? joinGroupName (ADD | REMOVE) LP_ tableName LP_ columnName RP_ RP_
    ;

dropInmemoryJoinGroup
    : DROP INMEMORY JOIN GROUP (schemaName DOT_)? joinGroupName
    ;

createRestorePoint
    : CREATE CLEAN? RESTORE POINT restorePointName (FOR PLUGGABLE DATABASE pdbName)?
      (AS OF scnTimestampLsn expr)?
      (PRESERVE | GUARANTEE FLASHBACK DATABASE)?
    ;

dropRestorePoint
    : DROP RESTORE POINT restorePointName (FOR PLUGGABLE DATABASE pdbName)?
    ;

dropOperator
    : DROP OPERATOR ifExists? (schemaName DOT_)? operatorName FORCE?
    ;

alterLibrary
    : ALTER LIBRARY (schemaName DOT_)? libraryName (libraryCompileClause | EDITIONABLE | NONEDITIONABLE)
    ;

libraryCompileClause
    : COMPILE DEBUG? compilerParametersClause* (REUSE SETTINGS)?
    ;

alterMaterializedZonemap
    : ALTER MATERIALIZED ZONEMAP (schemaName DOT_)? zonemapName
    ( alterZonemapAttributes
    | zonemapRefreshClause
    | (ENABLE | DISABLE) PRUNING
    | COMPILE
    | REBUILD
    | UNUSABLE)
    ;

alterZonemapAttributes
    : (PCTFREE INTEGER_ | PCTUSED INTEGER_ | CACHE | NOCACHE)+
    ;

zonemapRefreshClause
    : REFRESH (FAST | COMPLETE | FORCE)?
      (ON (DEMAND | COMMIT | LOAD | DATA MOVEMENT | LOAD DATA MOVEMENT) )?
    ;

alterJava
   : ALTER JAVA (SOURCE | CLASS) objectName resolveClauses? (COMPILE | RESOLVE | invokerRightsClause)
   ;

resolveClauses
    : RESOLVER LP_ resolveClause+ RP_
    ;

resolveClause
    : LP_ matchString DOT_? (schemaName | MINUS_) RP_
    ;

alterAuditPolicy
    : ALTER AUDIT POLICY policyName
      ((ADD | DROP) subAuditClause)?
      (CONDITION (DROP | STRING_ EVALUATE PER (STATEMENT | SESSION | INSTANCE)))?
    ;

subAuditClause
    : privilegeAuditClause
    | actionAuditClause
    | roleAuditClause
    | ONLY TOPLEVEL
    | (privilegeAuditClause actionAuditClause)
    | (privilegeAuditClause roleAuditClause)
    | (privilegeAuditClause ONLY TOPLEVEL)
    | (actionAuditClause roleAuditClause)
    | (actionAuditClause ONLY TOPLEVEL)
    | (actionAuditClause roleAuditClause ONLY TOPLEVEL)
    | (privilegeAuditClause roleAuditClause ONLY TOPLEVEL)
    | (privilegeAuditClause actionAuditClause ONLY TOPLEVEL)
    | (privilegeAuditClause actionAuditClause roleAuditClause)
    | (privilegeAuditClause actionAuditClause roleAuditClause ONLY TOPLEVEL)
    ;

privilegeAuditClause
    : PRIVILEGES systemPrivilegeClause (COMMA_ systemPrivilegeClause)*
    ;

actionAuditClause
    : (standardActions | componentActions)*
    ;

standardActions
    : ACTIONS standardActionsClause standardActionsClause*
    ;

standardActionsClause
    : (objectAction ON (DIRECTORY directoryName | MINING MODEL objectName | objectName) | systemAction)
    ;

objectAction
    : ALL
    | ALTER
    | AUDIT
    | COMMENT
    | CREATE
    | DELETE
    | EXECUTE
    | FLASHBACK
    | GRANT
    | INDEX
    | INSERT
    | LOCK
    | READ
    | RENAME
    | SELECT
    | UPDATE
    | USE
    | WRITE
    ;

systemAction
    : ALL
    | ALTER EDITION
    | ALTER REWRITE EQUIVALENCE
    | ALTER SUMMARY
    | ALTER TRACING
    | CREATE BITMAPFILE
    | CREATE CONTROL FILE
    | CREATE DATABASE
    | CREATE SUMMARY
    | DECLARE REWRITE EQUIVALENCE
    | DROP BITMAPFILE
    | DROP DATABASE
    | DROP REWRITE EQUIVALENCE
    | DROP SUMMARY
    | FLASHBACK DATABASE
    | MERGE
    | SAVEPOINT
    | SET CONSTRAINTS
    | UNDROP OBJECT
    | UPDATE INDEXES
    | UPDATE JOIN INDEX
    | VALIDATE INDEX
    ;

componentActions
    : ACTIONS COMPONENT EQ_ (DATAPUMP | DIRECT_LOAD | OLS | XS) componentAction (COMMA_ componentAction)*
    | DV componentAction ON objectName (COMMA_ componentAction ON objectName)*
    ;

componentAction
    : ALL
    | dataDumpAction
    | directLoadAction
    | labelSecurityAction
    | securityAction
    | databaseVaultAction
    ;

dataDumpAction
    : EXPORT
    | IMPORT
    ;

directLoadAction
    : LOAD
    ;

labelSecurityAction
    : CREATE POLICY
    | ALTER POLICY
    | DROP POLICY
    | APPLY POLICY
    | REMOVE POLICY
    | SET AUTHORIZATION
    | PRIVILEGED ACTION
    | ENABLE POLICY
    | DISABLE POLICY
    | SUBSCRIBE OID
    | UNSUBSCRIBE OID
    | CREATE DATA LABEL
    | ALTER DATA LABEL
    | DROP DATA LABEL
    | CREATE LABEL COMPONENT
    | ALTER LABEL COMPONENTS
    | DROP LABEL COMPONENTS
    ;

securityAction
    : CREATE USER
    | UPDATE USER
    | DELETE USER
    | CREATE ROLE
    | UPDATE ROLE
    | DELETE ROLE
    | GRANT ROLE
    | REVOKE ROLE
    | ADD PROXY
    | REMOVE PROXY
    | SET USER PASSWORD
    | SET USER VERIFIER
    | CREATE ROLESET
    | UPDATE ROLESET
    | DELETE ROLESET
    | CREATE SECURITY CLASS
    | UPDATE SECURITY CLASS
    | DELETE SECURITY CLASS
    | CREATE NAMESPACE TEMPLATE
    | UPDATE NAMESPACE TEMPLATE
    | DELETE NAMESPACE TEMPLATE
    | CREATE ACL
    | UPDATE ACL
    | DELETE ACL
    | CREATE DATA SECURITY
    | UPDATE DATA SECURITY
    | DELETE DATA SECURITY
    | ENABLE DATA SECURITY
    | DISABLE DATA SECURITY
    | ADD GLOBAL CALLBACK
    | DELETE GLOBAL CALLBACK
    | ENABLE GLOBAL CALLBACK
    | ENABLE ROLE
    | DISABLE ROLE
    | SET COOKIE
    | SET INACTIVE TIMEOUT
    | CREATE SESSION
    | DESTROY SESSION
    | SWITCH USER
    | ASSIGN USER
    | CREATE SESSION NAMESPACE
    | DELETE SESSION NAMESPACE
    | CREATE NAMESPACE ATTRIBUTE
    | GET NAMESPACE ATTRIBUTE
    | SET NAMESPACE ATTRIBUTE
    | DELETE NAMESPACE ATTRIBUTE
    | SET USER PROFILE
    | GRANT SYSTEM PRIVILEGE
    | REVOKE SYSTEM PRIVILEGE
    ;

databaseVaultAction
    : REALM VIOLATION
    | REALM SUCCESS
    | REALM ACCESS
    | RULE SET FAILURE
    | RULE SET SUCCESS
    | RULE SET EVAL
    | FACTOR ERROR
    | FACTOR NULL
    | FACTOR VALIDATE ERROR
    | FACTOR VALIDATE FALSE
    | FACTOR TRUST LEVEL NULL
    | FACTOR TRUST LEVEL NEG
    | FACTOR ALL
    ;

roleAuditClause
    : ROLES roleName (COMMA_ roleName)*
    ;

alterCluster
    : ALTER CLUSTER clusterName
    (physicalAttributesClause
    | SIZE sizeClause
    | (MODIFY PARTITION partitionName)? allocateExtentClause
    | deallocateUnusedClause
    | (CACHE | NOCACHE))+ (parallelClause)?
    ;

alterOperator
    : ALTER OPERATOR operatorName (addBindingClause | dropBindingClause | COMPILE)
    ;

addBindingClause
    : ADD BINDING LP_ parameterType (COMMA_ parameterType)* RP_ RETURN (LP_ returnType RP_ | NUMBER) implementationClause? usingFunctionClause
    ;

implementationClause
    : (ANCILLARY TO primaryOperatorClause (COMMA_ primaryOperatorClause)*) | contextClauseWithOpeartor
    ;

primaryOperatorClause
    : operatorName LP_ parameterType (COMMA_ parameterType)* RP_
    ;

contextClauseWithOpeartor
    : withIndexClause withColumnClause?
    | withIndexClause? withColumnClause
    ;

withIndexClause
    : WITH INDEX CONTEXT COMMA_ SCAN CONTEXT implementationType (COMPUTE ANCILLARY DATA)?
    ;

withColumnClause
    : WITH COLUMN CONTEXT
    ;

usingFunctionClause
    : USING (packageName DOT_ | typeName DOT_)? functionName
    ;

dropBindingClause
    : DROP BINDING LP_ parameterType (COMMA_ parameterType)* RP_ FORCE?
    ;

alterDiskgroup
    : ALTER DISKGROUP ((diskgroupName ((((addDiskClause | dropDiskClause) (COMMA_ (addDiskClause | dropDiskClause))* | resizeDiskClause) (rebalanceDiskgroupClause)?)
    | replaceDiskClause
    | renameDiskClause
    | diskOnlineClause
    | diskOfflineClause
    | rebalanceDiskgroupClause
    | checkDiskgroupClause
    | diskgroupTemplateClauses
    | diskgroupDirectoryClauses
    | diskgroupAliasClauses
    | diskgroupVolumeClauses
    | diskgroupAttributes
    | modifyDiskgroupFile
    | dropDiskgroupFileClause
    | convertRedundancyClause
    | usergroupClauses
    | userClauses
    | filePermissionsClause
    | fileOwnerClause
    | scrubClause
    | quotagroupClauses
    | filegroupClauses))
    | (((diskgroupName (COMMA_ diskgroupName)*) | ALL) (undropDiskClause | diskgroupAvailability | enableDisableVolume)))
    ;

addDiskClause
    : ADD ((SITE siteName)? (QUORUM | REGULAR)? (FAILGROUP failgroupName)? DISK qualifiedDiskClause (COMMA_ qualifiedDiskClause)*)+
    ;

qualifiedDiskClause
    : searchString (NAME diskName)? (SIZE sizeClause)? (FORCE | NOFORCE)?
    ;

dropDiskClause
    : DROP ((QUORUM | REGULAR)? DISK diskName (FORCE | NOFORCE)? (',' diskName (FORCE | NOFORCE)?)*
    | DISKS IN (QUORUM | REGULAR)? FAILGROUP failgroupName (FORCE | NOFORCE)? (COMMA_ failgroupName (FORCE | NOFORCE)?)*)
    ;

resizeDiskClause
    : RESIZE (ALL | DISKS IN FAILGROUP failgroupName) (SIZE sizeClause)?
    ;

rebalanceDiskgroupClause
    : REBALANCE (
        (WITH withPhases | WITHOUT withoutPhases)? (POWER INTEGER_)? (WAIT | NOWAIT)?
        | (MODIFY POWER INTEGER_?)?
    )
    ;

withPhases
    : withPhase (COMMA_ withPhase)*
    ;

withPhase
    : RESTORE | BALANCE | PREPARE | COMPACT
    ;

withoutPhases
    : withoutPhase (COMMA_ withoutPhase)*
    ;

withoutPhase
    : BALANCE | PREPARE | COMPACT
    ;

replaceDiskClause
    : REPLACE DISK diskName WITH pathString (FORCE | NOFORCE)?
    (COMMA_ diskName WITH pathString (FORCE | NOFORCE)?)*
    (POWER INTEGER_)? (WAIT | NOWAIT)?
    ;

renameDiskClause
    : RENAME (DISK diskName TO diskName (COMMA_ diskName TO diskName)* | DISKS ALL)
    ;

diskOnlineClause
    : ONLINE (((QUORUM | REGULAR)? DISK diskName (COMMA_ diskName)*
    | DISKS IN (QUORUM | REGULAR)? FAILGROUP failgroupName (COMMA_ failgroupName)*)+
    | ALL) (POWER INTEGER_)? (WAIT | NOWAIT)?
    ;

diskOfflineClause
    : OFFLINE ((QUORUM | REGULAR)? DISK diskName (COMMA_ diskName)*
    | DISKS IN (QUORUM | REGULAR)? FAILGROUP failgroupName (COMMA_ failgroupName)*)+ (timeoutClause)?
    ;

timeoutClause
    : DROP AFTER INTEGER_ timeUnit
    ;

checkDiskgroupClause
    : CHECK ALL? (REPAIR | NOREPAIR)?
    ;

diskgroupTemplateClauses
    : (((ADD | MODIFY) TEMPLATE templateName qualifiedTemplateClause (COMMA_ templateName qualifiedTemplateClause)*)
    | (DROP TEMPLATE templateName (COMMA_ templateName)*))
    ;

qualifiedTemplateClause
    : (ATTRIBUTE | ATTRIBUTES) LP_ redundancyClause? stripingClause diskRegionClause RP_
    ;

redundancyClause
    : MIRROR | HIGH | UNPROTECTED | PARITY
    ;

stripingClause
    : (FINE | COARSE)?
    ;

diskRegionClause
    : (HOT | COLD)? (MIRRORHOT | MIRRORCOLD)?
    ;

diskgroupDirectoryClauses
    : (ADD DIRECTORY fileName (COMMA_ fileName)*
    | DROP DIRECTORY fileName (FORCE | NOFORCE)? (COMMA_ fileName (FORCE | NOFORCE)?)*
    | RENAME DIRECTORY directoryName TO directoryName (COMMA_ directoryName TO directoryName)*)
    ;

diskgroupAliasClauses
    : ((ADD ALIAS aliasName FOR fileName (COMMA_ aliasName FOR fileName)*)
    | (DROP ALIAS aliasName (COMMA_ aliasName)*)
    | (RENAME ALIAS aliasName TO aliasName (COMMA_ aliasName TO aliasName)*))
    ;

diskgroupVolumeClauses
    : (addVolumeClause
    | modifyVolumeClause
    | RESIZE VOLUME asmVolumeName SIZE sizeClause
    | DROP VOLUME asmVolumeName)
    ;

addVolumeClause
    : ADD VOLUME asmVolumeName SIZE sizeClause redundancyClause? (STRIPE_WIDTH INTEGER_ (K | M))? (STRIPE_COLUMNS INTEGER_)? (ATTRIBUTE (diskRegionClause))?
    ;

modifyVolumeClause
    : MODIFY VOLUME asmVolumeName (ATTRIBUTE (diskRegionClause))? (MOUNTPATH mountpathName)? (USAGE usageName)?
    ;

diskgroupAttributes
    : SET ATTRIBUTE attributeName EQ_ attributeValue
    ;

modifyDiskgroupFile
    : MODIFY FILE fileName ATTRIBUTE LP_ diskRegionClause RP_ (COMMA_ fileName ATTRIBUTE ( diskRegionClause ))*
    ;

dropDiskgroupFileClause
    : DROP FILE fileName (COMMA_ fileName)*
    ;

convertRedundancyClause
    : CONVERT REDUNDANCY TO FLEX
    ;

usergroupClauses
    : (ADD USERGROUP SQ_ usergroupName SQ_ WITH MEMBER SQ_ username SQ_ (COMMA_ SQ_ username SQ_)*
    | MODIFY USERGROUP usergroupName (ADD | DROP) MEMBER username (COMMA_ username)*
    | DROP USERGROUP SQ_ usergroupName SQ_)
    ;

userClauses
    : (ADD USER username (COMMA_ username)*
    | DROP USER SQ_ username SQ_ (COMMA_ SQ_ username SQ_)* (CASCADE)?
    | REPLACE USER SQ_ username SQ_ WITH SQ_ username SQ_ (COMMA_ SQ_ username SQ_ WITH SQ_ username SQ_)*)
    ;

filePermissionsClause
    : SET PERMISSION (OWNER | GROUP | OTHER) EQ_ (NONE | READ ONLY | READ WRITE) (COMMA_ (OWNER | GROUP | OTHER | ALL)
    EQ_ (NONE | READ ONLY | READ WRITE))* FOR FILE fileName (COMMA_ fileName)*
    ;

fileOwnerClause
    : SET OWNERSHIP (setOwnerClause (COMMA_ setOwnerClause)*) FOR FILE fileName (COMMA_ fileName)*
    ;

setOwnerClause
    : OWNER EQ_ username | GROUP EQ_ usergroupName
    ;

scrubClause
    : SCRUB (FILE asmFileName | DISK diskName)? (REPAIR | NOREPAIR)?
    (POWER (AUTO | LOW | HIGH | MAX))? (WAIT | NOWAIT)? (FORCE | NOFORCE)? (STOP)?
    ;

quotagroupClauses
    : (ADD QUOTAGROUP quotagroupName (setPropertyClause)?
    | MODIFY QUOTAGROUP quotagroupName setPropertyClause
    | MOVE FILEGROUP filegroupName TO quotagroupName
    | DROP QUOTAGROUP quotagroupName)
    ;

setPropertyClause
    : SET propertyName EQ_ propertyValue
    ;

quotagroupName
    : identifier
    ;

propertyName
    : QUOTA
    ;

propertyValue
    : sizeClause | UNLIMITED
    ;

filegroupName
    : identifier
    ;

filegroupClauses
    : (addFilegroupClause
    | modifyFilegroupClause
    | moveToFilegroupClause
    | dropFilegroupClause)
    ;

addFilegroupClause
    : ADD FILEGROUP filegroupName (DATABASE databaseName
    | CLUSTER clusterName
    | VOLUME asmVolumeName) (setFileTypePropertyclause)?
    ;

setFileTypePropertyclause
    :SET SQ_ (fileType DOT_)? propertyName SQ_ EQ_ SQ_ propertyValue SQ_
    ;

modifyFilegroupClause
    : MODIFY FILEGROUP filegroupName setFileTypePropertyclause
    ;

moveToFilegroupClause
    : MOVE FILE asmFileName TO FILEGROUP filegroupName
    ;

dropFilegroupClause
    : DROP FILEGROUP filegroupName (CASCADE)?
    ;

undropDiskClause
    : UNDROP DISKS
    ;

diskgroupAvailability
    : ((MOUNT (RESTRICTED | NORMAL)? (FORCE | NOFORCE)?) | (DISMOUNT (FORCE | NOFORCE)?))
    ;

enableDisableVolume
    : (ENABLE | DISABLE) VOLUME (asmVolumeName (COMMA_ asmVolumeName)* | ALL)
    ;

alterIndexType
    : ALTER INDEXTYPE indexTypeName ((addOrDropClause (COMMA_ addOrDropClause)* usingTypeClause?) | COMPILE) withLocalClause
    ;

addOrDropClause
    : (ADD | DROP) operatorName LP_ parameterType RP_
    ;

usingTypeClause
    : USING implementationType arrayDMLClause?
    ;

withLocalClause
    : (WITH LOCAL RANGE? PARTITION)? storageTableClause?
    ;

arrayDMLClause
    : (WITH | WITHOUT)? ARRAY DML arryDMLSubClause (COMMA_ arryDMLSubClause)*
    ;

arryDMLSubClause
    : LP_ typeName (COMMA_ varrayType)? RP_
    ;

// DM  https://eco.dameng.com/document/dm/zh-cn/pm/materialized-view.html#7.1%20%E7%89%A9%E5%8C%96%E8%A7%86%E5%9B%BE%E7%9A%84%E5%AE%9A%E4%B9%89
createMaterializedView
    : CREATE MATERIALIZED VIEW materializedViewName (OF typeName )? materializedViewColumnClause? (materializedViewPrebuiltClause) materializedViewUsingClause? createMvRefresh? (FOR UPDATE)? ( (DISABLE | ENABLE) QUERY REWRITE )? AS selectSubquery
    ;

materializedViewColumnClause
    : ( LP_ (scopedTableRefConstraint | mvColumnAlias) (COMMA_ (scopedTableRefConstraint | mvColumnAlias))* RP_ )
    ;

materializedViewPrebuiltClause
    : (ON PREBUILT TABLE ( (WITH | WITHOUT) REDUCED PRECISION)? | physicalProperties?  (CACHE | NOCACHE)? parallelClause? buildClause?)
    ;

materializedViewUsingClause
    : ( USING INDEX ( (physicalAttributesClause | TABLESPACE tablespaceName)+ )* | USING NO INDEX)
    ;

mvColumnAlias
    : (identifier | quotedString) (ENCRYPT encryptionSpecification)?
    ;

createMvRefresh
    : ( NEVER REFRESH | REFRESH createMvRefreshOptions+)
    ;

createMvRefreshOptions
    : (FAST | COMPLETE | FORCE) | ON (DEMAND | COMMIT | STATEMENT) | ((START WITH | NEXT) dateValue)+ | WITH (PRIMARY KEY | ROWID) | USING ( DEFAULT (MASTER | LOCAL)? ROLLBACK SEGMENT | (MASTER | LOCAL)? ROLLBACK SEGMENT rb_segment=identifier ) | USING (ENFORCED | TRUSTED) CONSTRAINTS
    ;

quotedString
    : variableName
    ;

buildClause
    : BUILD (IMMEDIATE | DEFERRED)
    ;

createMaterializedViewLog
    : CREATE MATERIALIZED VIEW LOG ON tableName materializedViewLogAttribute? parallelClause? storageClause? ( WITH ( ( COMMA_? ( OBJECT ID | PRIMARY KEY | ROWID | SEQUENCE | COMMIT SCN ) ) | (LP_ columnName ( COMMA_ columnName )* RP_ ) )* )? newViewValuesClause? mvLogPurgeClause? createMvRefresh? ((FOR UPDATE)? ( (DISABLE | ENABLE) QUERY REWRITE )? AS selectSubquery)?
    ;

materializedViewLogAttribute
    : ( ( physicalAttributesClause | TABLESPACE tablespaceName | loggingClause | (CACHE | NOCACHE))+ )
    ;

newViewValuesClause
    : (INCLUDING | EXCLUDING ) NEW VALUES
    ;

alterMaterializedView
    : ALTER MATERIALIZED VIEW materializedViewName materializedViewAttribute? alterIotClauses? (USING INDEX physicalAttributesClause)?
    ((MODIFY scopedTableRefConstraint) | alterMvRefresh)? evaluationEditionClause?
    ((ENABLE | DISABLE) ON QUERY COMPUTATION)? (alterQueryRewriteClause | COMPILE | CONSIDER FRESH)?
    ;

materializedViewAttribute
    : physicalAttributesClause
    | modifyMvColumnClause
    | tableCompression
    | inmemoryTableClause
    | lobStorageClause (COMMA_ lobStorageClause)*
    | modifylobStorageClause (COMMA_ modifylobStorageClause)*
    | alterTablePartitioning
    | parallelClause
    | loggingClause
    | allocateExtentClause
    | deallocateUnusedClause
    | shrinkClause
    | CACHE
    | NOCACHE
    ;

modifyMvColumnClause
    : MODIFY LP_ columnName ((ENCRYPT encryptionSpecification) | DECRYPT)? RP_
    ;

modifylobStorageClause
    : MODIFY LOB LP_ lobItem RP_ LP_ modifylobParameters+ RP_
    ;

modifylobParameters
    : storageClause
    | PCTVERSION INTEGER_
    | FREEPOOLS INTEGER_
    | REBUILD FREEPOOLS
    | lobRetentionClause
    | lobDeduplicateClause
    | lobCompressionClause
    | ENCRYPT encryptionSpecification
    | DECRYPT
    | CACHE
    | (NOCACHE | (CACHE READS)) loggingClause?
    | allocateExtentClause
    | shrinkClause
    | deallocateUnusedClause
    ;

alterIotClauses
    : indexOrgTableClause
    | alterOverflowClause
    | COALESCE
    ;

alterXMLSchemaClause
    : ALLOW (ANYSCHEMA | NONSCHEMA) | DISALLOW NONSCHEMA
    ;

alterOverflowClause
    : addOverflowClause | overflowClause
    ;

overflowClause
    : OVERFLOW (segmentAttributesClause | allocateExtentClause | shrinkClause | deallocateUnusedClause)+
    ;

addOverflowClause
    : ADD OVERFLOW segmentAttributesClause? (LP_ PARTITION segmentAttributesClause? (COMMA_ PARTITION segmentAttributesClause?)* RP_)?
    ;

scopedTableRefConstraint
    : SCOPE FOR LP_ (columnName | attributeName) RP_ IS (schemaName DOT_)? (tableName | alias)
    ;

alterMvRefresh
    : REFRESH ( ((FAST | COMPLETE | FORCE) (START WITH dateValue)? (NEXT dateValue)?)
    | ON DEMAND
    | ON COMMIT
    | START WITH dateValue
    | NEXT dateValue
    | WITH PRIMARY KEY
    | USING DEFAULT (MASTER ROLLBACK SEGMENT)?
    | USING MASTER ROLLBACK SEGMENT rollbackSegment
    | USING ENFORCED CONSTRAINTS
    | USING TRUSTED CONSTRAINTS)
    ;

evaluationEditionClause
    : EVALUATE USING (CURRENT EDITION | EDITION editionName | NULL EDITION)
    ;

alterQueryRewriteClause
    : (ENABLE | DISABLE)? QUERY REWRITE unusableEditionsClause
    ;

unusableEditionsClause
    : unusableBefore? unusableBeginning?
    ;

unusableBefore
    : UNUSABLE BEFORE (CURRENT EDITION | EDITION editionName)
    ;

unusableBeginning
    : UNUSABLE BEGINNING WITH (CURRENT EDITION | EDITION editionName | NULL EDITION)
    ;

alterMaterializedViewLog
    : ALTER MATERIALIZED VIEW LOG FORCE? ON tableName
    ( physicalAttributesClause
    | addMvLogColumnClause
    | alterTablePartitioning
    | parallelClause
    | loggingClause
    | allocateExtentClause
    | shrinkClause
    | moveMvLogClause
    | CACHE
    | NOCACHE)? mvLogAugmentation? mvLogPurgeClause? forRefreshClause?
    ;

addMvLogColumnClause
    : ADD LP_ columnName RP_
    ;

moveMvLogClause
    : MOVE segmentAttributesClause parallelClause?
    ;

mvLogAugmentation
    : ADD addClause (COMMA_ addClause)* newValuesClause?
    ;

addClause
    : OBJECT ID columnNames?
    | PRIMARY KEY columnNames?
    | ROWID columnNames?
    | SEQUENCE columnNames?
    | columnNames
    ;

newValuesClause
    : (INCLUDING | EXCLUDING) NEW VALUES
    ;

mvLogPurgeClause
    : PURGE (IMMEDIATE (SYNCHRONOUS | ASYNCHRONOUS)?
    | START WITH dateValue nextOrRepeatClause?
    | (START WITH dateValue)? nextOrRepeatClause)
    ;

nextOrRepeatClause
    : NEXT dateValue | REPEAT intervalLiterals
    ;


forRefreshClause
    : FOR ((SYNCHRONOUS REFRESH USING stagingLogName) | (FAST REFRESH))
    ;

alterFunction
    : ALTER FUNCTION functionName (functionCompileClause | (EDITIONABLE | NONEDITIONABLE))
    ;

functionCompileClause
    : COMPILE CASCADE? DEBUG? compilerParametersClause* (REUSE SETTINGS)?
    ;

alterHierarchy
    : ALTER HIERARCHY hierarchyName (RENAME TO hierarchyName | COMPILE)
    ;

alterLockdownProfile
    : ALTER LOCKDOWN PROFILE profileName (lockdownFeatures | lockdownOptions | lockdownStatements)
    ;

lockdownFeatures
    : (DISABLE | ENABLE) FEATURE featureClauses
    ;

featureClauses
    : EQ_ LP_ featureName (COMMA_ featureName)* RP_
    | ALL (EXCEPT (EQ_ LP_ featureName (COMMA_ featureName)* RP_))?
    ;

lockdownOptions
    : (DISABLE | ENABLE) OPTION lockDownOptionClauses
    ;

lockDownOptionClauses
    : EQ_ LP_ optionName (COMMA_ optionName)* RP_
    | ALL (EXCEPT (EQ_ LP_ optionName (COMMA_ optionName)* RP_))?
    ;

lockdownStatements
    : (DISABLE | ENABLE) STATEMENT lockdownStatementsClauses
    ;

lockdownStatementsClauses
    : EQ_ LP_ sqlStatement (COMMA_ sqlStatement )* RP_
    | EQ_ LP_ sqlStatement RP_ statementClauses
    | ALL (EXCEPT (EQ_ LP_ sqlStatement (COMMA_ sqlStatement)* RP_))?
    ;

statementClauses
    : CLAUSE statementsSubClauses
    ;

statementsSubClauses
    : EQ_ LP_ clause (COMMA_ clause)* RP_
    | EQ_ LP_ clause RP_ clauseOptions
    | ALL (EXCEPT (EQ_ LP_ clause (COMMA_ clause)* RP_))?
    ;

clauseOptions
    : OPTION optionClauses
    ;

optionClauses
    : EQ_ LP_ clauseOptionOrPattern (COMMA_ clauseOptionOrPattern)* RP_
    | EQ_ LP_ clauseOption RP_ optionValues+
    | ALL (EXCEPT EQ_ LP_ clauseOptionOrPattern (COMMA_ clauseOptionOrPattern)* RP_)?
    ;

clauseOptionOrPattern
    : clauseOption | clauseOptionPattern
    ;

optionValues
    : VALUE EQ_ LP_ optionValue (COMMA_ optionValue)* RP_
    | MINVALUE EQ_ optionValue
    | MAXVALUE EQ_ optionValue
    ;

alterPluggableDatabase
    : ALTER databaseClause (pdbUnplugClause
    | pdbSettingsClauses
    | pdbDatafileClause
    | pdbRecoveryClauses
    | pdbChangeState
    | pdbChangeStateFromRoot
    | applicationClauses
    | snapshotClauses
    | prepareClause
    | dropMirrorCopy
    | lostWriteProtection)
    ;

databaseClause
    : DATABASE dbName?
    | PLUGGABLE DATABASE pdbName?
    ;

pdbUnplugClause
    : pdbName UNPLUG INTO fileName pdbUnplugEncrypt?
    ;

pdbUnplugEncrypt
    : ENCRYPT USING transportSecret
    ;

pdbSettingsClauses
    : pdbName? pdbSettingClause
    | CONTAINERS (DEFAULT TARGET EQ_ ((LP_ containerName RP_) | NONE) | HOST EQ_ hostName | PORT EQ_ numberValue)
    ;

pdbSettingClause
    : DEFAULT EDITION EQ_ editionName
    | SET DEFAULT (BIGFILE | SMALLFILE) TABLESPACE
    | DEFAULT TABLESPACE tablespaceName
    | DEFAULT TEMPORARY TABLESPACE (tablespaceName | tablespaceGroupName)
    | RENAME GLOBAL_NAME TO databaseName (DOT_ domain)+
    | setTimeZoneClause
    | databaseFileClauses
    | supplementalDbLogging
    | pdbStorageClause
    | pdbLoggingClauses
    | pdbRefreshModeClause
    | REFRESH pdbRefreshSwitchoverClause?
    | SET CONTAINER_MAP EQ_ mapObject
    ;

pdbStorageClause
    : STORAGE ((LP_ storageMaxSizeClauses+ RP_) | UNLIMITED)
    ;

storageMaxSizeClauses
    : (MAXSIZE | MAX_AUDIT_SIZE | MAX_DIAG_SIZE) (UNLIMITED | sizeClause)
    ;

pdbLoggingClauses
    : loggingClause | pdbForceLoggingClause
    ;

pdbForceLoggingClause
    : (ENABLE | DISABLE) FORCE (LOGGING | NOLOGGING)
    | SET STANDBY NOLOGGING FOR ((DATA AVAILABILITY) | (LOAD PERFORMANCE))
    ;

pdbRefreshModeClause
    : REFRESH MODE (MANUAL | (EVERY refreshInterval (MINUTES | HOURS)) | NONE )
    ;

pdbRefreshSwitchoverClause
    : FROM sourcePdbName dbLinkClause SWITCHOVER
    ;

pdbDatafileClause
    : pdbName? DATAFILE (fileNameAndNumber | ALL)  (ONLINE | OFFLINE)
    ;

fileNameAndNumber
    : (fileName | fileNumber) (COMMA_ (fileName | fileNumber))*
    ;

pdbRecoveryClauses
    : pdbName? (pdbGeneralRecovery
    | BEGIN BACKUP
    | END BACKUP
    | ENABLE RECOVERY
    | DISABLE RECOVERY)
    ;

pdbGeneralRecovery
    : RECOVER AUTOMATIC? (FROM locationName)? (DATABASE
    | TABLESPACE tablespaceName (COMMA_ tablespaceName)*
    | DATAFILE fileNameAndNumber
    | LOGFILE fileName
    | CONTINUE DEFAULT?)?
    ;

pdbChangeState
    : pdbName? (pdbOpen | pdbClose | pdbSaveOrDiscardState)
    ;

pdbOpen
    : OPEN (((READ WRITE) | (READ ONLY))? RESTRICTED? FORCE?
    | (READ WRITE)? UPGRADE RESTRICTED?
    | RESETLOGS) instancesClause?
    ;

instancesClause
    : INSTANCES EQ_ (instanceNameClause | (ALL (EXCEPT instanceName)?))
    ;

instanceNameClause
    : LP_ instanceName (COMMA_ instanceName )* RP_
    ;

pdbClose
    : CLOSE ((IMMEDIATE? (instancesClause | relocateClause)?) | (ABORT? instancesClause?))
    ;

relocateClause
    : RELOCATE (TO instanceName)?
    | NORELOCATE
    ;

pdbSaveOrDiscardState
    : (SAVE | DISCARD) STATE instancesClause?
    ;

pdbChangeStateFromRoot
    : (pdbNameClause | (ALL (EXCEPT pdbNameClause)?)) (pdbOpen | pdbClose | pdbSaveOrDiscardState)
    ;

pdbNameClause
    : pdbName (COMMA_ pdbName)*
    ;

applicationClauses
    : APPLICATION ((appName appClause) | (ALL SYNC))
    ;

appClause
    : BEGIN INSTALL SQ_ appVersion SQ_ (COMMENT SQ_ commentValue SQ_)?
    | END INSTALL (SQ_ appVersion SQ_)?
    | BEGIN PATCH numberValue (MINIMUM VERSION SQ_ appVersion SQ_)? (COMMENT SQ_ commentValue SQ_)?
    | END PATCH numberValue?
    | BEGIN UPGRADE (SQ_ startAppVersion SQ_)? TO SQ_ endAppVersion SQ_ (COMMENT SQ_ commentValue SQ_)?
    | END UPGRADE (TO SQ_ endAppVersion SQ_)?
    | BEGIN UNINSTALL
    | END UNINSTALL
    | SET PATCH numberValue
    | SET VERSION SQ_ appVersion SQ_
    | SET COMPATIBILITY VERSION ((SQ_ appVersion SQ_) | CURRENT)
    | SYNC TO ((SQ_ appVersion SQ_) | (PATCH patchNumber))
    | SYNC
    ;

snapshotClauses
    : pdbSnapshotClause
    | materializeClause
    | createSnapshotClause
    | dropSnapshotClause
    | setMaxPdbSnapshotsClause
    ;

pdbSnapshotClause
    : SNAPSHOT (MANUAL | (EVERY snapshotInterval (HOURS | MINUTES)) | NONE)
    ;

materializeClause
    : MATERIALIZE
    ;

createSnapshotClause
    : SNAPSHOT snapshotName
    ;

dropSnapshotClause
    : DROP SNAPSHOT snapshotName
    ;

setMaxPdbSnapshotsClause
    : SET maxPdbSnapshots EQ_ maxNumberOfSnapshots
    ;

dropIndexType
    : DROP INDEXTYPE indexTypeName FORCE?
    ;

dropProfile
    : DROP PROFILE ifExists? profileName cascadeOrRestrict?
    ;

dropPluggableDatabase
    : DROP PLUGGABLE DATABASE pdbName ((KEEP | INCLUDING) DATAFILES)?
    ;

dropSequence
    : DROP SEQUENCE ifExists? sequenceName
    ;

dropJava
     : DROP JAVA (SOURCE | CLASS | RESOURCE) objectName
     ;

dropLibrary
    : DROP LIBRARY libraryName
    ;

dropMaterializedView
    : DROP MATERIALIZED VIEW ifExists? materializedViewName (PRESERVE TABLE)?
    ;

dropMaterializedViewLog
    : DROP MATERIALIZED VIEW LOG ON tableName
    ;

dropMaterializedZonemap
    : DROP MATERIALIZED ZONEMAP zonemapName
    ;

tablespaceEncryptionSpec
    : USING encryptAlgorithmName
    ;

segmentManagementClause
    : SEGMENT SPACE MANAGEMENT (AUTO|MANUAL)
    ;

tablespaceGroupClause
    : TABLESPACE GROUP (tablespaceGroupName | SQ_ SQ_)
    ;

temporaryTablespaceClause
    : TEMPORARY TABLESPACE tablespaceName (TEMPFILE fileSpecification (COMMA_ fileSpecification)* )? tablespaceGroupClause? extentManagementClause?
    ;

tablespaceRetentionClause
    : RETENTION (GUARANTEE | NOGUARANTEE)
    ;

undoTablespaceClause
    : UNDO TABLESPACE tablespaceName (DATAFILE fileSpecification (COMMA_ fileSpecification)*)? extentManagementClause? tablespaceRetentionClause?
    ;

createTablespace
    : CREATE (BIGFILE|SMALLFILE)?  (permanentTablespaceClause | temporaryTablespaceClause | undoTablespaceClause)
    ;

permanentTablespaceClause
    : TABLESPACE tablespaceName (DATAFILE fileSpecifications)? permanentTablespaceAttrs* (IN SHARDSPACE shardspace=identifier)?
    ;
permanentTablespaceAttrs
    : MINIMUM EXTEND sizeClause
    | BLOCKSIZE INTEGER_ capacityUnit?
    | loggingClause
    | FORCE LOGGING
    | tablespaceEncryptionClause
    | defaultTablespaceParams
    | (ONLINE|OFFLINE)
    | extentManagementClause
    | segmentManagementClause
    | flashbackModeClause
    | lostWriteProtection
    ;

tablespaceEncryptionClause
    : ENCRYPTION (tablespaceEncryptionSpec? ENCRYPT | DECRYPT)
    ;

alterTablespace
    : ALTER TABLESPACE tablespaceName
    ( defaultTablespaceParams
    | MINIMUM EXTENT sizeClause
    | RESIZE sizeClause
    | COALESCE
    | SHRINK SPACE (KEEP sizeClause)?
    | RENAME TO newTablespaceName
    | (BEGIN | END) BACKUP
    | datafileTempfileClauses
    | tablespaceLoggingClauses
    | tablespaceGroupClause
    | tablespaceStateClauses
    | autoextendClause
    | flashbackModeClause
    | tablespaceRetentionClause
    | alterTablespaceEncryption
    | lostWriteProtection
    )
    ;

defaultTablespaceParams
    : DEFAULT (defaultTableCompression | defaultIndexCompression | inmemoryClause | ilmClause | storageClause | tableCompression)+
    ;

defaultTableCompression
    : TABLE (COMPRESS FOR OLTP | COMPRESS FOR QUERY (LOW | HIGH) | COMPRESS FOR ARCHIVE (LOW | HIGH) | NOCOMPRESS)
    ;

defaultIndexCompression
    : INDEX (COMPRESS ADVANCED (LOW | HIGH) | NOCOMPRESS)
    ;

datafileTempfileClauses
    : ADD (DATAFILE | TEMPFILE) (fileSpecification (COMMA_ fileSpecification)*)?
    | DROP (DATAFILE | TEMPFILE) (fileName | fileNumber)
    | SHRINK TEMPFILE (fileName | fileNumber) (KEEP sizeClause)?
    | RENAME DATAFILE fileName (COMMA_ fileName)* TO fileName (COMMA_ fileName)*
    | (DATAFILE | TEMPFILE) (ONLINE | OFFLINE)
    ;

tablespaceLoggingClauses
    : loggingClause | NO? FORCE LOGGING
    ;

tablespaceStateClauses
    : ONLINE | OFFLINE (NORMAL | TEMPORARY | IMMEDIATE)? | READ (ONLY | WRITE) | (PERMANENT | TEMPORARY)
    ;

tablespaceFileNameConvert
    : FILE_NAME_CONVERT EQ_ LP_ filenamePattern COMMA_ replacementFilenamePattern (COMMA_ filenamePattern COMMA_ replacementFilenamePattern)* RP_ KEEP?
    ;

alterTablespaceEncryption
    : ENCRYPTION(OFFLINE (tablespaceEncryptionSpec? ENCRYPT | DECRYPT)
    | ONLINE (tablespaceEncryptionSpec? (ENCRYPT | REKEY) | DECRYPT) tablespaceFileNameConvert?
    | FINISH (ENCRYPT | REKEY | DECRYPT) tablespaceFileNameConvert?)
    ;

dropFunction
    : DROP FUNCTION ifExists? functionName
    ;

compileTypeClause
    : COMPILE DEBUG? (SPECIFICATION|BODY)? compilerParametersClause? (REUSE SETTINGS)?
    ;

inheritanceClauses
    : (NOT? (OVERRIDING | FINAL | INSTANTIABLE))+
    ;

procedureSpec
    : PROCEDURE procedureName parameterDeclarationList? ((IS | AS) callSpec)?
    ;

returnClause
    : RETURN dataType ((IS | AS) callSpec)?
    ;
// #26092
functionSpec
    : FUNCTION funName parameterDeclarationList? returnClause
    ;

// 适配DM方法声明可为空
subprogramSpec
    : (MEMBER | STATIC)? (procedureSpec | functionSpec)
    ;

constructorSpec
    : FINAL? INSTANTIABLE? CONSTRUCTOR FUNCTION typeName
    (LP_ (SELF IN OUT dataType COMMA_)? parameterDeclaration (COMMA_ parameterDeclaration)* RP_)?
    RETURN SELF AS RESULT ((AS | IS) callSpec)?
    ;

// 适配DM方法声明可为空
mapOrderFunctionSpec
    : (MAP | ORDER) MEMBER functionSpec
    ;

restrictReferencesPragma
    : PRAGMA RESTRICT_REFERENCES
    LP_ (subprogramName | methodName | DEFAULT) COMMA_
    (RNDS | WNDS | RNPS | WNPS | TRUST)
    (COMMA_ (RNDS | WNDS | RNPS | WNPS | TRUST))* RP_
    ;

elementSpecification
    : inheritanceClauses? specificationClause+ (COMMA_ restrictReferencesPragma)?
    ;
specificationClause
    : subprogramSpec | constructorSpec | mapOrderFunctionSpec
    ;
replaceTypeClause
    : REPLACE invokerRightsClause? AS OBJECT LP_ (attributeName dataType (COMMA_ (elementSpecification | attributeName dataType))*) RP_
    ;

alterMethodSpec
    : (ADD | DROP) (mapOrderFunctionSpec | subprogramSpec) ((ADD | DROP) (mapOrderFunctionSpec | subprogramSpec))*
    ;

alterAttributeDefinition
    : (ADD | MODIFY) ATTRIBUTE ( attributeName dataType? | LP_ attributeName dataType (COMMA_ attributeName dataType)* RP_)
      | DROP ATTRIBUTE ( attributeName | LP_ attributeName (COMMA_ attributeName)* RP_)
    ;

alterCollectionClauses
    : MODIFY (LIMIT INTEGER_ | ELEMENT TYPE dataType)
    ;

dependentHandlingClause
    : INVALIDATE | CASCADE (NOT? INCLUDING TABLE DATA | CONVERT TO SUBSTITUTABLE)? (FORCE? exceptionsClause)?
    ;

alterType
    : ALTER TYPE typeName (compileTypeClause | replaceTypeClause | RESET
    | (alterMethodSpec | alterAttributeDefinition | alterCollectionClauses | NOT? (INSTANTIABLE | FINAL)) dependentHandlingClause?)
    ;

createCluster
    : CREATE CLUSTER (schemaName DOT_)? clusterName LP_ (columnName dataType (COLLATE columnCollationName)? SORT? (COMMA_ columnName dataType (COLLATE columnCollationName)? SORT?)*) RP_
    ( physicalAttributesClause | SIZE sizeClause | TABLESPACE tablespaceName | INDEX | (SINGLE TABLE)? HASHKEYS INTEGER_ (HASH IS expr)?)* parallelClause?
    ( NOROWDEPENDENCIES | ROWDEPENDENCIES)? (CACHE | NOCACHE)? clusterRangePartitions?
    ;

clusterRangePartitions
    : PARTITION BY RANGE columnNames LP_ (PARTITION partitionName? rangeValuesClause tablePartitionDescription) (COMMA_ (PARTITION partitionName? rangeValuesClause tablePartitionDescription))* RP_
    ;

// Oracle https://docs.oracle.com/en/database/oracle/oracle-database/23/sqlrf/CREATE-JAVA.html
createJava
    : CREATE (OR REPLACE)? (AND (RESOLVE | COMPILE))? NOFORCE? JAVA ifNotExists?
    ((SOURCE | RESOURCE) NAMED (schemaName DOT_)? primaryName
    | CLASS (SCHEMA schemaName)?) sharingClause? invokerRightsClause? resolveClauses?
    (javaUsingClause | AS sourceText)
    ;

javaUsingClause
    : USING (type=(BFILE | CLOB | BLOB) (LP_ directoryName COMMA_ serverFileName RP_ | subquery) | BQ_ keyForBlob BQ_)
    ;

createLibrary
    : CREATE (OR REPLACE)? (EDITIONABLE | NONEDITIONABLE)? LIBRARY plsqlLibrarySource
    ;

plsqlLibrarySource
    : libraryName sharingClause? (IS | AS) (fullPathName | (fileName IN directoryObject)) agentClause
    ;

agentClause
    : (AGENT agentDblink)? (CREDENTIAL credentialName)?
    ;

switch
    : SWITCH switchClause TO COPY
    ;

switchClause
    : DATABASE
    | DATAFILE datafileSpecClause (COMMA_ datafileSpecClause)*
    | TABLESPACE SQ_? tablespaceName SQ_? (COMMA_ SQ_? tablespaceName SQ_?)*
    ;

datafileSpecClause
    : SQ_ fileName SQ_ | INTEGER_
    ;

createProfile
    : CREATE MANDATORY? PROFILE profileName LIMIT (resourceParameters | passwordParameters)+ (CONTAINER EQ_ (CURRENT | ALL))?
    ;

noAudit
    : noAuditTraditional | noAuditUnified
    ;

noAuditTraditional
    : NOAUDIT (auditOperationClause auditingByClause? |  auditSchemaObjectClause | NETWORK | DIRECT_PATH LOAD auditingByClause?)
    (WHENEVER NOT? SUCCESSFUL)? (CONTAINER EQ_ (CURRENT | ALL))?
    ;

dropDatabase
    : DROP DATABASE (INCLUDING BACKUPS)? NOPROMPT?
    ;

call
    : CALL (routineCaluse | objectAccessExpression) (INTO ':' hostVariable=name (INDICATOR? ':' indicatorVariable=name)?)? SEMI_?
    ;

routineCaluse
    : ((packageName | typeName) DOT_)? (procedureName | functionName | methodName) dbLinkClause? LP_ (parameterValue (COMMA_ parameterValue)*)? RP_
    ;

alterProcedure
    : ALTER PROCEDURE procedureName (procedureCompileClause | (EDITIONABLE | NONEDITIONABLE))
    ;

procedureCompileClause
    : COMPILE CASCADE? DEBUG? (compilerParametersClause)* (REUSE SETTINGS)?
    ;

dropProcedure
    : DROP PROCEDURE procedureName
    ;

createProcedure
    : CREATE (OR REPLACE)? (EDITIONABLE | NONEDITIONABLE)? PROCEDURE ifNotExists? plsqlProcedureSource
    ;

plsqlProcedureSource
    : (schemaName DOT_)? name (
        parameterDeclarationList? sharingClause? (defaultCollationClause | invokerRightsClause | accessibleByClause)* (IS | AS) (callSpec | declareSection? body)
        | WRAPPED stringLiterals   // DM
    )
    ;

createFunction
    : CREATE (OR REPLACE)? (EDITIONABLE | NONEDITIONABLE)? FUNCTION ifNotExists? plsqlFunctionSource
    ;

// DM https://eco.dameng.com/document/dm/zh-cn/pm/trigger.html
createTrigger
    : CREATE (OR REPLACE)? (EDITIONABLE | NONEDITIONABLE)? TRIGGER plsqlTriggerSource
    ;

plsqlFunctionSource
    : (schemaName DOT_)? name (parameterDeclarationList? returnDateType?
    sharingClause? (invokerRightsClause
    | accessibleByClause
    | defaultCollationClause
    | deterministicClause
    | parallelEnableClause
    | resultCacheClause
    | aggregateClause
    | pipelinedClause
    | sqlMacroClause)*
    (
        (IS | AS) (callSpec | declareSection? body)
        | EXTERNAL pathString AND? alias? USING (SINGLE_C | JAVA)  //DM  https://eco.dameng.com/document/dm/zh-cn/pm/external-function.html#10.1.2%20C%20%E5%A4%96%E9%83%A8%E5%87%BD%E6%95%B0%E5%88%9B%E5%BB%BA
    ))
    ;

returnDateType
    : RETURN dataType
    ;

body
    : BEGIN statementList (EXCEPTION (exceptionHandler)+)? END (identifier)? SEMI_
    ;
statementList
    : statement+
    ;
statement
    : (SIGNED_LEFT_SHIFT_ label SIGNED_RIGHT_SHIFT_)*
        (assignStatement
        | basicLoopStatement
        | caseStatement
        | closeStatement
        | collectionMethodCall
        | continueStatement
        | cursorForLoopStatement
        | executeImmediateStatement
        | exitStatement
        | fetchStatement
        | forLoopStatement
        | forallStatement
        | gotoStatement
        | ifStatement
        | modifyingStatement // ?
        | nullStatement
        | openStatement
        | openForStatement
        | pipeRowStatement
        | plsqlBlock
        | raiseStatement
        | returnStatement
        | sqlStatementInPlsql
        | whileLoopStatement
        | procedureCall
        )
    ;

assignStatement
    : assignStatementTarget ASSIGNMENT_OPERATOR_ expression SEMI_
    | assignStatementTarget EQ_ expression SEMI_      // DM
    ;

assignStatementTarget
    : // collectionVariable (LP_ INTEGER_ RP_)?
    // cursor_variable, out_parameter, scalar_variable
//    | name
     placeholder
//    | hostCursorVariable
    // object.attribute, record_variable.field
    | attributeName  (LP_ INTEGER_ RP_)?
    ;

placeholder
    : COLON_ hostVariable=name (DOT_ columnName)? (COLON_ indicatorVariable=name)?
    ;

expression
    : expr
    ;

booleanExpression
    : NOT? booleanPrimary ((AND | OR) NOT? booleanPrimary)*
    ;

basicLoopStatement
    : (SIGNED_LEFT_SHIFT_ label SIGNED_RIGHT_SHIFT_)?
    LOOP statementList END LOOP label? SEMI_
    ;

caseStatement
    : simpleCaseStatement | searchedCaseStatement
    ;

simpleCaseStatement
    : (SIGNED_LEFT_SHIFT_ label SIGNED_RIGHT_SHIFT_)?
    CASE selector=expression
    (WHEN booleanExpression THEN (statementList | expr))+
    (ELSE statementList)?
    END CASE label? SEMI_
    ;

searchedCaseStatement
    : (SIGNED_LEFT_SHIFT_ label SIGNED_RIGHT_SHIFT_)?
    CASE
    (WHEN booleanExpression THEN (statementList | expr))+
    (ELSE statementList)?
    END CASE label? SEMI_
    ;

closeStatement
    : CLOSE (cursor | cursorVariable | hostCursorVariable) SEMI_
    ;
collectionMethodCall
    : collectionVariable '.' (
    COUNT | FIRST | LAST | LIMIT
    | (EXTEND | DELETE) '(' NUMBER (COMMA_ NUMBER)* ')'
    | (EXISTS | NEXT | PRIOR) '(' NUMBER ')'
    | TRIM ('(' NUMBER ')')?
    )
    ;
continueStatement
    : CONTINUE label? (WHEN booleanExpression)? SEMI_
    ;

cursorForLoopStatement
    : FOR record IN
    (cursor (LP_ actualCursorParameter (COMMA_? actualCursorParameter)* RP_)?
    | LP_ select RP_
    )
    LOOP statementList END LOOP label? SEMI_
    ;

executeImmediateStatement
    : EXECUTE IMMEDIATE dynamicSqlStmt
        ((intoClause | bulkCollectIntoClause) plsqlUsingClause?
        | plsqlUsingClause dynamicReturningClause?
        | dynamicReturningClause
        )? SEMI_
    ;

dynamicReturningClause
    : (RETURNING | RETURN) (intoClause | bulkCollectIntoClause)
    ;

exitStatement
    : EXIT label? (WHEN booleanExpression)? SEMI_
    ;

fetchStatement
    : FETCH (cursor | cursorVariable | hostCursorVariable)
    (intoClause | bulkCollectIntoClause (LIMIT expression)?) SEMI_
    ;

forLoopStatement
    : (SIGNED_LEFT_SHIFT_ label SIGNED_RIGHT_SHIFT_)?
    FOR iterator
        LOOP statementList
    END LOOP label? SEMI_
    ;

iterator
    : iterandDecl (COMMA_ iterandDecl)* IN iterationCtlSeq
    ;

iterandDecl
    : plsIdentifier=identifier (MUTABLE | IMMUTABLE)? constrainedType=dataType?
    ;

iterationCtlSeq
    : qualIterationCtl (COMMA_ qualIterationCtl)*
    ;

modifyingExpression: INSERTING | DELETING | UPDATING;

qualIterationCtl
    : REVERSE? iterationCcontrol predClauseSeq
    ;

iterationCcontrol
    : steppedControl
    | singleExpressionControl
    | valuesOfControl
    | indicesOfControl
    | pairsOfControl
    | cursorIterationControl
    ;

predClauseSeq
    : (WHILE booleanExpression)? (WHEN booleanExpression)?
    ;

steppedControl
    : lowerBound RANGE_OPERATOR_ upperBound (BY step=expression)?
    ;

singleExpressionControl
    : REPEAT? expression
    ;

valuesOfControl
    : VALUES OF
    (expression
    | cursorVariable
    | LP_ cursorObject | dynamicSql | sqlStatementInPlsql RP_
    )
    ;

indicesOfControl
    : INDICES OF
    (expression
    | cursorVariable
    | LP_ cursorObject | cursorVariable | dynamicSql | sqlStatementInPlsql RP_
    )
    ;

pairsOfControl
    : PAIRS OF
    (expression
    | cursorVariable
    | LP_ cursorObject | dynamicSql | sqlStatementInPlsql RP_
    )
    ;

cursorIterationControl
    : LP_ cursorObject | cursorVariable | dynamicSql | sqlStatementInPlsql RP_
    ;

dynamicSql
    : EXECUTE IMMEDIATE dynamicSqlStmt (USING IN? (bindArgument COMMA_?)* )?
    ;

cursorObject
    : variableName
    ;

forallStatement
    : FORALL index=name IN boundsClause (SAVE EXCEPTIONS)? dmlStatement SEMI_
    ;

boundsClause
    : lowerBound RANGE_OPERATOR_ upperBound
    | INDICES OF collection=name (BETWEEN lowerBound AND upperBound)?
    | VALUES OF indexCollection=name
    ;

lowerBound
    : expression
    ;

upperBound
    : expression
    ;

dmlStatement
    : insert | update | delete | merge | dynamicSqlStmt
    ;

dynamicSqlStmt
    : expression
    ;

gotoStatement
    : GOTO label SEMI_
    ;

ifStatement
    : IF booleanExpression THEN statementList
    (ELSIF booleanExpression THEN statementList)*
    (ELSE statementList)?
    END IF SEMI_
    ;

modifyingStatement: IF modifyingExpression THEN statementList (ELSIF modifyingExpression THEN statementList)* (ELSE statementList)? END IF SEMI_;

nullStatement
    : NULL SEMI_
    ;

openStatement
    : OPEN cursor (LP_ actualCursorParameter (COMMA_? actualCursorParameter)* RP_)? SEMI_
    ;

cursor
    : variableName
    ;

openForStatement
    : OPEN (cursorVariable | hostCursorVariable) FOR (select | dynamicSqlStmt) plsqlUsingClause? SEMI_
    ;

cursorVariable
    : variableName
    ;

plsqlUsingClause
    : USING (IN | OUT | IN OUT)? bindArgument (COMMA_? (IN | OUT | IN OUT)? bindArgument)*
    ;

bindArgument
    : expression
    ;

pipeRowStatement
    : PIPE ROW LP_ row=expression RP_ SEMI_
    ;

plsqlBlock
    : (SIGNED_LEFT_SHIFT_ label SIGNED_RIGHT_SHIFT_)* (DECLARE declareSection?)? body
    ;

procedureCall
    : (packageName DOT_)? procedureName (LP_ (parame=expression (COMMA_ parame=expression)*)? RP_)? SEMI_
    ;

raiseStatement
    : RAISE name? SEMI_
    ;

returnStatement
    : RETURN expression? SEMI_
    ;


intoClause
    : INTO variableName (COMMA_ variableName)*
    ;

record
    : name
    ;

bulkCollectIntoClause
    : BULK COLLECT INTO (collection=name | hostArray)
    ;

hostArray
    : COLON_ variableName
    ;

hostCursorVariable
    : COLON_ variableName
    ;

actualCursorParameter
    : expression
    ;

sqlStatementInPlsql
    : (commit
    | collectionMethodCall
    | delete
    | insert
    | lockTable
    | merge
    | select
    | rollback
    | savepoint
    | setTransaction
    | update
    ) SEMI_
    ;

whileLoopStatement
    : WHILE booleanExpression
    LOOP statementList END LOOP label? SEMI_
    ;

exceptionHandler
    : WHEN ((typeName (OR typeName)*)| OTHERS) THEN statementList
    ;

declareSection
    : declareItem+
    ;

declareItem
    : typeDefinition
    | cursorDeclaration
    | itemDeclaration
    | functionDeclaration
    | procedureDeclaration
    | cursorDefinition
    | functionDefinition
    | procedureDefinition
    | pragma
    ;

cursorDefinition
    : CURSOR variableName cursorParameterList? (RETURN rowtype)? IS select SEMI_
    ;

functionDefinition
    : functionHeading (DETERMINISTIC | PIPELINED | PARALLEL_ENABLE | resultCacheClause)*  (IS | AS) (DECLARE? declareSection ? body | callSpec)
    ;

procedureDefinition
    : procedureHeading procedureProperties*  (IS | AS) (callSpec | DECLARE? declareSection? body)
    ;

cursorDeclaration
    : CURSOR variableName cursorParameterList? RETURN rowtype SEMI_
    ;

cursorParameterList
    : LP_ cursorParameter (COMMA_ cursorParameter)* RP_
    ;
cursorParameter
    : variableName IN? dataType ((ASSIGNMENT_OPERATOR_ | DEFAULT) expr)?
    ;

rowtype
    : typeName MOD_ ROWTYPE
    | typeName (MOD_ TYPE)?
    ;

itemDeclaration
    : constantDeclaration
    | exceptionDeclaration
    | cursorVariableDeclaration
    | collectionVariableDecl
    | recordVariableDeclaration
    | variableDeclaration
    ;

collectionVariableDecl
    : variableName
      typeName ASSIGNMENT_OPERATOR_ (qualifiedExpression | functionCall | variableName)
      SEMI_
    ;

qualifiedExpression
    : typemark LP_ explicitChoiceList? RP_
    ;

explicitChoiceList
    : expr EQ_ GT_ expr (COMMA_ expr EQ_ GT_ expr)*
    ;

typemark
    : typeName
    ;

constantDeclaration
    : variableName CONSTANT dataType (NOT NULL)? ((ASSIGNMENT_OPERATOR_ | DEFAULT) expr)? SEMI_
    ;

cursorVariableDeclaration
    : variableName typeName SEMI_
    ;

exceptionDeclaration
    : variableName EXCEPTION (FOR stringLiterals (COMMA_ stringLiterals)?)? SEMI_
    ;

recordVariableDeclaration
    : variableName (rowtypeAttribute | typeAttribute) ((ASSIGNMENT_OPERATOR_ | DEFAULT | ASSIGN) expr)? SEMI_
    ;

variableDeclaration
    : variableName dataType ((NOT NULL)? (ASSIGNMENT_OPERATOR_ | DEFAULT | ASSIGN) expr)? SEMI_
    ;

typeDefinition
    : collectionTypeDefinition | recordTypeDefinition | refCursorTypeDefinition | subtypeDefinition | otherTypeDefinition
    ;
//    type 通配规则
otherTypeDefinition
    : TYPE (typeName | dataType) (AS | IS) .*? SEMI_
    ;
recordTypeDefinition
    : TYPE typeName IS RECORD  LP_ fieldDefinition (COMMA_ fieldDefinition)* RP_ SEMI_
    ;

fieldDefinition
    : typeName dataType ((NOT NULL)? (ASSIGNMENT_OPERATOR_ | DEFAULT) expr)?
    ;

refCursorTypeDefinition
    : TYPE typeName IS REF CURSOR (RETURN typeName (MOD_ (ROWTYPE | TYPE))? )? SEMI_
    ;

subtypeDefinition
    : SUBTYPE (typeName| dataType) IS (typeName| dataType) (typeConstraint | characterSetClause)? (NOT NULL)? SEMI_?
    ;

// constraint
typeConstraint
    : (INTEGER_ COMMA_ INTEGER_) | (RANGE  (numberValue | STRING_) RANGE_OPERATOR_  (numberValue | STRING_) )
    ;

collectionTypeDefinition
    : TYPE typeName IS (assocArrayTypeDef | varrayTypeDef | nestedTableTypeDef) SEMI_
    ;

varrayTypeDef
    : (VARRAY | (VARYING? ARRAY)) LP_ INTEGER_ RP_ OF dataType (NOT NULL)?
    ;

nestedTableTypeDef
    : TABLE OF dataType (NOT NULL)?
    ;

assocArrayTypeDef
    : TABLE OF dataType (NOT NULL)?  INDEX BY (PLS_INTEGER | BINARY_INTEGER | (VARCHAR2 | VARCHAR2 | STRING) LP_ INTEGER_ RP_ | LONG | typeAttribute | rowtypeAttribute)
    ;

rowtypeAttribute
    : objectName MOD_ ROWTYPE
    ;

//    https://docs.oracle.com/en/database/oracle/oracle-database/21/lnpls/plsql-language-fundamentals.html
pragma
    : seriallyReusablePragma
    | autonomousTransPragma
    | restrictReferencesPragma SEMI_
    | exceptionInitPragma
    | inlinePragma
    | coveragePragma
    | deprecatePragma
    | suppressesWarning6009Pragma
    | udfPragma
    | otherPragma // 适配文档中不存在的 pragma 语法
    ;

exceptionInitPragma
    : PRAGMA EXCEPTION_INIT LP_ identifier COMMA_ errorCode RP_ SEMI_
    ;

errorCode
    : MINUS_? INTEGER_ | STRING_
    ;

autonomousTransPragma
    : PRAGMA AUTONOMOUS_TRANSACTION SEMI_
    ;
seriallyReusablePragma
    : PRAGMA SERIALLY_REUSABLE SEMI_
    ;
coveragePragma
    : PRAGMA COVERAGE '(' optionValue ')' SEMI_
    ;
deprecatePragma
    : PRAGMA DEPRECATE '(' identifier (',' optionValue)? ')' SEMI_
    ;
inlinePragma
    : PRAGMA INLINE LP_ subprogramName COMMA_  optionValue RP_ SEMI_
    ;
suppressesWarning6009Pragma
    : PRAGMA SUPPRESSES_WARNING_6009 '(' identifier ')' (SEMI_ | ',')
    ;
udfPragma
    : PRAGMA UDF SEMI_
    ;
otherPragma
    : PRAGMA identifier '(' (parameterValue (',' parameterValue)* )? ')' SEMI_
    ;

plsqlTriggerSource
    : triggerName sharingClause? defaultCollationClause? (simpleDmlTrigger | systemTrigger)
    ;

simpleDmlTrigger
    : (BEFORE | AFTER) dmlEventClause forEachClause? triggerBody
    ;

forEachClause
    : FOR EACH ROW
    ;

dmlEventClause
    : dmlEventElement (OR dmlEventElement)* ON viewName
    ;

dmlEventElement
    : (DELETE | INSERT | UPDATE) (OF LP_? columnName (COMMA_ columnName)* RP_?)?
    ;

systemTrigger
    : (BEFORE | AFTER | INSTEAD OF) (systemTriggerEvent (OR systemTriggerEvent)* | databaseEvent (OR databaseEvent)* | dmlEvent) ON LOCAL? ((PLUGGABLE? DATABASE) | (schemaName DOT_)? SCHEMA?) tableName? triggerBody
    ;

systemTriggerEvent
    : ddlEvent
    ;

ddlEvent
    : ALTER
    | ANALYZE
    | ASSOCIATE STATISTICS
    | AUDIT
    | COMMENT
    | CREATE
    | DISASSOCIATE STATISTICS
    | DROP
    | GRANT
    | NOAUDIT
    | RENAME
    | REVOKE
    | TRUNCATE
    | DDL
    | STARTUP
    | SHUTDOWN
    | DB_ROLE_CHANGE
    | LOGON
    | LOGOFF
    | SERVERERROR
    | SUSPEND
    | DATABASE
    | SCHEMA
    | FOLLOWS
    ;

databaseEvent
    : AFTER STARTUP
    | BEFORE SHUTDOWN
    | AFTER DB_ROLE_CHANGE
    | AFTER SERVERERROR
    | AFTER LOGON
    | BEFORE LOGOFF
    | AFTER SUSPEND
    | AFTER CLONE
    | BEFORE UNPLUG
    | (BEFORE | AFTER) SET CONTAINER
    ;

dmlEvent
    : INSERT
    ;

triggerBody
    : plsqlBlock
    ;

// DM https://eco.dameng.com/document/dm/zh-cn/pm/package.html
createPackage
    : CREATE (OR REPLACE)? (EDITIONABLE | NONEDITIONABLE)? PACKAGE ifNotExists? plsqlPackageSource
    ;
plsqlPackageSource
    : packageName sharingClause? (
      invokerRightsClause | defaultCollationClause | accessibleByClause
    )* (IS | AS) packageItemList+ END name?
    ;

packageItemList
    :  packageFunctionDeclaration | packageProcedureDeclaration | declareSection
    ;

packageFunctionDeclaration
    : functionHeading functionProperties* SEMI_
    ;

functionProperties
    : accessibleByClause | deterministicClause | pipelinedClause | parallelEnableClause | resultCacheClause
    ;

packageProcedureDeclaration
    : procedureHeading accessibleByClause? SEMI_
    ;

createPackageBody
    : CREATE (OR REPLACE)? (EDITIONABLE | NONEDITIONABLE)? PACKAGE BODY plsqlPackageBodySource
    ;
plsqlPackageBodySource
    : packageName sharingClause? (IS | AS) declareSection initializeSection? END name?
    ;

initializeSection
    : BEGIN statementList (EXCEPTION exceptionHandler+)?
    ;


setTransaction
    : SET TRANSACTION ((READ (ONLY | WRITE)
    | ISOLATION LEVEL (SERIALIZABLE | READ (COMMITTED))
    | USE ROLLBACK SEGMENT rollbackSegment) (NAME stringLiterals)?
    | NAME stringLiterals)
    ;

commit
    : COMMIT WORK? ((commentClause? writeClause?) | forceClause?)
    ;

commentClause
    : COMMENT stringLiterals
    ;

writeClause
    : WRITE (WAIT | NOWAIT)? (IMMEDIATE | BATCH)?
    ;

forceClause
    : FORCE stringLiterals (COMMA_ numberLiterals)?
    ;

rollback
    : ROLLBACK WORK? savepointClause
    ;

savepointClause
    : (TO SAVEPOINT? savepointName | FORCE stringLiterals)?
    ;

savepoint
    : SAVEPOINT savepointName
    ;

setConstraints
    : SET (CONSTRAINT | CONSTRAINTS) (constraintName (COMMA_ constraintName)* | ALL) (IMMEDIATE | DEFERRED)
    ;


// TODO ===================== base ===========================================

literals
    : stringLiterals
    | numberLiterals
    | dateTimeLiterals
    | hexadecimalLiterals
    | bitValueLiterals
    | booleanLiterals
    | nullValueLiterals
    | intervalLiterals
    | bindLiterals
    ;

intervalLiterals
    : INTERVAL stringLiterals intervalUnit (intervalPrecision)? (TO intervalUnit (intervalPrecision)?)?
    ;

bindLiterals
    : COLON_ identifier
    ;

intervalPrecision
    : LP_ INTEGER_ RP_
    ;

intervalUnit
    : SECOND | MINUTE | HOUR | DAY | MONTH | YEAR
    ;

stringLiterals
    : STRING_
    | NCHAR_TEXT
    | UCHAR_TEXT
    ;

numberLiterals
   : (PLUS_ | MINUS_)? numberValue
   ;

dateTimeLiterals
    : (DATE | TIME | TIMESTAMP) stringLiterals
    | LBE_ identifier stringLiterals RBE_
    ;

hexadecimalLiterals
    : HEX_DIGIT_
    ;

bitValueLiterals
    : BIT_NUM_
    ;

booleanLiterals
    : TRUE | FALSE
    ;

nullValueLiterals
    : NULL
    ;

identifier
    : IDENTIFIER_ | unreservedWord | DOUBLE_QUOTED_TEXT | I_CURSOR | QUESTION_
    ;

unreservedWord
    : unreservedWord1 | unreservedWord2 | unreservedWord3 | reservedWord | capacityUnit | timeUnit | A
    ;
//    适配数据库自带包、包体解析，将部分保留字设为用户定义标识符
reservedWord
    : LEFT | RIGHT | EXISTS | PRIOR | SET | FUNCTION | PACKAGE
    ;


unreservedWord1
    : XDB | RESOURCE | CONNECT | DIRECT_PATH | PARTIAL | CONDITION | MULTIVALUE | CHARSET
    | ACTIONS | EQUIVALENCE | BITMAPFILE | CONTROL | COMPONENT | DATAPUMP | OLS | XS
    | DV | PRIVILEGED | ACTION | SUBSCRIBE | UNSUBSCRIBE | LABEL | COMPONENTS | PROXY
    | VERIFIER | ROLESET | ACL | CALLBACK | COOKIE | INACTIVE | DESTROY | ASSIGN | GET
    | REALM | VIOLATION | SUCCESS | FAILURE | EVAL | FACTOR | NEG | BALANCE | QUOTAGROUP
    | FILEGROUP | CREDENTIAL | ONLINELOG | PARAMETERFILE
    | DATAGUARDCONFIG | CHANGETRACKING | DUMPSET | XTRANSPORT | AUTOBACKUP
    | TRUNCATE | PROCEDURE | CASE | WHEN | CAST | TRIM | SUBSTRING
    | USING | IF | TRUE | FALSE | LIMIT | OFFSET
    | COMMIT | ROLLBACK | SAVEPOINT | OUTER
    | ARRAY | INTERVAL | TIME | TIMESTAMP | LOCALTIME | LOCALTIMESTAMP | YEAR
    | MONTH | DAY | HOUR | MINUTE | SECOND
    | MAX | MIN | SUM | COUNT | AVG | ENABLE
    | DISABLE | BINARY | ESCAPE | MOD | UNKNOWN | ALWAYS
    | CASCADE | GENERATED | PRIVILEGES | READ | WRITE | REFERENCES | TRANSACTION
    | ROLE | VISIBLE | INVISIBLE | EXECUTE | USE | DEBUG | UNDER
    | FLASHBACK | ARCHIVE | REFRESH | QUERY | REWRITE | KEEP | SEQUENCE
    | INHERIT | TRANSLATE | SQL | MERGE | AT | BITMAP | CACHE | CHECKPOINT
    | CONSTRAINTS | CYCLE | ENCRYPT | DECRYPT | DEFERRABLE
    | DEFERRED | EDITION | ELEMENT | EXCEPTIONS | FORCE | GLOBAL
    | IDENTITY | INITIALLY | INVALIDATE | JAVA | LEVELS | LOCAL | MAXVALUE
    | MINVALUE | NOMAXVALUE | NOMINVALUE | MINING | MODEL | NATIONAL | NEW
    | NOCACHE | NOCYCLE | NOORDER | NORELY | NOVALIDATE | ONLY | PRESERVE
    | PROFILE | REF | REKEY | RELY | REPLACE | SOURCE | SALT
    | SCOPE | SORT | SUBSTITUTABLE | TABLESPACE | TEMPORARY | TRANSLATION | TREAT
    | NO | TYPE | UNUSED | VALUE | VARYING | VIRTUAL | ZONE
    | ADVISOR | ADMINISTER | TUNING | MANAGE | MANAGEMENT | OBJECT
    | CONTEXT | EXEMPT | REDACTION | POLICY | DATABASE | SYSTEM
    | LINK | ANALYZE | DICTIONARY | DIMENSION | INDEXTYPE | EXTERNAL | JOB
    | CLASS | PROGRAM | SCHEDULER | LIBRARY | LOGMINING | MATERIALIZED | CUBE
    | MEASURE | FOLDER | BUILD | PROCESS | OPERATOR | OUTLINE | PLUGGABLE
    | CONTAINER | SEGMENT | RESTRICTED | COST | BACKUP | UNLIMITED
    | BECOME | CHANGE | NOTIFICATION | PRIVILEGE | PURGE | RESUMABLE
    | SYSGUID | SYSBACKUP | SYSDBA | SYSDG | SYSKM | SYSOPER | DBA_RECYCLEBIN |SCHEMA
    | DEFINER | CURRENT_USER | CLOSE | OPEN | NEXT | NAME | NAMES
    | COLLATION | REAL | TYPE | FIRST | RANK | SAMPLE | SYSTIMESTAMP | MINUTE | ANY
    | LENGTH | SINGLE_C | TARGET | PUBLIC | ID | STATE | PRIORITY
    | PRIMARY | FOREIGN | KEY | POSITION | PRECISION | PROCEDURE | SPECIFICATION | CASE
    | WHEN | CAST | TRIM | SUBSTRING | JOIN
    | USING | FALSE | SAVEPOINT | BODY | CHARACTER | ARRAY | TIME | TIMEOUT | TIMESTAMP | LOCALTIME
    | DAY | ENABLE | DISABLE | CALL | INSTANCE | CLOSE | NEXT | NAME | NUMERIC
    | TRIGGERS | GLOBAL_NAME | BINARY | MOD | UNKNOWN | ALWAYS | CASCADE | GENERATED | PRIVILEGES
    | READ | WRITE | ROLE | VISIBLE | INVISIBLE | EXECUTE | USE | DEBUG | UNDER | FLASHBACK
    | ARCHIVE | REFRESH | QUERY | REWRITE | CHECKPOINT | ENCRYPT | DIRECTORY | CREDENTIALS | EXCEPT | NOFORCE
    | NOSORT | MINING | MODEL | REVERSE | SORT | TABLESPACE | TREAT | ADMINISTER | TUNING
    | CONTEXT | LINK | DICTIONARY | INDEXTYPES | RESTRICT | BACKUP | RECYCLEBIN | NCHAR | NVARCHAR2 | BLOB
    | CLOB | NCLOB | BINARY_FLOAT | BINARY_DOUBLE | PLS_INTEGER | BINARY_INTEGER | NATURALN | POSITIVE | POSITIVEN | SIGNTYPE
    | SIMPLE_INTEGER | BFILE | UROWID | JSON | DEC | SHARING | PRIVATE | SHARDED | SHARD | DUPLICATED
    | METADATA | DATA | EXTENDED | NONE | MEMOPTIMIZE | PARENT | IDENTIFIER | WORK | CONTAINER_MAP | CONTAINERS_DEFAULT
    | WAIT | BATCH | BLOCK | REBUILD | INVALIDATION | COMPILE | USABLE | UNUSABLE | MONITORING | NOMONITORING
    | USAGE | COALESCE | CLEANUP | PARALLEL | NOPARALLEL | LOG | REUSE | SETTINGS | STORAGE | MATCHED
    | ERRORS | REJECT | RETENTION | CHUNK | PCTVERSION | FREEPOOLS | AUTO | DEDUPLICATE | KEEP_DUPLICATES | HIGH
    | MEDIUM | LOW | READS | CREATION | PCTUSED | INITRANS | LOGGING | NOLOGGING | FILESYSTEM_LIKE_LOGGING | MINEXTENTS
    | BASIC | ADVANCED | PCTINCREASE | FREELISTS | DML | DDL | CAPACITY | FREELIST | GROUPS | OPTIMAL
    | BUFFER_POOL | RECYCLE | FLASH_CACHE | CELL_FLASH_CACHE | MAXSIZE | MAX_AUDIT_SIZE | MAX_DIAG_SIZE | STORE | LOCKING | INMEMORY
    | MEMCOMPRESS | PRIORITY | CRITICAL | DISTRIBUTE | RANGE | PARTITION | SUBPARTITION | SERVICE | DUPLICATE | ILM
    | DELETE_ALL | ENABLE_ALL | DISABLE_ALL | AFTER | MODIFICATION | DAYS | MONTHS | YEARS | TIER | ORGANIZATION
    | HEAP | PCTTHRESHOLD | PARAMETERS | LOCATION | MAPPING | NOMAPPING | INCLUDING | OVERFLOW | ATTRIBUTE | ATTRIBUTES
    | RESULT_CACHE | ROWDEPENDENCIES | NOROWDEPENDENCIES | ARCHIVAL | EXCHANGE | INDEXING | OFF | LESS | INTERNAL | VARRAY
    | NESTED | RETURN | LOCATOR | LOB | SECUREFILE | BASICFILE | THAN | LIST | AUTOMATIC | HASH
    | PARTITIONS | SUBPARTITIONS | TEMPLATE | PARTITIONSET | REFERENCE | CONSISTENT | CLUSTERING | LINEAR | INTERLEAVED | YES
    | LOAD | MOVEMENT | ZONEMAP | WITHOUT | XMLTYPE | RELATIONAL | XML | VARRAYS | LOBS | TABLES
    | ALLOW | DISALLOW | NONSCHEMA | ANYSCHEMA | XMLSCHEMA | COLUMNS | OIDINDEX | EDITIONABLE | NONEDITIONABLE | DEPENDENT
    | INDEXES | SHRINK | SPACE | COMPACT | SUPPLEMENTAL | ADVISE | NOTHING | GUARD | SYNC | VISIBILITY
    | ACTIVE | DEFAULT_COLLATION | MOUNT | STANDBY | CLONE | RESETLOGS | NORESETLOGS | UPGRADE | DOWNGRADE | RECOVER
    | LOGFILE | TEST | CORRUPTION | CONTINUE | CANCEL | UNTIL | CONTROLFILE | SNAPSHOT | DATAFILE | MANAGED
    | ARCHIVED | DISCONNECT | NODELAY | INSTANCES | FINISH | LOGICAL | AUTOEXTEND | BLOCKSIZE | RESIZE | TEMPFILE
    | DATAFILES | ARCHIVELOG | MANUAL | NOARCHIVELOG | AVAILABILITY | PERFORMANCE | CLEAR | UNARCHIVED | UNRECOVERABLE | THREAD
    | PHYSICAL | FAR | TRACE | DISTRIBUTED | RECOVERY | FLUSH | NOREPLY | SWITCH | LOGFILES
    | PROCEDURAL | REPLICATION | SUBSET | ACTIVATE | APPLY | MAXIMIZE | PROTECTION | SUSPEND | RESUME | QUIESCE
    | UNQUIESCE | SHUTDOWN | REGISTER | PREPARE | SWITCHOVER | FAILED | SKIP_SYMBOL | STOP | ABORT | VERIFY
    | CONVERT | FAILOVER | BIGFILE | SMALLFILE | TRACKING | CACHING | CONTAINERS | UNDO | MOVE | MIRROR
    | COPY | UNPROTECTED | REDUNDANCY | REMOVE | LOST | LEAD_CDB | LEAD_CDB_URI | PROPERTY | DEFAULT_CREDENTIAL | TIME_ZONE
    | RESET | RELOCATE | CLIENT | PASSWORDFILE_METADATA_CACHE | NOSWITCH | POST_TRANSACTION | KILL | ROLLING | MIGRATION | PATCH
    | ENCRYPTION | WALLET | AFFINITY | MEMORY | SPFILE | PFILE | BOTH | SID | SHARED_POOL | BUFFER_CACHE
    | REDO | CONFIRM | MIGRATE | USE_STORED_OUTLINES | GLOBAL_TOPIC_ENABLED | LOCKED | FETCH | PERCENT | TIES | SIBLINGS
    | NULLS | LAST | ISOLATION | SERIALIZABLE | COMMITTED | FILTER | FACT | DETERMINISTIC | PIPELINED | PARALLEL_ENABLE
    | OUT | NOCOPY | ACCESSIBLE | PACKAGE | PACKAGES | USING_NLS_COMP | AUTHID | SEARCH | DEPTH | BREADTH
    | ANALYTIC | HIERARCHIES | MEASURES | OVER | LAG | LAG_DIFF | LAG_DIF_PERCENT | LEAD | LEAD_DIFF | LEAD_DIFF_PERCENT
    | HIERARCHY | WITHIN | ACROSS | ANCESTOR | BEGINNING | UNBOUNDED | PRECEDING | FOLLOWING | DENSE_RANK | AVERAGE_RANK
    | ROW_NUMBER | SHARE_OF | HIER_ANCESTOR | HIER_PARENT | HIER_LEAD | HIER_LAG | QUALIFY | HIER_CAPTION | HIER_DEPTH | HIER_DESCRIPTION
    | HIER_LEVEL | HIER_MEMBER_NAME | HIER_MEMBER_UNIQUE_NAME | CHAINED | STATISTICS | DANGLING | STRUCTURE | FAST | COMPLETE | ASSOCIATE
    | DISASSOCIATE | FUNCTIONS | TYPES | SELECTIVITY | RETURNING | VERSIONS | SCN | PERIOD | LATERAL | BADFILE
    | DISCARDFILE | PIVOT | UNPIVOT | INCLUDE | EXCLUDE | SEED | SHARDS | MATCH_RECOGNIZE | PATTERN | DEFINE
    | ONE | PER | MATCH | PAST | PERMUTE | CLASSIFIER | MATCH_NUMBER | RUNNING | FINAL | PREV
    | USERS | GRANTED | ROLES | NAMESPACE | ROLLUP | GROUPING | SETS | DECODE | RESTORE | POINT
    | BEFORE | IGNORE | NAV | SINGLE | UPDATED | MAIN | RULES | UPSERT | SEQUENTIAL | ITERATE
    | DECREMENT | SOME | NAN | INFINITE | PRESENT | EMPTY | SUBMULTISET | LIKEC | LIKE2 | LIKE4
    | REGEXP_LIKE | EQUALS_PATH | UNDER_PATH | FORMAT | STRICT | LAX | KEYS | JSON_EXISTS | PASSING
    | ERROR | JSON_TEXTCONTAINS | HAS | STARTS | LIKE_REGEX | EQ_REGEX | SYS | MAXDATAFILES | MAXINSTANCES | AL32UTF8
    | AL16UTF16 | UTF8 | USER_DATA | MAXLOGFILES | MAXLOGMEMBERS | MAXLOGHISTORY | EXTENT | SYSAUX | LEAF | AUTOALLOCATE
    | UNIFORM | FILE_NAME_CONVERT | ALLOCATE | DEALLOCATE | SHARED | AUTHENTICATED | CHILD | DETERMINES | RELIES_ON | AGGREGATE
    | POLYMORPHIC | SQL_MARCO | LANGUAGE | AGENT | SELF | TDO | INDICATOR | STRUCT | LENGTH | DURATION
    | MAXLEN | CHARSETID | CHARSETFORM | CATEGORY | NOKEEP | SCALE | NOSCALE | EXTEND | NOEXTEND | NOSHARD
    | INITIALIZED | EXTERNALLY | GLOBALLY | ACCESSED | RESTART | OPTIMIZE | QUOTA | DISKGROUP | NORMAL | FLEX
    | SITE | QUORUM | REGULAR | FAILGROUP | DISK | EXCLUDING | CONTENTS | LOCKDOWN | CLEAN | GUARANTEE
    | PRUNING | DEMAND | RESOLVE | RESOLVER | ANCILLARY | BINDING | SCAN | COMPUTE | UNDROP | DISKS
    | COARSE | FINE | ALIAS | SCRUB | DISMOUNT | REBALANCE | COMPUTATION | CONSIDER | FRESH | MASTER
    | ENFORCED | TRUSTED | ID | SYNCHRONOUS | ASYNCHRONOUS | REPEAT | FEATURE | STATEMENT | CLAUSE | UNPLUG
    | HOST | PORT | EVERY | MINUTES | HOURS | NORELOCATE | SAVE | DISCARD | APPLICATION | INSTALL
    | MINIMUM | VERSION | UNINSTALL | COMPATIBILITY | MATERIALIZE | SUBTYPE | RECORD | CONSTANT | CURSOR
    | OTHERS | EXCEPTION | CPU_PER_SESSION | CONNECT_TIME | LOGICAL_READS_PER_SESSION | PRIVATE_SGA | PERCENT_RANK | ROWID
    | ZONE | XMLELEMENT | COLUMN_VALUE | EVALNAME | LEVEL | CONTENT | ON | LOOP | EXIT | ELSIF
    ;

unreservedWord2
    : ABS | ACCESS | ACCOUNT | ADD
    | ADD_COLUMN | ADD_GROUP | ADMIN | APPEND
    | APPENDCHILDXML | ASCII | ASYNC | AUDIT | AUTHENTICATION
    | AUTHORIZATION | BULK | BYTE
    | CHAR_CS | CHR | CLUSTER_ID
    | CLUSTER_PROBABILITY | CLUSTER_SET | COLD | COLLECT | COLUMN | COMMENT | COMPOSITE_LIMIT
    | CONNECT_BY_ROOT | CORR | CORR_K | CORR_S
    | COVAR_POP | COVAR_SAMP | CPU_PER_CALL | CUME_DIST | CURRENT | DB_ROLE_CHANGE
    | DECLARE | DEFAULT | DEFAULTS | DEGREE | DELETEXML
    | DIRECT_LOAD
    | DOCUMENT | DROP_COLUMN | DROP_GROUP | EACH | EDITIONING | EDITIONS | ENCODING | ENTERPRISE
    | EVALUATION | EVENTS | EXISTSNODE | EXPIRE | EXPLAIN
    | EXPORT | EXTRACT | EXTRACTVALUE
    | FAILED_LOGIN_ATTEMPTS | FEATURE_ID | FEATURE_SET | FEATURE_VALUE | FILE | FIRST_VALUE | FOLLOWS
    | GROUPING_ID | GROUP_ID | HASHKEYS
    | HEADER | HIDE | HOT
    | IDLE_TIME | IMMEDIATE
    | IMPORT | INCR | INCREMENT | INDENT | INITIAL
    | INLINE | INSERTCHILDXML | INSERTCHILDXMLAFTER | INSERTCHILDXMLBEFORE | INSERTXMLAFTER
    | INSERTXMLBEFORE | INSTANTIABLE | INSTEAD
    | KERBEROS | LAST_VALUE
    | LEADING | LIFE
    | LISTAGG | LN| LOGICAL_READS_PER_CALL | LOGOFF | LOGON
    | MAXEXTENTS | MAXTRANS | MEDIAN | MINIMIZE
    | MIRRORCOLD | MIRRORHOT | MLSLABEL
    | MODIFY | MONITOR | MOUNTPATH
    | MULTISET | NAMED | NCHAR_CS | NCHR
    | NETWORK | NEVER | NLS_COMP | NOAUDIT |  NOGUARANTEE | NOMINIMIZE | NOREPAIR | NOREVERSE | NTH_VALUE | NTILE
    | OFFLINE | OID | OLD | OLTP | ONLINE | ORDINALITY
    | OTHER | OWN | OWNER | OWNERSHIP | PARAM | PARITY | PASSWORD
    | PASSWORD_GRACE_TIME | PASSWORD_LIFE_TIME | PASSWORD_LOCK_TIME | PASSWORD_REUSE_MAX | PASSWORD_REUSE_TIME
    | PASSWORD_VERIFY_FUNCTION | PATH | PATHS | PERCENTILE_CONT | PERCENTILE_DISC
    | PERMANENT | PERMISSION | PIKEY | PLAN | POWER | PREBUILT
    ;

unreservedWord3
    : PREDICTION | PREDICTION_BOUNDS | PREDICTION_DETAILS
    | PREDICTION_PROBABILITY | PREDICTION_SET
    | PROTECTED | QUEUE | RATIO_TO_REPORT
    | RECORDS_PER_BLOCK | RECOVERABLE | REDUCED | REGR_AVGX | REGR_AVGY | REGR_COUNT | REGR_INTERCEPT | REGR_R2
    | REGR_SLOPE | REGR_SXX | REGR_SXY | REGR_SYY | REPAIR | REQUIRED | RESPECT | ROW
    | ROWS | RTRIM | RULE | SB4 | SECURITY | SERVERERROR | SESSION | SESSIONS_PER_USER
    | SHOW | SIGN | SIMPLE | SIN | SPLIT
    | STALE | STANDALONE | STAR | STARTUP | STATEMENTS | STATEMENT_ID
    | STATIC | STATS_BINOMIAL_TEST | STATS_CROSSTAB | STATS_F_TEST | STATS_KS_TEST | STATS_MODE
    | STATS_MW_TEST | STATS_ONE_WAY_ANOVA | STATS_T_TEST_INDEP | STATS_T_TEST_INDEPU | STATS_T_TEST_ONE | STATS_T_TEST_PAIRED
    | STATS_WSR_TEST | STDDEV | STDDEV_POP | STDDEV_SAMP | STRING | STRIP | STRIPE_COLUMNS | STRIPE_WIDTH
    | SUBSTR | SUCCESSFUL | SUMMARY | SYS_DBURIGEN | SYS_XMLGEN | TAN
    | THE | THROUGH | TIMES | TIMEZONE_ABBR | TIMEZONE_HOUR | TIMEZONE_MINUTE | TIMEZONE_REGION
    | TOPLEVEL | TO_DATE | TRACING | TRAILING | TRUNC | TX | UB2
    | UID | UNBOUND | UNLOCK | UPDATEXML
    | USER | USERGROUP | VALIDATE | VALIDATION | VARIANCE | VAR_POP
    | VAR_SAMP | VOLUME | WELLFORMED | WHENEVER | WHITESPACE
    | WRAPPED | XMLAGG | XMLATTRIBUTES | XMLCAST | XMLCDATA | XMLCOLATTVAL | XMLCOMMENT | XMLCONCAT | XMLDIFF
    | XMLEXISTS | XMLFOREST
    | XMLISVALID | XMLNAMESPACES | XMLPARSE | XMLPATCH | XMLPI | XMLQUERY | XMLROOT | XMLSERIALIZE | XMLTABLE
    | XMLTRANSFORM | RESULT | TABLE | NUMBER | CHAR | SQLCODE
    ;

schemaName
    : identifier
    ;


tableName
    : (owner DOT_)? name
    ;

viewName
    : (owner DOT_)? name
    ;

triggerName
    : (owner DOT_)? name
    ;

materializedViewName
    : (owner DOT_)? name
    ;

columnName
    : (owner DOT_)* name
    ;

objectName
    : (owner DOT_)? name
    ;

clusterName
    : (owner DOT_)? name
    ;

indexName
    : (owner DOT_)? name
    ;

statisticsTypeName
    : (owner DOT_)? name
    ;

functionName
    : (owner DOT_)? name
    ;

packageName
    : (owner DOT_)? name
    ;

profileName
    : (owner DOT_)? identifier
    ;

rollbackSegmentName
    : identifier
    ;

typeName
    : (owner DOT_)? name
    ;

indexTypeName
    : (owner DOT_)? name
    ;

modelName
    : (owner DOT_)? name
    ;

operatorName
    : (owner DOT_)? name
    ;

dimensionName
    : (owner DOT_)? name
    ;

directoryName
    : (owner DOT_)? name
    ;

constraintName
    : identifier
    ;

contextName
    : identifier
    ;

savepointName
    : identifier
    ;

synonymName
    : (owner DOT_)? name
    ;

owner
    : identifier
    ;

name
    : identifier
    ;

tablespaceName
    : identifier
    ;

newTablespaceName
    : identifier
    ;

subprogramName
    : identifier
    ;
collectionVariable
    : identifier
    ;
methodName
    : identifier
    ;

tablespaceSetName
    : identifier
    ;

serviceName
    : identifier
    ;

ilmPolicyName
    : identifier
    ;

policyName
    : identifier
    ;

connectionQualifier
    : identifier
    ;

featureId
    : numberLiterals
    ;

dbLink
    : identifier (DOT_ identifier)*
    ;

parameterValue
    : literals | identifier
    ;

dispatcherName
    : stringLiterals
    ;

clientId
    : stringLiterals
    ;

opaqueFormatSpec
    : identifier
    ;

accessDriverType
    : identifier
    ;

varrayItem
    : identifier
    ;

nestedItem
    : identifier
    ;

storageTable
    : identifier
    ;

lobSegname
    : identifier
    ;

locationSpecifier
    : identifier
    ;

xmlSchemaURLName
    : identifier
    ;

elementName
    : identifier
    ;

subpartitionName
    : identifier
    ;

editionName
    : identifier
    ;

outlineName
    : identifier
    ;

containerName
    : identifier
    ;

newName
    : identifier
    ;

partitionName
    : identifier
    ;

partitionSetName
    : identifier
    ;

partitionKeyValue
    : INTEGER_ | dateTimeLiterals | toDateFunction
    ;

subpartitionKeyValue
    : INTEGER_ | dateTimeLiterals
    ;

encryptAlgorithmName
    : STRING_
    ;

integrityAlgorithm
    : STRING_
    ;

zonemapName
    : identifier
    ;

flashbackArchiveName
    : identifier
    ;

roleName
    : identifier
    ;

username
    : identifier | STRING_
    ;

password
    : identifier | STRING_ | INTEGER_
    ;

logGroupName
    : identifier
    ;

columnNames
    : LP_ columnName (COMMA_ columnName)* RP_
    | columnName (COMMA_ columnName)*
    ;


oracleId
    : ((STRING_ | identifier) DOT_)* (STRING_ | identifier)
    ;

collationName
    : STRING_ | IDENTIFIER_
    ;

columnCollationName
    : identifier
    ;

alias
    : identifier | STRING_
    ;

dataTypeLength
    : LP_ (length=(INTEGER_ | ASTERISK_) (COMMA_ (MINUS_)? INTEGER_)? type=(CHAR | BYTE)?)? RP_
    ;

primaryKey
    : PRIMARY KEY
    ;

exprs
    : expr (COMMA_ expr)*
    ;

exprList
    : LP_ exprs RP_
    ;

expr
    : expr andOperator expr
    | expr orOperator expr
    | notOperator expr
    | LP_ expr RP_
    | booleanPrimary
    | expr datetimeExpr
    | multisetExpr
    | builtinFunctionsExpr
    | dataManipulationLanguageExpr
    ;

dataManipulationLanguageExpr
    : UPDATING | INSERTING | DELETING
    ;

andOperator
    : AND | AND_
    ;

orOperator
    : OR | OR_
    ;

notOperator
    : NOT | NOT_
    ;

booleanPrimary
    : booleanPrimary IS NOT? (TRUE | FALSE | UNKNOWN | NULL)
    | booleanPrimary IS NOT? OF TYPE? LP_ (typeName (COMMA_ typeName)*) RP_
    | (PRIOR | DISTINCT) predicate
    | CONNECT_BY_ROOT predicate
    | booleanPrimary SAFE_EQ_ predicate
    | booleanPrimary comparisonOperator (ALL | ANY) subquery
    | booleanPrimary comparisonOperator predicate
    | predicate
    ;

comparisonOperator
    : EQ_ | GTE_ | GT_ | LTE_ | LT_ | NEQ_
    ;

predicate
    : PRIOR? bitExpr NOT? IN subquery
//    | PRIOR? PRIOR predicate
    // | PRIOR? bitExpr NOT? IN LP_ expr (COMMA_ expr)* RP_
    | PRIOR? bitExpr NOT? IN LP_ expr (COMMA_ expr)* RP_ (AND predicate)?
    | PRIOR? bitExpr NOT? BETWEEN bitExpr AND predicate
    | PRIOR? bitExpr NOT? LIKE simpleExpr (ESCAPE simpleExpr)?
    | PRIOR? bitExpr
    ;

bitExpr
    : bitExpr VERTICAL_BAR_ bitExpr
    | bitExpr AMPERSAND_ bitExpr
    | bitExpr SIGNED_LEFT_SHIFT_ bitExpr
    | bitExpr SIGNED_RIGHT_SHIFT_ bitExpr
    | bitExpr PLUS_ bitExpr
    | simpleExpr
    | bitExpr MINUS_ bitExpr
    | bitExpr BACKSLASH_? ASTERISK_ bitExpr
    | bitExpr SLASH_ bitExpr
    | bitExpr MOD_ bitExpr
    | bitExpr MOD bitExpr
    | bitExpr CARET_ bitExpr
    | bitExpr DOT_ bitExpr
    | bitExpr ARROW_ bitExpr
    ;

simpleExpr
    : functionCall #functionExpr
    | literals #literalsExpr
    | ROW? LP_ expr (COMMA_ expr)* RP_ #rowExpr
    | EXISTS? subquery #subqueryExpr
    | LBE_ identifier expr RBE_ #objectAccessExpr
    | caseExpression #caseExpr
    | columnName joinOperator? #columnExpr
    | privateExprOfDb #privateExpr
    | PRIOR identifier #priorExpr
    | simpleExpr OR_ simpleExpr #orExpr
    | (PLUS_ | MINUS_ | TILDE_ | NOT_ | BINARY) simpleExpr #unaryExpr
    ;

functionCall
    : aggregationFunction | analyticFunction | specialFunction | xmlFunction | regularFunction
    ;

aggregationFunction
    : aggregationFunctionName LP_ (((DISTINCT | ALL)? expr (COMMA_ expr)*) | ASTERISK_) (COMMA_ stringLiterals)?
     listaggOverflowClause? orderByClause? RP_ withinClause? keepClause? overClause? overClause?
    ;

withinClause
    : WITHIN GROUP LP_ orderByClause RP_
    ;

keepClause
    : KEEP LP_ DENSE_RANK (FIRST | LAST) orderByClause RP_ overClause?
    ;

aggregationFunctionName
    : MAX | MIN | SUM | COUNT | AVG | GROUPING | LISTAGG | PERCENT_RANK | PERCENTILE_CONT | PERCENTILE_DISC | CUME_DIST | RANK
    | REGR_SLOPE | REGR_INTERCEPT | REGR_COUNT | REGR_R2 | REGR_AVGX | REGR_AVGY | REGR_SXX | REGR_SYY | REGR_SXY
    | COLLECT | CORR | CORR_S | CORR_K | COVAR_POP | COVAR_SAMP | DENSE_RANK | FIRST
    | GROUP_ID | GROUPING_ID | LAST | MEDIAN | STATS_BINOMIAL_TEST | STATS_CROSSTAB | STATS_F_TEST | STATS_KS_TEST
    | STATS_MODE | STATS_MW_TEST | STATS_ONE_WAY_ANOVA | STATS_T_TEST_ONE | STATS_T_TEST_PAIRED | STATS_T_TEST_INDEP
    | STATS_T_TEST_INDEPU | STATS_WSR_TEST | STDDEV | STDDEV_POP | STDDEV_SAMP | VAR_POP | VAR_SAMP | VARIANCE
    ;

listaggOverflowClause
    : ON OVERFLOW (ERROR | (TRUNCATE stringLiterals? ((WITH | WITHOUT) COUNT)?))
    ;

analyticClause
    : queryPartitionClause? (orderByClause windowingClause?)?
    ;

windowingClause
    : (ROWS | RANGE) ((BETWEEN (UNBOUNDED PRECEDING | CURRENT ROW | expr (PRECEDING | FOLLOWING)) AND (UNBOUNDED FOLLOWING | CURRENT ROW | expr (PRECEDING | FOLLOWING)))
    | (UNBOUNDED PRECEDING | CURRENT ROW | expr PRECEDING))
    ;

analyticFunction
    : specifiedAnalyticFunctionName = (LEAD | LAG) ((LP_ expr leadLagInfo? RP_ respectOrIgnoreNulls?) | (LP_ expr respectOrIgnoreNulls? leadLagInfo? RP_)) overClause
    | specifiedAnalyticFunctionName = (NTILE | MEDIAN | RATIO_TO_REPORT) LP_ expr RP_ overClause?
    | specifiedAnalyticFunctionName = NTH_VALUE LP_ expr COMMA_ expr RP_ fromFirstOrLast? respectOrIgnoreNulls? overClause
    | specifiedAnalyticFunctionName = (PERCENTILE_CONT | PERCENTILE_DISC | LISTAGG) LP_ expr (COMMA_ expr)* RP_ withinClause overClause?
    | specifiedAnalyticFunctionName = (CORR | COVAR_POP | COVAR_SAMP) LP_ expr COMMA_ expr RP_ overClause?
    | specifiedAnalyticFunctionName = (PERCENT_RANK | RANK | ROW_NUMBER) LP_ RP_ overClause
    | analyticFunctionName LP_ dataType* RP_ overClause
    ;

fromFirstOrLast
    : FROM FIRST | FROM LAST
    ;

leadLagInfo
    : COMMA_ expr (COMMA_ expr)?
    ;

specialFunction
    : castFunction | charFunction | extractFunction | formatFunction | firstOrLastValueFunction | trimFunction | featureFunction
    | setFunction | translateFunction | cursorFunction | toDateFunction | approxRank | wmConcatFunction
    ;

wmConcatFunction
    : (owner DOT_)? WM_CONCAT LP_ expr RP_ overClause?
    ;

approxRank
    : APPROX_RANK LP_ queryPartitionClause? orderByClause RP_
    ;

toDateFunction
    : TO_DATE LP_ char=STRING_ (DEFAULT returnValue=STRING_ ON CONVERSION ERROR)? (COMMA_ fmt=STRING_ (COMMA_ STRING_)?)? RP_
    ;

cursorFunction
    : CURSOR subquery
    ;

translateFunction
    : TRANSLATE LP_ expr USING (CHAR_CS | NCHAR_CS) RP_
    ;

setFunction
    : SET LP_ expr RP_
    ;

featureFunction
    : featureFunctionName LP_ (schemaName DOT_)? modelName (COMMA_ featureId)? (COMMA_ numberLiterals (COMMA_ numberLiterals)?)?
    (DESC | ASC | ABS)? cost_matrix_clause? miningAttributeClause (AND miningAttributeClause)? RP_
    ;

featureFunctionName
    : FEATURE_COMPARE | FEATURE_DETAILS | FEATURE_SET | FEATURE_ID | FEATURE_VALUE | CLUSTER_DETAILS | CLUSTER_DISTANCE | CLUSTER_ID | CLUSTER_PROBABILITY | CLUSTER_SET
    | PREDICTION_PROBABILITY | PREDICTION_SET | PREDICTION_BOUNDS | PREDICTION | PREDICTION_DETAILS
    ;

cost_matrix_clause
    : COST (MODEL (AUTO)?)? | LP_ literals RP_ (COMMA_ LP_ literals RP_)* VALUES LP_ LP_ literals (COMMA_ literals)* RP_ (COMMA_ LP_ literals (COMMA_ literals)* RP_) RP_
    ;

miningAttributeClause
    : USING (ASTERISK_ | ((schemaName DOT_)? tableName DOT_ ASTERISK_ | expr aliasClause?) (COMMA_ ((schemaName DOT_)? tableName DOT_ ASTERISK_ | expr aliasClause?))*)
    ;

trimFunction
    : TRIM LP_ trimOperands? expr RP_
    ;

trimOperands
    : (trimType expr? FROM) | (expr FROM)
    ;

trimType
    : LEADING
    | TRAILING
    | BOTH
    ;

firstOrLastValueFunction
    : (FIRST_VALUE | LAST_VALUE)  (LP_ expr respectOrIgnoreNulls? RP_ | LP_ expr RP_ respectOrIgnoreNulls?) overClause
    ;

respectOrIgnoreNulls
    : (RESPECT | IGNORE) NULLS
    ;

overClause
    : OVER LP_ analyticClause RP_
    ;

formatFunction
    : (owner DOT_)* name DOT_ FORMAT LP_ expr (COMMA_ expr)* RP_
    ;

castFunction
    : CAST LP_ ((MULTISET subquery) | expr) AS dataType RP_
    | XMLCAST LP_ expr AS dataType RP_
    ;

charFunction
    : (CHR | CHAR) LP_ expr (COMMA_ expr)* (USING charSet)? RP_
    ;

charSet
    : NCHAR_CS
    | ignoredIdentifier
    ;

extractFunction
    : EXTRACT LP_ (YEAR | MONTH | DAY | HOUR | MINUTE | SECOND | TIMEZONE_HOUR | TIMEZONE_MINUTE | TIMEZONE_REGION | TIMEZONE_ABBR) FROM expr RP_
    ;

regularFunction
    : regularFunctionName LP_ (expr (COMMA_ expr)* | ASTERISK_)? (jsonParamsOptions+)? RP_
    ;

// DM https://eco.dameng.com/document/dm/zh-cn/pm/json.html#18.2.1.4%20json_table
// json_value | json_query | json_table | json_table
jsonParamsOptions
    : ASCII
    | jsonReturningClause
    | jsonWrapperOptions
    | jsonEmptyErrorOptions
    | jsonColumnsClause
    ;

jsonReturningClause
    : RETURNING (VARCHAR | VARCHAR2 | NUMBER | DATE | CLOB | JSON)
    ;

jsonEmptyErrorOptions
    : ((NULL | ERROR | defaultString | TRUE | FALSE | EMPTY (ARRAY | OBJECT)?) ON (EMPTY | ERROR))+
    ;

jsonWrapperOptions
    : WITH (ARRAY)?
    | WITHOUT (ARRAY)?
    ;

jsonColumnsClause
    : COLUMNS LP_ jsonColumn (COMMA_ jsonColumn)* RP_
    ;

jsonColumn
    : columnName dataType (EXISTS | FORMAT JSON)? PATH expr jsonEmptyErrorOptions?
    | NESTED PATH expr jsonColumnsClause
    | columnName FOR ORDINALITY
    ;

regularFunctionName
    : IF | LOCALTIME | LOCALTIMESTAMP | INTERVAL | DECODE | functionName
    ;

joinOperator
    : LP_ PLUS_ RP_
    ;

caseExpression
    : CASE simpleExpr? caseWhen+ caseElse? END
    ;

caseWhen
    : WHEN expr THEN expr
    ;

caseElse
    : ELSE expr
    ;

orderByClause
    : ORDER SIBLINGS? BY orderByItem (COMMA_ orderByItem)*
    ;

orderByItem
    : (columnName | numberLiterals | expr) (ASC | DESC)? (NULLS FIRST | NULLS LAST)?
    ;

attribute
    : (owner DOT_)? identifier
    ;

attributeName
    : oracleId
    ;

lobItem
    : attributeName | columnName
    ;

lobItems
    : lobItem (COMMA_ lobItem)*
    ;

lobItemList
    : LP_ lobItems RP_
    ;
// https://docs.oracle.com/en/database/oracle/oracle-database/21/lnpls/datatype-attribute.html
// https://docs.oracle.com/en/database/oracle/oracle-database/21/sqlrf/Data-Types.html
dataType
    : characterSetDataType
    | intervalDatatype
    | typeAttribute
    | rowtypeAttribute
    | specialDatatype
    | dataTypeName dataTypeLength? datetimeTypeSuffix?
    | customDataType
    ;
characterSetDataType
    : dataTypeName CHARACTER SET characterSetName ('%' CHARSET)?
    ;
customDataType
    : (dataTypeName DOT_ | REF)? identifier
    ;
specialDatatype
    : NATIONAL? dataTypeName VARYING? dataTypeLength
    | (SYS DOT_)? dataTypeName
    ;
intervalDatatype
    : INTERVAL DAY (LP_ numberValue RP_)? TO SECOND (LP_ numberValue RP_)?
    | INTERVAL YEAR (LP_ numberValue RP_)? TO MONTH
    ;
dataTypeName
    : CHARACTER | CHAR | NCHAR | RAW | VARCHAR | VARCHAR2 | NVARCHAR2 | LONG | LONG RAW | BLOB | CLOB | NCLOB | BINARY_FLOAT | BINARY_DOUBLE
    | BOOLEAN | PLS_INTEGER | BINARY_INTEGER | INTEGER | NUMBER | NATURAL | NATURALN | POSITIVE | POSITIVEN | SIGNTYPE
    | SIMPLE_INTEGER | BFILE | MLSLABEL | UROWID | DATE | TIMESTAMP | TIMESTAMP WITH TIME ZONE | TIMESTAMP WITH LOCAL TIME ZONE
    | INTERVAL DAY TO SECOND | INTERVAL YEAR TO MONTH | JSON | FLOAT | REAL | DOUBLE PRECISION | INT | SMALLINT
    | DECIMAL | NUMERIC | DEC | IDENTIFIER_ | XMLTYPE | ROWID | ANYDATA | ANYTYPE | ANYDATASET | UB2 | SB4 | TIME
    ;

datetimeTypeSuffix
    : WITH LOCAL? TIME ZONE | TO MONTH | TO SECOND (LP_ numberValue RP_)?
    ;

typeAttribute
    : objectName MOD_ TYPE
    ;

treatFunction
    : TREAT LP_ expr AS REF? dataTypeName RP_
    ;

privateExprOfDb
    : treatFunction | caseExpression | intervalExpression | objectAccessExpression | constructorExpr
    ;

intervalExpression
    : LP_ expr MINUS_ expr RP_ (intervalDayToSecondExpression | intervalYearToMonthExpression)
    ;

intervalDayToSecondExpression
    : DAY (LP_ leadingFieldPrecision RP_)? TO SECOND (LP_ fractionalSecondPrecision RP_)?
    ;

intervalYearToMonthExpression
    : YEAR (LP_ leadingFieldPrecision RP_)? TO MONTH
    ;

leadingFieldPrecision
    : INTEGER_
    ;

fractionalSecondPrecision
    : INTEGER_
    ;

objectAccessExpression
    : (LP_ simpleExpr RP_ | treatFunction) DOT_ (attributeName (DOT_ attributeName)* (DOT_ functionCall)? | functionCall)
    ;

constructorExpr
    : NEW dataTypeName exprList
    ;

ignoredIdentifier
    : IDENTIFIER_
    ;

ignoredIdentifiers
    : ignoredIdentifier (COMMA_ ignoredIdentifier)*
    ;

odciParameters
    : STRING_
    ;

databaseName
    : identifier
    ;

locationName
    : STRING_
    ;

fileName
    : identifier | STRING_
    ;

asmFileName
    : fullyQualifiedFileName
    | numericFileName
    | incompleteFileName
    | aliasFileName
    ;

fullyQualifiedFileName
    : PLUS_ diskgroupName SLASH_ dbName SLASH_ fileType SLASH_ fileTypeTag DOT_ fileNumber DOT_ incarnationNumber
    ;

dbName
    : identifier
    ;

fileType
    : CONTROLFILE
    | DATAFILE
    | ONLINELOG
    | ARCHIVELOG
    | TEMPFILE
    | BACKUPSET
    | PARAMETERFILE
    | DATAGUARDCONFIG
    | FLASHBACK
    | CHANGETRACKING
    | DUMPSET
    | XTRANSPORT
    | AUTOBACKUP
    ;

fileTypeTag
    : currentBackup
    | tablespaceName
    | groupGroup POUND_
    | threadThread POUND_ UL_ seqSequence POUND_
    | hasspfileTimestamp
    | serverParameterFile
    | dbName
    | logLog POUND_
    | changeTrackingFile
    | userObj POUND_ UL_ fileName POUND_
    ;

currentBackup
    : identifier
    ;

groupGroup
    : identifier
    ;

threadThread
    : identifier
    ;

seqSequence
    : identifier
    ;

hasspfileTimestamp
    : timestampValue
    ;

serverParameterFile
    : identifier
    ;

logLog
    : identifier
    ;

changeTrackingFile
    : identifier
    ;

userObj
    : identifier
    ;

numericFileName
    : PLUS_ diskgroupName DOT_ fileNumber DOT_ incarnationNumber
    ;

incompleteFileName
    : PLUS_ diskgroupName (LP_ templateName RP_)?
    ;

aliasFileName
    : PLUS_ diskgroupName (LP_ templateName RP_)? SLASH_ aliasName
    ;

fileNumber
    : INTEGER_
    ;

incarnationNumber
    : INTEGER_
    ;

instanceName
    : STRING_
    ;

logminerSessionName
    : identifier
    ;

tablespaceGroupName
    : identifier | STRING_
    ;

copyName
    : identifier
    ;

mirrorName
    : identifier
    ;

uriString
    : identifier
    ;

qualifiedCredentialName
    : identifier
    ;

pdbName
    : identifier
    ;

diskgroupName
    : identifier
    ;

templateName
    : identifier
    ;

aliasName
    : pathString
    ;

domain
    : identifier
    ;

dateValue
    : dateTimeLiterals | stringLiterals | numberLiterals | expr
    ;

sessionId
    : STRING_
    ;

serialNumber
    : numberLiterals
    ;

instanceId
    : numberValue
    ;

sqlId
    : identifier
    ;

logFileName
    : stringLiterals
    ;

logFileGroupsArchivedLocationName
    : stringLiterals
    ;

asmVersion
    : stringLiterals
    ;

walletPassword
    : identifier
    ;

hsmAuthString
    : identifier
    ;

targetDbName
    : identifier
    ;

certificateId
    : identifier
    ;

categoryName
    : identifier
    ;

offset
    : numberLiterals | expr | nullValueLiterals
    ;

rowcount
    : numberLiterals | expr | nullValueLiterals
    ;


rollbackSegment
    : identifier
    ;

queryName
    : identifier | STRING_
    ;

cycleValue
    : STRING_
    ;

noCycleValue
    : STRING_
    ;

orderingColumn
    : columnName
    ;

subavName
    : (owner DOT_)? name
    ;

baseAvName
    : (owner DOT_)? name
    ;

measName
    : identifier
    ;

levelRef
    : identifier
    ;

offsetExpr
    : expr | numberLiterals
    ;

memberKeyExpr
    : identifier
    ;

depthExpression
    : identifier
    ;

unitName
    : (owner DOT_)? name
    ;

procedureName
    : (owner DOT_)? name
    ;

cpuCost
    : INTEGER_
    ;

ioCost
    : INTEGER_
    ;

networkCost
    : INTEGER_
    ;

defaultSelectivity
    : INTEGER_
    ;

dataItem
    : variableName
    ;

variableName
    : (owner DOT_)? name | stringLiterals
    ;

validTimeColumn
    : columnName
    ;


hierarchyName
    : (owner DOT_)? name
    ;

analyticViewName
    : (owner DOT_)? name
    ;

samplePercent
    : numberLiterals
    ;

seedValue
    : numberLiterals
    ;

namespace
    : identifier
    ;

restorePoint
    : identifier
    | stringLiterals
    ;

scnValue
    : literals
    ;

timestampValue
    : LP_? expr+ RP_?
    ;

scnTimestampExpr
    : scnValue | timestampValue
    ;

referenceModelName
    : identifier
    ;

mainModelName
    : identifier
    ;

measureColumn
    : columnName
    ;

dimensionColumn
    : columnName
    ;

pattern
    : stringLiterals
    ;

analyticFunctionName
    : identifier
    ;

condition
    : comparisonCondition
    | floatingPointCondition
    | condition (AND | OR) condition | NOT condition
    | modelCondition
    | multisetCondition
    | patternMatchingCondition
    | rangeCondition
    | nullCondition
    | xmlCondition
    | jsonCondition
    | LP_ condition RP_ | NOT condition | condition (AND | OR) condition
    | existsCondition
    | inCondition
    | isOfTypeCondition
    ;

comparisonCondition
    : simpleComparisonCondition | groupComparisonCondition
    ;

simpleComparisonCondition
    : (expr (EQ_ | NEQ_ | GT_ | LT_ | GTE_ | LTE_) expr)
    | (exprList (EQ_ | NEQ_) LP_ (expressionList | subquery) RP_)
    ;

expressionList
    : exprs | LP_ expr? (COMMA_ expr?)* RP_
    ;

groupComparisonCondition
    : (expr (EQ_ | NEQ_ | GT_ | LT_ | GTE_ | LTE_) (ANY | SOME | ALL) LP_ (expressionList | subquery) RP_)
    | (exprList (EQ_ | NEQ_) (ANY | SOME | ALL) LP_ ((expressionList (SQ_ expressionList)*) | subquery) RP_)
    ;

floatingPointCondition
    : expr IS NOT? (NAN | INFINITE)
    ;

modelCondition
    : isAnyCondition | isPresentCondition
    ;

isAnyCondition
    : (dimensionColumn IS)? ANY
    ;

isPresentCondition
    : cellReference IS PRESENT
    ;

cellReference
    : identifier
    ;

multisetCondition
    : isASetCondition
    | isEmptyCondition
    | memberCondition
    | submultisetCondition
    ;

isASetCondition
    : tableName IS NOT? A SET
    ;

isEmptyCondition
    : tableName IS NOT? EMPTY
    ;

memberCondition
    : expr NOT? MEMBER OF? tableName
    ;

submultisetCondition
    : tableName NOT? SUBMULTISET OF? tableName
    ;

patternMatchingCondition
    : likeCondition | regexpLikeCondition
    ;

likeCondition
    : searchValue NOT? (LIKE | LIKEC | LIKE2 | LIKE4) pattern (ESCAPE escapeChar)?
    ;

searchValue
    : identifier | stringLiterals
    ;

escapeChar
    : stringLiterals
    ;

regexpLikeCondition
    : REGEXP_LIKE LP_ searchValue COMMA_ pattern (COMMA_ matchParam)? RP_
    ;

matchParam
    : stringLiterals
    ;

rangeCondition
    : expr NOT? BETWEEN expr AND expr
    ;

nullCondition
    : expr IS NOT? NULL
    ;

xmlCondition
    : equalsPathCondition | underPathCondition
    ;

equalsPathCondition
    : EQUALS_PATH LP_ columnName COMMA_ pathString (COMMA_ correlationInteger)? RP_
    ;

pathString
    : stringLiterals
    ;

correlationInteger
    : INTEGER_
    ;

underPathCondition
    : UNDER_PATH LP_ columnName (COMMA_ levels)? COMMA_ pathString (COMMA_ correlationInteger)? RP_
    ;

level
    : identifier
    ;

levels
    : INTEGER_
    ;

jsonCondition
    : isJsonCondition | jsonExistsCondition | jsonTextcontainsCondition
    ;

isJsonCondition
    : expr IS NOT? JSON (FORMAT JSON)? (STRICT | LAX)? ((WITH | WITHOUT) UNIQUE KEYS)?
    ;

jsonExistsCondition
    : JSON_EXISTS LP_ expr (FORMAT JSON)? COMMA_ jsonBasicPathExpr
    jsonPassingClause? jsonExistsOnErrorClause? jsonExistsOnEmptyClause? RP_
    ;

jsonPassingClause
    : PASSING expr AS identifier (COMMA_ expr AS identifier)*
    ;

jsonExistsOnErrorClause
    : (ERROR | TRUE | FALSE) ON ERROR
    ;

jsonExistsOnEmptyClause
    : (ERROR | TRUE | FALSE) ON EMPTY
    ;

jsonTextcontainsCondition
    : JSON_TEXTCONTAINS LP_ columnName COMMA_ jsonBasicPathExpr COMMA_ stringLiterals RP_
    ;

jsonBasicPathExpr
    : jsonAbsolutePathExpr | jsonRelativePathExpr
    ;

jsonAbsolutePathExpr
    : DOLLAR_ jsonNonfunctionSteps? jsonFunctionStep?
    ;

jsonNonfunctionSteps
    : ((jsonObjectStep | jsonArrayStep | jsonDescendentStep) jsonFilterExpr?)+
    ;

jsonObjectStep
    : DOT_ASTERISK_ | DOT_ jsonFieldName
    ;

jsonFieldName
    : jsonString | (letter (letter | digit)*)
    ;

letter
    : identifier
    ;

digit
    : numberLiterals
    ;

jsonArrayStep
    : LBT_ (ASTERISK_ | INTEGER_ (TO INTEGER_)? (COMMA_ INTEGER_ (TO INTEGER_)?)*) RBT_
    ;

jsonDescendentStep
    : DOT_ DOT_ jsonFieldName
    ;

jsonFunctionStep
    : DOT_ jsonItemMethod LP_ RP_
    ;

jsonItemMethod
    : identifier
    ;

jsonFilterExpr
    : QUESTION_ LP_ jsonCond RP_
    ;

jsonCond
    : jsonCond OR_ jsonCond | jsonCond AND_ jsonCond | jsonNegation
    | LP_ jsonCond RP_ | jsonComparison | jsonExistsCond
    | jsonInCond | jsonLikeCond | jsonLikeRegexCond
    | jsonEqRegexCond | jsonHasSubstringCond | jsonStartsWithCond
    ;

jsonNegation
    : NOT_ LP_ jsonCond RP_
    ;

jsonExistsCond
    : EXISTS LP_ jsonRelativePathExpr RP_
    ;

jsonHasSubstringCond
    : jsonRelativePathExpr HAS SUBSTRING (jsonString | jsonVar)
    ;

jsonStartsWithCond
    : jsonRelativePathExpr STARTS WITH (jsonString | jsonVar)
    ;

jsonLikeCond
    : jsonRelativePathExpr LIKE (jsonString | jsonVar)
    ;

jsonLikeRegexCond
    : jsonRelativePathExpr LIKE_REGEX (jsonString | jsonVar)
    ;

jsonEqRegexCond
    : jsonRelativePathExpr EQ_REGEX (jsonString | jsonVar)
    ;

jsonInCond
    : jsonRelativePathExpr IN valueList
    ;

valueList
    : LP_ (jsonScalar | jsonVar) (COMMA_ (jsonScalar | jsonVar))* RP_
    ;

jsonComparison
    : (jsonRelativePathExpr jsonComparePred (jsonVar | jsonScalar))
    | ((jsonVar | jsonScalar) jsonComparePred jsonRelativePathExpr)
    | (jsonScalar jsonComparePred jsonScalar)
    ;

jsonRelativePathExpr
    : AT_ jsonNonfunctionSteps? jsonFunctionStep?
    ;

jsonComparePred
    : DEQ_ | NEQ_ | LT_ | LTE_ | GTE_ | GT_
    ;

jsonVar
    : DOLLAR_ identifier
    ;

jsonScalar
    : jsonNumber | TRUE | FALSE | NULL | jsonString
    ;

jsonNumber
    : numberLiterals
    ;

jsonString
    : stringLiterals | identifier
    ;

existsCondition
    : EXISTS LP_ subquery RP_
    ;

inCondition
    : (expr NOT? IN LP_ (expressionList | subquery) RP_)
    | (exprList NOT? IN LP_ ((expressionList (COMMA_ expressionList)*) | subquery) RP_)
    ;

isOfTypeCondition
    : expr IS NOT? OF TYPE? LP_ ONLY? typeName (COMMA_ ONLY? typeName)* RP_
    ;

databaseCharset
    : AL32UTF8
    ;

nationalCharset
    : AL16UTF16 | UTF8
    ;

filenamePattern
    : STRING_
    ;

replacementFilenamePattern
    : STRING_
    ;

connectString
    : STRING_
    ;

argument
    : identifier
    ;

dataSource
    : identifier
    ;

implementationType
    : (owner DOT_)? name
    ;

implementationPackage
    : (owner DOT_)? name
    ;

label
    : identifier
    ;

libName
    : identifier
    ;

externalDatatype
    : dataType
    ;

capacityUnit
    : K | M | G | T | P | E
    ;

timeUnit
    : M | H
    ;

attributeDimensionName
    : identifier
    ;

sequenceName
    : (owner DOT_)? name
    ;

spfileName
    : STRING_
    ;

pfileName
    : STRING_
    ;

characterSetName
    : identifier
    ;

quotaUnit
    : ('M' | 'G' | 'T' | 'P' | 'E')
    ;

siteName
    : identifier
    ;

diskName
    : identifier
    ;

searchString
    : STRING_
    ;

attributeValue
    : STRING_
    ;

joinGroupName
    : identifier
    ;

restorePointName
    : identifier
    ;

libraryName
    : identifier
    ;

matchString
    : identifier | ASTERISK_
    ;

parameterType
    : (owner DOT_)? identifier
    ;

returnType
    : identifier
    ;

failgroupName
    : identifier
    ;

asmVolumeName
    : identifier
    ;

mountpathName
    : identifier
    ;

usageName
    : identifier
    ;

usergroupName
    : STRING_
    ;

varrayType
    : (owner DOT_)? name
    ;

stagingLogName
    : identifier
    ;

featureName
   : STRING_
   ;

optionName
    : STRING_
    ;

clauseOption
    : STRING_
    ;

clauseOptionPattern
    : STRING_
    ;

optionValue
    : STRING_
    ;

clause
    : STRING_
    ;

sqlStatement
    : STRING_
    ;


transportSecret
    : STRING_
    ;

hostName
    : STRING_
    ;

mapObject
    : STRING_
    ;

refreshInterval
    : INTEGER_
    ;

sourcePdbName
    : STRING_
    ;

appName
    : STRING_
    ;

commentValue
    : STRING_
    ;
numberValue
    : INTEGER_ | NUMBER_
    ;
appVersion
    : numberValue
    ;

startAppVersion
    : numberValue
    ;

endAppVersion
    : numberValue
    ;

patchNumber
    : INTEGER_
    ;

snapshotInterval
    : INTEGER_
    ;

snapshotName
    : STRING_
    ;

maxPdbSnapshots
    : INTEGER_
    ;

maxNumberOfSnapshots
    : INTEGER_
    ;

datetimeExpr
    : AT (LOCAL | TIME ZONE expr)
    ;

xmlFunction
    : xmlElementFunction
    | xmlCdataFunction
    | xmlAggFunction
    | xmlColattvalFunction
    | xmlExistsFunction
    | xmlForestFunction
    | xmlParseFunction
    | xmlPiFunction
    | xmlQueryFunction
    | xmlRootFunction
    | xmlSerializeFunction
    | xmlTableFunction
    | xmlIsSchemaValidFunction
    | specifiedFunctionName = (SYS_XMLGEN | SYS_XMLAGG | APPENDCHILDXML | DELETEXML | EXISTSNODE | EXTRACT | EXTRACTVALUE
        | INSERTCHILDXML | INSERTCHILDXMLAFTER | INSERTCHILDXMLBEFORE | INSERTXMLAFTER | INSERTXMLBEFORE
        | SYS_DBURIGEN | UPDATEXML | XMLCONCAT | XMLDIFF | XMLEXISTS | XMLISVALID | XMLPATCH | XMLSEQUENCE | XMLTRANSFORM) exprList
    | specifiedFunctionName = (DEPTH | PATH) LP_ correlationInteger RP_
    | specifiedFunctionName = XMLCOMMENT LP_ stringLiterals RP_
    ;

xmlElementFunction
    : XMLELEMENT LP_ identifier (COMMA_ xmlAttributes)? (COMMA_ exprWithAlias)* RP_
    ;

exprWithAlias
    : expr (AS alias)?
    ;

xmlAttributes
    : XMLATTRIBUTES LP_ exprWithAlias (COMMA_ exprWithAlias)* RP_
    ;

xmlCdataFunction
    : XMLCDATA LP_ stringLiterals RP_
    ;

xmlAggFunction
    : XMLAGG LP_ expr orderByClause? RP_
    ;

xmlColattvalFunction
    : XMLCOLATTVAL LP_ expr (xmlAsAliasOrEvalnameExpr)? (COMMA_ expr (xmlAsAliasOrEvalnameExpr)?)* RP_
    ;

xmlAsAliasOrEvalnameExpr
    :AS (alias | EVALNAME expr)
    ;

xmlExistsFunction
    : XMLEXISTS LP_ STRING_ xmlPassingClause? RP_
    ;

xmlForestFunction
   : XMLFOREST LP_ expr (xmlAsAliasOrEvalnameExpr)? (COMMA_ expr (xmlAsAliasOrEvalnameExpr)?)* RP_
   ;

xmlParseFunction
    : XMLPARSE LP_ (DOCUMENT | CONTENT) expr (WELLFORMED)? RP_
    ;

xmlPiFunction
    : XMLPI LP_ (EVALNAME expr | (NAME)? identifier) (COMMA_ expr)? RP_
    ;

xmlQueryFunction
    : XMLQUERY LP_ STRING_ xmlPassingClause? RETURNING CONTENT (NULL ON EMPTY)? RP_
    ;

xmlPassingClause
    : PASSING (BY VALUE)? expr (AS alias)? (COMMA_ expr (AS alias)?)*
    ;

xmlRootFunction
    : XMLROOT LP_ expr COMMA_ VERSION (expr | NO VALUE) (COMMA_ STANDALONE (YES | NO | NO VALUE))? RP_
    ;

xmlSerializeFunction
    : XMLSERIALIZE LP_ (DOCUMENT | CONTENT) expr (AS dataType)? (ENCODING STRING_)? (VERSION stringLiterals)? (NO INDENT | INDENT (SIZE EQ_ INTEGER_)?)? ((HIDE | SHOW) DEFAULTS)? RP_
    ;

xmlTableFunction
    : XMLTABLE LP_ (xmlNamespacesClause COMMA_)? STRING_ xmlTableOptions RP_
    ;

xmlIsSchemaValidFunction
    : (owner DOT_)* name DOT_ ISSCHEMAVALID LP_ expr (COMMA_ expr)* RP_
    ;

xmlNamespacesClause
    : XMLNAMESPACES LP_ (defaultString COMMA_)? (xmlNamespaceStringAsIdentifier | defaultString) (COMMA_ (xmlNamespaceStringAsIdentifier | defaultString))* RP_
    ;

xmlNamespaceStringAsIdentifier
    : STRING_ AS identifier
    ;

defaultString
    : DEFAULT STRING_
    ;

xmlTableOptions
    : xmlPassingClause? (RETURNING SEQUENCE BY REF)? (COLUMNS xmlTableColumn (COMMA_ xmlTableColumn)*)?
    ;

xmlTableColumn
    : columnName (FOR ORDINALITY | (dataType | XMLTYPE (LP_ SEQUENCE RP_ BY REF)?) (PATH STRING_)? (DEFAULT expr)?)
    ;

multisetExpr
    : columnName MULTISET multisetOperator (ALL | DISTINCT)? columnName
    ;

multisetOperator
    : EXCEPT
    | INTERSECT
    | UNION
    ;

builtinFunctionsExpr
    : (packageIdentifier DOT_ builtinFunction) dbLinkClause LP_ expr RP_
    ;

packageIdentifier
    : identifier
    ;

builtinFunction
   : identifier
   ;

superview
    : identifier
    ;

primaryName
    : identifier
    ;

serverFileName
    : identifier
    ;

keyForBlob
    : identifier
    ;

sourceText
    : .*? RBE_
    ;

fullPathName
    : STRING_
    ;

directoryObject
    : identifier
    ;

credentialName
    : identifier
    ;

agentDblink
    : STRING_
    ;

xPathsList
    : STRING_
    ;

sizeClause
    : INTEGER_ capacityUnit?
    ;

maxsizeClause
    : MAXSIZE (UNLIMITED | sizeClause)
    ;

editionType
    : VIEW | SYNONYM | PROCEDURE | FUNCTION | PACKAGE | PACKAGE BODY | TRIGGER | TYPE | TYPE BODY | LIBRARY
    ;

containerDataClause
    : (SET CONTAINER_DATA EQ_ (ALL | DEFAULT | containerName (COMMA_ containerName)*)
    | (ADD | REMOVE) CONTAINER_DATA containerName (COMMA_ containerName)*)
    (FOR (schemaName DOT_)? name)?
    ;

proxyClause
    : GRANT CONNECT THROUGH (ENTERPRISE USERS | dbUserProxy dbUserProxyClauses)
    | REVOKE CONNECT THROUGH (ENTERPRISE USERS | username)
    ;

dbUserProxy
    : identifier
    ;

dbUserProxyClauses
    : (WITH ((ROLE (ALL EXCEPT)? roleName (COMMA_ roleName)*) | NO ROLES))? (AUTHENTICATION REQUIRED | AUTHENTICATED USING PASSWORD)?
    ;

delimSpec
     : terminatedBySpec? optionallyEnclosedBySpec? lrtrimSpec?
     ;

terminatedBySpec
     : TERMINATED BY (STRING_ | WHITESPACE)
     ;

optionallyEnclosedBySpec
     : OPTIONALLY? ENCLOSED BY STRING_ AND STRING_
     ;

lrtrimSpec
     : LRTRIM
     ;
