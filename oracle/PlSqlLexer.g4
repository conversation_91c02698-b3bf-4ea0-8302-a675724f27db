lexer grammar PlSqlLexer;

options {
    caseInsensitive = true;
}

ABORT                         : 'ABORT';
ABS                           : 'ABS';
ACCESS                        : 'ACCESS';
ACCESSED                      : 'ACCESSED';
ACCESSIBLE                    : 'ACCESSIBLE';
ACCOUNT                       : 'ACCOUNT';
ACL                           : 'ACL';
ACROSS                        : 'ACROSS';
ACTION                        : 'ACTION';
ACTIONS                       : 'ACTIONS';
ACTIVATE                      : 'ACTIVATE';
ACTIVE                        : 'ACTIVE';
ADD                           : 'ADD';
ADD_COLUMN                    : 'ADD_COLUMN';
ADD_GROUP                     : 'ADD_GROUP';
ADMIN                         : 'ADMIN';
ADMINISTER                    : 'ADMINISTER';
ADVANCED                      : 'ADVANCED';
ADVISE                        : 'ADVISE';
ADVISOR                       : 'ADVISOR';
AFFINITY                      : 'AFFINITY';
AFTER                         : 'AFTER';
AGENT                         : 'AGENT';
AGGREGATE                     : 'AGGREGATE';
AL16UTF16                     : 'AL16UTF16';
AL32UTF8                      : 'AL32UTF8';
ALIAS                         : 'ALIAS';
ALL                           : 'ALL';
ALLOCATE                      : 'ALLOCATE';
ALLOW                         : 'ALLOW';
ALTER                         : 'ALTER';
ALWAYS                        : 'ALWAYS';
ANALYTIC                      : 'ANALYTIC';
ANALYZE                       : 'ANALYZE';
ANCESTOR                      : 'ANCESTOR';
ANCILLARY                     : 'ANCILLARY';
AND                           : 'AND';
ANY                           : 'ANY';
ANYDATA                       : 'ANYDATA';
ANYDATASET                    : 'ANYDATASET';
ANYSCHEMA                     : 'ANYSCHEMA';
ANYTYPE                       : 'ANYTYPE';
APP                           : 'APP';
APPEND                        : 'APPEND';
APPENDCHILDXML                : 'APPENDCHILDXML';
APPLICATION                   : 'APPLICATION';
APPLY                         : 'APPLY';
APPROX_RANK                   : 'APPROX_RANK';
ARCHIVAL                      : 'ARCHIVAL';
ARCHIVE                       : 'ARCHIVE';
ARCHIVED                      : 'ARCHIVED';
ARCHIVELOG                    : 'ARCHIVELOG';
ARRAY                         : 'ARRAY';
AS                            : 'AS';
ASC                           : 'ASC';
ASCII                         : 'ASCII';
ASSIGN                        : 'ASSIGN';
ASSOCIATE                     : 'ASSOCIATE';
ASYNC                         : 'ASYNC';
ASYNCHRONOUS                  : 'ASYNCHRONOUS';
AT                            : 'AT';
ATTRIBUTE                     : 'ATTRIBUTE';
ATTRIBUTES                    : 'ATTRIBUTES';
AUDIT                         : 'AUDIT';
AUTHENTICATED                 : 'AUTHENTICATED';
AUTHENTICATION                : 'AUTHENTICATION';
AUTHID                        : 'AUTHID';
AUTHORIZATION                 : 'AUTHORIZATION';
AUTO                          : 'AUTO';
AUTOALLOCATE                  : 'AUTOALLOCATE';
AUTOBACKUP                    : 'AUTOBACKUP';
AUTOEXTEND                    : 'AUTOEXTEND';
AUTOMATIC                     : 'AUTOMATIC';
AUTONOMOUS_TRANSACTION        : 'AUTONOMOUS_TRANSACTION';
AVAILABILITY                  : 'AVAILABILITY';
AVERAGE_RANK                  : 'AVERAGE_RANK';
AVG                           : 'AVG';
AZURE_ROLE                    : 'AZURE_ROLE';
AZURE_USER                    : 'AZURE_USER';
BACKUP                        : 'BACKUP';
BACKUPS                       : 'BACKUPS';
BACKUPSET                     : 'BACKUPSET';
BADFILE                       : 'BADFILE';
BALANCE                       : 'BALANCE';
BASIC                         : 'BASIC';
BASICFILE                     : 'BASICFILE';
BATCH                         : 'BATCH';
BECOME                        : 'BECOME';
BEFORE                        : 'BEFORE';
BEGIN                         : 'BEGIN';
BEGINNING                     : 'BEGINNING';
BEQUEATH                      : 'BEQUEATH';
BETWEEN                       : 'BETWEEN';
BFILE                         : 'BFILE';
BIGFILE                       : 'BIGFILE';
BINARY                        : 'BINARY';
BINARY_DOUBLE                 : 'BINARY_DOUBLE';
BINARY_FLOAT                  : 'BINARY_FLOAT';
BINARY_INTEGER                : 'BINARY_INTEGER';
BINDING                       : 'BINDING';
BITMAP                        : 'BITMAP';
BITMAPFILE                    : 'BITMAPFILE';
BLOB                          : 'BLOB';
BLOCK                         : 'BLOCK';
BLOCKCHAIN                    : 'BLOCKCHAIN';
BLOCKSIZE                     : 'BLOCKSIZE';
BODY                          : 'BODY';
BOOLEAN                       : 'BOOLEAN';
BOTH                          : 'BOTH';
BREADTH                       : 'BREADTH';
BTI                           : 'BTI';
BTITLE                        : 'BTITLE';
BUFFER_CACHE                  : 'BUFFER_CACHE';
BUFFER_POOL                   : 'BUFFER_POOL';
BUILD                         : 'BUILD';
BULK                          : 'BULK';
BY                            : 'BY';
BYTE                          : 'BYTE';
CACHE                         : 'CACHE';
CACHING                       : 'CACHING';
CALL                          : 'CALL';
CALLBACK                      : 'CALLBACK';
CANCEL                        : 'CANCEL';
CAPACITY                      : 'CAPACITY';
CASCADE                       : 'CASCADE';
CASE                          : 'CASE';
CAST                          : 'CAST';
CATEGORY                      : 'CATEGORY';
CELL_FLASH_CACHE              : 'CELL_FLASH_CACHE';
CERTIFICATE_DN                : 'CERTIFICATE_DN';
CHAINED                       : 'CHAINED';
CHANGE                        : 'CHANGE';
CHANGETRACKING                : 'CHANGETRACKING';
CHAR                          : 'CHAR';
CHAR_CS                       : 'CHAR_CS';
CHARACTER                     : 'CHARACTER';
CHARSETFORM                   : 'CHARSETFORM';
CHARSETID                     : 'CHARSETID';
CHECK                         : 'CHECK';
CHECKPOINT                    : 'CHECKPOINT';
CHILD                         : 'CHILD';
CHR                           : 'CHR';
CHUNK                         : 'CHUNK';
CLASS                         : 'CLASS';
CLASSIFIER                    : 'CLASSIFIER';
CLAUSE                        : 'CLAUSE';
CLEAN                         : 'CLEAN';
CLEANUP                       : 'CLEANUP';
CLEAR                         : 'CLEAR';
CLIENT                        : 'CLIENT';
CLOB                          : 'CLOB';
CLONE                         : 'CLONE';
CLOSE                         : 'CLOSE';
CLUSTER                       : 'CLUSTER';
CLUSTER_DETAILS               : 'CLUSTER_DETAILS';
CLUSTER_DISTANCE              : 'CLUSTER_DISTANCE';
CLUSTER_ID                    : 'CLUSTER_ID';
CLUSTER_PROBABILITY           : 'CLUSTER_PROBABILITY';
CLUSTER_SET                   : 'CLUSTER_SET';
CLUSTERING                    : 'CLUSTERING';
COALESCE                      : 'COALESCE';
COARSE                        : 'COARSE';
COLD                          : 'COLD';
COLLATE                       : 'COLLATE';
COLLATION                     : 'COLLATION';
COLLECT                       : 'COLLECT';
COLUMN                        : 'COLUMN';
COLUMN_VALUE                  : 'COLUMN_VALUE';
COLUMNS                       : 'COLUMNS';
COMMENT                       : 'COMMENT';
COMMIT                        : 'COMMIT';
COMMITTED                     : 'COMMITTED';
COMPACT                       : 'COMPACT';
COMPATIBILITY                 : 'COMPATIBILITY';
COMPILE                       : 'COMPILE';
COMPLETE                      : 'COMPLETE';
COMPONENT                     : 'COMPONENT';
COMPONENTS                    : 'COMPONENTS';
COMPOSITE_LIMIT               : 'COMPOSITE_LIMIT';
COMPRESS                      : 'COMPRESS';
COMPUTATION                   : 'COMPUTATION';
COMPUTE                       : 'COMPUTE';
CON_ID                        : 'CON_ID';
CON_NAME                      : 'CON_NAME';
CONDITION                     : 'CONDITION';
CONFIRM                       : 'CONFIRM';
CONNECT                       : 'CONNECT';
CONNECT_BY_ROOT               : 'CONNECT_BY_ROOT';
CONNECT_TIME                  : 'CONNECT_TIME';
CONSIDER                      : 'CONSIDER';
CONSISTENT                    : 'CONSISTENT';
CONSTANT                      : 'CONSTANT';
CONSTRAINT                    : 'CONSTRAINT';
CONSTRAINTS                   : 'CONSTRAINTS';
CONSTRUCTOR                   : 'CONSTRUCTOR';
CONTAINER                     : 'CONTAINER';
CONTAINER_DATA                : 'CONTAINER_DATA';
CONTAINER_MAP                 : 'CONTAINER_MAP';
CONTAINERS                    : 'CONTAINERS';
CONTAINERS_DEFAULT            : 'CONTAINERS_DEFAULT';
CONTENT                       : 'CONTENT';
CONTENTS                      : 'CONTENTS';
CONTEXT                       : 'CONTEXT';
CONTINUE                      : 'CONTINUE';
CONTROL                       : 'CONTROL';
CONTROLFILE                   : 'CONTROLFILE';
CONVERSION                    : 'CONVERSION';
CONVERT                       : 'CONVERT';
COOKIE                        : 'COOKIE';
COPY                          : 'COPY';
CORR                          : 'CORR';
CORR_K                        : 'CORR_K';
CORR_S                        : 'CORR_S';
CORRUPTION                    : 'CORRUPTION';
COST                          : 'COST';
COUNT                         : 'COUNT';
COVAR_POP                     : 'COVAR_POP';
COVAR_SAMP                    : 'COVAR_SAMP';
COVERAGE                      : 'COVERAGE';
CPU_PER_CALL                  : 'CPU_PER_CALL';
CPU_PER_SESSION               : 'CPU_PER_SESSION';
CRE                           : 'CRE';
CREATE                        : 'CREATE';
CREATION                      : 'CREATION';
CREDENTIAL                    : 'CREDENTIAL';
CREDENTIALS                   : 'CREDENTIALS';
CRITICAL                      : 'CRITICAL';
CROSS                         : 'CROSS';
CUBE                          : 'CUBE';
CUME_DIST                     : 'CUME_DIST';
CURRENT                       : 'CURRENT';
CURRENT_USER                  : 'CURRENT_USER';
CURSOR                        : 'CURSOR';
CYCLE                         : 'CYCLE';
DANGLING                      : 'DANGLING';
DATA                          : 'DATA';
DATABASE                      : 'DATABASE';
DATAFILE                      : 'DATAFILE';
DATAFILES                     : 'DATAFILES';
DATAGUARDCONFIG               : 'DATAGUARDCONFIG';
DATAPUMP                      : 'DATAPUMP';
DATE                          : 'DATE';
DAY                           : 'DAY';
DAYS                          : 'DAYS';
DB_RECOVERY_FILE_DEST_SIZE    : 'DB_RECOVERY_FILE_DEST_SIZE';
DB_ROLE_CHANGE                : 'DB_ROLE_CHANGE';
DBA_RECYCLEBIN                : 'DBA_RECYCLEBIN';
DDL                           : 'DDL';
DEALLOCATE                    : 'DEALLOCATE';
DEBUG                         : 'DEBUG';
DEC                           : 'DEC';
DECIMAL                       : 'DECIMAL';
DECLARE                       : 'DECLARE';
DECODE                        : 'DECODE';
DECREMENT                     : 'DECREMENT';
DECRYPT                       : 'DECRYPT';
DEDUPLICATE                   : 'DEDUPLICATE';
DEFAULT                       : 'DEFAULT';
DEFAULT_COLLATION             : 'DEFAULT_COLLATION';
DEFAULT_CREDENTIAL            : 'DEFAULT_CREDENTIAL';
DEFAULTS                      : 'DEFAULTS';
DEFERRABLE                    : 'DEFERRABLE';
DEFERRED                      : 'DEFERRED';
DEFINE                        : 'DEFINE';
DEFINER                       : 'DEFINER';
DEFINITION                    : 'DEFINITION';
DEGREE                        : 'DEGREE';
DELEGATE                      : 'DELEGATE';
DELETE                        : 'DELETE';
DELETE_ALL                    : 'DELETE_ALL';
DELETEXML                     : 'DELETEXML';
DELETING                      : 'DELETING';
DEMAND                        : 'DEMAND';
DENSE_RANK                    : 'DENSE_RANK';
DEPENDENT                     : 'DEPENDENT';
DEPRECATE                     : 'DEPRECATE';
DEPTH                         : 'DEPTH';
DESC                          : 'DESC';
DESTROY                       : 'DESTROY';
DETERMINES                    : 'DETERMINES';
DETERMINISTIC                 : 'DETERMINISTIC';
DICTIONARY                    : 'DICTIONARY';
DIGEST                        : 'DIGEST';
DIMENSION                     : 'DIMENSION';
DIRECT_LOAD                   : 'DIRECT_LOAD';
DIRECT_PATH                   : 'DIRECT_PATH';
DIRECTORY                     : 'DIRECTORY';
DISABLE                       : 'DISABLE';
DISABLE_ALL                   : 'DISABLE_ALL';
DISALLOW                      : 'DISALLOW';
DISASSOCIATE                  : 'DISASSOCIATE';
DISCARD                       : 'DISCARD';
DISCARDFILE                   : 'DISCARDFILE';
DISCONNECT                    : 'DISCONNECT';
DISK                          : 'DISK';
DISKGROUP                     : 'DISKGROUP';
DISKS                         : 'DISKS';
DISMOUNT                      : 'DISMOUNT';
DISTINCT                      : 'DISTINCT';
DISTRIBUTE                    : 'DISTRIBUTE';
DISTRIBUTED                   : 'DISTRIBUTED';
DML                           : 'DML';
DOCUMENT                      : 'DOCUMENT';
DOUBLE                        : 'DOUBLE';
DOWNGRADE                     : 'DOWNGRADE';
DROP                          : 'DROP';
DROP_COLUMN                   : 'DROP_COLUMN';
DROP_GROUP                    : 'DROP_GROUP';
DUMPSET                       : 'DUMPSET';
DUPLICATE                     : 'DUPLICATE';
DUPLICATED                    : 'DUPLICATED';
DURATION                      : 'DURATION';
DV                            : 'DV';
EACH                          : 'EACH';
EDITION                       : 'EDITION';
EDITIONABLE                   : 'EDITIONABLE';
EDITIONING                    : 'EDITIONING';
EDITIONS                      : 'EDITIONS';
ELEMENT                       : 'ELEMENT';
ELSE                          : 'ELSE';
ELSIF                         : 'ELSIF';
EMPTY                         : 'EMPTY';
ENABLE                        : 'ENABLE';
ENABLE_ALL                    : 'ENABLE_ALL';
ENCLOSED                      : 'ENCLOSED';
ENCODING                      : 'ENCODING';
ENCRYPT                       : 'ENCRYPT';
ENCRYPTION                    : 'ENCRYPTION';
END                           : 'END';
ENFORCED                      : 'ENFORCED';
ENTERPRISE                    : 'ENTERPRISE';
EQ_REGEX                      : 'EQ_REGEX';
EQUALS_PATH                   : 'EQUALS_PATH';
EQUIVALENCE                   : 'EQUIVALENCE';
ERR                           : 'ERR';
ERROR                         : 'ERROR';
ERRORS                        : 'ERRORS';
ESCAPE                        : 'ESCAPE';
EVAL                          : 'EVAL';
EVALNAME                      : 'EVALNAME';
EVALUATE                      : 'EVALUATE';
EVALUATION                    : 'EVALUATION';
EVENTS                        : 'EVENTS';
EVERY                         : 'EVERY';
EXCEPT                        : 'EXCEPT';
EXCEPTION                     : 'EXCEPTION';
EXCEPTION_INIT                : 'EXCEPTION_INIT';
EXCEPTIONS                    : 'EXCEPTIONS';
EXCHANGE                      : 'EXCHANGE';
EXCLUDE                       : 'EXCLUDE';
EXCLUDING                     : 'EXCLUDING';
EXCLUSIVE                     : 'EXCLUSIVE';
EXECUTE                       : 'EXECUTE';
EXEMPT                        : 'EXEMPT';
EXISTS                        : 'EXISTS';
EXISTSNODE                    : 'EXISTSNODE';
EXIT                          : 'EXIT';
EXPIRE                        : 'EXPIRE';
EXPLAIN                       : 'EXPLAIN';
EXPORT                        : 'EXPORT';
EXTEND                        : 'EXTEND';
EXTENDED                      : 'EXTENDED';
EXTENT                        : 'EXTENT';
EXTERNAL                      : 'EXTERNAL';
EXTERNALLY                    : 'EXTERNALLY';
EXTRACT                       : 'EXTRACT';
EXTRACTVALUE                  : 'EXTRACTVALUE';
FACT                          : 'FACT';
FACTOR                        : 'FACTOR';
FAILED                        : 'FAILED';
FAILED_LOGIN_ATTEMPTS         : 'FAILED_LOGIN_ATTEMPTS';
FAILGROUP                     : 'FAILGROUP';
FAILOVER                      : 'FAILOVER';
FAILURE                       : 'FAILURE';
FALSE                         : 'FALSE';
FAR                           : 'FAR';
FAST                          : 'FAST';
FEATURE                       : 'FEATURE';
FEATURE_COMPARE               : 'FEATURE_COMPARE';
FEATURE_DETAILS               : 'FEATURE_DETAILS';
FEATURE_ID                    : 'FEATURE_ID';
FEATURE_SET                   : 'FEATURE_SET';
FEATURE_VALUE                 : 'FEATURE_VALUE';
FETCH                         : 'FETCH';
FILE                          : 'FILE';
FILE_NAME_CONVERT             : 'FILE_NAME_CONVERT';
FILEGROUP                     : 'FILEGROUP';
FILESYSTEM_LIKE_LOGGING       : 'FILESYSTEM_LIKE_LOGGING';
FILTER                        : 'FILTER';
FINAL                         : 'FINAL';
FINE                          : 'FINE';
FINISH                        : 'FINISH';
FIRST                         : 'FIRST';
FIRST_VALUE                   : 'FIRST_VALUE';
FLASH_CACHE                   : 'FLASH_CACHE';
FLASHBACK                     : 'FLASHBACK';
FLEX                          : 'FLEX';
FLOAT                         : 'FLOAT';
FLUSH                         : 'FLUSH';
FOLDER                        : 'FOLDER';
FOLLOWING                     : 'FOLLOWING';
FOLLOWS                       : 'FOLLOWS';
FOR                           : 'FOR';
FORALL                        : 'FORALL';
FORCE                         : 'FORCE';
FOREIGN                       : 'FOREIGN';
FORMAT                        : 'FORMAT';
FREELIST                      : 'FREELIST';
FREELISTS                     : 'FREELISTS';
FREEPOOLS                     : 'FREEPOOLS';
FRESH                         : 'FRESH';
FROM                          : 'FROM';
FULL                          : 'FULL';
FUNCTION                      : 'FUNCTION';
FUNCTIONS                     : 'FUNCTIONS';
GENERATED                     : 'GENERATED';
GET                           : 'GET';
GLOBAL                        : 'GLOBAL';
GLOBAL_NAME                   : 'GLOBAL_NAME';
GLOBAL_TOPIC_ENABLED          : 'GLOBAL_TOPIC_ENABLED';
GLOBALLY                      : 'GLOBALLY';
GOTO                          : 'GOTO';
GRANT                         : 'GRANT';
GRANTED                       : 'GRANTED';
GROUP                         : 'GROUP';
GROUP_ID                      : 'GROUP_ID';
GROUPING                      : 'GROUPING';
GROUPING_ID                   : 'GROUPING_ID';
GROUPS                        : 'GROUPS';
GUARANTEE                     : 'GUARANTEE';
GUARD                         : 'GUARD';
HAS                           : 'HAS';
HASH                          : 'HASH';
HASHING                       : 'HASHING';
HASHKEYS                      : 'HASHKEYS';
HAVING                        : 'HAVING';
HEADER                        : 'HEADER';
HEAP                          : 'HEAP';
HIDE                          : 'HIDE';
HIER_ANCESTOR                 : 'HIER_ANCESTOR';
HIER_CAPTION                  : 'HIER_CAPTION';
HIER_DEPTH                    : 'HIER_DEPTH';
HIER_DESCRIPTION              : 'HIER_DESCRIPTION';
HIER_LAG                      : 'HIER_LAG';
HIER_LEAD                     : 'HIER_LEAD';
HIER_LEVEL                    : 'HIER_LEVEL';
HIER_MEMBER_NAME              : 'HIER_MEMBER_NAME';
HIER_MEMBER_UNIQUE_NAME       : 'HIER_MEMBER_UNIQUE_NAME';
HIER_PARENT                   : 'HIER_PARENT';
HIERARCHIES                   : 'HIERARCHIES';
HIERARCHY                     : 'HIERARCHY';
HIGH                          : 'HIGH';
HISTORY                       : 'HISTORY';
HOST                          : 'HOST';
HOT                           : 'HOT';
HOUR                          : 'HOUR';
HOURS                         : 'HOURS';
HTTP                          : 'HTTP';
IAM_GROUP_NAME                : 'IAM_GROUP_NAME';
IAM_PRINCIPAL_NAME            : 'IAM_PRINCIPAL_NAME';
ID                            : 'ID';
IDENTIFIED                    : 'IDENTIFIED';
IDENTIFIER                    : 'IDENTIFIER';
IDENTITY                      : 'IDENTITY';
IDLE                          : 'IDLE';
IDLE_TIME                     : 'IDLE_TIME';
IF                            : 'IF';
IGNORE                        : 'IGNORE';
ILM                           : 'ILM';
IMMEDIATE                     : 'IMMEDIATE';
IMMUTABLE                     : 'IMMUTABLE';
IMPORT                        : 'IMPORT';
IN                            : 'IN';
INACTIVE                      : 'INACTIVE';
INACTIVE_ACCOUNT_TIME         : 'INACTIVE_ACCOUNT_TIME';
INCLUDE                       : 'INCLUDE';
INCLUDING                     : 'INCLUDING';
INCR                          : 'INCR';
INCREMENT                     : 'INCREMENT';
INDENT                        : 'INDENT';
INDEX                         : 'INDEX';
INDEX_ALL_PATHS               : 'INDEX_ALL_PATHS';
INDEXES                       : 'INDEXES';
INDEXING                      : 'INDEXING';
INDEXTYPE                     : 'INDEXTYPE';
INDEXTYPES                    : 'INDEXTYPES';
INDICATOR                     : 'INDICATOR';
INDICES                       : 'INDICES';
INFINITE                      : 'INFINITE';
INHERIT                       : 'INHERIT';
INITIAL                       : 'INITIAL';
INITIALIZED                   : 'INITIALIZED';
INITIALLY                     : 'INITIALLY';
INITRANS                      : 'INITRANS';
INLINE                        : 'INLINE';
INMEMORY                      : 'INMEMORY';
INNER                         : 'INNER';
INSERT                        : 'INSERT';
INSERTCHILDXML                : 'INSERTCHILDXML';
INSERTCHILDXMLAFTER           : 'INSERTCHILDXMLAFTER';
INSERTCHILDXMLBEFORE          : 'INSERTCHILDXMLBEFORE';
INSERTING                     : 'INSERTING';
INSERTXMLAFTER                : 'INSERTXMLAFTER';
INSERTXMLBEFORE               : 'INSERTXMLBEFORE';
INSTALL                       : 'INSTALL';
INSTANCE                      : 'INSTANCE';
INSTANCES                     : 'INSTANCES';
INSTANTIABLE                  : 'INSTANTIABLE';
INSTEAD                       : 'INSTEAD';
INT                           : 'INT';
INTEGER                       : 'INTEGER';
INTERLEAVED                   : 'INTERLEAVED';
INTERNAL                      : 'INTERNAL';
INTERSECT                     : 'INTERSECT';
INTERVAL                      : 'INTERVAL';
INTO                          : 'INTO';
INVALIDATE                    : 'INVALIDATE';
INVALIDATION                  : 'INVALIDATION';
INVISIBLE                     : 'INVISIBLE';
IS                            : 'IS';
ISOLATION                     : 'ISOLATION';
ISSCHEMAVALID                 : 'ISSCHEMAVALID';
ITERATE                       : 'ITERATE';
JAVA                          : 'JAVA';
JOB                           : 'JOB';
JOIN                          : 'JOIN';
JSON                          : 'JSON';
JSON_EXISTS                   : 'JSON_EXISTS';
JSON_TEXTCONTAINS             : 'JSON_TEXTCONTAINS';
KEEP                          : 'KEEP';
KEEP_DUPLICATES               : 'KEEP_DUPLICATES';
KERBEROS                      : 'KERBEROS';
KERBEROS_PRINCIPAL_NAME       : 'KERBEROS_PRINCIPAL_NAME';
KEY                           : 'KEY';
KEYS                          : 'KEYS';
KILL                          : 'KILL';
LABEL                         : 'LABEL';
LAG                           : 'LAG';
LAG_DIF_PERCENT               : 'LAG_DIF_PERCENT';
LAG_DIFF                      : 'LAG_DIFF';
LANGUAGE                      : 'LANGUAGE';
LAST                          : 'LAST';
LAST_VALUE                    : 'LAST_VALUE';
LATERAL                       : 'LATERAL';
LAX                           : 'LAX';
LEAD                          : 'LEAD';
LEAD_CDB                      : 'LEAD_CDB';
LEAD_CDB_URI                  : 'LEAD_CDB_URI';
LEAD_DIFF                     : 'LEAD_DIFF';
LEAD_DIFF_PERCENT             : 'LEAD_DIFF_PERCENT';
LEADING                       : 'LEADING';
LEAF                          : 'LEAF';
LEFT                          : 'LEFT';
LENGTH                        : 'LENGTH';
LESS                          : 'LESS';
LEVEL                         : 'LEVEL';
LEVELS                        : 'LEVELS';
LIBRARY                       : 'LIBRARY';
LIFE                          : 'LIFE';
LIKE                          : 'LIKE';
LIKE2                         : 'LIKE2';
LIKE4                         : 'LIKE4';
LIKE_REGEX                    : 'LIKE_REGEX';
LIKEC                         : 'LIKEC';
LIMIT                         : 'LIMIT';
LINEAR                        : 'LINEAR';
LINK                          : 'LINK';
LIST                          : 'LIST';
LISTAGG                       : 'LISTAGG';
LN                            : 'LN';
LNO                           : 'LNO';
LOAD                          : 'LOAD';
LOB                           : 'LOB';
LOBPREFETCH                   : 'LOBPREFETCH';
LOBS                          : 'LOBS';
LOCAL                         : 'LOCAL';
LOCALTIME                     : 'LOCALTIME';
LOCALTIMESTAMP                : 'LOCALTIMESTAMP';
LOCATION                      : 'LOCATION';
LOCATOR                       : 'LOCATOR';
LOCK                          : 'LOCK';
LOCKDOWN                      : 'LOCKDOWN';
LOCKED                        : 'LOCKED';
LOCKING                       : 'LOCKING';
LOG                           : 'LOG';
LOGFILE                       : 'LOGFILE';
LOGFILES                      : 'LOGFILES';
LOGGING                       : 'LOGGING';
LOGICAL                       : 'LOGICAL';
LOGICAL_READS_PER_CALL        : 'LOGICAL_READS_PER_CALL';
LOGICAL_READS_PER_SESSION     : 'LOGICAL_READS_PER_SESSION';
LOGMINING                     : 'LOGMINING';
LOGOFF                        : 'LOGOFF';
LOGON                         : 'LOGON';
LONG                          : 'LONG';
LOOP                          : 'LOOP';
LOST                          : 'LOST';
LOW                           : 'LOW';
LRTRIM                        : 'LRTRIM';
MAIN                          : 'MAIN';
MANAGE                        : 'MANAGE';
MANAGED                       : 'MANAGED';
MANAGEMENT                    : 'MANAGEMENT';
MANDATORY                     : 'MANDATORY';
MANUAL                        : 'MANUAL';
MAP                           : 'MAP';
MAPPING                       : 'MAPPING';
MASTER                        : 'MASTER';
MATCH                         : 'MATCH';
MATCH_NUMBER                  : 'MATCH_NUMBER';
MATCH_RECOGNIZE               : 'MATCH_RECOGNIZE';
MATCHED                       : 'MATCHED';
MATERIALIZE                   : 'MATERIALIZE';
MATERIALIZED                  : 'MATERIALIZED';
MAX                           : 'MAX';
MAX_AUDIT_SIZE                : 'MAX_AUDIT_SIZE';
MAX_DIAG_SIZE                 : 'MAX_DIAG_SIZE';
MAXDATAFILES                  : 'MAXDATAFILES';
MAXEXTENTS                    : 'MAXEXTENTS';
MAXIMIZE                      : 'MAXIMIZE';
MAXINSTANCES                  : 'MAXINSTANCES';
MAXLEN                        : 'MAXLEN';
MAXLOGFILES                   : 'MAXLOGFILES';
MAXLOGHISTORY                 : 'MAXLOGHISTORY';
MAXLOGMEMBERS                 : 'MAXLOGMEMBERS';
MAXSIZE                       : 'MAXSIZE';
MAXTRANS                      : 'MAXTRANS';
MAXVALUE                      : 'MAXVALUE';
MEASURE                       : 'MEASURE';
MEASURES                      : 'MEASURES';
MEDIAN                        : 'MEDIAN';
MEDIUM                        : 'MEDIUM';
MEMBER                        : 'MEMBER';
MEMCOMPRESS                   : 'MEMCOMPRESS';
MEMOPTIMIZE                   : 'MEMOPTIMIZE';
MEMORY                        : 'MEMORY';
MERGE                         : 'MERGE';
METADATA                      : 'METADATA';
MIGRATE                       : 'MIGRATE';
MIGRATION                     : 'MIGRATION';
MIN                           : 'MIN';
MINEXTENTS                    : 'MINEXTENTS';
MINIMIZE                      : 'MINIMIZE';
MINIMUM                       : 'MINIMUM';
MINING                        : 'MINING';
MINUS                         : 'MINUS';
MINUTE                        : 'MINUTE';
MINUTES                       : 'MINUTES';
MINVALUE                      : 'MINVALUE';
MIRROR                        : 'MIRROR';
MIRRORCOLD                    : 'MIRRORCOLD';
MIRRORHOT                     : 'MIRRORHOT';
MLSLABEL                      : 'MLSLABEL';
MOD                           : 'MOD';
MODE                          : 'MODE';
MODEL                         : 'MODEL';
MODIFICATION                  : 'MODIFICATION';
MODIFY                        : 'MODIFY';
MODIFY_COLUMN_TYPE            : 'MODIFY_COLUMN_TYPE';
MONITOR                       : 'MONITOR';
MONITORING                    : 'MONITORING';
MONTH                         : 'MONTH';
MONTHS                        : 'MONTHS';
MOUNT                         : 'MOUNT';
MOUNTPATH                     : 'MOUNTPATH';
MOVE                          : 'MOVE';
MOVEMENT                      : 'MOVEMENT';
MULTISET                      : 'MULTISET';
MUTABLE                       : 'MUTABLE';
NAME                          : 'NAME';
NAMED                         : 'NAMED';
NAMES                         : 'NAMES';
NAMESPACE                     : 'NAMESPACE';
NAN                           : 'NAN';
NATIONAL                      : 'NATIONAL';
NATURAL                       : 'NATURAL';
NATURALN                      : 'NATURALN';
NAV                           : 'NAV';
NCHAR                         : 'NCHAR';
NCHAR_CS                      : 'NCHAR_CS';
NCHR                          : 'NCHR';
NCLOB                         : 'NCLOB';
NEG                           : 'NEG';
NESTED                        : 'NESTED';
NETWORK                       : 'NETWORK';
NEVER                         : 'NEVER';
NEW                           : 'NEW';
NEXT                          : 'NEXT';
NLS_COMP                      : 'NLS_COMP';
NO                            : 'NO';
NOARCHIVELOG                  : 'NOARCHIVELOG';
NOAUDIT                       : 'NOAUDIT';
NOCACHE                       : 'NOCACHE';
NOCOMPRESS                    : 'NOCOMPRESS';
NOCOPY                        : 'NOCOPY';
NOCYCLE                       : 'NOCYCLE';
NODELAY                       : 'NODELAY';
NOEXTEND                      : 'NOEXTEND';
NOFORCE                       : 'NOFORCE';
NOGUARANTEE                   : 'NOGUARANTEE';
NOKEEP                        : 'NOKEEP';
NOLOGGING                     : 'NOLOGGING';
NOMAPPING                     : 'NOMAPPING';
NOMAXVALUE                    : 'NOMAXVALUE';
NOMINIMIZE                    : 'NOMINIMIZE';
NOMINVALUE                    : 'NOMINVALUE';
NOMONITORING                  : 'NOMONITORING';
NONBLOCKING                   : 'NONBLOCKING';
NONE                          : 'NONE';
NONEDITIONABLE                : 'NONEDITIONABLE';
NONSCHEMA                     : 'NONSCHEMA';
NOORDER                       : 'NOORDER';
NOPARALLEL                    : 'NOPARALLEL';
NOPROMPT                      : 'NOPROMPT';
NORELOCATE                    : 'NORELOCATE';
NORELY                        : 'NORELY';
NOREPAIR                      : 'NOREPAIR';
NOREPLY                       : 'NOREPLY';
NORESETLOGS                   : 'NORESETLOGS';
NOREVERSE                     : 'NOREVERSE';
NORMAL                        : 'NORMAL';
NOROWDEPENDENCIES             : 'NOROWDEPENDENCIES';
NOSCALE                       : 'NOSCALE';
NOSHARD                       : 'NOSHARD';
NOSORT                        : 'NOSORT';
NOSWITCH                      : 'NOSWITCH';
NOT                           : 'NOT';
NOTHING                       : 'NOTHING';
NOTIFICATION                  : 'NOTIFICATION';
NOVALIDATE                    : 'NOVALIDATE';
NOWAIT                        : 'NOWAIT';
NTH_VALUE                     : 'NTH_VALUE';
NTILE                         : 'NTILE';
NULL                          : 'NULL';
NULLS                         : 'NULLS';
NUMBER                        : 'NUMBER';
NUMERIC                       : 'NUMERIC';
NVARCHAR2                     : 'NVARCHAR2';
OBJECT                        : 'OBJECT';
OF                            : 'OF';
OFF                           : 'OFF';
OFFLINE                       : 'OFFLINE';
OFFSET                        : 'OFFSET';
OID                           : 'OID';
OIDINDEX                      : 'OIDINDEX';
OLD                           : 'OLD';
OLS                           : 'OLS';
OLTP                          : 'OLTP';
ON                            : 'ON';
ONE                           : 'ONE';
ONLINE                        : 'ONLINE';
ONLINELOG                     : 'ONLINELOG';
ONLY                          : 'ONLY';
OPEN                          : 'OPEN';
OPERATOR                      : 'OPERATOR';
OPTIMAL                       : 'OPTIMAL';
OPTIMIZE                      : 'OPTIMIZE';
OPTION                        : 'OPTION';
OPTIONALLY                    : 'OPTIONALLY';
OR                            : 'OR';
ORDER                         : 'ORDER';
ORDINALITY                    : 'ORDINALITY';
ORGANIZATION                  : 'ORGANIZATION';
OTHER                         : 'OTHER';
OTHERS                        : 'OTHERS';
OUT                           : 'OUT';
OUTER                         : 'OUTER';
OUTLINE                       : 'OUTLINE';
OVER                          : 'OVER';
OVERFLOW                      : 'OVERFLOW';
OVERRIDING                    : 'OVERRIDING';
OWN                           : 'OWN';
OWNER                         : 'OWNER';
OWNERSHIP                     : 'OWNERSHIP';
PACKAGE                       : 'PACKAGE';
PACKAGES                      : 'PACKAGES';
PAIRS                         : 'PAIRS';
PARALLEL                      : 'PARALLEL';
PARALLEL_ENABLE               : 'PARALLEL_ENABLE';
PARAM                         : 'PARAM';
PARAMETER                     : 'PARAMETER';
PARAMETERFILE                 : 'PARAMETERFILE';
PARAMETERS                    : 'PARAMETERS';
PARENT                        : 'PARENT';
PARITY                        : 'PARITY';
PARTIAL                       : 'PARTIAL';
PARTITION                     : 'PARTITION';
PARTITIONS                    : 'PARTITIONS';
PARTITIONSET                  : 'PARTITIONSET';
PASSING                       : 'PASSING';
PASSWORD                      : 'PASSWORD';
PASSWORD_GRACE_TIME           : 'PASSWORD_GRACE_TIME';
PASSWORD_LIFE_TIME            : 'PASSWORD_LIFE_TIME';
PASSWORD_LOCK_TIME            : 'PASSWORD_LOCK_TIME';
PASSWORD_REUSE_MAX            : 'PASSWORD_REUSE_MAX';
PASSWORD_REUSE_TIME           : 'PASSWORD_REUSE_TIME';
PASSWORD_ROLLOVER_TIME        : 'PASSWORD_ROLLOVER_TIME';
PASSWORD_VERIFY_FUNCTION      : 'PASSWORD_VERIFY_FUNCTION';
PASSWORDFILE_METADATA_CACHE   : 'PASSWORDFILE_METADATA_CACHE';
PAST                          : 'PAST';
PATCH                         : 'PATCH';
PATH                          : 'PATH';
PATHS                         : 'PATHS';
PATTERN                       : 'PATTERN';
PCTFREE                       : 'PCTFREE';
PCTINCREASE                   : 'PCTINCREASE';
PCTTHRESHOLD                  : 'PCTTHRESHOLD';
PCTUSED                       : 'PCTUSED';
PCTVERSION                    : 'PCTVERSION';
PDBS                          : 'PDBS';
PER                           : 'PER';
PERCENT                       : 'PERCENT';
PERCENT_RANK                  : 'PERCENT_RANK';
PERCENTILE_CONT               : 'PERCENTILE_CONT';
PERCENTILE_DISC               : 'PERCENTILE_DISC';
PERFORMANCE                   : 'PERFORMANCE';
PERIOD                        : 'PERIOD';
PERMANENT                     : 'PERMANENT';
PERMISSION                    : 'PERMISSION';
PERMUTE                       : 'PERMUTE';
PERSISTABLE                   : 'PERSISTABLE';
PFILE                         : 'PFILE';
PHYSICAL                      : 'PHYSICAL';
PIKEY                         : 'PIKEY';
PIPE                          : 'PIPE';
PIPELINED                     : 'PIPELINED';
PIVOT                         : 'PIVOT';
PLAN                          : 'PLAN';
PLS_INTEGER                   : 'PLS_INTEGER';
PLUGGABLE                     : 'PLUGGABLE';
PNO                           : 'PNO';
POINT                         : 'POINT';
POLICY                        : 'POLICY';
POLYMORPHIC                   : 'POLYMORPHIC';
PORT                          : 'PORT';
POSITION                      : 'POSITION';
POSITIVE                      : 'POSITIVE';
POSITIVEN                     : 'POSITIVEN';
POST_TRANSACTION              : 'POST_TRANSACTION';
POWER                         : 'POWER';
PRAGMA                        : 'PRAGMA';
PREBUILT                      : 'PREBUILT';
PRECEDING                     : 'PRECEDING';
PRECISION                     : 'PRECISION';
PREDICTION                    : 'PREDICTION';
PREDICTION_BOUNDS             : 'PREDICTION_BOUNDS';
PREDICTION_DETAILS            : 'PREDICTION_DETAILS';
PREDICTION_PROBABILITY        : 'PREDICTION_PROBABILITY';
PREDICTION_SET                : 'PREDICTION_SET';
PREPARE                       : 'PREPARE';
PRESENT                       : 'PRESENT';
PRESERVE                      : 'PRESERVE';
PREV                          : 'PREV';
PRIMARY                       : 'PRIMARY';
PRIOR                         : 'PRIOR';
PRIORITY                      : 'PRIORITY';
PRIVATE                       : 'PRIVATE';
PRIVATE_SGA                   : 'PRIVATE_SGA';
PRIVILEGE                     : 'PRIVILEGE';
PRIVILEGED                    : 'PRIVILEGED';
PRIVILEGES                    : 'PRIVILEGES';
PROCEDURAL                    : 'PROCEDURAL';
PROCEDURE                     : 'PROCEDURE';
PROCESS                       : 'PROCESS';
PROFILE                       : 'PROFILE';
PROGRAM                       : 'PROGRAM';
PROPERTY                      : 'PROPERTY';
PROTECTED                     : 'PROTECTED';
PROTECTION                    : 'PROTECTION';
PROXY                         : 'PROXY';
PRUNING                       : 'PRUNING';
PUBLIC                        : 'PUBLIC';
PURGE                         : 'PURGE';
QUALIFY                       : 'QUALIFY';
QUERY                         : 'QUERY';
QUEUE                         : 'QUEUE';
QUIESCE                       : 'QUIESCE';
QUORUM                        : 'QUORUM';
QUOTA                         : 'QUOTA';
QUOTAGROUP                    : 'QUOTAGROUP';
RAISE                         : 'RAISE';
RANGE                         : 'RANGE';
RANK                          : 'RANK';
RATIO_TO_REPORT               : 'RATIO_TO_REPORT';
RAW                           : 'RAW';
READ                          : 'READ';
READS                         : 'READS';
REAL                          : 'REAL';
REALM                         : 'REALM';
REBALANCE                     : 'REBALANCE';
REBUILD                       : 'REBUILD';
RECORD                        : 'RECORD';
RECORDS_PER_BLOCK             : 'RECORDS_PER_BLOCK';
RECOVER                       : 'RECOVER';
RECOVERABLE                   : 'RECOVERABLE';
RECOVERY                      : 'RECOVERY';
RECYC                         : 'RECYC';
RECYCLE                       : 'RECYCLE';
RECYCLEBIN                    : 'RECYCLEBIN';
REDACTION                     : 'REDACTION';
REDO                          : 'REDO';
REDUCED                       : 'REDUCED';
REDUNDANCY                    : 'REDUNDANCY';
REF                           : 'REF';
REFERENCE                     : 'REFERENCE';
REFERENCES                    : 'REFERENCES';
REFRESH                       : 'REFRESH';
REGEXP_LIKE                   : 'REGEXP_LIKE';
REGISTER                      : 'REGISTER';
REGR_AVGX                     : 'REGR_AVGX';
REGR_AVGY                     : 'REGR_AVGY';
REGR_COUNT                    : 'REGR_COUNT';
REGR_INTERCEPT                : 'REGR_INTERCEPT';
REGR_R2                       : 'REGR_R2';
REGR_SLOPE                    : 'REGR_SLOPE';
REGR_SXX                      : 'REGR_SXX';
REGR_SXY                      : 'REGR_SXY';
REGR_SYY                      : 'REGR_SYY';
REGULAR                       : 'REGULAR';
REJECT                        : 'REJECT';
REKEY                         : 'REKEY';
REL                           : 'REL';
RELATIONAL                    : 'RELATIONAL';
RELEASE                       : 'RELEASE';
RELIES_ON                     : 'RELIES_ON';
RELOCATE                      : 'RELOCATE';
RELY                          : 'RELY';
REMOVE                        : 'REMOVE';
RENAME                        : 'RENAME';
REP                           : 'REP';
REPAIR                        : 'REPAIR';
REPEAT                        : 'REPEAT';
REPF                          : 'REPF';
REPFOOTER                     : 'REPFOOTER';
REPH                          : 'REPH';
REPHEADER                     : 'REPHEADER';
REPLACE                       : 'REPLACE';
REPLICATION                   : 'REPLICATION';
REQUIRED                      : 'REQUIRED';
RESET                         : 'RESET';
RESETLOGS                     : 'RESETLOGS';
RESIZE                        : 'RESIZE';
RESOLVE                       : 'RESOLVE';
RESOLVER                      : 'RESOLVER';
RESOURCE                      : 'RESOURCE';
RESPECT                       : 'RESPECT';
RESTART                       : 'RESTART';
RESTORE                       : 'RESTORE';
RESTRICT                      : 'RESTRICT';
RESTRICT_REFERENCES           : 'RESTRICT_REFERENCES';
RESTRICTED                    : 'RESTRICTED';
RESULT                        : 'RESULT';
RESULT_CACHE                  : 'RESULT_CACHE';
RESUMABLE                     : 'RESUMABLE';
RESUME                        : 'RESUME';
RETENTION                     : 'RETENTION';
RETURN                        : 'RETURN';
RETURNING                     : 'RETURNING';
REUSE                         : 'REUSE';
REVERSE                       : 'REVERSE';
REVOKE                        : 'REVOKE';
REWRITE                       : 'REWRITE';
RIGHT                         : 'RIGHT';
RNDS                          : 'RNDS';
RNPS                          : 'RNPS';
ROLE                          : 'ROLE';
ROLES                         : 'ROLES';
ROLESET                       : 'ROLESET';
ROLLBACK                      : 'ROLLBACK';
ROLLING                       : 'ROLLING';
ROLLUP                        : 'ROLLUP';
ROW                           : 'ROW';
ROW_NUMBER                    : 'ROW_NUMBER';
ROWDEPENDENCIES               : 'ROWDEPENDENCIES';
ROWID                         : 'ROWID';
ROWPREF                       : 'ROWPREF';
ROWPREFETCH                   : 'ROWPREFETCH';
ROWS                          : 'ROWS';
ROWTYPE                       : 'ROWTYPE';
RTRIM                         : 'RTRIM';
RULE                          : 'RULE';
RULES                         : 'RULES';
RUNNING                       : 'RUNNING';
SALT                          : 'SALT';
SAMPLE                        : 'SAMPLE';
SAVE                          : 'SAVE';
SAVEPOINT                     : 'SAVEPOINT';
SB4                           : 'SB4';
SCALE                         : 'SCALE';
SCAN                          : 'SCAN';
SCHEDULER                     : 'SCHEDULER';
SCHEMA                        : 'SCHEMA';
SCN                           : 'SCN';
SCOPE                         : 'SCOPE';
SCRUB                         : 'SCRUB';
SEARCH                        : 'SEARCH';
SECOND                        : 'SECOND';
SECUREFILE                    : 'SECUREFILE';
SECURITY                      : 'SECURITY';
SEED                          : 'SEED';
SEGMENT                       : 'SEGMENT';
SELECT                        : 'SELECT';
SELECTIVITY                   : 'SELECTIVITY';
SELF                          : 'SELF';
SEQUENCE                      : 'SEQUENCE';
SEQUENTIAL                    : 'SEQUENTIAL';
SERIALIZABLE                  : 'SERIALIZABLE';
SERIALLY_REUSABLE             : 'SERIALLY_REUSABLE';
SERVERERROR                   : 'SERVERERROR';
SERVICE                       : 'SERVICE';
SESSION                       : 'SESSION';
SESSIONS_PER_USER             : 'SESSIONS_PER_USER';
SET                           : 'SET';
SETS                          : 'SETS';
SETTINGS                      : 'SETTINGS';
SGA                           : 'SGA';
SHARD                         : 'SHARD';
SHARDED                       : 'SHARDED';
SHARDS                        : 'SHARDS';
SHARE                         : 'SHARE';
SHARE_OF                      : 'SHARE_OF';
SHARED                        : 'SHARED';
SHARED_POOL                   : 'SHARED_POOL';
SHARING                       : 'SHARING';
SHO                           : 'SHO';
SHOW                          : 'SHOW';
SHRINK                        : 'SHRINK';
SHUTDOWN                      : 'SHUTDOWN';
SIBLINGS                      : 'SIBLINGS';
SID                           : 'SID';
SIGN                          : 'SIGN';
SIGNTYPE                      : 'SIGNTYPE';
SIMPLE                        : 'SIMPLE';
SIMPLE_INTEGER                : 'SIMPLE_INTEGER';
SIN                           : 'SIN';
SINGLE                        : 'SINGLE';
SINGLE_C                      : 'C';
SITE                          : 'SITE';
SIZE                          : 'SIZE';
SKIP_SYMBOL                   : 'SKIP';
SMALLFILE                     : 'SMALLFILE';
SMALLINT                      : 'SMALLINT';
SNAPSHOT                      : 'SNAPSHOT';
SOME                          : 'SOME';
SORT                          : 'SORT';
SOURCE                        : 'SOURCE';
SPACE                         : 'SPACE';
SPECIFICATION                 : 'SPECIFICATION';
SPFILE                        : 'SPFILE';
SPLIT                         : 'SPLIT';
SPO                           : 'SPO';
SPOO                          : 'SPOO';
SPOOL                         : 'SPOOL';
SPPARAMETER                   : 'SPPARAMETER';
SPPARAMETERS                  : 'SPPARAMETERS';
SQL                           : 'SQL';
SQL_MARCO                     : 'SQL_MARCO';
SQLCODE                       : 'SQLCODE';
STALE                         : 'STALE';
STANDALONE                    : 'STANDALONE';
STANDBY                       : 'STANDBY';
STAR                          : 'STAR';
START                         : 'START';
STARTS                        : 'STARTS';
STARTUP                       : 'STARTUP';
STATE                         : 'STATE';
STATEMENT                     : 'STATEMENT';
STATEMENT_ID                  : 'STATEMENT_ID';
STATEMENTC                    : 'STATEMENTC';
STATEMENTCACHE                : 'STATEMENTCACHE';
STATEMENTS                    : 'STATEMENTS';
STATIC                        : 'STATIC';
STATISTICS                    : 'STATISTICS';
STATS_BINOMIAL_TEST           : 'STATS_BINOMIAL_TEST';
STATS_CROSSTAB                : 'STATS_CROSSTAB';
STATS_F_TEST                  : 'STATS_F_TEST';
STATS_KS_TEST                 : 'STATS_KS_TEST';
STATS_MODE                    : 'STATS_MODE';
STATS_MW_TEST                 : 'STATS_MW_TEST';
STATS_ONE_WAY_ANOVA           : 'STATS_ONE_WAY_ANOVA';
STATS_T_TEST_INDEP            : 'STATS_T_TEST_INDEP';
STATS_T_TEST_INDEPU           : 'STATS_T_TEST_INDEPU';
STATS_T_TEST_ONE              : 'STATS_T_TEST_ONE';
STATS_T_TEST_PAIRED           : 'STATS_T_TEST_PAIRED';
STATS_WSR_TEST                : 'STATS_WSR_TEST';
STDDEV                        : 'STDDEV';
STDDEV_POP                    : 'STDDEV_POP';
STDDEV_SAMP                   : 'STDDEV_SAMP';
STOP                          : 'STOP';
STORAGE                       : 'STORAGE';
STORE                         : 'STORE';
STRICT                        : 'STRICT';
STRING                        : 'STRING';
STRIP                         : 'STRIP';
STRIPE_COLUMNS                : 'STRIPE_COLUMNS';
STRIPE_WIDTH                  : 'STRIPE_WIDTH';
STRUCT                        : 'STRUCT';
STRUCTURE                     : 'STRUCTURE';
SUBMULTISET                   : 'SUBMULTISET';
SUBPARTITION                  : 'SUBPARTITION';
SUBPARTITIONS                 : 'SUBPARTITIONS';
SUBSCRIBE                     : 'SUBSCRIBE';
SUBSET                        : 'SUBSET';
SUBSTITUTABLE                 : 'SUBSTITUTABLE';
SUBSTR                        : 'SUBSTR';
SUBSTRING                     : 'SUBSTRING';
SUBTYPE                       : 'SUBTYPE';
SUCCESS                       : 'SUCCESS';
SUCCESSFUL                    : 'SUCCESSFUL';
SUM                           : 'SUM';
SUMMARY                       : 'SUMMARY';
SUPPLEMENTAL                  : 'SUPPLEMENTAL';
SUPPRESSES_WARNING_6009       : 'SUPPRESSES_WARNING_6009';
SUSPEND                       : 'SUSPEND';
SWITCH                        : 'SWITCH';
SWITCHOVER                    : 'SWITCHOVER';
SYNC                          : 'SYNC';
SYNCHRONOUS                   : 'SYNCHRONOUS';
SYNONYM                       : 'SYNONYM';
SYS                           : 'SYS';
SYS_DBURIGEN                  : 'SYS_DBURIGEN';
SYS_XMLAGG                    : 'SYS_XMLAGG';
SYS_XMLGEN                    : 'SYS_XMLGEN';
SYSAUX                        : 'SYSAUX';
SYSBACKUP                     : 'SYSBACKUP';
SYSDBA                        : 'SYSDBA';
SYSDG                         : 'SYSDG';
SYSGUID                       : 'SYSGUID';
SYSKM                         : 'SYSKM';
SYSOPER                       : 'SYSOPER';
SYSTEM                        : 'SYSTEM';
SYSTIMESTAMP                  : 'SYSTIMESTAMP';
TABLE                         : 'TABLE';
TABLES                        : 'TABLES';
TABLESPACE                    : 'TABLESPACE';
TAN                           : 'TAN';
TARGET                        : 'TARGET';
TDO                           : 'TDO';
TEMPFILE                      : 'TEMPFILE';
TEMPLATE                      : 'TEMPLATE';
TEMPORARY                     : 'TEMPORARY';
TERMINATED                    : 'TERMINATED';
TEST                          : 'TEST';
THAN                          : 'THAN';
THE                           : 'THE';
THEN                          : 'THEN';
THREAD                        : 'THREAD';
THROUGH                       : 'THROUGH';
TIER                          : 'TIER';
TIES                          : 'TIES';
TIME                          : 'TIME';
TIME_ZONE                     : 'TIME_ZONE';
TIMEOUT                       : 'TIMEOUT';
TIMES                         : 'TIMES';
TIMESTAMP                     : 'TIMESTAMP';
TIMEZONE_ABBR                 : 'TIMEZONE_ABBR';
TIMEZONE_HOUR                 : 'TIMEZONE_HOUR';
TIMEZONE_MINUTE               : 'TIMEZONE_MINUTE';
TIMEZONE_REGION               : 'TIMEZONE_REGION';
TLE                           : 'TLE';
TO                            : 'TO';
TO_DATE                       : 'TO_DATE';
TOPLEVEL                      : 'TOPLEVEL';
TRACE                         : 'TRACE';
TRACING                       : 'TRACING';
TRACKING                      : 'TRACKING';
TRAILING                      : 'TRAILING';
TRANSACTION                   : 'TRANSACTION';
TRANSLATE                     : 'TRANSLATE';
TRANSLATION                   : 'TRANSLATION';
TREAT                         : 'TREAT';
TRIGGER                       : 'TRIGGER';
TRIGGERS                      : 'TRIGGERS';
TRIM                          : 'TRIM';
TRUE                          : 'TRUE';
TRUNC                         : 'TRUNC';
TRUNCATE                      : 'TRUNCATE';
TRUST                         : 'TRUST';
TRUSTED                       : 'TRUSTED';
TTI                           : 'TTI';
TUNING                        : 'TUNING';
TX                            : 'TX';
TYPE                          : 'TYPE';
TYPES                         : 'TYPES';
UB2                           : 'UB2';
UDF                           : 'UDF';
UID                           : 'UID';
UNARCHIVED                    : 'UNARCHIVED';
UNBOUND                       : 'UNBOUND';
UNBOUNDED                     : 'UNBOUNDED';
UNDER                         : 'UNDER';
UNDER_PATH                    : 'UNDER_PATH';
UNDO                          : 'UNDO';
UNDROP                        : 'UNDROP';
UNIFORM                       : 'UNIFORM';
UNINSTALL                     : 'UNINSTALL';
UNION                         : 'UNION';
UNIQUE                        : 'UNIQUE';
UNKNOWN                       : 'UNKNOWN';
UNLIMITED                     : 'UNLIMITED';
UNLOCK                        : 'UNLOCK';
UNPIVOT                       : 'UNPIVOT';
UNPLUG                        : 'UNPLUG';
UNPROTECTED                   : 'UNPROTECTED';
UNQUIESCE                     : 'UNQUIESCE';
UNRECOVERABLE                 : 'UNRECOVERABLE';
UNSUBSCRIBE                   : 'UNSUBSCRIBE';
UNTIL                         : 'UNTIL';
UNUSABLE                      : 'UNUSABLE';
UNUSED                        : 'UNUSED';
UPDATE                        : 'UPDATE';
UPDATED                       : 'UPDATED';
UPDATEXML                     : 'UPDATEXML';
UPDATING                      : 'UPDATING';
UPGRADE                       : 'UPGRADE';
UPSERT                        : 'UPSERT';
UROWID                        : 'UROWID';
USABLE                        : 'USABLE';
USAGE                         : 'USAGE';
USE                           : 'USE';
USE_STORED_OUTLINES           : 'USE_STORED_OUTLINES';
USER                          : 'USER';
USER_DATA                     : 'USER_DATA';
USERGROUP                     : 'USERGROUP';
USERS                         : 'USERS';
USING                         : 'USING';
USING_NLS_COMP                : 'USING_NLS_COMP';
UTF8                          : 'UTF8';
VALIDATE                      : 'VALIDATE';
VALIDATION                    : 'VALIDATION';
VALUE                         : 'VALUE';
VALUES                        : 'VALUES';
VAR_POP                       : 'VAR_POP';
VAR_SAMP                      : 'VAR_SAMP';
VARCHAR                       : 'VARCHAR';
VARCHAR2                      : 'VARCHAR2';
VARIANCE                      : 'VARIANCE';
VARRAY                        : 'VARRAY';
VARRAYS                       : 'VARRAYS';
VARYING                       : 'VARYING';
VERIFIER                      : 'VERIFIER';
VERIFY                        : 'VERIFY';
VERSION                       : 'VERSION';
VERSIONS                      : 'VERSIONS';
VIEW                          : 'VIEW';
VIOLATION                     : 'VIOLATION';
VIRTUAL                       : 'VIRTUAL';
VISIBILITY                    : 'VISIBILITY';
VISIBLE                       : 'VISIBLE';
VOLUME                        : 'VOLUME';
WAIT                          : 'WAIT';
WALLET                        : 'WALLET';
WELLFORMED                    : 'WELLFORMED';
WHEN                          : 'WHEN';
WHENEVER                      : 'WHENEVER';
WHERE                         : 'WHERE';
WHILE                         : 'WHILE';
WHITESPACE                    : 'WHITESPACE';
WITH                          : 'WITH';
WITHIN                        : 'WITHIN';
WITHOUT                       : 'WITHOUT';
WM_CONCAT                     : 'WM_CONCAT';
WNDS                          : 'WNDS';
WNPS                          : 'WNPS';
WORK                          : 'WORK';
WRAPPED                       : 'WRAPPED';
WRITE                         : 'WRITE';
XDB                           : 'XDB';
XML                           : 'XML';
XMLAGG                        : 'XMLAGG';
XMLATTRIBUTES                 : 'XMLATTRIBUTES';
XMLCAST                       : 'XMLCAST';
XMLCDATA                      : 'XMLCDATA';
XMLCOLATTVAL                  : 'XMLCOLATTVAL';
XMLCOMMENT                    : 'XMLCOMMENT';
XMLCONCAT                     : 'XMLCONCAT';
XMLDIFF                       : 'XMLDIFF';
XMLELEMENT                    : 'XMLELEMENT';
XMLEXISTS                     : 'XMLEXISTS';
XMLFOREST                     : 'XMLFOREST';
XMLINDEX                      : 'XMLINDEX';
XMLISVALID                    : 'XMLISVALID';
XMLNAMESPACES                 : 'XMLNAMESPACES';
XMLPARSE                      : 'XMLPARSE';
XMLPATCH                      : 'XMLPATCH';
XMLPI                         : 'XMLPI';
XMLQUERY                      : 'XMLQUERY';
XMLROOT                       : 'XMLROOT';
XMLSCHEMA                     : 'XMLSCHEMA';
XMLSEQUENCE                   : 'XMLSEQUENCE';
XMLSERIALIZE                  : 'XMLSERIALIZE';
XMLTABLE                      : 'XMLTABLE';
XMLTRANSFORM                  : 'XMLTRANSFORM';
XMLTYPE                       : 'XMLTYPE';
XQUERY                        : 'XQUERY';
XS                            : 'XS';
XTRANSPORT                    : 'XTRANSPORT';
YEAR                          : 'YEAR';
YEARS                         : 'YEARS';
YES                           : 'YES';
ZONE                          : 'ZONE';
ZONEMAP                       : 'ZONEMAP';
MULTIVALUE                    : 'MULTIVALUE';
CHARSET                       : 'CHARSET';
SHARDSPACE                    : 'SHARDSPACE';


SHA2_512_Q      : '"SHA2_512"';
V1_Q            : '"V1"';


AMPERSAND_           : '&';
AND_                 : '&&';
ARROW_               : '=>';
ASSIGNMENT_OPERATOR_ : ':=';
ASTERISK_            : '*';
AT_                  : '@';
BACKSLASH_           : '\\';
BQ_                  : '`';
CARET_               : '^';
COLON_               : ':';
COMMA_               : ',';
DEQ_                 : '==';
DOLLAR_              : '$';
DOT_                 : '.';
DOT_ASTERISK_        : '.*';
DQ_                  : '"';
EQ_                  : '=';
EXPONENT_            : '**';
GT_                  : '>';
GTE_                 : '>=';
LBE_                 : '{';
LBT_                 : '[';
LP_                  : '(';
LT_                  : '<';
LTE_                 : '<=';
MINUS_               : '-';
MOD_                 : '%';
NEQ_                 : '<>' | '!=' | '^=';
NOT_                 : '!';
OR_                  : '||';
PLUS_                : '+';
POUND_               : '#';
QUESTION_            : '?';
RANGE_OPERATOR_      : '..';
RBE_                 : '}';
RBT_                 : ']';
RP_                  : ')';
SAFE_EQ_             : '<=>';
SEMI_                : ';';
SIGNED_LEFT_SHIFT_   : '<<';
SIGNED_RIGHT_SHIFT_  : '>>';
SLASH_               : '/';
SQ_                  : '\'';
TILDE_               : '~';
VERTICAL_BAR_        : '|';
UL_                  : '_';
TYPE_CAST_           : '::';
JSON_EXTRACT_        : '->';
JSON_EXTRACT_TEXT_   : '->>';
JSONB_CONTAIN_RIGHT_ : '@>';

WS : [ \t\r\n\u3000] + ->skip;

BLOCK_HINT : '/*+' .*? '*/';
INLINE_HINT: '--+' ~[\r\n]* ('\r'? '\n' | EOF);

BLOCK_COMMENT:  '/*' .*? '*/'                           -> channel(HIDDEN);
INLINE_COMMENT: '--' ~[\r\n]* ('\r'? '\n' | EOF)        -> channel(HIDDEN);

ERROR_END_BLOCK  : '$error' .*? '$end'                  -> channel(HIDDEN);
IF_END_BLOCK  : '$if' (ERROR_END_BLOCK | .)*? '$end'    -> channel(HIDDEN);

STRING_: SINGLE_QUOTED_TEXT;
//SINGLE_QUOTED_TEXT: (SQ_ ('\\'. | '\'\'' | ~('\'' | '\\'))* SQ_);
SINGLE_QUOTED_TEXT: SQ_ (~('\'' | '\r' | '\n') | '\'' '\'' | '\r'? '\n')* SQ_;
DOUBLE_QUOTED_TEXT: (DQ_ ( '\\'. | '""' | ~('"'| '\\') )* DQ_);
NCHAR_TEXT: 'N' STRING_;
UCHAR_TEXT: 'U' STRING_;
//OPERATOR_: [~/>%<*+^\-@&=|`][~/>%<*+^\-@&=|`!]*[^%*=<>/!&|`];

INTEGER_: INT_;
NUMBER_: INTEGER_? DOT_? INTEGER_ ('E' (PLUS_ | MINUS_)? INTEGER_)?;
HEX_DIGIT_: '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_;
BIT_NUM_: '0b' ('0' | '1')+ | 'B' SQ_ ('0' | '1')+ SQ_;

A: 'A';
K: 'K';
M: 'M';
G: 'G';
T: 'T';
P: 'P';
E: 'E';
H: 'H';

I_CURSOR : '[CURSOR]' ;
IDENTIFIER_: [A-Z\u0080-\u2FFF\u3001-\uFF0B\uFF0D-\uFFFF]+[A-Z_$#0-9\u0080-\u2FFF\u3001-\uFF0B\uFF0D-\uFFFF]*;

fragment INT_: [0-9]+;
fragment HEX_: [0-9A-F];

