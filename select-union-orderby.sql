(select *  from b union select * from b) order by a;

select * from b union (select *  from b union select * from b) order by a;

(select *  from b union select * from b) union select * from b order by a;

((select *  from b union select * from b) union select * from b) order by a;

(select *  from b order by a union select * from b) order by a;

select * from b order by a union (select *  from b order by a union select * from b order by a) order by a;

(select *  from b union select * from b order by a) union select * from b order by a;

((select *  from b order by a union select * from b) union select * from b order by a) order by a;
