lexer grammar BaseLexer;

options {
    caseInsensitive = true;
}

// tokens





AMPERSAND_           : '&';
AND_                 : '&&';
ARROW_               : '=>';
ASSIGNMENT_OPERATOR_ : ':=';
ASTERISK_            : '*';
AT_                  : '@';
BACKSLASH_           : '\\';
BQ_                  : '`';
CARET_               : '^';
COLON_               : ':';
COMMA_               : ',';
DEQ_                 : '==';
DOLLAR_              : '$';
DOT_                 : '.';
DOT_ASTERISK_        : '.*';
DQ_                  : '"';
EQ_                  : '=';
EXPONENT_            : '**';
GT_                  : '>';
GTE_                 : '>=';
LBE_                 : '{';
LBT_                 : '[';
LP_                  : '(';
LT_                  : '<';
LTE_                 : '<=';
MINUS_               : '-';
MOD_                 : '%';
NEQ_                 : '<>' | '!=' | '^=';
NOT_                 : '!';
OR_                  : '||';
PLUS_                : '+';
POUND_               : '#';
QUESTION_            : '?';
RANGE_OPERATOR_      : '..';
RBE_                 : '}';
RBT_                 : ']';
RP_                  : ')';
SAFE_EQ_             : '<=>';
SEMI_                : ';';
SIGNED_LEFT_SHIFT_   : '<<';
SIGNED_RIGHT_SHIFT_  : '>>';
SLASH_               : '/';
SQ_                  : '\'';
TILDE_               : '~';
VERTICAL_BAR_        : '|';
UL_                  : '_';

SHIFT_RIGHT_UNSIGNED_: '>>>';

WS : [ \t\r\n\u3000] + ->skip;


BLOCK_COMMENT:  '/*' .*? '*/'                           -> channel(HIDDEN);
INLINE_COMMENT: '--' ~[\r\n]* ('\r'? '\n' | EOF)        -> channel(HIDDEN);


STRING_: SINGLE_QUOTED_TEXT;
SINGLE_QUOTED_TEXT: SQ_ (~('\'' | '\r' | '\n') | '\'' '\'' | '\r'? '\n')* SQ_;
DOUBLE_QUOTED_TEXT: (DQ_ ( '\\'. | '""' | ~('"'| '\\') )* DQ_);
NCHAR_TEXT: 'N' STRING_;
UCHAR_TEXT: 'U' STRING_;

INTEGER_: INT_;
NUMBER_: INTEGER_? DOT_? INTEGER_ ('E' (PLUS_ | MINUS_)? INTEGER_)?;
HEX_DIGIT_: '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_;
BIT_NUM_: '0b' ('0' | '1')+ | 'B' SQ_ ('0' | '1')+ SQ_;


IDENTIFIER_: [A-Z\u0080-\u2FFF\u3001-\uFF0B\uFF0D-\uFFFF]+[A-Z_$#0-9\u0080-\u2FFF\u3001-\uFF0B\uFF0D-\uFFFF]*;

fragment INT_: [0-9]+;
fragment HEX_: [0-9A-F];