parser grammar RedisParser;

options {
    tokenVocab = RedisLexer;
}
root
    : redis SEMI_? EOF
    ;

redis
    : genericCommands
    | stringCommands
    | listCommands
    | hashCommands
    | setCommands
    | zsetCommands
    | connectionCommands
    ;

// https://redis.io/docs/latest/commands/copy/
genericCommands
    : COPY source destination (DB destinationDB)? (REPLACE)?
    | DEL key+
    | DUMP key
    | EXISTS key+
    | EXPIRE key seconds (NX | XX | GT | LT)?
    | EXPIREAT key seconds (NX | XX | GT | LT)?
    | EXPIRETIME key
    | KEYS pattern
    | MIGRATE host port key destinationDB timeout COPY? REPLACE? (AUTH password | AUTH2 username password)? (KEYS key+)?
    | MOVE key destinationDB
    | OBJECT (ENCODING | FREQ | IDLETIME | REFCOUNT) key
    | PERSIST key
    | PEXPIRE key seconds (NX | XX | GT | LT)?
    | PEXPIREAT key seconds (NX | XX | GT | LT)?
    | PEXPIRETIME key
    | PTTL key
    | RANDOMKEY
    | RENAME key newkey=key
    | RENAMENX key newkey=key
    | RESTORE key ttl=numeric serializedValue=identifier REPLACE? ABSTTL? (IDLETIME seconds)? (FREQ frequency=numeric)?
    | SCAN cursor (MATCH pattern)? (COUNT count)? (TYPE type=identifier)?
    | SORT key (BY pattern)? (LIMIT offset count)? (GET pattern)* (ASC | DESC)? ALPHA? (STORE destination)?
    | SORT_RO key (BY pattern)? (LIMIT offset count)? (GET pattern)* (ASC | DESC)? ALPHA?
    | TOUCH key+
    | TTL key
    | TYPE key
    | UNLINK key+
    | WAIT numreplicas=numeric timeout
    | WAITAOF numlocal=numeric numreplicas=numeric timeout
    ;

// https://redis.io/docs/latest/commands/append/
stringCommands
    : APPEND key value
    | DECR key
    | DECRBY key decrement=numeric
    | GET key
    | GETDEL key
    | GETEX key ((EX | PX | EXAT | PXAT) seconds | PERSIST)?
    | GETRANGE key start end
    | GETSET key value
    | INCR key
    | INCRBY key increment
    | INCRBYFLOAT key increment
    | LCS key1=key key2=key LEN? IDX? (MINMATCHLEN numeric)? WITHMATCHLEN?
    | MGET key+
    | MSET (key value)+
    | MSETNX (key value)+
    | PSETEX key seconds value
    | SET key value (NX | XX)? GET? ((EX | PX | EXAT | PXAT) seconds | KEEPTTL)?
    | SETEX key seconds value
    | SETNX key value
    | SETRANGE key offset value
    | STRLEN key
    | SUBSTR key start end
    ;

//https://redis.io/docs/latest/commands/blmove/
listCommands
    : BLMOVE source destination (LEFT | RIGHT) (LEFT | RIGHT) timeout
    | BLMPOP timeout numkeys key+ (LEFT | RIGHT) (COUNT count)?
    | BLPOP key+ timeout
    | BRPOP key+ timeout
    | BRPOPLPUSH source destination timeout
    | LINDEX key index
    | LINSERT key (BEFORE | AFTER) pivot=identifier element
    | LLEN key
    | LMOVE source destination (LEFT | RIGHT) (LEFT | RIGHT)
    | LMPOP numkeys key+ (LEFT | RIGHT) (COUNT count)?
    | LPOP key count?
    | LPOS key element (RANK rank=numeric)? (COUNT count)? (MAXLEN len=numeric)?
    | LPUSH key element+
    | LPUSHX key element+
    | LRANGE key start end
    | LREM key count element
    | LSET key index element
    | LTRIM key start end
    | RPOP key count?
    | RPOPLPUSH source destination
    | RPUSH key element+
    | RPUSHX key element+
    ;
// https://redis.io/docs/latest/commands/hdel/
hashCommands
    : HDEL key field+
    | HEXISTS key field
    | HEXPIRE key seconds (NX | XX | GT | LT)? FIELDS numfields field+
    | HEXPIREAT key seconds (NX | XX | GT | LT)? FIELDS numfields field+
    | HEXPIRETIME key FIELDS numfields field+
    | HGET key field
    | HGETALL key
    | HINCRBY key field increment
    | HINCRBYFLOAT key field increment
    | HKEYS key
    | HLEN key
    | HMGET key field+
    | HMSET key (field value)+
    | HPERSIST key FIELDS numfields field+
    | HPEXPIRE key seconds (NX | XX | GT | LT)? FIELDS numfields field+
    | HPEXPIREAT key seconds (NX | XX | GT | LT)? FIELDS numfields field+
    | HPEXPIRETIME key FIELDS numfields field+
    | HPTTL key FIELDS numfields field+
    | HRANDFIELD key (count WITHVALUES?)?
    | HSCAN key cursor (MATCH pattern)? (COUNT count)? NOVALUES?
    | HSET key (field value)+
    | HSETNX key field value
    | HSTRLEN key field
    | HTTL key FIELDS numfields field+
    | HVALS key
    ;
// https://redis.io/docs/latest/commands/sadd/
setCommands
    : SADD key member+
    | SCARD key
    | SDIFF key+
    | SDIFFSTORE destination key+
    | SINTER key+
    | SINTERCARD numkeys key+ (LIMIT limit=numeric)?
    | SINTERSTORE destination key+
    | SISMEMBER key member
    | SMEMBERS key
    | SMISMEMBER key member+
    | SMOVE source destination member
    | SPOP key count?
    | SRANDMEMBER key count?
    | SREM key member+
    | SSCAN key cursor (MATCH pattern)? (COUNT count)?
    | SUNION key+
    | SUNIONSTORE destination key+
    ;

// https://redis.io/docs/latest/commands/bzmpop/
zsetCommands
    : BZMPOP timeout numkeys key+ (MIN | MAX) (COUNT count)?
    | BZPOPMAX key+ timeout
    | BZPOPMIN key+ timeout
    | ZADD key (NX | XX)? (GT | LT)? CH? INCR? (score=numeric member)+
    | ZCARD key
    | ZCOUNT key min max
    | ZDIFF numkeys key+ WITHSCORES?
    | ZDIFFSTORE destination numkeys key+
    | ZINCRBY key increment member
    | ZINTER numkeys key+ (WEIGHTS numeric+)? (AGGREGATE (SUM | MIN | MAX))? WITHSCORES?
    | ZINTERCARD numkeys key+ (LIMIT limit=numeric)?
    | ZINTERSTORE destination numkeys key+ (WEIGHTS numeric+)? (AGGREGATE (SUM | MIN | MAX))?
    | ZLEXCOUNT key min max
    | ZMPOP numkeys key+ (MIN | MAX) (COUNT count)?
    | ZMSCORE key member+
    | ZPOPMAX key count?
    | ZPOPMIN key count?
    | ZRANDMEMBER key (count WITHSCORES?)?
    | ZRANGE key start end (BYSCORE | BYLEX)? REV? (LIMIT offset count)? WITHSCORES?
    | ZRANGEBYLEX key min max (LIMIT offset count)?
    | ZRANGEBYSCORE key min max WITHSCORES? (LIMIT offset count)?
    | ZRANGESTORE dst=key src=key min max (BYSCORE | BYLEX)? REV? (LIMIT offset count)?
    | ZRANK key member WITHSCORE?
    | ZREM key member+
    | ZREMRANGEBYLEX key min max
    | ZREMRANGEBYRANK key start end
    | ZREMRANGEBYSCORE key min max
    | ZREVRANGE key start end WITHSCORES?
    | ZREVRANGEBYLEX key max min (LIMIT offset count)?
    | ZREVRANGEBYSCORE key max min WITHSCORES? (LIMIT offset count)?
    | ZREVRANK key member WITHSCORE?
    | ZSCAN key cursor (MATCH pattern)? (COUNT count)?
    | ZSCORE key member
    | ZUNION numkeys key+ (WEIGHTS numeric+)? (AGGREGATE (SUM | MIN | MAX))? WITHSCORES?
    | ZUNIONSTORE destination numkeys key+ (WEIGHTS numeric+)? (AGGREGATE (SUM | MIN | MAX))?
    ;

// https://redis.io/docs/latest/commands/auth/
connectionCommands
    : AUTH username? password
    | CLIENT CACHING (YES | NO)
    | CLIENT GETNAME
    | CLIENT GETREDIR
    | CLIENT ID
    | CLIENT INFO
    | CLIENT KILL ( ipPort | killFilter+ )
    | CLIENT LIST (TYPE (NORMAL | MASTER | REPLICA | PUBSUB))? (ID (clientid=identifier)+)?
    | CLIENT NO_EVICT (ON | OFF)
    | CLIENT NO_TOUCH (ON | OFF)
    | CLIENT PAUSE timeout (WRITE | ALL)?
    | CLIENT REPLY (ON | OFF | SKIP_)
    | CLIENT SETINFO (LIB_NAME libname=identifier | LIB_VER libver=identifier)
    | CLIENT SETNAME connectionName=identifier
    | CLIENT TRACKING (ON | OFF) (REDIRECT clientid=identifier)? (PREFIX prefix=identifier)* BCAST? OPTIN? OPTOUT? NOLOOP?
    | CLIENT TRACKINGINFO
    | CLIENT UNBLOCK clientid=identifier (TIMEOUT | ERROR)?
    | CLIENT UNPAUSE
    | ECHO message=identifier
    | HELLO (protover=identifier (AUTH username password)? (SETNAME clientname=identifier)?)?
    | PING message=identifier
    | QUIT
    | RESET
    | SELECT index
    ;
killFilter
    : ID clientid=identifier
    | TYPE (NORMAL | MASTER | SLAVE | REPLICA | PUBSUB)
    | USER username
    | ADDR ipPort
    | LADDR ipPort
    | SKIPME (YES | NO)
    | MAXAGE maxage=identifier
    ;
seconds
    : numeric
    ;
// 0-15
destinationDB
    : numeric
    ;
ipPort
    : identifier
    ;
host
    : identifier
    ;
port
    : numeric
    ;
username
    : identifier
    ;
password
    : identifier
    ;
offset
    : numeric
    ;
increment
    : numeric
    ;
source
    : identifier
    ;
destination
    : identifier
    ;
timeout
    : numeric
    ;
index
    : numeric
    ;
element
    : identifier
    ;
count
    : numeric
    ;
numfields
    : numeric
    ;
field
    : identifier
    ;
cursor
    : numeric
    ;
pattern
    : identifier
    ;
member
    : identifier
    ;
start
    : range
    ;
end
    : range
    ;
min
    : range
    ;
max
    : range
    ;
numkeys
    : numeric
    ;
// -inf +inf (1 3  - + [b [f
range
    : INF_P | INF_M | PLUS_ | MINUS_ | (LBT_ | LP_)? identifier
    ;
key
    : identifier
    ;
value
    : identifier
    ;

identifier
    : string | numeric | IDENTIFIER_ | .
    ;

string
    : STRING_
    ;

numeric
    : INTEGER_ | NUMBER_
    ;
