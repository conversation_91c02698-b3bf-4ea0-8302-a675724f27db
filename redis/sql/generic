COPY dolly clone
DEL key1 key2 key3
DUMP mykey
EXISTS key1 key2 nosuchkey
EXPIRE mykey 10 XX
EXPIREAT mykey 1293840000
EXPIRETIME mykey
KEYS *name*
KEYS a??
KEYS *
MIGRATE 192.168.1.34 6379 "" 0 5000 KEYS key1 key2 key3
MOVE key 0
OBJECT ENCODING key
OBJECT FREQ key
OBJECT IDLETIME key
OBJECT REFCOUNT key
PERSIST mykey
PEXPIRE mykey 1500
PEXPIRE mykey 1000 XX
PEXPIREAT mykey 1555555555005
PEXPIRETIME mykey
PTTL mykey
RANDOMKEY
RENAME mykey myotherkey
RENAMENX mykey myotherkey
RESTORE mykey 0 "\n\x17\x17\x00\x00\x00\x12\x00\x00\x00\x03\x00\x00\xc0\x01\x00\x04\xc0\x02\x00\x04\xc0\x03\x00\xff\x04\x00u#<\xc0;.\xe9\xdd"
scan 0
SORT mylist
SORT mylist DESC
SORT mylist ALPHA
SORT mylist LIMIT 0 10
SORT mylist LIMIT 0 5 DESC ALPHA
SORT mylist BY weight_*
SORT mylist BY nosort
SORT mylist BY weight_* GET object_*
SORT mylist BY weight_* GET object_* GET #
SORT mylist BY weight_* STORE resultkey
SORT mylist BY weight_*->fieldname GET object_*->fieldname
SORT_RO mylist BY weight_*->fieldname GET object_*->fieldname
TOUCH key1 key2
TTL mykey
TYPE key3
UNLINK key1 key2 key3
WAIT 2 1000
WAITAOF 0 1 1000
























