BZPOPMAX zset1 zset2 0
BZPOPMIN zset1 zset2 0
ZADD myzset 2 "two" 3 "three"
ZCARD myzset
ZCOUNT myzset -inf +inf
ZCOUNT myzset (1 3
ZDIFF 2 zset1 zset2
ZDIFF 2 zset1 zset2 WITHSCORES
ZDIFFSTORE out 2 zset1 zset2
ZINCRBY myzset 2 "one"
ZINTER 2 zset1 zset2
ZINTERCARD 2 zset1 zset2
ZINTERCARD 2 zset1 zset2 LIMIT 1
ZINTERSTORE out 2 zset1 zset2 WEIGHTS 2 3
ZLEXCOUNT myzset - +
ZLEXCOUNT myzset [b [f
ZMPOP 1 myzset MIN
ZMSCORE myzset "one" "two" "nofield"
ZPOPMAX myzset
ZPOPMIN myzset
ZRANDMEMBER dadi -5 WITHSCORES
ZRANGE zset (1 5 BYSCORE
ZRANGE zset (5 (10 BYSCORE
ZRANGE zset 10 5 BYSCORE REV
ZRANGE zset 5 10 REV
ZRANGE myzset -2 -1
ZADD myzset 1 "one" 2 "two" 3 "three"
ZRANGE myzset (1 +inf BYSCORE LIMIT 1 1
ZRANGEBYLEX myzset - [c
ZRANGEBYLEX myzset - (c
ZRANGEBYLEX myzset [aaa (g
ZRANGEBYSCORE zset (1 5
ZRANGEBYSCORE myzset -inf +inf
ZRANGEBYSCORE myzset (1 (2
ZRANGESTORE dstzset srczset 2 -1
ZRANK myzset "three" WITHSCORE
ZREM myzset "two"
ZREMRANGEBYLEX myzset [alpha [omega
ZREMRANGEBYRANK myzset 0 1
ZREMRANGEBYSCORE myzset -inf (2
ZREVRANGE myzset 0 -1
ZREVRANGEBYLEX myzset [c -
ZREVRANGEBYLEX myzset (g [aaa
ZREVRANGEBYSCORE myzset +inf -inf
ZREVRANGEBYSCORE myzset 2 1
ZREVRANK myzset "three" WITHSCORE
ZSCORE myzset "one"
ZUNION 2 zset1 zset2
ZUNION 2 zset1 zset2 WITHSCORES
ZUNIONSTORE out 2 zset1 zset2 WEIGHTS 2 3




