APPEND mykey "Hello"
DECR mykey
DECRBY mykey 3
GET nonexisting
<PERSON>T<PERSON>L mykey
GETEX mykey
GETEX mykey EX 60
GETRANGE mykey -3 -1
GETRANGE mykey 10 100
GETSET mycounter "0"
INCR mykey
INCRBY mykey 5
INCRBYFLOAT mykey -5
INCRBYFLOAT mykey 0.1
INCRBYFLOAT mykey 2.0e2
LCS key1 key2
LCS key1 key2 LEN
LCS key1 key2 IDX
LCS key1 key2 IDX MINMATCHLEN 4
LCS key1 key2 IDX MINMATCHLEN 4 WITHMATCHLEN
MGET key1 key2 nonexisting
MSET key1 "Hello" key2 "World"
MSETNX key1 "Hello" key2 "there"
PSETEX mykey 1000 "Hello"
SET mykey "Hello"
SET anotherkey "will expire in a minute" EX 60
SETEX mykey 10 "Hello"
SETNX mykey "World"
SETRANGE key1 6 "Redis"
STRLEN nonexisting
SUBSTR  mykey -3 -1
BLPOP list1 list2 list3 0
BLPOP foo 0
BRPOP list1 list2 0
LINDEX mylist -1
LINSERT mylist BEFORE "World" "There"
LLEN mylist
LMOVE mylist myotherlist RIGHT LEFT
LMPOP 2 non1 non2 LEFT COUNT 10
LMPOP 1 mylist LEFT
LMPOP 1 mylist RIGHT COUNT 10
LMPOP 2 mylist mylist2 right count 3
LPOP mylist 2
LPOP mylist
LPOS mylist c
LPOS mylist c RANK 2
LPOS mylist c RANK -1
LPOS mylist c RANK -1 COUNT 2
LPUSH mylist "hello"
LPUSHX mylist "Hello"
LRANGE mylist 0 0
LREM mylist -2 "hello"
LSET mylist 0 "four"
LTRIM mylist 0 99
RPOP mylist
RPOP mylist 2
RPOPLPUSH mylist myotherlist
RPUSH mylist "world"
RPUSHX myotherlist "World"


























