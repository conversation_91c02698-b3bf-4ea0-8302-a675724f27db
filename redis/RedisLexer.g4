lexer grammar RedisLexer;

options {
    caseInsensitive = true;
}

ABSTTL           : 'ABSTTL';
ADDR             : 'ADDR';
AFTER            : 'AFTER';
AGGREGATE        : 'AGGREGATE';
ALL              : 'ALL';
ALPHA            : 'ALPHA';
APPEND           : 'APPEND';
ASC              : 'ASC';
AUTH             : 'AUTH';
AUTH2            : 'AUTH2';
BCAST            : 'BCAST';
BEFORE           : 'BEFORE';
BLMOVE           : 'BLMOVE';
BLMPOP           : 'BLMPOP';
BLPOP            : 'BLPOP';
BRPOP            : 'BRPOP';
BRPOPLPUSH       : 'BRPOPLPUSH';
BY               : 'BY';
BYLEX            : 'BYLEX';
BYSCORE          : 'BYSCORE';
BZMPOP           : 'BZMPOP';
BZPOPMAX         : 'BZPOPMAX';
BZPOPMIN         : 'BZPOPMIN';
CACHING          : 'CACHING';
CH               : 'CH';
CLIENT           : 'CLIENT';
COPY             : 'COPY';
COUNT            : 'COUNT';
DB               : 'DB';
DECR             : 'DECR';
DECRBY           : 'DECRBY';
DEL              : 'DEL';
DESC             : 'DESC';
DUMP             : 'DUMP';
ECHO             : 'ECHO';
ENCODING         : 'ENCODING';
ERROR            : 'ERROR';
EX               : 'EX';
EXAT             : 'EXAT';
EXISTS           : 'EXISTS';
EXPIRE           : 'EXPIRE';
EXPIREAT         : 'EXPIREAT';
EXPIRETIME       : 'EXPIRETIME';
FIELDS           : 'FIELDS';
FREQ             : 'FREQ';
GET              : 'GET';
GETDEL           : 'GETDEL';
GETEX            : 'GETEX';
GETNAME          : 'GETNAME';
GETRANGE         : 'GETRANGE';
GETREDIR         : 'GETREDIR';
GETSET           : 'GETSET';
GT               : 'GT';
HDEL             : 'HDEL';
HELLO            : 'HELLO';
HEXISTS          : 'HEXISTS';
HEXPIRE          : 'HEXPIRE';
HEXPIREAT        : 'HEXPIREAT';
HEXPIRETIME      : 'HEXPIRETIME';
HGET             : 'HGET';
HGETALL          : 'HGETALL';
HINCRBY          : 'HINCRBY';
HINCRBYFLOAT     : 'HINCRBYFLOAT';
HKEYS            : 'HKEYS';
HLEN             : 'HLEN';
HMGET            : 'HMGET';
HMSET            : 'HMSET';
HPERSIST         : 'HPERSIST';
HPEXPIRE         : 'HPEXPIRE';
HPEXPIREAT       : 'HPEXPIREAT';
HPEXPIRETIME     : 'HPEXPIRETIME';
HPTTL            : 'HPTTL';
HRANDFIELD       : 'HRANDFIELD';
HSCAN            : 'HSCAN';
HSET             : 'HSET';
HSETNX           : 'HSETNX';
HSTRLEN          : 'HSTRLEN';
HTTL             : 'HTTL';
HVALS            : 'HVALS';
ID               : 'ID';
IDLETIME         : 'IDLETIME';
IDX              : 'IDX';
INCR             : 'INCR';
INCRBY           : 'INCRBY';
INCRBYFLOAT      : 'INCRBYFLOAT';
INFO             : 'INFO';
KEEPTTL          : 'KEEPTTL';
KEYS             : 'KEYS';
KILL             : 'KILL';
LADDR            : 'LADDR';
LCS              : 'LCS';
LEFT             : 'LEFT';
LEN              : 'LEN';
LIB_NAME         : 'LIB-NAME';
LIB_VER          : 'LIB-VER';
LIMIT            : 'LIMIT';
LINDEX           : 'LINDEX';
LINSERT          : 'LINSERT';
LIST             : 'LIST';
LLEN             : 'LLEN';
LMOVE            : 'LMOVE';
LMPOP            : 'LMPOP';
LPOP             : 'LPOP';
LPOS             : 'LPOS';
LPUSH            : 'LPUSH';
LPUSHX           : 'LPUSHX';
LRANGE           : 'LRANGE';
LREM             : 'LREM';
LSET             : 'LSET';
LT               : 'LT';
LTRIM            : 'LTRIM';
MASTER           : 'MASTER';
MATCH            : 'MATCH';
MAX              : 'MAX';
MAXAGE           : 'MAXAGE';
MAXLEN           : 'MAXLEN';
MGET             : 'MGET';
MIGRATE          : 'MIGRATE';
MIN              : 'MIN';
MINMATCHLEN      : 'MINMATCHLEN';
MOVE             : 'MOVE';
MSET             : 'MSET';
MSETNX           : 'MSETNX';
NO               : 'NO';
NO_EVICT         : 'NO-EVICT';
NO_TOUCH         : 'NO-TOUCH';
NOLOOP           : 'NOLOOP';
NORMAL           : 'NORMAL';
NOVALUES         : 'NOVALUES';
NX               : 'NX';
OBJECT           : 'OBJECT';
OFF              : 'OFF';
ON               : 'ON';
OPTIN            : 'OPTIN';
OPTOUT           : 'OPTOUT';
PAUSE            : 'PAUSE';
PERSIST          : 'PERSIST';
PEXPIRE          : 'PEXPIRE';
PEXPIREAT        : 'PEXPIREAT';
PEXPIRETIME      : 'PEXPIRETIME';
PING             : 'PING';
PREFIX           : 'PREFIX';
PSETEX           : 'PSETEX';
PTTL             : 'PTTL';
PUBSUB           : 'PUBSUB';
PX               : 'PX';
PXAT             : 'PXAT';
QUIT             : 'QUIT';
RANDOMKEY        : 'RANDOMKEY';
RANK             : 'RANK';
REDIRECT         : 'REDIRECT';
REFCOUNT         : 'REFCOUNT';
RENAME           : 'RENAME';
RENAMENX         : 'RENAMENX';
REPLACE          : 'REPLACE';
REPLICA          : 'REPLICA';
REPLY            : 'REPLY';
RESET            : 'RESET';
RESTORE          : 'RESTORE';
REV              : 'REV';
RIGHT            : 'RIGHT';
RPOP             : 'RPOP';
RPOPLPUSH        : 'RPOPLPUSH';
RPUSH            : 'RPUSH';
RPUSHX           : 'RPUSHX';
SADD             : 'SADD';
SCAN             : 'SCAN';
SCARD            : 'SCARD';
SDIFF            : 'SDIFF';
SDIFFSTORE       : 'SDIFFSTORE';
SELECT           : 'SELECT';
SET              : 'SET';
SETEX            : 'SETEX';
SETINFO          : 'SETINFO';
SETNAME          : 'SETNAME';
SETNX            : 'SETNX';
SETRANGE         : 'SETRANGE';
SINTER           : 'SINTER';
SINTERCARD       : 'SINTERCARD';
SINTERSTORE      : 'SINTERSTORE';
SISMEMBER        : 'SISMEMBER';
SKIP_            : 'SKIP';
SKIPME           : 'SKIPME';
SLAVE            : 'SLAVE';
SMEMBERS         : 'SMEMBERS';
SMISMEMBER       : 'SMISMEMBER';
SMOVE            : 'SMOVE';
SORT             : 'SORT';
SORT_RO          : 'SORT_RO';
SPOP             : 'SPOP';
SRANDMEMBER      : 'SRANDMEMBER';
SREM             : 'SREM';
SSCAN            : 'SSCAN';
STORE            : 'STORE';
STRLEN           : 'STRLEN';
SUBSTR           : 'SUBSTR';
SUM              : 'SUM';
SUNION           : 'SUNION';
SUNIONSTORE      : 'SUNIONSTORE';
TIMEOUT          : 'TIMEOUT';
TOUCH            : 'TOUCH';
TRACKING         : 'TRACKING';
TRACKINGINFO     : 'TRACKINGINFO';
TTL              : 'TTL';
TYPE             : 'TYPE';
UNBLOCK          : 'UNBLOCK';
UNLINK           : 'UNLINK';
UNPAUSE          : 'UNPAUSE';
USER             : 'USER';
WAIT             : 'WAIT';
WAITAOF          : 'WAITAOF';
WEIGHTS          : 'WEIGHTS';
WITHMATCHLEN     : 'WITHMATCHLEN';
WITHSCORE        : 'WITHSCORE';
WITHSCORES       : 'WITHSCORES';
WITHVALUES       : 'WITHVALUES';
WRITE            : 'WRITE';
XX               : 'XX';
YES              : 'YES';
ZADD             : 'ZADD';
ZCARD            : 'ZCARD';
ZCOUNT           : 'ZCOUNT';
ZDIFF            : 'ZDIFF';
ZDIFFSTORE       : 'ZDIFFSTORE';
ZINCRBY          : 'ZINCRBY';
ZINTER           : 'ZINTER';
ZINTERCARD       : 'ZINTERCARD';
ZINTERSTORE      : 'ZINTERSTORE';
ZLEXCOUNT        : 'ZLEXCOUNT';
ZMPOP            : 'ZMPOP';
ZMSCORE          : 'ZMSCORE';
ZPOPMAX          : 'ZPOPMAX';
ZPOPMIN          : 'ZPOPMIN';
ZRANDMEMBER      : 'ZRANDMEMBER';
ZRANGE           : 'ZRANGE';
ZRANGEBYLEX      : 'ZRANGEBYLEX';
ZRANGEBYSCORE    : 'ZRANGEBYSCORE';
ZRANGESTORE      : 'ZRANGESTORE';
ZRANK            : 'ZRANK';
ZREM             : 'ZREM';
ZREMRANGEBYLEX   : 'ZREMRANGEBYLEX';
ZREMRANGEBYRANK  : 'ZREMRANGEBYRANK';
ZREMRANGEBYSCORE : 'ZREMRANGEBYSCORE';
ZREVRANGE        : 'ZREVRANGE';
ZREVRANGEBYLEX   : 'ZREVRANGEBYLEX';
ZREVRANGEBYSCORE : 'ZREVRANGEBYSCORE';
ZREVRANK         : 'ZREVRANK';
ZSCAN            : 'ZSCAN';
ZSCORE           : 'ZSCORE';
ZUNION           : 'ZUNION';
ZUNIONSTORE      : 'ZUNIONSTORE';




INF_P: '+inf';
INF_M: '-inf';

COLON_               : ':';
SEMI_                : ';';
LBT_                 : '[';
LP_                  : '(';
PLUS_                : '+';
MINUS_               : '-';


STRING_: SINGLE_QUOTED_TEXT | DOUBLE_QUOTED_TEXT;
SINGLE_QUOTED_TEXT: ('\'' ('\\'. | '\'\'' | ~('\'' | '\\'))* '\'');
DOUBLE_QUOTED_TEXT: ('"' ( '\\'. | '""' | ~('"'| '\\') )* '"');

INTEGER_: INT_;
NUMBER_: '-'? INTEGER_? '.'? INTEGER_ (('p' | 'e') ('-' | '+')? INTEGER_)?;

WS : [ \t\r\n\u3000] + ->skip;

IDENTIFIER_: ~(' ' | '\n' | '\r')+;

fragment INT_: [0-9]+;
fragment HEX_: [0-9A-F];
