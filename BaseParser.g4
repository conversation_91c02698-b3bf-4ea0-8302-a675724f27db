parser grammar BaseParser;

options {
    tokenVocab = BaseLexer;
}

root
    : (select
    | insert
    | update
    | delete
    | createView
    | createTable
    | alterTable
    | dropTable
    | truncateTable
    ) SEMI_? SLASH_? EOF
    ;

select
    : SELECT selectList  selectFromClause whereClause? groupByClause?
    ;

literals
    : stringLiterals
    | numberLiterals
    | dateTimeLiterals
    | hexadecimalLiterals
    | bitValueLiterals
    | booleanLiterals
    | nullValueLiterals
    ;


stringLiterals
    : STRING_
    | NCHAR_TEXT
    | UCHAR_TEXT
    ;

numberLiterals
   : (PLUS_ | MINUS_)? (INTEGER_ | NUMBER_)
   ;

dateTimeLiterals
    : (DATE | TIME | TIMESTAMP) stringLiterals
    | LBE_ identifier stringLiterals RBE_
    ;

hexadecimalLiterals
    : HEX_DIGIT_
    ;

bitValueLiterals
    : BIT_NUM_
    ;

booleanLiterals
    : TRUE | FALSE
    ;

nullValueLiterals
    : NULL
    ;

identifier
    : IDENTIFIER_ | unreservedWord | QUESTION_
    ;

// 非保留关键字
unreservedWord
    :
    ;

schemaName
    : identifier
    ;

tableName
    : (owner DOT_)? name
    ;

viewName
    : (owner DOT_)? name
    ;

columnName
    : (owner DOT_)* name
    ;

objectName
    : (owner DOT_)? name
    ;

owner
    : identifier
    ;

name
    : identifier
    ;

alias
    : identifier | STRING_
    ;

expr
    : expr andOperator expr
    | expr orOperator expr
    | notOperator expr
    | LP_ expr RP_
    | booleanPrimary
    ;

andOperator
    : AND | AND_
    ;

orOperator
    : OR | OR_
    ;

notOperator
    : NOT | NOT_
    ;

booleanPrimary
    : booleanPrimary IS NOT? (TRUE | FALSE | UNKNOWN | NULL)
    | (PRIOR | DISTINCT) predicate
    | CONNECT_BY_ROOT predicate
    | booleanPrimary SAFE_EQ_ predicate
    | booleanPrimary comparisonOperator (ALL | ANY) subquery
    | booleanPrimary comparisonOperator predicate
    | predicate
    ;

comparisonOperator
    : EQ_ | GTE_ | GT_ | LTE_ | LT_ | NEQ_
    ;

predicate
    : bitExpr NOT? IN subquery
    | PRIOR predicate
    | bitExpr NOT? IN LP_ expr (COMMA_ expr)* RP_
    | bitExpr NOT? IN LP_ expr (COMMA_ expr)* RP_ AND predicate
    | bitExpr NOT? BETWEEN bitExpr AND predicate
    | bitExpr NOT? LIKE simpleExpr (ESCAPE simpleExpr)?
    | bitExpr
    ;

bitExpr
    : bitExpr VERTICAL_BAR_ bitExpr
    | bitExpr AMPERSAND_ bitExpr
    | bitExpr SIGNED_LEFT_SHIFT_ bitExpr
    | bitExpr SIGNED_RIGHT_SHIFT_ bitExpr
    | bitExpr PLUS_ bitExpr
    | simpleExpr
    | bitExpr MINUS_ bitExpr
    | bitExpr ASTERISK_ bitExpr
    | bitExpr SLASH_ bitExpr
    | bitExpr MOD_ bitExpr
    | bitExpr MOD bitExpr
    | bitExpr CARET_ bitExpr
    | bitExpr DOT_ bitExpr
    | bitExpr ARROW_ bitExpr
    ;

simpleExpr
    : functionCall #functionExpr
    | literals #literalsExpr
    | simpleExpr OR_ simpleExpr #orExpr
    | (PLUS_ | MINUS_ | TILDE_ | NOT_ | BINARY) simpleExpr #unaryExpr
    | ROW? LP_ expr (COMMA_ expr)* RP_ #rowExpr
    | EXISTS? subquery #subqueryExpr
    | LBE_ identifier expr RBE_ #objectAccessExpr
    | caseExpression #caseExpr
    | columnName #columnExpr
    // | ...
    ;

functionCall
    : // aggregationFunction | analyticFunction | specialFunction | regularFunction | xmlFunction
    ;
caseExpression
    : CASE simpleExpr? caseWhen+ caseElse? END
    ;

caseWhen
    : WHEN expr THEN expr
    ;

caseElse
    : ELSE expr
    ;

subquery
    : LP_ select RP_
    ;