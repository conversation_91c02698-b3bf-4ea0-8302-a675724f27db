lexer grammar ImpalaLexer;

options { caseInsensitive = true; }

ABORT: 'ABORT';
ABORT_ON_ERROR: 'ABORT_ON_ERROR';
ACTIVATE: 'ACTIVATE';
ACTIVE:'ACTIVE';
ADD: 'ADD';
ADMIN:'ADMIN';
AFTER:'AFTER';
AGGREGATE: 'AGGREGATE';
ALLOC_FRACTION:'ALLOC_FRACTION';
ALL: 'ALL';
ALLOW_ERASURE_CODED_FILES: 'ALLOW_ERASURE_CODED_FILES';
ALLOW_UNSUPPORTED_FORMATS: 'ALLOW_UNSUPPORTED_FORMATS';
ALTER: 'ALTER';
ANALYTIC: 'ANALYTIC';
ANALYZE: 'ANALYZE';
AND: 'AND';
ANTI: 'ANTI';
ANY:'ANY';
APPX_COUNT_DISTINCT: 'APPX_COUNT_DISTINCT';
ARRAY: 'ARRAY';
ARCHIVE:'ARCHIVE';
AS: 'AS';
ASC: 'ASC';
AST:'AST';
AT:'AT';
AUTHORIZATION: 'AUTHORIZATION';
AUTO_ENCODING: 'AUTO_ENCODING';
AVRO: 'AVRO';
BATCH_SIZE: 'BATCH_SIZE';
BETWEEN: 'BETWEEN';
BIGINT: 'BIGINT';
BIT_SHIFFLE: 'BIT_SHIFFLE';
BINARY: 'BINARY';
BLOCK_SIZE: 'BLOCK_SIZE';
BOOLEAN: 'BOOLEAN';
BROADCAST_BYTES_LIMIT: 'BROADCAST_BYTES_LIMIT';
BUFFER_POOL_LIMIT: 'BUFFER_POOL_LIMIT';
BY: 'BY';
CACHED: 'CACHED';
CASCADE: 'CASCADE';
CHANGE: 'CHANGE';
CHAR: 'CHAR';
CLOSEFN: 'CLOSEFN';
COLLATE: 'COLLATE';
COLUMN: 'COLUMN';
COLUMNS: 'COLUMNS';
COMMENT: 'COMMENT';
COMPRESSION: 'COMPRESSION';
COMPRESSION_CODEC: 'COMPRESSION_CODEC';
COMPUTE: 'COMPUTE';
COMPUTE_STATS_MIN_SAMPLE_SIZE: 'COMPUTE_STATS_MIN_SAMPLE_SIZE';
COPY: 'COPY';
TESTCASE: 'TESTCASE';
CREATE: 'CREATE';
CROSS: 'CROSS';
CURRENT: 'CURRENT';
CURRENT_USER: 'CURRENT_USER';
CUME_DIST: 'CUME_DIST';
DATA: 'DATA';
DATABASE: 'DATABASE';
DATABASES: 'DATABASES';
DATE: 'DATE';
DEBUG_ACTION: 'DEBUG_ACTION';
DECIMAL: 'DECIMAL';
DECIMAL_V2: 'DECIMAL_V2';
DEFAULT: 'DEFAULT';
DEFAULT_FILE_FORMAT: 'DEFAULT_FILE_FORMAT';
DEFAULT_HINTS_INSERT_STATEMENT: 'DEFAULT_HINTS_INSERT_STATEMENT';
DEFAULT_JOIN_DISTRIBUTION_MODE: 'DEFAULT_JOIN_DISTRIBUTION_MODE';
DEFAULT_SPILLABLE_BUFFER_SIZE: 'DEFAULT_SPILLABLE_BUFFER_SIZE';
DEFAULT_TRANSACTIONAL_TYPE: 'DEFAULT_TRANSACTIONAL_TYPE';
DELETE: 'DELETE';
DELETE_STATS_IN_TRUNCATE: 'DELETE_STATS_IN_TRUNCATE';
DELIMITED: 'DELIMITED';
DESC: 'DESC';
DESCRIBE: 'DESCRIBE';
DENSE_RANK:'DENSE_RANK';
DICT_ENCODING: 'DICT_ENCODING';
DISABLE_CODEGEN: 'DISABLE_CODEGEN';
DISABLE_CODEGEN_ROWS_THRESHOLD: 'DISABLE_CODEGEN_ROWS_THRESHOLD';
DISABLE_HBASE_NUM_ROWS_ESTIMATE: 'DISABLE_HBASE_NUM_ROWS_ESTIMATE';
DISABLE_ROW_RUNTIME_FILTERING: 'DISABLE_ROW_RUNTIME_FILTERING';
DISABLE_STREAMING_PREAGGREGATIONS: 'DISABLE_STREAMING_PREAGGREGATIONS';
DISABLE_UNSAFE_SPILLS: 'DISABLE_UNSAFE_SPILLS';
DISTINCT: 'DISTINCT';
DISTINCTROW:'DISTINCTROW';
DOUBLE: 'DOUBLE';
DROP: 'DROP';
DUMPFILE:'DUMPFILE';
ENABLE_EXPR_REWRITES: 'ENABLE_EXPR_REWRITES';
ENCODING: 'ENCODING';
ESCAPE: 'ESCAPE';
ESCAPED: 'ESCAPED';
ESTIMATE: 'ESTIMATE';
EXEC_SINGLE_NODE_ROWS_THRESHOLD: 'EXEC_SINGLE_NODE_ROWS_THRESHOLD';
EXEC_TIME_LIMIT_S: 'EXEC_TIME_LIMIT_S';
EXISTS: 'EXISTS';
EXPAND_COMPLEX_TYPES: 'EXPAND_COMPLEX_TYPES';
EXPLAIN: 'EXPLAIN';
EXPLAIN_LEVEL: 'EXPLAIN_LEVEL';
EXTENDED: 'EXTENDED';
EXTERNAL: 'EXTERNAL';
FALSE: 'FALSE';
FETCH_ROWS_TIMEOUT_MS: 'FETCH_ROWS_TIMEOUT_MS';
FIELDS: 'FIELDS';
FILEFORMAT: 'FILEFORMAT';
FILES: 'FILES';
FINALIZE_FN: 'FINALIZE_FN';
FIRST: 'FIRST';
FLOAT: 'FLOAT';
FOLLOWING: 'FOLLOWING';
FOR:'FOR';
FORMAT: 'FORMAT';
FORMATTED: 'FORMATTED';
FROM: 'FROM';
FULL: 'FULL';
FUNCTION: 'FUNCTION';
FUNCTIONS: 'FUNCTIONS';
GRANT: 'GRANT';
GROUP: 'GROUP';
GROUP_CONCAT: 'GROUP_CONCAT';
HAVING: 'HAVING';
HBASE_CACHE_BLOCKS: 'HBASE_CACHE_BLOCKS';
HBASE_CACHING: 'HBASE_CACHING';
IDLE_SESSION_TIMEOUT: 'IDLE_SESSION_TIMEOUT';
IF: 'IF';
IN: 'IN';
INCREMENTAL: 'INCREMENTAL';
INIT_FN: 'INIT_FN';
INNER: 'INNER';
INPATH: 'INPATH';
INSERT: 'INSERT';
INT: 'INT';
INTERMEDIATE: 'INTERMEDIATE';
INTO: 'INTO';
INVALIDATE: 'INVALIDATE';
IS: 'IS';
JOIN: 'JOIN';
JOIN_ROWS_PRODUCED_LIMIT: 'JOIN_ROWS_PRODUCED_LIMIT';
KUDU_READ_MODE: 'KUDU_READ_MODE';
LAST: 'LAST';
LEFT: 'LEFT';
LEVEL: 'LEVEL';
LIKE: 'LIKE';
LIMIT: 'LIMIT';
LINES: 'LINES';
LIVE_PROGRESS: 'LIVE_PROGRESS';
LIVE_SUMMARY: 'LIVE_SUMMARY';
LOAD: 'LOAD';
LOCATION: 'LOCATION';
LOCKED: 'LOCKED';
LZ4: 'LZ4';
MAP: 'MAP';
MAX_ERRORS: 'MAX_ERRORS';
MAX_MEM_ESTIMATE_FOR_ADMISSION: 'MAX_MEM_ESTIMATE_FOR_ADMISSION';
MAX_RESULT_SPOOLING_MEM: 'MAX_RESULT_SPOOLING_MEM';
MAX_ROW_SIZE: 'MAX_ROW_SIZE';
MAX_SCAN_RANGE_LENGTH: 'MAX_SCAN_RANGE_LENGTH';
MAX_SPILLED_RESULT_SPOOLING_MEM: 'MAX_SPILLED_RESULT_SPOOLING_MEM';
MEM_LIMIT: 'MEM_LIMIT';
MERGE:'MERGE';
MERGE_FN: 'MERGE_FN';
METADATA: 'METADATA';
MIN_SPILLABLE_BUFFER_SIZE: 'MIN_SPILLABLE_BUFFER_SIZE';
MODE:'MODE';
MT_DOP: 'MT_DOP';
NONE: 'NONE';
NOSHUFFLE: 'NOSHUFFLE';
NOT: 'NOT';
NOWAIT: 'NOWAIT';
NULL: 'NULL';
NULLS: 'NULLS';
NUM_NODES: 'NUM_NODES';
NUM_ROWS_PRODUCED_LIMIT: 'NUM_ROWS_PRODUCED_LIMIT';
NUM_SCANNER_THREADS: 'NUM_SCANNER_THREADS';
OF:'OF';
OFFSET: 'OFFSET';
ON: 'ON';
ONE: 'ONE';
OPTIMIZE_PARTITION_KEY_SCANS: 'OPTIMIZE_PARTITION_KEY_SCANS';
OR:'OR';
ORDER: 'ORDER';
OUTER: 'OUTER';
OVERWRITE: 'OVERWRITE';
OWNER : 'OWNER';
PARQUET: 'PARQUET';
PARQUET_ANNOTATE_STRINGS_UTF8: 'PARQUET_ANNOTATE_STRINGS_UTF8';
PARQUET_ARRAY_RESOLUTION: 'PARQUET_ARRAY_RESOLUTION';
PARQUET_COMPRESSION_CODEC: 'PARQUET_COMPRESSION_CODEC';
PARQUET_DICTIONARY_FILTERING: 'PARQUET_DICTIONARY_FILTERING';
PARQUET_FALLBACK_SCHEMA_RESOLUTION: 'PARQUET_FALLBACK_SCHEMA_RESOLUTION';
PARQUET_FILE_SIZE: 'PARQUET_FILE_SIZE';
PARQUET_OBJECT_STORE_SPLIT_SIZE: 'PARQUET_OBJECT_STORE_SPLIT_SIZE';
PARQUET_PAGE_ROW_COUNT_LIMIT: 'PARQUET_PAGE_ROW_COUNT_LIMIT';
PARQUET_READ_PAGE_INDEX: 'PARQUET_READ_PAGE_INDEX';
PARQUET_READ_STATISTICS: 'PARQUET_READ_STATISTICS';
PARQUET_WRITE_PAGE_INDEX: 'PARQUET_WRITE_PAGE_INDEX';
PARTITION: 'PARTITION';
PARTITIONS: 'PARTITIONS';
PERCENT: 'PERCENT';
PERCENT_RANK: 'PERCENT_RANK';
PLAIN_ENCODING: 'PLAIN_ENCODING';
PREFETCH_MODE: 'PREFETCH_MODE';
PREFIX_ENCODING: 'PREFIX_ENCODING';
PREPARE_FN: 'PREPARE_FN';
PRECEDING: 'PRECEDING';
PRIMARY:'PRIMARY';
PURGE: 'PURGE';
QUERY_TIMEOUT_S: 'QUERY_TIMEOUT_S';
RANGE: 'RANGE';
RANK: 'RANK';
RCFILE: 'RCFILE';
REAL: 'REAL';
RECOVER: 'RECOVER';
RECURSIVE:'RECURSIVE';
REFRESH: 'REFRESH';
REFRESH_UPDATED_HMS_PARTITIONS: 'REFRESH_UPDATED_HMS_PARTITIONS';
REGEXP: 'REGEXP';
RENAME: 'RENAME';
REPEATABLE: 'REPEATABLE';
REPLACE: 'REPLACE';
REPLICA_PREFERENCE: 'REPLICA_PREFERENCE';
REPLICATION: 'REPLICATION';
REQUEST_POOL: 'REQUEST_POOL';
RESOURCE_TRACE_RATIO: 'RESOURCE_TRACE_RATIO';
RESTRICT: 'RESTRICT';
RETRY_FAILED_QUERIES: 'RETRY_FAILED_QUERIES';
RETURNS: 'RETURNS';
REVOKE: 'REVOKE';
RIGHT: 'RIGHT';
RLE: 'RLE';
RLIKE: 'RLIKE';
ROLE: 'ROLE';
ROLES: 'ROLES';
ROLLUP:'ROLLUP';
ROW: 'ROW';
ROWS: 'ROWS';
ROW_NUMBER: 'ROW_NUMBER';
RUNTIME_BLOOM_FILTER_SIZE: 'RUNTIME_BLOOM_FILTER_SIZE';
RUNTIME_FILTER_MAX_SIZE: 'RUNTIME_FILTER_MAX_SIZE';
RUNTIME_FILTER_MIN_SIZE: 'RUNTIME_FILTER_MIN_SIZE';
RUNTIME_FILTER_MODE: 'RUNTIME_FILTER_MODE';
RUNTIME_FILTER_WAIT_TIME_MS: 'RUNTIME_FILTER_WAIT_TIME_MS';
S3_SKIP_INSERT_STAGING: 'S3_SKIP_INSERT_STAGING';
SAMPLE: 'SAMPLE';
SCAN_BYTES_LIMIT: 'SCAN_BYTES_LIMIT';
SCHEDULE_RANDOM_REPLICA: 'SCHEDULE_RANDOM_REPLICA';
SCHEMA: 'SCHEMA';
SCHEMAS: 'SCHEMAS';
SCRATCH_LIMIT: 'SCRATCH_LIMIT';
SELECT: 'SELECT';
SEMI: 'SEMI';
SEPARATOR: 'SEPARATOR';
SEQUENCEFILE: 'SEQUENCEFILE';
SERDEPROPERTIES: 'SERDEPROPERTIES';
SERIALIZE_FN: 'SERIALIZE_FN';
SERVER: 'SERVER';
SET : 'SET';
SHARE:'SHARE';
SHOW: 'SHOW';
SHUFFLE: 'SHUFFLE';
SHUFFLE_DISTINCT_EXPRS: 'SHUFFLE_DISTINCT_EXPRS';
SHUTDOWN: 'SHUTDOWN';
SKIP_SYMBOL: 'SKIP';
SMALLINT: 'SMALLINT';
SNAPPY: 'SNAPPY';
SOUNDS: 'SOUNDS';
SPOOL_QUERY_RESULTS: 'SPOOL_QUERY_RESULTS';
STATISTICS: 'STATISTICS';
STATS: 'STATS';
STORED: 'STORED';
STRAIGHT_JOIN: 'STRAIGHT_JOIN';
STRING: 'STRING';
STRUCT: 'STRUCT';
SUPPORT_START_OVER: 'SUPPORT_START_OVER';
SYMBOL: 'SYMBOL';
SYNC_DDL: 'SYNC_DDL';
SYSTEM: 'SYSTEM';
TABLE: 'TABLE';
TABLES: 'TABLES';
TABLESAMPLE: 'TABLESAMPLE';
TBLPROPERTIES: 'TBLPROPERTIES';
TERMINATED: 'TERMINATED';
TEXTFILE: 'TEXTFILE';
THREAD_RESERVATION_AGGREGATE_LIMIT: 'THREAD_RESERVATION_AGGREGATE_LIMIT';
THREAD_RESERVATION_LIMIT: 'THREAD_RESERVATION_LIMIT';
TIMESTAMP: 'TIMESTAMP';
TIMEZONE: 'TIMEZONE';
TINYINT: 'TINYINT';
TO: 'TO';
TOPN_BYTES_LIMIT: 'TOPN_BYTES_LIMIT';
TRUE: 'TRUE';
TRUNCATE: 'TRUNCATE';
UNCACHED: 'UNCACHED';
UNION: 'UNION';
UNBOUNDED: 'UNBOUNDED';
UPDATE: 'UPDATE';
UPDATE_FN: 'UPDATE_FN';
UPSERT: 'UPSERT';
URI: 'URI';
USE: 'USE';
USER: 'USER';
UTF8_MODE: 'UTF8_MODE';
VALUE: 'VALUE';
VALUES: 'VALUES';
VARCHAR: 'VARCHAR';
VIEW: 'VIEW';
WHERE: 'WHERE';
WITH: 'WITH';
WINDOW:'WINDOW';
ZERO: 'ZERO';
ZLIB: 'ZLIB';

//other
NTILE: 'NTILE';
LEAD: 'LEAD';
LAG: 'LAG';
FIRST_VALUE: 'FIRST_VALUE';
LAST_VALUE: 'LAST_VALUE';
NTH_VALUE: 'NTH_VALUE';
CAST: 'CAST';
TIME: 'TIME';
BYTE: 'BYTE';
ASCII: 'ASCII';
UNICODE: 'UNICODE';
CHARACTER: 'CHARACTER';
CHARSET: 'CHARSET';
NCHAR: 'NCHAR';
NATIONAL_CHAR: 'NATIONAL_CHAR';
SIGNED: 'SIGNED';
SIGNED_INT: 'SIGNED_INT';
SIGNED_INTEGER: 'SIGNED_INTEGER';
JSON: 'JSON';
PRECISION: 'PRECISION';
CONVERT: 'CONVERT';
USING:'USING';
POSITION:'POSITION';
SUBSTRING:'SUBSTRING';
SUBSTR:'SUBSTR';
EXTRACT:'EXTRACT';
LEADING:'LEADING';
BOTH:'BOTH';
TRAILING:'TRAILING';
WEIGHT_STRING:'WEIGHT_STRING';
REVERSE: 'REVERSE';
LOCALTIME: 'LOCALTIME';
LOCALTIMESTAMP: 'LOCALTIMESTAMP';
GEOMETRYCOLLECTION: 'GEOMETRYCOLLECTION';
REPEAT: 'REPEAT';
LINESTRING:'LINESTRING';
MULTILINESTRING:'MULTILINESTRING';
MULTIPOINT:'MULTIPOINT';
MULTIPOLYGON:'MULTIPOLYGON';
POINT:'POINT';
POLYGON:'POLYGON';
TIMESTAMP_ADD:'TIMESTAMP_ADD';
TIMESTAMP_DIFF:'TIMESTAMP_DIFF';
CURRENT_TIMESTAMP:'CURRENT_TIMESTAMP';
CURRENT_DATE:'CURRENT_DATE';
CURRENT_TIME:'CURRENT_TIME';
UTC_TIMESTAMP:'UTC_TIMESTAMP';
LAST_DAY:'LAST_DAY';
MEMBER:'MEMBER';
GLOBAL:'GLOBAL';
SESSION:'SESSION';
LOCAL:'LOCAL';
RETURNING:'RETURNING';
ERROR:'ERROR';
EMPTY:'EMPTY';
MATCH:'MATCH';
AGAINST:'AGAINST';
NATURAL:'NATURAL';
LANGUAGE:'LANGUAGE';
EXPANSION:'EXPANSION';
WHEN:'WHEN';
THEN:'THEN';
ELSE:'ELSE';
END:'END';
CASE:'CASE';
XOR:'XOR';
HIGH_PRIORITY:'HIGH_PRIORITY';
SQL_SMALL_RESULT:'SQL_SMALL_RESULT';
SQL_BIG_RESULT:'SQL_BIG_RESULT';
SQL_BUFFER_RESULT:'SQL_BUFFER_RESULT';
SQL_NO_CACHE:'SQL_NO_CACHE';
SQL_CALC_FOUND_ROWS:'SQL_CALC_FOUND_ROWS';
OUTFILE:'OUTFILE';
OPTIONALLY:'OPTIONALLY';
ENCLOSED:'ENCLOSED';
STARTING:'STARTING';
FOREIGN:'FOREIGN';
REFERENCES:'REFERENCES';
KUDU:'KUDU';
DUAL:'DUAL';
HASH:'HASH';
LEXICAL:'LEXICAL';
ZORDER:'ZORDER';



//json func name
JSON_ARRAY: 'JSON_ARRAY';
JSON_ARRAY_APPEND: 'JSON_ARRAY_APPEND';
JSON_ARRAY_INSERT: 'JSON_ARRAY_INSERT';
JSON_CONTAINS: 'JSON_CONTAINS';
JSON_CONTAINS_PATH: 'JSON_CONTAINS_PATH';
JSON_DEPTH: 'JSON_DEPTH';
JSON_EXTRACT: 'JSON_EXTRACT';
JSON_INSERT: 'JSON_INSERT';
JSON_KEYS: 'JSON_KEYS';
JSON_LENGTH: 'JSON_LENGTH';
JSON_MERGE: 'JSON_MERGE';
JSON_MERGE_PATCH: 'JSON_MERGE_PATCH';
JSON_MERGE_PRESERVE: 'JSON_MERGE_PRESERVE';
JSON_OBJECT: 'JSON_OBJECT';
JSON_OVERLAPS: 'JSON_OVERLAPS';
JSON_PRETTY: 'JSON_PRETTY';
JSON_QUOTE: 'JSON_QUOTE';
JSON_REMOVE: 'JSON_REMOVE';
JSON_REPLACE: 'JSON_REPLACE';
JSON_SCHEMA_VALID: 'JSON_SCHEMA_VALID';
JSON_SCHEMA_VALIDATION_REPORT: 'JSON_SCHEMA_VALIDATION_REPORT';
JSON_SEARCH: 'JSON_SEARCH';
JSON_SET: 'JSON_SET';
JSON_STORAGE_FREE: 'JSON_STORAGE_FREE';
JSON_STORAGE_SIZE: 'JSON_STORAGE_SIZE';
JSON_TABLE: 'JSON_TABLE';
JSON_TYPE: 'JSON_TYPE';
JSON_UNQUOTE: 'JSON_UNQUOTE';
JSON_VALID: 'JSON_VALID';
JSON_VALUE: 'JSON_VALUE';




//hive补充
AUTOCOMMIT: 'AUTOCOMMIT';
AVG: 'AVG';
BATCH: 'BATCH';
BEFORE: 'BEFORE';
BIT_XOR: 'BIT_XOR';
BUCKET: 'BUCKET';
BUCKETS: 'BUCKETS';
CACHE: 'CACHE';
CBO: 'CBO';
CHECK: 'CHECK';
CLUSTER: 'CLUSTER';
CLUSTERED: 'CLUSTERED';
CLUSTERSTATUS: 'CLUSTERSTATUS';
COLLECTION: 'COLLECTION';
COMPACT: 'COMPACT';
COMPACTIONS: 'COMPACTIONS';
CONCATENATE: 'CONCATENATE';
CONTINUE: 'CONTINUE';
COST: 'COST';
COUNT: 'COUNT';
CRON: 'CRON';
DATETIME: 'DATETIME';
DAY: 'DAY';
DAY_MICROSECOND: 'DAY_MICROSECOND';
DAY_SECOND: 'DAY_SECOND';
DAY_MINUTE: 'DAY_MINUTE';
DAY_HOUR: 'DAY_HOUR';
DAYOFWEEK: 'DAYOFWEEK';
DBPROPERTIES: 'DBPROPERTIES';
DCPROPERTIES: 'DCPROPERTIES';
DEBUG: 'DEBUG';
DEFERRED: 'DEFERRED';
DEFINED: 'DEFINED';
DEPENDENCY: 'DEPENDENCY';
DETAIL: 'DETAIL';
DIRECTORIES: 'DIRECTORIES';
DIRECTORY: 'DIRECTORY';
DISABLE: 'DISABLE';
DISTRIBUTE: 'DISTRIBUTE';
DISTRIBUTED: 'DISTRIBUTED';
DIV: 'DIV';
DO: 'DO';
DOW: 'DOW';
DUMP: 'DUMP';
ELEM_TYPE: 'ELEM_TYPE';
ENABLE: 'ENABLE';
ENFORCED: 'ENFORCED';
EVERY: 'EVERY';
EXCLUSIVE: 'EXCLUSIVE';
EXCEPT:'EXCEPT';
EXECUTE: 'EXECUTE';
EXECUTED: 'EXECUTED';
EXPIRE_SNAPSHOTS: 'EXPIRE_SNAPSHOTS';
EXPORT: 'EXPORT';
EXPRESSION: 'EXPRESSION';
FORCE:'FORCE';
FILE: 'FILE';
HOLD_DDLTIME: 'HOLD_DDLTIME';
HOUR: 'HOUR';
HOUR_MICROSECOND: 'HOUR_MICROSECOND';
HOUR_SECOND: 'HOUR_SECOND';
HOUR_MINUTE: 'HOUR_MINUTE';
IDXPROPERTIES: 'IDXPROPERTIES';
IGNORE: 'IGNORE';
INDEX: 'INDEX';
INDEXES: 'INDEXES';
INPUTDRIVER: 'INPUTDRIVER';
INPUTFORMAT: 'INPUTFORMAT';
INTERSECT:'INTERSECT';
INTERVAL: 'INTERVAL';
ISOLATION: 'ISOLATION';
ITEMS: 'ITEMS';
JAR: 'JAR';
JOINCOST: 'JOINCOST';
KEY: 'KEY';
KEYS: 'KEYS';
KEY_TYPE: 'KEY_TYPE';
KILL: 'KILL';
LOCK: 'LOCK';
LOCKS: 'LOCKS';
LOGICAL: 'LOGICAL';
LONG: 'LONG';
MAX: 'MAX';
MANAGED: 'MANAGED';
MANAGEDLOCATION: 'MANAGEDLOCATION';
MANAGEMENT: 'MANAGEMENT';
MAPJOIN: 'MAPJOIN';
MAPPING: 'MAPPING';
MATCHED: 'MATCHED';
MATERIALIZED: 'MATERIALIZED';
MIN: 'MIN';
MINUS:'MINUS';
MINUTE: 'MINUTE';
MICROSECOND: 'MICROSECOND';
MINUTE_MICROSECOND: 'MINUTE_MICROSECOND';
MINUTE_SECOND: 'MINUTE_SECOND';
MONTH: 'MONTH';
MOD: 'MOD';
MOVE: 'MOVE';
MSCK: 'MSCK';
NORELY: 'NORELY';
NOSCAN: 'NOSCAN';
NOVALIDATE: 'NOVALIDATE';
NO_DROP: 'NO_DROP';
OFFLINE: 'OFFLINE';
OPERATOR: 'OPERATOR';
OPTION: 'OPTION';
OUTPUTDRIVER: 'OUTPUTDRIVER';
OUTPUTFORMAT: 'OUTPUTFORMAT';
OVER: 'OVER';
OJ:'OJ';
PARTITIONED: 'PARTITIONED';
PATH: 'PATH';
PLAN: 'PLAN';
PLANS: 'PLANS';
PLUS: 'PLUS';
POOL: 'POOL';
PRINCIPALS: 'PRINCIPALS';
PROTECTION: 'PROTECTION';
QUARTER: 'QUARTER';
QUERY: 'QUERY';
QUERY_PARALLELISM: 'QUERY_PARALLELISM';
READ: 'READ';
READONLY: 'READONLY';
REBUILD: 'REBUILD';
RECORDREADER: 'RECORDREADER';
RECORDWRITER: 'RECORDWRITER';
RELOAD: 'RELOAD';
RELY: 'RELY';
REMOTE: 'REMOTE';
REOPTIMIZATION: 'REOPTIMIZATION';
REPAIR: 'REPAIR';
REPL: 'REPL';
RESOURCE: 'RESOURCE';
RESPECT: 'RESPECT';
REWRITE: 'REWRITE';
SCHEDULED: 'SCHEDULED';
SCHEDULING_POLICY: 'SCHEDULING_POLICY';
SECOND: 'SECOND';
SECOND_MICROSECOND: 'SECOND_MICROSECOND';
SERDE: 'SERDE';
SETS: 'SETS';
SET_CURRENT_SNAPSHOT: 'SET_CURRENT_SNAPSHOT';
SHARED: 'SHARED';
SHOW_DATABASE: 'SHOW_DATABASE';
SKEWED: 'SKEWED';
SNAPSHOT: 'SNAPSHOT';
SORT: 'SORT';
SORTED: 'SORTED';
SPEC: 'SPEC';
SSL: 'SSL';
STATUS: 'STATUS';
STREAMTABLE: 'STREAMTABLE';
SUM: 'SUM';
SUMMARY: 'SUMMARY';
SYSTEM_TIME: 'SYSTEM_TIME';
SYSTEM_VERSION: 'SYSTEM_VERSION';
TEMPORARY: 'TEMPORARY';
TIMESTAMPTZ: 'TIMESTAMPTZ';
TOUCH: 'TOUCH';
TRANSACTION: 'TRANSACTION';
TRANSACTIONAL: 'TRANSACTIONAL';
TRANSACTIONS: 'TRANSACTIONS';
TRANSFORM:'TRANSFORM';
TRIM: 'TRIM';
TYPE: 'TYPE';
UNARCHIVE: 'UNARCHIVE';
UNDO: 'UNDO';
UNIONTYPE: 'UNIONTYPE';
UNKNOWN: 'UNKNOWN';
UNLOCK: 'UNLOCK';
UNMANAGED: 'UNMANAGED';
UNSET: 'UNSET';
UNSIGNED: 'UNSIGNED';
UNSIGNED_INT: 'UNSIGNED_INT';
UNSIGNED_INTEGER: 'UNSIGNED_INTEGER';
URL: 'URL';
UTC: 'UTC';
UTCTIMESTAMP: 'UTCTIMESTAMP';
VALIDATE: 'VALIDATE';
VALUE_TYPE: 'VALUE_TYPE';
VECTORIZATION: 'VECTORIZATION';
VIEWS: 'VIEWS';
WAIT: 'WAIT';
WEEK: 'WEEK';
WHILE: 'WHILE';
WITHIN: 'WITHIN';
WORK: 'WORK';
WORKLOAD: 'WORKLOAD';
WRITE: 'WRITE';
YEAR: 'YEAR';
YEAR_MONTH: 'YEAR_MONTH';
ZONE: 'ZONE';



OR_:                 '||';
AND_:                '&&';
AMPERSAND_:          '&';
NEQ_:                  '<>' | '!=' | '^=';
EQ_:                  '=';
GT_:                  '>';
GTE_:                  '>=';
LT_:                  '<';
LTE_:                  '<=';
NOT_:                '!';
DOT_:                 '.';
DOT_ASTERISK_:       '.*';
UNDERLINE_:           '_';
LP_:          '(';
RP_:          ')';
COMMA_:               ',';
SEMI_:                ';';
DASHES_:             '--';
PLUS_:                '+';
MINUS_:               '-';
COLON_:               ':';
SQ_:                  '\'';
DQ_:                  '"';
BQ_:                  '`';
SAFE_EQ_:            '<=>';
VERTICAL_BAR_:       '|';
SIGNED_LEFT_SHIFT_:  '<<';
SIGNED_RIGHT_SHIFT_: '>>';
ASTERISK_:           '*';
SLASH_:              '/';
MOD_:                '%';
CARET_:              '^';
AT_:                 '@';
TILDE_:              '~';
LBE_:                '{';
RBE_:                '}';
LBT_:                '[';
RBT_:                ']';
ASSIGNMENT_:         ':=';
JSON_SEPARATOR:      '->';
JSON_UNQUOTED_SEPARATOR:      '->>';
QUESTION_:         '?';

WS: [ \t\r\n\u3000]+   -> skip;


BLOCK_HINT: '/*' WS* '+' ((BLOCK_HINT | INLINE_HINT) | .)*? '*/';
INLINE_HINT: '--' WS* '+' ((BLOCK_HINT | INLINE_HINT) | ~[\r\n])*? ('\r'? '\n' | EOF);

HINT: BLOCK_HINT | INLINE_HINT;


BLOCK_COMMENT:            '/*' (BLOCK_COMMENT | .)*? '*/'                  -> channel(HIDDEN);
INLINE_COMMENT:           '--' ~[\r\n]* ('\r'? '\n' | EOF)                 -> channel(HIDDEN);


STRING_: SINGLE_QUOTED_TEXT | DOUBLE_QUOTED_TEXT;
SINGLE_QUOTED_TEXT: SQ_ (~('\'' | '\r' | '\n') | '\'' '\'' | '\r'? '\n')* SQ_;
DOUBLE_QUOTED_TEXT: (DQ_ ( '\\'. | '""' | ~('"'| '\\') )* DQ_);

BQUOTA_STRING: BQ_ ( '\\'. | '``' | ~('`'|'\\'))* BQ_;

UNDERSCORE_CHARSET: UL_ [0-9A-Z]+;
UL_BINARY: UL_ 'BINARY';


HEX_DIGIT_: '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_ | 'X' SQ_ + SQ_;
BIT_NUM_: '0b' ('0' | '1')+ | 'B' SQ_ ('0' | '1')+ SQ_;
INTEGER_: INT_;
NUMBER_: INT_ DOT_? INT_? ('E' (PLUS_ | MINUS_)? INT_)?;




I_CURSOR : '[CURSOR]' ;
IDENTIFIER_
    : [A-Z_$0-9\u0080-\uFFFF]*?[A-Z_$\u0080-\uFFFF]+?[A-Z_$0-9\u0080-\uFFFF]*
    | BQ_ ~'`'+ BQ_
    ;
E: 'E';
N: 'N';

fragment LETTER:       [A-Z_];
fragment DEC_DOT_DEC:  (DEC_DIGIT+ '.' DEC_DIGIT+ |  DEC_DIGIT+ '.' | '.' DEC_DIGIT+);
fragment DEC_DIGIT:    [0-9];
fragment INT_: [0-9]+;
fragment HEX_: [0-9A-F];
fragment UL_: '_';
fragment EscapeSequence
    : '\\' [btnfr"'\\]
    | '\\' ([0-3]? [0-7])? [0-7]
    | '\\' 'u'+ HEX_DIGIT_ HEX_DIGIT_ HEX_DIGIT_ HEX_DIGIT_
    ;