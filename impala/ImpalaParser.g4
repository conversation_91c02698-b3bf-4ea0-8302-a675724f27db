parser grammar ImpalaParser;

options {
    tokenVocab = ImpalaLexer;
}

root: (copyTestcase
    | alterDatabase
    | alterTable
    | alterView
    | comment
    | computeStats
    | createDatabase
    | createFunction
    | createRole
    | createTable
    | createView
    | delete
    | describe
    | dropDatabase
    | dropFunction
    | dropRole
    | dropStats
    | dropTable
    | dropView
    | explain
    | grant
    | insert
    | invalidateMetadata
    | loadData
    | merge
    | refresh
    | refreshAuthorization
    | refreshFunctions
    | revoke
    | select
    | set
    | show
    | shutdown
    | truncateTable
    | update
    | upsert
    | use
    | values
     ) SEMI_? EOF;



select
    : queryExpression lockClauseList?
    | queryExpressionParens
    | selectWithInto
    ;

selectWithInto
    : LP_ selectWithInto RP_
    | queryExpression selectIntoExpression lockClauseList?
    | queryExpression lockClauseList selectIntoExpression
    ;



queryExpression
    : withClause? (queryExpressionBody | queryExpressionParens) orderByClause? limitClause?
    ;
queryExpressionBody
    : queryPrimary
    | queryExpressionParens combineClause
    | queryExpressionBody combineClause
    ;


combineClause
    : UNION combineOption? (queryPrimary | queryExpressionParens)
    | EXCEPT combineOption? (queryPrimary | queryExpressionParens)
    ;
combineOption
    : ALL | DISTINCT
    ;


queryPrimary
    : querySpecification
    | tableValueConstructor
    | tableStatement
    ;
querySpecification
    : SELECT hint? selectSpecification* projections selectIntoExpression? fromClause? whereClause? groupByClause? havingClause? windowClause?
    ;

selectSpecification
    : duplicateSpecification | HIGH_PRIORITY | STRAIGHT_JOIN | SQL_SMALL_RESULT | SQL_BIG_RESULT | SQL_BUFFER_RESULT | SQL_NO_CACHE | SQL_CALC_FOUND_ROWS
    ;
duplicateSpecification
    : ALL | DISTINCT | DISTINCTROW
    ;

projections
    : (unqualifiedShorthand | projection) (COMMA_ projection)*
    ;
unqualifiedShorthand
    : ASTERISK_
    ;
projection
    : expr (AS? alias)? | qualifiedShorthand
    ;
qualifiedShorthand
    : (identifier DOT_)? identifier DOT_ ASTERISK_
    ;


selectIntoExpression
    : INTO variable (COMMA_ variable )* | INTO DUMPFILE STRING_
    | (INTO OUTFILE STRING_ (CHARACTER SET charsetName)?(COLUMNS selectFieldsInto+)? (LINES selectLinesInto+)?)
    ;
selectFieldsInto
    : TERMINATED BY STRING_ | OPTIONALLY? ENCLOSED BY STRING_ | ESCAPED BY STRING_
    ;
selectLinesInto
    : STARTING BY STRING_ | TERMINATED BY STRING_
    ;


windowClause
    : WINDOW windowItem (COMMA_ windowItem)*
    ;
windowItem
    : identifier AS windowSpecification
    ;



havingClause
    : HAVING expr
    ;



groupByClause
    : GROUP BY orderByItem (COMMA_ orderByItem)* (WITH ROLLUP)?
    ;



whereClause
    : WHERE expr
    ;



limitClause
    : LIMIT ((limitOffset COMMA_)? limitRowCount | limitRowCount OFFSET limitOffset)
    ;
limitOffset
    : numberLiterals | parameterMarker
    ;
limitRowCount
    : numberLiterals | parameterMarker
    ;




withClause
    : WITH RECURSIVE? cteClause (COMMA_ cteClause)*
    ;
cteClause
    : identifier (LP_ columnNames RP_)? AS subquery
    ;
columnNames
    : columnName (COMMA_ columnName)*
    ;

orderByClause
    : ORDER BY orderByItem (COMMA_ orderByItem)*
    ;
orderByItem
    : (numberLiterals | expr) direction? sortOrderForNullValues?
    ;

numberLiterals
    : (PLUS_ | MINUS_)? number
    ;
number
    : NUMBER_ | INTEGER_
    ;

expr
    : booleanPrimary
    | expr andOperator expr
    | expr orOperator expr
    | expr XOR expr
    | notOperator expr
    | LP_ expr RP_
    ;
andOperator
    : AND | AND_
    ;

orOperator
    : OR | OR_
    ;

booleanPrimary
    : booleanPrimary IS NOT? (TRUE | FALSE | UNKNOWN | NULL)
    | booleanPrimary SAFE_EQ_ predicate
    | booleanPrimary MEMBER OF LP_ (expr) RP_
    | booleanPrimary comparisonOperator predicate
    | booleanPrimary comparisonOperator (ALL | ANY) subquery
    | booleanPrimary assignmentOperator predicate
    | predicate
    ;
comparisonOperator
    : EQ_ | GTE_ | GT_ | LTE_ | LT_ | NEQ_ | LIKE
    ;
assignmentOperator
    : EQ_ | ASSIGNMENT_
    ;

predicate
    : bitExpr NOT? IN subquery
    | bitExpr NOT? IN LP_ expr (COMMA_ expr)* RP_
    | bitExpr NOT? BETWEEN bitExpr AND predicate
    | bitExpr SOUNDS LIKE bitExpr
    | bitExpr NOT? LIKE simpleExpr (ESCAPE simpleExpr)?
    | bitExpr NOT? (REGEXP | RLIKE) bitExpr
    | bitExpr
    ;

bitExpr
    : bitExpr VERTICAL_BAR_ bitExpr
    | bitExpr AMPERSAND_ bitExpr
    | bitExpr SIGNED_LEFT_SHIFT_ bitExpr
    | bitExpr SIGNED_RIGHT_SHIFT_ bitExpr
    | bitExpr PLUS_ bitExpr
    | bitExpr MINUS_ bitExpr
    | bitExpr ASTERISK_ bitExpr
    | bitExpr SLASH_ bitExpr
    | bitExpr DIV bitExpr
    | bitExpr MOD bitExpr
    | bitExpr MOD_ bitExpr
    | bitExpr CARET_ bitExpr
    | bitExpr PLUS_ intervalExpression
    | bitExpr MINUS_ intervalExpression
    | simpleExpr
    ;

simpleExpr
    : functionCall
    | parameterMarker
    | literals
    | columnName (mapKeyValueSymbol)?
    | simpleExpr collateClause
    | variable
    | simpleExpr OR_ simpleExpr
    | (PLUS_ | MINUS_ | TILDE_ | notOperator | BINARY) simpleExpr
    | ROW? LP_ expr (COMMA_ expr)* RP_
    | EXISTS? subquery
    | LBE_ identifier expr RBE_
    | identifier (JSON_SEPARATOR | JSON_UNQUOTED_SEPARATOR) STRING_
    | path (RETURNING dataType)? onEmptyError?
    | matchExpression
    | caseExpression
    | intervalExpression
    ;


matchExpression
    : MATCH (columnNames | LP_ columnNames RP_ ) AGAINST LP_ expr matchSearchModifier? RP_
    ;
matchSearchModifier
    : IN NATURAL LANGUAGE MODE | IN NATURAL LANGUAGE MODE WITH QUERY EXPANSION | IN BOOLEAN MODE | WITH QUERY EXPANSION
    ;
notOperator
    : NOT | NOT_
    ;
onEmptyError
    : (NULL | ERROR | DEFAULT literals) ON (EMPTY | ERROR)
    ;

caseExpression
    : CASE simpleExpr? caseWhen+ caseElse? END
    ;
caseWhen
    : WHEN expr THEN expr
    ;
caseElse
    : ELSE expr
    ;



variable
    : userVariable | systemVariable
    ;
userVariable
    : AT_ textOrIdentifier
    | textOrIdentifier
    ;
systemVariable
    : AT_ AT_ (systemVariableScope=(GLOBAL | SESSION | LOCAL) DOT_)? rvalueSystemVariable
    ;
rvalueSystemVariable
    : textOrIdentifier
    | textOrIdentifier DOT_ identifier
    ;





intervalExpression
    : INTERVAL intervalValue
    ;

intervalValue
    : expr intervalUnit
    ;

intervalUnit
    : MICROSECOND | SECOND | MINUTE | HOUR | DAY | WEEK | MONTH
    | QUARTER | YEAR | SECOND_MICROSECOND | MINUTE_MICROSECOND | MINUTE_SECOND | HOUR_MICROSECOND | HOUR_SECOND
    | HOUR_MINUTE | DAY_MICROSECOND | DAY_SECOND | DAY_MINUTE | DAY_HOUR | YEAR_MONTH
    ;



literals
    : stringLiterals
    | numberLiterals
    | temporalLiterals
    | hexadecimalLiterals
    | bitValueLiterals
    | booleanLiterals
    | nullValueLiterals
    ;

stringLiterals
    : (UNDERSCORE_CHARSET | UL_BINARY )? STRING_ | nCharText
    ;

temporalLiterals
    : (DATE | TIME | TIMESTAMP) SINGLE_QUOTED_TEXT
    ;

hexadecimalLiterals
    : UNDERSCORE_CHARSET? HEX_DIGIT_ collateClause?
    ;

bitValueLiterals
    : UNDERSCORE_CHARSET? BIT_NUM_ collateClause?
    ;

booleanLiterals
    : TRUE | FALSE
    ;

nullValueLiterals
    : NULL
    ;




alias
    : identifier
    ;




fromClause
    : FROM (DUAL | tableReferences)
    ;

tableReferences
    : tableReference (COMMA_ tableReference)*
    ;

tableReference
    : (tableFactor | LBE_ OJ escapedTableReference RBE_) joinedTable* tablesampleClause?
    ;
tableFactor
    : tableName partitionNames? (AS? alias)? indexHintList? | subquery AS? alias (LP_ columnNames RP_)? | LP_ tableReferences RP_
    ;
partitionNames
    : PARTITION LP_ identifier (COMMA_ identifier)* RP_
    ;
indexHintList
    : indexHint (COMMA_ indexHint)*
    ;
indexHint
    : (USE | IGNORE | FORCE) (INDEX | KEY) (FOR (JOIN | ORDER BY | GROUP BY))? LP_ indexName (COMMA_ indexName)* RP_
    ;
indexName
    : identifier
    ;
escapedTableReference
    : tableFactor joinedTable*
    ;
joinedTable
    : innerJoinType hint? tableReference joinSpecification?
    | outerJoinType hint? tableReference joinSpecification
    | naturalJoinType hint? tableFactor
    ;
innerJoinType
    : (INNER | CROSS)? JOIN
    | STRAIGHT_JOIN
    ;
outerJoinType
    : (LEFT | RIGHT) OUTER? JOIN
    ;
naturalJoinType
    : NATURAL INNER? JOIN
    | NATURAL (LEFT | RIGHT) OUTER? JOIN
    ;
joinSpecification
    : ON expr | USING LP_ columnNames RP_
    ;



tableValueConstructor
    : VALUES rowConstructorList
    ;
rowConstructorList
    : (ROW)? assignmentValues (COMMA_ (ROW)? assignmentValues)*
    ;
assignmentValues
    : LP_ assignmentValue (COMMA_ assignmentValue)* RP_
    | LP_ RP_
    ;
assignmentValue
    : blobValue | expr | DEFAULT
    ;
blobValue
    : UL_BINARY STRING_
    ;


tableStatement
    : TABLE tableName
    ;
tablesampleClause
    : TABLESAMPLE SYSTEM LP_ percentage RP_ (REPEATABLE LP_ seed RP_)?
    ;


copyTestcase
    : copyTestcaseTo | copyTestcaseFrom
    ;
copyTestcaseTo
    : COPY TESTCASE TO dirPath select
    ;
copyTestcaseFrom
    : COPY TESTCASE FROM filePath
    ;



subquery
    : queryExpressionParens
    ;
queryExpressionParens
    : LP_ (queryExpressionParens | queryExpression lockClauseList?) RP_
    ;
lockClauseList
    : lockClause+
    ;
lockClause
    : FOR lockStrength tableLockingList? lockedRowAction?
    | LOCK IN SHARE MODE
    ;
lockStrength
    : UPDATE | SHARE
    ;
tableLockingList
    : OF tableAliasRefList
    ;
tableAliasRefList
    : tableIdentOptWild (COMMA_ tableIdentOptWild)*
    ;
tableIdentOptWild
    : tableName DOT_ ASTERISK_?
    ;
lockedRowAction
    : SKIP_SYMBOL LOCKED | NOWAIT
    ;



delete
    : DELETE hint? FROM? tableName (whereClause)?
    | DELETE hint? tableReference FROM multipleJoinedTable (whereClause)?
    ;

multipleJoinedTable
    : tableFactor joinedTable+
    ;


insert
    : withClause?
        INSERT insertHintClause? (INTO | OVERWRITE) TABLE? tableName
        (LP_ columnNames RP_)?
        insertPartitionClause?
      (
          insertHintClause? select
        | insertValuesClause
      )
    ;

insertValuesClause
    : VALUES rowConstructorList
    ;

insertPartitionClause
    : PARTITION LP_ partitionSpec (COMMA_ partitionSpec)* RP_
    ;


insertHintClause
    : hintWithDashes
    | hintWithCstyleComments
    | hintWithBrackets
    ;

hintWithDashes
    : INLINE_HINT
    ;

hintWithCstyleComments
    : BLOCK_HINT
    ;

hintWithBrackets
    : LBT_ (SHUFFLE | NOSHUFFLE) RBT_
        //(With this hint format, the square brackets are part of the syntax.)
    ;

update
    : UPDATE hint? tableName setAssignmentsClause fromClause? whereClause?
    ;
setAssignmentsClause
    : valueReference? SET assignment (COMMA_ assignment)*
    ;
valueReference
    : AS alias derivedColumns?
    ;
derivedColumns
    : LP_ alias (COMMA_ alias)* RP_
    ;
assignment
    : columnName EQ_ assignmentValue
    ;



upsert
    : UPSERT upsertHintClause? INTO TABLE? tableName
        (LP_ columnNames RP_)?
      (
          upsertHintClause? select
        | upsertValuesClause
      )
    ;

upsertHintClause
    : LBT_ SHUFFLE RBT_
    | LBT_ NOSHUFFLE RBT_
      //  (Note: the square brackets are part of the syntax.)
    ;
upsertValuesClause
    : VALUES rowConstructorList
    ;

truncateTable
    : TRUNCATE TABLE? ifExists? tableName
    ;

values
    : originValues
    | selectFromValues
    ;

originValues
    : VALUES originValuesRowList
    ;
selectFromValues
    : SELECT projections FROM LP_ originValues RP_ AS alias
    ;

originValuesRowList
    : valuesRow (COMMA_ valuesRow)*
    ;
valuesRow
    : LP_ valuesRowElement (COMMA_ valuesRowElement)* RP_
    | LP_ RP_
    ;
valuesRowElement
    : bitExpr (AS alias)?
    ;




alterDatabase
    : ALTER DATABASE databaseName SET OWNER USER username
    ;

alterTable
    : alterTableRename
    | alterTableAddColumns
    | alterTableReplaceColumns
    | alterTableAddColumn
    | alterTableDropColumn
    | alterTableChange
    | alterTableSetOwnerUser
      // Kudu tables only.
    | alterTableAlterSetOrDrop
      // Non-Kudu tables only.
    | alterTableAlterSetComment
    | alterTableAddPartition
    | alterTableAddRangePartition
    | alterTableDropPartition
    | alterTableDropRangePartition
    | alterTableRecoverPartitions
    | alterTableSet
    | alterTableColumnStatsKey
    | alterTableSetCachedOrUncached
    ;

alterTableRename
    : ALTER TABLE ot=tableName RENAME TO nt=tableName
    ;
alterTableAddColumns
    : ALTER TABLE tableName ADD ifNotExists? COLUMNS LP_ colSpec (COMMA_ colSpec)* RP_
    ;
alterTableReplaceColumns
    : ALTER TABLE tableName REPLACE COLUMNS LP_ colSpec (COMMA_ colSpec)* RP_
    ;
alterTableAddColumn
    : ALTER TABLE tableName ADD COLUMN ifNotExists? colSpec
    ;
alterTableDropColumn
    : ALTER TABLE tableName DROP COLUMN? columnName
    ;
alterTableChange
    : ALTER TABLE tableName CHANGE columnName colSpec
    ;
alterTableSetOwnerUser
    : ALTER TABLE tableName SET OWNER USER username
    ;
alterTableAlterSetOrDrop
    : ALTER TABLE tableName ALTER COLUMN? columnName (SET kuduStorageAttrAndValue | DROP DEFAULT)
    ;
alterTableAlterSetComment
    : ALTER TABLE tableName ALTER COLUMN? columnName SET COMMENT STRING_
    ;
alterTableAddPartition
    : ALTER TABLE tableName ADD ifNotExists? PARTITION partitionSpec
              location_spec?
              cache_spec?
    ;
alterTableAddRangePartition
    : ALTER TABLE tableName ADD ifNotExists? RANGE PARTITION kuduPartitionSpec
    ;
alterTableDropPartition
    : ALTER TABLE tableName DROP ifExists? PARTITION partitionSpec PURGE?
    ;
alterTableDropRangePartition
    : ALTER TABLE tableName DROP ifExists? RANGE PARTITION kuduPartitionSpec
    ;
alterTableRecoverPartitions
    : ALTER TABLE tableName RECOVER PARTITIONS
    ;
alterTableSet
    : ALTER TABLE tableName (PARTITION partitionSpec)?
              SET ( FILEFORMAT fileFormat
                  | rowFormatClause
                  | locationClause
                  | TBLPROPERTIES LP_ kvProperties RP_
                  | SERDEPROPERTIES LP_ kvProperties RP_
                  )
    ;
alterTableColumnStatsKey
    : ALTER TABLE tableName (SET COLUMN STATS)? columnName LP_ kvProperties RP_
    ;
alterTableSetCachedOrUncached
    : ALTER TABLE tableName (PARTITION partitionSpec)? SET ( CACHED IN STRING_ (WITH REPLICATION EQ_ INTEGER_)? | UNCACHED )
    ;




kuduStorageAttrAndValue
    : DEFAULT constant?
    | BLOCK_SIZE number
    | ENCODING codec
    | COMPRESSION compAlgorithm
    ;

colSpec
    : columnName dataType commentClause? kuduAttributes?
    ;


primitiveType
    : TINYINT
    | SMALLINT
    | INT
    | BIGINT
    | BOOLEAN
    | FLOAT
    | DATE
    | DOUBLE
    | REAL
    | DECIMAL
    | STRING
    | CHAR
    | VARCHAR
    | TIMESTAMP
    ;

complexType
    : structType
    | arrayType
    | mapType
    ;

structType
    : ctname=STRUCT LT_ name COLON_ dataType (COMMENT STRING_)? (COMMA_ name COLON_ dataType (COMMENT STRING_)?)* GT_
    ;

arrayType
    : ctname=ARRAY LT_ dataType GT_
    ;

mapType
    : ctname=MAP LT_ primitiveType COMMA_ (primitiveType | complexType) GT_
    ;

kuduAttributes
    : NOT? NULL
    | ENCODING codec
    | COMPRESSION compAlgorithm
    | DEFAULT constant
    | BLOCK_SIZE number
    ;

codec
    : AUTO_ENCODING
    | PLAIN_ENCODING
    | RLE
    | DICT_ENCODING
    | BIT_SHIFFLE
    | PREFIX_ENCODING
    ;

//常量字面量，非表达式
constant
    : NULL
    | INTEGER_
    | NUMBER_
    | STRING_
    | TRUE
    | FALSE
    ;

compAlgorithm
    : LZ4
    | SNAPPY
    | ZLIB
    ;

partitionSpec
    : complexPartitionSpec
    | simplePartitionSpec
    ;

simplePartitionSpec
    : partitionCol=name
    ;

complexPartitionSpec
    : comparisonExpressionOnPartitionCol
    ;

comparisonExpressionOnPartitionCol
    : name comparisonOperator (constant | bitExpr)        // 基础比较：year >= 2023
    | name BETWEEN low=constant AND high=constant               // 范围：month BETWEEN 1 AND 6
    | LP_ comparisonExpressionOnPartitionCol (COMMA_ comparisonExpressionOnPartitionCol)* RP_             // 括号包裹表达式
    | left=comparisonExpressionOnPartitionCol logicOp=(AND | OR) right=comparisonExpressionOnPartitionCol  // 逻辑组合
    | simplePartitionSpec IN valuesRow
    ;

kuduPartitionSpec
    : constant rangeOperator VALUES rangeOperator constant
    | VALUE EQ_ constant
    ;

rangeOperator
    : LT_ | LTE_ | GT_ | GTE_
    ;

cache_spec
    : CACHED IN STRING_ (WITH REPLICATION '=' INTEGER_)?
    | UNCACHED
    ;

location_spec
    : LOCATION hdfsPathOfDirectory
    ;

kvProperties
    : keyName EQ_ valueName (COMMA_ keyName EQ_ valueName)*
    ;

keyName
    : STRING_
    ;

valueName
    : STRING_
    ;

fileFormat
    : PARQUET
    | TEXTFILE
    | RCFILE
    | SEQUENCEFILE
    | AVRO
    ;


rowFormat
    : DELIMITED
        (FIELDS TERMINATED BY STRING_ (ESCAPED BY STRING_)?)?
        (LINES TERMINATED BY STRING_)?
    ;

alterView
    : ALTER VIEW viewName (LP_ alterViewColumnDefinition (COMMA_ alterViewColumnDefinition)* RP_)? alterViewSuffix
    ;

alterViewColumnDefinition
    : columnName (commentClause)?
    ;

alterViewSuffix
    : alterViewSuffixAsSelect
    | alterViewSuffixRenameTo
    | alterViewSuffixSetOwnerUser
    | alterViewSuffixSetTblProperties
    | alterViewSuffixUnsetTblProperties
    ;
alterViewSuffixAsSelect
    : AS select
    ;
alterViewSuffixRenameTo
    : RENAME TO viewName
    ;
alterViewSuffixSetOwnerUser
    : SET OWNER USER name
    ;
alterViewSuffixSetTblProperties
    : SET tblPropertiesClause
    ;
alterViewSuffixUnsetTblProperties
    : UNSET tblPropertiesClause
    ;




createDatabase
   : CREATE (DATABASE | SCHEMA) ifNotExists? databaseName (COMMENT STRING_)? (LOCATION hdfsPath)?
   ;


createFunction
    : createScalarCppUdf
    | createScalarJavaUdf
    | createAggregateUda
    ;

createScalarCppUdf
    : //To create a persistent scalar C++ UDF with CREATE FUNCTION:
       CREATE FUNCTION ifNotExists? functionName LP_ (argType(COMMA_ argType)*)? RP_
         RETURNS returnType
         LOCATION hdfsPathToDotSo
         SYMBOL EQ_ STRING_
    ;

createScalarJavaUdf
    : //    To create a persistent Java UDF with CREATE FUNCTION:
       CREATE FUNCTION ifNotExists? functionName
         LOCATION hdfsPathToJar
         SYMBOL EQ_ STRING_
    ;

createAggregateUda
    : //   To create a persistent UDA, which must be written in C++, issue a CREATE AGGREGATE FUNCTION statement:

       CREATE AGGREGATE? FUNCTION ifNotExists? functionName LP_ (argType (COMMA_ argType)*)? RP_
         RETURNS returnType
         (INTERMEDIATE typeSpec)?
         LOCATION hdfsPath
         (INIT_FN EQ_ STRING_)?
         UPDATE_FN EQ_ STRING_
         MERGE_FN EQ_ STRING_
         (PREPARE_FN EQ_ STRING_)?
         (CLOSEFN EQ_ STRING_)?
         (SERIALIZE_FN EQ_ STRING_)?
         (FINALIZE_FN EQ_ STRING_)?
    ;

argType
    : dataType
    ;

typeSpec
    : dataType
    ;

createRole
    : CREATE ROLE r=name
    ;

createTable
    : createTableExplicitColumnDefinition
    | createTableAsSelect
    | createTableColumnDefinitionFromFile
    | createInternalKuduTable
    | createExternalKuduTable
    | createKuduTableAsSelect
    ;

//显示列定义，explicitColumnDefinition
createTableExplicitColumnDefinition
    : CREATE (EXTERNAL)? TABLE (ifNotExists)? tableName
            LP_ columnDefinition (COMMA_ columnDefinition)* RP_
            (partitionedByClause)?
            (sortByClause)?
            (commentClause)?
            (rowFormatClause)?
            (serdePropertiesClause)?
            (storedAsClause)?
            (locationClause)?
            (cacheClause)?
            (tblPropertiesClause)?
    ;

columnDefinition
    : (columnName dataType)?
            (constraintSpecification)?
            (commentColumnClause)?
    | (DISABLE | NOVALIDATE | RELY)
    ;

constraintSpecification
    : PRIMARY KEY LP_ columnName (COMMA_ columnName)* RP_ (DISABLE)? (NOVALIDATE)? (RELY)?
      (COMMA_ foreignKeySpecification)*
      | PRIMARY KEY
      | (NOT)? NULL
      | ENCODING codec
      | COMPRESSION algorithm
      | DEFAULT constant
      | BLOCK_SIZE number
    ;

foreignKeySpecification
    : FOREIGN KEY LP_ columnName (COMMA_ columnName)* RP_ REFERENCES tableName LP_ columnName (COMMA_ columnName)* RP_
      (DISABLE)? (NOVALIDATE)? (RELY)?
    ;

commentColumnClause
    : COMMENT STRING_
    ;

partitionedByClause
    : PARTITIONED BY LP_ partitionColumn (COMMA_ partitionColumn)* RP_
    ;
partitionColumn
    : columnName dataType? (commentColumnClause)?
    ;
sortByClause
    : SORT BY (orderings)? LP_ columnName (COMMA_ columnName)* RP_
    ;
orderings
    : LEXICAL | ZORDER
    ;
commentClause
    : COMMENT STRING_
    ;
rowFormatClause
    : ROW FORMAT DELIMITED
      (FIELDS TERMINATED BY STRING_ (ESCAPED BY STRING_)?)?
      (LINES TERMINATED BY STRING_)?
    ;
serdePropertiesClause
    : WITH SERDEPROPERTIES LP_ serdeProperty (',' serdeProperty)* RP_
    ;
serdeProperty
    : key=STRING_ EQ_ value=STRING_
    ;
storedAsClause
    : STORED AS fileFormat
    ;
locationClause
    : LOCATION hdfsPath
    ;
cacheClause
    : CACHED IN poolName=STRING_ (WITH REPLICATION EQ_ INTEGER_)?
    | UNCACHED
    ;
tblPropertiesClause
    : TBLPROPERTIES LP_ tblProperty (',' tblProperty)* RP_
    ;
tblProperty
    : key=STRING_ (EQ_ value=STRING_)?
    ;



// createTableAsSelect。
createTableAsSelect
    : CREATE (EXTERNAL)? TABLE (ifNotExists)? tableName
      (partitionedByClause)?
      (sortByClause)?
      (commentClause)?
      (rowFormatClause)?
      (serdePropertiesClause)?
      (storedAsCTASClause)?
      (locationClause)?
      (cacheClause)?
      (tblPropertiesClause)?
      AS select
    ;
storedAsCTASClause
    : STORED AS ctasFileFormat
    ;
ctasFileFormat
    : PARQUET
    | TEXTFILE
    ;



// 从数据文件推断列定义，columnDefinitionFromFile。
createTableColumnDefinitionFromFile
    : CREATE (EXTERNAL)? TABLE (ifNotExists)? tableName
      LIKE (tableName | PARQUET hdfsPathOfFile)
      (partitionedByClause)?
      (sortByClause)?
      (commentClause)?
      (rowFormatClause)?
      (serdePropertiesClause)?
      (storedAsClause)?
      (locationClause)?
      (cacheClause)?
      (tblPropertiesClause)?
    ;



// 内部 Kudu 表，internalKuduTable
createInternalKuduTable
    : CREATE TABLE (ifNotExists)? tableName
      LP_ kuduColumnDefinition (COMMA_ kuduColumnDefinition)* (primaryKeyClause)? RP_
      (partitionByKuduClause)?
      (commentClause)?
      STORED AS KUDU
      (tblPropertiesClause)?
    ;
kuduColumnDefinition
    : columnName dataType
      (kuduColumnAttribute)*
      (commentColumnClause)?
    ;
kuduColumnAttribute
    : PRIMARY KEY LP_ columnName (COMMA_ columnName)* RP_ (DISABLE)? (NOVALIDATE)? (RELY)?
    | (NOT)? NULL
    | ENCODING codec
    | COMPRESSION algorithm
    | DEFAULT constant
    | BLOCK_SIZE number
    | PRIMARY KEY
    ;
algorithm
    : identifier | STRING_
    ;
primaryKeyClause
    : PRIMARY KEY (LP_ columnName (COMMA_ columnName)* RP_)?
    ;
partitionByKuduClause
    : PARTITION BY (hashClause (COMMA_ hashClause)*)? (COMMA_? rangeClause)?
    ;
hashClause
    : HASH (LP_ pkCol (COMMA_ pkCol)* RP_)? PARTITIONS n=INTEGER_
    ;
pkCol
    : identifier
    ;
rangeClause
    : RANGE (LP_ pkCol (COMMA_ pkCol)* RP_)?
      LP_ rangePartition (COMMA_ rangePartition)* RP_
    ;

rangePartition
    : PARTITION rangePartitionValueComparisonExpr
    ;
rangePartitionValueComparisonExpr
    : constantExpression rangeComparisonOperator VALUES rangeComparisonOperator constantExpression
    | VALUE EQ_ constantExpressionOrTuple
    | constantExpressionOrTupleOrVALUES rangeComparisonOperator constantExpressionOrTupleOrVALUES
    ;
constantExpressionOrTupleOrVALUES
    : constantExpressionOrTuple | VALUES
    ;
rangeComparisonOperator
    : LT_
    | LTE_
    | GT_
    | GTE_
    ;
// 常量表达式
constantExpression
    : primaryExpression (binaryOperator primaryExpression)*
    ;
primaryExpression
    : constant
    | unaryExpression
    | functionCall
    | LP_ constantExpression RP_
    ;
// 一元表达式
unaryExpression
    : unaryOperator primaryExpression
    ;

// 一元操作符
unaryOperator
    : PLUS_
    | MINUS_
    | NOT
    ;



// 二元操作符
binaryOperator
    : PLUS_
    | MINUS_
    | ASTERISK_
    | SLASH_
    | MOD_
    | EQ_
    | NEQ_
    | LT_
    | GT_
    | LTE_
    | GTE_
    | AND
    | OR
    ;


functionCall
    : aggregationFunction | specialFunction | regularFunction | jsonFunction | udfFunction
    ;

aggregationFunction
    : aggregationFunctionName LP_ distinct? (expr (COMMA_ expr)* | ASTERISK_)? collateClause? RP_ overClause?
    ;

specialFunction
    : groupConcatFunction | windowFunction | castFunction | convertFunction | positionFunction | substringFunction | extractFunction
    | charFunction | trimFunction | weightStringFunction | valuesFunction | currentUserFunction
    ;

currentUserFunction
    : CURRENT_USER (LP_ RP_)?
    ;

groupConcatFunction
    : GROUP_CONCAT LP_ distinct? (expr (COMMA_ expr)* | ASTERISK_)? (orderByClause)? (SEPARATOR expr)? RP_
    ;

windowFunction
    : funcName = (ROW_NUMBER | RANK | DENSE_RANK | CUME_DIST | PERCENT_RANK) LP_ RP_ windowingClause
    | funcName = NTILE (simpleExpr) windowingClause
    | funcName = (LEAD | LAG) LP_ expr leadLagInfo? RP_ nullTreatment? windowingClause
    | funcName = (FIRST_VALUE | LAST_VALUE) LP_ expr RP_ nullTreatment? windowingClause
    | funcName = NTH_VALUE LP_ expr COMMA_ simpleExpr RP_ (FROM (FIRST | LAST))? nullTreatment? windowingClause
    ;
windowingClause
    : OVER (windowName=identifier | windowSpecification)
    ;
leadLagInfo
    : COMMA_ (number | QUESTION_) (COMMA_ expr)?
    ;
nullTreatment
    : (RESPECT | IGNORE) NULLS
    ;

castFunction
    : CAST LP_ expr AS castType ARRAY? (FORMAT STRING_)? RP_
    | CAST LP_ expr AT TIME ZONE expr AS DATETIME typeDatetimePrecision? RP_
    ;

castType
    : dataType
    ;

typeDatetimePrecision
    : LP_ number RP_
    ;



charsetName
    : textOrIdentifier | BINARY | DEFAULT
    ;

convertFunction
    : CONVERT LP_ expr COMMA_ castType RP_
    | CONVERT LP_ expr USING charsetName RP_
    ;

positionFunction
    : POSITION LP_ expr IN expr RP_
    ;

substringFunction
    : (SUBSTRING | SUBSTR) LP_ expr FROM number (FOR number)? RP_
    | (SUBSTRING | SUBSTR) LP_ expr COMMA_ number (COMMA_ number)? RP_
    ;

extractFunction
    : EXTRACT LP_ identifier FROM expr RP_
    ;

charFunction
    : CHAR LP_ expr (COMMA_ expr)* (USING charsetName)? RP_
    ;

trimFunction
    : TRIM LP_ ((LEADING | BOTH | TRAILING) expr? FROM)? expr RP_
    | TRIM LP_ (expr FROM)? expr RP_
    ;

valuesFunction
    : VALUES LP_ columnNames RP_
    ;


weightStringFunction
    : WEIGHT_STRING LP_ expr (AS dataType)? levelClause? RP_
    ;
levelClause
    : LEVEL (levelInWeightListElement (COMMA_ levelInWeightListElement)* | number MINUS_ number)
    ;
levelInWeightListElement
    : number direction? REVERSE?
    ;
direction
    : ASC | DESC
    ;
sortOrderForNullValues
    : NULLS (FIRST | LAST)
    ;





regularFunction
    : completeRegularFunction
    | shorthandRegularFunction
    ;

completeRegularFunction
    : regularFunctionName (LP_ (expr (COMMA_ expr)* | ASTERISK_)? RP_)
    ;
regularFunctionName
    : IF | LOCALTIME | LOCALTIMESTAMP | REPLACE | INSERT | INTERVAL | MOD
    | DATABASE | SCHEMA | LEFT | RIGHT | DATE | DAY | GEOMETRYCOLLECTION | REPEAT
    | LINESTRING | MULTILINESTRING | MULTIPOINT | MULTIPOLYGON | POINT | POLYGON
    | TIME | TIMESTAMP | TIMESTAMP_ADD | TIMESTAMP_DIFF | DATE | CURRENT_TIMESTAMP
    | CURRENT_DATE | CURRENT_TIME | UTC_TIMESTAMP | identifier
    ;

shorthandRegularFunction
    : CURRENT_DATE | CURRENT_TIME (LP_ number? RP_)? | CURRENT_TIMESTAMP | LAST_DAY | LOCALTIME | LOCALTIMESTAMP
    ;




jsonFunction
    : columnName (JSON_SEPARATOR | JSON_UNQUOTED_SEPARATOR) path
    | jsonFunctionName LP_ (expr? | expr (COMMA_ expr)*) RP_
    ;
jsonFunctionName
    : JSON_ARRAY | JSON_ARRAY_APPEND |  JSON_ARRAY_INSERT |  JSON_CONTAINS
    | JSON_CONTAINS_PATH | JSON_DEPTH | JSON_EXTRACT | JSON_INSERT | JSON_KEYS | JSON_LENGTH | JSON_MERGE | JSON_MERGE_PATCH
    | JSON_MERGE_PRESERVE | JSON_OBJECT | JSON_OVERLAPS | JSON_PRETTY | JSON_QUOTE | JSON_REMOVE | JSON_REPLACE
    | JSON_SCHEMA_VALID | JSON_SCHEMA_VALIDATION_REPORT | JSON_SEARCH | JSON_SET | JSON_STORAGE_FREE | JSON_STORAGE_SIZE
    | JSON_TABLE | JSON_TYPE | JSON_UNQUOTE | JSON_VALID | JSON_VALUE | MEMBER OF
    ;






overClause
    : OVER (windowSpecification | identifier)
    ;

windowSpecification
    : LP_ identifier? (PARTITION BY expr (COMMA_ expr)*)? orderByClause? frameClause? RP_
    ;

frameClause
    : (ROWS | RANGE) (frameStart | frameBetween)
    ;

frameStart
    : CURRENT ROW | UNBOUNDED PRECEDING | UNBOUNDED FOLLOWING | expr PRECEDING | expr FOLLOWING
    ;

frameEnd
    : frameStart
    ;

frameBetween
    : BETWEEN frameStart AND frameEnd
    ;

udfFunction
    : functionName LP_ (expr? | expr (COMMA_ expr)*) RP_
    ;

aggregationFunctionName
    : MAX | MIN | SUM | COUNT | AVG | BIT_XOR
    ;

distinct
    : DISTINCT
    ;

collateClause
    : COLLATE (collationName | parameterMarker)
    ;

collationName
    : textOrIdentifier | BINARY
    ;

textOrIdentifier
    : identifier | STRING_ | ipAddress
    ;

ipAddress
    : INTEGER_ DOT_ INTEGER_ DOT_ INTEGER_ DOT_ INTEGER_
    ;
parameterMarker
    : QUESTION_
    ;




// 常量表达式或元组
constantExpressionOrTuple
    : constantExpression
    | LP_ constantExpression (',' constantExpression)* RP_
    ;



// 外部 Kudu 表，externalKuduTable
createExternalKuduTable
    : CREATE EXTERNAL TABLE (ifNotExists)? tableName
      (commentClause)?
      STORED AS KUDU
      (tblPropertiesClause)?
    | CREATE EXTERNAL TABLE (ifNotExists)? tableName
      LP_ kuduColumnDefinition (COMMA_ kuduColumnDefinition)* (primaryKeyClause)? RP_
      (partitionByKuduClause)?
      (commentClause)?
      STORED AS KUDU
      (tblPropertiesClause)?
    ;




// Kudu 表的 CREATE TABLE AS SELECT，kuduCreateTableAsSelect
createKuduTableAsSelect
    : CREATE TABLE (ifNotExists)? tableName
      (primaryKeyClause)?
      (partitionByKuduClause)?
      (commentClause)?
      STORED AS KUDU
      (tblPropertiesClause)?
      AS select
    ;




dataType
    : simpleDataType
    | complexType
    ;

simpleDataType
    : dtname=BIGINT
    | dtname=BOOLEAN
    | dtname=CHAR (LP_ INTEGER_ RP_)?
    | dtname=DATE
    | dtname=DECIMAL (LP_ prec=INTEGER_ (COMMA_ scale=INTEGER_)? RP_)?
    | dtname=DOUBLE
    | dtname=FLOAT
    | dtname=INT
    | dtname=REAL
    | dtname=SMALLINT
    | dtname=STRING
    | dtname=TIMESTAMP
    | dtname=TINYINT
    | dtname=VARCHAR(LP_ maxLength=INTEGER_ RP_)
    ;



createView
    : CREATE VIEW ifNotExists? viewName
          (LP_ columnName (COMMENT colComment=STRING_)? (',' columnName (COMMENT colComment=STRING_)?)* RP_ )?
          (COMMENT viewComment=STRING_)?
          (tblPropertiesClause)?
        AS select
    ;


dropDatabase
    : DROP (DATABASE | SCHEMA) ifExists? databaseName (RESTRICT | CASCADE)?
    ;

dropFunction
    : dropCppUdfsAndUdas
    | dropJavaUdfs
    ;
dropCppUdfsAndUdas
    : DROP AGGREGATE? FUNCTION ifExists? functionName LP_ dataType (COMMA_ dataType)* RP_
    ;
dropJavaUdfs
    : DROP FUNCTION ifExists? functionName
    ;



dropRole
    : DROP ROLE r=name
    ;

dropStats
    : DROP ( STATS tableName | INCREMENTAL STATS tableName PARTITION partitionSpec)
    ;

dropTable
    : DROP TABLE ifExists? tableName PURGE?
    ;

dropView
    : DROP VIEW ifExists? viewName
    ;


path
    : STRING_
    ;



comment
    : COMMENT ON ( DATABASE databaseName
                 | TABLE tableName
                 | COLUMN columnName
                 ) commentContent
    ;

commentContent
    : IS (STRING_ | NULL)
    ;

computeStats
    : COMPUTE STATS tableName  ( LP_ columnNames RP_ )? (TABLESAMPLE SYSTEM LP_ percentage RP_ (REPEATABLE LP_ seed RP_)?)?
    | COMPUTE INCREMENTAL STATS tableName (PARTITION partitionSpec)? ( LP_ columnNames RP_ )?
    ;


describe
    : (DESCRIBE | DESC) DATABASE? (FORMATTED | EXTENDED)? objectName
    ;

objectName
    : (owner DOT_)? name (DOT_ name)*
    | databaseName
    | STRING_
    ;

explain
    : EXPLAIN (select | ctas | insert)
    ;


ctas
    : createTableAsSelect
    | createKuduTableAsSelect
    ;

grant
    : grantRoleToGroup
    | grantPrivilegeOnObjectToPrincipal
    ;
grantRoleToGroup
    : GRANT ROLE roleName TO GROUP groupName
    ;
grantPrivilegeOnObjectToPrincipal
    : GRANT privilege ON grantObject grantToSuffix (withGrantOption)?
    ;
grantToSuffix
    : TO USER username
    | TO GROUP groupName
    | TO ROLE roleName
    ;

withGrantOption
    : WITH GRANT OPTION
    ;
grantObject
    : SERVER name?
    | URI STRING_
    | DATABASE databaseName
    | TABLE tableName
    ;

invalidateMetadata
    : INVALIDATE METADATA (tableName)?
    ;

loadData
    : LOAD DATA INPATH hdfsPath OVERWRITE? INTO TABLE tableName
        (loadDataPartitionClause)?
    ;
loadDataPartitionClause
    : PARTITION LP_ partitionSpec (COMMA_ partitionSpec)* RP_
    ;


merge
    : MERGE (hint)? mergeIntoClause usingClause whenClauses
    ;
mergeIntoClause
    : INTO (hint)? (tableName | viewName | select) (AS? alias)?
    ;
usingClause
    : USING (hint)? ((tableName | viewName) | select) (AS? alias)? ON (LP_ expr RP_ | expr)
    ;
whenClauses
    : (whenMatchedThenClause)* whenNotMatchedClause?
    ;
whenNotMatchedClause
    : WHEN NOT MATCHED (AND expr)? THEN INSERT columnParenthesesNames? originValues
    ;

whenMatchedThenClause
    : WHEN MATCHED (AND expr)? THEN mergeUpdateOrDelete
    ;

columnParenthesesNames
    : LP_ columnNames RP_
    ;
mergeUpdateOrDelete
    : UPDATE setAssignmentsClause
    | DELETE
    ;


refresh
    : REFRESH tableName (generalPartitionClause)?
    ;
    //通用的分区指定表达式
generalPartitionClause
    : PARTITION LP_ partitionSpec (COMMA_ partitionSpec)* RP_
    ;

refreshAuthorization
    : REFRESH AUTHORIZATION
    ;

refreshFunctions
    : REFRESH FUNCTIONS databaseName
    ;

revoke
    : revokeRoleFromGroup
    | revokePrivilegeOnObjectFromPrincipal
    ;
revokeRoleFromGroup
    : REVOKE ROLE roleName FROM GROUP groupName
    ;
revokePrivilegeOnObjectFromPrincipal
    : REVOKE privilege ON grantObject
        revokeFromSuffix
    | REVOKE (GRANT OPTION FOR)? privilege ON grantObject
        revokeFromRoleSuffix
    ;
revokeFromSuffix
    : FROM USER username
    | FROM GROUP groupName
    ;
revokeFromRoleSuffix
    : FROM (ROLE)? roleName
    ;
privilege
    : ALL
    | ALTER
    | CREATE
    | DROP
    | INSERT
    | REFRESH
    | SELECT (LP_ columnNames RP_)?
    ;



set
    : onlySet
    | setAll
    | setQueryOption
    ;
onlySet
    : SET
    ;
setAll
    : SET ALL
    ;
setQueryOption
    : SET queryOption EQ_ queryOptionValue
    ;
queryOption
    : ABORT_ON_ERROR
    | ALLOW_ERASURE_CODED_FILES
    | ALLOW_UNSUPPORTED_FORMATS
    | APPX_COUNT_DISTINCT
    | BATCH_SIZE
    | BROADCAST_BYTES_LIMIT
    | BUFFER_POOL_LIMIT
    | COMPRESSION_CODEC
    | COMPUTE_STATS_MIN_SAMPLE_SIZE
    | DEBUG_ACTION
    | DECIMAL_V2
    | DEFAULT_FILE_FORMAT
    | DEFAULT_HINTS_INSERT_STATEMENT
    | DEFAULT_JOIN_DISTRIBUTION_MODE
    | DEFAULT_SPILLABLE_BUFFER_SIZE
    | DEFAULT_TRANSACTIONAL_TYPE
    | DELETE_STATS_IN_TRUNCATE
    | DISABLE_CODEGEN
    | DISABLE_CODEGEN_ROWS_THRESHOLD
    | DISABLE_HBASE_NUM_ROWS_ESTIMATE
    | DISABLE_ROW_RUNTIME_FILTERING
    | DISABLE_STREAMING_PREAGGREGATIONS
    | DISABLE_UNSAFE_SPILLS
    | ENABLE_EXPR_REWRITES
    | EXEC_SINGLE_NODE_ROWS_THRESHOLD
    | EXEC_TIME_LIMIT_S
    | EXPLAIN_LEVEL
    | FETCH_ROWS_TIMEOUT_MS
    | JOIN_ROWS_PRODUCED_LIMIT
    | HBASE_CACHE_BLOCKS
    | HBASE_CACHING
    | IDLE_SESSION_TIMEOUT
    | KUDU_READ_MODE
    | LIVE_PROGRESS
    | LIVE_SUMMARY
    | MAX_ERRORS
    | MAX_MEM_ESTIMATE_FOR_ADMISSION
    | MAX_RESULT_SPOOLING_MEM
    | MAX_ROW_SIZE
    | MAX_SCAN_RANGE_LENGTH
    | MAX_SPILLED_RESULT_SPOOLING_MEM
    | MEM_LIMIT
    | MIN_SPILLABLE_BUFFER_SIZE
    | MT_DOP
    | NUM_NODES
    | NUM_ROWS_PRODUCED_LIMIT
    | NUM_SCANNER_THREADS
    | OPTIMIZE_PARTITION_KEY_SCANS
    | PARQUET_COMPRESSION_CODEC
    | PARQUET_ANNOTATE_STRINGS_UTF8
    | PARQUET_ARRAY_RESOLUTION
    | PARQUET_DICTIONARY_FILTERING
    | PARQUET_FALLBACK_SCHEMA_RESOLUTION
    | PARQUET_FILE_SIZE
    | PARQUET_OBJECT_STORE_SPLIT_SIZE
    | PARQUET_PAGE_ROW_COUNT_LIMIT
    | PARQUET_READ_STATISTICS
    | PARQUET_READ_PAGE_INDEX
    | PARQUET_WRITE_PAGE_INDEX
    | PREFETCH_MODE
    | QUERY_TIMEOUT_S
    | REFRESH_UPDATED_HMS_PARTITIONS
    | REPLICA_PREFERENCE
    | REQUEST_POOL
    | RESOURCE_TRACE_RATIO
    | RETRY_FAILED_QUERIES
    | RUNTIME_BLOOM_FILTER_SIZE
    | RUNTIME_FILTER_MAX_SIZE
    | RUNTIME_FILTER_MIN_SIZE
    | RUNTIME_FILTER_MODE
    | RUNTIME_FILTER_WAIT_TIME_MS
    | S3_SKIP_INSERT_STAGING
    | SCAN_BYTES_LIMIT
    | SCHEDULE_RANDOM_REPLICA
    | SCRATCH_LIMIT
    | SHUFFLE_DISTINCT_EXPRS
    | SPOOL_QUERY_RESULTS
    | SUPPORT_START_OVER
    | SYNC_DDL
    | THREAD_RESERVATION_AGGREGATE_LIMIT
    | THREAD_RESERVATION_LIMIT
    | TIMEZONE
    | TOPN_BYTES_LIMIT
    | UTF8_MODE
    | EXPAND_COMPLEX_TYPES
    ;

queryOptionValue
    : literals
    ;

show
    : showDatabases
    | showTables
    | showFunctions
    | showCreate
    | showStats
    | showPartitions
    | showFiles
    | showRoles
    | showGrant
    ;


showDatabases
    : SHOW (DATABASES | SCHEMAS) showLikeClause?
    ;
showTables
    : SHOW TABLES (IN databaseName)? showLikeClause?
    ;
showFunctions
    : SHOW (AGGREGATE | ANALYTIC)? FUNCTIONS (IN databaseName)? showLikeClause?
    ;
showCreate
    : SHOW CREATE TABLE tableName
    | SHOW CREATE VIEW viewName
    ;
showStats
    : SHOW TABLE STATS tableName
    | SHOW COLUMN STATS tableName
    ;
showPartitions
    : SHOW (RANGE)? PARTITIONS tableName generalPartitionClause?
    ;
showFiles
    : SHOW FILES IN tableName generalPartitionClause?
    ;
showRoles
    : SHOW ROLES
    | SHOW CURRENT ROLES
    | SHOW ROLE GRANT GROUP groupName
    ;
showGrant
    : showGrantUser
    | showGrantRole
    | showGrantGroup
    ;
showGrantUser
    : SHOW GRANT USER username showOnClause?
    ;
showGrantRole
    : SHOW GRANT ROLE roleName showOnClause?
    ;
showGrantGroup
    : SHOW GRANT GROUP groupName showOnClause
    ;

showLikeClause
    : (LIKE)? p=stringLiterals
    ;

showOnClause
    : ON SERVER
    | ON DATABASE databaseName
    | ON TABLE tableName
    | ON URI uri
    | ON COLUMN columnName
    ;



uri
    : STRING_
    ;

shutdown
    : COLON_ SHUTDOWN LP_ RP_
    | COLON_ SHUTDOWN LP_ shutdownArguments RP_
    ;
shutdownArguments
    : hostName (COLON_ portNumber)?
    | deadline
    | hostName (COLON_ portNumber)? COMMA_ deadline
    ;

deadline
    : constantExpression
    ;

portNumber
    : INTEGER_
    ;

percentage
    : INTEGER_
    ;

seed
    : INTEGER_
    | MINUS_ INTEGER_
    ;

use
    : USE databaseName
    ;

ifNotExists
    : IF NOT EXISTS
    ;

ifExists
    : IF EXISTS
    ;


columnName
    : (name DOT_)? (name DOT_)? identifier
    ;


viewName
    : (owner DOT_)? name
    ;


tableName
    : (owner DOT_)? name
    ;

owner
    : identifier
    ;

name
    : identifier
    ;

hostName
    : STRING_
    ;

databaseName
    : identifier
    ;
functionName
    : (owner DOT_)? identifier
    ;


mapKeyValueSymbol
    : LBT_ literals RBT_
    ;


hdfsPath
    : STRING_
    ;

hdfsPathOfFile
    : STRING_
    ;

hdfsPathOfDirectory
    : STRING_
    ;

hdfsPathToDotSo
    : STRING_
    ;

hdfsPathToJar
    : STRING_
    ;

dirPath
    : STRING_
    ;

filePath
    : STRING_
    ;
username
    : identifier | STRING_
    ;
groupName
    : identifier | STRING_
    ;
roleName
    : identifier | STRING_
    ;
returnType
    : dataType
    ;
hint
    : BLOCK_HINT | INLINE_HINT
    ;
nCharText : N SINGLE_QUOTED_TEXT;


identifier
    : IDENTIFIER_ | unreservedWord | DOUBLE_QUOTED_TEXT | I_CURSOR | QUESTION_ | BQUOTA_STRING
    ;
unreservedWord
    : ABORT
    | ACTIVATE
    | ACTIVE
    | ADD
    | ADMIN
    | AFTER
    | ALLOC_FRACTION
    | ANALYZE
    | ARCHIVE
    | ASC
    | AST
    | AT
    | AUTOCOMMIT
    | BATCH
    | BEFORE
    | BUCKET
    | BUCKETS
    | CACHE
    | CASCADE
    | CBO
    | CHANGE
    | CHECK
    | CLUSTER
    | CLUSTERED
    | CLUSTERSTATUS
    | COLLECTION
    | COLUMNS
    | COMMENT
    | COMPACT
    | COMPACTIONS
    | COMPUTE
    | CONCATENATE
    | CONTINUE
    | COST
    | CRON
    | DATA
    | DATABASES
    | DATETIME
    | DAY
    | DAYOFWEEK
    | DBPROPERTIES
    | DCPROPERTIES
    | DEBUG
    | DEFAULT
    | DEFERRED
    | DEFINED
    | DELIMITED
    | DEPENDENCY
    | DESC
    | DETAIL
    | DIRECTORIES
    | DIRECTORY
    | DISABLE
    | DISTRIBUTE
    | DISTRIBUTED
    | DO
    | DOW
    | DUMP
    | ELEM_TYPE
    | ENABLE
    | ENFORCED
    | ESCAPED
    | EVERY
    | EXCLUSIVE
    | EXECUTE
    | EXECUTED
    | EXPIRE_SNAPSHOTS
    | EXPLAIN
    | EXPORT
    | EXPRESSION
    | FIELDS
    | FILE
    | FILEFORMAT
    | FIRST
    | FORMAT
    | FORMATTED
    | FUNCTIONS
    | HOLD_DDLTIME
    | HOUR
    | IDXPROPERTIES
    | IGNORE
    | INDEX
    | INDEXES
    | INPATH
    | INPUTDRIVER
    | INPUTFORMAT
    | ISOLATION
    | ITEMS
    | JAR
    | JOINCOST
    | KEY
    | KEYS
    | KEY_TYPE
    | KILL
    | LAST
    | LEVEL
    | LIMIT
    | LINES
    | LOAD
    | LOCATION
    | LOCK
    | LOCKS
    | LOGICAL
    | LONG
    | MANAGED
    | MANAGEDLOCATION
    | MANAGEMENT
    | MAPJOIN
    | MAPPING
    | MATCHED
    | MATERIALIZED
    | METADATA
    | MINUTE
    | MONTH
    | MOVE
    | MSCK
    | NORELY
    | NOSCAN
    | NOVALIDATE
    | NO_DROP
    | NULLS
    | OFFLINE
    | OFFSET
    | OPERATOR
    | OPTION
    | OUTPUTDRIVER
    | OUTPUTFORMAT
    | OVERWRITE
    | OWNER
    | PARTITIONED
    | PARTITIONS
    | PATH
    | PLAN
    | PLANS
    | PLUS
    | POOL
    | PRINCIPALS
    | PROTECTION
    | PURGE
    | QUARTER
    | QUERY
    | QUERY_PARALLELISM
    | READ
    | READONLY
    | REBUILD
    | RECORDREADER
    | RECORDWRITER
    | RELOAD
    | RELY
    | REMOTE
    | RENAME
    | REOPTIMIZATION
    | REPAIR
    | REPL
    | REPLACE
    | REPLICATION
    | RESOURCE
    | RESPECT
    | RESTRICT
    | REWRITE
    | ROLE
    | ROLES
    | SCHEDULED
    | SCHEDULING_POLICY
    | SCHEMA
    | SCHEMAS
    | SECOND
    | SEMI
    | SERDE
    | SERDEPROPERTIES
    | SERVER
    | SETS
    | SET_CURRENT_SNAPSHOT
    | SHARED
    | SHOW
    | SHOW_DATABASE
    | SKEWED
    | SNAPSHOT
    | SORT
    | SORTED
    | SPEC
    | SSL
    | STATISTICS
    | STATUS
    | STORED
    | STREAMTABLE
    | STRING
    | STRUCT
    | SUMMARY
    | SYSTEM_TIME
    | SYSTEM_VERSION
    | TABLES
    | TBLPROPERTIES
    | TEMPORARY
    | TERMINATED
    | TIMESTAMP
    | TIMESTAMPTZ
    | TINYINT
    | TOUCH
    | TRANSACTION
    | TRANSACTIONAL
    | TRANSACTIONS
    | TRIM
    | TYPE
    | UNARCHIVE
    | UNDO
    | UNIONTYPE
    | UNKNOWN
    | UNLOCK
    | UNMANAGED
    | UNSET
    | UNSIGNED
    | URI
    | URL
    | USE
    | UTC
    | UTCTIMESTAMP
    | VALIDATE
    | VALUE_TYPE
    | VECTORIZATION
    | VIEW
    | VIEWS
    | WAIT
    | WEEK
    | WHILE
    | WITHIN
    | WORK
    | WORKLOAD
    | WRITE
    | YEAR
    | ZONE
    ;


