-- fro update
SELECT * FROM x WHERE a=1 FOR UPDATE WAIT 1;
SELECT * FROM y WHERE b=1 FOR UPDATE NOWAIT;
SELECT * FROM x WHERE a=1 FOR UPDATE OF x.c;
SELECT * FROM x, y WHERE a=1 FOR UPDATE;
SELECT * FROM x, y WHERE x.a=1 FOR UPDATE OF y.b;
SELECT * FROM x, (SELECT * FROM y) WHERE a=1 FOR UPDATE OF y.b;
-- Examples with TIME TRAVEL
SELECT last_commit_id FROM m_history_index_last_commit_id WHERE session_id = current_connection;
SELECT * FROM x AS OF COMMIT ID 30;
SELECT * FROM x AS OF COMMIT ID 20;
SELECT * FROM x AS OF COMMIT ID 10;
SELECT commit_time FROM sys.transaction_history WHERE commit_id = 10;
SELECT commit_time FROM sys.transaction_history WHERE commit_id = 20;
SELECT commit_time FROM sys.transaction_history WHERE commit_id = 30;
SELECT * FROM x AS OF UTCTIMESTAMP '2012-01-02 02:00:00';
SELECT * FROM x AS OF UTCTIMESTAMP '2012-01-03 03:00:00';
SELECT * FROM x AS OF UTCTIMESTAMP '2012-01-04 04:00:00';
-- Examples with WITH
WITH q1 AS (SELECT * FROM t1) SELECT * FROM q1;
WITH q1(c1, c2) AS (SELECT * FROM t1) SELECT * FROM q1;
WITH q1(c1) AS (SELECT a+b FROM t1), q2(c2) AS (SELECT MAX(c1) FROM q1) SELECT c1 FROM q1 UNION ALL SELECT c2 FROM q2;
-- Example with FOR JSON
SELECT id, name from JTable for JSON;
SELECT id, name from JTable for JSON ('format'='yes');
SELECT id, name from JTable for JSON ('format'='yes', 'omitnull'='no', 'arraywrap'='no');
-- Example with FOR XML
SELECT C1, C2 FROM T1 FOR XML ('schemaloc'='http://thiscompany.com/schemalib', 'targetns'='http://thiscompany.com/samples', 'nullstyle'='omit');
-- Examples with GROUP BY GROUPING SETS
SELECT customer, year, product, SUM(sales)
FROM t1
GROUP BY GROUPING SETS
(
    (customer, year),
        (customer, product)
);
SELECT customer, year, NULL, SUM(sales)
FROM t1
GROUP BY customer, year
UNION ALL
SELECT customer, NULL, product, SUM(sales)
FROM t1
GROUP BY customer, product;

SELECT customer, year, SUM(sales)
FROM t1
GROUP BY ROLLUP(customer, year);
SELECT customer, year, SUM(sales)
FROM t1
GROUP BY GROUPING SETS
(
    (customer, year),
        (customer)
)
UNION ALL
SELECT NULL, NULL, SUM(sales)
FROM t1;

SELECT customer, year, SUM(sales)
FROM t1
GROUP BY CUBE(customer, year);
SELECT customer, year, SUM(sales)
FROM t1
GROUP BY GROUPING SETS
(
    (customer, year),
        (customer),
        (year)
)
UNION ALL
SELECT NULL, NULL, SUM(sales)
FROM t1;

SELECT customer, year, product, SUM(sales)
FROM t1
GROUP BY GROUPING SETS BEST 1
     (
      (customer, year),
      (product)
     );

SELECT customer, year, product, SUM(sales)
FROM t1
GROUP BY GROUPING SETS LIMIT 2
    (
    (customer, year),
    (product)
    );

SELECT customer, year, product, SUM(sales)
FROM t1
GROUP BY GROUPING SETS LIMIT 2 WITH SUBTOTAL
    (
    (customer, year),
    (product)
    );

SELECT customer, year, product, SUM(sales)
FROM t1
GROUP BY GROUPING SETS LIMIT 2 WITH BALANCE
    (
    (customer, year),
    (product)
    );

SELECT customer, year, product, SUM(sales)
FROM t1
GROUP BY GROUPING SETS LIMIT 2 WITH TOTAL
    (
    (customer, year),
    (product)
    );

SELECT customer, year, product, SUM(sales), TEXT_FILTER(customer), TEXT_FILTER(product)
FROM t1
GROUP BY GROUPING SETS TEXT_FILTER '*2'
     (
      (customer, year),
      (product)
     );

SELECT customer, year, product, SUM(sales), TEXT_FILTER(customer), TEXT_FILTER(product)
FROM t1
GROUP BY GROUPING SETS TEXT_FILTER '*2' FILL UP
     (
      (customer, year),
      (product)
     );

SELECT customer, year, product, SUM(sales), TEXT_FILTER(customer), TEXT_FILTER(product)
FROM t1
GROUP BY GROUPING SETS TEXT_FILTER '*2' FILL UP SORT MATCHES TO TOP
     (
      (customer, year),
      (product)
     );

SELECT customer, year, product, SUM(sales)
FROM t1
GROUP BY GROUPING SETS STRUCTURED RESULT
     (
      (customer, year),
      (product)
     );

SELECT * FROM "#GN1";
SELECT * FROM "#GN2";

SELECT customer, year, product, SUM(sales)
FROM t1
GROUP BY GROUPING SETS STRUCTURED RESULT WITH OVERVIEW
    (
    (customer, year),
    (product)
    );

SELECT customer, year, product, SUM(sales)
FROM t1
GROUP BY GROUPING SETS STRUCTURED RESULT WITH OVERVIEW PREFIX '#MYTAB'
    (
    (customer, year),
    (product)
    );

SELECT customer, year, product, SUM(sales)
FROM t1
GROUP BY GROUPING SETS MULTIPLE RESULTSETS
     (
      (customer, year),
      (product)
     );
-- Example with TABLESAMPLE
SELECT COUNT(*), AVG(salary)
FROM employee TABLESAMPLE SYSTEM (1)
WHERE employee.type = 'manager';
-- Examples with EXPRESSION MACROS
SELECT EXPRESSION_MACRO(avgA) FROM v1;
SELECT EXPRESSION_MACRO(sum_x2), EXPRESSION_MACRO(count_y2) FROM v2;
-- Example with HINTS
SELECT T2.* FROM ( SELECT MAX(COL) FROM T1 GROUP BY COL WITH HINT( NO_USE_OLAP_PLAN )) T2 WITH HINT( USE_OLAP_PLAN );
SELECT T2.* FROM ( SELECT MAX(COL) FROM T1 GROUP BY COL WITH HINT( NO_USE_OLAP_PLAN )) T2;
SELECT * FROM T1 WITH HINT( IGNORE_PLAN_CACHE );
SELECT * FROM T1 WITH HINT( USE_REMOTE_CACHE );
SELECT * FROM T1 WITH HINT( ROUTE_TO(1));
SELECT * FROM T1 WITH HINT( NO_ROUTE_TO(2,3));
SELECT * FROM T1 WITH HINT( ROUTE_BY(T2));
SELECT * FROM T1 WITH HINT( ROUTE_BY_CARDINALITY(T1,T2,T3));
SELECT * FROM T1 WITH HINT( NO_ROUTE_TO(1), ROUTE_TO(1));
SELECT * FROM T1 WITH HINT( DATA_TRANSFER_COST(0) );
SELECT * FROM T1 WITH HINT( USE_OLAP_PLAN );
SELECT * FROM T1 WITH HINT( NO_USE_OLAP_PLAN );
SELECT * FROM T1 WITH HINT( INDEX_SEARCH );
SELECT * FROM T1 WITH HINT( NO_INDEX_SEARCH );
SELECT * FROM T1, T2 WITH HINT( INDEX_JOIN );
SELECT * FROM T1, T2 WITH HINT( NO_INDEX_JOIN );
SELECT * FROM T1, T2 WITH HINT( HASH_JOIN );
SELECT * FROM T1, T2 WITH HINT( NO_HASH_JOIN );
SELECT * FROM T1, T2 WITH HINT( MIXED_INVERTED_INDEX_JOIN );
SELECT * FROM T1, T2 WITH HINT( NO_MIXED_INVERTED_INDEX_JOIN );
SELECT * FROM T1, T2 WITH HINT( OPTIMIZE_METAMODEL );
SELECT * * FROM T1, T2 WITH HINT( NO_OPTIMIZE_METAMODEL );
SELECT * FROM T1 WITH HINT( SUBPLAN_SHARING);
SELECT * FROM T1 WITH HINT( NO_SUBPLAN_SHARING );
-- Examples of selecting from arrays
SELECT ARRAY ( 1, 2, 3, 4 ) FROM DUMMY;
SELECT ARRAY( SELECT C1 FROM T0 ) FROM DUMMY;
-- Example with PARTITION
SELECT * FROM T0 PARTITION (1, 3, 4);
-- Example with FOR SHARE LOCK
SELECT A FROM X FOR SHARE LOCK;
SELECT * FROM TAB1 FOR SHARE LOCK OF A;
SELECT * FROM TAB2 FOR SHARE LOCK OF A, C;
-- Examples with ASSOCIATIONS
SELECT * FROM EMPLOYEES:CITY.STATE;
SELECT * FROM EMPLOYEES[ID<5]:CITY.STATE[ID<2];
-- Example with CASE JOIN
SELECT t1.branch, case_join.c1, case_join.c2
FROM t1 LEFT OUTER MANY TO ONE CASE JOIN
      WHEN t1.branch = 1 THEN RETURN (c1, c2) FROM t2 ON t1.id = t2.id
    WHEN t1.branch = 2 THEN RETURN (c1, c2) FROM t3 ON t1.id = t3.id
    END AS case_join;
SELECT * FROM test_rowcount LIMIT 1 TOTAL ROWCOUNT;
SELECT SESSION_CONTEXT('TOTAL_ROWCOUNT') FROM DUMMY;