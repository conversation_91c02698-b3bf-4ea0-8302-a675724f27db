-- TRUNCATE
    TRUNCATE TABLE A;
    TRUNCATE TABLE T1 PARTITION (2,3);

-- alter index
    ALTER FULLTEXT INDEX i2 PHRASE INDEX RATIO 0.3 FUZZY SEARCH INDEX ON;
    ALTER FULLTEXT INDEX i2 SUSPEND QUEUE;
    ALTER FULLTEXT INDEX i2 FLUSH QUEUE;
    ALTER INDEX INDEX_1 REBUILD;
    ALTER INDEX INDEX_1 UNIQUE INVERTED HASH;
    ALTER INDEX myIDX PAGE LOADABLE;
    ALTER INDEX MYINDEX UNIQUE INVERTED VALUE ONLINE;

-- alter sequence
    ALTER SEQUENCE seq RESTART WITH 2;
    ALTER SEQUENCE seq MAXVALUE 100 NO MINVALUE;
    ALTER SEQUENCE seq INCREMENT BY 3 NO CYCLE;
    ALTER SEQUENCE s1 RESET BY SELECT MAX(a) FROM B;

-- alter STATISTICS
    ALTER STATISTICS on MYSYSTEM.T(X) TYPE HISTOGRAM SET REFRESH TYPE AUTO;
    ALTER STATISTICS MYSIMPLESTA ADD VALID FOR DATA DEPENDENCY;
    ALTER STATISTICS on "REMOTE2"."SYSTEM"."A1" TYPE TOPK SET BUCKETS 10 NO INITIAL REFRESH;
    ALTER STATISTICS on MYSYSTEM.REMOTE2_A1 TYPE TOPK SET BUCKETS 10;

-- alter scheduler
    ALTER SCHEDULER JOB schedule1 DISABLE;
    ALTER SCHEDULER JOB schedule1 CRON '* * */1 * 12 34 56' ENABLE PARAMETERS DAYS=7,abc=2.6;

/* alter table start */
-- partition
    ALTER TABLE myTable3 ADD RANGE (C1) (PARTITION '0' <= VALUES < '10', PARTITION OTHERS PERSISTENT MEMORY ON DEFERRED);
    ALTER TABLE T1 MOVE PARTITION 1,2 TO 'MyServer1:34240', PARTITION 3,4 TO 'MyServer2' ONLINE;
    ALTER TABLE T1 MOVE PARTITION 1,2 TO MyServer1:34240;
    ALTER TABLE T1 PARTITION BY RANGE (a) ( PARTITION 10 <= VALUES < 20);
    ALTER TABLE T1 PARTITION BY
        RANGE (a) ( PARTITION 10 <= VALUES < 20),
       RANGE (b) ( PARTITION 100 <= VALUES < 150);
    ALTER TABLE T1 PARTITION BY
        RANGE (a) ( PARTITION 10 <= VALUES < 15, PARTITION 15 <= VALUES < 20),
       RANGE (b) ( PARTITION 100 <= VALUES < 150);
    ALTER TABLE T1 ADD PARTITION (b) USING DEFAULT STORAGE (150 <= VALUES < 200);
    ALTER TABLE T1 MOVE PARTITION 1 TO 'myhost:30201';
    ALTER TABLE T2 MOVE PARTITION 1,2 TO 'myhost2:34240', PARTITION 3,4 TO 'myhost3:34242' ONLINE;
    ALTER TABLE T1 DROP PARTITION (a) 10 <= VALUES < 15;
    ALTER TABLE T1 MERGE PARTITIONS;
    ALTER TABLE T1 PARTITION BY RANGE (a) (PARTITION 15 <= VALUES < 20 NUMA NODE ('3') DEFERRED);
    ALTER TABLE T1 ALTER PARTITION 1 NUMA NODE ('4') IMMEDIATE;
    ALTER TABLE T2 PARTITION BY RANGE (a) (PARTITION VALUE = 1, PARTITION OTHERS);
    ALTER TABLE T2 ADD PARTITION (a) 2 <= VALUES < 10;
    ALTER TABLE T3 PARTITION BY RANGE (a) (PARTITION VALUE = 1, PARTITION OTHERS),
    RANGE (b) (PARTITION 2 <= VALUES < 10);
    ALTER TABLE T4 PARTITION BY RANGE (A) (PARTITION 10 <= values < 20, PARTITION OTHERS);
    ALTER TABLE T5 PARTITION BY HASH (A) PARTITIONS 2;
    ALTER TABLE T6 PARTITION BY RANGE(A) (PARTITION VALUE = 1, PARTITION 10 <= VALUES < 20, PARTITION OTHERS);
    ALTER TABLE A1 PARTITION OTHERS DYNAMIC;
    ALTER TABLE A1 ADD PARTITION FROM OTHERS;
    ALTER TABLE A1 DROP EMPTY PARTITIONS;
    ALTER TABLE A1 PARTITION OTHERS DYNAMIC THRESHOLD 2;
    ALTER TABLE A1 PARTITION OTHERS NO DYNAMIC;
    ALTER TABLE T1 PARTITION BY RANGE (a)
        ((PARTITION 10 <= VALUES < 20 INSERT OFF, PARTITION 20 <= VALUES < 30 INSERT OFF)
        SUBPARTITION BY RANGE (b) (PARTITION 0 <= VALUES < 10, PARTITION 10 <= VALUES < 100, PARTITION OTHERS DYNAMIC THRESHOLD 2),
        (PARTITION VALUES = 40)
           SUBPARTITION BY RANGE(B) (PARTITION 0 <= VALUES < 10, PARTITION OTHERS DYNAMIC THRESHOLD 2),
        (PARTITION OTHERS));
    ALTER TABLE T2 PARTITION BY RANGE (a) ((PARTITION 10 <= VALUES < 20 INSERT OFF, PARTITION 20 <= VALUES < 30 INSERT OFF)
        SUBPARTITION BY HASH (b) PARTITIONS 2);
    ALTER TABLE T1 ADD PARTITION RANGE (a) ((PARTITION 41 <= VALUES < 50 )
       SUBPARTITION by RANGE (b) (PARTITION 0 <= VALUES < 10 ));
    ALTER TABLE T1 PARTITION BY RANGE (a)
        ((PARTITION 5 <= VALUES < 10, PARTITION 10 <= VALUES < 20 INSERT OFF, PARTITION 20 <= VALUES < 30 INSERT OFF)
        SUBPARTITION BY RANGE (b) (PARTITION 0 <= VALUES < 10, PARTITION 10 <= VALUES < 100, PARTITION OTHERS DYNAMIC THRESHOLD 2),
        (PARTITION VALUES = 40) SUBPARTITION BY RANGE(B) (PARTITION 0 <= VALUES < 10, PARTITION OTHERS DYNAMIC THRESHOLD 2),
        (PARTITION 41 <= VALUES < 50 ) SUBPARTITION by RANGE (b) (PARTITION 0 <= VALUES < 10),
        (PARTITION OTHERS));
    ALTER TABLE T1 MOVE PARTITION 1 TO 'myhost:30203';
    ALTER TABLE T1 MOVE RANGE (a) ((PARTITION 5 <= VALUES < 10) SUBPARTITION BY RANGE (b) (PARTITION 0 <= VALUES < 10)) TO 'myhost:30203';
    ALTER TABLE T2 MOVE SUBPARTITIONS OF RANGE (a) ((PARTITION 10 <= VALUES < 20)) to ('myhost1:30203', 'myhost2:30203');
    ALTER TABLE T1 DROP PARTITION RANGE (a) (( PARTITION 10 <= VALUES < 15 ));
    ALTER TABLE T1 ALTER PARTITION RANGE (a) ((PARTITION 10 <= VALUES < 20)) DROP PARTITION RANGE (b) ((PARTITION 10 <= VALUES < 100));
    ALTER TABLE T1 MERGE PARTITIONS AT LOCATION 'myhost3:30203';
    ALTER TABLE T1 ALTER PARTITION 2 SET GROUP NAME "GROUP1";
    ALTER TABLE T1 ALTER PARTITION RANGE (a) ((PARTITION 20 <= VALUES < 30)) SET GROUP NAME "GROUP2" GROUP TYPE "HR" CASCADE;
    ALTER TABLE T1 ALTER PARTITION 2 UNSET GROUP;
    ALTER TABLE T1 ALTER PARTITION RANGE (a) ((PARTITION 20 <= VALUES < 30)) UNSET GROUP CASCADE;
    ALTER TABLE T4 ALTER PARTITION 1 COLUMN LOADABLE;
    ALTER TABLE T4 ALTER PARTITION RANGE (a) ((PARTITION 10 <= VALUES < 20)
        SUBPARTITION BY RANGE (b) (PARTITION VALUES = 100)) PAGE LOADABLE;
    ALTER TABLE A1 PARTITION BY RANGE (A)
        ((PARTITION 10 <= VALUES < 20)
        SUBPARTITION BY RANGE (B) (PARTITION 15 <= VALUES < 20, PARTITION OTHERS DYNAMIC),
        (PARTITION VALUES = 30)
        SUBPARTITION BY RANGE (B) (PARTITION VALUES=20, PARTITION OTHERS),
        (PARTITION OTHERS));
    ALTER TABLE A1 ADD PARTITION FROM OTHERS;
    ALTER TABLE A1 DROP EMPTY PARTITIONS;
    ALTER TABLE A1 PARTITION BY RANGE (A)
        ((PARTITION 10 <= VALUES < 20)
        SUBPARTITION BY RANGE (B) (PARTITION 15 <= VALUES < 20, PARTITION OTHERS DYNAMIC THRESHOLD 2),
        (PARTITION VALUES = 30)
        SUBPARTITION BY RANGE (B) (PARTITION VALUES = 20, PARTITION OTHERS),
        (PARTITION OTHERS));
    ALTER TABLE A1 PARTITION BY RANGE (A)
        ((PARTITION 10 <= VALUES < 20)
        SUBPARTITION BY RANGE (B) (PARTITION 15 <= VALUES < 20, PARTITION OTHERS),
        (PARTITION VALUES = 30)
        SUBPARTITION BY RANGE (B) (PARTITION VALUES = 20, PARTITION OTHERS),
        (PARTITION OTHERS));
-- other
    ALTER TABLE t ALTER (b INT DEFAULT 10);
    ALTER TABLE t ADD (d NVARCHAR(10) DEFAULT 'NCHAR');
    ALTER TABLE t DROP PRIMARY KEY;
    ALTER TABLE t ADD PRIMARY KEY (c,d);
    ALTER TABLE t ADD CONSTRAINT prim_key PRIMARY KEY (a, b);
    ALTER TABLE t COLUMN;
    ALTER TABLE t PRELOAD (b, c);
    ALTER TABLE t UNLOAD PRIORITY 2;
    ALTER TABLE t LOB REORGANIZE (a);
    ALTER TABLE t LOB REORGANIZE;
    ALTER TABLE T3 ALTER (C3 VARCHAR(80) COLUMN LOADABLE, C6 ALTER PAGE LOADABLE);
    ALTER TABLE T3 SET GROUP TYPE group_type1 GROUP NAME group_name1;
    ALTER TABLE T3 UNSET GROUP;
    ALTER TABLE t1 DROP (a) ONLINE;
    ALTER TABLE T1 ALTER PRIMARY KEY INVERTED HASH;
    ALTER TABLE T ADD PRIMARY KEY USING INDEX IDX;
    ALTER TABLE A CLEAR COLUMN JOIN DATA STATISTICS;
    ALTER TABLE T1 ENABLE SCHEMA FLEXIBILITY;
    ALTER TABLE T2 ALTER (COL1 VARCHAR(10) FUZZY SEARCH INDEX OFF);
    ALTER TABLE T2 ALTER (COL2 NVARCHAR(10) FUZZY SEARCH MODE NULL);
    ALTER TABLE T5 ADD (B INT GENERATED ALWAYS AS IDENTITY (START WITH 100 INCREMENT BY 10));
    ALTER TABLE T_ALWAYS ADD (B INT GENERATED ALWAYS AS IDENTITY (START WITH 100 INCREMENT BY 10));
    ALTER TABLE T_DEFAULT ADD (B INT GENERATED ALWAYS AS IDENTITY (START WITH 100 INCREMENT BY 10));
    ALTER TABLE SRC_SCHEMA.TAB1 ADD ASYNCHRONOUS REPLICA;
    ALTER TABLE SRC_SCHEMA.TAB1 DROP REPLICA AT ALL LOCATIONS;
    ALTER TABLE SRC_SCHEMA.TAB1 DROP REPLICA AT 'hostb:30301';
    ALTER TABLE SRC_SCHEMA.TAB1 ENABLE ASYNCHRONOUS REPLICA;
    ALTER TABLE T4 DROP REPLICA AT ALL LOCATIONS;
    ALTER TABLE T4 ADD REPLICA AT 'host_name:30040';
    ALTER TABLE T4 MOVE REPLICA FROM 'host_name:30040' TO 'host_name:30042';
    ALTER TABLE SRC_TABLE ADD REPLICA PARTITION BY HASH (C2) PARTITIONS 8;
    ALTER TABLE T6 ALTER ( Name ALTER CLIENTSIDE ENCRYPTION ON WITH myCEK RANDOM );
    ALTER TABLE T6 ALTER ( Name ALTER CLIENTSIDE ENCRYPTION WITH myCEK2 DETERMINISTIC );
    ALTER TABLE T6 CANCEL CLIENTSIDE ENCRYPTION;
    ALTER TABLE T6 ALTER ( Name ALTER CLIENTSIDE ENCRYPTION OFF );
    ALTER TABLE T1 ALTER ( C2 ALTER CLIENTSIDE ENCRYPTION ACTIVATE NEW VERSION);
    ALTER TABLE PERSONAL_INFORMATION ADD MASK (SSN USING '****');
    ALTER TABLE PERSONAL_INFORMATION DROP MASK (SSN);
    ALTER TABLE REFERENCING_T ADD CONSTRAINT fk1 FOREIGN KEY(A) REFERENCES REFERENCED_T(A);
    ALTER TABLE REFERENCING_T ALTER CONSTRAINT fk1 NOT ENFORCED;
    ALTER TABLE REFERENCING_T ALTER CONSTRAINT fk1 ENFORCED;
    ALTER TABLE REFERENCING_T ALTER CONSTRAINT fk1 ENFORCED NOT VALIDATED;
    ALTER TABLE R ADD CONSTRAINT UK UNIQUE (B);
    ALTER TABLE R DROP CONSTRAINT UK;
    ALTER TABLE S ADD CONSTRAINT FK FOREIGN KEY(FA) REFERENCES R(A) ON DELETE CASCADE;
    ALTER TABLE account ADD (valid_from TIMESTAMP NOT NULL GENERATED ALWAYS AS ROW START);
    ALTER TABLE account ADD (valid_to TIMESTAMP NOT NULL GENERATED ALWAYS AS ROW END);
    ALTER TABLE account ADD PERIOD FOR SYSTEM_TIME(valid_from, valid_to);
    ALTER TABLE account ADD SYSTEM VERSIONING HISTORY TABLE account_history;
    ALTER TABLE myTable1 PERSISTENT MEMORY ON IMMEDIATE CASCADE;
    ALTER TABLE myTable2 ALTER (C1 INT PERSISTENT MEMORY DEFAULT);
    ALTER TABLE myTable3 ADD RANGE (C1) (PARTITION '0' <= VALUES < '10', PARTITION OTHERS PERSISTENT MEMORY ON DEFERRED);
    ALTER TABLE myTable4 PARTITION BY RANGE (C1) (PARTITION '0' <= VALUES < '10' PERSISTENT MEMORY ON IMMEDIATE, PARTITION OTHERS);
    ALTER TABLE myTable5 ALTER PARTITION 10 PERSISTENT MEMORY DEFAULT;
    ALTER TABLE t1 WITH ANNOTATIONS(
    UNSET ALL SET 'k1' = 'v1', 'k2'='v2'
    COLUMN c1 UNSET ALL SET 'k1'='v1', 'k2'='v2');
    ALTER TABLE T1 NUMA NODE NULL IMMEDIATE;
    ALTER TABLE T1 PARTITION BY RANGE (colint) (PARTITION '0' <= VALUES < '20' NUMA NODE ('3') DEFERRED);
    ALTER TABLE T1 ALTER PARTITION P0 NUMA NODE ('4') IMMEDIATE;
    ALTER TABLE T1 NUMA NODE ('4') IMMEDIATE;
    ALTER TABLE T PAGE LOADABLE CASCADE;
    ALTER TABLE T PARTITION BY RANGE (C1)
    (PARTITION 0 <= VALUES < 10 PAGE LOADABLE,
    PARTITION OTHERS DEFAULT LOADABLE);
    ALTER TABLE T ADD RANGE (C1) (
    PARTITION 0 <= VALUES < 10,
    PARTITION OTHERS COLUMN LOADABLE);
    ALTER TABLE T ALTER PARTITION 10 DEFAULT LOADABLE;
    ALTER TABLE T ALTER (C1 INT DEFAULT LOADABLE);
-- alter virtual table
    ALTER VIRTUAL TABLE REMOTE1_VT SET PROPERTY 'name1' = 'value1';
    ALTER VIRTUAL TABLE REMOTE1_VT SET PROPERTY 'name1' = 'value2';
    ALTER VIRTUAL TABLE REMOTE1_VT UNSET PROPERTY 'name1';
    ALTER VIRTUAL TABLE REMOTE1_VT ALTER A SET PROPERTY 'name1' = 'value1';
    ALTER VIRTUAL TABLE REMOTE1_VT ALTER A SET PROPERTY 'name1' = 'value2';
    ALTER VIRTUAL TABLE REMOTE1_VT ALTER A UNSET PROPERTY 'name1';
/* alter table end */

-- alter view
    ALTER VIEW V ADD STATIC CACHE RETENTION 10;
    ALTER VIEW V ALTER STATIC CACHE RETENTION 20;
    ALTER VIEW V DROP STATIC CACHE;
    ALTER VIEW V ADD DYNAMIC CACHE;
    ALTER VIEW view_a ADD STATIC CACHE AT LOCATION 'myhost2:00002';
    ALTER VIEW view_a ALTER STATIC CACHE DROP AT LOCATION 'myhost2:00002';
    ALTER VIEW V ADD STATIC CACHE RETENTION 10;
    ALTER VIEW V ALTER STATIC CACHE RETENTION 20;
    ALTER VIEW V DROP STATIC CACHE;
    ALTER VIEW V_1 ADD STATIC CACHE RETENTION 1000 OF A, SUM(B), AVG(C);
    ALTER VIEW view_a ADD DYNAMIC CACHE;
    ALTER VIEW view_a ALTER DYNAMIC CACHE ADD INDEX ON A;
    ALTER VIEW v1 ADD EXPRESSION MACROS(AVG(a) AS avg_a);
    ALTER VIEW v1 ADD EXPRESSION MACROS(EXPRESSION_MACRO(sum_a) / EXPRESSION_MACRO(count_a) AS avg2_a);
    ALTER VIEW v1 DROP EXPRESSION MACROS( avg_a, avg2_a );
    ALTER VIEW myView WITH ANNOTATIONS (
        UNSET 'key1' SET 'key1' = 'value1'
        COLUMN column1 UNSET 'Key2' SET 'Key2' = 'value2'
    );
    ALTER VIEW v1 AS SELECT * FROM t1 WITH NO DDL ONLY;
    ALTER VIEW v1 AS SELECT * FROM t1 WITH DDL ONLY;

-- annotate
    ANNOTATE add_shipping_func SET 'region'='EMEA';
    ANNOTATE PARAMETER add_shipping_func.price SET 'currency'='USD';
    ANNOTATE PARAMETER add_shipping_func.shipping SET 'currency'='USD';
    ANNOTATE PARAMETER add_shipping_func.price SET 'currency'='CAD';
    ANNOTATE PARAMETER add_shipping_func.shipping UNSET 'currency';
    ANNOTATE t1 SET 'priority'='high';
    ANNOTATE COLUMN t1.col1 SET 'datatype'='integer';
    ANNOTATE COLUMN t1.col1 SET 'nullability'='no nulls';
    ANNOTATE COLUMN t1.col1 UNSET ALL;
    ANNOTATE t1 UNSET 'priority';

-- comment
    COMMENT ON TABLE COMMENT_ON_EX IS 'Used for comment on examples';
    COMMENT ON COLUMN COMMENT_ON_EX.A IS 'This is an example column comment';
    COMMENT ON VIEW COMMENT_ON_EX_VIEW IS 'This is a view comment';
    COMMENT ON COLUMN COMMENT_ON_EX_VIEW.A IS 'This is a view column comment';
    COMMENT ON SCHEDULER JOB retention_job IS 'This job implements a retention policy';
    COMMENT ON VIEW COMMENT_ON_EX_VIEW IS NULL;
    COMMENT ON COLUMN COMMENT_ON_EX_VIEW.A IS NULL;

-- create index
    CREATE INDEX idx ON t(b);
    CREATE CPBTREE INDEX idx1 ON t(a, b DESC);
    CREATE INDEX idx2 ON t(a, c) DESC;
    CREATE UNIQUE INDEX idx3 ON t(b, c);
    CREATE UNIQUE INDEX idx4 ON t(a);
    CREATE INDEX idx10 ON s(b);
    CREATE INVERTED VALUE INDEX idx11 ON s(a, b);
    CREATE UNIQUE INVERTED INDIVIDUAL INDEX idx12 ON s(a, c);
    CREATE UNIQUE INVERTED VALUE INDEX idx13 ON s(b, c) PAGE LOADABLE;
    CREATE FULLTEXT INDEX i ON A(A) FUZZY SEARCH INDEX OFF SYNC LANGUAGE DETECTION ('EN','DE','KR');
    CREATE FUZZY SEARCH INDEX fzy_idx1 ON T1 (a1) SEARCH MODE TEXT;
    CREATE FUZZY SEARCH INDEX fzy_idx2 ON T2(COL1) MIME TYPE 'application/x-abap-rawstring' TOKEN SEPARATORS '\/;,.:-_()[]<>!?*@+{}="&#$~|';

-- create graph workspace
    CREATE GRAPH WORKSPACE FAMILY_TREE.GENEALOGY
        EDGE TABLE FAMILY_TREE.EDGES
        SOURCE COLUMN "SOURCE"
        TARGET COLUMN "TARGET"
        KEY COLUMN ID
        VERTEX TABLE FAMILY_TREE.VERTICES
        KEY COLUMN "KEY";

-- create projection view
    CREATE PROJECTION VIEW myProjView (Item, Description, Status) AS SELECT * FROM SEARCH_TEXT;
    CREATE PROJECTION VIEW V1 AS SELECT A, B FROM T1 WITH ASSOCIATIONS(JOIN T2 AS C ON C.A = A);
    CREATE PROJECTION VIEW v2 AS SELECT * FROM t1 WITH ANNOTATIONS(
        SET 'v_k1' = 'v_v1', 'v_k2' = 'v_v2'
        COLUMN c1 SET 'c_k1' = 'c_v1', 'c_k2' = 'c_v2'
    );
    CREATE PROJECTION VIEW v1 AS SELECT * FROM t1 WITH DDL ONLY;
    CREATE PROJECTION VIEW v2 AS SELECT * FROM v1;

-- create scheduler job
    CREATE SCHEDULER JOB RETENTION_JOB CRON '* * * mon,tue,wed,thu,fri 1 23 45'
        ENABLE PROCEDURE RETENTION_PROCEDURE PARAMETERS DAYS=14;

-- create schema
    CREATE SCHEMA my_schema OWNED BY system;

-- create synonym
    CREATE SCHEMA SYNONYM SCHEMA_SYNONYM FOR SCHEMA_A;
    CREATE SCHEMA SYNONYM TEST_SCHEMA_SYN FOR SYSTEM;
    CREATE SYNONYM a_synonym FOR A;

-- create sequence
    CREATE SEQUENCE mySequence START WITH 1000 INCREMENT BY 1;
    CREATE SEQUENCE s1 START WITH 9223372036854775806;
    CREATE SEQUENCE s1 START WITH 9223372036854775806 MAXVALUE 9999999999999999999999999999;

-- create STATISTICS
    CREATE STATISTICS ON MYSYSTEM.T(X) TYPE HISTOGRAM BUCKETS 100;
    CREATE STATISTICS ON MYSYSTEM.T(X,Y) TYPE SIMPLE;
    CREATE STATISTICS ON MYSYSTEM.T(X) TYPE HISTOGRAM BUCKETS 1000;
    CREATE STATISTICS MYSYSTEM.SKETCH_T_XY ON MYSYSTEM.T(X,Y) TYPE SKETCH REFRESH TYPE MANUAL;
    CREATE STATISTICS ON MYSYSTEM.MY_VIRTUAL_TABLE TYPE RECORD COUNT;
    CREATE STATISTICS ON REMOTE1.MYSYSTEM.T(A) TYPE TOPK BUCKETS 1500;
    CREATE STATISTICS MYSYSTEM.MYSIMPLTESTAT ON MYSYSTEM.MYTABLE(A) TYPE SIMPLE VALID FOR DATA DEPENDENCY;
    CREATE STATISTICS ON MYSYSTEM.MY_TABLE TYPE SAMPLE SAMPLE SIZE 10000;

-- create trigger
    CREATE TRIGGER TEST_TRIGGER
        AFTER INSERT ON TARGET FOR EACH ROW
        BEGIN
        DECLARE SAMPLE_COUNT INT;
        SELECT COUNT(*) INTO SAMPLE_COUNT FROM SAMPLE;
        IF :SAMPLE_COUNT = 0
        THEN
        INSERT INTO SAMPLE VALUES(5);
        ELSEIF :SAMPLE_COUNT = 1
        THEN
        INSERT INTO SAMPLE VALUES(6);
        END IF;
        END;
    CREATE TRIGGER TRIGGER_TEST_1
        AFTER INSERT ON TARGET
        FOR EACH ROW FOLLOWS TEST_TRIGGER
        BEGIN
        DECLARE SAMPLE_COUNT INT;
        SELECT COUNT(*) INTO SAMPLE_COUNT FROM SAMPLE;
        IF :SAMPLE_COUNT = 0
        THEN
        INSERT INTO SAMPLE VALUES(5);
        ELSEIF :SAMPLE_COUNT = 1
        THEN
        INSERT INTO SAMPLE VALUES(6);
        END IF;
        END;
    CREATE TRIGGER TEST_TRIGGER_WHILE_UPDATE
        AFTER UPDATE ON TARGET
        BEGIN
        DECLARE found INT := 1;
        DECLARE val INT := 1;
        WHILE :found <> 0 DO
        SELECT count(*) INTO found FROM sample WHERE a = :val;
        IF :found = 0 THEN
        INSERT INTO sample VALUES(:val);
        END IF;
        val := :val + 1;
        END WHILE;
        END;
    CREATE TRIGGER TEST_TRIGGER_UPDATE_COLUMN_LIST
        AFTER UPDATE OF B ON TARGET
        BEGIN
        INSERT INTO SAMPLE VALUES(1);
        END;
    CREATE TRIGGER TEST_TRIGGER_UPDATE_EXCEPT_OF_COLUMN_LIST
        AFTER UPDATE EXCEPT OF B ON TARGET
        BEGIN
        INSERT INTO SAMPLE VALUES(1);
        END;
    CREATE TRIGGER TEST_TRIGGER_FOR_INSERT
        AFTER INSERT ON TARGET
        BEGIN
        DECLARE v_id        INT := 0;
        DECLARE v_name      VARCHAR(20) := '';
        DECLARE v_pay       INT := 0;
        DECLARE v_msg       VARCHAR(200) := '';
        DELETE FROM message_box;
        FOR v_id IN 100 .. 103 DO
        SELECT name, payment INTO v_name, v_pay FROM control_tab WHERE id = :v_id;
        v_msg := :v_name || ' has ' || TO_VARCHAR(:v_pay);
        INSERT INTO message_box VALUES (:v_msg, CURRENT_TIMESTAMP);
        END FOR;
        END;
    CREATE TRIGGER MYTRIG_SQLEXCEPTION
        AFTER INSERT ON TARGET
        BEGIN
        DECLARE EXIT HANDLER FOR SQLEXCEPTION RESIGNAL;
        INSERT INTO MYTAB VALUES (1);
        INSERT INTO MYTAB VALUES (1);  -- expected unique violation error: 301
    -- not reached
        END;

    CREATE TRIGGER MYTRIG_SQL_ERROR_CODE
        AFTER UPDATE ON TARGET
        BEGIN
        DECLARE EXIT HANDLER FOR SQL_ERROR_CODE 301 RESIGNAL;
        INSERT INTO MYTAB VALUES (1);
        INSERT INTO MYTAB VALUES (1);  -- expected unique violation error: 301
    -- not reached
        END;

    CREATE TRIGGER MYTRIG_CONDITION
        AFTER DELETE ON TARGET
        BEGIN
        DECLARE MYCOND CONDITION FOR SQL_ERROR_CODE 301;
        DECLARE EXIT HANDLER FOR MYCOND RESIGNAL;
        INSERT INTO MYTAB VALUES (1);
        INSERT INTO MYTAB VALUES (1);  -- expected unique violation error: 301
    -- not reached
        END;
    CREATE TRIGGER MYTRIG_SIGNAL
        AFTER INSERT ON TARGET
        BEGIN
        DECLARE MYCOND CONDITION FOR SQL_ERROR_CODE 10001;
        DECLARE EXIT HANDLER FOR MYCOND INSERT INTO MYTAB_TRIGGER_ERR VALUES (::SQL_ERROR_CODE, ::SQL_ERROR_MESSAGE);
        INSERT INTO MYTAB VALUES (1);
        SIGNAL MYCOND SET MESSAGE_TEXT = 'my error message1';  -- signal immediately
    -- not reached
        INSERT INTO MYTAB VALUES (2);
        END;
    CREATE TRIGGER MYTRIG_RESIGNAL
        AFTER UPDATE ON TARGET
        BEGIN
        DECLARE MYCOND CONDITION FOR SQL_ERROR_CODE 10002;
        DECLARE EXIT HANDLER FOR MYCOND RESIGNAL;  -- 2. throws error with error code 10002
        INSERT INTO MYTAB VALUES (1);
        SIGNAL MYCOND SET MESSAGE_TEXT = 'my error message2';  -- 1. signal immediately
    -- not reached
        INSERT INTO MYTAB VALUES (2);
        END;
    CREATE TRIGGER TEST_TRIGGER_VAR_UPDATE
        AFTER UPDATE ON TARGET
        REFERENCING NEW ROW mynewrow, OLD ROW myoldrow
        FOR EACH ROW
        BEGIN
        INSERT INTO SAMPLE_new VALUES(:mynewrow.a, :mynewrow.b);
        INSERT INTO SAMPLE_old VALUES(:myoldrow.a, :myoldrow.b);
        INSERT INTO SAMPLE VALUES(0, 'trigger');
        END;
    CREATE TRIGGER TEST_TRIGGER_TAB_UPDATE
        AFTER UPDATE ON TARGET
        REFERENCING NEW TABLE mynewtab, OLD TABLE myoldtab
        FOR EACH STATEMENT
        BEGIN
        INSERT INTO SAMPLE_new SELECT * FROM :mynewtab;
        INSERT INTO SAMPLE_old SELECT * FROM :myoldtab;
        INSERT INTO SAMPLE VALUES(0, 'trigger');
        END;
    CREATE TRIGGER TR1 INSTEAD OF INSERT ON V1 REFERENCING NEW ROW NEW
        BEGIN
        INSERT INTO T1 VALUES(:NEW.A, :NEW.B);
        INSERT INTO T2 VALUES(:NEW.A, :NEW.C);
        END;
    CREATE TRIGGER TR2 INSTEAD OF UPDATE ON V1 REFERENCING OLD ROW OLD, NEW ROW NEW
        BEGIN
        UPDATE T1 SET A=:NEW.A, B=:NEW.B WHERE A=:OLD.A;
        UPDATE T2 SET A=:NEW.A, C=:NEW.C WHERE A=:OLD.A;
        END;
    CREATE TRIGGER TR3 INSTEAD OF DELETE ON V1 REFERENCING OLD ROW OLD
        BEGIN
        DELETE FROM T1 WHERE A = :OLD.A;
        DELETE FROM T2 WHERE A = :OLD.A;
        END;

-- create view
    CREATE VIEW v AS SELECT * FROM A;
    CREATE VIEW v AS SELECT * FROM A WHERE A > 0 WITH CHECK OPTION;
    CREATE VIEW V AS SELECT DISTINCT A, B FROM A  WITH STATIC CACHE RETENTION 10;
    CREATE VIEW view_a AS SELECT DISTINCT A, B FROM A
        WITH STATIC CACHE
        AT LOCATION 'myhost2:00002';
    CREATE VIEW V AS SELECT DISTINCT A, B FROM A WITH DYNAMIC CACHE;
    CREATE VIEW credit_card_view
        AS SELECT * FROM credit_card_tab
        WITH MASK (CREDIT_CARD USING credit_card_mask(credit_card));
    CREATE VIEW credit_view AS SELECT * FROM credit_tab WITH MASK (CREDIT_CARD USING mask_owner.credit_mask(credit_card));
    CREATE VIEW v1 AS SELECT * FROM t1 WITH EXPRESSION MACROS(SUM(a) AS sum_a, COUNT(a) AS count_a);
    CREATE VIEW v1 AS SELECT * FROM t WITH EXPRESSION MACROS (
        SUM(x) AS sum_x,
        AVG(y) AS avg_y
    );
    CREATE VIEW v2 AS SELECT * FROM v1 WITH EXPRESSION MACROS (
        sum_x AS sum_x2,
        avg_y,
        COUNT(x) AS count_x
    );
    CREATE VIEW v1 AS SELECT * FROM t1 WITH ANNOTATIONS(
    SET 'v_k1' = 'v_v1', 'v_k2' = 'v_v2'
    COLUMN c1 SET 'c_k1' = 'c_v1', 'c_k2' = 'c_v2');
    CREATE VIEW v1 AS SELECT * FROM t1 WITH DDL ONLY;
    CREATE VIEW Illness_K_Anon (ID, Gender, Location, Illness)
            AS SELECT ID, Gender, City AS Location, Illness
            FROM Illness
            WITH ANONYMIZATION ( ALGORITHM 'K-ANONYMITY'
            PARAMETERS '{"data_change_strategy": "qualified", "k": 2}'
            COLUMN ID PARAMETERS '{"is_sequence": true}'
            COLUMN Gender PARAMETERS '{"is_quasi_identifier":true, "hierarchy":{"embedded": [["F"], ["M"]]}}'
            COLUMN Location PARAMETERS '{"is_quasi_identifier":true, "hierarchy":{"embedded": [["Paris", "France"], ["Munich", "Germany"], ["Nice", "France"]]}}');

-- create table
    CREATE ROW TABLE A (A INT PRIMARY KEY, B INT);
    CREATE ROW TABLE T4 (C1 INT PRIMARY KEY, C2 DATE) GROUP TYPE group_type1 GROUP NAME group_name1;
    CREATE COLUMN TABLE account_history (
  account_id INT,
  account_owner_id NVARCHAR(10),
  account_balance DOUBLE,
  valid_from timestamp NOT NULL,
  valid_to timestamp NOT NULL
 );

CREATE COLUMN TABLE account (
  account_id INT PRIMARY KEY,
  account_owner_id NVARCHAR(10),
  account_balance DOUBLE,
  valid_from TIMESTAMP NOT NULL GENERATED ALWAYS AS ROW START,
  valid_to TIMESTAMP NOT NULL GENERATED ALWAYS AS ROW END,
  PERIOD FOR SYSTEM_TIME (valid_from, valid_to)
 )
 WITH SYSTEM VERSIONING HISTORY TABLE account_history;

 CREATE COLUMN TABLE T (A INT, B INT GENERATED ALWAYS AS IDENTITY (START WITH 100 INCREMENT BY 10));
 CREATE COLUMN TABLE MyData ( My_Field NVARCHAR(10) COMMENT 'My data values') COMMENT 'Table for data';
 CREATE TABLE BASE_TABLE (a int, b nvarchar(10));
CREATE LOCAL TEMPORARY TABLE #TARGET_TABLE (RENAME_A, RENAME_B) ADD (ID INT GENERATED ALWAYS AS IDENTITY(START WITH 1 INCREMENT BY 1)) AS (SELECT * FROM BASE_TABLE);
CREATE TABLE t1(a INT, b INT, c INT);  
CREATE TABLE t2(a INT, b INT, c INT);
CREATE TABLE ct1 AS (WITH alias AS (SELECT * FROM t1) SELECT * FROM alias);
CREATE TABLE ct2 AS (WITH w1 AS (SELECT * FROM t1) SELECT * FROM w1);   
CREATE TABLE ct3 AS (WITH t1 AS (SELECT * FROM t2) SELECT * FROM t1);                                           
CREATE TABLE ct4 AS (WITH w1 AS (SELECT * FROM t2) SELECT w1.a, t2.b FROM w1,t2);
CREATE TABLE ct5 AS (WITH w1 AS (SELECT * FROM t2), w2 AS (SELECT * FROM t1 INNER JOIN t2 ON t1.a=t2.a) SELECT w1.a FROM w1,w2);
CREATE TABLE ct6 AS (WITH d AS (SELECT 1 AS val FROM dummy) SELECT val FROM d);
CREATE COLUMN TABLE t1(c1 INT) WITH ANNOTATIONS(
    SET 't_k1' = 't_v1', 't_k2' = 't_v2'
    COLUMN c1 SET 'c_k1' = 'c_v1', 'c_k2' = 'c_v2');
    CREATE COLUMN TABLE A1(cola INT);
    CREATE COLUMN TABLE A2 LIKE A1 WITH DATA WITH INDEX index1 AS INDEX2;
    CREATE COLUMN TABLE t1(a INT) ASYNCHRONOUS REPLICA AT 'seltera17:30040';
    CREATE COLUMN TABLE C1 LIKE A WITH DATA;
    CREATE ROW TABLE T4 (C1 INT PRIMARY KEY) REPLICA AT ALL LOCATIONS;
    CREATE ROW TABLE REP_SCHEMA.TAB1 LIKE SRC_SCHEMA.TAB1 ASYNCHRONOUS REPLICA;
    CREATE COLUMN TABLE SRC_TABLE (C1 INT, C2 INT, C3 INT, primary key (C1, C2, C3)) PARTITION BY HASH (C1) PARTITIONS 32;
    CREATE COLUMN TABLE REP_TABLE1 LIKE SRC_TABLE REPLICA PARTITION BY HASH (C2) PARTITIONS 8;
    CREATE COLUMN TABLE REP_TABLE2 LIKE SRC_TABLE REPLICA (C1, C3) PARTITION BY RANGE (C3) (PARTITION '1' <= VALUES < '100', PARTITION OTHERS);
    CREATE COLUMN TABLE srcSchema.srcTableName (A INT, B INT, C NVARCHAR(10), D CHAR(1), E BIGINT, PRIMARY KEY (A));
CREATE COLUMN TABLE repSchema.repName LIKE srcSchema.srcTableName SYNCHRONOUS REPLICA (A, C, D);
CREATE HISTORY COLUMN TABLE H (A INT);
CREATE ROW TABLE H1 LIKE H WITH DATA;
CREATE ROW TABLE H2 LIKE H WITH NO DATA WITHOUT HISTORY;
CREATE COLUMN TABLE P1 (A  PRIMARY KEY) 
   PARTITION BY RANGE (A) 
      (PARTITION '2010-02-03' <= VALUES < '2011-01-01', PARTITION VALUE = '2011-05-01');
CREATE COLUMN TABLE P2 (I INT, J INT, K INT, PRIMARY KEY(I, J)) 
   PARTITION BY HASH (I, J) PARTITIONS 2;
   CREATE COLUMN TABLE P3 (I INT, J INT, K INT, PRIMARY KEY(I, J)) 
   PARTITION BY HASH (I, J) PARTITIONS 2, HASH (K) PARTITIONS 2;
   CREATE COLUMN TABLE P4 (A INT, B INT) 
   PARTITION BY RANGE (A) 
      (PARTITION 10 <= VALUES < 20 PERSISTENT MEMORY ON NUMA NODE ('3'), PARTITION OTHERS PERSISTENT MEMORY ON),
   RANGE (B) (PARTITION VALUES=100 PERSISTENT MEMORY ON);
   CREATE COLUMN TABLE P5 (A INT, B INT NOT NULL) PARTITION BY RANGE (A) (PARTITION VALUES = 10), 
   RANGE (B) (PARTITION VALUES = 20, PARTITION OTHERS DYNAMIC THRESHOLD 2);
   CREATE COLUMN TABLE P6 (A INT, B INT PRIMARY KEY, _DATAAGING NVARCHAR(8)) 
   PARTITION BY RANGE (_DATAAGING)
      (USING DEFAULT STORAGE (PARTITION value = '00000000' IS CURRENT, 
         PARTITION '20100101' <= VALUES < '20110101', PARTITION OTHERS))
   WITH PARTITIONING ON ANY COLUMNS ON
   FOR NON CURRENT PARTITIONS UNIQUE CONSTRAINTS OFF
   FOR DEFAULT STORAGE NON CURRENT PARTITIONS PAGE LOADABLE;
   CREATE COLUMN TABLE A1 (I INT, J INT, K INT, PRIMARY KEY(I, J)) 
   PARTITION BY RANGE (I) ((PARTITION 10 <= VALUES < 20, PARTITION 20 <= VALUES < 30) );
   CREATE COLUMN TABLE A2 (I INT, J INT, K INT, PRIMARY KEY(I, J)) 
   PARTITION BY RANGE (I) ((PARTITION 10 <= VALUES < 20, PARTITION 20 <= VALUES < 30)
      SUBPARTITION BY RANGE (J) (PARTITION 100 <= VALUES < 150, PARTITION VALUE = 200) );
CREATE COLUMN TABLE A3 (A INT, B INT NOT NULL, C INT) PARTITION BY RANGE (A)
   ((PARTITION 10 <= VALUES < 20 GROUP NAME 'GRP 1' INSERT OFF, PARTITION 20 <= VALUES < 30 GROUP NAME 'GRP 1' INSERT OFF) 
      SUBPARTITION BY RANGE(B) (PARTITION 0 <= VALUES < 10 PAGE LOADABLE GROUP SUBTYPE 'GRP 1a', PARTITION 10 <= VALUES < 
           100 GROUP SUBTYPE 'GRP 1b' INSERT OFF PAGE LOADABLE, PARTITION OTHERS DYNAMIC THRESHOLD 2),
    (PARTITION VALUES = 40 GROUP NAME 'GRP 2' INSERT OFF COLUMN LOADABLE)
      SUBPARTITION BY RANGE(B) (PARTITION 0 <= VALUES < 10 GROUP SUBTYPE 'GRP 2a', PARTITION OTHERS DYNAMIC THRESHOLD 2));
CREATE COLUMN TABLE T4 (A INT, B INT NOT NULL) PARTITION BY RANGE (A)
   ((PARTITION 10 <= VALUES < 20) SUBPARTITION BY RANGE (B) (PARTITION 100 <= VALUES < 150, PARTITION OTHERS DYNAMIC THRESHOLD 3 ),
    (PARTITION VALUES = 30) SUBPARTITION BY RANGE (B) (PARTITION VALUES = 200, PARTITION OTHERS DYNAMIC THRESHOLD 3));
CREATE COLUMN TABLE A5 (A DATE, B INT, C INT) PARTITION BY RANGE (B) 
   ((PARTITION 10 <= VALUES < 20 INSERT ON PAGE LOADABLE, PARTITION VALUES = 30 COLUMN LOADABLE GROUP NAME 'HR') 
       SUBPARTITION BY HASH (YEAR(a), c) PARTITIONS 4,
    (PARTITION 40 <= VALUES < 50 PAGE LOADABLE GROUP NAME 'HR') 
       SUBPARTITION BY HASH (YEAR(a), c) PARTITIONS 4);
CREATE COLUMN TABLE A3 (A INT, B DATE NOT NULL, C INT) PARTITION BY RANGE (A)
   ((PARTITION 0<= VALUES < 100, PARTITION OTHERS) 
       SUBPARTITION BY RANGE(YEAR(B)) (PARTITION '2020' <= VALUES < '2021',
   PARTITION OTHERS DYNAMIC INTERVAL 2 YEAR));
   CREATE ROW TABLE PERSONAL_INFO (SSN VARCHAR(20)) WITH MASK (SSN USING '****');
   CREATE TABLE PERSONAL_INFO (SSN VARCHAR(20)) WITH SESSION USER MASK ( SSN USING '****' );
   CREATE ROW TABLE C2 AS (SELECT * FROM A) WITH NO DATA;
   CREATE ROW TABLE R (A INT PRIMARY KEY, B NVARCHAR(10));
 CREATE ROW TABLE F (FK INT, B NVARCHAR(10), FOREIGN KEY(FK) REFERENCES R ON UPDATE CASCADE);
CREATE ROW TABLE SELF(A INTEGER PRIMARY KEY, B INTEGER);
CREATE ROW TABLE GRAND_PARENT (A INT, I INT, B INT, PRIMARY KEY (A) );
CREATE ROW TABLE PARENT (A INT, I INT, B INT, PRIMARY KEY (A) );
CREATE COLUMN TABLE T(A INT) WITH SCHEMA FLEXIBILITY;
CREATE COLUMN TABLE T2 (KEY INT, COL1 VARCHAR(10) FUZZY SEARCH INDEX ON, COL2 NVARCHAR(10) FUZZY SEARCH MODE 'POSTCODE');
CREATE GLOBAL TEMPORARY TABLE T (C1 INT, C2 INT) ON COMMIT PRESERVE ROWS;
CREATE GLOBAL TEMPORARY TABLE T (C1 INT, C2 INT) ON COMMIT DELETE ROWS;
CREATE GLOBAL TEMPORARY TABLE T (C1 INT, C2 INT) ON COMMIT PRESERVE ROWS;
CREATE GLOBAL TEMPORARY TABLE T (C1 INT, C2 INT) ON COMMIT DELETE ROWS;
CREATE COLUMN TABLE ExampleStockTrades(
     ticker_symbol VARCHAR(5),
     trade_time TIMESTAMP,
     price DECIMAL(10,2),
     volume INTEGER)
     SERIES(SERIES KEY(ticker_symbol) NOT EQUIDISTANT
         MINVALUE '2013-01-01'
         MAXVALUE '2014-01-01'
         PERIOD FOR SERIES(trade_time));
CREATE COLUMN TABLE ExampleCityTemperature(
     city_id INTEGER,
     day_start TIMESTAMP,
     day_end TIMESTAMP,
     min_temperature DECIMAL(5,2),
     max_temperature DECIMAL(5,2))
     SERIES(SERIES KEY(city_id) EQUIDISTANT INCREMENT BY INTERVAL 1 DAY
         MINVALUE '2010-01-01'
         MAXVALUE '2020-01-01'
         PERIOD FOR SERIES (day_start, day_end));
CREATE COLUMN TABLE T1 ( ID INT PRIMARY KEY, C1 INT ARRAY );
CREATE COLUMN TABLE T2 ( ID INT PRIMARY KEY, C1 INT ARRAY (1, 10) );
CREATE COLUMN TABLE T3 ( ID INT PRIMARY KEY, C1 INT ARRAY (10) );
CREATE COLUMN TABLE T4 ( ID INT PRIMARY KEY, C1 INT ARRAY (1, *) );
CREATE COLUMN TABLE T5 ( ID INT PRIMARY KEY, C1 INT ARRAY (1, 5) WITHOUT DUPLICATES );
CREATE COLUMN TABLE T6 ( ID INT PRIMARY KEY, C1 INT ARRAY DEFAULT ARRAY() );
CREATE ROW TABLE T7 (
 ID INT PRIMARY KEY,
 Name NVARCHAR(32) CLIENTSIDE ENCRYPTION ON WITH myCEK RANDOM);
 CREATE COLUMN TABLE myTable1 (C1 INT, C2 VARCHAR (10)) PERSISTENT MEMORY ON;
 CREATE COLUMN TABLE myTable2 (C1 INT) PARTITION BY RANGE (C1) (PARTITION '0' <= VALUES < '10' PERSISTENT MEMORY ON, PARTITION OTHERS PERSISTENT MEMORY OFF);
 CREATE COLUMN TABLE PMTABLE (C1 INT PERSISTENT MEMORY ON, C2 VARCHAR (10), C3 INT PERSISTENT MEMORY OFF);
 CREATE COLUMN TABLE T1(colint INT, colvarchar VARCHAR(10)) NUMA NODE ('1', '3' TO '5');
 CREATE COLUMN TABLE T1(colint INT, colvarchar VARCHAR(10)) PARTITION BY RANGE (colint) (PARTITION '0' <= VALUES < '20' NUMA NODE ('2'));
 CREATE COLUMN TABLE T1(colint INT NUMA NODE(3), colvarchar VARCHAR(10)) NUMA NODE ('5');
 CREATE COLUMN TABLE T1 LIKE T2; 
 CREATE COLUMN TABLE T1 LIKE T2 WITHOUT NUMA NODE;
 CREATE COLUMN TABLE T (C1 INT, C2 VARCHAR (10)) PAGE LOADABLE;
CREATE TABLE t2(x2 INT, y2 INT) WITH ASSOCIATIONS 
(JOIN t1 AS a ON a.x1 = x2 WITH DEFAULT FILTER y1 < 3);
CREATE VIRTUAL TABLE VT AT "S"."HA1"."SYSTEM"."tableA";
CREATE VIRTUAL TABLE AAA AT "S"."HA1"."SYSTEM"."tableA" 
   REMOTE PROPERTY 'dataprovisioning_parameters'='<Parameter name="objects">1,10,1000,300</Parameter>
   <Parameter name="stateHierarchy">USA</Parameter><Parameter name="date">2014-06-01</Parameter>';
CREATE VIRTUAL TABLE virtual_A1 (a int, b int) at "Remote1"."HA1"."SYSTEM"."A1" WITH REMOTE;

-- create AUDIT POLICY
CREATE AUDIT POLICY priv_audit AUDITING SUCCESSFUL GRANT PRIVILEGE, REVOKE PRIVILEGE, GRANT ROLE, REVOKE ROLE LEVEL CRITICAL;
CREATE AUDIT POLICY OBJECT_AUDIT AUDITING SUCCESSFUL INSERT ON MY_SCHEMA.MY_TABLE FOR FRED LEVEL INFO;
CREATE AUDIT POLICY UPDATE_OBJECT_AUDIT AUDITING SUCCESSFUL UPDATE ON MY_SCHEMA.MY_TABLE EXCEPT FOR TECH_ADMIN LEVEL CRITICAL;
CREATE AUDIT POLICY SYSTEMDB_TEST FOR HA2 AUDITING SUCCESSFUL ALTER PSE LEVEL CRITICAL;
CREATE AUDIT POLICY MY_AUDIT_POLICY AUDITING ALL CREATE TABLE ON SCHEMA TEST_SCHEMA LEVEL INFO;

-- alter AUDIT POLICY
ALTER AUDIT POLICY "priv_audit" ENABLE;
ALTER AUDIT POLICY "priv_audit" SET TRAIL TYPE SYSLOG, TABLE;
ALTER AUDIT POLICY "priv_audit" RESET TRAIL TYPE;
ALTER AUDIT POLICY "priv_audit" DISABLE;
ALTER AUDIT POLICY "priv_audit" SET RETENTION 30;
ALTER AUDIT POLICY "priv_audit" RESET RETENTION;
ALTER AUDIT POLICY SYSTEMDB_TEST FOR HA2 ENABLE;

-- CREDENTIAL
CREATE CREDENTIAL FOR COMPONENT 'SAPHANAFEDERATION' PURPOSE 'H1' TYPE 'KERBEROS';
CREATE CREDENTIAL FOR COMPONENT 'INTERNAL_APP' PURPOSE 'COMPANY_MASTER_MACHINE' TYPE 'PASSWORD' USING 'PASSWORD_9876';
ALTER CREDENTIAL FOR COMPONENT 'INTERNAL_APP' PURPOSE 'COMPANY_MASTER_MACHINE' TYPE 'PASSWORD' USING '1234_PASSWORD';
DROP CREDENTIAL FOR USER WORKER COMPONENT 'INTERNAL_APP' PURPOSE 'COMPANY_MASTER_MACHINE' TYPE 'PASSWORD';

-- function
DROP FUNCTION my_func;

CREATE FUNCTION Scale (val INT)
 RETURNS TABLE (a INT, b INT) LANGUAGE SQLSCRIPT AS
 BEGIN
     RETURN SELECT a, :val * b AS  b FROM mytab;
 END;

 CREATE FUNCTION func_add_mul(x Double, y Double) 
 RETURNS result_add Double, result_mul Double 
 LANGUAGE SQLSCRIPT READS SQL DATA AS
 BEGIN
     result_add := :x + :y;
     result_mul := :x * :y;
 END;

 CREATE FUNCTION func_mul(input1 INT)
 RETURNS output1 INT LANGUAGE SQLSCRIPT AS
 BEGIN
     output1 := :input1 * :input1;
 END;

 CREATE FUNCTION func_mul_wrapper(input1 INT)
 RETURNS output1 INT LANGUAGE SQLSCRIPT AS
 BEGIN
     output1 := func_mul(:input1);
 END;

CREATE FUNCTION FuncHeader (input1 integer) RETURNS  output1  integer AS HEADER ONLY;

ALTER FUNCTION FuncHeader (input1 integer) RETURNS  output1  integer 
AS
BEGIN 
  output1 := :input1 * :input1;
END;

CREATE FUNCTION FUNC RETURNS TABLE (COL1 INT, COL2 INT) AS
BEGIN
    RETURN SELECT * FROM TAB;
END
WITH CACHE RETENTION 10;

CREATE FUNCTION hooi1 RETURNS TABLE(i int)
    AS HEADER ONLY ON INVALID BEGIN
        RETURN SELECT i FROM T2;
    END;

 CREATE FUNCTION scale (val INT)
 RETURNS TABLE (a INT, b INT) LANGUAGE SQLSCRIPT AS
 BEGIN
    RETURN SELECT a, :val * b AS  b FROM mytab;
 END;

CREATE FUNCTION hooi2 RETURNS TABLE(i int)
        AS BEGIN
        RETURN SELECT i FROM hooi1();
        END;

 CREATE FUNCTION func_add_mul(x Double, y Double) 
 RETURNS result_add Double, result_mul Double 
 LANGUAGE SQLSCRIPT READS SQL DATA AS
 BEGIN
     result_add = :x + :y;
     result_mul = :x * :y;
 END;

ALTER FUNCTION function_a ADD STATIC CACHE RETENTION 10;
ALTER FUNCTION function_a ALTER CACHE RETENTION 20;
ALTER FUNCTION function_a ALTER CACHE ADD AT LOCATION 'lbsrv18:31576';
ALTER FUNCTION function_a ALTER CACHE DROP AT LOCATION 'lbsrv18:31576';
ALTER FUNCTION function_a DROP CACHE;
ALTER FUNCTION function_a RETURNS TABLE (COL1 INT, COL2 INT)
    AS HEADER ONLY ON INVALID BEGIN
        RETURN SELECT * FROM T1;
    END;

CREATE VIRTUAL FUNCTION MYSCHEMA1.VTUDF1 AT "HANA1"."DB1"."MYSCHEMA2"."TUDF1";

CREATE VIRTUAL FUNCTION  v_tpch10() RETURNS TABLE
        ("c_custkey" integer , "c_name"  varchar (255), "revenue"  double ,
        "c_acctbal" double , "n_name"  varchar (255), "c_address" varchar (255),
        "c_phone"  varchar (255), "c_comment"  varchar (255))
        PACKAGE SYSTEM."tpch10" CONFIGURATION'mapred_jobchain=[{"mapred_input":"/user/hive/warehouse/tpch.db/orders,/user/hive/warehouse/tpch.db/customer",
     "mapred_mapper":"com.sap.hana.hadoop.samples.TestQuery5$Map",
     "mapred_reducer":"com.sap.hana.hadoop.samples.TestQuery5$Reduce"},
     {"mapred_input":"result{0},/user/hive/warehouse/tpch.db/lineitem",
     "mapred_mapper":"com.sap.hana.hadoop.samples.TestQuery4$Map",
     "mapred_reducer":"com.sap.hana.hadoop.samples.TestQuery4$Reduce"},
     {"mapred_input":"result{1},/user/hive/warehouse/tpch.db/nation",
     "mapred_mapper":"com.sap.hana.hadoop.samples.TestQuery3$Map",
     "mapred_reducer":"com.sap.hana.hadoop.samples.TestQuery3$Reduce"},
     {"mapred_input":"result{2}","mapred_mapper":"com.sap.hana.hadoop.samples.TestQuery2$Map",
     "mapred_reducer":"com.sap.hana.hadoop.samples.TestQuery2$Reduce"},
     {"mapred_input":"result{3}","mapred_mapper":"com.sap.hana.hadoop.samples.TestQuery1$Map",
     "mapred_reducer":"com.sap.hana.hadoop.samples.TestQuery1$Reduce",
     "mapreduce.partitioner.class":"org.apache.hadoop.mapred.lib.KeyFieldBasedPartitioner",
     "mapred.output.key.comparator.class":"org.apache.hadoop.mapred.lib.KeyFieldBasedComparator",
     "mapreduce.partition.keycomparator.options":"-k1,1nr",
     "mapreduce.map.output.key.field.separator":"|"}]'
        AT HOSTA1;

-- procedure
DROP PROCEDURE my_proc;

CREATE PROCEDURE orchestrationProc
 LANGUAGE SQLSCRIPT AS
 BEGIN
   DECLARE v_id BIGINT;
   DECLARE v_name VARCHAR(30);
   DECLARE  v_pmnt BIGINT;
   DECLARE v_msg VARCHAR(200);
   DECLARE CURSOR c_cursor1 (p_payment BIGINT) FOR
     SELECT id, name, payment FROM control_tab
       WHERE payment > :p_payment ORDER BY id ASC;
   CALL init_proc();
   OPEN c_cursor1(250000);
   FETCH c_cursor1 INTO v_id, v_name, v_pmnt; v_msg := :v_name || ' (id ' || :v_id || ') earns ' || :v_pmnt || ' $.';
   CALL ins_msg_proc(:v_msg);
   CLOSE c_cursor1;
 END;

CREATE PROCEDURE ProcWithResultView(IN id INT, OUT o1 CUSTOMER)
 LANGUAGE SQLSCRIPT
 READS SQL DATA WITH RESULT VIEW ProcView AS
 BEGIN
   o1 = SELECT * FROM CUSTOMER WHERE CUST_ID = :id;
 END;

CREATE PROCEDURE ProcHeader (IN id INTEGER) AS HEADER ONLY;

CREATE PROCEDURE Proc1() READS SQL DATA AS
  BEGIN
      EXEC 'SELECT * FROM DUMMY';
  END;

 CREATE PROCEDURE Proc1(IN A NVARCHAR(12)) AS
  BEGIN
      EXECUTE IMMEDIATE 'SELECT * FROM ' || :A READS SQL DATA;
 END;

CREATE PROCEDURE GET_PROCEDURES(OUT procedures TABLE(schema_name NVARCHAR(256), name NVARCHAR(256)))

AS
BEGIN 
   procedures = SELECT schema_name AS schema_name, procedure_name AS name FROM PROCEDURES; 
END;

ALTER PROCEDURE GET_PROCEDURES( OUT procedures TABLE(schema_name NVARCHAR(256), name NVARCHAR(256)))

AS
BEGIN 
   procedures = SELECT schema_name AS schema_name, procedure_name AS name FROM PROCEDURES WHERE IS_VALID = 'TRUE'; 
END;

CREATE VIRTUAL PROCEDURE SYSTEM.FINDNGRAMS(
    IN N INT,
    OUT NGRAMS TABLE(STR TEXT)
    )
    LANGUAGE SCALASPARK
    AT SPARK_OAKL
    AS
    BEGIN
    import sqlContext.implicits._
    import scala.collection.mutable.WrappedArray
    import org.apache.spark.ml.feature.NGram
    // $example on$
    val wordDataFrame = sqlContext.createDataFrame(Seq(
    (0, Array("Hi", "I", "heard", "about", "Spark")),
    (1, Array("I", "wish", "Java", "could", "use", "case", "classes")),
    (2, Array("Logistic", "regression", "models", "are", "neat"))
    )).toDF("id", "words")

    val ngram = new NGram().setN(N).setInputCol("words").setOutputCol("ngrams")

    val ngramDataFrame = ngram.transform(wordDataFrame)
    ngramDataFrame.select("ngrams").show(false)

    NGRAMS = ngramDataFrame.select("ngrams").
    map(y=>y(0).asInstanceOf[WrappedArray[_]].
    mkString(",")).toDF
    END;

ALTER PROCEDURE my_proc RECOMPILE;

-- type
CREATE TYPE "my_type" AS TABLE ( "column_a" DOUBLE );
CREATE TYPE tt_publishers AS TABLE (
        publisher INTEGER,
        name VARCHAR(50),
        price DECIMAL,
        cnt INTEGER);
DROP TYPE "my_type";

-- database
CREATE DATABASE DB0 SYSTEM USER PASSWORD Manager1;
CREATE DATABASE DB1 AT LOCATION 'HOST_A:30147' SYSTEM USER PASSWORD Manager1;
CREATE DATABASE DB2 AT LOCATION 'HOST_A' ADD 'indexserver' AT 'HOST_B' ADD 'scriptserver' SYSTEM USER PASSWORD Manager1;

ALTER DATABASE my_database ADD 'scriptserver' AT LOCATION 'hostA:30303';
ALTER DATABASE my_database ALTER 'scriptserver' AT 'hostA:30303' TO '30340';
ALTER DATABASE my_database OS USER 'KPC28' OS GROUP 'admin';
ALTER DATABASE my_database FINALIZE REPLICA DROP SOURCE DATABASE;
ALTER DATABASE my_database CANCEL REPLICA;
ALTER DATABASE my_database SYSTEM USER PASSWORD fhu37656;
ALTER DATABASE my_database NO RESTART;
ALTER DATABASE my_database ENCRYPTION CONFIGURATION CONTROLLED BY LOCAL DATABASE;
ALTER DATABASE my_database LOG ENCRYPTION ON;
ALTER DATABASE my_database DROP FALLBACK SNAPSHOT;

DROP DATABASE my_database;
RENAME DATABASE MYOPS TO MYOPSDEV;