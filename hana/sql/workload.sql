CREATE WORKLOAD CLASS "MyWorkloadClass"
   SET 'PRIORITY' = '3', 'STATEMENT MEMORY LIMIT' = '2', 'STATEMENT THREAD LIMIT' = '20';
CREATE WORKLOAD MAPPING "MyMapping1" WORKLOAD CLASS "MyClass" SET 'CLIENT'='001';
CREATE WORKLOAD MAPPING "MyMapping2"
   WORKLOAD CLASS "MyClass" SET 'CLIENT'='002';
CREATE WORKLOAD CLASS "MyClass1"
   SET 'PRIORITY' = '0',
   'STATEMENT MEMORY LIMIT' = '3',
   'STATEMENT THREAD LIMIT' = '10';
CREATE WORKLOAD MAPPING "MyMapping3"
   WORKLOAD CLASS "MyClass1" SET 'CLIENT'='003';
CREATE WORKLOAD CLASS class1 SET 'TOTAL STATEMENT MEMORY LIMIT'='30', 'TOTAL STATEMENT THREAD LIMIT'='50', 'PRIORITY' = '5';
CREATE WORKLOAD CLASS class1 SET 'WRITE TRANSACTION LIFETIME' = '10', 'IDLE CURSOR LIFETIME' = '11';
CREATE WORKLOAD CLASS class2 SET 'ADMISSION CONTROL REJECT CPU THRESHOLD' = '0', 'ADMISSION CONTROL REJECT MEMORY THRESHOLD' = '0';
CREATE WORKLOAD CLASS class3 SET 'ADMISSION CONTROL REJECT CPU THRESHOLD' = '100', 'ADMISSION CONTROL REJECT MEMORY THRESHOLD' = '100';
CREATE WORKLOAD CLASS class4 SET 'ADMISSION CONTROL REJECT CPU THRESHOLD' = '70', 'ADMISSION CONTROL REJECT MEMORY THRESHOLD' = '80';
CREATE WORKLOAD CLASS "PARENT_CLS" SET 'TOTAL STATEMENT MEMORY LIMIT' = '40';
CREATE WORKLOAD CLASS "CHILD_CLS" PARENT "PARENT_CLS" SET 'STATEMENT MEMORY LIMIT' = '10'; 
CREATE WORKLOAD MAPPING "MyWorkloadMapping1" WORKLOAD CLASS "MyWorkloadClass" SET 'USER NAME' = 'ABCADM', 'APPLICATION NAME' = 'BW';
CREATE WORKLOAD MAPPING "MyWorkloadMapping1" WORKLOAD CLASS "MyWorkloadClass" SET 'APPLICATION NAME' = 'BW%' WITH WILDCARD;
CREATE WORKLOAD MAPPING "MyWorkloadMapping1" WORKLOAD CLASS "MyWorkloadClass" SET 'APPLICATION NAME' = 'BW%123' WITH WILDCARD;
CREATE WORKLOAD MAPPING "MyWorkloadMapping1" WORKLOAD CLASS "MyWorkloadClass" SET 'APPLICATION NAME' = 'BW%*123' WITH WILDCARD '*';
CREATE WORKLOAD MAPPING "MyWorkloadMapping1" WORKLOAD CLASS "MyWorkloadClass" SET 'USERGROUP NAME' = 'USERGROUP_TEST';
CREATE WORKLOAD MAPPING "MyWorkloadMapping1" WORKLOAD CLASS "DATAMART" SET 'SCHEMA NAME' = 'SYS', 'OBJECT NAME' = 'CHECK_ES';
CREATE WORKLOAD MAPPING "MyWorkloadMapping1" WORKLOAD CLASS "DATAMART" SET 'XS APPLICATION USER NAME' = 'TESTER';

ALTER WORKLOAD CLASS "MyWorkloadClass"
   UNSET 'STATEMENT MEMORY LIMIT'
   SET 'PRIORITY' = '5';
ALTER WORKLOAD CLASS ALL DISABLE;
ALTER WORKLOAD CLASS ALL ENABLE;
ALTER WORKLOAD CLASS "MyClass" DISABLE; 
ALTER WORKLOAD CLASS "MyClass" ENABLE;
ALTER WORKLOAD MAPPING "MyWorkloadMapping" WORKLOAD CLASS "MyWorkloadClass"
   SET 'USER NAME' = 'ABCADM' UNSET 'APPLICATION NAME';
ALTER WORKLOAD MAPPING "MyWorkloadMapping1" WORKLOAD CLASS "MyWorkloadClass" SET 'APPLICATION NAME' = 'BW%' WITH WILDCARD;
ALTER WORKLOAD MAPPING "MyWorkloadMapping1" WORKLOAD CLASS "MyWorkloadClass" SET 'APPLICATION NAME' = 'BW%*';

DROP WORKLOAD CLASS "MyWorkloadClass";
DROP WORKLOAD MAPPING "MyWorkloadMapping";