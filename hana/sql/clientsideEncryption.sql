CREATE CLIENTSIDE ENCRYPTION COLUMN KEY myschema.hrapp_cek1 ALGORITHM 'AES-256-CBC' ENCRYPTED WITH KEYPAIR key_admin_ckp;
CREATE CLIENTSIDE ENCRYPTION COLUMN KEY hrapp_cek2 ALGORITHM 'AES-256-CBC' HEADER ONLY;
CREATE CLIENTSIDE ENCRYPTION KEYPAIR user1_ckp ALGORITHM 'RSA-OAEP-2048';
CREATE CLIENTSIDE ENCRYPTION COLUMN KEY hrapp_cek2 HEADER ONLY;
CREATE CLIENTSIDE ENCRYPTION KEYPAIR Ckp1 ALGORITHM 'RSA-OAEP-2048';


ALTER CLIENTSIDE ENCRYPTION COLUMN KEY hrapp_cek1 ADD KEYCOPY ENCRYPTED WITH KEYPAIR user1_ckp;
ALTER CLIENTSIDE ENCRYPTION COLUMN KEY hrapp_cek1 DROP KEYCOPY ENCRYPTED WITH KEYPAIR user1_ckp;
ALTER CLIENTSIDE ENCRYPTION COLUMN KEY hrapp_cek2 ENCRYPTED WITH KEYPAIR user1_ckp;
ALTER CLIENTSIDE ENCRYPTION COLUMN KEY hrapp_cek2 ADD NEW VERSION;
ALTER CLIENTSIDE ENCRYPTION COLUMN KEY hrapp_cek2 DROP OLD VERSIONS;
ALTER CLIENTSIDE ENCRYPTION KEYPAIR Ckp1 ADD NEW VERSION; 
ALTER CLIENTSIDE ENCRYPTION KEYPAIR Ckp1 DROP OLD VERSIONS;

DROP CLIENTSIDE ENCRYPTION COLUMN KEY mychema.hrapp_cek1;
DROP CLIENTSIDE ENCRYPTION KEYPAIR user1_ckp;