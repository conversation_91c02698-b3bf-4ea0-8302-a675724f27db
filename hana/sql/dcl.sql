-- PROVIDER
CREATE JWT PROVIDER my_jwt_provider WITH ISSUER 'www/url/my_url'
   CLAIM 'user1' AS EXTERNAL IDENTITY CASE SENSITIVE IDENTITY;
CREATE JWT PROVIDER prov_a WITH ISSUER 'http://xsuaa' CLAIM 'origin' = 'http://customerA' CLAIM 'aud' HAS MEMBER 'app1' CLAIM 'sub' AS EXTERNAL IDENTITY;
CREATE JWT PROVIDER prov_b WITH ISSUER 'http://xsuaa' CLAIM 'sub' AS EXTERNAL IDENTITY CLAIM 'appuser' AS APPLICATION USER PRIORITY 110;
ALTER JWT PROVIDER my_jwt_provider SET ISSUER 'www/url/my_new_url';
ALTER JWT PROVIDER my_jwt_provider SET CLAIM 'user2' AS EXTERNAL IDENTITY;
ALTER JWT PROVIDER my_jwt_provider CASE INSENSITIVE IDENTITY;
DROP JWT PROVIDER my_jwt_provider2 CASCADE;

CREATE LDAP PROVIDER my_ldap_provider
   CREDENTIAL TYPE 'PASSWORD' USING 'user=cn=LookupAccount,o=largebank.com;password=secret'
   USER LOOKUP URL 'ldap://myhostname:389/ou=Users,dc=largebank,dc=com??sub?(&(objectClass=user)(sAMAccountName=*))'
   ATTRIBUTE DN 'distinguishedName'
   ATTRIBUTE MEMBER_OF 'memberOf'
   SSL ON
   DEFAULT ON
   ENABLE PROVIDER;
CREATE LDAP PROVIDER my_ldap_provider
 CREDENTIAL TYPE 'PASSWORD' USING 'user=cn=LookupAccount,o=largebank.com;password=secret'
 USER LOOKUP URL 'ldap://myhostname:389/ou=Users,dc=largebank,dc=com??sub?(&(objectClass=user)(sAMAccountName=*))'
 NESTED GROUP LOOKUP URL 'ldap://myhostname:389/ou=groupsOU,dc=x??sub?(member:1.2.840.113556.1.4.1941:=*)'
 ATTRIBUTE DN 'distinguishedName'
 SSL ON
 DEFAULT ON
 ENABLE PROVIDER;
CREATE LDAP PROVIDER my_ldap_provider
 CREDENTIAL TYPE 'PASSWORD' USING 'user=cn=LookupAccount,o=largebank.com;password=secret'
 USER LOOKUP URL 'ldap://myhostname:389/ou=Users,dc=largebank,dc=com??sub?(&(objectClass=user)(sAMAccountName=*))'
 ATTRIBUTE DN 'distinguishedName'
 SSL ON
 DEFAULT ON
 ENABLE PROVIDER
 ENABLE USER CREATION FOR LDAP;
CREATE LDAP PROVIDER my_ldap_provider
  CREDENTIAL TYPE 'PASSWORD' USING 'user=cn=LookupAccount,o=largebank.com;password=secret'
  USER LOOKUP URL 'ldap://myhostname:389/ou=Users,dc=largebank,dc=com??sub?(&(objectClass=user)(sAMAccountName=*))'
  ATTRIBUTE DN 'distinguishedName'
  SSL ON
  DEFAULT ON
  ENABLE PROVIDER
  ENABLE USER CREATION FOR LDAP USER TYPE RESTRICTED;
ALTER LDAP PROVIDER my_ldap_provider
  USER LOOKUP URL 'ldap://myhostname:389/ou=Users,dc=largebank,dc=com??sub? (&(objectClass=user)(sAMAccountName=*))';
ALTER LDAP PROVIDER my_ldap_provider DEFAULT ON ENABLE PROVIDER;
ALTER LDAP PROVIDER my_ldap_provider
 NESTED GROUP LOOKUP URL 'ldap://myhostname:389/ou=groupsOU,dc=x?sub?(member:1.2.840.113556.1.4.1941:=*)';
ALTER LDAP PROVIDER my_ldap_provider
 NESTED GROUP LOOKUP URL NULL
 ATTRIBUTE MEMBER_OF 'memberOf';
ALTER LDAP PROVIDER my_ldap_provider ENABLE USER CREATION FOR LDAP;
ALTER LDAP PROVIDER my_ldap_provider
  ENABLE USER CREATION FOR LDAP USER TYPE RESTRICTED;
ALTER LDAP PROVIDER my_ldap_provider DISABLE USER CREATION FOR LDAP;
DROP LDAP PROVIDER my_ldap_provider;

CREATE SAML PROVIDER ac_saml_provider 
    WITH SUBJECT 'CN = wiki.detroit.ACompany.corp,OU = ACNet,O = ACompany,C = EN' 
    ISSUER 'E = <EMAIL>,CN = ACNetCA,OU = ACNet,O = ACompany,C = EN'
    ENTITY ID 'entity1' CASE SENSITIVE IDENTITY;
ALTER SAML PROVIDER ac_saml_provider
        SET SUBJECT 'CN = wiki.detroit.BCompany.corp,OU = BCNet,O = BCompany,C = EN'
        ISSUER 'E = <EMAIL>,CN = BCNetCA,OU = BCNet,O = BCompany,C = EN';
ALTER SAML PROVIDER ac_saml_provider ENABLE USER CREATION;
ALTER SAML PROVIDER ac_saml_provider CASE INSENSITIVE IDENTITY;
ALTER SAML PROVIDER ac_saml_provider UNSET ENTITY ID;
DROP SAML PROVIDER ac_saml_provider2 CASCADE;

VALIDATE LDAP PROVIDER my_ldap_provider CHECK USER johnd;
VALIDATE LDAP PROVIDER my_ldap_provider CHECK USER testuser1 PASSWORD 'Sap123456';
VALIDATE LDAP PROVIDER my_ldap_provider CHECK USER testuser2 NO AUTHORIZATION CHECK;


-- REMOTE SOURCE
CREATE REMOTE SOURCE HOSTA ADAPTER hadoop 
    CONFIGURATION 'webhdfs.url=http://hosta:50070/;webhcat.url=http://host:50111' 
    WITH CREDENTIAL TYPE 'PASSWORD' USING 'user=dbuser;password=DBtest123';
CREATE REMOTE SOURCE HOSTB ADAPTER hanaodbc
    CONFIGURATION 'Driver=libodbcHDB.so;ServerNode=my_hanaserver:30115;' 
    WITH CREDENTIAL TYPE 'KERBEROS';
CREATE REMOTE SOURCE HOSTC ADAPTER hanaodbc
    CONFIGURATION 'Driver=livodbcHDB.so;ServerNode=my_hanaserver:30115,my_failover_hanaserver:30215;
    sessionVariable:APPLICATIONUSER=?;linkeddatabase_mode=optimized]'
    WITH CREDENTIAL TYPE 'PASSWORD' USING 'user=dbuser;password=DBtest123';
ALTER REMOTE SOURCE MY_REMOTE1 ADAPTER HANAODBC CONFIGURATION 'DSN=MYHANA1' 
   WITH CREDENTIAL TYPE 'PASSWORD' USING 'user="user1";password="Password1"';
ALTER REMOTE SOURCE MY_REMOTE1 DROP CREDENTIAL TYPE 'PASSWORD';
ALTER REMOTE SOURCE MY_REMOTE1 DROP LINKED OBJECTS CASCADE;
ALTER REMOTE SOURCE MY_REMOTE1 REFRESH LINKED OBJECTS;
ALTER REMOTE SOURCE MY_REMOTE REFRESH LINKED TABLE MY_REMOTE1.MYSCHEMA.MYTABLE;
ALTER REMOTE SOURCE MY_REMOTE1 ADAPTER HANAODBC
     CONFIGURATION 'Driver=libodbcHDB.so;ServerNode=my_machine1:30115,failover_machine1:30215
     sessionVariable:APPLICATIONUSER=?;linkeddatabase_mode=optimized]';
ALTER REMOTE SOURCE RS1 SET PROPERTY 'CAP_LIMIT' = 'true', 'PROP_USE_UNIX_MANAGER'= 'true';
ALTER REMOTE SOURCE RS1 UNSET PROPERTY ALL;

-- role
CREATE ROLE role_for_work_on_my_schema NO GRANT TO CREATOR;
CREATE ROLE Securities_DBA 
 LDAP GROUP 'cn=Securities_DBA,OU=Application,OU=Groups,ou=DatabaseAdmins,cn=Users,o=largebank.com';
ALTER ROLE Securities_DBA 
   ADD LDAP GROUP 'cn=Securities_DBA,OU=Application,OU=Groups,ou=DatabaseAdmins,cn=Users,o=verylargebank.com';
DROP ROLE role_for_work_on_my_schema;

-- user
CREATE USER T12345 PASSWORD Password123;
CREATE USER new_user PASSWORD Password1 WITH IDENTITY ANY FOR SAML PROVIDER ac_saml_provider;
CREATE USER testuser WITH IDENTITY 'C=US, ST=VA, L=Fairfax, O=example.com, CN=testuser' ISSUER 'C=US, ST=VA, L=Fairfax, O=example.com, CN=Root CA' FOR X509;
CREATE USER testuser PASSWORD BadPassword5 WITH IDENTITY 'C=US, ST=VA, L=Fairfax, O=example.com, CN=testuser' ISSUER 'C=US, ST=VA, L=Fairfax, O=example.com, CN=Root CA' FOR X509;
CREATE USER testuser WITH IDENTITY 'C=US, ST=VA, L=Fairfax, O=example.com, CN=testuser' ISSUER 'C=US, ST=VA, L=Fairfax, O=example.com, CN=Root CA' FOR X509 VALID UNTIL '2028-01-01';
CREATE USER testuser WITH IDENTITY ANY FOR SAML PROVIDER nonexistandsamlprovider;
CREATE USER USER1 WITH REMOTE IDENTITY USER2 AT DATABASE DB2;

ALTER USER new_user FORCE PASSWORD CHANGE;
ALTER USER new_user DISABLE SAML;
ALTER USER new_user RESET CONNECT ATTEMPTS;
ALTER USER new_user ADD IDENTITY 'testkerberosName' FOR KERBEROS;
ALTER USER new_user ENABLE KERBEROS;
ALTER USER new_user DROP IDENTITY FOR SAML PROVIDER ac_saml_provider;
ALTER USER new_user DEACTIVATE;

DROP USER new_user CASCADE;
VALIDATE USER BJones PASSWORD 34875hgj75;
VALIDATE USER JSmith PASSWORD shg8475ghh11;
VALIDATE USER T12345 PASSWORD shgi33shi;
VALIDATE USER WITH ASSERTION 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************.TJVA95OrM7E2cBab30RMHrHDcEfxjoYZgeFONFh7HgQ';

-- userGroup
CREATE USERGROUP MyUserGroup DISABLE USER ADMIN NO GRANT TO CREATOR;
CREATE USERGROUP MyUserGroup SET PARAMETER 'password_layout' = 'A1a!', 'minimal_password_length' = '16' ENABLE PARAMETER SET 'password policy';
CREATE USERGROUP MyUserGroup DISABLE CLIENT CONNECT;

ALTER USERGROUP MyUserGroup ENABLE USER ADMIN;
ALTER USERGROUP MyUserGroup DISABLE PARAMETER SET 'password policy';
ALTER USERGROUP MyUserGroup ENABLE CLIENT CONNECT;
ALTER USERGROUP MyUserGroup DISABLE CLIENT CONNECT;

DROP USERGROUP MyUserGroup;

-- grant && revoke
GRANT SELECT ON SCHEMA my_schema TO role_for_work_on_my_schema;
GRANT INSERT ON my_schoolwork_done TO role_for_work_on_my_schema;
GRANT role_for_work_on_my_schema TO worker WITH ADMIN OPTION;
GRANT DELETE ON my_schoolwork_done TO worker;
GRANT CREATE ANY ON SCHEMA my_schema TO worker;
GRANT INIFILE ADMIN, TRACE ADMIN TO worker WITH ADMIN OPTION;
GRANT LINKED DATABASE ON REMOTE SOURCE myremotesys TO myuser1;
GRANT USAGE ON CLIENTSIDE ENCRYPTION COLUMN KEY my_cek TO User1 WITH GRANT OPTION;
GRANT CLIENTSIDE ENCRYPTION COLUMN KEY ADMIN ON SCHEMA MySchema TO KeyAdministrator;

REVOKE SELECT ON SCHEMA my_schema FROM role_for_work_on_my_schema;
REVOKE TRACE ADMIN FROM worker;
REVOKE LINKED DATABASE ON REMOTE SOURCE myremotesys FROM myuser1;
REVOKE USAGE ON CLIENTSIDE ENCRYPTION COLUMN KEY Key5 FROM bsmith;
REVOKE CLIENTSIDE ENCRYPTION COLUMN KEY ADMIN ON SCHEMA my_schema FROM bsmith;