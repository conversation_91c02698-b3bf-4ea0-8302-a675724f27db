BACKUP CANCEL 1331715084250;
BACKUP CATALOG DELETE ALL BEFORE BACKUP_ID 1496915612668 COMPLETE;
BACKUP CATALOG DELETE FOR PR1 ALL BEFORE BACKUP_ID 1496915612668 COMPLETE;
BACKUP CATALOG DELETE BACKUP_ID 1496915612668;
BACKUP CHECK USING FILE ('/backup/data') SIZE 53687091200;
BACKUP DATA FOR FULL SYSTEM CLOSE SNAPSHOT BACKUP_ID 1400761459486 SUCCESSFUL 'External_ID_23';
BACKUP DATA FOR FULL SYSTEM CLOSE SNAPSHOT BACKUP_ID 1400761459486 UNSUCCESSFUL 'Do not use - was manually terminated';
BACKUP DATA FOR FULL SYSTEM CREATE SNAPSHOT COMMENT 'MySnapshot';
BACKUP DATA FOR FULL SYSTEM DROP SNAPSHOT;
BACKUP ENCRYPTION ROOT KEYS USING FILE ('backup/sec/backup001');
BACKUP ENCRYPTION ROOT KEYS FOR testDB USING FILE ('backup001');
RECOVER DATA USING FILE ('/backup/THURSDAY') CLEAR LOG;
RECOVER DATA FOR Tenant_1 USING SOURCE 'Tenant_0@PR1' USING BACKINT('2020-11-26') CLEAR LOG;
RECOVER DATA FOR PR2 USING SOURCE 'PR1@PR1' USING BACKUP_ID 1591709192198 USING DATA PATH ('/hana/PR1/backup/data') CLEAR LOG;
RECOVER DATA FOR PR2 USING SOURCE 'PR1@PR1' USING BACKUP_ID 1591709192198 USING CATALOG PATH ('/hana/PR1/backup/catalog') CLEAR LOG;
RECOVER DATABASE FOR TENANT_1 UNTIL TIMESTAMP '2020-10-21 15:00:00' CHECK ACCESS ALL;
RECOVER DATABASE FOR TENANT_1 UNTIL TIMESTAMP '2020-10-21 15:00:00' USING DATA PATH ('/backup/WEDNESDAY/') USING LOG PATH ('/backup/logs1/','/backup/logs2/');
RECOVER DATABASE UNTIL TIMESTAMP '2020-10-21 15:00:00' USING SNAPSHOT;
RECOVER DATABASE FOR TENANT_1 UNTIL TIMESTAMP '2027-10-21 15:00:00' USING SNAPSHOT;
RECOVER DATABASE FOR TENANT_1 UNTIL TIMESTAMP '2020-10-21' USING CATALOG PATH ('/hana/PR1/backup/catalog') USING LOG PATH ('/hana/HHB/backup/log') CHECK ACCESS ALL;
RECOVER DATABASE FOR TENANT_1 UNTIL LOG POSITION 7553856 at volume 2 CLEAR LOG USING GUID '41D53842-FA51-0017-3527-F4A470000005';
RECOVER DATABASE FOR PR1
				UNTIL TIMESTAMP '2020-10-21 05:00:00'
				CLEAR LOG
				USING CATALOG BACKINT
				USING LOG PATH ('/usr/sap/PR1/HDB/backup/log/DB_PR1')
				USING DATA PATH ('/usr/sap/PR1/SYS/global/hdb/backint/DB_PR1/')
				USING BACKUP_ID 1540499452637
				CHECK ACCESS USING FILE;
RECOVER DATABASE FOR PR1
				UNTIL TIMESTAMP '2020-10-21 05:00:00'
				CLEAR LOG
				USING CATALOG BACKINT
				USING RESUME
				CHECK ACCESS USING FILE;
RECOVER DATABASE FOR PR2 UNTIL TIMESTAMP '2020' CLEAR LOG USING SOURCE 'PR1@PR1' USING DATA PATH ('/hana/PR1/backup/data');
RECOVER DATABASE FOR PR2 UNTIL TIMESTAMP '2020' CLEAR LOG USING SOURCE 'PR1@PR1' USING CATALOG PATH ('/hana/PR1/backup/catalog');
RECOVER ENCRYPTION ROOT KEYS FOR testDB USING FILE ('/backup/HDB/rootkeys/backup001') PASSWORD MyBackupPwd123 SECURE STORE SECOND ACCESS PRIVATE KEY '-----BEGIN ENCRYPTED PRIVATE KEY-----\nMIIFH... \n-----END ENCRYPTED PRIVATE KEY-----' PASSWORD 'PasswordOfSecondAccessKey';
RECOVER ENCRYPTION ROOT KEYS FOR testDB USING FILE ('/backup/HDB/rootkeys/backup001') PASSWORD MyBackupPwd123 SECURE STORE SECOND ACCESS PRIVATE KEY '-----BEGIN PRIVATE KEY-----\nMIIFH... \n-----END PRIVATE KEY-----' PASSWORD '';



