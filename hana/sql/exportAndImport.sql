EXPORT INTO '/tmp/view_content.csv' FROM v;
EXPORT INTO '/tmp/table_with_header.csv' FROM t WITH COLUMN LIST IN FIRST ROW FIELD DELIMITED BY '!' ESCAPE '@';
EXPORT INTO 's3-eu-central-1://AKIAxxxxxxxxxx:xl6WWxxxxxxxxxx@imex-demo/tpch1_lineitem.csv' FROM TPCH1.LINEITEM WITH FIELD DELIMITED BY',' THREADS 4;
EXPORT CLIENTSIDE ENCRYPTION COLUMN KEY MYSCHEMA.MYCEK1, MYSCHEMA.MYCEK2 AS CSV INTO '/tmp/';
EXPORT MY_SCHEMA.TAB1 AS CSV INTO '/tmp' WITH REPLACE SCRAMBLE THREADS 10;
EXPORT a."*", b."*" AS CSV INTO '/tmp' WITH REPLACE;
EXPORT SYSTEM.TBL_A WHERE COL_A > 5 INTO '/tmp/' WITH REPLACE;
EXPORT SYSTEM.TBL_A WHERE COL_B LIKE '%ch%' AND MOD(COL_C, 2) = 0, SYSTEM.TBL_B INTO '/tmp/' WITH REPLACE;
EXPORT my_schema."*" INTO '/usr/sap/MS1/HDB01/work' WITH TRACE 'export.trc';
EXPORT ALL HAVING schema_name = 'my_schema' AND object_type = 'VIEW' AND object_name != 'V2'
    INTO '/usr/sap/MS1/HDB01/work/incexc' WITH REPLACE DEPENDENCIES object_name = 'T1';
EXPORT TPCH1."*" AS CSV INTO 's3-eu-central-1://AKIAxxxxxxxxxx:xl6WWxxxxxxxxxx@imex-demo/tpch1' WITH THREADS 4 REPLACE;
EXPORT T1 INTO '/tmp/my_demo' WITH ENCRYPTION PASSWORD xxx;

IMPORT FROM CSV FILE 'data.csv'
    INTO TEST.IMPORT_TABLE
    WITH THREADS 20
    BATCH 50000
    COLUMN LIST IN FIRST ROW WITH SCHEMA FLEXIBILITY
    RECORD DELIMITED BY '\n'
    FIELD DELIMITED BY ','
    OPTIONALLY ENCLOSED BY '"'
    DATE FORMAT 'YYYY-MM-DD'
    ERROR LOG 'error_log.txt';
IMPORT FROM CSV FILE '/data/data.csv' INTO "MYTABLE"
   WITH RECORD DELIMITED BY '\n'
   FIELD DELIMITED BY ',';
IMPORT DATA INTO TABLE "MYTABLE" FROM '/data/data.csv'
   RECORD DELIMITED BY '\n'
   FIELD DELIMITED BY ','
   OPTIONALLY ENCLOSED BY '"'
   ERROR LOG '/data/data.err';
IMPORT DATA INTO STATISTICS "My_Schema"."SKETCH5" FROM '/data/data'
 ERROR LOG '/data/data.err';
IMPORT FROM CSV FILE '/data/data_different_date.csv' INTO "MYTABLE" WITH RECORD
   DELIMITED BY '\n'
   FIELD DELIMITED BY ','
   DATE FORMAT 'MM-DD-YYYY';
IMPORT FROM CSV FILE '/data/data_col_list.csv' INTO "COLLIST"
   WITH RECORD DELIMITED BY '\n'
   FIELD DELIMITED BY ','
   COLUMN LIST ("A", "B", "C", "D");
IMPORT FROM CSV FILE '/data/data_col_list.csv' INTO "FLEX"
   WITH RECORD DELIMITED BY '\n'
   FIELD DELIMITED BY ','
   COLUMN LIST ("A", "B", "C", "D")
   WITH SCHEMA FLEXIBILITY;
IMPORT FROM CSV FILE 's3-eu-central-1://AKIAxxxxxxxxxx:xl6WWxxxxxxxxxx@imex-demo/tpch1_lineitem.csv' INTO TPCH1.LINEITEM WITH FIELD DELIMITED BY ',' THREADS 4;
IMPORT SCAN '/data/import_path';
IMPORT * AS SAP_TIMEZONE_DATASET FROM '/path/to/tzdata.dat';
IMPORT CLIENTSIDE ENCRYPTION COLUMN KEY MYSCHEMA.MYCEK1, MYSCHEAM.MYCEK2 FROM '/path/to/cek';
IMPORT TESTSCH."*" AS CSV FROM '/tmp' WITH REPLACE THREADS 10;
IMPORT MYSCHEMA.DATA AS LOAD_HISTORY FROM '/path/to/dump';
IMPORT MYSCHEMA.DATA AS LOAD_HISTORY FROM '/path/to/nameserver_history.trc';
IMPORT MY_SCHEMA."*" FROM '/usr/sap/MS1/HDB01/work' WITH TRACE 'import.trc' REPLACE;
IMPORT SYSTEM."T14" FROM '/tmp/test/' WITH REPLACE THREADS 40 IGNORE NUMA NODE;
IMPORT ALL HAVING schema_name = 'my_schema' AND object_type = 'VIEW' AND object_name != 'V2'
    FROM '/usr/sap/MS1/HDB01/work' WITH REPLACE DEPENDENCIES object_name = 'T1';
IMPORT ALL FROM 's3-eu-central-1://AKIAxxxxxxxxxx:xl6WWxxxxxxxxxx@imex-demo/tpch1' WITH THREADS 4 REPLACE;
IMPORT T1 FROM '/tmp/my_demo' WITH ENCRYPTION PASSWORD xxx;













