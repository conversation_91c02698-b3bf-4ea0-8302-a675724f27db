-- pse
ALTER PSE example_pse SET OWN CERTIFICATE
'-----BEGIN RSA PRIVATE KEY-----
MIIFDjBABgkqhkiG9w0BBQ0wMzAbBgkqhkiG9w0BBQwwDgQIS2qgprFqPxECAggA
.... MAN<PERSON> LINES LIKE THAT .... 
H0ga/iLNvWYexG7FHLRiq5hTj0g9mUPEbeTXuPtOkTEb/0ckVE2iZH9l7g5edmUZ=
-----END RSA PRIVATE KEY-----
-----B<PERSON>IN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAJC1HiIAZAiIMA0GCSqGSIb3DQEBBQUAMEUxCzAJBgNV
.... MANY LINES LIKE THAT .... 
B7xxt8BVc69rLeHV15A0qyx77CLSj3tCx2IUXVqRs5mlSbq094NBxsauYcm0A6Jq==
-----END CERTIFICATE-----
-----B<PERSON>IN CERTIFICATE-----
MIID4ppoB7xxAwIBAgIJAJCWgAIAZAiIMA0GCSqGSIb3DQEBBQUAMEUxCzAJ3tCx2
.... MANY LINES LIKE THAT .... 
A34ppoB7xxtanU8BVc69rLeHV15A0qyx77CLSj3tCx2IUXVqRs5mlSbuYcm0A6JvA==
-----END CERTIFICATE-----';
ALTER PSE pse1 ADD PROVIDER provider1;
ALTER PSE mypse2 ADD HOST 'host1.sap.corp';
ALTER PSE pse1 DROP PROVIDER provider1;
ALTER PSE mypse2 DROP HOST 'host1.sap.corp';
ALTER PSE saml_pse ADD CERTIFICATE saml_cert;
ALTER PSE HDB_PSE ADD REMOTE SOURCE HDB_ADM;

SET PSE HDB_PSE PURPOSE REMOTE SOURCE FOR REMOTE SOURCE HDB_SYS;
SET PSE example_pse PURPOSE SAP LOGON;
SET PSE PSE1 PURPOSE SSL FOR HOST 'host1.sap.corp';
SET PSE HDB_PSE PURPOSE REMOTE SOURCE FOR REMOTE SOURCE HDB_SYS;

-- system
ALTER SYSTEM ADD STATEMENT HINT (NO_CS_JOIN, NO_CS_UNION_ALL) FOR SELECT * FROM AAA, BBB WHERE AAA.X = BBB.X;
ALTER SYSTEM ALTER STATEMENT HINT COMMENT 'AAA related join' FOR SELECT * FROM AAA, BBB WHERE AAA.X = BBB.X;
ALTER SYSTEM ALTER STATEMENT HINT COMMENT 'AAA related join' FOR SELECT * FROM AAA, BBB WHERE AAA.X = BBB.X WITH HINT (NO_CS_JOIN);
ALTER SYSTEM REMOVE STATEMENT HINT FOR SELECT * FROM AAA, BBB WHERE AAA.X = BBB.X;
ALTER SYSTEM ADD STATEMENT HINT (NO_CS_JOIN) FOR STATEMENT HASH '36896ef08346b8321f88449d5c5e97f8';
ALTER SYSTEM REMOVE STATEMENT HINT FOR STATEMENT HASH '36896ef08346b8321f88449d5c5e97f8';
ALTER SYSTEM REMOVE STATEMENT HINT ALL FOR STATEMENT HASH '36896ef08346b8321f88449d5c5e97f8';
ALTER SYSTEM ADD STATEMENT HINT (NO_CS_JOIN, NO_CS_UNION_ALL) ON PROCEDURE MY_PROC FOR SELECT * FROM :TVAR AAA, BBB WHERE AAA.X = BBB.X;
ALTER SYSTEM ADD ABSTRACT SQL PLAN FILTER 'abc' SET 'APPLICATION NAME'='App1';
ALTER SYSTEM ADD ABSTRACT SQL PLAN FILTER 'def' SET 'APPLICATION NAME'='App2', 'APPLICATION USER NAME'='TEST';
ALTER SYSTEM REMOVE ABSTRACT SQL PLAN FILTER 'abc';
ALTER SYSTEM REMOVE ABSTRACT SQL PLAN FILTER ALL;
ALTER SYSTEM ALTER CONFIGURATION ('global.ini', 'SYSTEM') 
   SET ('alt_sys_test', 'new_test_value') = 'test';
ALTER SYSTEM ALTER CONFIGURATION ('indexserver.ini', 'DATABASE', 'C11') 
 SET ('memorymanager', 'allocationlimit') = '500000' 
 WITH RECONFIGURE 
 COMMENT 'Reverting to previous setting';
ALTER SYSTEM ALTER DATAVOLUME ADD PARTITION;
ALTER SYSTEM ALTER DATAVOLUME ADD PARTITION PATH '/main/datavolumes';
ALTER SYSTEM ALTER DATAVOLUME DROP PARTITION 1;
ALTER SYSTEM ALTER SESSION 11111 SET 'MY_VAR'= 'some_value';
ALTER SYSTEM ALTER SESSION 12345 UNSET 'MY_VAR';
ALTER SYSTEM ALTER TABLE PLACEMENT SET (LOCATION => 'slave');
ALTER SYSTEM ALTER TABLE PLACEMENT (SCHEMA_NAME => 'SAPKIT') SET (LOCATION => 'master');
ALTER SYSTEM ALTER TABLE PLACEMENT (SCHEMA_NAME => 'SAPKIT', GROUP_TYPE => 'sap.bw.dso') 
 SET (LOCATION => 'slave', MIN_ROWS_FOR_PARTITIONING => 40000000, REPARTITIONING_THRESHOLD => 40000000, INITIAL_PARTITIONS => 3);
ALTER SYSTEM ALTER TABLE PLACEMENT (SCHEMA_NAME => 'SAPKIT', GROUP_TYPE => 'sap.bw.dso') UNSET;
ALTER SYSTEM ALTER TABLE PLACEMENT (SCHEMA_NAME => 'SAPKIT', GROUP_TYPE => 'sap.bw.dso') UNSET (LOCATION);
ALTER SYSTEM ALTER TABLE PLACEMENT (SCHEMA_NAME=>'SYSTEM') SET (SAME_PARTITION_COUNT => 'TRUE');
ALTER SYSTEM ALTER TABLE PLACEMENT (SCHEMA_NAME=>'SYSTEM') UNSET (SAME_PARTITION_COUNT);
ALTER SYSTEM ALTER TABLE PLACEMENT LOCATION MyLocation SET (INCLUDE => '2,3', EXCLUDE => 'SLAVE');				
ALTER SYSTEM ALTER TABLE PLACEMENT LOCATION MyLocation SET (INCLUDE => '2,3', EXCLUDE => '');
ALTER SYSTEM ALTER TABLE PLACEMENT LOCATION MyLocation UNSET;
ALTER SYSTEM APPLICATION ENCRYPTION CREATE NEW KEY;
ALTER SYSTEM APPLICATION ENCRYPTION CREATE NEW KEY WITH ALGORITHM 'AES-256-CTR';
ALTER SYSTEM BACKUP ENCRYPTION CREATE NEW ROOT KEY WITHOUT ACTIVATE;
ALTER SYSTEM BACKUP ENCRYPTION CREATE NEW ROOT KEY WITH ALGORITHM 'AES-256-CTR';
ALTER SYSTEM CANCEL SESSION '<connection_id>';
ALTER SYSTEM CLEAR AUDIT LOG UNTIL '2012-12-31 23:59:59';
ALTER SYSTEM CLEAR AUDIT LOG FOR AUDIT POLICY MY_POLICY ALL;
ALTER SYSTEM CLEAR COLUMN JOIN DATA STATISTICS;
ALTER SYSTEM CLEAR TIMEZONE CACHE DATASET 'sap';
ALTER SYSTEM CLEAR TRACES('ALERT');
ALTER SYSTEM CLEAR TRACES('ALERT', 'CLIENT');
ALTER SYSTEM CLEAR TRACES('ALERT', 'CLIENT') WITH BACKUP;
ALTER SYSTEM CLEAR TRACES ('ALERT', 'CLIENT') UNTIL '2015-12-31 23:59:59';
ALTER SYSTEM CLEAR TRACES('indexserver') WITH BACKUP;
ALTER SYSTEM CREATE RUNTIMEDUMP AT LOCATION 'myhost:30003' SECTIONS ('STACK_SHORT') INTO FILE 'my_rte_dump.trc';
ALTER SYSTEM CREATE RUNTIMEDUMP PROFILE 'myRTEProfile' INTO FILE 'my_rte_dump.trc';
ALTER SYSTEM CREATE WAITGRAPH AT LOCATION 'myhost:30003' INTO FILE 'my_waitgraph.trc';
ALTER SYSTEM CREATE WAITGRAPH ALL THREADS INTO FILE 'my_waitgraph.trc';
ALTER SYSTEM DISCONNECT SESSION '<connection_id>';
ALTER SYSTEM REMOVE ABSTRACT SQL PLAN ENTRY ALL;
ALTER SYSTEM ENABLE ABSTRACT SQL PLAN ENTRY FOR 9630002;
ALTER SYSTEM ENABLE ALL SYNCHRONOUS TABLE REPLICAS;
ALTER SYSTEM DISABLE ALL ASYNCHRONOUS TABLE REPLICAS;
ALTER SYSTEM DISABLE ALL TABLE REPLICAS;
ALTER SYSTEM ENABLE STATEMENT HINT ALL;
ALTER SYSTEM DISABLE STATEMENT HINT FOR SELECT * FROM AAA, BBB WHERE AAA.X = BBB.X;
ALTER SYSTEM ENABLE STATEMENT HINT FOR STATEMENT HASH '36896ef08346b8321f88449d5c5e97f8';
ALTER SYSTEM DISABLE STATEMENT HINT FOR STATEMENT HASH '36896ef08346b8321f88449d5c5e97f8';
ALTER SYSTEM DISABLE STATEMENT HINT ON PROCEDURE MY_PROC FOR SELECT * FROM :TVAR AAA, BBB WHERE AAA.X = BBB.X;




-- CERTIFICATE
 CREATE CERTIFICATE FROM '-----BEGIN CERTIFICATE-----
MIIEVjCCAz6gAwIBAgIJAKZmSWxYxVmGMA0GCSqGSIb3DQEBBQUAMHkxCzAJBgNV
...
zn2Q+T5Og6ozD1WgUYsegJl3W2gNznEj66Ku1SDDzR0POjCnfK5xLt1WE5KBAIav
1SSbSTsw6rCRdg==
-----END CERTIFICATE-----' COMMENT 'Subject SAP AG Valid until 2285';
 CREATE CERTIFICATE SAP_CERT FROM '-----BEGIN CERTIFICATE-----
MIIEVjCCAz6gAwIBAgIJAKZmSWxYxVmGMA0GCSqGSIb3DQEBBQUAMHkxCzAJBgNV
...
zn2Q+T5Og6ozD1WgUYsegJl3W2gNznEj66Ku1SDDzR0POjCnfK5xLt1WE5KBAIav
1SSbSTsw6rCRdg==
-----END CERTIFICATE-----';
DROP CERTIFICATE SAP_CERT;

-- public key
CREATE PUBLIC KEY jwt_pubkey FROM '-----BEGIN PUBLIC KEY-----...-----END PUBLIC KEY-----' KEY ID HINT 'key1';
























