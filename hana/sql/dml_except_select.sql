-- explain
EXPLAIN PLAN SET STATEMENT_NAME = 'TPC-H Q10' FOR
SELECT TOP 20
      c_custkey,
        c_name,
       SUM(l_extendedprice * (1 - l_discount)) AS revenue,
       c_acctbal,
       n_name,
       c_address,
       c_phone,
       c_comment
FROM
    customer,
    orders,
    lineitem,
    nation
WHERE
        c_custkey = o_custkey
  AND l_orderkey = o_orderkey
  AND o_orderdate >= '1993-10-01'
  AND o_orderdate < ADD_MONTHS('1993-10-01',3)
  AND l_returnflag = 'R'
  AND c_nationkey = n_nationkey
GROUP BY
    c_custkey,
    c_name,
    c_acctbal,
    c_phone,
    n_name,
    c_address,
    c_comment
ORDER BY
    revenue DESC;

EXPLAIN PLAN SET STATEMENT_NAME = 'TPC-H Q10' FOR SQL PLAN CACHE ENTRY 2050002;

-- delete
DELETE FROM T WHERE KEY = 1;
DELETE FROM T1 PARTITION(1);
DELETE HISTORY FROM HIST WHERE KEY=1;
DELETE FROM T1 WHERE 3 MEMBER OF C1;
DELETE FROM departments WHERE CURRENT OF cur;
DELETE FROM T1 FOR PORTION OF APPLICATION_TIME FROM '2018-8-4' TO '2018-8-8' WHERE PKEY = 1;

-- insert
INSERT INTO T VALUES (1, 1, 'The first');
INSERT INTO T (KEY, VAL2) VALUES (2,3);
INSERT INTO T SELECT 3, 3, 'The third' FROM DUMMY;
INSERT INTO T1 VALUES ( 1, ARRAY ( 1, 2, 3, 4 ) );
INSERT INTO T1 VALUES ( 2, ARRAY( SELECT C1 FROM T0 ) );
INSERT INTO T1 (ID) VALUES (3);
INSERT INTO CXML_LOG_CONTENT AS t0 (t0.IS_COMPRESSED, CONTENT, t0.CONTENT_KEY, t0.CONTENT_NEED_ENCR, t0.CONTENT_SIZE, ID)
VALUES ('true', EMPTY_BLOB(), NULL, '0', '333', '1234');
INSERT INTO t1 WITH alias AS (SELECT * FROM t1) SELECT * FROM alias;
INSERT INTO t1 WITH w1 AS (SELECT * FROM t2) SELECT * FROM w1;
INSERT INTO CXML_LOG_CONTENT AS t0 (t0.IS_COMPRESSED, CONTENT, t0.CONTENT_KEY, t0.CONTENT_NEED_ENCR, t0.CONTENT_SIZE, ID)
   VALUES ('true', EMPTY_BLOB(), NULL, '0', '333', '1234');
INSERT INTO T (T.KEY, T.VAL2) VALUES(2,3);
INSERT INTO T ALIAS_T (ALIAS_T.KEY, ALIAS_T.VAL2) VALUES(2,3);
INSERT INTO T1 VALUES (1, 10) WITH NOWAIT;

-- load && unload
LOAD A all;
LOAD A (A,B);
UNLOAD A1;
UNLOAD A1 PARTITION (1 DELETE PERSISTENT MEMORY, 2 RETAIN PERSISTENT MEMORY);
UNLOAD A1 DELETE PERSISTENT MEMORY;

-- update
UPDATE T SET VAL = VAL + 1 WHERE KEY = 1;
UPDATE T SET VAL = KEY + 10;
UPDATE T SET VAL = T2.VAR FROM T, T2 WHERE T.KEY = T2.KEY;
UPDATE T A SET VAL = B.VAR FROM T A, T2 B WHERE A.KEY = B.KEY;
UPDATE T SET VAL = B.VAL FROM T A, T B WHERE A.KEY = B.KEY;
UPDATE PARTTAB1 PARTITION(1) SET B = 10;
update top 100000 testtab set updated = 1 where request = 'XXX' and updated = 0;
UPDATE T1 SET C1 = ARRAY ( 11, 12, 13, 14 ) WHERE ID = 1;
UPDATE T1 SET C1 = ARRAY( SELECT C1 FROM T0 ) WHERE ID = 1;
UPDATE T SET VAL = T2.VAR FROM T, T2 WHERE T.KEY = T2.KEY;
UPDATE TAB01 FOR PORTION OF '2018-08-20' AND '2018-10-20' SET VALUE=42 WHERE PKEY=1;

-- merge delta
MERGE DELTA OF TableA;
MERGE DELTA OF TableA WITH PARAMETERS('SMART_MERGE' = 'ON');
MERGE DELTA OF TableA PART 1;
MERGE DELTA OF TableA FORCE REBUILD;
MERGE HISTORY DELTA OF TableA PART 1;

-- merge into
MERGE INTO "my_schema".t1 USING "my_schema".t2 ON "my_schema".t1.a = "my_schema".t2.a
 WHEN MATCHED THEN UPDATE SET "my_schema".t1.b = "my_schema".t2.b
                       WHEN NOT MATCHED THEN INSERT VALUES("my_schema".t2.a, "my_schema".t2.b);
MERGE INTO "my_schema".t1 USING "my_schema".t2 ON "my_schema".t1.a = "my_schema".t2.a
 WHEN MATCHED THEN UPDATE SET t1.b = "my_schema".t2.b;
MERGE INTO "my_schema".t1 USING "my_schema".t2 ON "my_schema".t1.a = "my_schema".t2.a
 WHEN NOT MATCHED THEN INSERT VALUES("my_schema".t2.a, "my_schema".t2.b);
MERGE INTO T1 USING T2 ON T1.A = T2.A
 WHEN MATCHED THEN DELETE;
MERGE INTO T1 USING T2 ON T1.A = T2.A
 WHEN MATCHED AND T1.A > 1 THEN UPDATE SET B = T2.B
                                    WHEN NOT MATCHED AND T2.A > 3 THEN INSERT VALUES (T2.A, T2.B);
MERGE INTO T1 PARTITION(1) USING T2 ON T1.b = T2.b
    WHEN MATCHED THEN UPDATE SET T1.b = 2
                          WHEN NOT MATCHED THEN INSERT VALUES (10, 1);

-- replace && upsert
REPLACE T VALUES (1, 1);
REPLACE T VALUES (2, 2) WHERE KEY = 2;
REPLACE T VALUES (1, 8) WITH PRIMARY KEY;
REPLACE T SELECT KEY + 2, VAL FROM T;
REPLACE T1 VALUES ( 1, ARRAY ( 21, 22, 23, 24 ) ) WHERE ID = 1;
REPLACE T1 VALUES ( 1, ARRAY ( SELECT C1 FROM T0 ) ) WHERE ID = 1;
UPSERT T VALUES (1, 1);
UPSERT T VALUES (2, 2) WHERE KEY = 2;
UPSERT T VALUES (1, 8) WITH PRIMARY KEY;
UPSERT T SELECT KEY + 2, VAL FROM T;
UPSERT T1 VALUES ( 1, ARRAY ( 21, 22, 23, 24 ) ) WHERE ID = 1;
UPSERT T1 VALUES ( 1, ARRAY ( SELECT C1 FROM T0 ) ) WHERE ID = 1;
UPSERT T1 PARTITION(1) VALUES(1,20);
UPSERT T2 PARTITION(1) SELECT * FROM T1;

-- call
    CALL proc(1000, 'EUR', ?, ?);
    CALL proc(udf(),'EUR',?,?);
    CALL proc(udf()* udf()-55,'EUR', ?, ?);