DO
BEGIN
    DECLARE I INTEGER;
    CREATE ROW TABLE TAB1 (I INTEGER);
FOR I IN 1..10 DO
        INSERT INTO TAB1 VALUES (:I);
END FOR;
END;

DO
BEGIN
    T1 = SELECT I, 10 AS J FROM TAB;
T2 = SELECT I, 20 AS K FROM TAB;
T3 = SELECT J, K FROM :T1, :T2 WHERE :T1.I = :T2.I;
SELECT * FROM :T3;
END;

DO
BEGIN
    T1 = SELECT * FROM TAB;
CALL PROC3(:T1, :T2);
SELECT * FROM :T2;
END;

DO
BEGIN
    DECLARE I, J INTEGER;
BEGIN
        DECLARE EXIT HANDLER FOR SQLEXCEPTION
        IF ::SQL_ERROR_CODE = 288 THEN
DROP TABLE TAB;
CREATE ROW TABLE TAB (I INTEGER PRIMARY KEY);
ELSE
            RESIGNAL;
END IF;

        CREATE ROW TABLE TAB (I INTEGER PRIMARY KEY);
END;

FOR I in 1..3 DO
        INSERT INTO TAB VALUES (:I);
END FOR;

    IF :J <> 3 THEN
        SIGNAL SQL_ERROR_CODE 10001;
END IF;
END;

DO
BEGIN
    CREATE ROW TABLE TAB2 (K INT);
COMMIT;
DROP TABLE TAB;
CREATE ROW TABLE TAB (J INT);
ROLLBACK;
DELETE FROM TAB;
END;

DO
BEGIN
    T1 = SELECT I, 10 AS J FROM TAB;
T2 = SELECT I, 20 AS K FROM TAB;
T3 = SELECT J, K FROM :T1, :T2 WHERE :T1.I = :T2.I;
CALL PROC3(:T3, T4);
SELECT * FROM :T4;
END;

DO
BEGIN
    DECLARE I INTEGER;
FOR I in 1..3 DO
        INSERT INTO TAB VALUES (:I);
END FOR;
END;

DO (IN A INT => 1, IN B INT => ?, OUT C INT => ?)
BEGIN
    C = :A + :B;
END;