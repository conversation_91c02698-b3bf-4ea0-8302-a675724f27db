lexer grammar HanaLexer;

options {
    caseInsensitive = true;
}

A_LETTER                    : 'A';
ADD                         : 'ADD';
AFTER                       : 'AFTER';
AGENT                       : 'AGENT';
AGGREGATE                   : 'AGGREGATE';
ALL                         : 'ALL';
ALTER                       : 'ALTER';
ALPHANUM                    : 'ALPHANUM';
ANALYZE                     : 'ANALYZE';
AND                         : 'AND';
ANY                         : 'ANY';
ARRAY                       : 'ARRAY';
AS                          : 'AS';
ASC                         : 'ASC';
ASSOCIATE                   : 'ASSOCIATE';
AT                          : 'AT';
ATTRIBUTE                   : 'ATTRIBUTE';
AUDIT                       : 'AUDIT';
AUTHID                      : 'AUTHID';
AUTO                        : 'AUTO';
AUTOMATIC                   : 'AUTOMATIC';
AUTONOMOUS_TRANSACTION      : 'AUTONOMOUS_TRANSACTION';
BATCH                       : 'BATCH';
BEFORE                      : 'BEFORE';
BEGIN                       : 'BEGIN';
BETWEEN                     : 'BETWEEN';
BFILE                       : 'BFILE';
BINARY_DOUBLE               : 'BINARY_DOUBLE';
BINARY_FLOAT                : 'BINARY_FLOAT';
BINARY_INTEGER              : 'BINARY_INTEGER';
BIGINT                      : 'BIGINT';
BLOB                        : 'BLOB';
BLOCK                       : 'BLOCK';
BODY                        : 'BODY';
BOOLEAN                     : 'BOOLEAN';
BOTH                        : 'BOTH';
BREADTH                     : 'BREADTH';
BULK                        : 'BULK';
BY                          : 'BY';
BYTE                        : 'BYTE';
C_LETTER                    : 'C';
CACHE                       : 'CACHE';
CALL                        : 'CALL';
CANONICAL                   : 'CANONICAL';
CASCADE                     : 'CASCADE';
CASE                        : 'CASE';
CAST                        : 'CAST';
CHAR                        : 'CHAR';
CHAR_CS                     : 'CHAR_CS';
CHARACTER                   : 'CHARACTER';
CHECK                       : 'CHECK';
CHR                         : 'CHR';
CLOB                        : 'CLOB';
CLOSE                       : 'CLOSE';
CLUSTER                     : 'CLUSTER';
COLLECT                     : 'COLLECT';
COLUMNS                     : 'COLUMNS';
COMMENT                     : 'COMMENT';
COMMIT                      : 'COMMIT';
COMMITTED                   : 'COMMITTED';
COMPATIBILITY               : 'COMPATIBILITY';
COMPILE                     : 'COMPILE';
COMPOUND                    : 'COMPOUND';
CONNECT                     : 'CONNECT';
CONNECT_BY_ROOT             : 'CONNECT_BY_ROOT';
CONSTANT                    : 'CONSTANT';
CONSTRAINT                  : 'CONSTRAINT';
CONSTRAINTS                 : 'CONSTRAINTS';
CONSTRUCTOR                 : 'CONSTRUCTOR';
CONTENT                     : 'CONTENT';
CONTEXT                     : 'CONTEXT';
CONTINUE                    : 'CONTINUE';
CONVERT                     : 'CONVERT';
CORRUPT_XID                 : 'CORRUPT_XID';
CORRUPT_XID_ALL             : 'CORRUPT_XID_ALL';
COST                        : 'COST';
COUNT                       : 'COUNT';
CREATE                      : 'CREATE';
CROSS                       : 'CROSS';
CUBE                        : 'CUBE';
CURRENT                     : 'CURRENT';
CURRENT_USER                : 'CURRENT_USER';
CURSOR                      : 'CURSOR';
CUSTOMDATUM                 : 'CUSTOMDATUM';
CYCLE                       : 'CYCLE';
DATA                        : 'DATA';
DATABASE                    : 'DATABASE';
DATE                        : 'DATE';
DAY                         : 'DAY';
DB_ROLE_CHANGE              : 'DB_ROLE_CHANGE';
DBTIMEZONE                  : 'DBTIMEZONE';
DDL                         : 'DDL';
DEBUG                       : 'DEBUG';
DEC                         : 'DEC';
DECIMAL                     : 'DECIMAL';
DECLARE                     : 'DECLARE';
DECOMPOSE                   : 'DECOMPOSE';
DECREMENT                   : 'DECREMENT';
DEFAULT                     : 'DEFAULT';
DEFAULTS                    : 'DEFAULTS';
DEFERRED                    : 'DEFERRED';
DEFINER                     : 'DEFINER';
DELETE                      : 'DELETE';
DEPTH                       : 'DEPTH';
DESC                        : 'DESC';
DETERMINISTIC               : 'DETERMINISTIC';
DIMENSION                   : 'DIMENSION';
DISABLE                     : 'DISABLE';
DISASSOCIATE                : 'DISASSOCIATE';
DISTINCT                    : 'DISTINCT';
DOCUMENT                    : 'DOCUMENT';
DOUBLE                      : 'DOUBLE';
DROP                        : 'DROP';
DSINTERVAL_UNCONSTRAINED    : 'DSINTERVAL_UNCONSTRAINED';
EACH                        : 'EACH';
ELEMENT                     : 'ELEMENT';
ELSE                        : 'ELSE';
ELSIF                       : 'ELSIF';
EMPTY                       : 'EMPTY';
ENABLE                      : 'ENABLE';
ENCODING                    : 'ENCODING';
END                         : 'END';
ENTITYESCAPING              : 'ENTITYESCAPING';
ERRORS                      : 'ERRORS';
ESCAPE                      : 'ESCAPE';
EVALNAME                    : 'EVALNAME';
EXCEPT                      : 'EXCEPT';
EXCEPTION                   : 'EXCEPTION';
EXCEPTION_INIT              : 'EXCEPTION_INIT';
EXCEPTIONS                  : 'EXCEPTIONS';
EXCLUDE                     : 'EXCLUDE';
EXCLUSIVE                   : 'EXCLUSIVE';
EXECUTE                     : 'EXECUTE';
EXISTS                      : 'EXISTS';
EXIT                        : 'EXIT';
EXPLAIN                     : 'EXPLAIN';
EXTERNAL                    : 'EXTERNAL';
EXTRACT                     : 'EXTRACT';
FAILURE                     : 'FAILURE';
FALSE                       : 'FALSE';
FETCH                       : 'FETCH';
FINAL                       : 'FINAL';
FIRST                       : 'FIRST';
FIRST_VALUE                 : 'FIRST_VALUE';
FLOAT                       : 'FLOAT';
FOLLOWING                   : 'FOLLOWING';
FOLLOWS                     : 'FOLLOWS';
FOR                         : 'FOR';
FORALL                      : 'FORALL';
FORCE                       : 'FORCE';
FROM                        : 'FROM';
FULL                        : 'FULL';
FUNCTION                    : 'FUNCTION';
GOTO                        : 'GOTO';
GRANT                       : 'GRANT';
GROUP                       : 'GROUP';
GROUPING                    : 'GROUPING';
HASH                        : 'HASH';
HAVING                      : 'HAVING';
HANDLER                     : 'HANDLER';
HIDE                        : 'HIDE';
HOUR                        : 'HOUR';
IF                          : 'IF';
IGNORE                      : 'IGNORE';
IMMEDIATE                   : 'IMMEDIATE';
IN                          : 'IN';
INCLUDE                     : 'INCLUDE';
INCLUDING                   : 'INCLUDING';
INCREMENT                   : 'INCREMENT';
INDENT                      : 'INDENT';
INDEX                       : 'INDEX';
INDEXED                     : 'INDEXED';
INDICATOR                   : 'INDICATOR';
INDICES                     : 'INDICES';
INFINITE                    : 'INFINITE';
INLINE                      : 'INLINE';
INNER                       : 'INNER';
INOUT                       : 'INOUT';
INSERT                      : 'INSERT';
INSTANTIABLE                : 'INSTANTIABLE';
INSTEAD                     : 'INSTEAD';
INT                         : 'INT';
INTEGER                     : 'INTEGER';
INTERSECT                   : 'INTERSECT';
INTERVAL                    : 'INTERVAL';
INTO                        : 'INTO';
INVALIDATE                  : 'INVALIDATE';
INVOKER                     : 'INVOKER';
IS                          : 'IS';
ISOLATION                   : 'ISOLATION';
ITERATE                     : 'ITERATE';
JAVA                        : 'JAVA';
JOIN                        : 'JOIN';
KEEP                        : 'KEEP';
LANGUAGE                    : 'LANGUAGE';
LAST                        : 'LAST';
LAST_VALUE                  : 'LAST_VALUE';
LEADING                     : 'LEADING';
LEFT                        : 'LEFT';
LEVEL                       : 'LEVEL';
LIBRARY                     : 'LIBRARY';
LIKE                        : 'LIKE';
LIKE2                       : 'LIKE2';
LIKE4                       : 'LIKE4';
LIKEC                       : 'LIKEC';
LIMIT                       : 'LIMIT';
LOCAL                       : 'LOCAL';
LOCK                        : 'LOCK';
LOCKED                      : 'LOCKED';
LOG                         : 'LOG';
LOGOFF                      : 'LOGOFF';
LOGON                       : 'LOGON';
LONG                        : 'LONG';
LOOP                        : 'LOOP';
MAIN                        : 'MAIN';
MAP                         : 'MAP';
MATCHED                     : 'MATCHED';
MAXVALUE                    : 'MAXVALUE';
MEASURES                    : 'MEASURES';
MEMBER                      : 'MEMBER';
MERGE                       : 'MERGE';
MINUS                       : 'MINUS';
MINUTE                      : 'MINUTE';
MINVALUE                    : 'MINVALUE';
MLSLABEL                    : 'MLSLABEL';
MODE                        : 'MODE';
MODEL                       : 'MODEL';
MODIFY                      : 'MODIFY';
MONTH                       : 'MONTH';
MULTISET                    : 'MULTISET';
NAME                        : 'NAME';
NAN                         : 'NAN';
NATURAL                     : 'NATURAL';
NATURALN                    : 'NATURALN';
NAV                         : 'NAV';
NCHAR                       : 'NCHAR';
NCHAR_CS                    : 'NCHAR_CS';
NCLOB                       : 'NCLOB';
NESTED                      : 'NESTED';
NEW                         : 'NEW';
NO                          : 'NO';
NOAUDIT                     : 'NOAUDIT';
NOCACHE                     : 'NOCACHE';
NOCOPY                      : 'NOCOPY';
NOCYCLE                     : 'NOCYCLE';
NOENTITYESCAPING            : 'NOENTITYESCAPING';
NOMAXVALUE                  : 'NOMAXVALUE';
NOMINVALUE                  : 'NOMINVALUE';
NONE                        : 'NONE';
NOORDER                     : 'NOORDER';
NOSCHEMACHECK               : 'NOSCHEMACHECK';
NOT                         : 'NOT';
NOWAIT                      : 'NOWAIT';
NULL                        : 'NULL';
NULLS                       : 'NULLS';
NUMBER                      : 'NUMBER';
NUMERIC                     : 'NUMERIC';
NVARCHAR                    : 'NVARCHAR';
OBJECT                      : 'OBJECT';
OF                          : 'OF';
OFF                         : 'OFF';
OID                         : 'OID';
OLD                         : 'OLD';
ON                          : 'ON';
ONLY                        : 'ONLY';
OPEN                        : 'OPEN';
OPTION                      : 'OPTION';
OR                          : 'OR';
ORADATA                     : 'ORADATA';
ORDER                       : 'ORDER';
ORDINALITY                  : 'ORDINALITY';
OSERROR                     : 'OSERROR';
OUT                         : 'OUT';
OUTER                       : 'OUTER';
OVER                        : 'OVER';
OVERRIDING                  : 'OVERRIDING';
PACKAGE                     : 'PACKAGE';
PARALLEL_ENABLE             : 'PARALLEL_ENABLE';
PARAMETERS                  : 'PARAMETERS';
PARENT                      : 'PARENT';
PARTITION                   : 'PARTITION';
PASSING                     : 'PASSING';
PATH                        : 'PATH';
PERCENT_ROWTYPE             : '%ROWTYPE';
PERCENT_TYPE                : '%TYPE';
PIPELINED                   : 'PIPELINED';
PIVOT                       : 'PIVOT';
PLAN                        : 'PLAN';
PLS_INTEGER                 : 'PLS_INTEGER';
POSITIVE                    : 'POSITIVE';
POSITIVEN                   : 'POSITIVEN';
PRAGMA                      : 'PRAGMA';
PRECEDING                   : 'PRECEDING';
PRECISION                   : 'PRECISION';
PRESENT                     : 'PRESENT';
PRIOR                       : 'PRIOR';
PROCEDURE                   : 'PROCEDURE';
RAISE                       : 'RAISE';
RANGE                       : 'RANGE';
RAW                         : 'RAW';
READ                        : 'READ';
READS                       : 'READS';
REAL                        : 'REAL';
RECORD                      : 'RECORD';
REF                         : 'REF';
REFERENCE                   : 'REFERENCE';
REFERENCING                 : 'REFERENCING';
REJECT                      : 'REJECT';
RELIES_ON                   : 'RELIES_ON';
RENAME                      : 'RENAME';
REPLACE                     : 'REPLACE';
RESPECT                     : 'RESPECT';
RESTRICT_REFERENCES         : 'RESTRICT_REFERENCES';
RESULT                      : 'RESULT';
RESULT_CACHE                : 'RESULT_CACHE';
RETURN                      : 'RETURN';
RETURNING                   : 'RETURNING';
REUSE                       : 'REUSE';
REVERSE                     : 'REVERSE';
REVOKE                      : 'REVOKE';
RIGHT                       : 'RIGHT';
ROLLBACK                    : 'ROLLBACK';
ROLLUP                      : 'ROLLUP';
ROW                         : 'ROW';
ROWID                       : 'ROWID';
ROWS                        : 'ROWS';
RULES                       : 'RULES';
SAMPLE                      : 'SAMPLE';
SAVE                        : 'SAVE';
SAVEPOINT                   : 'SAVEPOINT';
SCHEMA                      : 'SCHEMA';
SCHEMACHECK                 : 'SCHEMACHECK';
SCN                         : 'SCN';
SEARCH                      : 'SEARCH';
SECOND                      : 'SECOND';
SECONDDATE                  : 'SECONDDATE';
SECURITY                    : 'SECURITY';
SEED                        : 'SEED';
SEGMENT                     : 'SEGMENT';
SELECT                      : 'SELECT';
SELF                        : 'SELF';
SEQUENCE                    : 'SEQUENCE';
SEQUENTIAL                  : 'SEQUENTIAL';
SERIALIZABLE                : 'SERIALIZABLE';
SERIALLY_REUSABLE           : 'SERIALLY_REUSABLE';
SERVERERROR                 : 'SERVERERROR';
SESSIONTIMEZONE             : 'SESSIONTIMEZONE';
SET                         : 'SET';
SETS                        : 'SETS';
SETTINGS                    : 'SETTINGS';
SHARE                       : 'SHARE';
SHOW                        : 'SHOW';
SHUTDOWN                    : 'SHUTDOWN';
SIBLINGS                    : 'SIBLINGS';
SIGNTYPE                    : 'SIGNTYPE';
SIMPLE_INTEGER              : 'SIMPLE_INTEGER';
SINGLE                      : 'SINGLE';
SIZE                        : 'SIZE';
SKIP_                       : 'SKIP';
SMALLINT                    : 'SMALLINT';
SMALLDECIMAL                : 'SMALLDECIMAL';
SNAPSHOT                    : 'SNAPSHOT';
SOME                        : 'SOME';
SPECIFICATION               : 'SPECIFICATION';
SQL                         : 'SQL';
SQLDATA                     : 'SQLDATA';
SQLERROR                    : 'SQLERROR';
SQLEXCEPTION                : 'SQLEXCEPTION';
SQLSCRIPT                   : 'SQLSCRIPT';
STANDALONE                  : 'STANDALONE';
START                       : 'START';
STARTUP                     : 'STARTUP';
STATEMENT                   : 'STATEMENT';
STATEMENT_ID                : 'STATEMENT_ID';
STATIC                      : 'STATIC';
STATISTICS                  : 'STATISTICS';
STRING                      : 'STRING';
SUBMULTISET                 : 'SUBMULTISET';
SUBPARTITION                : 'SUBPARTITION';
SUBSTITUTABLE               : 'SUBSTITUTABLE';
SUBTYPE                     : 'SUBTYPE';
SUCCESS                     : 'SUCCESS';
SUSPEND                     : 'SUSPEND';
TABLE                       : 'TABLE';
THE                         : 'THE';
THEN                        : 'THEN';
TIME                        : 'TIME';
TIMESTAMP                   : 'TIMESTAMP';
TIMESTAMP_LTZ_UNCONSTRAINED : 'TIMESTAMP_LTZ_UNCONSTRAINED';
TIMESTAMP_TZ_UNCONSTRAINED  : 'TIMESTAMP_TZ_UNCONSTRAINED';
TIMESTAMP_UNCONSTRAINED     : 'TIMESTAMP_UNCONSTRAINED';
TIMEZONE_ABBR               : 'TIMEZONE_ABBR';
TIMEZONE_HOUR               : 'TIMEZONE_HOUR';
TIMEZONE_MINUTE             : 'TIMEZONE_MINUTE';
TIMEZONE_REGION             : 'TIMEZONE_REGION';
TINYINT                     : 'TINYINT';
TO                          : 'TO';
TRAILING                    : 'TRAILING';
TRANSACTION                 : 'TRANSACTION';
TRANSLATE                   : 'TRANSLATE';
TREAT                       : 'TREAT';
TRIGGER                     : 'TRIGGER';
TRIM                        : 'TRIM';
TRUE                        : 'TRUE';
TRUNCATE                    : 'TRUNCATE';
TYPE                        : 'TYPE';
UNBOUNDED                   : 'UNBOUNDED';
UNDER                       : 'UNDER';
UNION                       : 'UNION';
UNIQUE                      : 'UNIQUE';
UNLIMITED                   : 'UNLIMITED';
UNPIVOT                     : 'UNPIVOT';
UNTIL                       : 'UNTIL';
UPDATE                      : 'UPDATE';
UPDATED                     : 'UPDATED';
UPSERT                      : 'UPSERT';
UROWID                      : 'UROWID';
USE                         : 'USE';
USING                       : 'USING';
VALIDATE                    : 'VALIDATE';
VALUE                       : 'VALUE';
VALUES                      : 'VALUES';
VARCHAR                     : 'VARCHAR';
VARCHAR2                    : 'VARCHAR2';
VARIABLE                    : 'VARIABLE';
VARRAY                      : 'VARRAY';
VARYING                     : 'VARYING';
VERSION                     : 'VERSION';
VERSIONS                    : 'VERSIONS';
VIEW                        : 'VIEW';
WAIT                        : 'WAIT';
WARNING                     : 'WARNING';
WELLFORMED                  : 'WELLFORMED';
WHEN                        : 'WHEN';
WHENEVER                    : 'WHENEVER';
WHERE                       : 'WHERE';
WHILE                       : 'WHILE';
WITH                        : 'WITH';
WITHIN                      : 'WITHIN';
WORK                        : 'WORK';
WRITE                       : 'WRITE';
XML                         : 'XML';
XMLAGG                      : 'XMLAGG';
XMLATTRIBUTES               : 'XMLATTRIBUTES';
XMLCAST                     : 'XMLCAST';
XMLCOLATTVAL                : 'XMLCOLATTVAL';
XMLELEMENT                  : 'XMLELEMENT';
XMLEXISTS                   : 'XMLEXISTS';
XMLFOREST                   : 'XMLFOREST';
XMLNAMESPACES               : 'XMLNAMESPACES';
XMLPARSE                    : 'XMLPARSE';
XMLPI                       : 'XMLPI';
XMLQUERY                    : 'XMLQUERY';
XMLROOT                     : 'XMLROOT';
XMLSERIALIZE                : 'XMLSERIALIZE';
XMLTABLE                    : 'XMLTABLE';
YEAR                        : 'YEAR';
YES                         : 'YES';
YMINTERVAL_UNCONSTRAINED    : 'YMINTERVAL_UNCONSTRAINED';
ZONE                        : 'ZONE';
AUTONOMOUS                  : 'AUTONOMOUS';
CONDITION                   : 'CONDITION';
ELSEIF                      : 'ELSEIF';
EXECUTION                   : 'EXECUTION';
OVERVIEW                    : 'OVERVIEW';
RESIGNAL                    : 'RESIGNAL';
MESSAGE_TEXT                : 'MESSAGE_TEXT';
SHORTTEXT                   : 'SHORTTEXT';
SIGNAL                      : 'SIGNAL';
SQL_ERROR_CODE              : 'SQL_ERROR_CODE';
SQL_ERROR_MESSAGE           : 'SQL_ERROR_MESSAGE';
SQLWARNING                  : 'SQLWARNING';
TEXT                        : 'TEXT';
UNNEST                      : 'UNNEST';
STRING_AGG                  : 'STRING_AGG';
CORR_SPEARMAN               : 'CORR_SPEARMAN';
VAR                         : 'VAR';
STDDEV_POP                  : 'STDDEV_POP';
VAR_POP                     : 'VAR_POP';
STDDEV_SAMP                 : 'STDDEV_SAMP';
VAR_SAMP                    : 'VAR_SAMP';
PREDICTION                  : 'PREDICTION';
PREDICTION_BOUNDS           : 'PREDICTION_BOUNDS';
PREDICTION_COST             : 'PREDICTION_COST';
PREDICTION_DETAILS          : 'PREDICTION_DETAILS';
PREDICTION_PROBABILITY      : 'PREDICTION_PROBABILITY';
PREDICTION_SET              : 'PREDICTION_SET';
CUME_DIST                   : 'CUME_DIST';
DENSE_RANK                  : 'DENSE_RANK';
LISTAGG                     : 'LISTAGG';
PERCENT_RANK                : 'PERCENT_RANK';
PERCENTILE_CONT             : 'PERCENTILE_CONT';
PERCENTILE_DISC             : 'PERCENTILE_DISC';
RANK                        : 'RANK';
AVG                         : 'AVG';
CORR                        : 'CORR';
LAG                         : 'LAG';
LEAD                        : 'LEAD';
MAX                         : 'MAX';
MEDIAN                      : 'MEDIAN';
MIN                         : 'MIN';
NTILE                       : 'NTILE';
RATIO_TO_REPORT             : 'RATIO_TO_REPORT';
ROW_NUMBER                  : 'ROW_NUMBER';
SUM                         : 'SUM';
VARIANCE                    : 'VARIANCE';
REGR_                       : 'REGR_';
STDDEV                      : 'STDDEV';
VAR_                        : 'VAR_';
COVAR_                      : 'COVAR_';
//new
FULLTEXT                    : 'FULLTEXT';
WEIGHT                      : 'WEIGHT';
EXACT                       : 'EXACT';
FUZZY                       : 'FUZZY';
LINGUISTIC                  : 'LINGUISTIC';
LIKE_REGEXPR                : 'LIKE_REGEXPR';
FLAG                        : 'FLAG';
RELEASE                     : 'RELEASE';
REPEATABLE                  : 'REPEATABLE';
ROUTE_TO                    : 'ROUTE_TO';
NO_ROUTE_TO                 : 'NO_ROUTE_TO';
ROUTE_BY                    : 'ROUTE_BY';
ROUTE_BY_CARDINALITY        : 'ROUTE_BY_CARDINALITY';
DATA_TRANSFER_COST          : 'DATA_TRANSFER_COST';
UTCTIMESTAMP                : 'UTCTIMESTAMP';
JSON                        : 'JSON';
RETURNS                     : 'RETURNS';
SYSTEM_TIME                 : 'SYSTEM_TIME';
APPLICATION_TIME            : 'APPLICATION_TIME';
TABLESAMPLE                 : 'TABLESAMPLE';
BERNOULLI                   : 'BERNOULLI';
SYSTEM                      : 'SYSTEM';
VARBINARY                   : 'VARBINARY';
CONTAINS                    : 'CONTAINS';
COLLATION                   : 'COLLATION';
HINT                        : 'HINT';
TIMEOUT                     : 'TIMEOUT';
AUTOCOMMIT                  : 'AUTOCOMMIT';
INTENTIONAL                 : 'INTENTIONAL';
TOP                         : 'TOP';
ONE                         : 'ONE';
MANY                        : 'MANY';
BEST                        : 'BEST';
SUBTOTAL                    : 'SUBTOTAL';
BALANCE                     : 'BALANCE';
TEXT_FILTER                 : 'TEXT_FILTER';
FILL : 'FILL';
UP:'UP';
SORT:'SORT';
MATCHES:'MATCHES';
STRUCTURED:'STRUCTURED';
PREFIX:'PREFIX';
MULTIPLE:'MULTIPLE';
RESULTSETS:'RESULTSETS';
OFFSET:'OFFSET';
TOTAL:'TOTAL';
ROWCOUNT:'ROWCOUNT';
ID:'ID';
MAX_CONCURRENCY:'MAX_CONCURRENCY';
STATEMENT_NAME:'STATEMENT_NAME';
ENTRY:'ENTRY';
VARIANT:'VARIANT';
HISTORY:'HISTORY';
PORTION:'PORTION';
USER:'USER';
LOAD:'LOAD';
UNLOAD:'UNLOAD';
DELTA:'DELTA';
RETAIN:'RETAIN';
PERSISTENT:'PERSISTENT';
MEMORY:'MEMORY';
REBUILD:'REBUILD';
PART:'PART';
PRIMARY:'PRIMARY';
KEY:'KEY';
FLUSH:'FLUSH';
ACTIVATE:'ACTIVATE';
QUEUE:'QUEUE';
PHRASE:'PHRASE';
RATIO:'RATIO';
MINING:'MINING';
CONFIGURATION:'CONFIGURATION';
OVERLAY:'OVERLAY';
INVERTED:'INVERTED';
INDIVIDUAL:'INDIVIDUAL';
COLUMN:'COLUMN';
PAGE:'PAGE';
LOADABLE:'LOADABLE';
ONLINE:'ONLINE';
PREFERRED:'PREFERRED';
RESTART:'RESTART';
RESET:'RESET';
KMINVAL:'KMINVAL';
PCSA:'PCSA';
LINEARCOUNTING:'LINEARCOUNTING';
LOGCOUNTING:'LOGCOUNTING';
LOGLOGCOUNTING:'LOGLOGCOUNTING';
SUPERLOGLOGCOUNTING:'SUPERLOGLOGCOUNTING';
REFRESH:'REFRESH';
MANUAL:'MANUAL';
HISTOGRAM:'HISTOGRAM';
SIMPLE:'SIMPLE';
TOPK:'TOPK';
SKETCH:'SKETCH';
BUCKETS:'BUCKETS';
QERROR:'QERROR';
QTHETA:'QTHETA';
PERCENT:'PERCENT';
ACCURACY:'ACCURACY';
PREFIXBITS:'PREFIXBITS';
VALID:'VALID';
ESTIMATION:'ESTIMATION';
DEPENDENCY:'DEPENDENCY';
INITIAL:'INITIAL';
SCHEDULER:'SCHEDULER';
JOB:'JOB';
CRON:'CRON';
THRESHOLD:'THRESHOLD';
FLEXIBILITY:'FLEXIBILITY';
GENERATED:'GENERATED';
ALWAYS:'ALWAYS';
IDENTITY:'IDENTITY';
CURRENT_DATE:'CURRENT_DATE';
CURRENT_TIME:'CURRENT_TIME';
CURRENT_TIMESTAMP:'CURRENT_TIMESTAMP';
CURRENT_UTCDATE:'CURRENT_UTCDATE';
CURRENT_UTCTIME:'CURRENT_UTCTIME';
CURRENT_UTCTIMESTAMP:'CURRENT_UTCTIMESTAMP';
SYSUUID:'SYSUUID';
CURRENT_SCHEMA:'CURRENT_SCHEMA';
SESSION_USER:'SESSION_USER';
CLIENTSIDE:'CLIENTSIDE';
ENCRYPTION:'ENCRYPTION';
RANDOM:'RANDOM';
REFERENCES:'REFERENCES';
ENFORCED:'ENFORCED';
VALIDATED:'VALIDATED';
INITIALLY:'INITIALLY';
HIDDEN_:'HIDDEN';
BTREE:'BTREE';
CPBTREE:'CPBTREE';
RESTRICT:'RESTRICT';
PERIOD:'PERIOD';
VERSIONING:'VERSIONING';
GET_NUM_SERVERS:'GET_NUM_SERVERS';
OTHERS:'OTHERS';
PARTITIONS:'PARTITIONS';
PHYSICAL:'PHYSICAL';
NON:'NON';
PARTITIONING:'PARTITIONING';
STORAGE:'STORAGE';
MOVE:'MOVE';
PARAMETER:'PARAMETER';
UNSET:'UNSET';
ANNOTATIONS:'ANNOTATIONS';
REORGANIZE:'REORGANIZE';
LOB:'LOB';
CLEAR:'CLEAR';
FOREIGN:'FOREIGN';
PRELOAD:'PRELOAD';
THREADS:'THREADS';
ASSOCIATION:'ASSOCIATION';
FILTER:'FILTER';
NUMA:'NUMA';
NODE:'NODE';
DYNAMIC:'DYNAMIC';
ROUNDROBIN:'ROUNDROBIN';
LOCATION:'LOCATION';
DISTANCE:'DISTANCE';
REPLICA:'REPLICA';
SUBPARTITIONS:'SUBPARTITIONS';
SOURCE:'SOURCE';
AUTOMERGE:'AUTOMERGE';
SYNCHRONOUS:'SYNCHRONOUS';
ASYNCHRONOUS:'ASYNCHRONOUS';
PRIORITY:'PRIORITY';
RECLAIM:'RECLAIM';
PROMOTION:'PROMOTION';
UNUSED:'UNUSED';
RETENTION:'RETENTION';
SPACE:'SPACE';
SERIES:'SERIES';
OWNER:'OWNER';
SESSION:'SESSION';
MASK:'MASK';
CANCEL:'CANCEL';
MOVABLE:'MOVABLE';
LOCATIONS:'LOCATIONS';
ANONYMIZATION:'ANONYMIZATION';
ALGORITHM:'ALGORITHM';
EXPRESSION:'EXPRESSION';
MACROS:'MACROS';
ASSOCIATIONS:'ASSOCIATIONS';
PRIVILEGE:'PRIVILEGE';
VIRTUAL:'VIRTUAL';
PROPERTY:'PROPERTY';
DEFINITION:'DEFINITION';
ANNOTATE:'ANNOTATE';
ROLE:'ROLE';
USERGROUP:'USERGROUP';
CERTIFICATE:'CERTIFICATE';
PUBLIC:'PUBLIC';
DETECTION:'DETECTION';
MIME:'MIME';
SYNC:'SYNC';
HRONOUS:'HRONOUS';
ASYNC:'ASYNC';
FAST:'FAST';
PREPROCESS:'PREPROCESS';
ANALYSIS:'ANALYSIS';
TOKEN:'TOKEN';
SEPARATORS:'SEPARATORS';
EVERY:'EVERY';
MINUTES:'MINUTES';
DOCUMENTS:'DOCUMENTS';
GRAPH:'GRAPH';
WORKSPACE:'WORKSPACE';
EDGE:'EDGE';
TARGET:'TARGET';
VERTEX:'VERTEX';
PROJECTION:'PROJECTION';
OWNED:'OWNED';
SYNONYM:'SYNONYM';
PRECEDES:'PRECEDES';
DO:'DO';
BREAK:'BREAK';
PARALLEL:'PARALLEL';
GLOBAL:'GLOBAL';
TEMPORARY:'TEMPORARY';
WITHOUT:'WITHOUT';
LOGGING:'LOGGING';
PRESERVE:'PRESERVE';
EQUIDISTANT:'EQUIDISTANT';
MISSING:'MISSING';
ELEMENTS:'ELEMENTS';
ALLOWED:'ALLOWED';
ALTERNATE:'ALTERNATE';
DUPLICATES:'DUPLICATES';
REMOTE:'REMOTE';
POLICY:'POLICY';
TRAIL:'TRAIL';
SYSLOG:'SYSLOG';
CSV:'CSV';
AUDITING:'AUDITING';
SUCCESSFUL:'SUCCESSFUL';
UNSUCCESSFUL:'UNSUCCESSFUL';
EMERGENCY:'EMERGENCY';
ALERT:'ALERT';
CRITICAL:'CRITICAL';
INFO:'INFO';
ACTIONS:'ACTIONS';
PRINCIPALS:'PRINCIPALS';
SUBSCRIPTION:'SUBSCRIPTION';
KEYPAIR:'KEYPAIR';
GEOCODE:'GEOCODE';
DISCONNECT:'DISCONNECT';
STOP:'STOP';
SERVICE:'SERVICE';
APPLICATION:'APPLICATION';
LICENSE:'LICENSE';
EXPORT:'EXPORT';
IMPORT:'IMPORT';
REPOSITORY:'REPOSITORY';
CHANGE:'CHANGE';
PSE:'PSE';
CREDENTIAL:'CREDENTIAL';
COMPONENT:'COMPONENT';
PURPOSE:'PURPOSE';
JWT:'JWT';
PROVIDER:'PROVIDER';
ISSUER:'ISSUER';
CLAIM:'CLAIM';
SENSITIVE:'SENSITIVE';
INSENSITIVE:'INSENSITIVE';
CREATION:'CREATION';
STANDARD:'STANDARD';
RESTRICTED:'RESTRICTED';
LDAP:'LDAP';
AUTHORIZATION:'AUTHORIZATION';
HAS:'HAS';
LOOKUP:'LOOKUP';
URL:'URL';
DN:'DN';
MEMBER_OF:'MEMBER_OF';
SSL:'SSL';
SAML:'SAML';
SUBJECT:'SUBJECT';
ENTITY:'ENTITY';
ADAPTER:'ADAPTER';
FILE:'FILE';
LINKED:'LINKED';
OBJECTS:'OBJECTS';
CREATOR:'CREATOR';
PASSWORD:'PASSWORD';
FORCE_FIRST_PASSWORD_CHANGE:'FORCE_FIRST_PASSWORD_CHANGE';
NOW:'NOW';
FOREVER:'FOREVER';
CLIENT:'CLIENT';
LOCALE:'LOCALE';
EMAIL:'EMAIL';
ADDRESS:'ADDRESS';
RSERVE:'RSERVE';
SOURCES:'SOURCES';
THREAD:'THREAD';
IDENTIFIED:'IDENTIFIED';
EXTERNALLY:'EXTERNALLY';
ATTEMPTS:'ATTEMPTS';
LIFETIME:'LIFETIME';
DEACTIVATE:'DEACTIVATE';
KERBEROS:'KERBEROS';
X509:'X509';
SAP:'SAP';
TICKET:'TICKET';
ASSERTION:'ASSERTION';
OWN:'OWN';
ADMIN:'ADMIN';
OPERATOR:'OPERATOR';
BACKUP:'BACKUP';
CATALOG:'CATALOG';
SCENARIO:'SCENARIO';
RECOVERY:'RECOVERY';
ROOT:'ROOT';
EXTENDED:'EXTENDED';
INIFILE:'INIFILE';
MONITOR:'MONITOR';
OPTIMIZER:'OPTIMIZER';
RESOURCE:'RESOURCE';
STRUCTUREDPRIVILEGE:'STRUCTUREDPRIVILEGE';
REPLICATION:'REPLICATION';
TRACE:'TRACE';
TRUST:'TRUST';
WORKLOAD:'WORKLOAD';
CAPTURE:'CAPTURE';
REPLAY:'REPLAY';
PRIVILEGES:'PRIVILEGES';
METADATA:'METADATA';
CDS:'CDS';
UNMASKED:'UNMASKED';
PROCESS:'PROCESS';
USAGE:'USAGE';
MESSAGING:'MESSAGING';
ATTACH:'ATTACH';
DEBUGGER:'DEBUGGER';
GRANTED:'GRANTED';
R:'R';
HEADER:'HEADER';
POS:'POS';
EXEC:'EXEC';
RECOMPILE:'RECOMPILE';
INVALID:'INVALID';
SCALASPARK:'SCALASPARK';
BACKUP_ID:'BACKUP_ID';
BACKINT:'BACKINT';
COMPLETE:'COMPLETE';
RETAINED:'RETAINED';
ACCESS:'ACCESS';
KEYS:'KEYS';
LIST:'LIST';
POSITION:'POSITION';
VOLUME:'VOLUME';
RECOVER:'RECOVER';
SECURE:'SECURE';
STORE:'STORE';
PRIVATE:'PRIVATE';
TOOLOPTION:'TOOLOPTION';
WORKERGROUPS:'WORKERGROUPS';
PERSISTENCE:'PERSISTENCE';
RESUME:'RESUME';
GUID_ID:'GUID_ID' | 'GUID';
DELIMITED:'DELIMITED';
FIELD:'FIELD';
OPTIONALLY:'OPTIONALLY';
ENCLOSED:'ENCLOSED';
BINARY:'BINARY';
DEPENDENCIES:'DEPENDENCIES';
SCRAMBLE:'SCRAMBLE';
STRIP:'STRIP';
CONTROL:'CONTROL';
FORMAT:'FORMAT';
ERROR:'ERROR';
FAIL:'FAIL';
SCAN:'SCAN';
SAP_TIMEZONE_DATASET:'SAP_TIMEZONE_DATASET';
SHAPEFILE:'SHAPEFILE';
LOAD_HISTORY:'LOAD_HISTORY';
KEYCOPY:'KEYCOPY';
ENCRYPTED:'ENCRYPTED';
OS:'OS';
FINALIZE:'FINALIZE';
CONTROLLED:'CONTROLLED';
FALLBACK:'FALLBACK';
REMOVE:'REMOVE';
MANAGEMENT:'MANAGEMENT';
PROPERTIES:'PROPERTIES';
COREFILE:'COREFILE';
BACKUPS:'BACKUPS';
CLASS:'CLASS';
MAPPING:'MAPPING';
WILDCARD:'WILDCARD';
HOST:'HOST';
OVERRIDE:'OVERRIDE';
ABSTRACT:'ABSTRACT';
RECONFIGURE:'RECONFIGURE';
DATAVOLUME:'DATAVOLUME';
SITE:'SITE';
PLACEMENT:'PLACEMENT';
TIMEZONE:'TIMEZONE';
DATASET:'DATASET';
TRACES:'TRACES';
CLIENTPKI:'CLIENTPKI';
CERTIFICATES:'CERTIFICATES';
CA:'CA';
SOLACE:'SOLACE';

AMPERSAND_           : '&';
AND_                 : '&&';
ARROW_               : '=>';
ASSIGNMENT_OPERATOR_ : ':=';
ASTERISK_            : '*';
AT_                  : '@';
BACKSLASH_           : '\\';
BQ_                  : '`';
CARET_               : '^';
COLON_               : ':';
TYPE_CAST_           : '::';
COMMA_               : ',';
DEQ_                 : '==';
DOLLAR_              : '$';
DOT_                 : '.';
DOT_ASTERISK_        : '.*';
DQ_                  : '"';
EQ_                  : '=';
EXPONENT_            : '**';
GT_                  : '>';
GTE_                 : '>=';
LBE_                 : '{';
LBT_                 : '[';
LP_                  : '(';
LT_                  : '<';
LTE_                 : '<=';
MINUS_               : '-';
MOD_                 : '%';
NEQ_                 : '<>' | '!=' | '^=' | '~=';
NOT_                 : '!';
OR_                  : '||';
PLUS_                : '+';
POUND_               : '#';
QUESTION_            : '?';
RANGE_OPERATOR_      : '..';
ANY_                 : '...';
RBE_                 : '}';
RBT_                 : ']';
RP_                  : ')';
SAFE_EQ_             : '<=>';
SEMI_                : ';';
SIGNED_LEFT_SHIFT_   : '<<';
SIGNED_RIGHT_SHIFT_  : '>>';
SLASH_               : '/';
SQ_                  : '\'';
TILDE_               : '~';
VERTICAL_BAR_        : '|';
UL_                  : '_';

SHIFT_RIGHT_UNSIGNED_: '>>>';

WS : [ \t\r\n] + ->skip;


BLOCK_COMMENT:  '/*' .*? '*/'                           -> channel(HIDDEN);
INLINE_COMMENT: '--' ~[\r\n]* ('\r'? '\n' | EOF)        -> channel(HIDDEN);


STRING_: SINGLE_QUOTED_TEXT;
SINGLE_QUOTED_TEXT: SQ_ (~('\'' | '\r' | '\n') | '\'' '\'' | '\r'? '\n')* SQ_;
DOUBLE_QUOTED_TEXT: (DQ_ (~('"' | '\r' | '\n') | '"' '"')+ DQ_);
NCHAR_TEXT: 'N' STRING_;

INTEGER_: INT_;
NUMBER_: INTEGER_? DOT_ INTEGER_ ('E' (PLUS_ | MINUS_)? ((INTEGER_ DOT_)? INTEGER_))? (D|F)?;
HEX_DIGIT_: '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_;
BIT_NUM_: '0b' ('0' | '1')+ | 'B' SQ_ ('0' | '1')+ SQ_;

PASSWORD_INT_: [0-9][A-Z$#0-9]*;
IDENTIFIER_: [A-Z_#][A-Z_$#0-9]*;

//todo
PROMPT_
	: 'PROMPT' [ \t] ( ~('\r' | '\n') )* ('\r'? '\n'|EOF)
	;
FLAG_
    : [imsxU]
    ;
IP_ADDRESS: INTEGER_ DOT_ INTEGER_ DOT_ INTEGER_ DOT_ INTEGER_;


fragment D: 'D';
fragment F: 'F';
fragment INT_: [0-9]+;
fragment HEX_: [0-9A-F];