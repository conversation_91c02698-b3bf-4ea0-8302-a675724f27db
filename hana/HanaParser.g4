parser grammar HanaParser;

options {
    tokenVocab = HanaLexer;
}

// 方便批量测试，完成之后需删除
roots
    : root*
    ;

root
    : ( do
    | plsqlBlock
    // DML
    | select
    | lockTable
    | update
    | insert
    | replace
    | upsert
    | loadStatement
    | unload
    | delete
    | merge
    | mergeDelta
    | call
    | explain
    // TCL
    | setTransaction
    | setConstraints
    | commit
    | rollback
    | savepoint
    | releaseSavepoint
    // DDL
    | truncateTable
    | createProcedure
    | alterIndex
    | alterSequence
    | alterStatistics
    | alterSchedulerJob
    | alterTable
    | alterView
    | alterFunction
    | alterProcedure
    | alterDatabase
    | annotate
    | comment
    | createType
    | createFunction
    | createProcedure
    | createIndex
    | createGraphWorkspace
    | createProjectionView
    | createSchedulerJob
    | createSchema
    | createSynonym
    | createSequence
    | createStatistics
    | createTrigger
    | createView
    | createTable
    | createDatabase
    | dropType
    | dropFunction
    | dropProcedure
    | dropIndex
    | dropGraphWorkspace
    | dropSchedulerJob
    | dropSchema
    | dropSynonym
    | dropSequence
    | dropStatistics
    | dropTrigger
    | dropView
    | dropTable
    | dropDatabase
    | refreshStatistics
    | refreshView
    | renameSchema
    | renameTable
    | renameIndex
    | renameColumn
    | renameDatabase
    // (DDL or DCL or DAL) ??
    | alterAuditPolicy
    | createAuditPolicy
    | dropAuditPolicy
    | alterCredential
    | createCredential
    | dropCredential
    | alterSystem
    | createCertificate
    | dropCertificate
    // DCL
    | alterProvider
    | createProvider
    | dropProvider
    | validateProvider
    | alterRemoteSource
    | createRemoteSource
    | dropRemoteSource
    | alterRole
    | createRole
    | dropRole
    | alterStructuredPrivilege
    | createStructuredPrivilege
    | dropStructuredPrivilege
    | alterUser
    | createUser
    | dropUser
    | validateUser
    | alterUserGroup
    | createUserGroup
    | dropUserGroup
    | grant
    | revoke
    // other
    | backup
    | recover
    | export
    | importStatement
    | connect
    | set
    | setSchema
    | unset
    | setSystemLicense
    | unsetSystemLicense
    | setPse
    | unsetPse
    | createPse
    | dropPse
    | alterPse
    | createClientSideEncryption
    | alterClientSideEncryption
    | dropClientSideEncryption
    | createPublicKey
    | dropPublicKey
    | alterWorkload
    | createWorkload
    | dropWorkload
    ) SEMI_? EOF
    ;

/* identifiers Start */
identifier        : IDENTIFIER_ | DOUBLE_QUOTED_TEXT | unreservedWord;
variableName      : identifier (LBT_ INTEGER_ RBT_)? | DEFAULT literals;
regularId         : IDENTIFIER_ | unreservedWord;
owner             : (databaseName DOT_)? schemaName;
schemaName        : identifier;
objectName        : (owner DOT_)* name;
label             : identifier;
username          : identifier;
databaseName      : identifier;
delimiter         : identifier;
cursorName        : identifier;
name              : identifier;
parameterName     : identifier;
hintName          : identifier;
policyName        : identifier;
userGroupName     : identifier;
providerName      : identifier;
sourceName        : identifier;
linkName          : objectName;
costClassName     : objectName;
typeName          : objectName;
functionName      : objectName;
exceptionName     : objectName;
savepointName     : objectName;
collectionName    : objectName;
tableName         : (owner DOT_)? name;
viewName          : (owner DOT_)? name;
indexName         : (owner DOT_)? name;
procedureName     : (owner DOT_)? name;
sequenceName      : (owner DOT_)? name;
statisticsName    : (owner DOT_)? name;
jobName           : (owner DOT_)? name;
triggerName       : (owner DOT_)? name;
workspaceName     : (owner DOT_)? name;
synonymName       : (owner DOT_)? name;
roleName          : (owner DOT_)? name;
columnName        : (tableName DOT_)? name;
columnNameList    : LP_ columnNames RP_;
columnNames       : columnName (COMMA_ columnName)*;
recordName        : objectName | bindVariable;
xmlColumnName     : objectName | stringLiterals;
routineName       : functionName (AT_ linkName)?;
constraintName    : objectName (AT_ linkName)?;
variableNames     : variableName (COMMA_ variableName)*;
alias             : identifier | stringLiterals | ASTERISK_;
tableWithLinkName : tableName (AT_ linkName | partitionExtensionClause)?;
memberName        : (schemaName DOT_)? name COLON_ name;
statisticsNames   : statisticsName (COMMA_ statisticsName)*;
usernames         : username (COMMA_ username)*;
objectNames       : objectName (COMMA_ objectName)*;
identifiers       : identifier (COMMA_ identifier)*;   
/* identifiers End */

/* words Start */
unreservedWord
    : A_LETTER | ADD | AFTER | AGENT | AGGREGATE | ANALYZE | ARRAY | ASSOCIATE | AT | ATTRIBUTE | AUDIT | AUTHID | AUTO | AUTOMATIC
    | AUTONOMOUS_TRANSACTION | BATCH | BEFORE | BFILE | BINARY_DOUBLE | BINARY_FLOAT | BINARY_INTEGER | BLOB | BLOCK | BODY
    | BOOLEAN | BOTH | BULK | BYTE | C_LETTER | CALL | CANONICAL | CASCADE | CAST | CHAR | CHAR_CS | CHARACTER | CHR | CLOB | CLOSE
    | CLUSTER | COLLECT | COLUMNS | COMMENT | COMMIT | COMMITTED | COMPATIBILITY | COMPILE | COMPOUND | CONSTANT | CONSTRAINT
    | CONSTRAINTS | CONSTRUCTOR | CONTENT | CONTEXT | CONTINUE | CONVERT | CORRUPT_XID | CORRUPT_XID_ALL | COST | COUNT | CROSS
    | CUBE | CURRENT_USER | CURSOR | CUSTOMDATUM | CYCLE | DATA | DATABASE | DAY | DB_ROLE_CHANGE | DBTIMEZONE | DDL | DEBUG | DEC
    | DECIMAL | DECOMPOSE | DECREMENT | DEFAULTS | DEFERRED | DEFINER | DETERMINISTIC | DIMENSION | DISABLE | DISASSOCIATE
    | DOCUMENT | DOUBLE | DSINTERVAL_UNCONSTRAINED | EACH | ELEMENT | EMPTY | ENABLE | ENCODING | ENTITYESCAPING | ERRORS | ESCAPE
    | EVALNAME | EXCEPTION_INIT | EXCEPTIONS | EXCLUDE | EXECUTE | EXIT | EXPLAIN | EXTERNAL | EXTRACT | FAILURE | FINAL | FIRST
    | FIRST_VALUE | FLOAT | FOLLOWING | FOLLOWS | FORALL | FORCE | FULL | FUNCTION | GROUPING | HASH | HIDE | HOUR | IGNORE
    | IMMEDIATE | INCLUDE | INCLUDING | INCREMENT | INDENT | INDEXED | INDICATOR | INDICES | INFINITE | INLINE | INNER | INOUT
    | INSTANTIABLE | INSTEAD | INT | INTEGER | INTERVAL | INVALIDATE | ISOLATION | ITERATE | JAVA | JOIN | KEEP | LANGUAGE | LAST
    | LAST_VALUE | LEADING | LEFT | LEVEL | LIBRARY | LIKE2 | LIKE4 | LIKEC | LIMIT | LOCAL | LOCKED | LOG | LOGOFF | LOGON | LONG
    | LOOP | MAIN | MAP | MATCHED | MAXVALUE | MEASURES | MEMBER | MERGE | MINUTE | MINVALUE | MLSLABEL | MODEL | MODIFY | MONTH
    | MULTISET | NAME | NAN | NATURAL | NATURALN | NAV | NCHAR | NCHAR_CS | NCLOB | NESTED | NEW | NO | NOAUDIT | NOCOPY | NOCYCLE
    | NOENTITYESCAPING | NONE | NOSCHEMACHECK | NULLS | NUMBER | NUMERIC | NVARCHAR | OBJECT | OFF | OID | OLD | ONLY | OPEN
    | ORADATA | ORDINALITY | OSERROR | OUT | OUTER | OVER | OVERRIDING | PACKAGE | PARALLEL_ENABLE | PARAMETERS | PARENT | PARTITION
    | PASSING | PATH | PIPELINED | PLAN | PLS_INTEGER | POSITIVE | POSITIVEN | PRAGMA | PRECEDING | PRECISION | PRESENT | RAISE
    | RANGE | RAW | READ | REAL | RECORD | REF | REFERENCE | REFERENCING | REJECT | RELIES_ON | RENAME | REPLACE | RESPECT
    | RESTRICT_REFERENCES | RESULT | RESULT_CACHE | RETURN | RETURNING | REUSE | REVERSE | RIGHT | ROLLBACK | ROLLUP | ROW | ROWID
    | ROWS | RULES | SAMPLE | SAVE | SAVEPOINT | SCHEMA | SCHEMACHECK | SCN | SECOND | SEED | SEGMENT | SELF | SEQUENTIAL
    | SERIALIZABLE | SERIALLY_REUSABLE | SERVERERROR | SESSIONTIMEZONE | SET | SETS | SETTINGS | SHOW | SHUTDOWN | SIBLINGS
    | SIGNTYPE | SIMPLE_INTEGER | SINGLE | SKIP_ | SMALLINT | SNAPSHOT | SOME | SPECIFICATION | SQLDATA | SQLERROR | STANDALONE
    | STARTUP | STATEMENT | STATEMENT_ID | STATIC | STATISTICS | STRING | SUBMULTISET | SUBPARTITION | SUBSTITUTABLE | SUBTYPE
    | SUCCESS | SUSPEND | TIME | TIMESTAMP | TIMESTAMP_LTZ_UNCONSTRAINED | TIMESTAMP_TZ_UNCONSTRAINED | TIMESTAMP_UNCONSTRAINED
    | TIMEZONE_ABBR | TIMEZONE_HOUR | TIMEZONE_MINUTE | TIMEZONE_REGION | TRAILING | TRANSACTION | TRANSLATE | TREAT | TRIGGER
    | TRIM | TRUNCATE | TYPE | UNBOUNDED | UNDER | UNLIMITED | UNTIL | UPDATED | UPSERT | UROWID | USE | VALIDATE | VALUE | VARCHAR
    | VARCHAR2 | VARIABLE | VARRAY | VARYING | VERSION | VERSIONS | WAIT | WARNING | WELLFORMED | WHENEVER | WHILE | WITHIN | WORK
    | WRITE | XML | XMLAGG | XMLATTRIBUTES | XMLCAST | XMLCOLATTVAL | XMLELEMENT | XMLEXISTS | XMLFOREST | XMLNAMESPACES | XMLPARSE
    | XMLPI | XMLQUERY | XMLROOT | XMLSERIALIZE | XMLTABLE | YEAR | YES | YMINTERVAL_UNCONSTRAINED | ZONE | PREDICTION
    | PREDICTION_BOUNDS | PREDICTION_COST | PREDICTION_DETAILS | PREDICTION_PROBABILITY | PREDICTION_SET | CUME_DIST | DENSE_RANK
    | LISTAGG | PERCENT_RANK | PERCENTILE_CONT | PERCENTILE_DISC | RANK | AVG | CORR | LAG | LEAD | MAX | MEDIAN | MIN | NTILE
    | RATIO_TO_REPORT | ROW_NUMBER | SUM | VARIANCE | REGR_ | STDDEV | VAR_ | COVAR_ | ID | TEXT_FILTER | VAR | KEY
    | unreservedWord1
    ;
unreservedWord1
    : FULLTEXT | WEIGHT | EXACT | FUZZY | LINGUISTIC | LIKE_REGEXPR | FLAG | RELEASE | REPEATABLE | ROUTE_TO | NO_ROUTE_TO | ROUTE_BY | ROUTE_BY_CARDINALITY | DATA_TRANSFER_COST 
    | JSON | SYSTEM_TIME | APPLICATION_TIME | BERNOULLI | SYSTEM | VARBINARY | CONTAINS | COLLATION | HINT | TIMEOUT | AUTOCOMMIT | INTENTIONAL | ONE | MANY | BEST | SUBTOTAL 
    | BALANCE | TEXT_FILTER | FILL | UP | SORT | MATCHES | STRUCTURED | PREFIX | MULTIPLE | RESULTSETS | OFFSET | TOTAL | ROWCOUNT | ID | MAX_CONCURRENCY | STATEMENT_NAME 
    | ENTRY | VARIANT | HISTORY | PORTION | USER | LOAD | UNLOAD | DELTA | RETAIN | PERSISTENT | MEMORY | REBUILD | PART | PRIMARY | KEY | FLUSH | ACTIVATE | QUEUE | PHRASE 
    | RATIO | MINING | CONFIGURATION | OVERLAY | INVERTED | INDIVIDUAL | COLUMN | PAGE | LOADABLE | ONLINE | PREFERRED | RESTART | RESET | KMINVAL | PCSA | LINEARCOUNTING 
    | LOGCOUNTING | LOGLOGCOUNTING | SUPERLOGLOGCOUNTING | REFRESH | MANUAL | HISTOGRAM | SIMPLE | TOPK | SKETCH | BUCKETS | QERROR | QTHETA | PERCENT | ACCURACY | PREFIXBITS 
    | VALID | ESTIMATION | DEPENDENCY | INITIAL | SCHEDULER | JOB | CRON | THRESHOLD | FLEXIBILITY | GENERATED | ALWAYS | IDENTITY | CLIENTSIDE | ENCRYPTION | RANDOM | REFERENCES 
    | ENFORCED | VALIDATED | INITIALLY | HIDDEN_ | BTREE | CPBTREE | RESTRICT | PERIOD | VERSIONING | GET_NUM_SERVERS | OTHERS | PARTITIONS | PHYSICAL | NON | PARTITIONING 
    | STORAGE | MOVE | PARAMETER | UNSET | ANNOTATIONS | REORGANIZE | LOB | CLEAR | FOREIGN | PRELOAD | THREADS | ASSOCIATION | FILTER | NUMA 
    | NODE | DYNAMIC | ROUNDROBIN | LOCATION | DISTANCE | REPLICA | SUBPARTITIONS | SOURCE | AUTOMERGE | SYNCHRONOUS | ASYNCHRONOUS | PRIORITY | RECLAIM | PROMOTION | UNUSED 
    | RETENTION | SPACE | SERIES | OWNER | SESSION | MASK | CANCEL | MOVABLE | LOCATIONS | ANONYMIZATION | ALGORITHM | EXPRESSION | MACROS | ASSOCIATIONS | PRIVILEGE | VIRTUAL 
    | PROPERTY | DEFINITION | ANNOTATE | ROLE | USERGROUP | CERTIFICATE | PUBLIC | DETECTION | MIME | SYNC | HRONOUS | ASYNC | FAST | PREPROCESS | ANALYSIS | TOKEN | SEPARATORS 
    | EVERY | MINUTES | DOCUMENTS | GRAPH | WORKSPACE | EDGE | TARGET | VERTEX | PROJECTION | OWNED | SYNONYM | PRECEDES | DO | BREAK | PARALLEL | GLOBAL | TEMPORARY | WITHOUT 
    | LOGGING | PRESERVE | EQUIDISTANT | MISSING | ELEMENTS | ALLOWED | ALTERNATE | DUPLICATES | REMOTE | POLICY | TRAIL | SYSLOG | CSV | AUDITING | SUCCESSFUL | UNSUCCESSFUL 
    | EMERGENCY | ALERT | CRITICAL | INFO | ACTIONS | PRINCIPALS | SUBSCRIPTION | KEYPAIR | GEOCODE | DISCONNECT | STOP | SERVICE | APPLICATION | LICENSE | EXPORT | IMPORT 
    | REPOSITORY | CHANGE | PSE | CREDENTIAL | COMPONENT | PURPOSE | JWT | PROVIDER | ISSUER | CLAIM | SENSITIVE | INSENSITIVE | CREATION | STANDARD | RESTRICTED | LDAP 
    | AUTHORIZATION | HAS | LOOKUP | URL | DN | MEMBER_OF | SSL | SAML | SUBJECT | ENTITY | ADAPTER | FILE | LINKED | OBJECTS | CREATOR | PASSWORD | FORCE_FIRST_PASSWORD_CHANGE 
    | NOW | FOREVER | CLIENT | LOCALE | EMAIL | ADDRESS | RSERVE | SOURCES | THREAD | IDENTIFIED | EXTERNALLY | ATTEMPTS | LIFETIME | DEACTIVATE | KERBEROS | X509 | SAP | TICKET 
    | ASSERTION | OWN | ADMIN | OPERATOR | BACKUP | CATALOG | SCENARIO | RECOVERY | ROOT | EXTENDED | INIFILE | MONITOR | OPTIMIZER | RESOURCE | STRUCTUREDPRIVILEGE | REPLICATION 
    | TRACE | TRUST | WORKLOAD | CAPTURE | REPLAY | PRIVILEGES | METADATA | CDS | UNMASKED | PROCESS | USAGE | MESSAGING | ATTACH | DEBUGGER | GRANTED | R | HEADER | POS
    | RECOMPILE | INVALID | SCALASPARK | BACKUP_ID | BACKINT | COMPLETE | RETAINED | ACCESS | KEYS | LIST | POSITION | VOLUME | RECOVER | SECURE | STORE | PRIVATE | TOOLOPTION 
    | WORKERGROUPS | PERSISTENCE | RESUME | GUID_ID | DELIMITED | FIELD | OPTIONALLY | ENCLOSED | BINARY | DEPENDENCIES | SCRAMBLE | STRIP | CONTROL | FORMAT | ERROR | FAIL 
    | SCAN | SAP_TIMEZONE_DATASET | SHAPEFILE | LOAD_HISTORY | KEYCOPY | ENCRYPTED | OS | FINALIZE | CONTROLLED | FALLBACK | REMOVE | MANAGEMENT | PROPERTIES | COREFILE | BACKUPS 
    | CLASS | MAPPING | WILDCARD | HOST | OVERRIDE | ABSTRACT | RECONFIGURE | DATAVOLUME | SITE | PLACEMENT | TIMEZONE | DATASET | TRACES | CLIENTPKI | CERTIFICATES | CA 
    | SOLACE
    ;

overClauseKeyword
    : AVG | CORR | LAG | LEAD | MAX | MEDIAN | MIN | NTILE | RATIO_TO_REPORT | ROW_NUMBER | SUM | VARIANCE | REGR_
    | STDDEV | VAR_ | COVAR_
    ;
withinOrOverClauseKeyword
    : CUME_DIST | DENSE_RANK | LISTAGG | PERCENT_RANK | PERCENTILE_CONT | PERCENTILE_DISC | RANK
    ;
standardPredictionFunctionKeyword
    : PREDICTION | PREDICTION_BOUNDS | PREDICTION_COST | PREDICTION_DETAILS | PREDICTION_PROBABILITY | PREDICTION_SET
    ;
aggName
    : CORR | CORR_SPEARMAN | COUNT | MIN | MEDIAN | MAX | SUM | AVG | STDDEV | VAR  | STDDEV_POP
    | VAR_POP | STDDEV_SAMP | VAR_SAMP
    ;
securityMode : DEFINER | INVOKER ;
multisetType
    : MEMBER | SUBMULTISET
    ;
likeType
    : LIKE | LIKEC | LIKE2 | LIKE4
    ;
forIncrementDecrementType
    : INCREMENT | DECREMENT
    ;
statType: ENABLE | DISABLE;
datetimeValueFunction
    : CURRENT_DATE | CURRENT_TIME | CURRENT_TIMESTAMP | CURRENT_UTCDATE | CURRENT_UTCTIME | CURRENT_UTCTIMESTAMP
    ;
stringValueFunction
    : SYSUUID | CURRENT_USER | CURRENT_SCHEMA | SESSION_USER
    ;
referenceAction
    : RESTRICT | CASCADE | SET NULL | SET DEFAULT
    ;
onOff: ON | OFF;
immediate: IMMEDIATE | DEFERRED;
syncMode: SYNCHRONOUS | ASYNCHRONOUS;
cacheType: STATIC | DYNAMIC;
orderType: ASC | DESC;
validUsage : ESTIMATION | DATA DEPENDENCY;
dropOption : (CASCADE | RESTRICT)?;
rootKeyType: PERSISTENCE | APPLICATION | BACKUP | LOG;
/* words End */

/* literals Start */
literals
    : stringLiterals
    | numberLiterals
    | dateTimeLiterals
    | hexadecimalLiterals
    | bitValueLiterals
    | booleanLiterals
    | nullValueLiterals
    | valueLiterals
    | intervalLiterals
    | bindVariable
    ;

stringLiterals
    : STRING_ | NCHAR_TEXT
    ;

stringLiteralsList
    : LP_? stringLiterals (COMMA_ stringLiterals)* RP_?
    ;

stringKeyValueList
    : LP_? stringLiterals EQ_ stringLiterals (COMMA_ stringLiterals EQ_ stringLiterals)* RP_?
    ;

numberLiterals
   : MINUS_? (INTEGER_ | NUMBER_)
   ;

numberLiteralsComma
    : INTEGER_ (COMMA_ INTEGER_)?
    ;

numberLiteralsList
    : numberLiterals (COMMA_ numberLiterals)*
    | LP_ numberLiteralsList RP_
    ;

partitionId
    : numberLiterals
    | STRING_
    | identifier
    | OTHERS
    ;

partitionIds
    : partitionId (COMMA_ partitionId)*
    | LP_ partitionIds RP_
    ;

dateTimeLiterals
    : DATE stringLiterals
    | TIMESTAMP (stringLiterals | bindVariable) (AT TIME ZONE stringLiterals)?
    | DBTIMEZONE
    | SESSIONTIMEZONE
    ;

hexadecimalLiterals
    : HEX_DIGIT_
    ;

bitValueLiterals
    : BIT_NUM_
    ;

booleanLiterals
    : TRUE | FALSE
    ;

nullValueLiterals
    : NULL
    ;

valueLiterals
    : MINVALUE | MAXVALUE | DEFAULT | datetimeValueFunction | stringValueFunction
    ;

intervalLiterals
    : INTERVAL intervalValue intervalUnit intervalPrecision? (TO intervalUnit intervalPrecision?)?
    ;

intervalPrecision
    : LP_ (INTEGER_ | bindVariable) (COMMA_ (INTEGER_ | bindVariable))? RP_
    ;

intervalValue
    : stringLiterals | bindVariable | functionCall | numberLiterals
    ;

intervalUnit
    : DAY | HOUR | MINUTE | SECOND | YEAR | MONTH
    ;

rangeValueLiterals
    : stringLiterals | numberLiterals
    ;

hostPort
    : STRING_
    | (identifier | IP_ADDRESS) COLON_ INTEGER_
    | LP_ hostPort (COMMA_ hostPort)* RP_
    ;

passwordLiterals
    : stringLiterals | identifier | PASSWORD_INT_
    ;

bindVariable
    : bindVar (INDICATOR? bindVar)? (DOT_ regularFunction)*
    | TYPE_CAST_? (SQL_ERROR_CODE | SQL_ERROR_MESSAGE)
    ;

bindVar
    : COLON_ (identifier | INTEGER_)
    | QUESTION_ // not in SQL, not in Oracle, not in OCI, use this for JDBC
    ;
/* literals End */

/* DataType Start */
dataType
    : dataTypeName arrayTypeOption? dataTypeLength? (WITH LOCAL? TIME ZONE)?
    | REF? typeName arrayTypeOption? (PERCENT_ROWTYPE | PERCENT_TYPE)?
    | INTERVAL (YEAR | DAY) (LP_ expr RP_)? TO (MONTH | SECOND) (LP_ expr RP_)?
    | arrayDatatype
    ;

arrayTypeOption
    : arrayConstructor (WITHOUT DUPLICATES)?
    ;

arrayDatatype
    : sqlType ARRAY EQ_ arrayConstructor
    ;

arrayConstructor
    : ARRAY exprList?
    ;

dataTypeName
    : BINARY_INTEGER | PLS_INTEGER | NATURAL | BINARY_FLOAT | BINARY_DOUBLE | BIGINT | NATURALN | POSITIVE | POSITIVEN
    | SECONDDATE | SIGNTYPE | SIMPLE_INTEGER | NVARCHAR | DEC | INTEGER | INT | NUMERIC | SMALLINT | NUMBER | DECIMAL
    | DOUBLE PRECISION? | FLOAT | REAL | NCHAR | LONG RAW? | CHAR | CHARACTER | VARCHAR2 | VARCHAR | STRING | RAW
    | BOOLEAN | DATE | ROWID | UROWID | YEAR | MONTH | DAY | HOUR | MINUTE | SECOND | TIMEZONE_HOUR | TIMEZONE_MINUTE
    | TIMEZONE_REGION | TIMEZONE_ABBR | TIMESTAMP | TIMESTAMP_UNCONSTRAINED | TIMESTAMP_TZ_UNCONSTRAINED
    | TIMESTAMP_LTZ_UNCONSTRAINED | YMINTERVAL_UNCONSTRAINED | DSINTERVAL_UNCONSTRAINED | BFILE | BLOB | CLOB | NCLOB
    | MLSLABEL | SHORTTEXT | TEXT | TIME | TINYINT | SMALLDECIMAL | ALPHANUM | VARBINARY
    ;

dataTypeLength
    : LP_ length=numberLiteralsComma type=(CHAR | BYTE)? RP_
    ;

sqlType
	: DATE
	| TIME
	| TIMESTAMP
	| SECONDDATE
	| TINYINT
	| SMALLINT
	| INTEGER
	| INT
	| BIGINT
	| DECIMAL (LP_ numberLiteralsComma RP_)?
	| SMALLDECIMAL
	| REAL
	| DOUBLE
	| VARCHAR (LP_ numberLiterals RP_)?
	| NVARCHAR (LP_ numberLiterals RP_)?
	| ALPHANUM (LP_ numberLiterals RP_)?
	| VARBINARY (LP_ numberLiterals RP_)?
	| BLOB
	| CLOB
	| NCLOB
	;
/* DataType End */

/* Operator Start */
relationalOperator
	: EQ_ | NEQ_ | GT_ | LT_ | GTE_ | LTE_
	;

setOperator
    : UNION (ALL | DISTINCT)? | ((INTERSECT | EXCEPT | MINUS) DISTINCT?)
    ;
/* Operator End */

/* expressions Start */
columnNameExpr
    : columnName | expr
    ;

columnNameExprs
    : columnNameExpr (COMMA_ columnNameExpr)*
    ;

columnNameExprList
    : LP_ columnNameExprs RP_
    ;

exprs
    : expr (COMMA_ expr)*
    ;

exprList
    : LP_ exprs? RP_
    ;

expr
    : expr andOperator expr
    | expr orOperator expr
    | notOperator expr
    | LP_ expr RP_
    | expr datetimeExpr
    | predicate
    | CURRENT OF cursorName
    ;

andOperator
    : AND | AND_
    ;

orOperator
    : OR | OR_
    ;

notOperator
    : NOT | NOT_
    ;

datetimeExpr
    : AT (LOCAL | TIME ZONE expr) | intervaleExpr
    ;

intervaleExpr
    : DAY (LP_ expr RP_)? TO SECOND (LP_ expr RP_)?
    | YEAR (LP_ expr RP_)? TO MONTH
    ;

bitExpr
    : bitExpr VERTICAL_BAR_ bitExpr
    | bitExpr AMPERSAND_ bitExpr
    | bitExpr SIGNED_LEFT_SHIFT_ bitExpr
    | bitExpr SIGNED_RIGHT_SHIFT_ bitExpr
    | bitExpr PLUS_ bitExpr
    | bitExpr MINUS_ bitExpr
    | bitExpr ASTERISK_ bitExpr
    | bitExpr SLASH_ bitExpr
    | bitExpr MOD_ bitExpr
    | bitExpr CARET_ bitExpr
    | bitExpr DOT_ bitExpr
    | bitExpr ARROW_ bitExpr
    | simpleExpr (LBT_ modelExprElement RBT_)?
    ;

modelExprElement
    : (ANY | condition) (COMMA_ (ANY | condition))*
    | singleColumnForLoop (COMMA_ singleColumnForLoop)*
    | multiColumnForLoop
    ;

singleColumnForLoop
    : FOR columnNameExpr (inItem | (LIKE expr)? FROM expr TO expr forIncrementDecrementType expr)
    ;

multiColumnForLoop
    : FOR columnNameExprList inItem
    ;

simpleExpr
    : (PLUS_ | MINUS_ | PRIOR | CONNECT_BY_ROOT | NEW | DISTINCT | ALL | UNIQUE | MULTISET) simpleExpr
    | caseStatement
    | functionCall
    | jsonObjectExpression
    | (SOME | EXISTS | ALL | ANY | CURSOR)? (select | exprList)
    | arrayConstructor
    | columnName
    | variableName
    | objectName LP_ PLUS_ RP_
    | literals
    | generalElement
    | ASTERISK_
    ;

functionCall
    : aggregateFunction | standardFunction | regularFunction
    ;

aggregateFunction
    : COUNT LP_ expr RP_ overClause?
    | aggName LP_ ( ALL | DISTINCT )? expr RP_
    | STRING_AGG LP_ expr (COMMA_ delimiter)? aggregateOrderByClause? RP_
    ;

aggregateOrderByClause
	: ORDER BY expr orderType? ( NULLS FIRST | NULLS LAST )?
	;

regularFunction
    : routineName functionArgument?
    ;

functionArgument
    : LP_ argument? (COMMA_ argument )* orderByClause? RP_ keepClause?
    ;

argument
    : (objectName EQ_ GT_)? expr
    ;

standardFunction
    : overClauseKeyword functionArgumentAnalytic overClause?
    | regularId functionArgumentModeling usingClause?
    | (CAST | XMLCAST) LP_ expr AS dataType RP_
    | CHR LP_ expr USING NCHAR_CS RP_
    | COLLECT LP_ expr orderByClause? RP_
    | withinOrOverClauseKeyword functionArgument withinOrOverPart+
    | DECOMPOSE LP_ expr (CANONICAL | COMPATIBILITY)? RP_
    | EXTRACT LP_ regularId FROM expr RP_
    | (FIRST_VALUE | LAST_VALUE) functionArgumentAnalytic respectOrIgnoreNulls? overClause
    | standardPredictionFunctionKeyword LP_ exprs costMatrixClause? usingClause? RP_
    | TRANSLATE LP_ expr (USING (CHAR_CS | NCHAR_CS))? (COMMA_ expr)* RP_
    | TREAT LP_ expr AS REF? dataType RP_
    | TRIM LP_ ((LEADING | TRAILING | BOTH)? stringLiterals? FROM)? expr RP_
    | XMLAGG LP_ expr orderByClause? RP_ (DOT_ regularFunction)?
    | (XMLCOLATTVAL|XMLFOREST) LP_ xmlMultiuseExpressionElement (COMMA_ xmlMultiuseExpressionElement)* RP_ (DOT_ regularFunction)?
    | XMLELEMENT LP_ (ENTITYESCAPING | NOENTITYESCAPING)? (NAME | EVALNAME)? expr (COMMA_ xmlAttributesClause)?
        (COMMA_ expr asClause?)* RP_ (DOT_ regularFunction)?
    | XMLEXISTS LP_ expr xmlPassingClause? RP_
    | XMLPARSE LP_ (DOCUMENT | CONTENT) expr WELLFORMED? RP_ (DOT_ regularFunction)?
    | XMLPI LP_ (NAME objectName | EVALNAME expr) (COMMA_ expr)? RP_ (DOT_ regularFunction)?
    | XMLQUERY LP_ expr xmlPassingClause? RETURNING CONTENT (NULL ON EMPTY)? RP_ (DOT_ regularFunction)?
    | XMLROOT LP_ expr (COMMA_ xmlrootParamVersionPart)? (COMMA_ xmlrootParamStandalonePart)? RP_ (DOT_ regularFunction)?
    | XMLSERIALIZE LP_ (DOCUMENT | CONTENT) expr (AS dataType)? xmlserializeParamEncondingPart? xmlserializeParamVersionPart?
        xmlserializeParamIdentPart? ((HIDE | SHOW) DEFAULTS)? RP_ (DOT_ regularFunction)?
    | XMLTABLE LP_ xmlNamespacesClause? expr xmlPassingClause? (COLUMNS xmlTableColumn (COMMA_ xmlTableColumn))? RP_ (DOT_ regularFunction)?
    ;

functionArgumentAnalytic
    : LP_ (argument respectOrIgnoreNulls?)? (COMMA_ argument respectOrIgnoreNulls?)* RP_ keepClause?
    ;

respectOrIgnoreNulls
    : (RESPECT | IGNORE) NULLS
    ;

functionArgumentModeling
    : LP_ columnNameExpr (COMMA_ (numberLiterals | NULL) (COMMA_ (numberLiterals | NULL))?)?
      USING (tableWithLinkName DOT_ ASTERISK_ | ASTERISK_ | expr asClause? (COMMA_ expr asClause?)*)
      RP_ keepClause?
    ;

xmlMultiuseExpressionElement
    : expr (AS (identifier | EVALNAME expr))?
    ;

xmlAttributesClause
    : XMLATTRIBUTES
     LP_ (ENTITYESCAPING | NOENTITYESCAPING)? (SCHEMACHECK | NOSCHEMACHECK)?
     xmlMultiuseExpressionElement (COMMA_ xmlMultiuseExpressionElement)* RP_
    ;

xmlPassingClause
    : PASSING (BY VALUE)? expr asClause? (COMMA_ expr asClause?)
    ;

xmlrootParamVersionPart
    : VERSION (NO VALUE | expr)
    ;

xmlrootParamStandalonePart
    : STANDALONE (YES | NO VALUE?)
    ;

xmlserializeParamEncondingPart
    : ENCODING expr
    ;

xmlserializeParamVersionPart
    : VERSION expr
    ;

xmlserializeParamIdentPart
    : NO INDENT
    | INDENT (SIZE EQ_ expr)?
    ;

xmlNamespacesClause
    : XMLNAMESPACES LP_ (expr asClause)? ( expr asClause)* xmlGeneralDefaultPart? RP_
    ;

xmlGeneralDefaultPart
    : DEFAULT expr
    ;

xmlTableColumn
    : xmlColumnName
      (FOR ORDINALITY | dataType (PATH expr)? xmlGeneralDefaultPart?)
    ;

jsonObjectExpression
    : LBE_ jsonItem (COMMA_ jsonItem)* RBE_
    ;

jsonItem
    : jsonKey COLON_ jsonKeyValue
    ;

jsonKey
    : DOUBLE_QUOTED_TEXT
    ;

jsonKeyValue
    : STRING_ | numberLiterals | booleanLiterals | nullValueLiterals | jsonObjectExpression
    | LBT_ (jsonKeyValue (COMMA_ jsonKeyValue)*)? RBT_
    ;

generalElement
    : regularFunction (DOT_ regularFunction)*
    ;

condition
	: expr
	;

// selectListExpression
associationExpression
    : associationRef (DOT_ associationRef)*
    ;

associationRef
    : columnName parameterizedViewValueList? (LBT_ condition associationCardinality? RBT_)?
    ;

parameterizedViewValueList
    : LP_ parameterViewValue (COMMA_ parameterViewValue)* RP_
    ;

parameterViewValue
    : (parameterName ARROW_)? literals
    ;

associationCardinality
    :  USING ( ONE | MANY ) TO ( ONE | MANY ) JOIN
    ;
/* expressions End*/

/* predicates Statr*/
predicate
	: comparisonPredicate
	| rangePredicate
	| containsPredicate
	| existPredicate
	| inPredicate
	| likePredicate
	| likeRegeprPredicate
	| memberOfPredicate
	| isPredicate
	| bitExpr
	;

comparisonPredicate
	: bitExpr relationalOperator ( ANY | SOME | ALL )? (exprs | subquery)
	;

rangePredicate
    : bitExpr NOT? BETWEEN bitExpr AND predicate
    ;

containsPredicate
    : CONTAINS LP_ containsColumns COMMA_ stringLiterals (COMMA_ searchSpecifier)? RP_
    ;

containsColumns
    : ASTERISK_ | columnNameExpr | columnNameExprList
    ;

searchSpecifier
    : searchType? optSearchSpecifier2List?
    | searchSpecifier2List
    ;

optSearchSpecifier2List
    : LP_ RP_
    | searchSpecifier2List
    ;

searchType
    : exactSearch | fuzzySearch | linguisticSearch
    ;

searchSpecifier2List
    : searchSpecifier2 (COMMA_ searchSpecifier2)*
    ;

searchSpecifier2
    : FULLTEXT ( onOff | AUTOMATIC )
    | LANGUAGE LP_ identifier RP_
    | WEIGHT LP_ NUMBER_ (COMMA_ NUMBER_)* RP_
    ;

exactSearch
    : EXACT (LP_ identifier RP_)?
    ;

fuzzySearch
    : FUZZY (LP_ fuzzyParams (COMMA_ fuzzyParams)* RP_)?
    ;

fuzzyParams
    : NUMBER_ (COMMA_ identifier)?
    | NULL COMMA_ identifier
    ;

linguisticSearch
    : LINGUISTIC (LP_ identifier RP_)?
    ;

existPredicate
    : NOT? EXISTS subquery
    ;

inPredicate
    : bitExpr NOT? inItem
    ;

inItem
    : IN (exprList | expr)
    ;

likePredicate
    : bitExpr NOT? likeType simpleExpr (ESCAPE simpleExpr)?
    ;

likeRegeprPredicate
    : bitExpr LIKE_REGEXPR simpleExpr (FLAG FLAG_)?
    ;

memberOfPredicate
    : bitExpr NOT? multisetType OF? expr
    ;

isPredicate
    : bitExpr IS NOT? (NULL | NAN | PRESENT | INFINITE | A_LETTER SET | EMPTY | OF TYPE? LP_ ONLY? dataType (COMMA_ dataType)* RP_)
    ;
/* predicates End */

/* Statements Start */
/* --------------------- Basic Clause Start --------------------- */
orReplace
    : (OR REPLACE)?
    ;

asClause
    : AS? alias
    ;

keepClause
    : KEEP LP_ DENSE_RANK (FIRST | LAST) orderByClause RP_ overClause?
    ;

overClause
    : OVER LP_ queryPartitionClause? (orderByClause windowingClause?)? RP_
    ;

queryPartitionClause
    : PARTITION BY (LP_ subquery RP_ | exprs | exprList)
    ;

windowingClause
    : windowingType (BETWEEN windowingElements AND windowingElements | windowingElements)
    ;

windowingType
    : ROWS | RANGE
    ;

windowingElements
    : UNBOUNDED PRECEDING
    | CURRENT ROW
    | expr (PRECEDING|FOLLOWING)
    ;

usingClause
    : USING ('*' | usingElement (COMMA_ usingElement)*)
    ;

usingElement
    : (IN OUT? | OUT)? selectList asClause?
    ;

withinOrOverPart
    : WITHIN GROUP LP_ orderByClause RP_
    | overClause
    ;

costMatrixClause
    : COST (MODEL AUTO? | LP_ costClassName (COMMA_ costClassName)* RP_ VALUES exprList)
    ;

partitionExtensionClause
    : (SUBPARTITION | PARTITION) FOR? exprList
    ;
/* --------------------- Basic Clause End --------------------- */

/* --------------------- plsqlBlock Strat --------------------*/
do
    : DO doParameterClause? plsqlBlock
    ;

doParameterClause
    : LP_ namedParameter (COMMA_ namedParameter)* RP_
    ;

plsqlBlock
    : labelDeclaration* declareClause? body
    ;

namedParameter
    : (IN | OUT) parameterName dataType ARROW_ expr
    ;

// label Start
labelDeclaration
    : SIGNED_LEFT_SHIFT_ label SIGNED_RIGHT_SHIFT_
    ;
// label End

// declare Start
declareClause
    : (DECLARE declareItem)+
    ;

declareItem
    : variableDeclaration
    | subtypeDeclaration
    | cursorDeclaration
    | exceptionDeclaration
    | pragmaDeclaration
    | recordDeclaration
    | tableDeclaration
    | procConditionDeclaration
    ;

variableDeclaration
    : variableNames (
        CONSTANT? (dataType | AUTO) (NOT NULL)? defaultValuePart?
        | tableTypeDefinition
        | ROW LIKE? expr
    ) SEMI_
    ;

defaultValuePart
    : (ASSIGNMENT_OPERATOR_ | DEFAULT | EQ_)? expr
    ;

tableTypeDefinition
    : TABLE LP_ columnListDefinition RP_
    ;

columnListDefinition
    : columnElem (COMMA_ columnElem)*
    ;

columnElem
    : columnName dataType (SEARCH KEY columnNameList)?
    ;

subtypeDeclaration
    : SUBTYPE typeName IS dataType (RANGE expr RANGE_OPERATOR_ expr)? (NOT NULL)? SEMI_
    ;

cursorDeclaration
    : CURSOR cursorName parameterList? (RETURN dataType)? ((IS | FOR) select)? SEMI_
    ;

parameterList
    : parameter (COMMA_ parameter)*
    | LP_ parameterList? RP_
    ;

parameter
	: ( IN | OUT | INOUT)? parameterName paramType defaultValuePart?
	;

paramType
	: dataType
	| tableTypeDefinition
    | TABLE LP_ ANY_ RP_
	;

exceptionDeclaration
    : exceptionName EXCEPTION SEMI_
    ;

pragmaDeclaration
    : PRAGMA (SERIALLY_REUSABLE
    | AUTONOMOUS_TRANSACTION
    | EXCEPTION_INIT LP_ exceptionName COMMA_ numberLiterals RP_
    | INLINE LP_ objectName COMMA_ expr RP_
    | RESTRICT_REFERENCES LP_ (objectName | DEFAULT) (COMMA_ objectName)+ RP_) SEMI_
    ;

recordDeclaration
    : TYPE typeName IS (RECORD LP_ fieldSpec (COMMA_ fieldSpec)* RP_ | REF CURSOR (RETURN dataType)?) SEMI_
    | recordName typeName (PERCENT_ROWTYPE | PERCENT_TYPE) SEMI_
    ;

fieldSpec
    : columnNameExpr (dataType | AUTO)? (NOT NULL)? defaultValuePart?
    ;

tableDeclaration
    : TYPE typeName IS (TABLE OF dataType tableIndexedByPart? (NOT NULL)? | varrayTypeDef) SEMI_
    | (
        variableNames CONSTANT? TABLE? LIKE (tableName | bindVariable) AUTO? defaultValuePart? | fieldSpec
    ) (SEARCH KEY columnNameList)? SEMI_
    ;

tableIndexedByPart
    : (INDEXED | INDEX) BY dataType
    ;

varrayTypeDef
    : (VARRAY | VARYING ARRAY) LP_ expr RP_ OF dataType (NOT NULL)?
    ;

procConditionDeclaration
    : variableName CONDITION (FOR sqlErrorCode)? SEMI_
    ;

sqlErrorCode
    : SQL_ERROR_CODE? numberLiterals
    ;
// declare End

// body Start
body
    : (HEADER ONLY ON INVALID)? BEGIN
        blockProperties? (procUsing+)? declareClause? procHandlerList? statementList exceptionClause?
    END hintClause?
    ;

procBody
    : blockProperties? declareClause? procHandlerList? statementList
    ;

blockProperties
    : ((PARALLEL | SEQUENTIAL | AUTONOMOUS ) EXECUTION)+
    ;

procUsing
    : USING objectName asClause
    ;

procHandlerList
    : exceptionStatement+
    ;

exceptionClause
    : EXCEPTION exceptionHandler+
    ;

exceptionHandler
    : WHEN exceptionName (OR exceptionName)* THEN statementList
    ;

statementList
    : statement+
    ;

statement
    : (plsqlBlock
    | assignmentStatement
    | ifStatement
    | loopStatement
    | whileStatement
    | forStatement
    | forallStatement
    | foreachStatement
    | procSignal
    | procResignal
    | sqlStatement
    | CALL functionCall
    | exitStatement
    | continueStatement
    | openStatement
    | fetchStatement
    | closeStatement
    | returnStatement
    | tabvarSearchStatement
    | procSingleAssign
    | procMultiAssign
    | executeImmediate
    | gotoStatement
    | nullStatement
    | raiseStatement
    | caseStatement
    | bindVariable
    ) SEMI_?
    ;

assignmentStatement
    : (generalElement | bindVariable) ASSIGNMENT_OPERATOR_ expr
    | variableName LBT_ expr RBT_ ASSIGNMENT_OPERATOR_ expr
    ;

continueStatement
    : CONTINUE label? (WHEN condition)?
    ;

exitStatement
    : EXIT label? (WHEN condition)?
    | BREAK
    ;

gotoStatement
    : GOTO label
    ;

ifStatement
    : IF condition THEN procBody elsifPart* elsePart? END IF
    ;

elsifPart
    : ELSEIF condition THEN procBody
    ;

elsePart
    : ELSE procBody
    ;

whileStatement
    : WHILE condition DO procBody END WHILE
    ;

loopStatement
    : LOOP procBody END LOOP
    ;

forStatement
    : FOR cursorLoopParam DO procBody END FOR
    ;

cursorLoopParam
    : indexName IN REVERSE? lowerBound RANGE_OPERATOR_ upperBound
    | recordName IN ( cursorName exprList? | LP_ select RP_)
    ;

foreachStatement
    : FOR columnName AS columnName exprs? DO procBody END FOR
    ;

forallStatement
    : FORALL indexName IN boundsClause procBody (SAVE EXCEPTIONS)?
    ;

boundsClause
    : lowerBound RANGE_OPERATOR_ upperBound
    | INDICES OF collectionName betweenBound?
    | VALUES OF indexName
    ;

betweenBound
    : BETWEEN lowerBound AND upperBound
    ;

lowerBound
    : expr
    ;

upperBound
    : expr
    ;

nullStatement
    : NULL
    ;

raiseStatement
    : RAISE exceptionName?
    ;

returnStatement
    : RETURN expr?
    ;

caseStatement
    : CASE simpleExpr? caseWhen+ caseElse? END
    ;

caseWhen
    : WHEN expr THEN expr
    ;

caseElse
    : ELSE expr
    ;

sqlStatement
    : selectInto
    | select
    | selectFromEmbeddedFunctionStmt
    | insert
    | delete
    | update
    | replace
    | upsert
    | call
    | CREATE swallowToSemi
    | DROP swallowToSemi
    | ALTER swallowToSemi
    | GRANT swallowToSemi
    | truncateTable
    | merge
    | mergeDelta
    | loadStatement
    | unload
    | explain
    | declareStatement
    | exceptionStatement
    | cursorManipulationStatements
    | tclStatement
    | setConstraints
    ;

selectInto
    : SELECT selectList INTO variableNames selectFromClause whereClause? groupByClause? havingClause?
    (setOperator subquery)* orderByClause? limitClause?
    ;

selectFromEmbeddedFunctionStmt
    : SELECT selectList FROM SQL FUNCTION embeddedFuncReturns BEGIN procBody END
    ;

embeddedFuncReturns
    : RETURNS (tableRef | columnNames | parameterList)
    ;

swallowToSemi
    : ~( ';' )+
    ;

executeImmediate
    : (EXECUTE IMMEDIATE | EXEC) expr (
        intoClause defaultClause? usingClause?
        | usingClause dynamicReturningClause?
        | dynamicReturningClause
    )? (READS SQL DATA)?
    ;

intoClause
    : INTO variableNames
    | BULK COLLECT INTO variableNames
    ;

dynamicReturningClause
    : (RETURNING | RETURN) intoClause
    ;

declareStatement
    : DECLARE objectName dataType? defaultValuePart?
    ;

exceptionStatement
    : DECLARE (EXIT | CONTINUE) HANDLER FOR procConditionValueList statementList
    ;

procConditionValueList
    : procConditionValue (COMMA_ procConditionValue)*
    ;

procConditionValue
	: SQLEXCEPTION
	| SQLWARNING
    | sqlErrorCode
    | variableName
    ;

cursorManipulationStatements
    : closeStatement
    | openStatement
    | fetchStatement
    | openForStatement
    ;

closeStatement
    : CLOSE cursorName
    ;

openStatement
    : OPEN cursorName exprList?
    ;

fetchStatement
    : FETCH cursorName intoClause
    ;

openForStatement
    : OPEN variableName FOR (select | expr) usingClause?
    ;

tclStatement
    : setTransaction
    | commit
    | rollback
    | savepoint
    | lockTable
    | releaseSavepoint
    ;

procSignal
    : SIGNAL signalValue setSignalInfo?
    ;

procResignal
    : RESIGNAL signalValue? setSignalInfo?
    ;

signalValue
    : identifier | sqlErrorCode
    ;

setSignalInfo
    : SET MESSAGE_TEXT EQ_ stringLiterals
    ;

tabvarSearchStatement
    : POS EQ_ variableName DOT_ SEARCH LP_ columnNameList COMMA_ exprList (COMMA_ expr)? RP_
    ;

procSingleAssign
	: variableName EQ_ (expr | unnestFunction)
	;

unnestFunction
    : UNNEST LP_ variableNames RP_ (WITH ORDINALITY)? (AS tableName? columnNameList)?
    ;

procMultiAssign
    : LP_ variableNames RP_ ASSIGNMENT_OPERATOR_ functionCall
    ;
// body End
/* --------------------- plsqlBlock End --------------------*/

/*
* TCL Document link
* @link https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/20a3ae8a75191014b039b36c43e2029d.html?locale=en-US
*/
/* --------------------- TCL Start -------------------- */
setTransaction
    : SET TRANSACTION (
         ISOLATION LEVEL (READ COMMITTED | REPEATABLE READ | SERIALIZABLE)
         | (READ ONLY | READ WRITE)
         | LOCK WAIT TIMEOUT numberLiterals
         | AUTOCOMMIT? DDL onOff
    )
    ;

commit
    : COMMIT
    ;

rollback
    : ROLLBACK (TO SAVEPOINT savepointName)?
    ;

savepoint
    : SAVEPOINT savepointName
    ;

releaseSavepoint
    : RELEASE SAVEPOINT savepointName
    ;

lockTable
    : LOCK TABLE tableName partitionPart?
    IN (EXCLUSIVE | INTENTIONAL EXCLUSIVE) MODE (WAIT INTEGER_ | NOWAIT)?
    ;

partitionPart
    : PARTITION numberLiteralsList
    ;
/* --------------------- TCL End -------------------- */

/*
* DML Document link
* @link https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/209eaa85751910149a30f95c936075be.html?locale=en-US
*/
/* --------------------- DML Start --------------------*/
call
    : CALL procedureName functionArgument hintClause?
    | CALL memberName functionArgument
    ;

loadStatement
    : LOAD tableName HISTORY? (DELTA | ALL | columnNameList)
    ;

unload
    : UNLOAD tableName (partitionRestriction | persistentMemoryClause)?
    ;

explain
    : EXPLAIN PLAN ( SET STATEMENT_NAME EQ_ stringLiterals )? FOR (
        select | SQL PLAN CACHE ENTRY INTEGER_ ( VARIANT INTEGER_ )?
    )
    ;

mergeDelta
    : MERGE HISTORY? DELTA OF tableName (PART INTEGER_)? (WITH PARAMETERS stringKeyValueList)? (FORCE REBUILD)?
    ;

//  merge into Start
merge
    : MERGE INTO tableName partitionRestriction? asClause? USING tableName ON condition mergeOperationSpecification
    ;

mergeOperationSpecification
    : whenMatchedClause whenNotMatchedClause?
    | whenNotMatchedClause
    ;

whenMatchedClause
    : WHEN MATCHED ( AND condition )? THEN whenMatchedSpecification
    ;

whenMatchedSpecification
    : DELETE | UPDATE setClause
    ;

whenNotMatchedClause
    : WHEN NOT MATCHED ( AND condition )? THEN INSERT columnNameList? insertValuesClause
    ;
//  merge into End

replace
    : REPLACE tableName columnNameList? (
      insertValuesClause (whereClause | WITH PRIMARY KEY)?
      | select
    )
    ;

upsert
    : UPSERT tableName partitionRestriction? columnNameList? (
        insertValuesClause (whereClause | WITH PRIMARY KEY)?
        | select
    )
    ;

// insert Start
insert
    : INSERT INTO tableName partitionRestriction? asClause? columnNameList?
    (insertValuesClause | overridingClause? select)
    (hintClause | WITH NOWAIT)?
    ;

insertValuesClause
    : VALUES exprList asClause?
    ;

overridingClause
    : OVERRIDING ( SYSTEM | USER ) VALUE
    ;
// insert End

// update Start
update
    : UPDATE topClause? tableName asClause? forPortionOfApplicationTimeClause? partitionRestriction?
    setClause selectFromClause? whereClause? hintClause?
    ;

forPortionOfApplicationTimeClause
    : FOR PORTION OF (
        APPLICATION_TIME FROM stringLiterals TO stringLiterals
        | stringLiterals AND stringLiterals
    )
    ;

setClause
    : SET setClauseList
    ;

setClauseList
    : setClauseItem (COMMA_ setClauseItem)*
    ;

setClauseItem
    : columnName EQ_ expr
    | select
    ;
// update End

delete
    : DELETE HISTORY? FROM tableName partitionRestriction? forPortionOfApplicationTimeClause?
    whereClause? hintClause?
    ;

// select Start
select
    : withClause? subquery selectOption? collationClause? hintClause?
    ;

withClause
    : WITH withListElement (COMMA_ withListElement)*
    | LP_ withClause RP_
    ;

withListElement
    : queryName=alias (columnNameList)? AS subquery
    ;

collationClause
    : WITH COLLATION name
    ;

hintClause
    : WITH HINT hintElementList
    ;

hintElementList
    : LP_ hintElement (COMMA_ hintElement)* RP_
    ;

hintElement
    : (
        (ROUTE_TO | NO_ROUTE_TO) LP_ INTEGER_ (COMMA_ INTEGER_)* RP_
        | (ROUTE_BY | ROUTE_BY_CARDINALITY) LP_ tableName (COMMA_ tableName)* RP_
        | (DATA_TRANSFER_COST | MAX_CONCURRENCY) LP_ INTEGER_ RP_
        | hintName hintArgumentList?
    ) CASCADE?
    ;

hintArgumentList
    : LP_ (hintArgument (COMMA_ hintArgument)*)? RP_
    ;

hintArgument
    : stringLiterals | numberLiterals | objectName | selectTableReference
    ;

selectOption
    : forUpdateClause
    | forShareLockClause
    | timeTravelClause
    | forSystemTimeClause
    | forJsonClause
    | forXmlClause
    | selectIntoClause
    ;

forUpdateClause
    : FOR UPDATE (OF columnNames)? (WAIT INTEGER_ | NOWAIT | IGNORE LOCKED)?
    ;

forShareLockClause
    : FOR SHARE LOCK (OF columnNames)? (WAIT INTEGER_ | NOWAIT | IGNORE LOCKED)?
    ;

timeTravelClause
    : AS OF timeTravelWhen
    ;

timeTravelWhen
    : COMMIT ID INTEGER_ | UTCTIMESTAMP stringLiterals
    ;

forJsonClause
    : FOR JSON stringKeyValueList? jsonReturnsClause?
    ;

jsonReturnsClause
    : RETURNS (((VARCHAR | NVARCHAR) LP_ INTEGER_ RP_) | CLOB | NCLOB)
    ;

forXmlClause
    : FOR XML stringKeyValueList? jsonReturnsClause?
    ;

selectIntoClause
    : INTO (variableNames | tableRef) columnNameList?
    ;

tableRef
    : tableWithLinkName (forSystemTimeClause | forApplicationTimePeriod)? partitionRestriction? asClause? tablesampleClause?
    ;

forSystemTimeClause
    : FOR SYSTEM_TIME AS OF stringLiterals
    | FOR SYSTEM_TIME FROM stringLiterals TO stringLiterals
    | FOR SYSTEM_TIME BETWEEN stringLiterals AND stringLiterals
    ;

forApplicationTimePeriod
    : FOR APPLICATION_TIME AS OF stringLiterals
    ;

partitionRestriction
    : PARTITION LP_? partitionRestrictionItem (COMMA_ partitionRestrictionItem)* RP_?
    ;

partitionRestrictionItem
    : INTEGER_ persistentMemoryClause?
    ;

persistentMemoryClause
    : ( DELETE | RETAIN ) PERSISTENT MEMORY
    ;

tablesampleClause
    : TABLESAMPLE ( BERNOULLI | SYSTEM )? LP_ INTEGER_ RP_
    ;

subquery
    : subquery setOperator subquery
    | (queryBlock | parenthesisSelectSubquery) orderByClause? limitClause?
    ;

parenthesisSelectSubquery
    : LP_ subquery RP_
    ;

queryBlock
    : SELECT topClause? (DISTINCT | UNIQUE | ALL)? selectList selectFromClause
    whereClause? groupByClause? havingClause?
    ;

topClause
    : TOP INTEGER_
    ;

selectList
    : selectItem (COMMA_ selectItem)*
    ;

selectItem
    : (
        tableWithLinkName DOT_ASTERISK_ | columnName | expr | associationExpression
    ) asClause?
    ;

selectFromClause
    : FROM fromClauseList
    ;

fromClauseList
    : selectTableReference (COMMA_ selectTableReference)*
    ;

selectTableReference
    : tableExpression selectJoinOption*
    ;

tableExpression
    : (tableRef
    | LP_ select RP_
//    | system_versioned_table_ref    //todo 找不到
    | collectionDerivedTable
    | functionCall
    | tableCollectionExpression
    | associatedTableExpression
    | bindVariable) asClause?
    ;

associatedTableExpression
    : tableName (LBT_ condition RBT_)? COLON_ associationExpression
    ;

tableCollectionExpression
    : (TABLE | THE) (LP_ subquery RP_ | LP_ expr RP_ (LP_ '+' RP_)?)
    ;

collectionDerivedTable
    : UNNEST LP_ collectionValueExpression (COMMA_ collectionValueExpression)*  RP_
    (WITH ORDINALITY)? asClause? columnNameList?
    ;

collectionValueExpression
    : ARRAY LP_ fromClauseList | columnName RP_
    ;

selectJoinOption
    : (normalJoinClause
    | crossJoinClause
    | caseJoinClause) asClause?
    ;

normalJoinClause
    : joinType? associationDef
    ;

associationDef
    : joinCardinality? JOIN selectTableReference ON (condition | (columnName asClause?)) (WITH DEFAULT FILTER predicate)?
    | columnName
    ;

joinType
    : INNER | ( LEFT | RIGHT | FULL ) OUTER?
    ;

joinCardinality
    : MANY TO MANY
    | MANY TO ONE
    | MANY TO EXACT ONE
    | ONE TO MANY
    | EXACT ONE TO MANY
    | ONE TO ONE
    | EXACT ONE TO ONE
    | ONE TO EXACT ONE
    | EXACT ONE TO EXACT ONE
    ;

crossJoinClause
    : CROSS JOIN selectTableReference
    ;

caseJoinClause
    : LEFT OUTER MANY TO ONE CASE JOIN
    (WHEN condition THEN RETURN columnNameList FROM selectTableReference ON condition)+
    (ELSE RETURN columnNameList FROM selectTableReference ON condition)?
    END
    ;

orderByClause
    : ORDER SIBLINGS? BY orderByElements (COMMA_ orderByElements)*
    ;

orderByElements
    : columnNameExpr orderType? (NULLS (FIRST | LAST))?
    ;

limitClause
    : LIMIT INTEGER_ (OFFSET INTEGER_)? (TOTAL ROWCOUNT)?
    ;

whereClause
    : WHERE expr
    ;

groupByClause
    : GROUP BY groupByItem (COMMA_ groupByItem)*
    ;

groupByItem
    : expr | groupingSet
    ;

groupingSet
    : (GROUPING SETS | ROLLUP | CUBE) ( BEST INTEGER_ )? limitClause?
      ( WITH (SUBTOTAL | BALANCE | TOTAL) )?
      ( TEXT_FILTER stringLiterals ( FILL UP ( SORT MATCHES TO TOP )? )? )?
      ( STRUCTURED RESULT ( WITH OVERVIEW )? ( PREFIX (name | STRING_) )? | MULTIPLE RESULTSETS )?
      groupingExpressionList
    ;

groupingExpressionList
    : LP_ groupingExpression (COMMA_ groupingExpression)* RP_
    ;

groupingExpression
    : exprs orderByClause?
    ;

havingClause
    : HAVING condition
    ;
// select End
/* --------------------- DML End -------------------- */

/*
 * DDL Document link
 * @link https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/209ce8cd75191014bcd59c2b379a17c9.html?locale=en-US
 */
/* --------------------- DDL Start -------------------- */
renameSchema
    : RENAME SCHEMA schemaName TO schemaName
    ;

renameTable
    : RENAME TABLE tableName TO tableName
    ;

renameIndex
    : RENAME INDEX indexName TO indexName
    ;

renameColumn
    : RENAME COLUMN columnName TO columnName
    ;


refreshStatistics
    : REFRESH statisticsDefinition
    ;

refreshView
    : REFRESH VIEW viewName ANONYMIZATION
    ;

dropIndex
    : DROP FULLTEXT? INDEX indexName onlinePref?
    ;

dropGraphWorkspace
    : DROP GRAPH WORKSPACE workspaceName
    ;

dropSchedulerJob
    : DROP SCHEDULER JOB jobName
    ;

dropSchema
    : DROP SCHEMA schemaName dropOption
    ;

dropSynonym
    : DROP (SCHEMA | PUBLIC)? SYNONYM synonymName dropOption
    ;

dropSequence
    : DROP SEQUENCE sequenceName dropOption
    ;

dropStatistics
    : DROP statisticsDefinition
    ;

dropTrigger
    : DROP TRIGGER triggerName dropOption ONLINE?
    ;

dropView
    : DROP VIEW viewName dropOption
    ;

dropTable
    : DROP TABLE tableName dropOption (WITH REMOTE)?
    ;

// createTable Start
createTable
    : CREATE tableType? TABLE tableName tableDefinition (tableOption+)?
    | CREATE VIRTUAL TABLE tableName tableDefinition? AT remoteLocationClause remotePropertyClause?
    ;

remoteLocationClause
    : DOUBLE_QUOTED_TEXT DOT_ DOUBLE_QUOTED_TEXT DOT_ DOUBLE_QUOTED_TEXT DOT_ DOUBLE_QUOTED_TEXT
    ;

remotePropertyClause
    : REMOTE PROPERTY stringKeyValueList
    | WITH REMOTE
    ;

tableType
    : ROW | HISTORY? COLUMN | VIRTUAL | (GLOBAL | LOCAL ) TEMPORARY (ROW | COLUMN)?
    ;

tableDefinition
    : LP_ tableElement (COMMA_ tableElement)* RP_
    | likeTableClause
    | asSubqueryClause
    ;

tableElement
    : columnDefinitions
    | sysVersioningClause
    | historyVersioningClause
    | constraintNameDefinition? tableConstraint
    ;

historyVersioningClause
    : historyVersioningList (COMMA_ periodAppTimeClause)?
    ;

historyVersioningList
    : columnName dataType NOT NULL (COMMA_ historyVersioningList)*
    ;

sysVersioningClause
    : sysValidfromOrTo START COMMA_ sysValidfromOrTo END (COMMA_ historyVersioningClause)?
    COMMA_ periodSysTimeClause (PRIMARY KEY columnNameList)? (WITH sysHistroyTableClause)?
    ;

likeTableClause
    : LIKE tableName (tableCreationSyntax | likeTableReplicaSpec)
    ;

likeTableReplicaSpec
    : syncMode? REPLICA columnNameList? partitionTableClause? atHostPort?
    ;

tableCreationSyntax
    : (WITH NO? DATA)? likeWithoutOption? (WITH INDEX)? definedIndexNameList?
    ;

definedIndexNameList
    : indexNameToken (COMMA_ indexNameToken)*
    ;

indexNameToken
    : sourceIndex=indexName AS targetIndex=indexName
    ;

likeWithoutOption
    : WITHOUT (
        AUTO MERGE | HISTORY | NO LOGGING | PARTITION | SCHEMA FLEXIBILITY | UNLOAD PRIORITY? | PRELOAD | (FUZZY SEARCH)? INDEX
       | FUZZY SEARCH MODE | (GLOBAL | LOCAL) TEMPORARY | CONSTRAINT | NUMA NODE
    )
    ;

asSubqueryClause
    : columnNameList? addIdentityColumnClause? AS (subquery (WITH NO? DATA)? | LP_ select RP_)
    ;

addIdentityColumnClause
    : ADD LP_ columnName INT GENERATED ALWAYS AS IDENTITY LP_ START WITH INTEGER_ INCREMENT BY INTEGER_ RP_ RP_
    ;

tableOption
    : timestampTableClause
    | withAssociationClause
    | withAnnotationClause
    | WITH (SESSION USER)? columnMaskClause
    | (LOGGING | NO LOGGING retentionClause)
    | NO? AUTO MERGE
    | UNLOAD PRIORITY INTEGER_
    | WITH SCHEMA FLEXIBILITY (LP_ flexibilityOptions+ RP_)?
    | partitionTableClause
    | persistentMemorySpecClause
    | groupOption+
    | NOT? MOVABLE atHostPort
    | replicaOption
    | WITH replicaOption+
    | ON COMMIT (PRESERVE | DELETE) ROWS
    | seriesClause
    | unusedRetentionPeriod
    | RECORD COMMIT TIMESTAMP
    | commentClause
    | numaNodePreferenceClause
    | loadUnit
    | pkUpdate
    ;

seriesClause
    :  SERIES LP_ seriesKey? seriesEquidistantDefinition? seriesMinvalue? seriesMaxvalue? seriesPeriod alternateSeries? RP_
    ;

alternateSeries
    : ALTERNATE PERIOD FOR SERIES columnNameList
    ;

seriesPeriod
    : PERIOD FOR SERIES LP_ (columnName | NULL) (COMMA_ (columnName | NULL))? RP_
    ;

seriesKey
    : SERIES KEY columnNameList
    ;

seriesEquidistantDefinition
    : NOT EQUIDISTANT
    | EQUIDISTANT INCREMENT BY literals (MISSING ELEMENTS NOT? ALLOWED)?
    ;

seriesMinvalue
    : NO MINVALUE
    | MINVALUE literals
    ;

seriesMaxvalue
    : NO MAXVALUE
    | MAXVALUE literals
    ;

timestampTableClause
    : WITH sysHistroyTableClause
    ;
// createTable End

createView
    : CREATE orReplace VIEW viewName commentClause? columnNameList? parameterList?
    AS select withAssociationClause? (WITH expressionClause)? withAnnotationClause? (WITH STRUCTURED PRIVILEGE CHECK)?
    withAnonymizationClause? viewRestriction? cacheClause?
    ;

createTrigger
    : CREATE orReplace TRIGGER triggerName triggerActionTime triggerEventList ON tableName (REFERENCING transitionList)?
    forEach? triggerOrderClause? ONLINE? body
    ;

triggerActionTime
    : BEFORE | AFTER | INSTEAD OF
    ;

triggerEventList
    : INSERT
    | DELETE
    | UPDATE (EXCEPT? OF columnNames)?
    | OR triggerEventList
    ;

transitionList
    : ((OLD | NEW) (ROW | TABLE) asClause) (COMMA_ transitionList)*
    ;

forEach
    : FOR EACH (ROW | STATEMENT)
    ;

triggerOrderClause
    : (FOLLOWS | PRECEDES) triggerName (COMMA_ triggerName)*
    ;

createStatistics
    : CREATE STATISTICS statisticsName? ON statisticsDataSources (TYPE statisticsType)? statisticsPropertiesList? initialRefresh?
    ;

createSequence
    : CREATE SEQUENCE sequenceName sequenceOption
    ;

createSynonym
    : CREATE orReplace (
        SCHEMA SYNONYM synonymName FOR schemaName
        | PUBLIC? SYNONYM synonymName FOR objectName
    )
    ;

createSchema
    : CREATE SCHEMA schemaName (OWNED BY username)
    ;

createSchedulerJob
    : CREATE SCHEDULER JOB jobName schedulerJobption
    ;

createProjectionView
    : CREATE orReplace PROJECTION VIEW viewName columnNameList? AS SELECT selectList
    FROM tableName withAssociationClause? withAnnotationClause? (WITH DDL ONLY)?
    ;

createGraphWorkspace
    : CREATE GRAPH WORKSPACE workspaceName
         EDGE TABLE tableName
            SOURCE COLUMN columnName
            TARGET COLUMN columnName
            KEY COLUMN columnName
         VERTEX TABLE tableName
            KEY COLUMN columnName
    ;

// createIndex Start
createIndex
    : CREATE indexType? INDEX indexName ON tableName createIndexDefineClause?
    ;

createIndexDefineClause
    : LP_ columnName RP_ (fulltextParameterList | fuzzySearchIndexDefineClause)
    | indexColumnList orderType? NOWAIT? ONLINE? loadUnit?
    ;

fuzzySearchIndexDefineClause
    : SEARCH MODE TEXT ((TOKEN SEPARATORS | MIME TYPE) stringLiterals?)*
    ;

fulltextParameterList
    : fulltextParameter (COMMA_? fulltextParameter)*
    ;

fulltextParameter
    : LANGUAGE COLUMN columnName
    | LANGUAGE DETECTION stringLiteralsList
    | MIME TYPE COLUMN columnName
    | SYNC HRONOUS?
    | ASYNC HRONOUS? (FLUSH QUEUE? flushQueueElem)?
    | CONFIGURATION stringLiterals
    | SEARCH ONLY onOff
    | FAST PREPROCESS onOff
    | TEXT ANALYSIS onOff
    | MIME TYPE stringLiterals
    | TOKEN SEPARATORS stringLiterals
    | alterFulltextParameter
    ;

flushQueueElem
    : EVERY numberLiterals MINUTES (OR AFTER numberLiterals DOCUMENTS)?
    | AFTER numberLiterals DOCUMENTS
    ;

csInvertedTypeIndex
    : INVERTED ( HASH | VALUE | INDIVIDUAL )
    ;

indexAction
    : (BTREE | CPBTREE) | csInvertedTypeIndex;

indexType
    : UNIQUE | UNIQUE? indexAction | FULLTEXT | FUZZY SEARCH
    ;

indexColumnList
    : LP_ (columnName orderType?) (COMMA_ (columnName orderType?))* RP_
    ;
// createIndex End

comment
    : COMMENT ON (TABLE | VIEW | COLUMN | USER | ROLE | USERGROUP | SCHEDULER JOB | CERTIFICATE | PUBLIC KEY) objectName
    IS (stringLiterals | NULL)
    ;

annotate
    : ANNOTATE annotation
    ;

// alterView Start
alterView
    : ALTER VIEW viewName alterViewClause
    ;

alterViewClause
    : columnNameList? (AS subquery)? viewOptionList alterCacheClause?
    | alterMaskSettingsClause
    | expressionMacros
    | withAnonymizationClause
    ;

withAnonymizationClause
    : WITH ANONYMIZATION LP_ algorithmClause (COLUMN columnName)? parametersClause? columnLevelParameters? RP_
    ;

algorithmClause
    : ALGORITHM STRING_
    ;

parametersClause
    : PARAMETERS expr
    ;

columnLevelParameters
    : columnSpec+
    ;

columnSpec
    : COLUMN columnName parametersClause
    ;

expressionClause
    : EXPRESSION MACROS LP_ (expr (AS expr)?) (COMMA_ (expr (AS expr)?))* RP_
    ;

expressionMacros
    : ADD expressionClause
    | DROP EXPRESSION MACROS exprList
    ;

cacheClause
    : cacheType? CACHE (NAME name)? retentionClause? (OF projectionList)? (FILTER condition)? atHostPort? FORCE?
    ;

alterCacheClause
    : ADD cacheClause
    | ALTER cacheType? CACHE (
        retentionClause
        | ( ADD | DROP ) atHostPort
        | ( ADD | DROP ) INDEX ON columnName
    )
    | DROP cacheType? CACHE
    ;

retentionClause
    : RETENTION INTEGER_
    ;

projectionList
    : ((SUM | MIN | MAX | COUNT | AVG) LP_ columnName RP_ | columnName) (COMMA_ projectionList)*
    ;

viewOptionList
    : viewRestriction? withAssociationClause? withAnnotationClause?
    ;

withAssociationClause
    : WITH ASSOCIATIONS LP_ associationDef (COMMA_ associationDef)* RP_
    ;

viewRestriction
    : WITH (NO? (CHECK OPTION | READ ONLY (DDL ONLY)? | DDL ONLY))? columnMaskClause? structuredPrivilegeCheck?
    ;

columnMaskClause
    : MASK LP_ columnMask (COMMA_ columnMask)* RP_
    ;

columnMask
    : columnName USING (structuredPrivilegeCheck | expr)
    ;

structuredPrivilegeCheck
    : STRUCTURED PRIVILEGE CHECK | NO STRUCTURED PRIVILEGE CHECK
    ;
// alterView End

//alterTable Start
alterTable
    : ALTER VIRTUAL? TABLE tableName alterDefinitionClause
    ;

alterDefinitionClause
    : alterTableColumnClause
    | systemVersioningConfiguration
    | applicationTimePeriod
    | lobReorganizeClause
    | clearColumnJoinDataStatisticsClause
    | constraintClause
    | preloadClause
    | tableConversionClause
    | associationClause
    | withAnnotationClause
    | partitionClause
    | tableLoadUnitClause
    | (ALTER PARTITION partitionIds)? persistentMemorySpecClause
    | replicaClause
    | statusClause
    | UNLOAD PRIORITY INTEGER_
    | schemaFlexibilityOption
    | setGroupOption
    | UNSET GROUP
    | rowOrderClause
    | unusedRetentionPeriod
    | reclaimDataSpaceClause
    | seriesReorganizeClause
    | ownerToClause
    | alterMaskSettingsClause
    | clientsideEncryptionClause
    | setMovableClause
    | convertIndexType
    | numaNodePreferenceClause
    | alterVirtualTableClause
    ;

alterVirtualTableClause
    : (ALTER columnName)? (
        SET PROPERTY stringKeyValueList | UNSET PROPERTY stringLiteralsList
    )
    | REFRESH DEFINITION
    ;

alterTableColumnClause
    : addColumnsClause | alterColumnsClause | dropColumnsClause
    ;

addColumnsClause
    : ADD columnSpecificationList
    ;

columnSpecificationList
    : LP_ columnSpecification (COMMA_ columnSpecification)* onlinePref? RP_
    ;

columnSpecification
    : columnDefinition commentClause? clientSideEncryption?
    | columnName ALTER loadUnit
    ;

columnDefinitions
    : columnDefinition (COMMA_ columnDefinition)*
    ;

columnDefinition
    : columnName dataType? (MEMORY threshold?)? defaultClause? clientSideEncryption? generatedClause? (AS expr)?
    (statType SCHEMA FLEXIBILITY)? fuzzySearchIndex? (FUZZY SEARCH MODE (stringLiterals | NULL))?
    persistentMemorySpecClause? commentClause? loadUnit? numaNodePreferenceClause? columnConstraint?
    ;

threshold
    : THRESHOLD (INTEGER_ | NULL)
    ;

persistentMemorySpecClause
    : PERSISTENT MEMORY (onOff | DEFAULT) immediate? CASCADE?
    ;

generatedClause
    : GENERATED ALWAYS AS expr
    | GENERATED (ALWAYS | BY DEFAULT) AS IDENTITY (LP_ sequenceOption RP_)?
    ;

defaultClause
    : DEFAULT ( NULL | (stringLiterals | numberLiterals) numberLiterals? | numberLiterals | datetimeValueFunction
    | stringValueFunction | arrayConstructor)
    ;

constraintNameDefinition
    : CONSTRAINT constraintName
    ;

columnConstraint
    : NOT? NULL
    | constraintNameDefinition? (uniqueSpecification | referencesSpecification)
    | NOT? HIDDEN_
    ;

uniqueSpecification
    : (PRIMARY KEY | UNIQUE) indexAction? (USING INDEX indexName)?
    ;

referencesSpecification
    : REFERENCES tableName columnNameList? referentialTriggeredAction? constraintStatus?
    (INITIALLY immediate)?
    ;

referentialTriggeredAction
    : updateRule deleteRule?
    | deleteRule updateRule?
    ;

updateRule
    : ON UPDATE referenceAction
    ;

deleteRule
    : ON DELETE referenceAction
    ;

constraintStatus
    : validationOption
    | enforcementOption validationOption?
    ;

validationOption
    : NOT? VALIDATED
    ;

enforcementOption
    : NOT? ENFORCED
    ;

commentClause
    : COMMENT stringLiterals
    ;

alterColumnsClause
    : ALTER columnSpecificationList
    ;

 dropColumnsClause
    : DROP columnNameList onlinePref?
    ;

fromToColumnName
    : LP_ columnName COMMA_ columnName RP_
    ;

systemVersioningConfiguration
    : (ALTER | ADD) LP_ sysValidfromOrTo (START | END) RP_
    | ADD periodSysTimeClause
    | DROP periodSysTime
    | ADD sysHistroyTableClause
    | DROP SYSTEM VERSIONING
    ;

sysHistroyTableClause
    : SYSTEM VERSIONING HISTORY TABLE tableName validationOption?
    ;

sysValidfromOrTo
    : columnName TIMESTAMP NOT NULL GENERATED ALWAYS AS ROW
    ;

periodSysTimeClause
    : periodSysTime fromToColumnName
    ;

periodSysTime
    : PERIOD FOR SYSTEM_TIME
    ;

applicationTimePeriod
    : ADD periodAppTimeClause
    | DROP periodAppTime
    ;

periodAppTime
    : PERIOD FOR APPLICATION_TIME
    ;

periodAppTimeClause
    : periodAppTime fromToColumnName
    ;

lobReorganizeClause
    : LOB REORGANIZE columnNameList? onlinePref?
    ;

clearColumnJoinDataStatisticsClause
    : CLEAR COLUMN JOIN DATA STATISTICS
    ;

constraintClause
    : addConstraintClause | dropConstraintClause | alterConstraintClause
    ;

addConstraintClause
    : ADD constraintNameDefinition? tableConstraint
    ;

tableConstraint
    : uniqueSpecification columnNameList?
    | FOREIGN KEY columnNameList? referencesSpecification
    | CHECK LP_ condition RP_
    ;

dropConstraintClause
    : DROP constraintNameDefinition
    | DROP PRIMARY KEY
    ;

alterConstraintClause
    : ALTER constraintNameDefinition constraintStatus
    | pkUpdate
    ;

pkUpdate
    : PRIMARY KEY UPDATE onOff
    ;

preloadClause
    : PRELOAD (ALL | NONE | columnNameList)
    ;

tableConversionClause
    : (ALTER TYPE)? (ROW | COLUMN) (THREADS numberLiterals (BATCH numberLiterals)?)?
    ;

associationClause
    : ADD ASSOCIATION LP_ associationDef RP_
    | DROP ASSOCIATION name
    ;

withAnnotationClause
    : WITH ANNOTATIONS LP_ annotation+ RP_
    ;

annotation
    : tableName? setUnsetAnnotation+
    | columnAnnotations
    | parameterAnnotations
    ;

setUnsetAnnotation
    : SET stringKeyValueList
    | UNSET (stringLiteralsList | ALL)
    ;

columnAnnotations
    : (COLUMN columnName setUnsetAnnotation+)+
    ;

parameterAnnotations
    : (PARAMETER ((functionName | procedureName) DOT_)? parameterName  setUnsetAnnotation+)+
    ;

// https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/f7ae27ca1b954471a4e1a7feab2c24d7.html?locale=en-US#loiof7ae27ca1b954471a4e1a7feab2c24d7__alter_partition_numa_node_preference_clause
// https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/a4258b865710423b8712b06fb5b8e7c5.html?locale=en-US#loioa4258b865710423b8712b06fb5b8e7c5__alter_dynamic_property_clause
partitionClause
    : partitionTableClause
    | addPartitionClause
    | movePartitionClause
    | dropPartitionClause
    | mergePartitionClause
    | alterDynamicRangeClause
    | alterDynamicPropertyClause
    | alterRangePartitionAttributesClause
    | alterPartitionNumaNodePreferenceClause
    | alterPartitionLoadUnitClause
    | alterPartitionGroupClauses
    ;

alterPartitionGroupClauses
    : ALTER REPLICA? PARTITION (partitionIds | subpartRange) (
        SET (GROUP (NAME | TYPE | SUBTYPE ) identifier)+ 
        | UNSET GROUP
    ) (CASCADE | atHostPort)?
    ;

alterPartitionLoadUnitClause
    : ALTER PARTITION (partitionIds | subpartRange ) loadUnit
    ;

alterPartitionNumaNodePreferenceClause
    : ALTER PARTITION partitionIds numaNodePreferenceClause
    ;

alterRangePartitionAttributesClause
    : FOR NON CURRENT PARTITIONS UNIQUE CONSTRAINTS onOff
    | WITH PARTITIONING ON ANY COLUMNS onOff
    | FOR DEFAULT STORAGE (ALL | NON CURRENT ) PARTITIONS loadUnit
    ;

alterDynamicRangeClause
    : ADD PARTITION FROM OTHERS
    | DROP EMPTY PARTITIONS
    | PARTITION othersValue
    ;

alterDynamicPropertyClause
    : ALTER PARTITION partitionIds dynamicRangeProperty
    | ALTER PARTITION partitionIds NO DYNAMIC DISTANCE
    | REFRESH DYNAMIC DISTANCE
    ;

dynamicRangeProperty
    : DYNAMIC ( threshold | INTERVAL numberLiterals (YEAR | MONTH | HOUR)) (DISTANCE numberLiterals PAGE LOADABLE)?
    ;

mergePartitionClause
    : MERGE PARTITIONS atHostPort?
    ;

dropPartitionClause
    : DROP PARTITION columnNameList? rangeValues
    | dropSubPartClause
    ;

dropSubPartClause
    : (ALTER PARTITION subpartRange)?
    (SUBPARTITION BY RANGE rangeValues)?
    DROP PARTITION subpartRange
    ;

movePartitionClause
    : MOVE (
        (PARTITION partitionIds TO hostPort)
        (COMMA_ PARTITION partitionIds TO hostPort)*
    )? PHYSICAL? ONLINE?
    | MOVE (PARTITION | SUBPARTITIONS OF)? subpartRange TO hostPort
    ;

addPartitionClause
    : ADD PARTITION? (columnNameList (USING DEFAULT STORAGE)? rangeValues | subpartRange)
    ;

partitionTableClause
    : PARTITION BY (hashPartitionClause | rangePartitionClause | roundrobinPartitionClasue)
    (alterRangePartitionAttributesClause+)?
    ;

hashPartitionClause
    : hashPartition (COMMA_ (rangePartition | hashPartition))?  ONLINE?
    ;

rangePartitionClause
    : rangePartition (COMMA_ rangePartition)? ONLINE?
    ;

roundrobinPartitionClasue
    : roundrobinPartition (COMMA_ rangePartition)?
    ;

hashPartition
    : HASH partitionExpressionList PARTITIONS partitionsValue
    ;

partitionsValue
    : partitionIds | (GET_NUM_SERVERS LP_ RP_)
    ;

partitionExpressionList
    : LP_ partitionExpression (COMMA_ partitionExpression)* RP_
    ;

partitionExpression
    : columnName (AS (INT | DATE))?
    | (YEAR | MONTH | HOUR) LP_ columnName RP_
    ;

rangePartition
    : RANGE partitionExpressionList (NO? PRIMARY KEY CHECK)? partRangeList
    ;

partRangeList
    : partRange (COMMA_ partRange)*
    | LP_ partRangeList RP_ subpartByClause*
    | LP_ (USING DEFAULT STORAGE)? partRangeList (COMMA_ partRangeList)* RP_
    ;

subpartByClause
    : SUBPARTITION BY (subpartRange | subpartHash)
    ;

subpartRange
    : RANGE exprList partRangeList
    ;

subpartHash
    : HASH exprList PARTITIONS INTEGER_ atHostPort?
    ;

atHostPort
    : AT ((LOCATION | LOCATIONS)? hostPort | ALL LOCATIONS)
    ;

partRange
    : PARTITION rangeValues (rangeProp+)?
    ;

rangeProp
    : persistentMemorySpecClause
    | numaNodePreferenceClause
    | loadUnit
    | groupOption+
    | INSERT onOff
    | atHostPort
    ;

numaNodePreferenceClause
    : NUMA NODE ( numaNodeIndexSpec | NULL ) immediate?
    ;

numaNodeIndexSpec
    : LP_ numaNodeSpec (COMMA_ numaNodeSpec)* RP_
    ;

numaNodeSpec
    : rangeValueLiterals (TO rangeValueLiterals)?
    ;

rangeValues
    : rangeValueLiterals LTE_ VALUES LT_ rangeValueLiterals
    | VALUE EQ_ stringLiterals IS CURRENT
    | (VALUES | VALUE) EQ_ rangeValueLiterals
    | othersValue
    | LP_ rangeValues RP_
    ;

othersValue
    : OTHERS ( othersDynamic | NO DYNAMIC )?
    ;

othersDynamic
    : DYNAMIC (threshold | intervalLiterals)? (DISTANCE intervalLiterals loadUnit)?
    ;

roundrobinPartition
    : ROUNDROBIN PARTITIONS partitionsValue
    ;

tableLoadUnitClause
    : loadUnit CASCADE?
    ;

replicaClause
    : ADD replicaOption
    | ALTER REPLICA SET (
      setGroupOption CASCADE?
      | UNSET GROUP CASCADE?
      | GROUP LEAD
    ) atHostPort?
    | DROP REPLICA atHostPort?
    | tableName? statType syncMode? REPLICA partitionPart?
    | MOVE REPLICA partitionPart? FROM LOCATION? hostPort TO LOCATION? hostPort
    | SET REPLICA SOURCE atHostPort
    ;

replicaOption
    : syncMode? (COLUMN | ROW)? REPLICA columnNameList? partitionTableClause? setGroupOption? atHostPort?
    ;

setGroupOption
    : SET groupOption+
    ;

groupOption
    : GROUP TYPE (name | stringLiterals)
    | GROUP SUBTYPE (name | stringLiterals)
    | GROUP NAME (name | stringLiterals)
    | GROUP LEAD
    ;

statusClause
    : statType (
        DELTA LOG | AUTOMERGE | SCHEMA FLEXIBILITY | TRIGGER triggerName
    )
    ;

schemaFlexibilityOption
    : ALTER SCHEMA FLEXIBILITY flexibilityOptions+
    ;

flexibilityOptions
    : RECLAIM
    | NO RECLAIM
    | DEFAULT DATA TYPE alias
    | NO DEFAULT DATA TYPE
    | AUTO DATA TYPE PROMOTION
    | NO AUTO DATA TYPE PROMOTION
    ;

rowOrderClause
    : SET ROW ORDER (BY VALUE)? columnNameList
    | UNSET ROW ORDER
    ;

unusedRetentionPeriod
    : UNUSED RETENTION PERIOD numberLiteralsList
    ;

reclaimDataSpaceClause
    : RECLAIM DATA SPACE
    ;

seriesReorganizeClause
    : SERIES REORGANIZE (PART INTEGER_)? limitClause?
    ;

ownerToClause
    : OWNER TO username
    ;

alterMaskSettingsClause
    : (ADD | ALTER) (DEFAULT | SESSION USER )? MASK LP_ (columnName USING expr) (COMMA_ columnName USING expr)* RP_
    | DROP MASK columnNameList
    ;

clientsideEncryptionClause
    : (CONTINUE | CANCEL) CLIENTSIDE ENCRYPTION
    | ALTER LP_ columnName ALTER clientSideEncryption RP_
    ;

clientSideEncryption
    : CLIENTSIDE ENCRYPTION ( (ON? WITH ((schemaName DOT_)? name) (RANDOM | DETERMINISTIC)) | OFF | ACTIVATE NEW VERSION)
    ;

setMovableClause
    : SET NOT? MOVABLE
    ;

convertIndexType
    : ALTER (constraintNameDefinition UNIQUE | PRIMARY KEY) csInvertedTypeIndex convertIndexType? onlinePref?
    ;
// alterTable End

//  alterSchedulerJob Start
alterSchedulerJob
    : ALTER SCHEDULER JOB jobName schedulerJobption
    ;

schedulerJobption
    : (CRON stringLiterals)? (FROM stringLiterals untilClause?)? statType? (PROCEDURE procedureName)?
    jobParameterClause?
    ;

untilClause
    : UNTIL stringLiterals
    ;

jobParameterClause
    : PARAMETERS parameterAssignment
    ;

parameterAssignment
    : (parameterName EQ_ literals) (COMMA_ parameterAssignment)*
    ;
// alterSchedulerJob End

// alterStatistics Start
alterStatistics
    : ALTER statisticsDefinition statisticsSetClause? addDropStatisticsProperties? initialRefresh?
    ;

statisticsDefinition
    : STATISTICS ( statisticsNames | ON statisticsDataSources statisticsHavingClause? )
    ;

statisticsDataSources
    : tableName (columnNameList (EXACT | CASCADE)?)?
    ;

statisticsHavingClause
    : HAVING? (TYPE statisticsType  | REFRESH TYPE (AUTO | MANUAL | ALL ))+
    ;

statisticsType
    : HISTOGRAM | SIMPLE | TOPK | SKETCH | SAMPLE (SAMPLE SIZE INTEGER_)? | RECORD COUNT | ALL
    ;

statisticsSetClause
    : SET statisticsPropertiesList
    ;

statisticsPropertiesList
    : statisticsProperties (COMMA_ statisticsProperties)*
    ;

statisticsProperties
    : REFRESH TYPE (AUTO | MANUAL | DEFAULT)
    | ENABLE onOff
    | BUCKETS INTEGER_
    | QERROR numberLiterals
    | QTHETA INTEGER_
    | MEMORY PERCENT? INTEGER_
    | ACCURACY numberLiterals
    | PREFIXBITS INTEGER_
    | PERSISTENT onOff
    | VALID FOR validUsage (COMMA_? validUsage)*
    | CONSTRAINT (KMINVAL
                 | PCSA
                 | LINEARCOUNTING
                 | LOGCOUNTING
                 | LOGLOGCOUNTING
                 | SUPERLOGLOGCOUNTING)
    ;

addDropStatisticsProperties
    : ( ADD | DROP ) VALID FOR validUsage (COMMA_? validUsage)*
    ;

initialRefresh
    : NO? INITIAL REFRESH
    ;
// alterStatistics End

//  alterSequence Start
alterSequence
    : ALTER SEQUENCE sequenceName (RESTART WITH numberLiterals)? sequenceOption
    ;

sequenceOption
    : sequenceParameterList? (RESET BY subquery)?
    ;

sequenceParameterList
    : sequenceParameter (COMMA_? sequenceParameter)*
    ;

sequenceParameter
    : START WITH numberLiterals
    | INCREMENT BY numberLiterals
    | MAXVALUE numberLiterals
    | NO MAXVALUE
    | MINVALUE numberLiterals
    | NO MINVALUE
    | CYCLE
    | NO CYCLE
    | CACHE INTEGER_
    | NO CACHE
    ;
// alterSequence End

// alterIndex Start
alterIndex
    : ALTER alterIndexClause
    ;

alterIndexClause
    : FULLTEXT INDEX indexName (alterFulltextParameter* | (FLUSH | SUSPEND | ACTIVATE) QUEUE)
    | INDEX indexName (REBUILD | UNIQUE csInvertedTypeIndex | loadUnit) onlinePref?
    ;

onlinePref
    : ONLINE PREFERRED?
    ;

loadUnit
    : ( COLUMN | PAGE | DEFAULT ) LOADABLE
    ;

alterFulltextParameter
    : fuzzySearchIndex
    | PHRASE INDEX RATIO numberLiterals
    | TEXT MINING onOff
    | TEXT MINING CONFIGURATION stringKeyValueList
    | TEXT MINING CONFIGURATION OVERLAY stringKeyValueList
    ;

fuzzySearchIndex
    : FUZZY SEARCH INDEX onOff
    ;
// alterIndex End

/*
 * truncateTable Document link
 * @link https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/20fe29f0751910149904f0c5c3201cfa.html?locale=en-US#example
 */
truncateTable
    : TRUNCATE TABLE tableName partitionRestriction?
    ;

setConstraints
    : SET (CONSTRAINT | CONSTRAINTS) (ALL | constraintName (COMMA_ constraintName)*) immediate
    ;

/*
 * Procedural Document link
 * @link https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/20a64c8c75191014af7ce80b4895b2eb.html?locale=en-US
 */
/* --------------------- Procedural Start -------------------- */
// createType Start
createType
    : CREATE TYPE typeName AS TABLE LP_ typeColumnDef RP_ (SQLSCRIPT SEARCH KEY columnNameList)?
    ;

typeColumnDef
    : columnName dataType (storeDataType=identifier)? (COMMA_ typeColumnDef)*
    ;
// createType Stop

dropType
    : DROP TYPE typeName dropOption
    ;

/* function Start */
// alterFunction Start
alterFunction
    : ALTER FUNCTION functionName procedureOptions
    ;
// alterFunction End

// createFunction Start
createFunction
    : CREATE (orReplace | VIRTUAL) FUNCTION functionName procedureOptions
    ;

returnsClause
    : RETURNS (parameterList | tableTypeDefinition)
    ;
// createFunction End

dropFunction
    : DROP FUNCTION functionName dropOption
    ;
/* function End */

/* procedure Start */
// alterProcedure Start
alterProcedure
    : ALTER PROCEDURE procedureName (procedureOptions | RECOMPILE | ENCRYPTION ON)
    ;
// alterProcedure End

// createProcedure Start
createProcedure
    : CREATE (orReplace | VIRTUAL) PROCEDURE procedureName procedureOptions
	;

procedureOptions
    : parameterList? returnsClause? languageOptions? (SQL SECURITY securityMode)? virtualOptions?
    (DEFAULT SCHEMA schemaName)? (READS SQL DATA (WITH RESULT VIEW viewName)?)? variableCacheClause? DETERMINISTIC?
    (WITH ENCRYPTION)? (AUTOCOMMIT DDL onOff)? (AS procedureBody)? (WITH cacheClause | alterCacheClause)?
    ;

languageOptions
    : LANGUAGE (SQLSCRIPT | R | GRAPH | SCALASPARK)
    ;

virtualOptions
    : (PACKAGE objectName)? (CONFIGURATION stringLiterals | languageOptions) AT sourceName
    ;

variableCacheClause
    : (ADD | ALTER | DROP)? VARIABLE CACHE ON (variableEntryWithModeList | ALL)
    ;

variableEntryWithModeList
    : variableEntryWithMode (COMMA_ variableEntryWithMode)*
    ;

variableEntryWithMode
    : (variableName | LP_ variableNames RP_) statType?
    ;

procedureBody
    : body | HEADER ONLY | BEGIN languageBody=swallowToSemi END
    ;
// createProcedure End

dropProcedure
    : DROP PROCEDURE procedureName dropOption
    ;
/* procedure End */
/* --------------------- Procedural End -------------------- */
/* --------------------- DDL End -------------------- */

/*
 * DCL Document link
 * @link https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/20a40b54751910148a82d91c57a21c1c.html?locale=en-US
 */
/* --------------------- DDL?? Start -------------------- */
// CREDENTIAL Start
alterCredential
    : ALTER credentialDefinition
    ;

credentialDefinition
    : CREDENTIAL FOR useUser? COMPONENT stringLiterals PURPOSE stringLiterals TYPE stringLiterals usingClause?
    ;

createCredential
    : CREATE credentialDefinition
    ;

dropCredential
    : DROP credentialDefinition
    ;
// CREDENTIAL End

// auditPolicy Start
alterAuditPolicy
    : ALTER AUDIT POLICY policyName (
         forDatabase statType
         | RESET TRAIL TYPE
         | SET auditTrailType
         | SET petention
         | RESET RETENTION
    )
    ;

auditTrailType
    : TRAIL TYPE auditTrailTypeList
    ;

auditTrailTypeList
    : auditTrailTypeName (COMMA_ auditTrailTypeName)*
    ;

auditTrailTypeName
    : TABLE petention? | SYSLOG | CSV
    ;

petention
    : RETENTION INTEGER_
    ;

createAuditPolicy
    : CREATE AUDIT POLICY policyName forDatabase AUDITING (SUCCESSFUL | UNSUCCESSFUL | ALL) auditActions auditExcept?
    LEVEL (EMERGENCY | ALERT | CRITICAL | WARNING | INFO) auditTrailType?
    ;

forDatabase
    : (FOR databaseName)?
    ;

auditActions
    : ACTIONS
    | auditTrailTypeList
    | targetAuditActionEntry
    ;

auditExcept
    : EXCEPT? FOR? principalList
    ;

targetAuditActionEntry
    : auditActionName (COMMA_ auditActionName)* (ON SCHEMA? objectNames)?
    ;

auditActionName
    : INSERT | UPDATE | DELETE | SELECT | EXECUTE | CANCEL SESSION | CONNECT | DISCONNECT SESSION | STOP SERVICE | SYSTEM CONFIGURATION CHANGE
    | VALIDATE USER | (GRANT | REVOKE) ANY |  (GRANT | REVOKE) APPLICATION PRIVILEGE | (GRANT | REVOKE) PRIVILEGE | (GRANT | REVOKE) ROLE
    | (GRANT | REVOKE) STRUCTURED PRIVILEGE | ALTER TABLE | (SET | UNSET ) SYSTEM LICENSE | (ACTIVATE | EXPORT | IMPORT) REPOSITORY CONTENT
    | CREATE TABLE | DROP TABLE| RENAME TABLE| CREATE PROCEDURE| DROP PROCEDURE| ALTER PROCEDURE| CREATE FUNCTION| DROP FUNCTION
    | ALTER FUNCTION| CREATE TRIGGER| DROP TRIGGER| CREATE VIEW| DROP VIEW| ALTER VIEW| CREATE STRUCTURED PRIVILEGE| DROP STRUCTURED PRIVILEGE
    | ALTER STRUCTURED PRIVILEGE| CREATE INDEX| DROP INDEX| ALTER INDEX| RENAME INDEX| CREATE REMOTE SUBSCRIPTION| ALTER REMOTE SUBSCRIPTION
    | DROP REMOTE SUBSCRIPTION| CREATE GRAPH WORKSPACE| DROP GRAPH WORKSPACE| CREATE REMOTE SOURCE| ALTER REMOTE SOURCE
    | DROP REMOTE SOURCE| CREATE CLIENTSIDE ENCRYPTION COLUMN KEY| ALTER CLIENTSIDE ENCRYPTION COLUMN KEY
    | ALTER CLIENTSIDE ENCRYPTION KEYPAIR| DROP CLIENTSIDE ENCRYPTION COLUMN KEY| CREATE CLIENTSIDE ENCRYPTION KEYPAIR
    | DROP CLIENTSIDE ENCRYPTION KEYPAIR| CREATE SEQUENCE| DROP SEQUENCE| ALTER SEQUENCE| CREATE SYNONYM| DROP SYNONYM
    | CREATE SCHEDULER JOB| ALTER SCHEDULER JOB| DROP SCHEDULER JOB| CREATE FULLTEXT INDEX| DROP FULLTEXT INDEX
    | ALTER FULLTEXT INDEX| CREATE GEOCODE INDEX| DROP GEOCODE INDEX| ALTER GEOCODE INDEX | ALTER PSE
    ;

principalList
    : usernames | PRINCIPALS principals?
    ;

principals
    : (useUser | USERGROUP userGroupName) (COMMA_ principals)*
    ;

useUser
    : USER username
    ;

dropAuditPolicy
    : DROP AUDIT POLICY policyName forDatabase
    ;
// auditPolicy End
/* --------------------- DDL?? End -------------------- */


/* --------------------- DCL Start -------------------- */
//  provider Start
alterProvider
    : ALTER (jwtProviderClause | ldapProviderClause | samlProviderClause)
    ;

jwtProviderClause
    : JWT PROVIDER providerName (
        SET (issuerClause | claimsClause+ | priorityclause)
        | caseClause
        | UNSET (CLAIM stringLiterals)+
        | enableDisableUserCreationClause
    )
    ;

issuerClause
    : WITH? ISSUER stringLiterals
    ;

claimsClause
    : CLAIM stringLiterals AS (EXTERNAL IDENTITY | APPLICATION USER)
    | (CLAIM stringKeyValueList) (COMMA_? CLAIM stringKeyValueList)*
    | (CLAIM stringLiterals HAS MEMBER stringLiterals) (COMMA_? stringLiterals HAS MEMBER stringLiterals)*
    ;

priorityclause
    : PRIORITY INTEGER_
    ;

caseClause
    : CASE (SENSITIVE | INSENSITIVE) IDENTITY?
    ;

enableDisableUserCreationClause
    : ENABLE USER CREATION (FOR LDAP)? ( USER TYPE (STANDARD | RESTRICTED) )? ( USERGROUP userGroupName )? (LDAP AUTHORIZATION)?
    | DISABLE USER CREATION (FOR LDAP)?
    ;

ldapProviderClause
    : LDAP PROVIDER providerName
    (CREDENTIAL TYPE stringLiterals USING stringLiterals)?
    (USER LOOKUP URL stringLiterals)?
    (NESTED GROUP LOOKUP URL (stringLiterals | NULL))?
    (ATTRIBUTE DN stringLiterals)?
    (ATTRIBUTE MEMBER_OF (stringLiterals | NULL))?
    (SSL onOff)?
    (DEFAULT onOff)?
    (statType PROVIDER)?
    enableDisableUserCreationClause?
    ;

samlProviderClause
    : SAML PROVIDER providerName
    ( SET subjectIssuerClause
    | UNSET? entityidClause
    | caseClause
    | enableDisableUserCreationClause
    )
    ;

subjectIssuerClause
    : SUBJECT stringLiterals ISSUER stringLiterals
    ;

entityidClause
    : ENTITY ID stringLiterals?
    ;

createProvider
    : CREATE (jwtProviderDef | ldapProviderClause | samlProviderDef)
    ;

jwtProviderDef
    : JWT PROVIDER providerName issuerClause claimsClause+ caseClause? priorityclause? enableDisableUserCreationClause?
    ;

samlProviderDef
    : SAML PROVIDER providerName (WITH subjectIssuerClause) entityidClause? caseClause? enableDisableUserCreationClause?
    ;

dropProvider
    : DROP (JWT | LDAP | SAML) PROVIDER providerName CASCADE?
    ;

validateProvider
    : VALIDATE LDAP PROVIDER providerName (
        CHECK USER username (passwordClasue | NO AUTHORIZATION CHECK)?
        | CHECK USER CREATION FOR LDAP USER username
    )?
    ;
//  provider End

// remote source start
alterRemoteSource
    : ALTER REMOTE SOURCE sourceName (
        adapterClause? ((WITH | DROP) credentialClause)?
        | refreshClause
        | dropClause
        | propertiesClause
    )
    ;

adapterClause
    : ADAPTER name (CONFIGURATION FILE stringLiterals)? CONFIGURATION stringLiterals
    ;

credentialClause
    : CREDENTIAL TYPE stringLiterals usingClause?
    ;

refreshClause
    : REFRESH LINKED (OBJECTS | TABLE tableName)
    ;

dropClause
    : DROP LINKED OBJECTS dropOption
    ;

propertiesClause
    : SET PROPERTY stringKeyValueList
    | UNSET PROPERTY (ALL | stringLiteralsList)
    ;

createRemoteSource
    : CREATE REMOTE SOURCE sourceName adapterClause (WITH credentialClause)?
    ;

dropRemoteSource
    : DROP REMOTE SOURCE sourceName dropOption
    ;
//  remote source End

// role Start
alterRole
    : ALTER ROLE roleName (ADD | DROP) roleLdapClasue
    ;

roleLdapClasue
    : LDAP GROUP stringLiteralsList
    ;

createRole
    : CREATE ROLE roleName roleLdapClasue? (NO GRANT TO CREATOR)?
    ;

dropRole
    : DROP ROLE roleName
    ;
// role End

// STRUCTURED PRIVILEGE Start
alterStructuredPrivilege
    : ALTER structuredPrivilegeDef
    ;

structuredPrivilegeDef
    : STRUCTURED PRIVILEGE objectName FOR SELECT ON objectNames (whereClause | CONDITION PROVIDER procedureName)
    ;

createStructuredPrivilege
    : CREATE structuredPrivilegeDef
    ;

dropStructuredPrivilege
    : DROP STRUCTURED PRIVILEGE objectName
    ;
// STRUCTURED PRIVILEGE End

// User Start
alterUser
    : ALTER USER username alteruserOptions
    ;

alteruserOptions
    : passwordValidationOption validitySpecification? userParameterOption?
    | usergroupMembershipOption
    | validitySpecification userParameterOption?
    | userParameterOption
    | externalIdent validitySpecification? userParameterOption?
    | resetConnectAttempts
    | dropConnectAttempts
    | passwordLifetime
    | forcePassChange
    | userActivationOpts
    | authentMechOpts
    | addIdentOpts
    | dropIdentOpts
    | remoteDatabaseIdentity
    | clientConnectOption
    | specialPrivilegeHandling
    | ldapGroupAuthorization
    ;

passwordClasue
    : PASSWORD passwordLiterals
    ;

passwordValidationOption
    : passwordClasue (NO FORCE_FIRST_PASSWORD_CHANGE)?
    ;

validitySpecification
    : VALID (FROM (stringLiterals | NOW))? (UNTIL (stringLiterals | FOREVER))?
    ;

userParameterOption
    : setUserParameters clearUserParameterOption?
    | clearUserParameterOption
    ;

setUserParameters
    : SET PARAMETER userParameterList
    ;

userParameterList
    : userParameter (COMMA_ userParameter)*
    ;

userParameterKeys
    : userParameterKey (COMMA_ userParameterKey)*
    ;

userParameterKey
    : CLIENT | LOCALE | TIME ZONE | EMAIL ADDRESS | RSERVE REMOTE SOURCES | STATEMENT (MEMORY | THREAD) LIMIT
    ;

userParameter
    : userParameterKey EQ_ stringLiterals
    ;

clearUserParameterOption
    : CLEAR (PARAMETER userParameterKeys | ALL PARAMETERS)
    ;

usergroupMembershipOption
    : SET USERGROUP userGroupName
    | UNSET USERGROUP
    ;

externalIdent
    : IDENTIFIED EXTERNALLY AS externalIdentity
    ;

externalIdentity
    : stringLiterals | identifier
    ;

resetConnectAttempts
    : RESET CONNECT ATTEMPTS
    ;

dropConnectAttempts
    : DROP CONNECT ATTEMPTS
    ;

passwordLifetime
    : statType PASSWORD LIFETIME
    ;

forcePassChange
    : FORCE PASSWORD CHANGE
    ;

userActivationOpts
    : (ACTIVATE | DEACTIVATE) (USER NOW)?
    ;

authentMechOpts
    : statType (PASSWORD | KERBEROS | SAML | X509 | SAP LOGON TICKET | SAP ASSERTION TICKET | JWT | LDAP)
    ;

addIdentOpts
    : ADD IDENTITY (providerInfo+ | externalIdentity FOR KERBEROS)
    ;

providerInfo
    : samlProviderIdent | x509ProviderIdent | kerberosProviderIdent | logonTicketIdent | assertionTicketIdent
    | jwtProviderIdent |  FOR LDAP PROVIDER
    ;

samlProviderIdent
    : mappedUserName? FOR SAML PROVIDER providerName
    ;

mappedUserName
    : ANY | stringLiterals
    ;

x509ProviderIdent
    : mappedUserName (ISSUER stringLiterals)? FOR X509 (PROVIDER providerName)?
    ;

kerberosProviderIdent
    : stringLiterals FOR KERBEROS
    ;

logonTicketIdent
    : FOR SAP LOGON TICKET
    ;

assertionTicketIdent
    : FOR SAP ASSERTION TICKET
    ;

jwtProviderIdent
    : mappedUserName FOR JWT PROVIDER providerName
    ;

dropIdentOpts
    : DROP IDENTITY (providerInfo+ | FOR KERBEROS)
    ;

remoteDatabaseIdentity
    : (ADD | DROP) remoteIdentityOption
    ;

remoteIdentityOption
    : REMOTE IDENTITY username AT DATABASE databaseName
    ;

clientConnectOption
    : statType CLIENT CONNECT
    ;

specialPrivilegeHandling
    : ( GRANT | REVOKE) (CREATE ANY ON OWN SCHEMA | ROLE PUBLIC)
    ;

ldapGroupAuthorization
    : AUTHORIZATION (LOCAL | LDAP)
    ;

createUser
    : CREATE RESTRICTED? USER username authenticationOptions? validitySpecification? setUserParameters? ldapGroupAuthorization? usergroupMembershipOption?
    ;

authenticationOptions
    : (WITH remoteIdentityOption | passwordValidationOption  | externalIdent  | WITH IDENTITY providerInfo)+
    ;

dropUser
    : DROP USER username dropOption
    ;

validateUser
    : VALIDATE USER (username passwordClasue | WITH ASSERTION (policyName | stringLiterals))
    ;
// User End

// UserGroup Start
alterUserGroup
    : ALTER USERGROUP userGroupName (statType USER ADMIN)? (SET PARAMETER stringKeyValueList)? (CLEAR PARAMETER stringLiteralsList)?
    (statType PARAMETER SET stringLiterals)? (statType CLIENT CONNECT)?
    ;

createUserGroup
    : CREATE USERGROUP userGroupName (DISABLE USER ADMIN)? (NO GRANT TO CREATOR)? (SET PARAMETER stringKeyValueList)?
    (ENABLE PARAMETER SET stringLiterals)? (DISABLE CLIENT CONNECT)?
    ;

dropUserGroup
    : DROP USERGROUP userGroupName
    ;
// UserGroup End

// grant Start
grant
    : GRANT (systemPrivilegeClasue | sourcePrivilegeClasue | schemaPrivilegeClasue | objectPrivilegeClasue | columnKeyPrivilegeClause
    | rolePrivilegeClause | structuredPrivilegeClause | userGroupPrivilegeClause) toClause
    ;

toClause
    : TO grantee (WITH ADMIN OPTION | WITH GRANT OPTION)?
    ;

grantee
    : username | roleName
    ;

systemPrivilegeClasue
    : systemPrivilege (COMMA_ systemPrivilege)*
    ;

systemPrivilege
    : ADAPTER ADMIN | AGENT ADMIN | ALTER CLIENTSIDE ENCRYPTION KEYPAIR | AUDIT ADMIN | AUDIT OPERATOR | AUDIT READ
    | BACKUP ADMIN | BACKUP OPERATOR | CATALOG READ | CERTIFICATE ADMIN | CLIENT PARAMETER ADMIN | CREATE CLIENTSIDE ENCRYPTION KEYPAIR
    | CREATE REMOTE SOURCE | CREATE SCENARIO | CREATE SCHEMA | CREATE STRUCTURED PRIVILEGE | CREDENTIAL ADMIN | DATA ADMIN
    | DATABASE ADMIN ASTERISK_ | DATABASE AUDIT ADMIN ASTERISK_ | DATABASE BACKUP ADMIN ASTERISK_ | DATABASE BACKUP OPERATOR ASTERISK_
    | DATABASE RECOVERY OPERATOR ASTERISK_ | DATABASE START ASTERISK_ | DATABASE STOP ASTERISK_ | DROP CLIENTSIDE ENCRYPTION KEYPAIR
    | ENCRYPTION ROOT KEY ADMIN | EXPORT | EXTENDED STORAGE ADMIN | IMPORT | INIFILE ADMIN | LDAP ADMIN | LICENSE ADMIN | LOG ADMIN
    | MONITOR ADMIN | OPTIMIZER ADMIN | PARTITION ADMIN | RESOURCE ADMIN | ROLE ADMIN | SAVEPOINT ADMIN | SCENARIO ADMIN | SERVICE ADMIN
    | SESSION ADMIN | SSL ADMIN | STRUCTUREDPRIVILEGE ADMIN | SYSTEM REPLICATION ADMIN | TABLE ADMIN | TRACE ADMIN | TRUST ADMIN
    | USER ADMIN | VERSION ADMIN | WORKLOAD ADMIN | WORKLOAD ANALYZE ADMIN | WORKLOAD CAPTURE ADMIN | WORKLOAD REPLAY ADMIN
    | name DOT_ name
    ;

sourcePrivilegeClasue
    : sourcePrivilege (COMMA_ sourcePrivilege)* ON REMOTE SOURCE name
    ;

sourcePrivilege
    : CREATE VIRTUAL TABLE | DROP | LINKED DATABASE | REMOTE EXECUTE
    ;

schemaPrivilegeClasue
    : schemaPrivilege (COMMA_ schemaPrivilege)* ON SCHEMA schemaName
    ;

schemaPrivilege
    : ALL PRIVILEGES | ALTER | CLIENTSIDE ENCRYPTION COLUMN KEY ADMIN | CREATE ANY | CREATE TEMPORARY TABLE | CREATE VIRTUAL PACKAGE
    | DEBUG | DEBUG MODIFY | DELETE | DROP | EXECUTE | INDEX | INSERT | SELECT | SELECT CDS METADATA | SELECT METADATA | TRIGGER
    | UNMASKED | UPDATE
    ;

objectPrivilegeClasue
    : objectPrivilege (COMMA_ objectPrivilege)* ON objectName
    ;

objectPrivilege
    : ALL PRIVILEGES | ALTER | AGENT MESSAGING | ATTACH DEBUGGER | CREATE ANY | CREATE OBJECT STRUCTURED PRIVILEGE | CREATE REMOTE SUBSCRIPTION
    | CREATE VIRTUAL FUNCTION | CREATE VIRTUAL PACKAGE | CREATE VIRTUAL PROCEDURE | CREATE VIRTUAL TABLE | DEBUG | DEBUG MODIFY
    | DELETE | DROP | EXECUTE | INDEX | INSERT | PROCESS REMOTE SUBSCRIPTION EXCEPTION | LINKED DATABASE | REFERENCES | SELECT
    | SELECT CDS METADATA | SELECT METADATA | SQLSCRIPT LOGGING | TRIGGER | REMOTE TABLE ADMIN | UNMASKED | UPDATE | USAGE
    | USERGROUP OPERATOR | objectName
    ;

columnKeyPrivilegeClause
    : USAGE ON CLIENTSIDE ENCRYPTION COLUMN KEY columnName
    ;

rolePrivilegeClause
    : roleName (COMMA_ roleName)*
    ;

structuredPrivilegeClause
    : STRUCTURED PRIVILEGE objectName
    ;

userGroupPrivilegeClause
    : USERGROUP OPERATOR ON USERGROUP userGroupName
    ;
// grant End

// revoke Start
revoke
    : REVOKE (systemPrivilegeClasue | sourcePrivilegeClasue | schemaPrivilegeClasue | objectPrivilegeClasue | columnKeyPrivilegeClause
    | rolePrivilegeClause | structuredPrivilegeClause) FROM grantee (GRANTED BY grantee)?
    ;
// revoke End
/* --------------------- DCL End -------------------- */

/* ------------------------------------ DAL Start ------------------------------------ */

/*
 * BackupAndRecovery Document link
 * @link https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/20a40b54751910148a82d91c57a21c1c.html?locale=en-US
 */
/* --------------------- BackupAndRecovery Start -------------------- */
// +14 Statements
backup
    : BACKUP (
        CANCEL forDatabase INTEGER_
        | CATALOG DELETE forDatabase (ALL BEFORE backupId (WITH FILE | WITH BACKINT | COMPLETE) | backupId COMPLETE?)
        | RETAINED (SET | UNSET) RETAINED forDatabase backupId
        | CHECK forDatabase usingFile? SIZE INTEGER_
        | CHECK ACCESS forDatabase backupId usingSource? usingPath?
        | DATA FOR FULL SYSTEM CLOSE SNAPSHOT backupId (SUCCESSFUL stringLiterals | UNSUCCESSFUL stringLiterals? )
        | DATA FOR FULL SYSTEM CREATE SNAPSHOT commentClause? ( TIMEOUT INTEGER_ )?
        | DATA FOR FULL SYSTEM DROP SNAPSHOT
        | ENCRYPTION ROOT KEYS rootKeyTypeList? forDatabase usingFile
        | COMPLETE? LIST DATA forDatabase untilDefinition? usingSource? usingPath? limitClause? backupEncryptionOption?
    );

backupId
    : BACKUP_ID INTEGER_
    ;

usingFile
    : USING FILE stringLiteralsList
    ;

usingSource
    : USING SOURCE stringLiterals
    ;

usingCatalog
    : USING CATALOG (PATH stringLiteralsList usingPath? | BACKINT)
    ;

usingPath
    : USING (LOG | DATA) PATH stringLiteralsList
    | usingCatalog
    ;

untilDefinition
    : UNTIL LOG POSITION expr AT VOLUME INTEGER_
    | UNTIL TIMESTAMP stringLiterals
    ;

rootKeyTypeList
    : rootKeyType (COMMA_ rootKeyType)*
    ;

backupEncryptionOption
    : ENCRYPTION ROOT KEYS BACKUP passwordClasue
    | ENCRYPTION ROOT KEYS BACKUP PASSWORD LP_ passwordLiterals (COMMA_? passwordLiterals)* RP_
    ;

recover
    : RECOVER (
        DATA forDatabase usingSource? recoverUsing* additionalOptions? ignoreOptions? backupEncryptionOption?
        | DATABASE forDatabase (untilDefinition additionalOptions?)? recoverUsing*
            (CHECK ACCESS (ALL | USING FILE |USING BACKINT))? ignoreOptions? backupEncryptionOption?
        | ENCRYPTION ROOT KEYS (AND SETTINGS | LP_ rootKeyTypeList RP_)? forDatabase recoverUsing* passwordClasue
            (SECURE STORE SECOND ACCESS PRIVATE KEY stringLiterals passwordClasue)?
    )
    ;

recoverUsing
    : usingSource | usingFile | USING BACKINT stringLiteralsList | USING SNAPSHOT | USING backupId usingPath? | USING RESUME
    | USING GUID_ID stringLiterals | usingPath
    ;

 additionalOptions
    : (SET LICENSE stringLiterals | TOOLOPTION stringLiterals  | INCLUDE CONFIGURATION | CLEAR LOG)+
    ;

 ignoreOptions
    : (IGNORE WORKERGROUPS | IGNORE COMPATIBILITY CHECK)+
    ;
/* --------------------- BackupAndRecovery End -------------------- */

/*
 * ImportAndExport Document link
 * @link https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/20a5342e75191014bb95e83cbbe772fa.html?locale=en-US
 */
/* --------------------- ImportAndExport Start -------------------- */
// +5 Statements
export
    : EXPORT (
        INTO stringLiterals FROM tableWithLinkName exportWith?
        | exportObjectNameList (whereClause | havingClause)? (AS (CSV | BINARY) ( DATA | RAW)?)? INTO stringLiterals exportWith? queryExportSpecification?
    )
    ;

exportWith
    : WITH? exportIntoOption+
    ;

exportIntoOption
    : THREADS numberLiterals
    | (SYSTEM | ALL) STATISTICS (COLUMNS columnNames)? (TYPE statisticsType)? ONLY?
    | BATCH numberLiterals
    | CATALOG ONLY
    | COLUMN LIST columnNameList (WITH SCHEMA FLEXIBILITY)?
    | COLUMN LIST IN FIRST ROW (WITH SCHEMA FLEXIBILITY)?
    | CREDENTIAL stringLiterals
    | DATE FORMAT stringLiterals
    | DEPENDENCIES (exprs | exprList)
    | ENCRYPTION passwordClasue
    | ERROR LOG stringLiterals
    | ESCAPE stringLiterals
    | FAIL ON INVALID DATA
    | FIELD DELIMITED BY stringLiterals
    | NO DEPENDENCIES
    | NO REMOTE DATA
    | NO STATISTICS
    | NO TYPE CHECK
    | OPTIONALLY ENCLOSED BY stringLiterals
    | PERSISTENT MEMORY
    | RECORD DELIMITED BY stringLiterals
    | REPLACE
    | SCRAMBLE (BY passwordLiterals)?
    | SKIP_ FIRST numberLiterals ROW
    | STRIP
    | TABLE LOCK
    | THREADS numberLiterals
    | TIME FORMAT stringLiterals
    | TIMESTAMP FORMAT stringLiterals
    | TRACE stringLiterals
    ;

exportObjectNameList
    : (objectName | ASTERISK_) (COMMA_ (objectName | ASTERISK_))*
    | CLIENTSIDE ENCRYPTION COLUMN KEY columnNames
    | ALL | ASTERISK_
    ;

queryExportSpecification
    : ( ON sqlscriptLocationList )? FOR functionCall
    ;

sqlscriptLocationList
    : (expr | statement)+
    ;

importStatement
    : IMPORT (
       (
           DATA INTO (STATISTICS| TABLE) objectName
           | exportObjectNameList havingClause? (AS formatOption)?
       )? FROM fileType? stringLiterals (INTO STATISTICS? objectName)? exportWith? atHostPort? (WITH? IGNORE NUMA NODE)?
       | SCAN stringLiterals (WITH CREDENTIAL stringLiterals)?
    )
    ;

fileType
    : (CONTROL | CSV) FILE 
    ;

formatOption
    : (CSV | BINARY) dataType?
    | SHAPEFILE 
    | LOAD_HISTORY
    | SAP_TIMEZONE_DATASET
    ;
/* --------------------- ImportAndExport End -------------------- */

/*
 * Client-side Document link
 * @link https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/6ac427db9fe44acf97428e85c4dc64ea.html?locale=en-US
 */
/* --------------------- Client-side Start -------------------- */
// +6 Statements
alterClientSideEncryption
    : ALTER CLIENTSIDE ENCRYPTION (columnKeyClause | keypairClause)
    ;

columnKeyDef
    : COLUMN KEY columnName
    ;

columnKeyClause
    : columnKeyDef (((ADD | DROP) KEYCOPY)? encryptionKeypairDef
    | ADD NEW VERSION
    | DROP OLD VERSIONS)
    ;

encryptionKeypairDef
    : ENCRYPTED WITH KEYPAIR name
    ;

alterClientSideEncryptionVersions
    : ADD NEW VERSION | DROP OLD VERSIONS
    ;

keypairDef
    : KEYPAIR name
    ;

keypairClause
    : keypairDef alterClientSideEncryptionVersions
    ;

createClientSideEncryption
    : CREATE CLIENTSIDE ENCRYPTION (
        columnKeyDef algorithmClause? (encryptionKeypairDef | HEADER ONLY)
        | keypairDef algorithmClause?
    )
    ;

dropClientSideEncryption
    : DROP CLIENTSIDE ENCRYPTION (columnKeyDef | keypairDef)
    ;
/* --------------------- Client-side End -------------------- */

/*
 * Session Management Statements link
 * @link https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/20a27b0a75191014a5bd9e2a08ff9354.html?locale=en-US
 */
/* --------------------- Session Management Start -------------------- */
// +5 Statements
connect
    : CONNECT (username passwordClasue | WITH SAML ASSERTION stringLiterals | WITH JWT stringLiterals)
    ;

set
    : SET SESSION? stringKeyValueList
    | SET HISTORY SESSION TO (NOW | timeTravelWhen)
    ;

setSchema
    : SET SCHEMA schemaName
    ;

unset
    : UNSET SESSION? stringLiterals
    ;
/* --------------------- Session Management End -------------------- */

/*
 * System Management Statements link
 * @link https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/20a1c3a975191014be0cf8b1b853ddf8.html?locale=en-US
 */
/* --------------------- System Management Start -------------------- */
// +72 Statements
alterSystem
    : ALTER SYSTEM (
        START DATABASE databaseName (FROM FALLBACK SNAPSHOT)
        | STOP DATABASE databaseName ( IMMEDIATE ( WITH COREFILE )? )?
        | (ADD | ALTER | REMOVE) STATEMENT HINT (LP_ identifiers RP_)? (NO? OVERRIDE)? commentClause? ALL? statementHintTarget
        | keyManagementSpecList
        | ADD ABSTRACT SQL PLAN FILTER stringLiterals SET stringKeyValueList
        | REMOVE ABSTRACT SQL PLAN FILTER (stringLiterals | ALL)
        | ALTER CONFIGURATION stringLiteralsList (SET | UNSET) (stringLiteralsList EQ_ stringLiterals) (COMMA_ (stringLiteralsList EQ_ stringLiterals))*  (WITH RECONFIGURE)? commentClause?
        | ALTER DATAVOLUME (ADD | DROP) PARTITION (PATH stringLiterals | numberLiterals)? (SYSTEM REPLICATION SITE (stringLiterals | numberLiterals))?
        | ALTER SESSION numberLiterals SET stringKeyValueList
        | ALTER SESSION numberLiterals UNSET stringLiterals
        | ALTER TABLE PLACEMENT tableSettingsList? (SET | UNSET) tableSettingsList?
        | ALTER TABLE PLACEMENT LOCATION name (SET tableSettingsList | UNSET)
        | APPLICATION ENCRYPTION encryptOption
        | BACKUP ENCRYPTION encryptOption
        | CANCEL (WORK IN)? SESSION stringLiterals
        | CLEAR AUDIT LOG (FOR AUDIT POLICY policyName)? (untilClause | ALL)
        | CLEAR CACHE LP_ identifiers SYNC? RP_
        | CLEAR COLUMN JOIN DATA STATISTICS
        | CLEAR INIFILE CONTENT HISTORY
        | CLEAR cacheType RESULT CACHE
        | CLEAR SQL PLAN CACHE
        | CLEAR TIMEZONE CACHE DATASET stringLiterals
        | CLEAR TRACES stringLiteralsList untilClause? (WITH BACKUP)
        | CLIENTPKI (UPDATE | DROP) (CERTIFICATES | ROOT CA)
        | swallowToSemi  // 通配
    );

encryptOption
    : CREATE NEW ROOT? KEY (WITH ALGORITHM stringLiterals)? (WITHOUT ACTIVATE)?
    | ACTIVATE NEW ROOT KEY
    | onOff
    ;

tableSettingsList
    : LP_ ((identifier (ARROW_ literals)?) (COMMA_ (identifier (ARROW_ literals)?))*) RP_
    ;

statementHintTarget
    : (ON (PROCEDURE | FUNCTION) routineName)? FOR select
    | FOR STATEMENT HASH stringLiterals
    ;

// alterPse Start
alterPse
    : ALTER PSE name ( setOwnCertificateClause
    | unsetOwnCertificateClause
    | (ADD | DROP) (purposeObjectClause | publicKeyClause | certificateClause)
    );

certificateClause
    : CERTIFICATE (stringLiteralsList | numberLiteralsList | identifiers)
    ;

setOwnCertificateClause
    : SET OWN CERTIFICATE stringLiterals
    ;

unsetOwnCertificateClause
    : UNSET OWN CERTIFICATE
    ;

purposeObjectClause
    : PROVIDER identifiers | HOST stringLiteralsList | REMOTE SOURCE identifiers
    ;

publicKeyClause
    : PUBLIC KEY numberLiteralsList
    ;
// alterPse End

createPse
    : CREATE PSE name
    ;

dropPse
    : DROP PSE name CASCADE ?
    ;

setPse
    : SET PSE name purposeClause (FOR purposeObjectClause)?
    ;

purposeClause
    : PURPOSE (JWT | SAML | SAP LOGON | SSL | X509 | LDAP | SOLACE | REMOTE SOURCE | DATABASE REPLICATION)
    ;

unsetPse
    : UNSET PSE name purposeClause
    ;

createCertificate
    : CREATE CERTIFICATE name? FROM stringLiterals commentClause?
    ; 

dropCertificate
    : DROP CERTIFICATE name
    ;

createPublicKey
    : CREATE PUBLIC KEY name? FROM stringLiterals (KEY ID HINT stringLiterals)? commentClause?
    ;

dropPublicKey
    : DROP PUBLIC KEY name
    ;

setSystemLicense
    : SET SYSTEM LICENSE stringLiterals
    ;

unsetSystemLicense
    : UNSET SYSTEM LICENSE ALL
    ;
/* --------------------- System Management End -------------------- */

/*
 * Tenant Database Management Statements link
 * @link https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/78e5dc55514941a4a9641b2a50179c7c.html?locale=en-US
 */
/* --------------------- Tenant Database Management Start -------------------- */
// +6 Statements
alterDatabase
    : ALTER DATABASE databaseName 
    ( alterServiceSpecList
    | ALTER stringLiterals atHostPort TO LOCATION? stringLiterals
    | osUserClause
    | FINALIZE REPLICA ( DROP SOURCE DATABASE )?
    | CANCEL REPLICA
    | systemUser
    | restartClause
    | ENCRYPTION CONFIGURATION CONTROLLED BY LOCAL DATABASE
    | (PERSISTENCE | LOG | BACKUP)? ENCRYPTION onOff
    | (CREATE | DROP) FALLBACK SNAPSHOT
    | keyManagementSpecList
    | UNSET LICENSE ALL
    | VALIDATE ENCRYPTION ROOT KEYS BACKUP passwordClasue
    | SET ENCRYPTION ROOT KEYS BACKUP passwordClasue
    );

systemUser
    : SYSTEM USER passwordClasue
    ;

restartClause
    : (NO | DEFAULT) RESTART
    ;

osUserClause
    : OS USER stringLiterals OS GROUP stringLiterals
    ;

alterServiceSpecList
    : ((ADD | REMOVE) stringLiterals atHostPort?)+
    ;

managementSpec
    : KEY MANAGEMENT CONFIGURATION stringLiterals (PROPERTIES stringLiterals)?
    ;

keyManagementSpecList
    : (ADD | ALTER | ACTIVATE | DROP) managementSpec
    ;

createDatabase
    : CREATE DATABASE databaseName databaseOptions* (
        AS REPLICA OF databaseName? atHostPort
        | managementSpec
        | systemUser
    ) osUserClause? (NO START)? restartClause?
    ;

databaseOptions
    : atHostPort (ADD stringLiterals)?
    ;

dropDatabase
    : DROP DATABASE databaseName (DROP BACKUPS)?
    ;

renameDatabase
    : RENAME DATABASE databaseName TO databaseName
    ;
/* --------------------- Tenant Database Management End -------------------- */

/*
 * Workload Management Statements link
 * @link https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/c5f48e60b55645fe91b241c4d0b89278.html?locale=en-US
 */
/* --------------------- Workload Management Start -------------------- */
// +6 Statements
alterWorkload
    : ALTER WORKLOAD (classClause | mappingClause)
    ;

classClause
    : CLASS ( name ( inheritance | workloadProperty* | statType ) | ALL statType )
    ;

workloadProperty
    : (SET? stringKeyValueList | UNSET stringLiteralsList) (WITH WILDCARD stringLiterals?)?
    ;

inheritance
    : PARENT (name | NULL)
    ;

mappingClause
    : MAPPING name WORKLOAD CLASS name workloadProperty*
    ;

createWorkload
    : CREATE WORKLOAD (
        CLASS name inheritance? workloadProperty* statType?
        | mappingClause
    );

dropWorkload
    : DROP WORKLOAD (CLASS | MAPPING) name
    ;
/* --------------------- Workload Management End -------------------- */

/* ------------------------------------ DAL End ------------------------------------ */
/* Statements End */