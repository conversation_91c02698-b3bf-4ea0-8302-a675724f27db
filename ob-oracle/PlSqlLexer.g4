lexer grammar PlSqlLexer;

options {
    caseInsensitive = true;
}

ABORT                         : 'ABORT';
ABS                           : 'ABS';
ACCESS                        : 'ACCESS';
ACCESSED                      : 'ACCESSED';
ACCESSIBLE                    : 'ACCESSIBLE';
ACCOUNT                       : 'ACCOUNT';
ACL                           : 'ACL';
ACOS                          : 'ACOS';
ACROSS                        : 'ACROSS';
ACTION                        : 'ACTION';
ACTIONS                       : 'ACTIONS';
ACTIVATE                      : 'ACTIVATE';
ACTIVE                        : 'ACTIVE';
ACTIVE_COMPONENT              : 'ACTIVE_COMPONENT';
ACTIVE_FUNCTION               : 'ACTIVE_FUNCTION';
ACTIVE_TAG                    : 'ACTIVE_TAG';
ADD                           : 'ADD';
ADD_COLUMN                    : 'ADD_COLUMN';
ADD_GROUP                     : 'ADD_GROUP';
ADD_MONTHS                    : 'ADD_MONTHS';
ADJ_DATE                      : 'ADJ_DATE';
ADMIN                         : 'ADMIN';
ADMINISTER                    : 'ADMINISTER';
ADMINISTRATOR                 : 'ADMINISTRATOR';
ADVANCED                      : 'ADVANCED';
ADVISE                        : 'ADVISE';
ADVISOR                       : 'ADVISOR';
AFFINITY                      : 'AFFINITY';
AFTER                         : 'AFTER';
AGENT                         : 'AGENT';
AGGREGATE                     : 'AGGREGATE';
AL16UTF16                     : 'AL16UTF16';
AL32UTF8                      : 'AL32UTF8';
ALIAS                         : 'ALIAS';
ALL                           : 'ALL';
ALL_ROWS                      : 'ALL_ROWS';
ALLOCATE                      : 'ALLOCATE';
ALLOW                         : 'ALLOW';
ALTER                         : 'ALTER';
ALWAYS                        : 'ALWAYS';
ANALYTIC                      : 'ANALYTIC';
ANALYZE                       : 'ANALYZE';
ANCESTOR                      : 'ANCESTOR';
ANCILLARY                     : 'ANCILLARY';
AND                           : 'AND';
AND_EQUAL                     : 'AND_EQUAL';
ANTIJOIN                      : 'ANTIJOIN';
ANY                           : 'ANY';
ANYDATA                       : 'ANYDATA';
ANYDATASET                    : 'ANYDATASET';
ANYSCHEMA                     : 'ANYSCHEMA';
ANYTYPE                       : 'ANYTYPE';
APP                           : 'APP';
APPEND                        : 'APPEND';
APPEND_VALUES                 : 'APPEND_VALUES';
APPENDCHILDXML                : 'APPENDCHILDXML';
APPLICATION                   : 'APPLICATION';
APPLY                         : 'APPLY';
APPROX_RANK                   : 'APPROX_RANK';
ARCHIVAL                      : 'ARCHIVAL';
ARCHIVE                       : 'ARCHIVE';
ARCHIVED                      : 'ARCHIVED';
ARCHIVELOG                    : 'ARCHIVELOG';
ARRAY                         : 'ARRAY';
AS                            : 'AS';
ASC                           : 'ASC';
ASCII                         : 'ASCII';
ASCIISTR                      : 'ASCIISTR';
ASIN                          : 'ASIN';
ASSEMBLY                      : 'ASSEMBLY';
ASSIGN                        : 'ASSIGN';
ASSOCIATE                     : 'ASSOCIATE';
ASYNC                         : 'ASYNC';
ASYNCHRONOUS                  : 'ASYNCHRONOUS';
AT                            : 'AT';
ATAN                          : 'ATAN';
ATAN2                         : 'ATAN2';
ATTRIBUTE                     : 'ATTRIBUTE';
ATTRIBUTES                    : 'ATTRIBUTES';
AUDIT                         : 'AUDIT';
AUTHENTICATED                 : 'AUTHENTICATED';
AUTHENTICATION                : 'AUTHENTICATION';
AUTHID                        : 'AUTHID';
AUTHORIZATION                 : 'AUTHORIZATION';
AUTO                          : 'AUTO';
AUTOALLOCATE                  : 'AUTOALLOCATE';
AUTOBACKUP                    : 'AUTOBACKUP';
AUTOEXTEND                    : 'AUTOEXTEND';
AUTOMATIC                     : 'AUTOMATIC';
AUTONOMOUS_TRANSACTION        : 'AUTONOMOUS_TRANSACTION';
AVAILABILITY                  : 'AVAILABILITY';
AVERAGE_RANK                  : 'AVERAGE_RANK';
AVG                           : 'AVG';
AZURE_ROLE                    : 'AZURE_ROLE';
AZURE_USER                    : 'AZURE_USER';
BACKUP                        : 'BACKUP';
BACKUPS                       : 'BACKUPS';
BACKUPSET                     : 'BACKUPSET';
BADFILE                       : 'BADFILE';
BALANCE                       : 'BALANCE';
BASIC                         : 'BASIC';
BASICFILE                     : 'BASICFILE';
BATCH                         : 'BATCH';
BECOME                        : 'BECOME';
BEFORE                        : 'BEFORE';
BEGIN                         : 'BEGIN';
BEGIN_OUTLINE_DATA            : 'BEGIN_OUTLINE_DATA';
BEGINNING                     : 'BEGINNING';
BEHALF                        : 'BEHALF';
BEQUEATH                      : 'BEQUEATH';
BETWEEN                       : 'BETWEEN';
BFILE                         : 'BFILE';
BFILENAME                     : 'BFILENAME';
BIGFILE                       : 'BIGFILE';
BIN_TO_NUM                    : 'BIN_TO_NUM';
BINARY                        : 'BINARY';
BINARY_DOUBLE                 : 'BINARY_DOUBLE';
BINARY_DOUBLE_INFINITY        : 'BINARY_DOUBLE_INFINITY';
BINARY_DOUBLE_NAN             : 'BINARY_DOUBLE_NAN';
BINARY_FLOAT                  : 'BINARY_FLOAT';
BINARY_FLOAT_INFINITY         : 'BINARY_FLOAT_INFINITY';
BINARY_FLOAT_NAN              : 'BINARY_FLOAT_NAN';
BINARY_INTEGER                : 'BINARY_INTEGER';
BIND_AWARE                    : 'BIND_AWARE';
BINDING                       : 'BINDING';
BITAND                        : 'BITAND';
BITMAP                        : 'BITMAP';
BITMAP_TREE                   : 'BITMAP_TREE';
BITMAPFILE                    : 'BITMAPFILE';
BITMAPS                       : 'BITMAPS';
BITS                          : 'BITS';
BLOB                          : 'BLOB';
BLOCK                         : 'BLOCK';
BLOCK_RANGE                   : 'BLOCK_RANGE';
BLOCKCHAIN                    : 'BLOCKCHAIN';
BLOCKS                        : 'BLOCKS';
BLOCKSIZE                     : 'BLOCKSIZE';
BODY                          : 'BODY';
BOOLEAN                       : 'BOOLEAN';
BOTH                          : 'BOTH';
BOUND                         : 'BOUND';
BRANCH                        : 'BRANCH';
BREADTH                       : 'BREADTH';
BROADCAST                     : 'BROADCAST';
BTI                           : 'BTI';
BTITLE                        : 'BTITLE';
BUFFER                        : 'BUFFER';
BUFFER_CACHE                  : 'BUFFER_CACHE';
BUFFER_POOL                   : 'BUFFER_POOL';
BUILD                         : 'BUILD';
BULK                          : 'BULK';
BULK_ROWCOUNT                 : 'BULK_ROWCOUNT';
BY                            : 'BY';
BYPASS_RECURSIVE_CHECK        : 'BYPASS_RECURSIVE_CHECK';
BYPASS_UJVC                   : 'BYPASS_UJVC';
BYTE                          : 'BYTE';
CACHE                         : 'CACHE';
CACHE_CB                      : 'CACHE_CB';
CACHE_INSTANCES               : 'CACHE_INSTANCES';
CACHE_TEMP_TABLE              : 'CACHE_TEMP_TABLE';
CACHING                       : 'CACHING';
CALL                          : 'CALL';
CALLBACK                      : 'CALLBACK';
CANCEL                        : 'CANCEL';
CAPACITY                      : 'CAPACITY';
CARDINALITY                   : 'CARDINALITY';
CASCADE                       : 'CASCADE';
CASCADED                      : 'CASCADED';
CASE                          : 'CASE';
CAST                          : 'CAST';
CATEGORY                      : 'CATEGORY';
CEIL                          : 'CEIL';
CELL_FLASH_CACHE              : 'CELL_FLASH_CACHE';
CERTIFICATE                   : 'CERTIFICATE';
CERTIFICATE_DN                : 'CERTIFICATE_DN';
CFILE                         : 'CFILE';
CHAINED                       : 'CHAINED';
CHANGE                        : 'CHANGE';
CHANGE_DUPKEY_ERROR_INDEX     : 'CHANGE_DUPKEY_ERROR_INDEX';
CHANGETRACKING                : 'CHANGETRACKING';
CHAR                          : 'CHAR';
CHAR_CS                       : 'CHAR_CS';
CHARACTER                     : 'CHARACTER';
CHARSETFORM                   : 'CHARSETFORM';
CHARSETID                     : 'CHARSETID';
CHARTOROWID                   : 'CHARTOROWID';
CHECK                         : 'CHECK';
CHECK_ACL_REWRITE             : 'CHECK_ACL_REWRITE';
CHECKPOINT                    : 'CHECKPOINT';
CHILD                         : 'CHILD';
CHOOSE                        : 'CHOOSE';
CHR                           : 'CHR';
CHUNK                         : 'CHUNK';
CLASS                         : 'CLASS';
CLASSIFIER                    : 'CLASSIFIER';
CLAUSE                        : 'CLAUSE';
CLEAN                         : 'CLEAN';
CLEANUP                       : 'CLEANUP';
CLEAR                         : 'CLEAR';
CLIENT                        : 'CLIENT';
CLOB                          : 'CLOB';
CLONE                         : 'CLONE';
CLOSE                         : 'CLOSE';
CLOSE_CACHED_OPEN_CURSORS     : 'CLOSE_CACHED_OPEN_CURSORS';
CLUSTER                       : 'CLUSTER';
CLUSTER_DETAILS               : 'CLUSTER_DETAILS';
CLUSTER_DISTANCE              : 'CLUSTER_DISTANCE';
CLUSTER_ID                    : 'CLUSTER_ID';
CLUSTER_PROBABILITY           : 'CLUSTER_PROBABILITY';
CLUSTER_SET                   : 'CLUSTER_SET';
CLUSTERING                    : 'CLUSTERING';
CLUSTERING_FACTOR             : 'CLUSTERING_FACTOR';
CO_AUTH_IND                   : 'CO_AUTH_IND';
COALESCE                      : 'COALESCE';
COALESCE_SQ                   : 'COALESCE_SQ';
COARSE                        : 'COARSE';
COLD                          : 'COLD';
COLLATE                       : 'COLLATE';
COLLATION                     : 'COLLATION';
COLLECT                       : 'COLLECT';
COLUMN                        : 'COLUMN';
COLUMN_AUTH_INDICATOR         : 'COLUMN_AUTH_INDICATOR';
COLUMN_STATS                  : 'COLUMN_STATS';
COLUMN_VALUE                  : 'COLUMN_VALUE';
COLUMNAR                      : 'COLUMNAR';
COLUMNS                       : 'COLUMNS';
COMMENT                       : 'COMMENT';
COMMIT                        : 'COMMIT';
COMMITTED                     : 'COMMITTED';
COMPACT                       : 'COMPACT';
COMPATIBILITY                 : 'COMPATIBILITY';
COMPILE                       : 'COMPILE';
COMPLETE                      : 'COMPLETE';
COMPLIANCE                    : 'COMPLIANCE';
COMPONENT                     : 'COMPONENT';
COMPONENTS                    : 'COMPONENTS';
COMPOSE                       : 'COMPOSE';
COMPOSITE                     : 'COMPOSITE';
COMPOSITE_LIMIT               : 'COMPOSITE_LIMIT';
COMPOUND                      : 'COMPOUND';
COMPRESS                      : 'COMPRESS';
COMPUTATION                   : 'COMPUTATION';
COMPUTE                       : 'COMPUTE';
CON_ID                        : 'CON_ID';
CON_NAME                      : 'CON_NAME';
CONCAT                        : 'CONCAT';
CONDITION                     : 'CONDITION';
CONFIRM                       : 'CONFIRM';
CONFORMING                    : 'CONFORMING';
CONNECT                       : 'CONNECT';
CONNECT_BY_CB_WHR_ONLY        : 'CONNECT_BY_CB_WHR_ONLY';
CONNECT_BY_COMBINE_SW         : 'CONNECT_BY_COMBINE_SW';
CONNECT_BY_COST_BASED         : 'CONNECT_BY_COST_BASED';
CONNECT_BY_ELIM_DUPS          : 'CONNECT_BY_ELIM_DUPS';
CONNECT_BY_FILTERING          : 'CONNECT_BY_FILTERING';
CONNECT_BY_ISCYCLE            : 'CONNECT_BY_ISCYCLE';
CONNECT_BY_ISLEAF             : 'CONNECT_BY_ISLEAF';
CONNECT_BY_ROOT               : 'CONNECT_BY_ROOT';
CONNECT_TIME                  : 'CONNECT_TIME';
CONSIDER                      : 'CONSIDER';
CONSISTENT                    : 'CONSISTENT';
CONST                         : 'CONST';
CONSTANT                      : 'CONSTANT';
CONSTRAINT                    : 'CONSTRAINT';
CONSTRAINTS                   : 'CONSTRAINTS';
CONSTRUCTOR                   : 'CONSTRUCTOR';
CONTAINER                     : 'CONTAINER';
CONTAINER_DATA                : 'CONTAINER_DATA';
CONTAINER_MAP                 : 'CONTAINER_MAP';
CONTAINERS                    : 'CONTAINERS';
CONTAINERS_DEFAULT            : 'CONTAINERS_DEFAULT';
CONTENT                       : 'CONTENT';
CONTENTS                      : 'CONTENTS';
CONTEXT                       : 'CONTEXT';
CONTINUE                      : 'CONTINUE';
CONTROL                       : 'CONTROL';
CONTROLFILE                   : 'CONTROLFILE';
CONVERSION                    : 'CONVERSION';
CONVERT                       : 'CONVERT';
COOKIE                        : 'COOKIE';
COPY                          : 'COPY';
CORR                          : 'CORR';
CORR_K                        : 'CORR_K';
CORR_S                        : 'CORR_S';
CORRUPT_XID                   : 'CORRUPT_XID';
CORRUPT_XID_ALL               : 'CORRUPT_XID_ALL';
CORRUPTION                    : 'CORRUPTION';
COS                           : 'COS';
COSH                          : 'COSH';
COST                          : 'COST';
COST_XML_QUERY_REWRITE        : 'COST_XML_QUERY_REWRITE';
COUNT                         : 'COUNT';
COVAR_POP                     : 'COVAR_POP';
COVAR_SAMP                    : 'COVAR_SAMP';
COVERAGE                      : 'COVERAGE';
CPU_COSTING                   : 'CPU_COSTING';
CPU_PER_CALL                  : 'CPU_PER_CALL';
CPU_PER_SESSION               : 'CPU_PER_SESSION';
CRASH                         : 'CRASH';
CRE                           : 'CRE';
CREATE                        : 'CREATE';
CREATE_STORED_OUTLINES        : 'CREATE_STORED_OUTLINES';
CREATION                      : 'CREATION';
CREDENTIAL                    : 'CREDENTIAL';
CREDENTIALS                   : 'CREDENTIALS';
CRITICAL                      : 'CRITICAL';
CROSS                         : 'CROSS';
CROSSEDITION                  : 'CROSSEDITION';
CSCONVERT                     : 'CSCONVERT';
CUBE                          : 'CUBE';
CUBE_GB                       : 'CUBE_GB';
CUME_DIST                     : 'CUME_DIST';
CUME_DISTM                    : 'CUME_DISTM';
CURRENT                       : 'CURRENT';
CURRENT_DATE                  : 'CURRENT_DATE';
CURRENT_SCHEMA                : 'CURRENT_SCHEMA';
CURRENT_TIME                  : 'CURRENT_TIME';
CURRENT_TIMESTAMP             : 'CURRENT_TIMESTAMP';
CURRENT_USER                  : 'CURRENT_USER';
CURRENTV                      : 'CURRENTV';
CURSOR                        : 'CURSOR';
CURSOR_SHARING_EXACT          : 'CURSOR_SHARING_EXACT';
CURSOR_SPECIFIC_SEGMENT       : 'CURSOR_SPECIFIC_SEGMENT';
CV                            : 'CV';
CYCLE                         : 'CYCLE';
DANGLING                      : 'DANGLING';
DATA                          : 'DATA';
DATABASE                      : 'DATABASE';
DATABASE_DEFAULT              : 'DATABASE_DEFAULT';
DATAFILE                      : 'DATAFILE';
DATAFILES                     : 'DATAFILES';
DATAGUARDCONFIG               : 'DATAGUARDCONFIG';
DATAOBJ_TO_PARTITION          : 'DATAOBJ_TO_PARTITION';
DATAOBJNO                     : 'DATAOBJNO';
DATAPUMP                      : 'DATAPUMP';
DATE                          : 'DATE';
DATE_MODE                     : 'DATE_MODE';
DAY                           : 'DAY';
DAYS                          : 'DAYS';
DB_RECOVERY_FILE_DEST_SIZE    : 'DB_RECOVERY_FILE_DEST_SIZE';
DB_ROLE_CHANGE                : 'DB_ROLE_CHANGE';
DB_VERSION                    : 'DB_VERSION';
DBA                           : 'DBA';
DBA_RECYCLEBIN                : 'DBA_RECYCLEBIN';
DBMS_LOB                      : 'DBMS_LOB';
DBMS_STATS                    : 'DBMS_STATS';
DBTIMEZONE                    : 'DBTIMEZONE';
DDL                           : 'DDL';
DEALLOCATE                    : 'DEALLOCATE';
DEBUG                         : 'DEBUG';
DEBUGGER                      : 'DEBUGGER';
DEC                           : 'DEC';
DECIMAL                       : 'DECIMAL';
DECLARE                       : 'DECLARE';
DECODE                        : 'DECODE';
DECOMPOSE                     : 'DECOMPOSE';
DECR                          : 'DECR';
DECREMENT                     : 'DECREMENT';
DECRYPT                       : 'DECRYPT';
DEDUPLICATE                   : 'DEDUPLICATE';
DEFAULT                       : 'DEFAULT';
DEFAULT_COLLATION             : 'DEFAULT_COLLATION';
DEFAULT_CREDENTIAL            : 'DEFAULT_CREDENTIAL';
DEFAULTS                      : 'DEFAULTS';
DEFERRABLE                    : 'DEFERRABLE';
DEFERRED                      : 'DEFERRED';
DEFINE                        : 'DEFINE';
DEFINED                       : 'DEFINED';
DEFINER                       : 'DEFINER';
DEFINITION                    : 'DEFINITION';
DEGREE                        : 'DEGREE';
DELAY                         : 'DELAY';
DELEGATE                      : 'DELEGATE';
DELETE                        : 'DELETE';
DELETE_ALL                    : 'DELETE_ALL';
DELETEXML                     : 'DELETEXML';
DELETING                      : 'DELETING';
DEMAND                        : 'DEMAND';
DENSE_RANK                    : 'DENSE_RANK';
DENSE_RANKM                   : 'DENSE_RANKM';
DEPENDENT                     : 'DEPENDENT';
DEPRECATE                     : 'DEPRECATE';
DEPTH                         : 'DEPTH';
DEQUEUE                       : 'DEQUEUE';
DEREF                         : 'DEREF';
DEREF_NO_REWRITE              : 'DEREF_NO_REWRITE';
DESC                          : 'DESC';
DESTROY                       : 'DESTROY';
DETACHED                      : 'DETACHED';
DETERMINES                    : 'DETERMINES';
DETERMINISTIC                 : 'DETERMINISTIC';
DEVICE                        : 'DEVICE';
DICTIONARY                    : 'DICTIONARY';
DIGEST                        : 'DIGEST';
DIMENSION                     : 'DIMENSION';
DIRECT_LOAD                   : 'DIRECT_LOAD';
DIRECT_PATH                   : 'DIRECT_PATH';
DIRECTORY                     : 'DIRECTORY';
DISABLE                       : 'DISABLE';
DISABLE_ALL                   : 'DISABLE_ALL';
DISABLE_PRESET                : 'DISABLE_PRESET';
DISABLE_RPKE                  : 'DISABLE_RPKE';
DISALLOW                      : 'DISALLOW';
DISASSOCIATE                  : 'DISASSOCIATE';
DISCARD                       : 'DISCARD';
DISCARDFILE                   : 'DISCARDFILE';
DISCONNECT                    : 'DISCONNECT';
DISK                          : 'DISK';
DISKGROUP                     : 'DISKGROUP';
DISKS                         : 'DISKS';
DISMOUNT                      : 'DISMOUNT';
DISTINCT                      : 'DISTINCT';
DISTINGUISHED                 : 'DISTINGUISHED';
DISTRIBUTE                    : 'DISTRIBUTE';
DISTRIBUTED                   : 'DISTRIBUTED';
DML                           : 'DML';
DML_UPDATE                    : 'DML_UPDATE';
DO                            : 'DO';
DOCFIDELITY                   : 'DOCFIDELITY';
DOCUMENT                      : 'DOCUMENT';
DOMAIN_INDEX_FILTER           : 'DOMAIN_INDEX_FILTER';
DOMAIN_INDEX_NO_SORT          : 'DOMAIN_INDEX_NO_SORT';
DOMAIN_INDEX_SORT             : 'DOMAIN_INDEX_SORT';
DOUBLE                        : 'DOUBLE';
DOWNGRADE                     : 'DOWNGRADE';
DRIVING_SITE                  : 'DRIVING_SITE';
DROP                          : 'DROP';
DROP_COLUMN                   : 'DROP_COLUMN';
DROP_GROUP                    : 'DROP_GROUP';
DST_UPGRADE_INSERT_CONV       : 'DST_UPGRADE_INSERT_CONV';
DUMP                          : 'DUMP';
DUMPSET                       : 'DUMPSET';
DUPLICATE                     : 'DUPLICATE';
DUPLICATED                    : 'DUPLICATED';
DURATION                      : 'DURATION';
DV                            : 'DV';
DYNAMIC                       : 'DYNAMIC';
DYNAMIC_SAMPLING              : 'DYNAMIC_SAMPLING';
DYNAMIC_SAMPLING_EST_CDN      : 'DYNAMIC_SAMPLING_EST_CDN';
EACH                          : 'EACH';
EDITION                       : 'EDITION';
EDITIONABLE                   : 'EDITIONABLE';
EDITIONING                    : 'EDITIONING';
EDITIONS                      : 'EDITIONS';
ELEMENT                       : 'ELEMENT';
ELIMINATE_JOIN                : 'ELIMINATE_JOIN';
ELIMINATE_OBY                 : 'ELIMINATE_OBY';
ELIMINATE_OUTER_JOIN          : 'ELIMINATE_OUTER_JOIN';
ELSE                          : 'ELSE';
ELSIF                         : 'ELSIF';
EMPTY                         : 'EMPTY';
EMPTY_BLOB                    : 'EMPTY_BLOB';
EMPTY_CLOB                    : 'EMPTY_CLOB';
ENABLE                        : 'ENABLE';
ENABLE_ALL                    : 'ENABLE_ALL';
ENABLE_PRESET                 : 'ENABLE_PRESET';
ENCLOSED                      : 'ENCLOSED';
ENCODING                      : 'ENCODING';
ENCRYPT                       : 'ENCRYPT';
ENCRYPTION                    : 'ENCRYPTION';
END                           : 'END';
END_OUTLINE_DATA              : 'END_OUTLINE_DATA';
ENFORCE                       : 'ENFORCE';
ENFORCED                      : 'ENFORCED';
ENQUEUE                       : 'ENQUEUE';
ENTERPRISE                    : 'ENTERPRISE';
ENTITYESCAPING                : 'ENTITYESCAPING';
ENTRY                         : 'ENTRY';
EQ_REGEX                      : 'EQ_REGEX';
EQUALS_PATH                   : 'EQUALS_PATH';
EQUIVALENCE                   : 'EQUIVALENCE';
ERR                           : 'ERR';
ERROR                         : 'ERROR';
ERROR_ARGUMENT                : 'ERROR_ARGUMENT';
ERROR_ON_OVERLAP_TIME         : 'ERROR_ON_OVERLAP_TIME';
ERRORS                        : 'ERRORS';
ESCAPE                        : 'ESCAPE';
ESTIMATE                      : 'ESTIMATE';
EVAL                          : 'EVAL';
EVALNAME                      : 'EVALNAME';
EVALUATE                      : 'EVALUATE';
EVALUATION                    : 'EVALUATION';
EVENTS                        : 'EVENTS';
EVERY                         : 'EVERY';
EXCEPT                        : 'EXCEPT';
EXCEPTION                     : 'EXCEPTION';
EXCEPTION_INIT                : 'EXCEPTION_INIT';
EXCEPTIONS                    : 'EXCEPTIONS';
EXCHANGE                      : 'EXCHANGE';
EXCLUDE                       : 'EXCLUDE';
EXCLUDING                     : 'EXCLUDING';
EXCLUSIVE                     : 'EXCLUSIVE';
EXECUTE                       : 'EXECUTE';
EXEMPT                        : 'EXEMPT';
EXISTS                        : 'EXISTS';
EXISTSNODE                    : 'EXISTSNODE';
EXIT                          : 'EXIT';
EXP                           : 'EXP';
EXPAND_GSET_TO_UNION          : 'EXPAND_GSET_TO_UNION';
EXPAND_TABLE                  : 'EXPAND_TABLE';
EXPIRE                        : 'EXPIRE';
EXPLAIN                       : 'EXPLAIN';
EXPLOSION                     : 'EXPLOSION';
EXPORT                        : 'EXPORT';
EXPR_CORR_CHECK               : 'EXPR_CORR_CHECK';
EXTEND                        : 'EXTEND';
EXTENDED                      : 'EXTENDED';
EXTENDS                       : 'EXTENDS';
EXTENT                        : 'EXTENT';
EXTENTS                       : 'EXTENTS';
EXTERNAL                      : 'EXTERNAL';
EXTERNALLY                    : 'EXTERNALLY';
EXTRA                         : 'EXTRA';
EXTRACT                       : 'EXTRACT';
EXTRACTVALUE                  : 'EXTRACTVALUE';
FACILITY                      : 'FACILITY';
FACT                          : 'FACT';
FACTOR                        : 'FACTOR';
FACTORIZE_JOIN                : 'FACTORIZE_JOIN';
FAILED                        : 'FAILED';
FAILED_LOGIN_ATTEMPTS         : 'FAILED_LOGIN_ATTEMPTS';
FAILGROUP                     : 'FAILGROUP';
FAILOVER                      : 'FAILOVER';
FAILURE                       : 'FAILURE';
FALSE                         : 'FALSE';
FAR                           : 'FAR';
FAST                          : 'FAST';
FBTSCAN                       : 'FBTSCAN';
FEATURE                       : 'FEATURE';
FEATURE_COMPARE               : 'FEATURE_COMPARE';
FEATURE_DETAILS               : 'FEATURE_DETAILS';
FEATURE_ID                    : 'FEATURE_ID';
FEATURE_SET                   : 'FEATURE_SET';
FEATURE_VALUE                 : 'FEATURE_VALUE';
FETCH                         : 'FETCH';
FILE                          : 'FILE';
FILE_NAME_CONVERT             : 'FILE_NAME_CONVERT';
FILEGROUP                     : 'FILEGROUP';
FILESYSTEM_LIKE_LOGGING       : 'FILESYSTEM_LIKE_LOGGING';
FILTER                        : 'FILTER';
FINAL                         : 'FINAL';
FINE                          : 'FINE';
FINISH                        : 'FINISH';
FIRST                         : 'FIRST';
FIRST_ROWS                    : 'FIRST_ROWS';
FIRST_VALUE                   : 'FIRST_VALUE';
FIRSTM                        : 'FIRSTM';
FLAGGER                       : 'FLAGGER';
FLASH_CACHE                   : 'FLASH_CACHE';
FLASHBACK                     : 'FLASHBACK';
FLEX                          : 'FLEX';
FLOAT                         : 'FLOAT';
FLOB                          : 'FLOB';
FLOOR                         : 'FLOOR';
FLUSH                         : 'FLUSH';
FOLDER                        : 'FOLDER';
FOLLOWING                     : 'FOLLOWING';
FOLLOWS                       : 'FOLLOWS';
FOR                           : 'FOR';
FORALL                        : 'FORALL';
FORCE                         : 'FORCE';
FORCE_XML_QUERY_REWRITE       : 'FORCE_XML_QUERY_REWRITE';
FOREIGN                       : 'FOREIGN';
FOREVER                       : 'FOREVER';
FORMAT                        : 'FORMAT';
FORWARD                       : 'FORWARD';
FREELIST                      : 'FREELIST';
FREELISTS                     : 'FREELISTS';
FREEPOOLS                     : 'FREEPOOLS';
FRESH                         : 'FRESH';
FROM                          : 'FROM';
FROM_TZ                       : 'FROM_TZ';
FULL                          : 'FULL';
FUNCTION                      : 'FUNCTION';
FUNCTIONS                     : 'FUNCTIONS';
GATHER_PLAN_STATISTICS        : 'GATHER_PLAN_STATISTICS';
GBY_CONC_ROLLUP               : 'GBY_CONC_ROLLUP';
GBY_PUSHDOWN                  : 'GBY_PUSHDOWN';
GENERATED                     : 'GENERATED';
GET                           : 'GET';
GLOBAL                        : 'GLOBAL';
GLOBAL_NAME                   : 'GLOBAL_NAME';
GLOBAL_TOPIC_ENABLED          : 'GLOBAL_TOPIC_ENABLED';
GLOBALLY                      : 'GLOBALLY';
GOTO                          : 'GOTO';
GRANT                         : 'GRANT';
GRANTED                       : 'GRANTED';
GREATEST                      : 'GREATEST';
GROUP                         : 'GROUP';
GROUP_BY                      : 'GROUP_BY';
GROUP_ID                      : 'GROUP_ID';
GROUPING                      : 'GROUPING';
GROUPING_ID                   : 'GROUPING_ID';
GROUPS                        : 'GROUPS';
GUARANTEE                     : 'GUARANTEE';
GUARANTEED                    : 'GUARANTEED';
GUARD                         : 'GUARD';
HAS                           : 'HAS';
HASH                          : 'HASH';
HASH_AJ                       : 'HASH_AJ';
HASH_SJ                       : 'HASH_SJ';
HASHING                       : 'HASHING';
HASHKEYS                      : 'HASHKEYS';
HAVING                        : 'HAVING';
HEADER                        : 'HEADER';
HEAP                          : 'HEAP';
HELP                          : 'HELP';
HEXTORAW                      : 'HEXTORAW';
HEXTOREF                      : 'HEXTOREF';
HIDE                          : 'HIDE';
HIER_ANCESTOR                 : 'HIER_ANCESTOR';
HIER_CAPTION                  : 'HIER_CAPTION';
HIER_DEPTH                    : 'HIER_DEPTH';
HIER_DESCRIPTION              : 'HIER_DESCRIPTION';
HIER_LAG                      : 'HIER_LAG';
HIER_LEAD                     : 'HIER_LEAD';
HIER_LEVEL                    : 'HIER_LEVEL';
HIER_MEMBER_NAME              : 'HIER_MEMBER_NAME';
HIER_MEMBER_UNIQUE_NAME       : 'HIER_MEMBER_UNIQUE_NAME';
HIER_PARENT                   : 'HIER_PARENT';
HIERARCHIES                   : 'HIERARCHIES';
HIERARCHY                     : 'HIERARCHY';
HIGH                          : 'HIGH';
HINTSET_BEGIN                 : 'HINTSET_BEGIN';
HINTSET_END                   : 'HINTSET_END';
HISTORY                       : 'HISTORY';
HOST                          : 'HOST';
HOT                           : 'HOT';
HOUR                          : 'HOUR';
HOURS                         : 'HOURS';
HTTP                          : 'HTTP';
HWM_BROKERED                  : 'HWM_BROKERED';
HYBRID                        : 'HYBRID';
IAM_GROUP_NAME                : 'IAM_GROUP_NAME';
IAM_PRINCIPAL_NAME            : 'IAM_PRINCIPAL_NAME';
ID                            : 'ID';
IDENTIFIED                    : 'IDENTIFIED';
IDENTIFIER                    : 'IDENTIFIER';
IDENTITY                      : 'IDENTITY';
IDGENERATORS                  : 'IDGENERATORS';
IDLE                          : 'IDLE';
IDLE_TIME                     : 'IDLE_TIME';
IF                            : 'IF';
IGNORE                        : 'IGNORE';
IGNORE_OPTIM_EMBEDDED_HINTS   : 'IGNORE_OPTIM_EMBEDDED_HINTS';
IGNORE_ROW_ON_DUPKEY_INDEX    : 'IGNORE_ROW_ON_DUPKEY_INDEX';
IGNORE_WHERE_CLAUSE           : 'IGNORE_WHERE_CLAUSE';
ILM                           : 'ILM';
IMMEDIATE                     : 'IMMEDIATE';
IMMUTABLE                     : 'IMMUTABLE';
IMPACT                        : 'IMPACT';
IMPORT                        : 'IMPORT';
IN                            : 'IN';
IN_MEMORY_METADATA            : 'IN_MEMORY_METADATA';
IN_XQUERY                     : 'IN_XQUERY';
INACTIVE                      : 'INACTIVE';
INACTIVE_ACCOUNT_TIME         : 'INACTIVE_ACCOUNT_TIME';
INCLUDE                       : 'INCLUDE';
INCLUDE_VERSION               : 'INCLUDE_VERSION';
INCLUDING                     : 'INCLUDING';
INCR                          : 'INCR';
INCREMENT                     : 'INCREMENT';
INCREMENTAL                   : 'INCREMENTAL';
INDENT                        : 'INDENT';
INDEX                         : 'INDEX';
INDEX_ALL_PATHS               : 'INDEX_ALL_PATHS';
INDEX_ASC                     : 'INDEX_ASC';
INDEX_COMBINE                 : 'INDEX_COMBINE';
INDEX_DESC                    : 'INDEX_DESC';
INDEX_FFS                     : 'INDEX_FFS';
INDEX_FILTER                  : 'INDEX_FILTER';
INDEX_JOIN                    : 'INDEX_JOIN';
INDEX_ROWS                    : 'INDEX_ROWS';
INDEX_RRS                     : 'INDEX_RRS';
INDEX_RS                      : 'INDEX_RS';
INDEX_RS_ASC                  : 'INDEX_RS_ASC';
INDEX_RS_DESC                 : 'INDEX_RS_DESC';
INDEX_SCAN                    : 'INDEX_SCAN';
INDEX_SKIP_SCAN               : 'INDEX_SKIP_SCAN';
INDEX_SS                      : 'INDEX_SS';
INDEX_SS_ASC                  : 'INDEX_SS_ASC';
INDEX_SS_DESC                 : 'INDEX_SS_DESC';
INDEX_STATS                   : 'INDEX_STATS';
INDEXED                       : 'INDEXED';
INDEXES                       : 'INDEXES';
INDEXING                      : 'INDEXING';
INDEXTYPE                     : 'INDEXTYPE';
INDEXTYPES                    : 'INDEXTYPES';
INDICATOR                     : 'INDICATOR';
INDICES                       : 'INDICES';
INFINITE                      : 'INFINITE';
INFORMATIONAL                 : 'INFORMATIONAL';
INHERIT                       : 'INHERIT';
INITCAP                       : 'INITCAP';
INITIAL                       : 'INITIAL';
INITIALIZED                   : 'INITIALIZED';
INITIALLY                     : 'INITIALLY';
INITRANS                      : 'INITRANS';
INLINE                        : 'INLINE';
INLINE_XMLTYPE_NT             : 'INLINE_XMLTYPE_NT';
INMEMORY                      : 'INMEMORY';
INNER                         : 'INNER';
INSERT                        : 'INSERT';
INSERTCHILDXML                : 'INSERTCHILDXML';
INSERTCHILDXMLAFTER           : 'INSERTCHILDXMLAFTER';
INSERTCHILDXMLBEFORE          : 'INSERTCHILDXMLBEFORE';
INSERTING                     : 'INSERTING';
INSERTXMLAFTER                : 'INSERTXMLAFTER';
INSERTXMLBEFORE               : 'INSERTXMLBEFORE';
INSTALL                       : 'INSTALL';
INSTANCE                      : 'INSTANCE';
INSTANCES                     : 'INSTANCES';
INSTANTIABLE                  : 'INSTANTIABLE';
INSTANTLY                     : 'INSTANTLY';
INSTEAD                       : 'INSTEAD';
INSTR                         : 'INSTR';
INSTR2                        : 'INSTR2';
INSTR4                        : 'INSTR4';
INSTRB                        : 'INSTRB';
INSTRC                        : 'INSTRC';
INT                           : 'INT';
INTEGER                       : 'INTEGER';
INTERLEAVED                   : 'INTERLEAVED';
INTERMEDIATE                  : 'INTERMEDIATE';
INTERNAL                      : 'INTERNAL';
INTERNAL_CONVERT              : 'INTERNAL_CONVERT';
INTERNAL_USE                  : 'INTERNAL_USE';
INTERPRETED                   : 'INTERPRETED';
INTERSECT                     : 'INTERSECT';
INTERVAL                      : 'INTERVAL';
INTO                          : 'INTO';
INVALIDATE                    : 'INVALIDATE';
INVALIDATION                  : 'INVALIDATION';
INVISIBLE                     : 'INVISIBLE';
IS                            : 'IS';
ISOLATION                     : 'ISOLATION';
ISOLATION_LEVEL               : 'ISOLATION_LEVEL';
ISSCHEMAVALID                 : 'ISSCHEMAVALID';
ITERATE                       : 'ITERATE';
ITERATION_NUMBER              : 'ITERATION_NUMBER';
JAVA                          : 'JAVA';
JOB                           : 'JOB';
JOIN                          : 'JOIN';
JSON                          : 'JSON';
JSON_EQUAL                    : 'JSON_EQUAL';
JSON_EXISTS                   : 'JSON_EXISTS';
JSON_TEXTCONTAINS             : 'JSON_TEXTCONTAINS';
KEEP                          : 'KEEP';
KEEP_DUPLICATES               : 'KEEP_DUPLICATES';
KERBEROS                      : 'KERBEROS';
KERBEROS_PRINCIPAL_NAME       : 'KERBEROS_PRINCIPAL_NAME';
KEY                           : 'KEY';
KEY_LENGTH                    : 'KEY_LENGTH';
KEYS                          : 'KEYS';
KEYSIZE                       : 'KEYSIZE';
KILL                          : 'KILL';
LABEL                         : 'LABEL';
LAG                           : 'LAG';
LAG_DIF_PERCENT               : 'LAG_DIF_PERCENT';
LAG_DIFF                      : 'LAG_DIFF';
LANGUAGE                      : 'LANGUAGE';
LAST                          : 'LAST';
LAST_DAY                      : 'LAST_DAY';
LAST_VALUE                    : 'LAST_VALUE';
LATERAL                       : 'LATERAL';
LAX                           : 'LAX';
LAYER                         : 'LAYER';
LDAP_REG_SYNC_INTERVAL        : 'LDAP_REG_SYNC_INTERVAL';
LDAP_REGISTRATION             : 'LDAP_REGISTRATION';
LDAP_REGISTRATION_ENABLED     : 'LDAP_REGISTRATION_ENABLED';
LEAD                          : 'LEAD';
LEAD_CDB                      : 'LEAD_CDB';
LEAD_CDB_URI                  : 'LEAD_CDB_URI';
LEAD_DIFF                     : 'LEAD_DIFF';
LEAD_DIFF_PERCENT             : 'LEAD_DIFF_PERCENT';
LEADING                       : 'LEADING';
LEAF                          : 'LEAF';
LEAST                         : 'LEAST';
LEFT                          : 'LEFT';
LENGTH                        : 'LENGTH';
LENGTH2                       : 'LENGTH2';
LENGTH4                       : 'LENGTH4';
LENGTHB                       : 'LENGTHB';
LENGTHC                       : 'LENGTHC';
LESS                          : 'LESS';
LEVEL                         : 'LEVEL';
LEVELS                        : 'LEVELS';
LIBRARY                       : 'LIBRARY';
LIFE                          : 'LIFE';
LIFETIME                      : 'LIFETIME';
LIKE                          : 'LIKE';
LIKE2                         : 'LIKE2';
LIKE4                         : 'LIKE4';
LIKE_EXPAND                   : 'LIKE_EXPAND';
LIKE_REGEX                    : 'LIKE_REGEX';
LIKEC                         : 'LIKEC';
LIMIT                         : 'LIMIT';
LINEAR                        : 'LINEAR';
LINK                          : 'LINK';
LIST                          : 'LIST';
LISTAGG                       : 'LISTAGG';
LN                            : 'LN';
LNNVL                         : 'LNNVL';
LNO                           : 'LNO';
LOAD                          : 'LOAD';
LOB                           : 'LOB';
LOBNVL                        : 'LOBNVL';
LOBPREFETCH                   : 'LOBPREFETCH';
LOBS                          : 'LOBS';
LOCAL                         : 'LOCAL';
LOCAL_INDEXES                 : 'LOCAL_INDEXES';
LOCALTIME                     : 'LOCALTIME';
LOCALTIMESTAMP                : 'LOCALTIMESTAMP';
LOCATION                      : 'LOCATION';
LOCATOR                       : 'LOCATOR';
LOCK                          : 'LOCK';
LOCKDOWN                      : 'LOCKDOWN';
LOCKED                        : 'LOCKED';
LOCKING                       : 'LOCKING';
LOG                           : 'LOG';
LOGFILE                       : 'LOGFILE';
LOGFILES                      : 'LOGFILES';
LOGGING                       : 'LOGGING';
LOGICAL                       : 'LOGICAL';
LOGICAL_READS_PER_CALL        : 'LOGICAL_READS_PER_CALL';
LOGICAL_READS_PER_SESSION     : 'LOGICAL_READS_PER_SESSION';
LOGMINING                     : 'LOGMINING';
LOGOFF                        : 'LOGOFF';
LOGON                         : 'LOGON';
LONG                          : 'LONG';
LOOP                          : 'LOOP';
LOST                          : 'LOST';
LOW                           : 'LOW';
LOWER                         : 'LOWER';
LPAD                          : 'LPAD';
LRTRIM                        : 'LRTRIM';
LTRIM                         : 'LTRIM';
MAIN                          : 'MAIN';
MAKE_REF                      : 'MAKE_REF';
MANAGE                        : 'MANAGE';
MANAGED                       : 'MANAGED';
MANAGEMENT                    : 'MANAGEMENT';
MANDATORY                     : 'MANDATORY';
MANUAL                        : 'MANUAL';
MAP                           : 'MAP';
MAPPING                       : 'MAPPING';
MASTER                        : 'MASTER';
MATCH                         : 'MATCH';
MATCH_NUMBER                  : 'MATCH_NUMBER';
MATCH_RECOGNIZE               : 'MATCH_RECOGNIZE';
MATCHED                       : 'MATCHED';
MATERIALIZE                   : 'MATERIALIZE';
MATERIALIZED                  : 'MATERIALIZED';
MAX                           : 'MAX';
MAX_AUDIT_SIZE                : 'MAX_AUDIT_SIZE';
MAX_DIAG_SIZE                 : 'MAX_DIAG_SIZE';
MAXARCHLOGS                   : 'MAXARCHLOGS';
MAXDATAFILES                  : 'MAXDATAFILES';
MAXEXTENTS                    : 'MAXEXTENTS';
MAXIMIZE                      : 'MAXIMIZE';
MAXINSTANCES                  : 'MAXINSTANCES';
MAXLEN                        : 'MAXLEN';
MAXLOGFILES                   : 'MAXLOGFILES';
MAXLOGHISTORY                 : 'MAXLOGHISTORY';
MAXLOGMEMBERS                 : 'MAXLOGMEMBERS';
MAXSIZE                       : 'MAXSIZE';
MAXTRANS                      : 'MAXTRANS';
MAXVALUE                      : 'MAXVALUE';
MEASURE                       : 'MEASURE';
MEASURES                      : 'MEASURES';
MEDIAN                        : 'MEDIAN';
MEDIUM                        : 'MEDIUM';
MEMBER                        : 'MEMBER';
MEMCOMPRESS                   : 'MEMCOMPRESS';
MEMOPTIMIZE                   : 'MEMOPTIMIZE';
MEMORY                        : 'MEMORY';
MERGE                         : 'MERGE';
MERGE_AJ                      : 'MERGE_AJ';
MERGE_CONST_ON                : 'MERGE_CONST_ON';
MERGE_SJ                      : 'MERGE_SJ';
METADATA                      : 'METADATA';
METHOD                        : 'METHOD';
MICROSECOND                   : 'MICROSECOND';
MIGRATE                       : 'MIGRATE';
MIGRATION                     : 'MIGRATION';
MIN                           : 'MIN';
MINEXTENTS                    : 'MINEXTENTS';
MINIMIZE                      : 'MINIMIZE';
MINIMUM                       : 'MINIMUM';
MINING                        : 'MINING';
MINUS                         : 'MINUS';
MINUS_NULL                    : 'MINUS_NULL';
MINUTE                        : 'MINUTE';
MINUTES                       : 'MINUTES';
MINVALUE                      : 'MINVALUE';
MIRROR                        : 'MIRROR';
MIRRORCOLD                    : 'MIRRORCOLD';
MIRRORHOT                     : 'MIRRORHOT';
MLSLABEL                      : 'MLSLABEL';
MOD                           : 'MOD';
MODE                          : 'MODE';
MODEL                         : 'MODEL';
MODEL_COMPILE_SUBQUERY        : 'MODEL_COMPILE_SUBQUERY';
MODEL_DONTVERIFY_UNIQUENESS   : 'MODEL_DONTVERIFY_UNIQUENESS';
MODEL_DYNAMIC_SUBQUERY        : 'MODEL_DYNAMIC_SUBQUERY';
MODEL_MIN_ANALYSIS            : 'MODEL_MIN_ANALYSIS';
MODEL_NO_ANALYSIS             : 'MODEL_NO_ANALYSIS';
MODEL_PBY                     : 'MODEL_PBY';
MODEL_PUSH_REF                : 'MODEL_PUSH_REF';
MODIFICATION                  : 'MODIFICATION';
MODIFY                        : 'MODIFY';
MODIFY_COLUMN_TYPE            : 'MODIFY_COLUMN_TYPE';
MONITOR                       : 'MONITOR';
MONITORING                    : 'MONITORING';
MONTH                         : 'MONTH';
MONTHS                        : 'MONTHS';
MONTHS_BETWEEN                : 'MONTHS_BETWEEN';
MOUNT                         : 'MOUNT';
MOUNTPATH                     : 'MOUNTPATH';
MOVE                          : 'MOVE';
MOVEMENT                      : 'MOVEMENT';
MULTISET                      : 'MULTISET';
MUTABLE                       : 'MUTABLE';
MV_MERGE                      : 'MV_MERGE';
NAME                          : 'NAME';
NAMED                         : 'NAMED';
NAMES                         : 'NAMES';
NAMESPACE                     : 'NAMESPACE';
NAN                           : 'NAN';
NANVL                         : 'NANVL';
NATIONAL                      : 'NATIONAL';
NATIVE                        : 'NATIVE';
NATIVE_FULL_OUTER_JOIN        : 'NATIVE_FULL_OUTER_JOIN';
NATURAL                       : 'NATURAL';
NATURALN                      : 'NATURALN';
NAV                           : 'NAV';
NCHAR                         : 'NCHAR';
NCHAR_CS                      : 'NCHAR_CS';
NCHR                          : 'NCHR';
NCLOB                         : 'NCLOB';
NEEDED                        : 'NEEDED';
NEG                           : 'NEG';
NESTED                        : 'NESTED';
NESTED_TABLE_FAST_INSERT      : 'NESTED_TABLE_FAST_INSERT';
NESTED_TABLE_GET_REFS         : 'NESTED_TABLE_GET_REFS';
NESTED_TABLE_ID               : 'NESTED_TABLE_ID';
NESTED_TABLE_SET_REFS         : 'NESTED_TABLE_SET_REFS';
NESTED_TABLE_SET_SETID        : 'NESTED_TABLE_SET_SETID';
NETWORK                       : 'NETWORK';
NEVER                         : 'NEVER';
NEW                           : 'NEW';
NEW_TIME                      : 'NEW_TIME';
NEXT                          : 'NEXT';
NEXT_DAY                      : 'NEXT_DAY';
NL_AJ                         : 'NL_AJ';
NL_SJ                         : 'NL_SJ';
NLJ_BATCHING                  : 'NLJ_BATCHING';
NLJ_INDEX_FILTER              : 'NLJ_INDEX_FILTER';
NLJ_INDEX_SCAN                : 'NLJ_INDEX_SCAN';
NLJ_PREFETCH                  : 'NLJ_PREFETCH';
NLS_CALENDAR                  : 'NLS_CALENDAR';
NLS_CHARACTERSET              : 'NLS_CHARACTERSET';
NLS_CHARSET_DECL_LEN          : 'NLS_CHARSET_DECL_LEN';
NLS_CHARSET_ID                : 'NLS_CHARSET_ID';
NLS_CHARSET_NAME              : 'NLS_CHARSET_NAME';
NLS_COMP                      : 'NLS_COMP';
NLS_CURRENCY                  : 'NLS_CURRENCY';
NLS_DATE_FORMAT               : 'NLS_DATE_FORMAT';
NLS_DATE_LANGUAGE             : 'NLS_DATE_LANGUAGE';
NLS_INITCAP                   : 'NLS_INITCAP';
NLS_ISO_CURRENCY              : 'NLS_ISO_CURRENCY';
NLS_LANG                      : 'NLS_LANG';
NLS_LANGUAGE                  : 'NLS_LANGUAGE';
NLS_LENGTH_SEMANTICS          : 'NLS_LENGTH_SEMANTICS';
NLS_LOWER                     : 'NLS_LOWER';
NLS_NCHAR_CONV_EXCP           : 'NLS_NCHAR_CONV_EXCP';
NLS_NUMERIC_CHARACTERS        : 'NLS_NUMERIC_CHARACTERS';
NLS_SORT                      : 'NLS_SORT';
NLS_SPECIAL_CHARS             : 'NLS_SPECIAL_CHARS';
NLS_TERRITORY                 : 'NLS_TERRITORY';
NLS_UPPER                     : 'NLS_UPPER';
NLSSORT                       : 'NLSSORT';
NO                            : 'NO';
NO_ACCESS                     : 'NO_ACCESS';
NO_BASETABLE_MULTIMV_REWRITE  : 'NO_BASETABLE_MULTIMV_REWRITE';
NO_BIND_AWARE                 : 'NO_BIND_AWARE';
NO_BUFFER                     : 'NO_BUFFER';
NO_CARTESIAN                  : 'NO_CARTESIAN';
NO_CHECK_ACL_REWRITE          : 'NO_CHECK_ACL_REWRITE';
NO_COALESCE_SQ                : 'NO_COALESCE_SQ';
NO_CONNECT_BY_CB_WHR_ONLY     : 'NO_CONNECT_BY_CB_WHR_ONLY';
NO_CONNECT_BY_COMBINE_SW      : 'NO_CONNECT_BY_COMBINE_SW';
NO_CONNECT_BY_COST_BASED      : 'NO_CONNECT_BY_COST_BASED';
NO_CONNECT_BY_ELIM_DUPS       : 'NO_CONNECT_BY_ELIM_DUPS';
NO_CONNECT_BY_FILTERING       : 'NO_CONNECT_BY_FILTERING';
NO_COST_XML_QUERY_REWRITE     : 'NO_COST_XML_QUERY_REWRITE';
NO_CPU_COSTING                : 'NO_CPU_COSTING';
NO_DOMAIN_INDEX_FILTER        : 'NO_DOMAIN_INDEX_FILTER';
NO_DST_UPGRADE_INSERT_CONV    : 'NO_DST_UPGRADE_INSERT_CONV';
NO_ELIMINATE_JOIN             : 'NO_ELIMINATE_JOIN';
NO_ELIMINATE_OBY              : 'NO_ELIMINATE_OBY';
NO_ELIMINATE_OUTER_JOIN       : 'NO_ELIMINATE_OUTER_JOIN';
NO_EXPAND                     : 'NO_EXPAND';
NO_EXPAND_GSET_TO_UNION       : 'NO_EXPAND_GSET_TO_UNION';
NO_EXPAND_TABLE               : 'NO_EXPAND_TABLE';
NO_FACT                       : 'NO_FACT';
NO_FACTORIZE_JOIN             : 'NO_FACTORIZE_JOIN';
NO_FILTERING                  : 'NO_FILTERING';
NO_GBY_PUSHDOWN               : 'NO_GBY_PUSHDOWN';
NO_INDEX                      : 'NO_INDEX';
NO_INDEX_FFS                  : 'NO_INDEX_FFS';
NO_INDEX_SS                   : 'NO_INDEX_SS';
NO_LOAD                       : 'NO_LOAD';
NO_MERGE                      : 'NO_MERGE';
NO_MODEL_PUSH_REF             : 'NO_MODEL_PUSH_REF';
NO_MONITOR                    : 'NO_MONITOR';
NO_MONITORING                 : 'NO_MONITORING';
NO_MULTIMV_REWRITE            : 'NO_MULTIMV_REWRITE';
NO_NATIVE_FULL_OUTER_JOIN     : 'NO_NATIVE_FULL_OUTER_JOIN';
NO_NLJ_BATCHING               : 'NO_NLJ_BATCHING';
NO_NLJ_PREFETCH               : 'NO_NLJ_PREFETCH';
NO_ORDER_ROLLUPS              : 'NO_ORDER_ROLLUPS';
NO_OUTER_JOIN_TO_INNER        : 'NO_OUTER_JOIN_TO_INNER';
NO_PARALLEL                   : 'NO_PARALLEL';
NO_PARALLEL_INDEX             : 'NO_PARALLEL_INDEX';
NO_PARTIAL_COMMIT             : 'NO_PARTIAL_COMMIT';
NO_PLACE_DISTINCT             : 'NO_PLACE_DISTINCT';
NO_PLACE_GROUP_BY             : 'NO_PLACE_GROUP_BY';
NO_PQ_MAP                     : 'NO_PQ_MAP';
NO_PRUNE_GSETS                : 'NO_PRUNE_GSETS';
NO_PULL_PRED                  : 'NO_PULL_PRED';
NO_PUSH_PRED                  : 'NO_PUSH_PRED';
NO_PUSH_SUBQ                  : 'NO_PUSH_SUBQ';
NO_PX_JOIN_FILTER             : 'NO_PX_JOIN_FILTER';
NO_QKN_BUFF                   : 'NO_QKN_BUFF';
NO_QUERY_TRANSFORMATION       : 'NO_QUERY_TRANSFORMATION';
NO_REF_CASCADE                : 'NO_REF_CASCADE';
NO_RESULT_CACHE               : 'NO_RESULT_CACHE';
NO_REWRITE                    : 'NO_REWRITE';
NO_SEMIJOIN                   : 'NO_SEMIJOIN';
NO_SET_TO_JOIN                : 'NO_SET_TO_JOIN';
NO_SQL_TUNE                   : 'NO_SQL_TUNE';
NO_STAR_TRANSFORMATION        : 'NO_STAR_TRANSFORMATION';
NO_STATEMENT_QUEUING          : 'NO_STATEMENT_QUEUING';
NO_STATS_GSETS                : 'NO_STATS_GSETS';
NO_SUBQUERY_PRUNING           : 'NO_SUBQUERY_PRUNING';
NO_SUBSTRB_PAD                : 'NO_SUBSTRB_PAD';
NO_SWAP_JOIN_INPUTS           : 'NO_SWAP_JOIN_INPUTS';
NO_TEMP_TABLE                 : 'NO_TEMP_TABLE';
NO_TRANSFORM_DISTINCT_AGG     : 'NO_TRANSFORM_DISTINCT_AGG';
NO_UNNEST                     : 'NO_UNNEST';
NO_USE_HASH                   : 'NO_USE_HASH';
NO_USE_HASH_AGGREGATION       : 'NO_USE_HASH_AGGREGATION';
NO_USE_INVISIBLE_INDEXES      : 'NO_USE_INVISIBLE_INDEXES';
NO_USE_MERGE                  : 'NO_USE_MERGE';
NO_USE_NL                     : 'NO_USE_NL';
NO_XML_DML_REWRITE            : 'NO_XML_DML_REWRITE';
NO_XML_QUERY_REWRITE          : 'NO_XML_QUERY_REWRITE';
NO_XMLINDEX_REWRITE           : 'NO_XMLINDEX_REWRITE';
NO_XMLINDEX_REWRITE_IN_SELECT : 'NO_XMLINDEX_REWRITE_IN_SELECT';
NOAPPEND                      : 'NOAPPEND';
NOARCHIVELOG                  : 'NOARCHIVELOG';
NOAUDIT                       : 'NOAUDIT';
NOCACHE                       : 'NOCACHE';
NOCOMPRESS                    : 'NOCOMPRESS';
NOCOPY                        : 'NOCOPY';
NOCPU_COSTING                 : 'NOCPU_COSTING';
NOCYCLE                       : 'NOCYCLE';
NODELAY                       : 'NODELAY';
NOENTITYESCAPING              : 'NOENTITYESCAPING';
NOEXTEND                      : 'NOEXTEND';
NOFORCE                       : 'NOFORCE';
NOGUARANTEE                   : 'NOGUARANTEE';
NOKEEP                        : 'NOKEEP';
NOLOCAL                       : 'NOLOCAL';
NOLOGGING                     : 'NOLOGGING';
NOMAPPING                     : 'NOMAPPING';
NOMAXVALUE                    : 'NOMAXVALUE';
NOMINIMIZE                    : 'NOMINIMIZE';
NOMINVALUE                    : 'NOMINVALUE';
NOMONITORING                  : 'NOMONITORING';
NONBLOCKING                   : 'NONBLOCKING';
NONE                          : 'NONE';
NONEDITIONABLE                : 'NONEDITIONABLE';
NONSCHEMA                     : 'NONSCHEMA';
NOORDER                       : 'NOORDER';
NOOVERRIDE                    : 'NOOVERRIDE';
NOPARALLEL                    : 'NOPARALLEL';
NOPARALLEL_INDEX              : 'NOPARALLEL_INDEX';
NOPROMPT                      : 'NOPROMPT';
NORELOCATE                    : 'NORELOCATE';
NORELY                        : 'NORELY';
NOREPAIR                      : 'NOREPAIR';
NOREPLY                       : 'NOREPLY';
NORESETLOGS                   : 'NORESETLOGS';
NOREVERSE                     : 'NOREVERSE';
NOREWRITE                     : 'NOREWRITE';
NORMAL                        : 'NORMAL';
NOROWDEPENDENCIES             : 'NOROWDEPENDENCIES';
NOSCALE                       : 'NOSCALE';
NOSCHEMACHECK                 : 'NOSCHEMACHECK';
NOSEGMENT                     : 'NOSEGMENT';
NOSHARD                       : 'NOSHARD';
NOSORT                        : 'NOSORT';
NOSTRICT                      : 'NOSTRICT';
NOSWITCH                      : 'NOSWITCH';
NOT                           : 'NOT';
NOTHING                       : 'NOTHING';
NOTIFICATION                  : 'NOTIFICATION';
NOVALIDATE                    : 'NOVALIDATE';
NOWAIT                        : 'NOWAIT';
NTH_VALUE                     : 'NTH_VALUE';
NTILE                         : 'NTILE';
NULL                          : 'NULL';
NULLIF                        : 'NULLIF';
NULLS                         : 'NULLS';
NUM_INDEX_KEYS                : 'NUM_INDEX_KEYS';
NUMBER                        : 'NUMBER';
NUMERIC                       : 'NUMERIC';
NUMTODSINTERVAL               : 'NUMTODSINTERVAL';
NUMTOYMINTERVAL               : 'NUMTOYMINTERVAL';
NVARCHAR2                     : 'NVARCHAR2';
NVL                           : 'NVL';
NVL2                          : 'NVL2';
OBJECT                        : 'OBJECT';
OBJECTTOXML                   : 'OBJECTTOXML';
OBJNO                         : 'OBJNO';
OBJNO_REUSE                   : 'OBJNO_REUSE';
OCCURENCES                    : 'OCCURENCES';
OF                            : 'OF';
OFF                           : 'OFF';
OFFLINE                       : 'OFFLINE';
OFFSET                        : 'OFFSET';
OID                           : 'OID';
OIDINDEX                      : 'OIDINDEX';
OLAP                          : 'OLAP';
OLD                           : 'OLD';
OLD_PUSH_PRED                 : 'OLD_PUSH_PRED';
OLS                           : 'OLS';
OLTP                          : 'OLTP';
ON                            : 'ON';
ONE                           : 'ONE';
ONLINE                        : 'ONLINE';
ONLINELOG                     : 'ONLINELOG';
ONLY                          : 'ONLY';
OPAQUE                        : 'OPAQUE';
OPAQUE_TRANSFORM              : 'OPAQUE_TRANSFORM';
OPAQUE_XCANONICAL             : 'OPAQUE_XCANONICAL';
OPCODE                        : 'OPCODE';
OPEN                          : 'OPEN';
OPERATIONS                    : 'OPERATIONS';
OPERATOR                      : 'OPERATOR';
OPT_ESTIMATE                  : 'OPT_ESTIMATE';
OPT_PARAM                     : 'OPT_PARAM';
OPTIMAL                       : 'OPTIMAL';
OPTIMIZE                      : 'OPTIMIZE';
OPTIMIZER_FEATURES_ENABLE     : 'OPTIMIZER_FEATURES_ENABLE';
OPTIMIZER_GOAL                : 'OPTIMIZER_GOAL';
OPTION                        : 'OPTION';
OPTIONALLY                    : 'OPTIONALLY';
OR                            : 'OR';
OR_EXPAND                     : 'OR_EXPAND';
OR_PREDICATES                 : 'OR_PREDICATES';
ORA_BRANCH                    : 'ORA_BRANCH';
ORA_CHECKACL                  : 'ORA_CHECKACL';
ORA_DST_AFFECTED              : 'ORA_DST_AFFECTED';
ORA_DST_CONVERT               : 'ORA_DST_CONVERT';
ORA_DST_ERROR                 : 'ORA_DST_ERROR';
ORA_GET_ACLIDS                : 'ORA_GET_ACLIDS';
ORA_GET_PRIVILEGES            : 'ORA_GET_PRIVILEGES';
ORA_HASH                      : 'ORA_HASH';
ORA_ROWSCN                    : 'ORA_ROWSCN';
ORA_ROWSCN_RAW                : 'ORA_ROWSCN_RAW';
ORA_ROWVERSION                : 'ORA_ROWVERSION';
ORA_TABVERSION                : 'ORA_TABVERSION';
ORADEBUG                      : 'ORADEBUG';
ORDER                         : 'ORDER';
ORDERED                       : 'ORDERED';
ORDERED_PREDICATES            : 'ORDERED_PREDICATES';
ORDINALITY                    : 'ORDINALITY';
ORGANIZATION                  : 'ORGANIZATION';
OTHER                         : 'OTHER';
OTHERS                        : 'OTHERS';
OUT                           : 'OUT';
OUT_OF_LINE                   : 'OUT_OF_LINE';
OUTER                         : 'OUTER';
OUTER_JOIN_TO_INNER           : 'OUTER_JOIN_TO_INNER';
OUTLINE                       : 'OUTLINE';
OUTLINE_LEAF                  : 'OUTLINE_LEAF';
OVER                          : 'OVER';
OVERFLOW                      : 'OVERFLOW';
OVERFLOW_NOMOVE               : 'OVERFLOW_NOMOVE';
OVERLAPS                      : 'OVERLAPS';
OVERRIDING                    : 'OVERRIDING';
OWN                           : 'OWN';
OWNER                         : 'OWNER';
OWNERSHIP                     : 'OWNERSHIP';
PACKAGE                       : 'PACKAGE';
PACKAGES                      : 'PACKAGES';
PAIRS                         : 'PAIRS';
PARALLEL                      : 'PARALLEL';
PARALLEL_ENABLE               : 'PARALLEL_ENABLE';
PARALLEL_INDEX                : 'PARALLEL_INDEX';
PARAM                         : 'PARAM';
PARAMETER                     : 'PARAMETER';
PARAMETERFILE                 : 'PARAMETERFILE';
PARAMETERS                    : 'PARAMETERS';
PARENT                        : 'PARENT';
PARITY                        : 'PARITY';
PARTIAL                       : 'PARTIAL';
PARTIALLY                     : 'PARTIALLY';
PARTITION                     : 'PARTITION';
PARTITION_HASH                : 'PARTITION_HASH';
PARTITION_LIST                : 'PARTITION_LIST';
PARTITION_RANGE               : 'PARTITION_RANGE';
PARTITIONS                    : 'PARTITIONS';
PARTITIONSET                  : 'PARTITIONSET';
PASSING                       : 'PASSING';
PASSWORD                      : 'PASSWORD';
PASSWORD_GRACE_TIME           : 'PASSWORD_GRACE_TIME';
PASSWORD_LIFE_TIME            : 'PASSWORD_LIFE_TIME';
PASSWORD_LOCK_TIME            : 'PASSWORD_LOCK_TIME';
PASSWORD_REUSE_MAX            : 'PASSWORD_REUSE_MAX';
PASSWORD_REUSE_TIME           : 'PASSWORD_REUSE_TIME';
PASSWORD_ROLLOVER_TIME        : 'PASSWORD_ROLLOVER_TIME';
PASSWORD_VERIFY_FUNCTION      : 'PASSWORD_VERIFY_FUNCTION';
PASSWORDFILE_METADATA_CACHE   : 'PASSWORDFILE_METADATA_CACHE';
PAST                          : 'PAST';
PATCH                         : 'PATCH';
PATH                          : 'PATH';
PATHS                         : 'PATHS';
PATTERN                       : 'PATTERN';
PBL_HS_BEGIN                  : 'PBL_HS_BEGIN';
PBL_HS_END                    : 'PBL_HS_END';
PCTFREE                       : 'PCTFREE';
PCTINCREASE                   : 'PCTINCREASE';
PCTTHRESHOLD                  : 'PCTTHRESHOLD';
PCTUSED                       : 'PCTUSED';
PCTVERSION                    : 'PCTVERSION';
PDBS                          : 'PDBS';
PENDING                       : 'PENDING';
PER                           : 'PER';
PERCENT                       : 'PERCENT';
PERCENT_RANK                  : 'PERCENT_RANK';
PERCENT_RANKM                 : 'PERCENT_RANKM';
PERCENTILE_CONT               : 'PERCENTILE_CONT';
PERCENTILE_DISC               : 'PERCENTILE_DISC';
PERFORMANCE                   : 'PERFORMANCE';
PERIOD                        : 'PERIOD';
PERMANENT                     : 'PERMANENT';
PERMISSION                    : 'PERMISSION';
PERMUTE                       : 'PERMUTE';
PERSISTABLE                   : 'PERSISTABLE';
PFILE                         : 'PFILE';
PHYSICAL                      : 'PHYSICAL';
PIKEY                         : 'PIKEY';
PIPE                          : 'PIPE';
PIPELINED                     : 'PIPELINED';
PIV_GB                        : 'PIV_GB';
PIV_SSF                       : 'PIV_SSF';
PIVOT                         : 'PIVOT';
PLACE_DISTINCT                : 'PLACE_DISTINCT';
PLACE_GROUP_BY                : 'PLACE_GROUP_BY';
PLAN                          : 'PLAN';
PLS_INTEGER                   : 'PLS_INTEGER';
PLSCOPE_SETTINGS              : 'PLSCOPE_SETTINGS';
PLSQL_CCFLAGS                 : 'PLSQL_CCFLAGS';
PLSQL_CODE_TYPE               : 'PLSQL_CODE_TYPE';
PLSQL_DEBUG                   : 'PLSQL_DEBUG';
PLSQL_OPTIMIZE_LEVEL          : 'PLSQL_OPTIMIZE_LEVEL';
PLSQL_WARNINGS                : 'PLSQL_WARNINGS';
PLUGGABLE                     : 'PLUGGABLE';
PNO                           : 'PNO';
POINT                         : 'POINT';
POLICY                        : 'POLICY';
POLYMORPHIC                   : 'POLYMORPHIC';
PORT                          : 'PORT';
POSITION                      : 'POSITION';
POSITIVE                      : 'POSITIVE';
POSITIVEN                     : 'POSITIVEN';
POST_TRANSACTION              : 'POST_TRANSACTION';
POWER                         : 'POWER';
POWERMULTISET                 : 'POWERMULTISET';
POWERMULTISET_BY_CARDINALITY  : 'POWERMULTISET_BY_CARDINALITY';
PQ_DISTRIBUTE                 : 'PQ_DISTRIBUTE';
PQ_MAP                        : 'PQ_MAP';
PQ_NOMAP                      : 'PQ_NOMAP';
PRAGMA                        : 'PRAGMA';
PREBUILT                      : 'PREBUILT';
PRECEDES                      : 'PRECEDES';
PRECEDING                     : 'PRECEDING';
PRECISION                     : 'PRECISION';
PRECOMPUTE_SUBQUERY           : 'PRECOMPUTE_SUBQUERY';
PREDICATE_REORDERS            : 'PREDICATE_REORDERS';
PREDICTION                    : 'PREDICTION';
PREDICTION_BOUNDS             : 'PREDICTION_BOUNDS';
PREDICTION_COST               : 'PREDICTION_COST';
PREDICTION_DETAILS            : 'PREDICTION_DETAILS';
PREDICTION_PROBABILITY        : 'PREDICTION_PROBABILITY';
PREDICTION_SET                : 'PREDICTION_SET';
PREPARE                       : 'PREPARE';
PRESENT                       : 'PRESENT';
PRESENTNNV                    : 'PRESENTNNV';
PRESENTV                      : 'PRESENTV';
PRESERVE                      : 'PRESERVE';
PRESERVE_OID                  : 'PRESERVE_OID';
PREV                          : 'PREV';
PREVIOUS                      : 'PREVIOUS';
PRIMARY                       : 'PRIMARY';
PRIOR                         : 'PRIOR';
PRIORITY                      : 'PRIORITY';
PRIVATE                       : 'PRIVATE';
PRIVATE_SGA                   : 'PRIVATE_SGA';
PRIVILEGE                     : 'PRIVILEGE';
PRIVILEGED                    : 'PRIVILEGED';
PRIVILEGES                    : 'PRIVILEGES';
PROCEDURAL                    : 'PROCEDURAL';
PROCEDURE                     : 'PROCEDURE';
PROCESS                       : 'PROCESS';
PROFILE                       : 'PROFILE';
PROGRAM                       : 'PROGRAM';
PROJECT                       : 'PROJECT';
PROPAGATE                     : 'PROPAGATE';
PROPERTY                      : 'PROPERTY';
PROTECTED                     : 'PROTECTED';
PROTECTION                    : 'PROTECTION';
PROXY                         : 'PROXY';
PRUNING                       : 'PRUNING';
PUBLIC                        : 'PUBLIC';
PULL_PRED                     : 'PULL_PRED';
PURGE                         : 'PURGE';
PUSH_PRED                     : 'PUSH_PRED';
PUSH_SUBQ                     : 'PUSH_SUBQ';
PX_GRANULE                    : 'PX_GRANULE';
PX_JOIN_FILTER                : 'PX_JOIN_FILTER';
QB_NAME                       : 'QB_NAME';
QUALIFY                       : 'QUALIFY';
QUARTER                       : 'QUARTER';
QUERY                         : 'QUERY';
QUERY_BLOCK                   : 'QUERY_BLOCK';
QUEUE                         : 'QUEUE';
QUEUE_CURR                    : 'QUEUE_CURR';
QUEUE_ROWP                    : 'QUEUE_ROWP';
QUIESCE                       : 'QUIESCE';
QUORUM                        : 'QUORUM';
QUOTA                         : 'QUOTA';
QUOTAGROUP                    : 'QUOTAGROUP';
RAISE                         : 'RAISE';
RANDOM                        : 'RANDOM';
RANDOM_LOCAL                  : 'RANDOM_LOCAL';
RANGE                         : 'RANGE';
RANK                          : 'RANK';
RANKM                         : 'RANKM';
RAPIDLY                       : 'RAPIDLY';
RATIO_TO_REPORT               : 'RATIO_TO_REPORT';
RAW                           : 'RAW';
RAWTOHEX                      : 'RAWTOHEX';
RAWTONHEX                     : 'RAWTONHEX';
RBA                           : 'RBA';
RBO_OUTLINE                   : 'RBO_OUTLINE';
RDBA                          : 'RDBA';
READ                          : 'READ';
READS                         : 'READS';
REAL                          : 'REAL';
REALM                         : 'REALM';
REBALANCE                     : 'REBALANCE';
REBUILD                       : 'REBUILD';
RECORD                        : 'RECORD';
RECORDS_PER_BLOCK             : 'RECORDS_PER_BLOCK';
RECOVER                       : 'RECOVER';
RECOVERABLE                   : 'RECOVERABLE';
RECOVERY                      : 'RECOVERY';
RECYC                         : 'RECYC';
RECYCLE                       : 'RECYCLE';
RECYCLEBIN                    : 'RECYCLEBIN';
REDACTION                     : 'REDACTION';
REDO                          : 'REDO';
REDUCED                       : 'REDUCED';
REDUNDANCY                    : 'REDUNDANCY';
REF                           : 'REF';
REF_CASCADE_CURSOR            : 'REF_CASCADE_CURSOR';
REFERENCE                     : 'REFERENCE';
REFERENCED                    : 'REFERENCED';
REFERENCES                    : 'REFERENCES';
REFERENCING                   : 'REFERENCING';
REFRESH                       : 'REFRESH';
REFTOHEX                      : 'REFTOHEX';
REGEXP_COUNT                  : 'REGEXP_COUNT';
REGEXP_INSTR                  : 'REGEXP_INSTR';
REGEXP_LIKE                   : 'REGEXP_LIKE';
REGEXP_REPLACE                : 'REGEXP_REPLACE';
REGEXP_SUBSTR                 : 'REGEXP_SUBSTR';
REGISTER                      : 'REGISTER';
REGR_AVGX                     : 'REGR_AVGX';
REGR_AVGY                     : 'REGR_AVGY';
REGR_COUNT                    : 'REGR_COUNT';
REGR_INTERCEPT                : 'REGR_INTERCEPT';
REGR_R2                       : 'REGR_R2';
REGR_SLOPE                    : 'REGR_SLOPE';
REGR_SXX                      : 'REGR_SXX';
REGR_SXY                      : 'REGR_SXY';
REGR_SYY                      : 'REGR_SYY';
REGULAR                       : 'REGULAR';
REJECT                        : 'REJECT';
REKEY                         : 'REKEY';
REL                           : 'REL';
RELATIONAL                    : 'RELATIONAL';
RELEASE                       : 'RELEASE';
RELIES_ON                     : 'RELIES_ON';
RELOCATE                      : 'RELOCATE';
RELY                          : 'RELY';
REMAINDER                     : 'REMAINDER';
REMOTE_MAPPED                 : 'REMOTE_MAPPED';
REMOVE                        : 'REMOVE';
RENAME                        : 'RENAME';
REP                           : 'REP';
REPAIR                        : 'REPAIR';
REPEAT                        : 'REPEAT';
REPF                          : 'REPF';
REPFOOTER                     : 'REPFOOTER';
REPH                          : 'REPH';
REPHEADER                     : 'REPHEADER';
REPLACE                       : 'REPLACE';
REPLICATION                   : 'REPLICATION';
REQUIRED                      : 'REQUIRED';
RESET                         : 'RESET';
RESETLOGS                     : 'RESETLOGS';
RESIZE                        : 'RESIZE';
RESOLVE                       : 'RESOLVE';
RESOLVER                      : 'RESOLVER';
RESOURCE                      : 'RESOURCE';
RESPECT                       : 'RESPECT';
RESTART                       : 'RESTART';
RESTORE                       : 'RESTORE';
RESTORE_AS_INTERVALS          : 'RESTORE_AS_INTERVALS';
RESTRICT                      : 'RESTRICT';
RESTRICT_ALL_REF_CONS         : 'RESTRICT_ALL_REF_CONS';
RESTRICT_REFERENCES           : 'RESTRICT_REFERENCES';
RESTRICTED                    : 'RESTRICTED';
RESULT                        : 'RESULT';
RESULT_CACHE                  : 'RESULT_CACHE';
RESUMABLE                     : 'RESUMABLE';
RESUME                        : 'RESUME';
RETENTION                     : 'RETENTION';
RETRY_ON_ROW_CHANGE           : 'RETRY_ON_ROW_CHANGE';
RETURN                        : 'RETURN';
RETURNING                     : 'RETURNING';
REUSE                         : 'REUSE';
REVERSE                       : 'REVERSE';
REVOKE                        : 'REVOKE';
REWRITE                       : 'REWRITE';
REWRITE_OR_ERROR              : 'REWRITE_OR_ERROR';
RIGHT                         : 'RIGHT';
RNDS                          : 'RNDS';
RNPS                          : 'RNPS';
ROLE                          : 'ROLE';
ROLES                         : 'ROLES';
ROLESET                       : 'ROLESET';
ROLLBACK                      : 'ROLLBACK';
ROLLING                       : 'ROLLING';
ROLLUP                        : 'ROLLUP';
ROUND                         : 'ROUND';
ROW                           : 'ROW';
ROW_LENGTH                    : 'ROW_LENGTH';
ROW_NUMBER                    : 'ROW_NUMBER';
ROWCOUNT                      : 'ROWCOUNT';
ROWDEPENDENCIES               : 'ROWDEPENDENCIES';
ROWID                         : 'ROWID';
ROWIDTOCHAR                   : 'ROWIDTOCHAR';
ROWIDTONCHAR                  : 'ROWIDTONCHAR';
ROWNUM                        : 'ROWNUM';
ROWPREF                       : 'ROWPREF';
ROWPREFETCH                   : 'ROWPREFETCH';
ROWS                          : 'ROWS';
ROWTYPE                       : 'ROWTYPE';
RPAD                          : 'RPAD';
RTRIM                         : 'RTRIM';
RULE                          : 'RULE';
RULES                         : 'RULES';
RUNNING                       : 'RUNNING';
SALT                          : 'SALT';
SAMPLE                        : 'SAMPLE';
SAVE                          : 'SAVE';
SAVE_AS_INTERVALS             : 'SAVE_AS_INTERVALS';
SAVEPOINT                     : 'SAVEPOINT';
SB4                           : 'SB4';
SCALE                         : 'SCALE';
SCALE_ROWS                    : 'SCALE_ROWS';
SCAN                          : 'SCAN';
SCAN_INSTANCES                : 'SCAN_INSTANCES';
SCHEDULER                     : 'SCHEDULER';
SCHEMA                        : 'SCHEMA';
SCHEMACHECK                   : 'SCHEMACHECK';
SCN                           : 'SCN';
SCN_ASCENDING                 : 'SCN_ASCENDING';
SCOPE                         : 'SCOPE';
SCRUB                         : 'SCRUB';
SD_ALL                        : 'SD_ALL';
SD_INHIBIT                    : 'SD_INHIBIT';
SD_SHOW                       : 'SD_SHOW';
SEARCH                        : 'SEARCH';
SECOND                        : 'SECOND';
SECUREFILE                    : 'SECUREFILE';
SECUREFILE_DBA                : 'SECUREFILE_DBA';
SECURITY                      : 'SECURITY';
SEED                          : 'SEED';
SEG_BLOCK                     : 'SEG_BLOCK';
SEG_FILE                      : 'SEG_FILE';
SEGMENT                       : 'SEGMENT';
SELECT                        : 'SELECT';
SELECTIVITY                   : 'SELECTIVITY';
SELF                          : 'SELF';
SEMIJOIN                      : 'SEMIJOIN';
SEMIJOIN_DRIVER               : 'SEMIJOIN_DRIVER';
SEQUENCE                      : 'SEQUENCE';
SEQUENCED                     : 'SEQUENCED';
SEQUENTIAL                    : 'SEQUENTIAL';
SERIALIZABLE                  : 'SERIALIZABLE';
SERIALLY_REUSABLE             : 'SERIALLY_REUSABLE';
SERVERERROR                   : 'SERVERERROR';
SERVICE                       : 'SERVICE';
SESSION                       : 'SESSION';
SESSION_CACHED_CURSORS        : 'SESSION_CACHED_CURSORS';
SESSIONS_PER_USER             : 'SESSIONS_PER_USER';
SESSIONTIMEZONE               : 'SESSIONTIMEZONE';
SESSIONTZNAME                 : 'SESSIONTZNAME';
SET                           : 'SET';
SET_TO_JOIN                   : 'SET_TO_JOIN';
SETS                          : 'SETS';
SETTINGS                      : 'SETTINGS';
SEVERE                        : 'SEVERE';
SGA                           : 'SGA';
SHARD                         : 'SHARD';
SHARDED                       : 'SHARDED';
SHARDS                        : 'SHARDS';
SHARE                         : 'SHARE';
SHARE_OF                      : 'SHARE_OF';
SHARED                        : 'SHARED';
SHARED_POOL                   : 'SHARED_POOL';
SHARING                       : 'SHARING';
SHO                           : 'SHO';
SHOW                          : 'SHOW';
SHRINK                        : 'SHRINK';
SHUTDOWN                      : 'SHUTDOWN';
SIBLINGS                      : 'SIBLINGS';
SID                           : 'SID';
SIGN                          : 'SIGN';
SIGNAL_COMPONENT              : 'SIGNAL_COMPONENT';
SIGNAL_FUNCTION               : 'SIGNAL_FUNCTION';
SIGNTYPE                      : 'SIGNTYPE';
SIMPLE                        : 'SIMPLE';
SIMPLE_INTEGER                : 'SIMPLE_INTEGER';
SIN                           : 'SIN';
SINGLE                        : 'SINGLE';
SINGLE_C                      : 'C';
SINGLETASK                    : 'SINGLETASK';
SINH                          : 'SINH';
SITE                          : 'SITE';
SIZE                          : 'SIZE';
SKIP_EXT_OPTIMIZER            : 'SKIP_EXT_OPTIMIZER';
SKIP_SYMBOL                   : 'SKIP';
SKIP_UNQ_UNUSABLE_IDX         : 'SKIP_UNQ_UNUSABLE_IDX';
SKIP_UNUSABLE_INDEXES         : 'SKIP_UNUSABLE_INDEXES';
SMALLFILE                     : 'SMALLFILE';
SMALLINT                      : 'SMALLINT';
SNAPSHOT                      : 'SNAPSHOT';
SOME                          : 'SOME';
SORT                          : 'SORT';
SOUNDEX                       : 'SOUNDEX';
SOURCE                        : 'SOURCE';
SPACE                         : 'SPACE';
SPECIFICATION                 : 'SPECIFICATION';
SPFILE                        : 'SPFILE';
SPLIT                         : 'SPLIT';
SPO                           : 'SPO';
SPOO                          : 'SPOO';
SPOOL                         : 'SPOOL';
SPPARAMETER                   : 'SPPARAMETER';
SPPARAMETERS                  : 'SPPARAMETERS';
SPREADSHEET                   : 'SPREADSHEET';
SQL                           : 'SQL';
SQL_MARCO                     : 'SQL_MARCO';
SQL_TRACE                     : 'SQL_TRACE';
SQLCODE                       : 'SQLCODE';
SQLLDR                        : 'SQLLDR';
SQRT                          : 'SQRT';
STALE                         : 'STALE';
STANDALONE                    : 'STANDALONE';
STANDBY                       : 'STANDBY';
STANDBY_MAX_DATA_DELAY        : 'STANDBY_MAX_DATA_DELAY';
STAR                          : 'STAR';
STAR_TRANSFORMATION           : 'STAR_TRANSFORMATION';
START                         : 'START';
STARTS                        : 'STARTS';
STARTUP                       : 'STARTUP';
STATE                         : 'STATE';
STATEMENT                     : 'STATEMENT';
STATEMENT_ID                  : 'STATEMENT_ID';
STATEMENT_QUEUING             : 'STATEMENT_QUEUING';
STATEMENTC                    : 'STATEMENTC';
STATEMENTCACHE                : 'STATEMENTCACHE';
STATEMENTS                    : 'STATEMENTS';
STATIC                        : 'STATIC';
STATISTICS                    : 'STATISTICS';
STATS_BINOMIAL_TEST           : 'STATS_BINOMIAL_TEST';
STATS_CROSSTAB                : 'STATS_CROSSTAB';
STATS_F_TEST                  : 'STATS_F_TEST';
STATS_KS_TEST                 : 'STATS_KS_TEST';
STATS_MODE                    : 'STATS_MODE';
STATS_MW_TEST                 : 'STATS_MW_TEST';
STATS_ONE_WAY_ANOVA           : 'STATS_ONE_WAY_ANOVA';
STATS_T_TEST_INDEP            : 'STATS_T_TEST_INDEP';
STATS_T_TEST_INDEPU           : 'STATS_T_TEST_INDEPU';
STATS_T_TEST_ONE              : 'STATS_T_TEST_ONE';
STATS_T_TEST_PAIRED           : 'STATS_T_TEST_PAIRED';
STATS_WSR_TEST                : 'STATS_WSR_TEST';
STDDEV                        : 'STDDEV';
STDDEV_POP                    : 'STDDEV_POP';
STDDEV_SAMP                   : 'STDDEV_SAMP';
STOP                          : 'STOP';
STORAGE                       : 'STORAGE';
STORE                         : 'STORE';
STREAMS                       : 'STREAMS';
STRICT                        : 'STRICT';
STRING                        : 'STRING';
STRIP                         : 'STRIP';
STRIPE_COLUMNS                : 'STRIPE_COLUMNS';
STRIPE_WIDTH                  : 'STRIPE_WIDTH';
STRUCT                        : 'STRUCT';
STRUCTURE                     : 'STRUCTURE';
SUBMULTISET                   : 'SUBMULTISET';
SUBPARTITION                  : 'SUBPARTITION';
SUBPARTITION_REL              : 'SUBPARTITION_REL';
SUBPARTITIONS                 : 'SUBPARTITIONS';
SUBQUERIES                    : 'SUBQUERIES';
SUBQUERY_PRUNING              : 'SUBQUERY_PRUNING';
SUBSCRIBE                     : 'SUBSCRIBE';
SUBSET                        : 'SUBSET';
SUBSTITUTABLE                 : 'SUBSTITUTABLE';
SUBSTR                        : 'SUBSTR';
SUBSTR2                       : 'SUBSTR2';
SUBSTR4                       : 'SUBSTR4';
SUBSTRB                       : 'SUBSTRB';
SUBSTRC                       : 'SUBSTRC';
SUBSTRING                     : 'SUBSTRING';
SUBTYPE                       : 'SUBTYPE';
SUCCESS                       : 'SUCCESS';
SUCCESSFUL                    : 'SUCCESSFUL';
SUM                           : 'SUM';
SUMMARY                       : 'SUMMARY';
SUPPLEMENTAL                  : 'SUPPLEMENTAL';
SUPPRESSES_WARNING_6009       : 'SUPPRESSES_WARNING_6009';
SUSPEND                       : 'SUSPEND';
SWAP_JOIN_INPUTS              : 'SWAP_JOIN_INPUTS';
SWITCH                        : 'SWITCH';
SWITCHOVER                    : 'SWITCHOVER';
SYNC                          : 'SYNC';
SYNCHRONOUS                   : 'SYNCHRONOUS';
SYNONYM                       : 'SYNONYM';
SYS                           : 'SYS';
SYS_AUDIT                     : 'SYS_AUDIT';
SYS_CHECKACL                  : 'SYS_CHECKACL';
SYS_CONNECT_BY_PATH           : 'SYS_CONNECT_BY_PATH';
SYS_CONTEXT                   : 'SYS_CONTEXT';
SYS_DBURIGEN                  : 'SYS_DBURIGEN';
SYS_DL_CURSOR                 : 'SYS_DL_CURSOR';
SYS_DM_RXFORM_CHR             : 'SYS_DM_RXFORM_CHR';
SYS_DM_RXFORM_NUM             : 'SYS_DM_RXFORM_NUM';
SYS_DOM_COMPARE               : 'SYS_DOM_COMPARE';
SYS_DST_PRIM2SEC              : 'SYS_DST_PRIM2SEC';
SYS_DST_SEC2PRIM              : 'SYS_DST_SEC2PRIM';
SYS_ET_BFILE_TO_RAW           : 'SYS_ET_BFILE_TO_RAW';
SYS_ET_BLOB_TO_IMAGE          : 'SYS_ET_BLOB_TO_IMAGE';
SYS_ET_IMAGE_TO_BLOB          : 'SYS_ET_IMAGE_TO_BLOB';
SYS_ET_RAW_TO_BFILE           : 'SYS_ET_RAW_TO_BFILE';
SYS_EXTPDTXT                  : 'SYS_EXTPDTXT';
SYS_EXTRACT_UTC               : 'SYS_EXTRACT_UTC';
SYS_FBT_INSDEL                : 'SYS_FBT_INSDEL';
SYS_FILTER_ACLS               : 'SYS_FILTER_ACLS';
SYS_GET_ACLIDS                : 'SYS_GET_ACLIDS';
SYS_GET_PRIVILEGES            : 'SYS_GET_PRIVILEGES';
SYS_GETTOKENID                : 'SYS_GETTOKENID';
SYS_GUID                      : 'SYS_GUID';
SYS_MAKE_XMLNODEID            : 'SYS_MAKE_XMLNODEID';
SYS_MAKEXML                   : 'SYS_MAKEXML';
SYS_MKXMLATTR                 : 'SYS_MKXMLATTR';
SYS_OP_ADT2BIN                : 'SYS_OP_ADT2BIN';
SYS_OP_ADTCONS                : 'SYS_OP_ADTCONS';
SYS_OP_ALSCRVAL               : 'SYS_OP_ALSCRVAL';
SYS_OP_ATG                    : 'SYS_OP_ATG';
SYS_OP_BIN2ADT                : 'SYS_OP_BIN2ADT';
SYS_OP_BITVEC                 : 'SYS_OP_BITVEC';
SYS_OP_BL2R                   : 'SYS_OP_BL2R';
SYS_OP_BLOOM_FILTER           : 'SYS_OP_BLOOM_FILTER';
SYS_OP_BLOOM_FILTER_LIST      : 'SYS_OP_BLOOM_FILTER_LIST';
SYS_OP_C2C                    : 'SYS_OP_C2C';
SYS_OP_CAST                   : 'SYS_OP_CAST';
SYS_OP_CEG                    : 'SYS_OP_CEG';
SYS_OP_CL2C                   : 'SYS_OP_CL2C';
SYS_OP_COMBINED_HASH          : 'SYS_OP_COMBINED_HASH';
SYS_OP_COMP                   : 'SYS_OP_COMP';
SYS_OP_CONVERT                : 'SYS_OP_CONVERT';
SYS_OP_COUNTCHG               : 'SYS_OP_COUNTCHG';
SYS_OP_CSCONV                 : 'SYS_OP_CSCONV';
SYS_OP_CSCONVTEST             : 'SYS_OP_CSCONVTEST';
SYS_OP_CSR                    : 'SYS_OP_CSR';
SYS_OP_CSX_PATCH              : 'SYS_OP_CSX_PATCH';
SYS_OP_DECOMP                 : 'SYS_OP_DECOMP';
SYS_OP_DESCEND                : 'SYS_OP_DESCEND';
SYS_OP_DISTINCT               : 'SYS_OP_DISTINCT';
SYS_OP_DRA                    : 'SYS_OP_DRA';
SYS_OP_DUMP                   : 'SYS_OP_DUMP';
SYS_OP_EXTRACT                : 'SYS_OP_EXTRACT';
SYS_OP_GROUPING               : 'SYS_OP_GROUPING';
SYS_OP_GUID                   : 'SYS_OP_GUID';
SYS_OP_IIX                    : 'SYS_OP_IIX';
SYS_OP_ITR                    : 'SYS_OP_ITR';
SYS_OP_LBID                   : 'SYS_OP_LBID';
SYS_OP_LOBLOC2BLOB            : 'SYS_OP_LOBLOC2BLOB';
SYS_OP_LOBLOC2CLOB            : 'SYS_OP_LOBLOC2CLOB';
SYS_OP_LOBLOC2ID              : 'SYS_OP_LOBLOC2ID';
SYS_OP_LOBLOC2NCLOB           : 'SYS_OP_LOBLOC2NCLOB';
SYS_OP_LOBLOC2TYP             : 'SYS_OP_LOBLOC2TYP';
SYS_OP_LSVI                   : 'SYS_OP_LSVI';
SYS_OP_LVL                    : 'SYS_OP_LVL';
SYS_OP_MAKEOID                : 'SYS_OP_MAKEOID';
SYS_OP_MAP_NONNULL            : 'SYS_OP_MAP_NONNULL';
SYS_OP_MSR                    : 'SYS_OP_MSR';
SYS_OP_NICOMBINE              : 'SYS_OP_NICOMBINE';
SYS_OP_NIEXTRACT              : 'SYS_OP_NIEXTRACT';
SYS_OP_NII                    : 'SYS_OP_NII';
SYS_OP_NIX                    : 'SYS_OP_NIX';
SYS_OP_NOEXPAND               : 'SYS_OP_NOEXPAND';
SYS_OP_NUMTORAW               : 'SYS_OP_NUMTORAW';
SYS_OP_OIDVALUE               : 'SYS_OP_OIDVALUE';
SYS_OP_OPNSIZE                : 'SYS_OP_OPNSIZE';
SYS_OP_PAR                    : 'SYS_OP_PAR';
SYS_OP_PAR_1                  : 'SYS_OP_PAR_1';
SYS_OP_PARGID                 : 'SYS_OP_PARGID';
SYS_OP_PARGID_1               : 'SYS_OP_PARGID_1';
SYS_OP_PIVOT                  : 'SYS_OP_PIVOT';
SYS_OP_R2O                    : 'SYS_OP_R2O';
SYS_OP_RAWTONUM               : 'SYS_OP_RAWTONUM';
SYS_OP_RDTM                   : 'SYS_OP_RDTM';
SYS_OP_REF                    : 'SYS_OP_REF';
SYS_OP_RMTD                   : 'SYS_OP_RMTD';
SYS_OP_ROWIDTOOBJ             : 'SYS_OP_ROWIDTOOBJ';
SYS_OP_RPB                    : 'SYS_OP_RPB';
SYS_OP_TOSETID                : 'SYS_OP_TOSETID';
SYS_OP_TPR                    : 'SYS_OP_TPR';
SYS_OP_TRTB                   : 'SYS_OP_TRTB';
SYS_OP_UNDESCEND              : 'SYS_OP_UNDESCEND';
SYS_OP_VECAND                 : 'SYS_OP_VECAND';
SYS_OP_VECBIT                 : 'SYS_OP_VECBIT';
SYS_OP_VECOR                  : 'SYS_OP_VECOR';
SYS_OP_VECXOR                 : 'SYS_OP_VECXOR';
SYS_OP_VERSION                : 'SYS_OP_VERSION';
SYS_OP_VREF                   : 'SYS_OP_VREF';
SYS_OP_VVD                    : 'SYS_OP_VVD';
SYS_OP_XPTHATG                : 'SYS_OP_XPTHATG';
SYS_OP_XPTHIDX                : 'SYS_OP_XPTHIDX';
SYS_OP_XPTHOP                 : 'SYS_OP_XPTHOP';
SYS_OP_XTXT2SQLT              : 'SYS_OP_XTXT2SQLT';
SYS_OPTLOBPRBSC               : 'SYS_OPTLOBPRBSC';
SYS_OPTXICMP                  : 'SYS_OPTXICMP';
SYS_OPTXQCASTASNQ             : 'SYS_OPTXQCASTASNQ';
SYS_ORDERKEY_DEPTH            : 'SYS_ORDERKEY_DEPTH';
SYS_ORDERKEY_MAXCHILD         : 'SYS_ORDERKEY_MAXCHILD';
SYS_ORDERKEY_PARENT           : 'SYS_ORDERKEY_PARENT';
SYS_PARALLEL_TXN              : 'SYS_PARALLEL_TXN';
SYS_PATH_REVERSE              : 'SYS_PATH_REVERSE';
SYS_PATHID_IS_ATTR            : 'SYS_PATHID_IS_ATTR';
SYS_PATHID_IS_NMSPC           : 'SYS_PATHID_IS_NMSPC';
SYS_PATHID_LASTNAME           : 'SYS_PATHID_LASTNAME';
SYS_PATHID_LASTNMSPC          : 'SYS_PATHID_LASTNMSPC';
SYS_PXQEXTRACT                : 'SYS_PXQEXTRACT';
SYS_RID_ORDER                 : 'SYS_RID_ORDER';
SYS_ROW_DELTA                 : 'SYS_ROW_DELTA';
SYS_SC_2_XMLT                 : 'SYS_SC_2_XMLT';
SYS_SYNRCIREDO                : 'SYS_SYNRCIREDO';
SYS_TYPEID                    : 'SYS_TYPEID';
SYS_UMAKEXML                  : 'SYS_UMAKEXML';
SYS_XMLAGG                    : 'SYS_XMLAGG';
SYS_XMLANALYZE                : 'SYS_XMLANALYZE';
SYS_XMLCONTAINS               : 'SYS_XMLCONTAINS';
SYS_XMLCONV                   : 'SYS_XMLCONV';
SYS_XMLEXNSURI                : 'SYS_XMLEXNSURI';
SYS_XMLGEN                    : 'SYS_XMLGEN';
SYS_XMLI_LOC_ISNODE           : 'SYS_XMLI_LOC_ISNODE';
SYS_XMLI_LOC_ISTEXT           : 'SYS_XMLI_LOC_ISTEXT';
SYS_XMLLOCATOR_GETSVAL        : 'SYS_XMLLOCATOR_GETSVAL';
SYS_XMLNODEID                 : 'SYS_XMLNODEID';
SYS_XMLNODEID_GETCID          : 'SYS_XMLNODEID_GETCID';
SYS_XMLNODEID_GETLOCATOR      : 'SYS_XMLNODEID_GETLOCATOR';
SYS_XMLNODEID_GETOKEY         : 'SYS_XMLNODEID_GETOKEY';
SYS_XMLNODEID_GETPATHID       : 'SYS_XMLNODEID_GETPATHID';
SYS_XMLNODEID_GETPTRID        : 'SYS_XMLNODEID_GETPTRID';
SYS_XMLNODEID_GETRID          : 'SYS_XMLNODEID_GETRID';
SYS_XMLNODEID_GETSVAL         : 'SYS_XMLNODEID_GETSVAL';
SYS_XMLNODEID_GETTID          : 'SYS_XMLNODEID_GETTID';
SYS_XMLT_2_SC                 : 'SYS_XMLT_2_SC';
SYS_XMLTRANSLATE              : 'SYS_XMLTRANSLATE';
SYS_XMLTYPE2SQL               : 'SYS_XMLTYPE2SQL';
SYS_XQ_ASQLCNV                : 'SYS_XQ_ASQLCNV';
SYS_XQ_ATOMCNVCHK             : 'SYS_XQ_ATOMCNVCHK';
SYS_XQ_NRNG                   : 'SYS_XQ_NRNG';
SYS_XQ_PKSQL2XML              : 'SYS_XQ_PKSQL2XML';
SYS_XQ_UPKXML2SQL             : 'SYS_XQ_UPKXML2SQL';
SYS_XQBASEURI                 : 'SYS_XQBASEURI';
SYS_XQCASTABLEERRH            : 'SYS_XQCASTABLEERRH';
SYS_XQCODEP2STR               : 'SYS_XQCODEP2STR';
SYS_XQCODEPEQ                 : 'SYS_XQCODEPEQ';
SYS_XQCON2SEQ                 : 'SYS_XQCON2SEQ';
SYS_XQCONCAT                  : 'SYS_XQCONCAT';
SYS_XQDELETE                  : 'SYS_XQDELETE';
SYS_XQDFLTCOLATION            : 'SYS_XQDFLTCOLATION';
SYS_XQDOC                     : 'SYS_XQDOC';
SYS_XQDOCURI                  : 'SYS_XQDOCURI';
SYS_XQED4URI                  : 'SYS_XQED4URI';
SYS_XQENDSWITH                : 'SYS_XQENDSWITH';
SYS_XQERR                     : 'SYS_XQERR';
SYS_XQERRH                    : 'SYS_XQERRH';
SYS_XQESHTMLURI               : 'SYS_XQESHTMLURI';
SYS_XQEXLOBVAL                : 'SYS_XQEXLOBVAL';
SYS_XQEXSTWRP                 : 'SYS_XQEXSTWRP';
SYS_XQEXTRACT                 : 'SYS_XQEXTRACT';
SYS_XQEXTRREF                 : 'SYS_XQEXTRREF';
SYS_XQEXVAL                   : 'SYS_XQEXVAL';
SYS_XQFB2STR                  : 'SYS_XQFB2STR';
SYS_XQFNBOOL                  : 'SYS_XQFNBOOL';
SYS_XQFNCMP                   : 'SYS_XQFNCMP';
SYS_XQFNDATIM                 : 'SYS_XQFNDATIM';
SYS_XQFNLNAME                 : 'SYS_XQFNLNAME';
SYS_XQFNNM                    : 'SYS_XQFNNM';
SYS_XQFNNSURI                 : 'SYS_XQFNNSURI';
SYS_XQFNPREDTRUTH             : 'SYS_XQFNPREDTRUTH';
SYS_XQFNQNM                   : 'SYS_XQFNQNM';
SYS_XQFNROOT                  : 'SYS_XQFNROOT';
SYS_XQFORMATNUM               : 'SYS_XQFORMATNUM';
SYS_XQFTCONTAIN               : 'SYS_XQFTCONTAIN';
SYS_XQFUNCR                   : 'SYS_XQFUNCR';
SYS_XQGETCONTENT              : 'SYS_XQGETCONTENT';
SYS_XQINDXOF                  : 'SYS_XQINDXOF';
SYS_XQINSERT                  : 'SYS_XQINSERT';
SYS_XQINSPFX                  : 'SYS_XQINSPFX';
SYS_XQIRI2URI                 : 'SYS_XQIRI2URI';
SYS_XQLANG                    : 'SYS_XQLANG';
SYS_XQLLNMFRMQNM              : 'SYS_XQLLNMFRMQNM';
SYS_XQMKNODEREF               : 'SYS_XQMKNODEREF';
SYS_XQNILLED                  : 'SYS_XQNILLED';
SYS_XQNODENAME                : 'SYS_XQNODENAME';
SYS_XQNORMSPACE               : 'SYS_XQNORMSPACE';
SYS_XQNORMUCODE               : 'SYS_XQNORMUCODE';
SYS_XQNSP4PFX                 : 'SYS_XQNSP4PFX';
SYS_XQNSPFRMQNM               : 'SYS_XQNSPFRMQNM';
SYS_XQPFXFRMQNM               : 'SYS_XQPFXFRMQNM';
SYS_XQPOLYABS                 : 'SYS_XQPOLYABS';
SYS_XQPOLYADD                 : 'SYS_XQPOLYADD';
SYS_XQPOLYCEL                 : 'SYS_XQPOLYCEL';
SYS_XQPOLYCST                 : 'SYS_XQPOLYCST';
SYS_XQPOLYCSTBL               : 'SYS_XQPOLYCSTBL';
SYS_XQPOLYDIV                 : 'SYS_XQPOLYDIV';
SYS_XQPOLYFLR                 : 'SYS_XQPOLYFLR';
SYS_XQPOLYMOD                 : 'SYS_XQPOLYMOD';
SYS_XQPOLYMUL                 : 'SYS_XQPOLYMUL';
SYS_XQPOLYRND                 : 'SYS_XQPOLYRND';
SYS_XQPOLYSQRT                : 'SYS_XQPOLYSQRT';
SYS_XQPOLYSUB                 : 'SYS_XQPOLYSUB';
SYS_XQPOLYUMUS                : 'SYS_XQPOLYUMUS';
SYS_XQPOLYUPLS                : 'SYS_XQPOLYUPLS';
SYS_XQPOLYVEQ                 : 'SYS_XQPOLYVEQ';
SYS_XQPOLYVGE                 : 'SYS_XQPOLYVGE';
SYS_XQPOLYVGT                 : 'SYS_XQPOLYVGT';
SYS_XQPOLYVLE                 : 'SYS_XQPOLYVLE';
SYS_XQPOLYVLT                 : 'SYS_XQPOLYVLT';
SYS_XQPOLYVNE                 : 'SYS_XQPOLYVNE';
SYS_XQREF2VAL                 : 'SYS_XQREF2VAL';
SYS_XQRENAME                  : 'SYS_XQRENAME';
SYS_XQREPLACE                 : 'SYS_XQREPLACE';
SYS_XQRESVURI                 : 'SYS_XQRESVURI';
SYS_XQRNDHALF2EVN             : 'SYS_XQRNDHALF2EVN';
SYS_XQRSLVQNM                 : 'SYS_XQRSLVQNM';
SYS_XQRYENVPGET               : 'SYS_XQRYENVPGET';
SYS_XQRYVARGET                : 'SYS_XQRYVARGET';
SYS_XQRYWRP                   : 'SYS_XQRYWRP';
SYS_XQSEQ2CON                 : 'SYS_XQSEQ2CON';
SYS_XQSEQ2CON4XC              : 'SYS_XQSEQ2CON4XC';
SYS_XQSEQDEEPEQ               : 'SYS_XQSEQDEEPEQ';
SYS_XQSEQINSB                 : 'SYS_XQSEQINSB';
SYS_XQSEQRM                   : 'SYS_XQSEQRM';
SYS_XQSEQRVS                  : 'SYS_XQSEQRVS';
SYS_XQSEQSUB                  : 'SYS_XQSEQSUB';
SYS_XQSEQTYPMATCH             : 'SYS_XQSEQTYPMATCH';
SYS_XQSTARTSWITH              : 'SYS_XQSTARTSWITH';
SYS_XQSTATBURI                : 'SYS_XQSTATBURI';
SYS_XQSTR2CODEP               : 'SYS_XQSTR2CODEP';
SYS_XQSTRJOIN                 : 'SYS_XQSTRJOIN';
SYS_XQSUBSTRAFT               : 'SYS_XQSUBSTRAFT';
SYS_XQSUBSTRBEF               : 'SYS_XQSUBSTRBEF';
SYS_XQTOKENIZE                : 'SYS_XQTOKENIZE';
SYS_XQTREATAS                 : 'SYS_XQTREATAS';
SYS_XQXFORM                   : 'SYS_XQXFORM';
SYSASM                        : 'SYSASM';
SYSAUX                        : 'SYSAUX';
SYSBACKUP                     : 'SYSBACKUP';
SYSDATE                       : 'SYSDATE';
SYSDBA                        : 'SYSDBA';
SYSDG                         : 'SYSDG';
SYSGUID                       : 'SYSGUID';
SYSKM                         : 'SYSKM';
SYSOPER                       : 'SYSOPER';
SYSTEM                        : 'SYSTEM';
SYSTEM_DEFINED                : 'SYSTEM_DEFINED';
SYSTIMESTAMP                  : 'SYSTIMESTAMP';
TABLE                         : 'TABLE';
TABLE_STATS                   : 'TABLE_STATS';
TABLES                        : 'TABLES';
TABLESPACE                    : 'TABLESPACE';
TABLESPACE_NO                 : 'TABLESPACE_NO';
TABNO                         : 'TABNO';
TAN                           : 'TAN';
TANH                          : 'TANH';
TARGET                        : 'TARGET';
TDO                           : 'TDO';
TEMP_TABLE                    : 'TEMP_TABLE';
TEMPFILE                      : 'TEMPFILE';
TEMPLATE                      : 'TEMPLATE';
TEMPORARY                     : 'TEMPORARY';
TERMINATED                    : 'TERMINATED';
TEST                          : 'TEST';
THAN                          : 'THAN';
THE                           : 'THE';
THEN                          : 'THEN';
THREAD                        : 'THREAD';
THROUGH                       : 'THROUGH';
TIER                          : 'TIER';
TIES                          : 'TIES';
TIME                          : 'TIME';
TIME_UNIT                     : 'TIME_UNIT';
TIME_ZONE                     : 'TIME_ZONE';
TIMEOUT                       : 'TIMEOUT';
TIMES                         : 'TIMES';
TIMESTAMP                     : 'TIMESTAMP';
TIMEZONE_ABBR                 : 'TIMEZONE_ABBR';
TIMEZONE_HOUR                 : 'TIMEZONE_HOUR';
TIMEZONE_MINUTE               : 'TIMEZONE_MINUTE';
TIMEZONE_OFFSET               : 'TIMEZONE_OFFSET';
TIMEZONE_REGION               : 'TIMEZONE_REGION';
TIV_GB                        : 'TIV_GB';
TIV_SSF                       : 'TIV_SSF';
TLE                           : 'TLE';
TO                            : 'TO';
TO_BINARY_DOUBLE              : 'TO_BINARY_DOUBLE';
TO_BINARY_FLOAT               : 'TO_BINARY_FLOAT';
TO_BLOB                       : 'TO_BLOB';
TO_CHAR                       : 'TO_CHAR';
TO_CLOB                       : 'TO_CLOB';
TO_DATE                       : 'TO_DATE';
TO_DSINTERVAL                 : 'TO_DSINTERVAL';
TO_LOB                        : 'TO_LOB';
TO_MULTI_BYTE                 : 'TO_MULTI_BYTE';
TO_NCHAR                      : 'TO_NCHAR';
TO_NCLOB                      : 'TO_NCLOB';
TO_NUMBER                     : 'TO_NUMBER';
TO_SINGLE_BYTE                : 'TO_SINGLE_BYTE';
TO_TIME                       : 'TO_TIME';
TO_TIME_TZ                    : 'TO_TIME_TZ';
TO_TIMESTAMP                  : 'TO_TIMESTAMP';
TO_TIMESTAMP_TZ               : 'TO_TIMESTAMP_TZ';
TO_YMINTERVAL                 : 'TO_YMINTERVAL';
TOPLEVEL                      : 'TOPLEVEL';
TRACE                         : 'TRACE';
TRACING                       : 'TRACING';
TRACKING                      : 'TRACKING';
TRAILING                      : 'TRAILING';
TRANSACTION                   : 'TRANSACTION';
TRANSFORM_DISTINCT_AGG        : 'TRANSFORM_DISTINCT_AGG';
TRANSITION                    : 'TRANSITION';
TRANSITIONAL                  : 'TRANSITIONAL';
TRANSLATE                     : 'TRANSLATE';
TRANSLATION                   : 'TRANSLATION';
TREAT                         : 'TREAT';
TRIGGER                       : 'TRIGGER';
TRIGGERS                      : 'TRIGGERS';
TRIM                          : 'TRIM';
TRUE                          : 'TRUE';
TRUNC                         : 'TRUNC';
TRUNCATE                      : 'TRUNCATE';
TRUST                         : 'TRUST';
TRUSTED                       : 'TRUSTED';
TTI                           : 'TTI';
TUNING                        : 'TUNING';
TX                            : 'TX';
TYPE                          : 'TYPE';
TYPES                         : 'TYPES';
TZ_OFFSET                     : 'TZ_OFFSET';
UB2                           : 'UB2';
UBA                           : 'UBA';
UDF                           : 'UDF';
UID                           : 'UID';
UNARCHIVED                    : 'UNARCHIVED';
UNBOUND                       : 'UNBOUND';
UNBOUNDED                     : 'UNBOUNDED';
UNDER                         : 'UNDER';
UNDER_PATH                    : 'UNDER_PATH';
UNDO                          : 'UNDO';
UNDROP                        : 'UNDROP';
UNIFORM                       : 'UNIFORM';
UNINSTALL                     : 'UNINSTALL';
UNION                         : 'UNION';
UNIQUE                        : 'UNIQUE';
UNISTR                        : 'UNISTR';
UNKNOWN                       : 'UNKNOWN';
UNLIMITED                     : 'UNLIMITED';
UNLOCK                        : 'UNLOCK';
UNNEST                        : 'UNNEST';
UNPACKED                      : 'UNPACKED';
UNPIVOT                       : 'UNPIVOT';
UNPLUG                        : 'UNPLUG';
UNPROTECTED                   : 'UNPROTECTED';
UNQUIESCE                     : 'UNQUIESCE';
UNRECOVERABLE                 : 'UNRECOVERABLE';
UNRESTRICTED                  : 'UNRESTRICTED';
UNSUBSCRIBE                   : 'UNSUBSCRIBE';
UNTIL                         : 'UNTIL';
UNUSABLE                      : 'UNUSABLE';
UNUSED                        : 'UNUSED';
UPD_INDEXES                   : 'UPD_INDEXES';
UPD_JOININDEX                 : 'UPD_JOININDEX';
UPDATABLE                     : 'UPDATABLE';
UPDATE                        : 'UPDATE';
UPDATED                       : 'UPDATED';
UPDATEXML                     : 'UPDATEXML';
UPDATING                      : 'UPDATING';
UPGRADE                       : 'UPGRADE';
UPPER                         : 'UPPER';
UPSERT                        : 'UPSERT';
UROWID                        : 'UROWID';
USABLE                        : 'USABLE';
USAGE                         : 'USAGE';
USE                           : 'USE';
USE_ANTI                      : 'USE_ANTI';
USE_CONCAT                    : 'USE_CONCAT';
USE_HASH                      : 'USE_HASH';
USE_HASH_AGGREGATION          : 'USE_HASH_AGGREGATION';
USE_INVISIBLE_INDEXES         : 'USE_INVISIBLE_INDEXES';
USE_MERGE                     : 'USE_MERGE';
USE_MERGE_CARTESIAN           : 'USE_MERGE_CARTESIAN';
USE_NL                        : 'USE_NL';
USE_NL_WITH_INDEX             : 'USE_NL_WITH_INDEX';
USE_PRIVATE_OUTLINES          : 'USE_PRIVATE_OUTLINES';
USE_SEMI                      : 'USE_SEMI';
USE_STORED_OUTLINES           : 'USE_STORED_OUTLINES';
USE_TTT_FOR_GSETS             : 'USE_TTT_FOR_GSETS';
USE_WEAK_NAME_RESL            : 'USE_WEAK_NAME_RESL';
USER                          : 'USER';
USER_DATA                     : 'USER_DATA';
USER_DEFINED                  : 'USER_DEFINED';
USER_RECYCLEBIN               : 'USER_RECYCLEBIN';
USERENV                       : 'USERENV';
USERGROUP                     : 'USERGROUP';
USERS                         : 'USERS';
USING                         : 'USING';
USING_NLS_COMP                : 'USING_NLS_COMP';
UTF8                          : 'UTF8';
VALIDATE                      : 'VALIDATE';
VALIDATION                    : 'VALIDATION';
VALUE                         : 'VALUE';
VALUES                        : 'VALUES';
VAR_POP                       : 'VAR_POP';
VAR_SAMP                      : 'VAR_SAMP';
VARCHAR                       : 'VARCHAR';
VARCHAR2                      : 'VARCHAR2';
VARIANCE                      : 'VARIANCE';
VARRAY                        : 'VARRAY';
VARRAYS                       : 'VARRAYS';
VARYING                       : 'VARYING';
VECTOR_READ                   : 'VECTOR_READ';
VECTOR_READ_TRACE             : 'VECTOR_READ_TRACE';
VERIFIER                      : 'VERIFIER';
VERIFY                        : 'VERIFY';
VERSION                       : 'VERSION';
VERSIONING                    : 'VERSIONING';
VERSIONS                      : 'VERSIONS';
VERSIONS_ENDSCN               : 'VERSIONS_ENDSCN';
VERSIONS_ENDTIME              : 'VERSIONS_ENDTIME';
VERSIONS_OPERATION            : 'VERSIONS_OPERATION';
VERSIONS_STARTSCN             : 'VERSIONS_STARTSCN';
VERSIONS_STARTTIME            : 'VERSIONS_STARTTIME';
VERSIONS_XID                  : 'VERSIONS_XID';
VIEW                          : 'VIEW';
VIOLATION                     : 'VIOLATION';
VIRTUAL                       : 'VIRTUAL';
VISIBILITY                    : 'VISIBILITY';
VISIBLE                       : 'VISIBLE';
VOLUME                        : 'VOLUME';
VSIZE                         : 'VSIZE';
WAIT                          : 'WAIT';
WALLET                        : 'WALLET';
WEEK                          : 'WEEK';
WELLFORMED                    : 'WELLFORMED';
WHEN                          : 'WHEN';
WHENEVER                      : 'WHENEVER';
WHERE                         : 'WHERE';
WHILE                         : 'WHILE';
WHITESPACE                    : 'WHITESPACE';
WIDTH_BUCKET                  : 'WIDTH_BUCKET';
WITH                          : 'WITH';
WITHIN                        : 'WITHIN';
WITHOUT                       : 'WITHOUT';
WM_CONCAT                     : 'WM_CONCAT';
WNDS                          : 'WNDS';
WNPS                          : 'WNPS';
WORK                          : 'WORK';
WRAPPED                       : 'WRAPPED';
WRITE                         : 'WRITE';
X_DYN_PRUNE                   : 'X_DYN_PRUNE';
XDB                           : 'XDB';
XID                           : 'XID';
XML                           : 'XML';
XML_DML_RWT_STMT              : 'XML_DML_RWT_STMT';
XMLAGG                        : 'XMLAGG';
XMLATTRIBUTES                 : 'XMLATTRIBUTES';
XMLCAST                       : 'XMLCAST';
XMLCDATA                      : 'XMLCDATA';
XMLCOLATTVAL                  : 'XMLCOLATTVAL';
XMLCOMMENT                    : 'XMLCOMMENT';
XMLCONCAT                     : 'XMLCONCAT';
XMLDIFF                       : 'XMLDIFF';
XMLELEMENT                    : 'XMLELEMENT';
XMLEXISTS                     : 'XMLEXISTS';
XMLEXISTS2                    : 'XMLEXISTS2';
XMLFOREST                     : 'XMLFOREST';
XMLINDEX                      : 'XMLINDEX';
XMLINDEX_REWRITE              : 'XMLINDEX_REWRITE';
XMLINDEX_REWRITE_IN_SELECT    : 'XMLINDEX_REWRITE_IN_SELECT';
XMLINDEX_SEL_IDX_TBL          : 'XMLINDEX_SEL_IDX_TBL';
XMLISNODE                     : 'XMLISNODE';
XMLISVALID                    : 'XMLISVALID';
XMLNAMESPACES                 : 'XMLNAMESPACES';
XMLPARSE                      : 'XMLPARSE';
XMLPATCH                      : 'XMLPATCH';
XMLPI                         : 'XMLPI';
XMLQUERY                      : 'XMLQUERY';
XMLROOT                       : 'XMLROOT';
XMLSCHEMA                     : 'XMLSCHEMA';
XMLSEQUENCE                   : 'XMLSEQUENCE';
XMLSERIALIZE                  : 'XMLSERIALIZE';
XMLTABLE                      : 'XMLTABLE';
XMLTOOBJECT                   : 'XMLTOOBJECT';
XMLTRANSFORM                  : 'XMLTRANSFORM';
XMLTRANSFORMBLOB              : 'XMLTRANSFORMBLOB';
XMLTYPE                       : 'XMLTYPE';
XOR                           : 'XOR';
XPATHTABLE                    : 'XPATHTABLE';
XQUERY                        : 'XQUERY';
XS                            : 'XS';
XS_SYS_CONTEXT                : 'XS_SYS_CONTEXT';
XTRANSPORT                    : 'XTRANSPORT';
YEAR                          : 'YEAR';
YEARS                         : 'YEARS';
YES                           : 'YES';
ZONE                          : 'ZONE';
ZONEMAP                       : 'ZONEMAP';
MULTIVALUE                    : 'MULTIVALUE';
CHARSET                       : 'CHARSET';
SHARDSPACE                    : 'SHARDSPACE';
// DM
NODE                          : 'NODE';
PASSWORD_POLICY               : 'PASSWORD_POLICY';
DISKSPACE                     : 'DISKSPACE';
SESSION_PER_USER              : 'SESSION_PER_USER';
CONNECT_IDLE_TIME             : 'CONNECT_IDLE_TIME';
FAILED_LOGIN_ATTEMPS          : 'FAILED_LOGIN_ATTEMPS';
MEM_SPACE                     : 'MEM_SPACE';
READ_PER_CALL                 : 'READ_PER_CALL';
READ_PER_SESSION              : 'READ_PER_SESSION';
ALLOW_IP                      : 'ALLOW_IP';
NOT_ALLOW_IP                  : 'NOT_ALLOW_IP';
ALLOW_DATETIME                : 'ALLOW_DATETIME';
NOT_ALLOW_DATETIME            : 'NOT_ALLOW_DATETIME';
DOMAIN                        : 'DOMAIN';
HUGE                          : 'HUGE';
GREAT                         : 'GREAT';
MICRO                         : 'MICRO';
CORRUPT                       : 'CORRUPT';
PENDANT                       : 'PENDANT';
AUTO_INCREMENT                : 'AUTO_INCREMENT';
FILLFACTOR                    : 'FILLFACTOR';
NOBRANCH                      : 'NOBRANCH';
CLUSTERBTR                    : 'CLUSTERBTR';
COUNTER                       : 'COUNTER';
LOGIC                         : 'LOGIC';
EQU                           : 'EQU';
RANDOMLY                      : 'RANDOMLY';
FULLY                         : 'FULLY';
FIELDS                        : 'FIELDS';
DELIMITED                     : 'DELIMITED';
RECORDS                       : 'RECORDS';
PARMS                         : 'PARMS';
NULL_STR                      : 'NULL_STR';
CHARACTER_CODE                : 'CHARACTER_CODE';
GBK                           : 'GBK';
UTF_8                         : 'UTF-8';
SINGLE_BYTE                   : 'SINGLE_BYTE';
EUC_KR                        : 'EUC-KR';
VARBINARY                     : 'VARBINARY';
DATETIME                      : 'DATETIME';
TEXT                          : 'TEXT';
LONGVARCHAR                   : 'LONGVARCHAR';
IMAGE                         : 'IMAGE';
STAT                          : 'STAT';
SECTION                       : 'SECTION';
FILESIZE                      : 'FILESIZE';
DELTA                         : 'DELTA';
PARTIITONS                    : 'PARTIITONS';
ROOT                          : 'ROOT';
LOGICLOG                      : 'LOGICLOG';
SPATIAL                       : 'SPATIAL';
LEXER                         : 'LEXER';
CHINESE_LEXER                 : 'CHINESE_LEXER';
CHINESE_VGRAM_LEXER           : 'CHINESE_VGRAM_LEXER';
CHINESE_FP_LEXER              : 'CHINESE_FP_LEXER';
ENGLISH_LEXER                 : 'ENGLISH_LEXER';
DEFAULT_LEXER                 : 'DEFAULT_LEXER';
AMERICAN                      : 'AMERICAN';
ENGLISH                       : 'ENGLISH';
NLS_TIMESTAMP_FORMAT          : 'NLS_TIMESTAMP_FORMAT';
NLS_TIMESTAMP_TZ_FORMAT       : 'NLS_TIMESTAMP_TZ_FORMAT';
NLS_TIME_FORMAT               : 'NLS_TIME_FORMAT';
NLS_TIME_TZ_FORMAT            : 'NLS_TIME_TZ_FORMAT';
SCHINESE_PINYIN_M             : 'SCHINESE_PINYIN_M';
SCHINESE_STROKE_M             : 'SCHINESE_STROKE_M';
SCHINESE_RADICAL_M            : 'SCHINESE_RADICAL_M';
THAI_CI_AS                    : 'THAI_CI_AS';
KOREAN_M                      : 'KOREAN_M';
CASE_SENSITIVE                : 'CASE_SENSITIVE';
GLOBAL_SESSION_PER_USER       : 'GLOBAL_SESSION_PER_USER';
CORRESPONDING                 : 'CORRESPONDING';
UNCOMMITTED                   : 'UNCOMMITTED';
INTENT                        : 'INTENT';
CS                            : 'CS';
CALCULATE                     : 'CALCULATE';
ABSTRACT                      : 'ABSTRACT';
LOGIN                         : 'LOGIN';
LOGOUT                        : 'LOGOUT';
SERERR                        : 'SERERR';
TIMER                         : 'TIMER';
CHECKPOIN                     : 'CHECKPOIN';
ONCE                          : 'ONCE';
PRINT                         : 'PRINT';
LOCAL_CODE                    : 'LOCAL_CODE';
CONVERT_MODE                  : 'CONVERT_MODE';
BYTES_IN_CHAR                 : 'BYTES_IN_CHAR';
DB_TYPE                       : 'DB_TYPE';
DATA_CHARSET                  : 'DATA_CHARSET';
CASE_OPT                      : 'CASE_OPT';
ROLLBACK_OPTION               : 'ROLLBACK_OPTION';
LSN                           : 'LSN';
PRETTY                        : 'PRETTY';
WRAPPER                       : 'WRAPPER';
UNCONDITIONAL                 : 'UNCONDITIONAL';
CONDITIONAL                   : 'CONDITIONAL';
JSONB                         : 'JSONB';
LEFTARG                       : 'LEFTARG';
RIGHTARG                      : 'RIGHTARG';
RECURSIVE                     : 'RECURSIVE';
LARGE                         : 'LARGE';
LISTAGG2                      : 'LISTAGG2';
TOP                           : 'TOP';
IDENTITY_INSERT               : 'IDENTITY_INSERT';
DDL_CLONE                     : 'DDL_CLONE';
SHADOW                        : 'SHADOW';
CUMULATIVE                    : 'CUMULATIVE';
BASE                          : 'BASE';
BACKUPDIR                     : 'BACKUPDIR';
BACKUPNAME                    : 'BACKUPNAME';
TAPE                          : 'TAPE';
BACKUPINFO                    : 'BACKUPINFO';
SPEED                         : 'SPEED';
MAXPIECESIZE                  : 'MAXPIECESIZE';
COMPRESSED                    : 'COMPRESSED';
TASK                          : 'TASK';
BACKED                        : 'BACKED';
UP                            : 'UP';
SINCE                         : 'SINCE';
INPUT                         : 'INPUT';
TRXID                         : 'TRXID';
DB                            : 'DB';
META                          : 'META';
INFO                          : 'INFO';
TXT                           : 'TXT';
BACKUPSETS                    : 'BACKUPSETS';
DMINI                         : 'DMINI';
OVERWRITE                     : 'OVERWRITE';
MAPPED                        : 'MAPPED';
BAK_MAGIC                     : 'BAK_MAGIC';


SHA2_512_Q      : '"SHA2_512"';
V1_Q            : '"V1"';


AMPERSAND_           : '&';
AND_                 : '&&';
ARROW_               : '=>';
ASSIGNMENT_OPERATOR_ : ':=';
ASTERISK_            : '*';
AT_                  : '@';
BACKSLASH_           : '\\';
BQ_                  : '`';
CARET_               : '^';
COLON_               : ':';
COMMA_               : ',';
DEQ_                 : '==';
DOLLAR_              : '$';
DOT_                 : '.';
DOT_ASTERISK_        : '.*';
DQ_                  : '"';
EQ_                  : '=';
EXPONENT_            : '**';
GT_                  : '>';
GTE_                 : '>=';
LBE_                 : '{';
LBT_                 : '[';
LP_                  : '(';
LT_                  : '<';
LTE_                 : '<=';
MINUS_               : '-';
MOD_                 : '%';
NEQ_                 : '<>' | '!=' | '^=';
NOT_                 : '!';
OR_                  : '||';
PLUS_                : '+';
POUND_               : '#';
QUESTION_            : '?';
RANGE_OPERATOR_      : '..';
RBE_                 : '}';
RBT_                 : ']';
RP_                  : ')';
SAFE_EQ_             : '<=>';
SEMI_                : ';';
SIGNED_LEFT_SHIFT_   : '<<';
SIGNED_RIGHT_SHIFT_  : '>>';
SLASH_               : '/';
SQ_                  : '\'';
TILDE_               : '~';
VERTICAL_BAR_        : '|';
UL_                  : '_';
TYPE_CAST_           : '::';
JSON_EXTRACT_        : '->';
JSON_EXTRACT_TEXT_   : '->>';
JSONB_CONTAIN_RIGHT_ : '@>';

WS : [ \t\r\n\u3000] + ->skip;

BLOCK_HINT : '/*+' .*? '*/';
INLINE_HINT: '--+' ~[\r\n]* ('\r'? '\n' | EOF);

BLOCK_COMMENT:  '/*' .*? '*/'                           -> channel(HIDDEN);
INLINE_COMMENT: '--' ~[\r\n]* ('\r'? '\n' | EOF)        -> channel(HIDDEN);

ERROR_END_BLOCK  : '$error' .*? '$end'                  -> channel(HIDDEN);
IF_END_BLOCK  : '$if' (ERROR_END_BLOCK | .)*? '$end'    -> channel(HIDDEN);

STRING_: SINGLE_QUOTED_TEXT;
//SINGLE_QUOTED_TEXT: (SQ_ ('\\'. | '\'\'' | ~('\'' | '\\'))* SQ_);
SINGLE_QUOTED_TEXT: SQ_ (~('\'' | '\r' | '\n') | '\'' '\'' | '\r'? '\n')* SQ_;
DOUBLE_QUOTED_TEXT: (DQ_ ( '\\'. | '""' | ~('"'| '\\') )* DQ_);
NCHAR_TEXT: 'N' STRING_;
UCHAR_TEXT: 'U' STRING_;
OPERATOR_: [~/>%<*+^\-@&=|`][~/>%<*+^\-@&=|`!]*[^%*=<>/!&|`];

INTEGER_: INT_;
NUMBER_: INTEGER_? DOT_? INTEGER_ ('E' (PLUS_ | MINUS_)? INTEGER_)?;
HEX_DIGIT_: '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_;
BIT_NUM_: '0b' ('0' | '1')+ | 'B' SQ_ ('0' | '1')+ SQ_;

A: 'A';
K: 'K';
M: 'M';
G: 'G';
T: 'T';
P: 'P';
E: 'E';
H: 'H';

I_CURSOR : '[CURSOR]' ;
IDENTIFIER_: [A-Z\u0080-\u2FFF\u3001-\uFF0B\uFF0D-\uFFFF]+[A-Z_$#0-9\u0080-\u2FFF\u3001-\uFF0B\uFF0D-\uFFFF]*;

fragment INT_: [0-9]+;
fragment HEX_: [0-9A-F];

