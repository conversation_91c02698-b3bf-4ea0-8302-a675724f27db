lexer grammar EsSplitLexer;

options {
    caseInsensitive = true;
}
//'GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'PATCH', 'OPTIONS'
DELETE  : 'DELETE';
GET     : 'GET';
HEAD    : 'HEAD';
OPTIONS : 'OPTIONS';
PATCH   : 'PATCH';
POST    : 'POST';
PUT     : 'PUT';



NULL_   : 'null';
TRUE    : 'true';
FALSE   : 'false';


AMPERSAND       : '&';
ASTERISK        : '*';
BAR             : '|';
COLON           : ':';
COMMA           : ',';
DOT             : '.';
EQUALS          : '=';
GREATER_THAN_OP : '>';
LEFT_BRACE      : '{';
LEFT_BRACKET    : '[';
LEFT_PAREN      : '(';
LESS_THAN_OP    : '<';
MINUS_SIGN      : '-';
PLUS_SIGN       : '+';
QUESTION        : '?';
RIGHT_BRACE     : '}';
RIGHT_BRACKET   : ']';
RIGHT_PAREN     : ')';
SEMICOLON       : ';';
SLASH           : '/';


DQUOTA_STRING : '"' (~('"' | '\r' | '\n') | '"' '"')* '"';
TQUOTA_STRING : '"""' .*? '"""';
NUMBER        : '-'?Digit+ ('.' Digit+)?;

METHOD: (GET | POST | PUT | DELETE | HEAD | PATCH | OPTIONS);
REQUEST_URL : METHOD ' ' ~[\r\n]*;

SPACES: [ \t\r\n]+ -> channel(HIDDEN);
SINGLE_LINE_COMMENT : ('//' | '#') ~('\r' | '\n')* NEWLINE_EOF -> channel(HIDDEN);
//MULTI_LINE_COMMENT  : '/*' .*? '*/'                    -> channel(HIDDEN);

I_CURSOR : '[CURSOR]' ;

fragment Digit: [0-9];
fragment NEWLINE_EOF    : NEWLINE | EOF;
fragment NEWLINE        : '\r'? '\n';
fragment SIMPLE_LETTER  : [A-Z];
