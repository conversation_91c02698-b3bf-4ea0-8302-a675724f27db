
parser grammar EsSplitParser;

options
{
    tokenVocab = EsSplitLexer;
}
// starting rule
root
    : methodStatement* EOF
    ;
methodStatement
    : REQUEST_URL jsonObject*
    ;
jsonObject
    : '{' (jsonPair (',' jsonPair)*)? '}'
    ;
jsonPair
    : DQUOTA_STRING ':' jsonValue
    ;
jsonValue
    : jsonNull
    | jsonBoolean
    | jsonNumber
    | jsonString
    | jsonObject
    | jsonArray
    ;
jsonNull
    : NULL_
    ;
jsonBoolean
    : TRUE
    | FALSE
    ;
jsonNumber
    : NUMBER
    ;
jsonString
    : DQUOTA_STRING
    | TQUOTA_STRING
    ;
jsonArray
    : '[' (jsonValue (',' jsonValue)*)? ']'
    ;
