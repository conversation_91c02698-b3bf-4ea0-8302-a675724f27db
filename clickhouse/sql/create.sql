-- create database
CREATE DATABASE db_comment ENGINE = Memory COMMENT 'The temporary database';
CREATE DATABASE test ENGINE = Atomic;
CREATE DATABASE test ENGINE = lazy(10);
CREATE DATABASE r ENGINE=Replicated('some/path/r','other_shard','{replica}');
CREATE DATABASE test_database
ENGINE = PostgreSQL('postgres1:5432', 'test_database', 'postgres', 'mysecretpassword', 'schema_name',1);
CREATE DATABASE mysql_db
ENGINE = MySQL('localhost:3306', 'test', 'my_user', 'user_password')
SETTINGS read_write_timeout=10000, connect_timeout=100;
CREATE DATABASE sqlite_db ENGINE = SQLite('sqlite.db');
CREATE DATABASE postgres_db
ENGINE = MaterializedPostgreSQL('postgres1:5432', 'postgres_database', 'postgres_user', 'postgres_password');


-- create dictionary
CREATE DICTIONARY id_value_dictionary
(
    id UInt64,
    value String
)
PRIMARY KEY id
SOURCE(CLICKHOUSE(TABLE 'source_table'))
LAYOUT(FLAT())
LIFETIME(MIN 0 MAX 1000);


CREATE DICTIONARY foo_db.id_value_dictionary
(
    id UInt64,
    value String
)
PRIMARY KEY id
SOURCE(CLICKHOUSE(TABLE 'source_table' USER 'clickhouse_admin' PASSWORD 'passworD43$x' DB 'foo_db' ))
LAYOUT(FLAT())
LIFETIME(MIN 0 MAX 1000);

CREATE DICTIONARY id_value_dictionary
(
    id UInt64,
    value String
)
PRIMARY KEY id
SOURCE(CLICKHOUSE(HOST 'HOSTNAME' PORT 9000 USER 'default' PASSWORD 'PASSWORD' TABLE 'source_table' DB 'default'))
LAYOUT(FLAT())
LIFETIME(MIN 0 MAX 1000);

CREATE DICTIONARY null_dict (
    id              UInt64,
    val             UInt8,
    default_val     UInt8 DEFAULT 123,
    nullable_val    Nullable(UInt8)
)
PRIMARY KEY id
SOURCE(NULL())
LAYOUT(FLAT())
LIFETIME(0);


-- create table
CREATE TABLE r.rmt (n UInt64) ENGINE=ReplicatedMergeTree ORDER BY n;

CREATE TABLE t1 (x String) ENGINE = Memory AS SELECT 1;

CREATE OR REPLACE TABLE test
(
    id UInt64,
    updated_at DateTime DEFAULT now(),
    updated_at_date Date DEFAULT toDate(updated_at)
)
ENGINE = MergeTree
ORDER BY id;

CREATE OR REPLACE TABLE test
(
    id UInt64,
    updated_at DateTime MATERIALIZED now(),
    updated_at_date Date MATERIALIZED toDate(updated_at)
)
ENGINE = MergeTree
ORDER BY id;

CREATE OR REPLACE TABLE test
(
    id UInt64,
    unhexed String EPHEMERAL,
    hexed FixedString(4) DEFAULT unhex(unhexed)
)
ENGINE = MergeTree
ORDER BY id;

CREATE OR REPLACE TABLE test
(
    id UInt64,
    size_bytes Int64,
    size String ALIAS formatReadableSize(size_bytes)
)
ENGINE = MergeTree
ORDER BY id;

CREATE TABLE users_a (
    uid Int16,
    name String,
    age Int16,
    name_len UInt8 MATERIALIZED length(name),
    CONSTRAINT c1 ASSUME length(name) = name_len
)
ENGINE=MergeTree
ORDER BY (name_len, name);

CREATE TABLE codec_example
(
    dt Date CODEC(ZSTD),
    ts DateTime CODEC(LZ4HC),
    float_value Float32 CODEC(NONE),
    double_value Float64 CODEC(LZ4HC(9)),
    value Float32 CODEC(Delta, ZSTD)
);

CREATE TABLE mytable
(
    x String CODEC(Delta, LZ4, AES_128_GCM_SIV)
)
ENGINE = MergeTree ORDER BY x;


CREATE TABLE t1 (x String) ENGINE = Memory COMMENT 'The temporary table';


-- create view
CREATE VIEW test_view
DEFINER = alice SQL SECURITY DEFINER
AS SELECT 1;

CREATE VIEW trace_view AS
SELECT
	TraceId AS traceID,
	SpanId AS spanID,
	SpanName AS operationName,
	ParentSpanId AS parentSpanID,
	ServiceName AS serviceName,
	Duration / 1000000 AS duration,
	Timestamp AS startTime,
	arrayMap(key -> map('key', key, 'value', SpanAttributes[key]), mapKeys(SpanAttributes)) AS tags,
	arrayMap(key -> map('key', key, 'value', ResourceAttributes[key]), mapKeys(ResourceAttributes)) AS serviceTags
FROM otel_traces
WHERE TraceId = '1f12a198ac3dd502d5201ccccad52967';


-- create materialized view
CREATE MATERIALIZED VIEW source REFRESH EVERY 1 DAY AS SELECT * FROM url('www.example.com/data.txt');
CREATE MATERIALIZED VIEW destination REFRESH EVERY 1 DAY AS SELECT * FROM source;
CREATE MATERIALIZED VIEW destination REFRESH EVERY 1 DAY DEPENDS ON source AS SELECT * FROM source;
CREATE MATERIALIZED VIEW destination REFRESH AFTER 1 HOUR DEPENDS ON source AS SELECT * FROM source;

-- create live view
CREATE LIVE VIEW lv WITH REFRESH 5 AS SELECT now();
CREATE LIVE VIEW lv AS SELECT sum(x) FROM mt;

-- create window view
CREATE WINDOW VIEW wv AS SELECT count(number), tumbleStart(w_id) as w_start from date GROUP BY tumble(now(), INTERVAL '5' SECOND) as w_id;
CREATE WINDOW VIEW wv WATERMARK=STRICTLY_ASCENDING AS SELECT count(number) FROM date GROUP BY tumble(timestamp, INTERVAL '5' SECOND);
CREATE WINDOW VIEW wv WATERMARK=ASCENDING AS SELECT count(number) FROM date GROUP BY tumble(timestamp, INTERVAL '5' SECOND);
CREATE WINDOW VIEW wv WATERMARK=INTERVAL '3' SECOND AS SELECT count(number) FROM date GROUP BY tumble(timestamp, INTERVAL '5' SECOND);
CREATE WINDOW VIEW test.wv TO test.dst WATERMARK=ASCENDING ALLOWED_LATENESS=INTERVAL '2' SECOND AS SELECT count(a) AS count, tumbleEnd(wid) AS w_end FROM test.mt GROUP BY tumble(timestamp, INTERVAL '5' SECOND) AS wid;
CREATE WINDOW VIEW wv TO dst AS SELECT count(id), tumbleStart(w_id) as window_start FROM data GROUP BY tumble(timestamp, INTERVAL '10' SECOND) as w_id;

-- create function
CREATE FUNCTION linear_equation AS (x, k, b) -> k*x + b;
CREATE FUNCTION parity_str AS (n) -> if(n % 2, 'odd', 'even');

-- create user
CREATE USER name1 NOT IDENTIFIED
PARALLEL WITH
CREATE USER name2 IDENTIFIED WITH plaintext_password BY 'my_password'
PARALLEL WITH
CREATE USER name3 IDENTIFIED WITH sha256_password BY 'my_password'
PARALLEL WITH
CREATE USER name4 IDENTIFIED WITH double_sha1_password BY 'my_password'
PARALLEL WITH
CREATE USER name4 IDENTIFIED WITH double_sha1_hash BY 'CCD3A959D6A004B9C3807B728BC2E55B67E10518'
PARALLEL WITH
CREATE USER name5 IDENTIFIED WITH bcrypt_password BY 'my_password'
PARALLEL WITH
CREATE USER name6 IDENTIFIED BY 'my_password'
PARALLEL WITH
CREATE USER user1 IDENTIFIED WITH plaintext_password by '1', bcrypt_password by '2', plaintext_password by '3'
PARALLEL WITH
CREATE USER mira@'127.0.0.1' NOT IDENTIFIED
PARALLEL WITH
CREATE USER name1 VALID UNTIL '2025-01-01'
PARALLEL WITH
CREATE USER name1 VALID UNTIL '2025-01-01 12:00:00 UTC'
PARALLEL WITH
CREATE USER name1 VALID UNTIL 'infinity'
PARALLEL WITH
CREATE USER name1 VALID UNTIL '2025-01-01 12:00:00 `Asia/Tokyo`'
PARALLEL WITH
CREATE USER name1 IDENTIFIED WITH plaintext_password BY 'no_expiration', bcrypt_password BY 'expiration_set' VALID UNTIL '2025-01-01'
PARALLEL WITH
CREATE USER mira HOST IP '127.0.0.1' IDENTIFIED WITH sha256_password BY 'qwerty'
PARALLEL WITH
CREATE USER john DEFAULT ROLE role1, role2
PARALLEL WITH
CREATE USER john DEFAULT ROLE ALL
PARALLEL WITH
CREATE USER john DEFAULT ROLE ALL EXCEPT role1, role2
PARALLEL WITH
CREATE USER john GRANTEES jack;

-- create role
CREATE ROLE accountant;

-- CREATE ROW POLICY
CREATE ROW POLICY pol1 ON mydb.table1 USING b=1 TO mira, peter;
CREATE ROW POLICY pol2 ON mydb.table1 USING c=2 TO peter, antonio;
CREATE ROW POLICY pol2 ON mydb.table1 USING c=2 AS RESTRICTIVE TO peter, antonio;
CREATE ROW POLICY filter1 ON mydb.mytable USING a<1000 TO accountant, john@localhost;
CREATE ROW POLICY filter2 ON mydb.mytable USING a<1000 AND b=5 TO ALL EXCEPT mira;
CREATE ROW POLICY filter3 ON mydb.mytable USING 1 TO admin;
CREATE ROW POLICY filter4 ON mydb.* USING 1 TO admin;

-- CREATE QUOTA
CREATE QUOTA qA FOR INTERVAL 15 month MAX queries = 123 TO CURRENT_USER;
CREATE QUOTA qB FOR INTERVAL 30 minute MAX execution_time = 0.5, FOR INTERVAL 5 quarter MAX queries = 321, errors = 10 TO default;

-- CREATE SETTINGS PROFILE
CREATE SETTINGS PROFILE max_memory_usage_profile SETTINGS max_memory_usage = ********* MIN ******** MAX ********* TO robin;

-- CREATE NAMED COLLECTION
CREATE NAMED COLLECTION foobar AS a = '1', b = '2' OVERRIDABLE;