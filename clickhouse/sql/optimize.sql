OPTIMIZE TABLE example <PERSON>DUP<PERSON>ICATE; -- all columns
OPTIMIZE TABLE example DEDUP<PERSON><PERSON>ATE BY *; -- excludes MATE<PERSON>ALIZED and ALIAS columns
OPTIMIZE TABLE example DEDUPLICATE BY colX,colY,colZ;
OPTIMIZE TABLE example DEDUPLICATE BY * EXCEPT colX;
OPTIMIZE TABLE example DEDUPLICATE BY * EXCEPT (colX, colY);
OPTIMIZE TABLE example DEDUPLICATE BY COLUMNS('column-matched-by-regex');
OPTIMIZE TABLE example DEDUPLICATE BY COLUMNS('column-matched-by-regex') EXCEPT colX;
OPTIMIZE TABLE example DEDUPLICATE BY COLUMNS('column-matched-by-regex') EXCEPT (colX, colY);
OPTIMIZE TABLE example FINAL DEDUPLICATE;
OPTIMIZE TABLE example FINAL DEDUPLICATE BY * EXCEPT value;
OPTIMIZ<PERSON> TABLE example FINAL DEDUPLICATE BY primary_key, secondary_key, partition_key;
OPTIMIZE TABLE example FINAL DEDUPLICATE BY COLUMNS('.*_key');
