SELECT URLHash, EventDate, count() AS PageViews FROM {table} WHERE CounterID = 62 AND EventDate >= '2013-07-01' AND EventDate <= '2013-07-31' AND NOT Refresh AND TraficSourceID IN (-1, 6) AND RefererHash = halfMD5('http://example.ru/') GROUP BY URLHash, EventDate ORDER BY PageViews DESC LIMIT 100;



-- desc
DESCRIBE TABLE describe_example;
DESCRIBE TABLE describe_example SETTINGS describe_include_subcolumns=1;

-- insert
INSERT INTO insert_select_testtable (*) VALUES (1, 'a', 1) ;
INSERT INTO insert_select_testtable (* EXCEPT(b)) Values (2, 2);
INSERT INTO insert_select_testtable VALUES (1, DEFAULT, 1) ;
INSERT INTO t FORMAT TabSeparated;
INSERT INTO x WITH y AS (SELECT * FROM numbers(10)) SELECT * FROM y;
WITH y AS (SELECT * FROM numbers(10)) INSERT INTO x SELECT * FROM y;
INSERT INTO infile_globs FROM INFILE 'input_*.csv' FORMAT CSV;
INSERT INTO infile_globs FROM INFILE 'input_{1,2}.csv' FORMAT CSV;
INSERT INTO infile_globs FROM INFILE 'input_?.csv' FORMAT CSV;
INSERT INTO TABLE FUNCTION remote('localhost', default.simple_table)
    VALUES (100, 'inserted via remote()');


-- delete
ALTER TABLE AAA DELETE WHERE Title LIKE '%hello%';


-- update
ALTER TABLE AAA UPDATE A = 'A' WHERE Title LIKE '%hello%';