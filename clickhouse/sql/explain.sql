EXPLAIN SELECT sum(number) FROM numbers(10) UNION ALL SELECT sum(number) FROM numbers(10) ORDER BY sum(number) ASC FORMAT TSV;
EXPLAIN AST SELECT 1;
EXPLAIN AST ALTER TABLE t1 DELETE WHERE date = today();
EXPLAIN SYNTAX SELECT * FROM system.numbers AS a, system.numbers AS b, system.numbers AS c;
EXPLAIN QUERY TREE SELECT id, value FROM test_table;
EXPLAIN SELECT sum(number) FROM numbers(10) GROUP BY number % 4;
EXPLAIN json = 1, description = 0 SELECT 1 UNION ALL SELECT 2 FORMAT TSVRaw;
EXPLAIN json = 1, description = 0, header = 1 SELECT 1, 2 + dummy;
EXPLAIN json = 1, actions = 1, description = 0 SELECT 1 FORMAT TSVRaw;
EXPLAIN PIPELINE SELECT sum(number) FROM numbers_mt(100000) GROUP BY number % 4;
EXPLAIN ESTIMATE SELECT * FROM ttt;
EXPLAIN TABLE OVERRIDE mysql('127.0.0.1:3306', 'db', 'tbl', 'root', 'clickhouse')
PARTITION BY toYYYYMM(assumeNotNull(created));