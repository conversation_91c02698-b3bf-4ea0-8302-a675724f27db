SELECT COLUMNS('a') FROM col_names;
SELECT COLUMNS('a'), COLUMNS('c'), toTypeName(COLUMNS('c')) FROM col_names;
SELECT COLUMNS('a') + COLUMNS('c') FROM col_names;
SELECT * APPLY(sum) FROM columns_transformers;
SELECT * EXCEPT (i) from columns_transformers;
SELECT * REPLACE(i + 1 AS i) from columns_transformers;
SELECT COLUMNS('[jk]') APPLY(toString) APPLY(length) APPLY(max) from columns_transformers;
SELECT * REPLACE(i + 1 AS i) EXCEPT (j) APPLY(sum) from columns_transformers;
SELECT * FROM some_table SETTINGS optimize_read_in_order=1, cast_keep_nullable=1;
SELECT sum(ALL number) FROM numbers(10);
SELECT sum(number) FROM numbers(10);
SELECT s, arr FROM arrays_test ARRAY JOIN arr;
SELECT s, arr FROM arrays_test LEFT ARRAY JOIN arr;
SELECT s, arr, a FROM arrays_test ARRAY JOIN arr AS a;
SELECT s, arr_external
FROM arrays_test
         ARRAY JOIN [1, 2, 3] AS arr_external;
SELECT s, arr, a, num, mapped
FROM arrays_test
         ARRAY JOIN arr AS a, arrayEnumerate(arr) AS num, arrayMap(x -> x + 1, arr) AS mapped;
SELECT s, arr, a, num, arrayEnumerate(arr)
FROM arrays_test
         ARRAY JOIN arr AS a, arrayEnumerate(arr) AS num;
SELECT s, arr, a, b
FROM arrays_test ARRAY JOIN arr as a, [['a','b'],['c']] as b
    SETTINGS enable_unaligned_array_join = 1;
SELECT s, `n.x`, `n.y`, `nest.x`, `nest.y`, num
FROM nested_test
         ARRAY JOIN nest AS n, arrayEnumerate(`nest.x`) AS num;
SELECT DISTINCT a FROM t1 ORDER BY b DESC;
SELECT number
FROM numbers(1, 10)
         EXCEPT
SELECT number
FROM numbers(3, 6);
SELECT * EXCEPT (default, alias_for, readonly, description)
FROM system.settings
LIMIT 5;
SELECT crypto_name FROM holdings
EXCEPT DISTINCT
SELECT crypto_name FROM crypto_prices
WHERE price < 10;
SELECT x, y FROM mytable FINAL WHERE x > 1;
SELECT x, y FROM mytable WHERE x > 1 SETTINGS final = 1;
SELECT year, month, day, count(*) FROM t GROUP BY ROLLUP(year, month, day);
SELECT year, month, day, count(*) FROM t GROUP BY year, month, day WITH ROLLUP;
SELECT crypto_name FROM holdings
INTERSECT DISTINCT
SELECT crypto_name FROM crypto_prices
WHERE price > 100;
SELECT name, text FROM table_1 LEFT OUTER JOIN table_2
                                               ON table_1.Id = table_2.Id AND startsWith(table_2.text, 'Text');
SELECT t1.*, t2.* from t1 LEFT JOIN t2 ON t1.key = t2.key and (t1.a < t2.a) ORDER BY (t1.key, t1.attr, t2.key, t2.attr);
SELECT *
FROM
    (
        SELECT number AS a
        FROM numbers(2)
    ) AS t1
    PASTE JOIN
(
    SELECT number AS a
    FROM numbers(2)
    ORDER BY a DESC
) AS t2;
SELECT a, b, toTypeName(a), toTypeName(b) FROM t_1 FULL JOIN t_2 USING (a, b);
SELECT
    CounterID,
    hits,
    visits
FROM
    (
        SELECT
            CounterID,
            count() AS hits
        FROM test.hits
        GROUP BY CounterID
    ) ANY LEFT JOIN
    (
        SELECT
            CounterID,
            sum(Sign) AS visits
        FROM test.visits
        GROUP BY CounterID
    ) USING CounterID
ORDER BY hits DESC
    LIMIT 10;
SELECT * FROM limit_by ORDER BY id, val LIMIT 2 BY id;
SELECT * FROM (
                  SELECT number%50 AS n FROM numbers(100)
              ) ORDER BY n LIMIT 0,5;
SELECT * FROM test_fetch ORDER BY a OFFSET 3 ROW FETCH FIRST 3 ROWS WITH TIES;
SELECT * FROM collate_test ORDER BY s ASC COLLATE 'en';
SELECT count()
FROM mydata
         PREWHERE C = 'x'
WHERE B = 0;
SELECT number, COUNT() OVER (PARTITION BY number % 3) AS partition_count
FROM numbers(10)
         QUALIFY partition_count = 4
ORDER BY number;
SELECT
    Title,
    count() * 10 AS PageViews
FROM hits_distributed
         SAMPLE 0.1
WHERE
    CounterID = 34
GROUP BY Title
ORDER BY PageViews DESC LIMIT 1000;
SELECT sum(PageViews * _sample_factor)
FROM visits
         SAMPLE 10000000;
SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 2;

SELECT CounterID, 1 AS table, toInt64(count()) AS c
FROM test.hits
GROUP BY CounterID
UNION ALL
SELECT CounterID, 2 AS table, sum(Sign) AS c
FROM test.visits
GROUP BY CounterID
HAVING c > 0;

with cte_numbers as
         (
             select
                 num
             from generateRandom('num UInt64', NULL)
             limit 1000000
         )
select
    count()
from cte_numbers
where num in (select num from cte_numbers);

WITH '2019-08-01 15:23:00' as ts_upper_bound
SELECT *
FROM hits
WHERE
        EventDate = toDate(ts_upper_bound) AND
        EventTime <= ts_upper_bound;

WITH sum(bytes) as s
SELECT
    formatReadableSize(s),
    table
FROM system.parts
GROUP BY table
ORDER BY s;

/* this example would return TOP 10 of most huge tables */
WITH
    (
        SELECT sum(bytes)
        FROM system.parts
        WHERE active
    ) AS total_disk_usage
SELECT
        (sum(bytes) / total_disk_usage) * 100 AS table_disk_usage,
    table
FROM system.parts
GROUP BY table
ORDER BY table_disk_usage DESC
    LIMIT 10;

WITH test1 AS (SELECT i + 1, j + 1 FROM test1)
SELECT * FROM test1;

WITH RECURSIVE AA AS (
    SELECT id, parent_id, data, [t.id] AS path
FROM tree t
WHERE t.id = 0
UNION ALL
SELECT t.id, t.parent_id, t.data, arrayConcat(path, [t.id])
FROM tree t, search_tree st
WHERE t.parent_id = st.id
    )
SELECT * FROM search_tree ORDER BY path;



