-- ALTER TABLE COLUMN
ALTER TABLE alter_test ADD COLUMN Added1 UInt32 FIRST;
ALTER TABLE alter_test ADD COLUMN Added2 UInt32 AFTER NestedColumn;
ALTER TABLE alter_test ADD COLUMN Added3 UInt32 AFTER ToDrop;
ALTER TABLE visits DROP COLUMN browser
ALTER TABLE visits RENAME COLUMN webBrowser TO browser
ALTER TABLE visits CLEAR COLUMN browser IN PARTITION tuple()
ALTER TABLE visits COMMENT COLUMN browser 'This column shows the browser used for accessing the site.'
ALTER TABLE visits MODIFY COLUMN browser Array(String)
ALTER TABLE table_name MODIFY COLUMN column_name REMOVE property;
ALTER TABLE table_with_ttl MODIFY COLUMN column_ttl REMOVE TTL;
ALTER TABLE table_name MODIFY COLUMN column_name MODIFY SETTING max_compress_block_size = 1048576;
ALTER TABLE table_name MOD<PERSON>Y COLUMN column_name RESET SETTING max_compress_block_size;
<PERSON>TER TABLE tmp ADD COLUMN s String MATERIALIZED toString(x);
ALTER TABLE tmp MATERIALIZE COLUMN s;

-- ALTER TABLE partition
ALTER TABLE mt DETACH PARTITION '2020-11-21';
ALTER TABLE mt DETACH PART '2020-11-21';
ALTER TABLE mt DROP PARTITION '2020-11-21';
ALTER TABLE mt DROP PART 'all_4_4_0';
ALTER TABLE mt DROP DETACHED PARTITION ALL;
ALTER TABLE mt DROP DETACHED PART ALL;
ALTER TABLE mt DROP DETACHED PARTITION 'all_4_4_0';
ALTER TABLE mt FORGET PARTITION '20201121';
ALTER TABLE visits ATTACH PARTITION 201901;
ALTER TABLE visits ATTACH PART 201901_2_2_0;
ALTER TABLE visits ATTACH PARTITION 201901 from table1;
ALTER TABLE visits REPLACE PARTITION 201901 from table1;
ALTER TABLE visits MOVE PARTITION 201901 TO TABLE table1;
ALTER TABLE hits MOVE PART '20190301_14343_16206_438' TO VOLUME 'slow';
ALTER TABLE hits MOVE PARTITION '2019-09-01' TO DISK 'fast_ssd';
ALTER TABLE visits FREEZE;
ALTER TABLE visits FREEZE PARTITION 201901;
ALTER TABLE visits FREEZE PARTITION 201901 WITH NAME 'BACKUP_201901';
ALTER TABLE visits UNFREEZE;
ALTER TABLE visits UNFREEZE PARTITION 201901;
ALTER TABLE visits UNFREEZE PARTITION 201901 WITH NAME 'BACKUP_201901';
ALTER TABLE users FETCH PARTITION 201902 FROM '/clickhouse/tables/01-01/visits';
ALTER TABLE users FETCH PART 201901_2_2_0 FROM '/clickhouse/tables/01-01/visits';

-- ALTER TABLE SETTINGS
-- CREATE TABLE example_table (id UInt32, data String) ENGINE=MergeTree() ORDER BY id;
ALTER TABLE example_table MODIFY SETTING max_part_loading_threads=8, max_parts_in_total=50000;
-- CREATE TABLE example_table (id UInt32, data String) ENGINE=MergeTree() ORDER BY id
--     SETTINGS max_part_loading_threads=8;
ALTER TABLE example_table RESET SETTING max_part_loading_threads;

-- ALTER TABLE MODIFY ORDER BY
ALTER TABLE example_table MODIFY ORDER BY new_expression;
ALTER TABLE example_table MODIFY SAMPLE BY new_expression;
ALTER TABLE example_table REMOVE SAMPLE BY;

-- ALTER TABLE index
ALTER TABLE visits ADD INDEX browser c1 type t1 FIRST abc;
ALTER TABLE visits CLEAR INDEX browser IN PARTITION tuple();
ALTER TABLE visits DROP INDEX browser;
ALTER TABLE visits MATERIALIZE INDEX browser IN PARTITION ID A;

-- ALTER TABLE constraint
ALTER TABLE visits ADD CONSTRAINT browser_not_null CHECK (browser IS NOT NULL);
ALTER TABLE visits DROP CONSTRAINT browser_not_null;

-- ALTER TABLE ttl
ALTER TABLE table_with_ttl MODIFY TTL ttl_expression;
ALTER TABLE table_with_ttl REMOVE TTL;

-- ALTER TABLE projection
/**
 * ALTER TABLE table_name ADD PROJECTION projection_name (
 *     SELECT_QUERY
 * )
 *
 * ALTER TABLE table_name DROP PROJECTION projection_name
 *
 * ALTER TABLE table_name MODIFY PROJECTION projection_name (
 *     SELECT_QUERY
 * )
 */
ALTER TABLE visits_order ADD PROJECTION user_name_projection (
    SELECT
        *
    ORDER BY user_name
);
ALTER TABLE visits_order MATERIALIZE PROJECTION user_name_projection;
ALTER TABLE visits_order DROP PROJECTION user_name_projection;
ALTER TABLE visits_order CLEAR PROJECTION user_name_projection IN PARTITION tuple();

-- ALTER materialized View
ALTER TABLE mv MODIFY QUERY SELECT a * 2 as a FROM src_table;

-- ALTER LIVE VIEW
ALTER LIVE VIEW visits refresh;

-- alter user
ALTER USER user1 NOT IDENTIFIED;
ALTER USER user1 RESET AUTHENTICATION METHODS TO NEW;
ALTER USER user1 ADD IDENTIFIED WITH no_password;
ALTER USER user1 ADD IDENTIFIED WITH plaintext_password by '1', bcrypt_password by '2', plaintext_password by '3'
ALTER USER WZY1 DEFAULT ROLE a1,a2;
ALTER USER WZY1 DEFAULT ROLE all;
ALTER USER WZY1 DEFAULT ROLE all except a1,a2;
ALTER USER user1 GRANTEES ANY EXCEPT user2;
ALTER USER john GRANTEES jack;

ALTER USER user1 IDENTIFIED WITH no_password # 无密码;
ALTER USER user1 IDENTIFIED WITH plaintext_password BY 'qwerty';
ALTER USER user1 IDENTIFIED WITH sha256_password BY 'qwerty';
ALTER USER user1 IDENTIFIED WITH sha256_hash BY 'hash';
ALTER USER user1 IDENTIFIED WITH double_sha1_password BY 'qwerty';
ALTER USER user1 IDENTIFIED WITH double_sha1_hash BY 'hash';
ALTER USER user1 IDENTIFIED WITH bcrypt_password BY 'qwerty';
ALTER USER user1 IDENTIFIED WITH bcrypt_hash BY 'hash';
ALTER USER user1 IDENTIFIED WITH ldap SERVER 'server_name';
ALTER USER user1 IDENTIFIED WITH kerberos;
ALTER USER user1 IDENTIFIED WITH kerberos REALM 'realm';
ALTER USER user1 IDENTIFIED WITH ssl_certificate CN 'mysite.com:user';
ALTER USER user1 IDENTIFIED BY 'qwerty';

ALTER USER name1 VALID UNTIL '2025-01-01';
ALTER USER name1 VALID UNTIL '2025-01-01 12:00:00 UTC';
ALTER USER name1 VALID UNTIL 'infinity';
ALTER USER name1 IDENTIFIED WITH plaintext_password BY 'no_expiration', bcrypt_password BY 'expiration_set' VALID UNTIL'2025-01-01';


-- alter QUOTA
ALTER QUOTA IF EXISTS qA FOR INTERVAL 15 month MAX queries = 123 TO CURRENT_USER;
ALTER QUOTA IF EXISTS qB FOR INTERVAL 30 minute MAX execution_time = 0.5, FOR INTERVAL 5 quarter MAX queries = 321, errors = 10 TO default;

-- alter role

-- alter policy

-- alter setting profile