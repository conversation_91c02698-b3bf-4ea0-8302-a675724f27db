parser grammar ClickHouseParser;

options {
    tokenVocab = ClickHouseLexer;
}

root
    : batch SEMI_? EOF
    ;

batch
    : statement (PARALLEL WITH statement)*
    ;

statement
    : select
    | insert
    | delete
    | update
    //DAL statement
    | use
    | exists
    | showWithFormat
    | showNotFormat
    | explain
    | checkTable
    | kill
    | optimizeTable
    | set
    | system
    | checkGrant
    // DCL statement
    | alterUser
    | alterRole
    | dropUser
    | dropRole
    | setRoleStmt
    | createUser
    | createRole
    | grant
    | revoke
    //DDL statement
    | describe
    | alterMaterializedView
    | alterTable
    | alterLiveView
    | alterQuota
    | alterPolicy
    | alterSettingsProfile
    | alterNamedCollection
    | attachDictionary
    | attachDatabase
    | attachTable
    | detachTable
    | detachDatabase
    | detachDictionary
    | detachView
    | dropTable
    | dropDatabase
    | dropDictionary
    | dropView
    | dropQuota
    | dropPolicy
    | dropProfile
    | dropFunction
    | dropNamedCollection
    | rename
    | exchange
    | truncate
    | watch
    | move
    | unDropTable
    | createDatabase
    | createDictionary
    | createTable
    | createLiveView
    | createMaterializedView
    | createView
    | createWindowView
    | createFunction
    | createPolicy
    | createQuota
    | createSettingsProfile
    | createNamedCollection
    ;

intoClause
    : INTO OUTFILE STRING_
    ;

formatClause
    : FORMAT identifierOrNull
    ;

// ALTER statement
delete
    : ALTER TABLE tableName clusterClause? DELETE (IN partitionClause)? whereClause
    ;

update
    : ALTER TABLE tableName clusterClause? UPDATE assignmentExprList (IN partitionClause)? whereClause?
    ;

alterMaterializedView
    : ALTER TABLE materializedViewName clusterClause? MODIFY QUERY select
    ;

alterLiveView
    : ALTER LIVE VIEW liveViewName clusterClause? REFRESH
    ;

alterTable
    : ALTER TABLE tableName clusterClause? alterTableClause (COMMA_ alterTableClause)*
    ;

alterTableModifyColumn
    : MODIFY COLUMN ifExists? tableColumnDfnt
    | COMMENT COLUMN ifExists? columnName STRING_       // 变更列comment
    | columnName REMOVE tableColumnPropertyType   // 删除列属性
    | columnName MODIFY SETTING settingExprList   // 变更列setting
    | columnName RESET SETTING identifier         // reset列setting
    ;

alterTableOtherColumn
    : CLEAR COLUMN ifExists? columnName (IN partitionClause)?
    | MATERIALIZE COLUMN ifExists? columnName (IN partitionClause)?
    ;

alterTableClause
    : ADD COLUMN ifNotExists? tableColumnDfnt (AFTER columnName)?            # AlterTableClauseAddColumn
    | DROP COLUMN ifExists? columnName                                       # AlterTableClauseDropColumn
    | alterTableModifyColumn                                                 # AlterTableClauseModifyColumn
    | RENAME COLUMN ifExists? columnName TO columnName                       # AlterTableClauseRenameColumn
    | alterTableOtherColumn                                                  # AlterTableClauseOtherColumn

    // Partition
    | ATTACH partitionClauseAsAlias                                                   # AlterTableClauseAttach
    | ATTACH partitionClause FROM tableName                                           # AlterTableClauseAttachFrom
    | DETACH partitionClauseAsAlias                                                   # AlterTableClauseDetach
    | DROP partitionClauseAsAlias                                                     # AlterTableClauseDropPartition
    | DROP DETACHED partitionClauseAsAlias                                            # AlterTableClauseDropDetachedPartition
    | MOVE partitionClauseAsAlias ( TO DISK STRING_
                           | TO VOLUME STRING_
                           | TO TABLE tableName
                           )                                                          # AlterTableClauseMovePartition
    | FORGET partitionClause                                                          # AlterTableClauseForgetPartition
    | FREEZE partitionClause? (WITH NAME STRING_)?                                    # AlterTableClauseFreezePartition
    | UNFREEZE partitionClause? WITH NAME STRING_                                     # AlterTableClauseUnFreezePartition
    | REPLACE partitionClause FROM tableName                                    # AlterTableClauseReplace
    | FETCH partitionClauseAsAlias FROM STRING_                                       # AlterTableClauseFetchPartition

    //setting
    | MODIFY SETTING settingExprList                                                  # AlterTableClauseModifySetting
    | RESET SETTING identifier                                                        # AlterTableClauseResetSetting

    //order by
    | MODIFY ORDER BY expr                                                      # AlterTableClauseModifyOrderBy

    //sample by
    | MODIFY SAMPLE BY expr                                                     # AlterTableClauseModifySampleBy
    | REMOVE SAMPLE BY                                                                # AlterTableClauseRemoveSampleBy

    //indexes
    | ADD INDEX ifNotExists? tableIndexDfnt ((FIRST|AFTER) indexName)?     # AlterTableClauseAddIndex
    | CLEAR INDEX ifExists? indexName (IN partitionClause)?                 # AlterTableClauseClearIndex
    | DROP INDEX ifExists? indexName                                        # AlterTableClauseDropIndex
    | MATERIALIZE INDEX ifExists? indexName (IN partitionClause)?           # AlterTableClauseMaterializeIndex

    //constraint
    | ADD CONSTRAINT ifNotExists? constraintName CHECK expr                     # AlterTableClauseAddConstraint
    | DROP CONSTRAINT ifExists? constraintName                                         # AlterTableClauseDropConstraint

    //projection
    | ADD PROJECTION ifNotExists? tableProjectionDfnt (AFTER projectionName)?   # AlterTableClauseAddProjection
    | CLEAR PROJECTION ifExists? projectionName (IN partitionClause)?            # AlterTableClauseClearProjection
    | DROP PROJECTION ifExists? projectionName                                   # AlterTableClauseDropProjection
    | MATERIALIZE PROJECTION ifExists? projectionName (IN partitionClause)?      # AlterTableClauseMaterializeProjection

    //ttl
    | MODIFY ttlClause                                                                # AlterTableClauseModifyTTL
    | REMOVE TTL                                                                      # AlterTableClauseRemoveTTL

    // Statistics
    | MODIFY STATISTICS columnArgList TYPE identifiers           # AlterTableClauseModifyStatistics
    | REMOVE STATISTICS ifExists? columnArgList          # AlterTableClauseRemoveStatistics
    | ADD STATISTICS ifNotExists? columnArgList TYPE identifiers                # AlterTableClauseAddStatistics
    | CLEAR STATISTICS ifExists? columnArgList               # AlterTableClauseClearStatistics
    | MATERIALIZE STATISTICS ifExists? columnArgList          # AlterTableClauseMaterializeStatistics

    // Apply mask of deleted rows
    | APPLY DELETED MASK (IN objectName)?                      # AlterTableClauseApplyMaskingPolicy
    ;

alterUser
    : ALTER USER ifExists? username usernameClause (alterUserClause)*
    ;

usernameClause
	: (COMMA_ username)*
	| RENAME TO username
	;

alterUserClause
    : clusterClause                        # AlterUserClauseOnCluster
    | alterUserIdentifiedClause                                  # AlterUserClauseIdentified
    | ADD HOST (hostClause (COMMA_ hostClause)* | ANY | NONE)    # AlterUserClauseAddHost
    | DROP HOST (hostClause (COMMA_ hostClause)* | ANY | NONE)   # AlterUserClauseDropHost
    | VALID UNTIL STRING_                                        # AlterUserClauseValid
    | DEFAULT ROLE toUserOrRoleClause                                    # AlterUserClauseDefaultRole
    | GRANTEES (identifier | ANY | NONE) (COMMA_ identifier | ANY | NONE)* (EXCEPT identifiers)? # AlterUserClauseGrantees
    | ADD? SETTINGS settingClause (COMMA_ settingClause)*               # AlterUserClauseAddSettings
    | MODIFY SETTINGS settingClause (COMMA_ settingClause)*             # AlterUserClauseModifySettings
    | DROP SETTINGS identifiers                                         # AlterUserClauseDropSettings
    | DROP ALL SETTINGS                                                 # AlterUserClauseDropAllSettings
    | ADD PROFILES STRING_ (COMMA_ STRING_)*                            # AlterUserClauseAddProfiles
    | DROP PROFILES STRING_ (COMMA_ STRING_)*                           # AlterUserClauseDropProfiles
    | DROP ALL PROFILES                                                 # AlterUserClauseDropAllProfiles
    ;

alterUserIdentifiedClause
    : NOT IDENTIFIED                                                           # AlterUserClauseNotIdentified
    | RESET AUTHENTICATION METHODS TO NEW                                      # AlterUserClauseResetIdentified
    | ADD? IDENTIFIED identifiedClause (COMMA_ identifiedClauseJoin)*          # AlterUserClauseAddIdentified
    ;

toUserOrRoleClause
    : usernameOrRoleList
    | CURRENT_USER
    | ALL
    | ALL EXCEPT usernameOrRoleList
    ;

settingClause
	: identifier alterSettingsClause*
    | PROFILE STRING_
    ;

alterSettingsClause
	: EQ_ literals
    | MIN EQ_? INTEGER_
    | MAX EQ_? INTEGER_
    | settingOptions
    ;

identifiedClause
	: (WITH identifiedByWith)? BY STRING_
	| WITH identifiedNotByClause
	;

identifiedClauseJoin
	: (identifiedByWith)? BY STRING_
    | identifiedNotByClause
	;

identifiedNotByClause
	: NO_PASSWORD
    | LDAP SERVER STRING_
    | KERBEROS (REALM STRING_)?
    | SSL (CN | SAN) STRING_
    | SSH_KEY BY KEY STRING_ TYPE STRING_
    | HTTP SERVER STRING_ (SCHEME STRING_)?
    ;

hostClause
	: LOCAL
    | (NAME | REGEXP | IP | LIKE) STRING_
    ;

alterQuota
	: ALTER QUOTA ifExists? quotaName clusterClause? (alterQuotaClause)*
	;

alterQuotaClause
	: RENAME TO identifier
    | keyedByClause
    | intervalClause (COMMA_ intervalClause)*
    | TO toUserOrRoleClause
	;
keyedByClause
	: KEYED BY keyedByIdentifier
	| NOT KEYED
	;
intervalClause
	: FOR RANDOMIZED? intervalLiterals
    (
        MAX quotaMaxClause (COMMA_ quotaMaxClause)*
        | NO LIMITS
        | TRACKING ONLY
    )
	;

quotaMaxClause
	: quotaMax EQ_ numberLiterals
	;

alterRole
	: ALTER ROLE ifExists? roleName alterNameClause (alterRoleClause)*
	;

alterRoleClause
	: clusterClause                                                     # AlterRoleClauseOnCluster
	| ADD? SETTINGS settingClause (COMMA_ settingClause)*               # AlterRoleClauseAddSettings
    | MODIFY SETTINGS settingClause (COMMA_ settingClause)*             # AlterRoleClauseModifySettings
    | DROP ALL PROFILES                                                # AlterRoleClauseDropAllProfiles
    | DROP ALL SETTINGS                                                # AlterRoleClauseDropAllSettings
    | DROP PROFILES STRING_ (COMMA_ STRING_)*                           # AlterRoleClauseDropProfiles
    | DROP SETTINGS identifiers                                         # AlterRoleClauseDropSettings
    | ADD PROFILES STRING_ (COMMA_ STRING_)*                            # AlterRoleClauseAddProfiles
	;

alterNameClause
	: (COMMA_ name)*
	| RENAME TO name
	;

alterPolicy
	: ALTER ROW? POLICY ifExists? policyIdentifier (policyClause)*
	;

policyIdentifier
	: policyName clusterClause? ON tableName (RENAME TO policyName)? (COMMA_ policyIdentifier)*
    ;

policyClause
	: AS (PERMISSIVE | RESTRICTIVE)
    | FOR SELECT
    | USING (expr | NONE) (COMMA_ USING (expr | NONE))?
    | TO toUserOrRoleClause
	;

alterSettingsProfile
	: ALTER SETTINGS PROFILE ifExists? name alterNameClause alterSettingsProfileClause*
	;

alterSettingsProfileClause
    : clusterClause
    | ADD? SETTINGS settingClause (COMMA_ settingClause)*
    | MODIFY SETTINGS settingClause (COMMA_ settingClause)*
    | DROP ALL PROFILES
    | DROP ALL SETTINGS
    | DROP PROFILES STRING_ (COMMA_ STRING_)*
    | DROP SETTINGS identifiers
    | ADD PROFILES STRING_ (COMMA_ STRING_)*
    ;

partitionClause
    : PARTITION expr         // actually we expect here any form of tuple of literals
    | PARTITION ID STRING_
    ;

partitionClauseAsAlias
    : (PARTITION | PART) expr
    | PARTITION ID STRING_
    ;

alterNamedCollection
    : ALTER NAMED COLLECTION ifExists? name clusterClause? alterNamedCollectionClause*
    ;
alterNamedCollectionClause
    : SET namedCollectionClause (COMMA_ namedCollectionClause)*
    | DELETE identifiers
    ;
namedCollectionClause
    : identifier EQ_ STRING_ ((NOT)? OVERRIDABLE)?
    ;
// AttachDictionaryStmt
attachDictionary
    : ATTACH DICTIONARY ifNotExists? dictionaryName clusterClause?
    ;

// AttachDatabaseStmt
attachDatabase
	: ATTACH DATABASE ifNotExists? databaseName engineClause? clusterClause?
	;

// AttachTableStmt
attachTable
	: ATTACH TABLE ifNotExists? tableName uuidClause? clusterClause? (FROM STRING_)? tableSchemaClause? engineClause? subqueryClause?
    | ATTACH TABLE tableName AS NOT? REPLICATED
	;

// CHECK table statement
checkTable: CHECK (ALL TABLES | TABLE tableName partitionClauseAsAlias?) (FORMAT identifierOrNull)? settingsClause?;

checkGrant: CHECK GRANT privileges;

unDropTable: UNDROP TABLE tableName uuidClause? clusterClause?;

/** CREATE statement **/

// CreateDatabaseStmt
createDatabase
    : CREATE DATABASE ifNotExists? databaseName clusterClause? engineExpr? (COMMENT STRING_)?
    ;

// CreateDictionaryStmt
createDictionary
    : (CREATE orReplace? | REPLACE) DICTIONARY ifNotExists? dictionaryName clusterClause? dictionarySchemaClause dictionaryEngineClause
    ;

// CreateLiveViewStmt
createLiveView
    : CREATE LIVE VIEW ifNotExists? liveViewName uuidClause? clusterClause? (WITH (TIMEOUT | REFRESH) INTEGER_?)? destinationClause? tableSchemaClause? subqueryClause
    ;

// CreateMaterializedViewStmt
createMaterializedView
    : CREATE MATERIALIZED VIEW ifNotExists? materializedViewName uuidClause? clusterClause? materializedViewClause subqueryClause (COMMENT STRING_)?
    ;

// CreateTableStmt
createTable
    : (CREATE orReplace? | REPLACE) TEMPORARY? TABLE ifNotExists? tableName uuidClause? clusterClause?
    tableSchemaClause? engineClause? (COMMENT STRING_)? subqueryClause?
    ;

// CreateViewStmt
createView
    : CREATE orReplace? VIEW ifNotExists? viewName uuidClause? viewSchemaClause? clusterClause?
    definerClause? sqlSecurityClause? subqueryClause (COMMENT STRING_)?
    ;
createWindowView
    : CREATE WINDOW VIEW ifNotExists? windowViewName destinationClause? (INNER ENGINE engineType)? engineClause?
    (WATERMARK EQ_ (STRICTLY_ASCENDING | ASCENDING | INTERVAL literals intervalUnit))?
    (ALLOWED_LATENESS EQ_ INTERVAL literals intervalUnit)? POPULATE? subqueryClause (COMMENT STRING_)?
    ;
createFunction
    : CREATE FUNCTION functionName clusterClause? AS LP_ tableArgList? RP_ ARROW_ expr
    ;
createUser
    : CREATE USER (ifNotExists | orReplace)? username clusterClause? createUserClause*
    ;
createUserClause
    :
    (
        NOT IDENTIFIED
        | IDENTIFIED identifiedClause (COMMA_ identifiedClauseJoin)*
    )
    | HOST (hostClause (COMMA_ hostClause)* | ANY | NONE)
    | VALID UNTIL STRING_
    | DEFAULT ROLE toUserOrRoleClause
    | IN accessStorageType
    | DEFAULT DATABASE (databaseName | NONE)
    | GRANTEES (identifier | ANY | NONE) (COMMA_ identifier | ANY | NONE)* (EXCEPT identifiers)?
    | SETTINGS settingClause (COMMA_ settingClause)*
    ;
createRole
    : CREATE ROLE (ifNotExists | orReplace)? roleName (COMMA_ roleName)* clusterClause?
    (
        IN accessStorageType
        | SETTINGS settingClause (COMMA_ settingClause)*
    )*
    ;
createPolicy
    : CREATE ROW? POLICY (ifNotExists | orReplace)? policyIdentifier (createPolicyClause)*
    ;
createPolicyClause
    : AS (PERMISSIVE | RESTRICTIVE)
    | IN accessStorageType
    | (FOR SELECT)? USING expr
    | TO toUserOrRoleClause
    ;
createQuota
    : CREATE QUOTA (ifNotExists | orReplace)? quotaName clusterClause? (createQuotaClause)*
    ;
createQuotaClause
    : IN accessStorageType
    | keyedByClause
    | intervalClause (COMMA_ intervalClause)*
    | TO toUserOrRoleClause
    ;
createSettingsProfile
    : CREATE SETTINGS PROFILE (ifNotExists | orReplace)? identifiers (createSettingsProfileClause)*
    ;
createSettingsProfileClause
    : clusterClause
    | IN accessStorageType
    | SETTINGS settingClause (COMMA_ settingClause)*
    | TO toUserOrRoleClause
    ;
createNamedCollection
    : CREATE NAMED COLLECTION ifNotExists? name clusterClause? AS namedCollectionClause (COMMA_ namedCollectionClause)*
    ;
dictionarySchemaClause: LP_ dictionaryAttrDfnt (COMMA_ dictionaryAttrDfnt)* RP_;
dictionaryAttrDfnt
    : identifier columnTypeExpr
    ( DEFAULT literals
    | EXPRESSION expr
    | HIERARCHICAL
    | INJECTIVE
    | IS_OBJECT_ID
    )*
    ;
dictionaryEngineClause
    : dictionaryPrimaryKeyClause?
    ( sourceClause
    | lifetimeClause
    | layoutClause
    | rangeClause
    | dictionarySettingsClause
    )*
    ;
dictionaryPrimaryKeyClause: PRIMARY KEY exprs;
dictionaryArgExpr: identifier (identifier (LP_ RP_)? | literals);
sourceClause: SOURCE LP_ identifier LP_ dictionaryArgExpr* RP_ RP_;
lifetimeClause: LIFETIME LP_ ( INTEGER_
                                | MIN INTEGER_ MAX INTEGER_
                                | MAX INTEGER_ MIN INTEGER_
                                ) RP_;
layoutClause: LAYOUT LP_ identifier LP_ dictionaryArgExpr* RP_ RP_;
rangeClause: RANGE LP_ (MIN identifier MAX identifier | MAX identifier MIN identifier) RP_;
dictionarySettingsClause: SETTINGS LP_ settingExprList RP_;

clusterClause: ON CLUSTER (identifier | STRING_);
uuidClause: UUID STRING_;
destinationClause: TO tableName;
subqueryClause: AS select;
viewSchemaClause: LP_ tableElementExpr (COMMA_ tableElementExpr)* RP_;
definerClause : DEFINER EQ_ (identifier | CURRENT_USER);
sqlSecurityClause: SQL SECURITY (DEFINER | INVOKER | NONE);
tableSchemaClause
    : LP_ tableElementExpr (COMMA_ tableElementExpr)* RP_       # schemaDescriptionClause
    | CLONE? AS tableName                                       # schemaAsTableClause
    | AS tableFunctionExpr                                      # schemaAsFunctionClause
    ;
engineClause
    : engineExpr
    ( orderByClause
    | partitionByClause
    | primaryKeyClause
    | sampleByClause
    | ttlClause
    | settingsClause
    )*
    ;
partitionByClause: PARTITION BY expr;
primaryKeyClause: PRIMARY KEY expr;
sampleByClause: SAMPLE BY expr;
ttlClause: TTL ttlExpr (COMMA_ ttlExpr)*;
materializedViewClause
    : (destinationClause viewSchemaClause?)? engineExpr? POPULATE? definerClause? sqlSecurityClause?
    | tableSchemaClause? (destinationClause | engineClause POPULATE?)
    | refreshAbleMaterializedViewClause*
    ;
refreshAbleMaterializedViewClause
    : REFRESH (EVERY|AFTER) INTEGER_ intervalUnit (OFFSET INTEGER_ intervalUnit)?
    | RANDOMIZE FOR intervalUnit
    | DEPENDS ON objectName (COMMA_ objectName)
    | settingsClause
    | APPEND
    | destinationClause viewSchemaClause? engineExpr?
    | EMPTY
    ;
engineExpr: ENGINE EQ_? engineTypeIdentifier (LP_ exprs? RP_)?;
tableElementExpr
    : tableColumnDfnt                                                              # TableElementExprColumn
    | CONSTRAINT constraintName (CHECK | ASSUME) expr                           # TableElementExprConstraint
    | INDEX tableIndexDfnt                                                         # TableElementExprIndex
    | PROJECTION tableProjectionDfnt                                               # TableElementExprProjection
    ;
tableColumnDfnt
    : columnName columnTypeExpr? tableColumnPropertyExpr? (COMMENT STRING_)? codecExpr? (TTL expr)?
    ;
tableColumnPropertyExpr: ((DEFAULT | MATERIALIZED | ALIAS) expr) | EPHEMERAL expr?;
tableIndexDfnt: indexName expr TYPE columnTypeExpr (GRANULARITY INTEGER_)?;
tableProjectionDfnt: projectionName projectionSelectStmt;
codecExpr: CODEC LP_ ((codecArgExpr (COMMA_ codecArgExpr)*) | NONE) RP_;
codecArgExpr: identifier (LP_ exprs? RP_)?;
ttlExpr: expr (DELETE | TO DISK STRING_ | TO VOLUME STRING_)?;

// DESCRIBE statement
describe: (DESCRIBE | DESC) TABLE? queryTableExpr settingsClause? intoClause? formatClause?;

detachTable
	: DETACH TABLE ifExists? tableName clusterClause? PERMANENTLY? SYNC?
	;

detachView
	: DETACH VIEW ifExists? viewName clusterClause? PERMANENTLY? SYNC?
	;

detachDictionary
	: DETACH DICTIONARY ifExists? dictionaryName clusterClause? PERMANENTLY? SYNC?
	;

detachDatabase
	: DETACH DATABASE ifExists? databaseName clusterClause? PERMANENTLY? SYNC?
	;

// DROP statement
dropDatabase
    : DROP DATABASE ifExists? databaseName clusterClause? SYNC?
    ;

dropTable
    : DROP TEMPORARY? TABLE ifExists? (IF EMPTY)? tableName (COMMA_ tableName)* clusterClause? SYNC?
    ;

dropDictionary
    : DROP DICTIONARY ifExists? dictionaryName SYNC?
    ;

dropUser
    : DROP USER ifExists? username clusterClause? (FROM accessStorageType)?
    ;

dropRole
    : DROP ROLE ifExists? roleName clusterClause? (FROM accessStorageType)?
    ;

dropQuota
    : DROP QUOTA ifExists? quotaName clusterClause? (FROM accessStorageType)?
    ;

dropProfile
    : DROP SETTINGS? PROFILE ifExists? profileName clusterClause? (FROM accessStorageType)?
    ;

dropPolicy
    : DROP ROW? POLICY ifExists? policyName ON policyName clusterClause? (FROM accessStorageType)?
    ;

dropView
    : DROP VIEW ifExists? viewName clusterClause? SYNC?
    ;

dropFunction
    : DROP FUNCTION ifExists? functionName clusterClause? SYNC?
    ;

dropNamedCollection
    : DROP NAMED COLLECTION ifExists? namedCollectionName clusterClause? SYNC?
    ;

// EXISTS statement
exists
    : EXISTS DATABASE databaseName intoClause? formatClause?
    | EXISTS objectName intoClause? formatClause?
    | EXISTS TEMPORARY? TABLE tableName intoClause? formatClause?
    | EXISTS VIEW viewName intoClause? formatClause?
    | EXISTS DICTIONARY dictionaryName intoClause? formatClause?
    ;

// EXPLAIN statement
explain
    : EXPLAIN (
        AST root                     //Dump query AST. Supports all types of queries, not only SELECT.
        | SYNTAX select
        | QUERY TREE settingExprList? select
        | PLAN settingExprList? select
        | PIPELINE settingExprList? select
        | ESTIMATE select
        | TABLE OVERRIDE identifier LP_ columnArgList? RP_
            (COLUMNS LP_ columnArgList? RP_)? orderByClause? partitionByClause? (PRIMARY KEY)? sampleByClause? ttlClause?
        | settingExprList? select
    ) (FORMAT identifierOrNull)?
    ;

// INSERT statement
insert
    : withClause? INSERT insertIntoClause (insertValuesClause | select | insertFromClause)
    ;

insertIntoClause
    : INTO TABLE? (tableName | FUNCTION tableFunctionExpr) aliasClause? settingsClause? insertColumnExprs?
    ;

insertColumnExprs
    : LP_ insertColumnExpr (COMMA_ insertColumnExpr)* modifiersClause* RP_
    ;

insertColumnExpr
    : ASTERISK_
    | tableName DOT_ASTERISK_
    | columnName
    ;

insertValuesClause
    : VALUES assignmentValueList
    | FORMAT identifier (assignmentValueList)?
    ;

insertFromClause
    : FROM INFILE STRING_ settingsClause? (FORMAT identifierOrNull)?
    ;

assignmentValueList
    : assignmentValues (COMMA_ assignmentValues)*
    ;

assignmentValues
    : LP_ assignmentValue (COMMA_ assignmentValue)* RP_
    | LP_ RP_
    ;

assignmentValue
    : expr | DEFAULT
    ;

// KILL statement
kill
    : KILL MUTATION clusterClause? whereClause TEST? (FORMAT identifierOrNull)?
    | KILL QUERY clusterClause? whereClause (SYNC | ASYNC | TEST)? (FORMAT identifierOrNull)?
    ;

// OPTIMIZE statement
optimizeTable
    : OPTIMIZE TABLE tableName clusterClause? partitionClause? (FINAL|FORCE)? (DEDUPLICATE (BY optimizeExpr+)?)?
    ;

optimizeExpr
	: ASTERISK_
    | (COLUMNS | EXCEPT)? exprs
	;

// RENAME statement
rename
	: RENAME TABLE renameTableClause (COMMA_ renameTableClause)* clusterClause?           # RenameTable
	| RENAME DICTIONARY renameDictionaryClause (COMMA_ renameDictionaryClause)* clusterClause?      # RenameDictionary
    | RENAME DATABASE renameDatabaseClause (COMMA_ renameDatabaseClause)* clusterClause?    # RenameDatabase
	;

renameTableClause
    : tableName TO tableName
    ;

renameDictionaryClause
    : dictionaryName TO dictionaryName
    ;

renameDatabaseClause
    : databaseName TO databaseName
    ;

exchange
	: EXCHANGE TABLES tableName AND tableName clusterClause?   # ExchangeTable
	| EXCHANGE DICTIONARIES dictionaryName AND dictionaryName clusterClause?   # ExchangeDictionary
	;

// PROJECTION SELECT statement

projectionSelectStmt
	: LP_ withClause? SELECT selectList groupByClause? projectionOrderByClause? RP_
    ;

// SELECT statement
select
    : selectSubquery intoClause? formatClause?
    ;

selectSubquery
    : selectSubquery combineType selectSubquery
    | ((queryBlock | parenthesisSelectSubquery) orderByClause? limitByClause?)
    ;

parenthesisSelectSubquery
    : LP_ selectSubquery RP_
    ;

combineType
    : (EXCEPT | INTERSECT ) DISTINCT? | UNION ALL?
    ;

queryBlock
	: withClause?
    SELECT duplicateSpecification? selectList modifiersClause*
    selectFromClause?
    prewhereClause?
    whereClause?
    groupByClause? (WITH (CUBE | ROLLUP))? (WITH TOTALS)?
    havingClause?
    windowClause?
    qualifyClause?
    orderByClause?
    limitByClause?
    limitClause?
    offsetClause?
    settingsClause?
    ;

duplicateSpecification
    : DISTINCT (ON columnNamesWithParentheses)?
    ;

selectList
    : topClause? (unqualifiedShorthand | selectProjection) (COMMA_ selectProjection)*
    ;

unqualifiedShorthand
    : ASTERISK_
    ;

selectProjection
    : (tableName | viewName | materializedViewName | alias) DOT_ASTERISK_
    | (columnName | expr) aliasClause?
    ;

aliasClause
    : AS? alias
    ;

qualifyClause
    : QUALIFY expr
    ;

offsetClause
    : OFFSET INTEGER_ (ROW | ROWS)? (FETCH (FIRST | NEXT) INTEGER_ (ROW | ROWS) (ONLY | WITH TIES))?
    ;

modifiersClause
    : (APPLY | EXCEPT | REPLACE) columnNamesWithParentheses
    ;

withClause
    : WITH RECURSIVE? (expressionWith | subQueryWith)
    ;

expressionWith
    : expr AS identifier
    ;

subQueryWith
    : namedQuery (',' namedQuery)*
    ;

namedQuery
    : name (columnNamesWithParentheses)? subqueryClause
    ;

topClause
    : TOP INTEGER_ (WITH TIES)?
    ;

windowClause: WINDOW identifier AS LP_ windowExpr RP_;
prewhereClause: PREWHERE expr;
whereClause: WHERE expr;
groupByClause: GROUP BY ((CUBE | ROLLUP) LP_ exprs RP_ | exprs);
havingClause: HAVING expr;
orderByClause: ORDER BY orderExprList;
projectionOrderByClause: ORDER BY exprs;
limitByClause: LIMIT limitExpr BY exprs;
limitClause: LIMIT limitExpr (WITH TIES)?;
settingsClause: SETTINGS settingExprList;

selectFromClause
    : FROM fromClauseList
    ;

fromClauseList
    : selectTableReference (COMMA_ selectTableReference)*
    ;

selectTableReference
    : queryTableExprClause aliasClause? selectJoinOption*
    | LP_ selectTableReference RP_
    ;

selectJoinOption
    : tableJoinClause | arrayJoinClause
    ;

tableJoinClause
    : globalLocalClause? joinType? joinRange? JOIN selectTableReference aliasClause? selectJoinSpecification?
    ;

arrayJoinClause
    : (LEFT | INNER)? ARRAY JOIN exprs
    ;

selectJoinSpecification
    : ON exprs
    | USING (columnNames | columnNamesWithParentheses)
    ;

queryTableExprClause
    : LP_ queryTableExpr  RP_ | queryTableExpr
    ;

queryTableExpr
    : queryTableExprSampleClause
    | subqueryTableExprClause
    | tableCollectionExpr
    ;

queryTableExprSampleClause
    : tableName FINAL? sampleClause?
    ;

sampleClause
    : SAMPLE ratioExpr (OFFSET ratioExpr)?
    ;

ratioExpr
    : numberLiterals (SLASH_ numberLiterals)?
    ;

subqueryTableExprClause
    : LP_ selectSubquery RP_
    ;

tableCollectionExpr
    : functionCall
    ;

limitExpr: expr ((COMMA_ | OFFSET) expr)?;
orderExprList: orderExpr (COMMA_ orderExpr)*;
orderExpr: (numberLiterals | expr) (ASCENDING | DESCENDING | DESC)? (NULLS (FIRST | LAST))? (COLLATE STRING_)?;
settingExprList: settingExpr (COMMA_ settingExpr)*;
settingExpr: identifier EQ_ literals;

windowExpr: winPartitionByClause? winOrderByClause? winFrameClause?;
winPartitionByClause: PARTITION BY exprs;
winOrderByClause: ORDER BY orderExprList;
winFrameClause: (ROWS | RANGE) winFrameExtend;
winFrameExtend
    : winFrameBound                             # frameStart
    | BETWEEN winFrameBound AND winFrameBound   # frameBetween
    ;
winFrameBound: (CURRENT ROW | UNBOUNDED PRECEDING | UNBOUNDED FOLLOWING | numberLiterals PRECEDING | numberLiterals FOLLOWING);
//rangeClause: RANGE LPAREN (MIN identifier MAX identifier | MAX identifier MIN identifier) RPAREN;

// SET statement
set: SET settingExprList;

setRoleStmt
	: SET ROLE (DEFAULT  | NONE | toUserOrRoleClause)                   # SetRole
    | SET DEFAULT ROLE (NONE | toUserOrRoleClause) toObjectClause       # SetDefaultRole
	;

// SHOW statements
showWithFormat
    : SHOW CREATE DATABASE databaseName intoClause? formatClause?                                                                    # ShowCreateDatabase
    | SHOW CREATE TEMPORARY? ( TABLE | DICTIONARY )? tableName intoClause? formatClause?                                              # ShowCreateTable
    | SHOW DATABASES intoClause? formatClause?                                                                                             # ShowDatabases
    | SHOW DICTIONARIES (FROM databaseName)? (LIKE STRING_)?  limitClause? intoClause? formatClause?                          # ShowDictionaries
    | SHOW TEMPORARY? TABLES ((FROM | IN) databaseName)? (LIKE STRING_ | whereClause)? limitClause? intoClause? formatClause? # ShowTables
    | SHOW PROCESSLIST intoClause? formatClause?                                                                                         # ShowProcessList
    ;

showNotFormat
	: SHOW GRANTS (FOR identifiers)? (WITH IMPLICIT)? FINAL?        # ShowGrant
    | SHOW CREATE USER (identifiers | CURRENT_USER)?                # ShowCreateUser
    | SHOW CREATE ROLE identifiers                                  # ShowCreateRole
    | SHOW CREATE ROW? POLICY identifier ON tableName (COMMA_ tableName) # ShowCreateRowPolicy
    | SHOW CREATE QUOTA  (identifiers | CURRENT)?                   # ShowCreateQuota
    | SHOW CREATE SETTINGS? PROFILE identifiers                    # ShowCreateProfile
	;
// SYSTEM statements

system
	: SYSTEM (
        SHUTDOWN
        | KILL
        | RESTART REPLICAS
        | DROP systemCache CACHE
        | RELOAD EMBEDDED? DICTIONARIES
        | RELOAD DICTIONARY dictionaryName
        | DROP REPLICA STRING_ (FROM
            (TABLE tableName | DATABASE databaseName | ZKPATH STRING_)
        )?
        | (
            STOP DISTRIBUTED SENDS
            | START DISTRIBUTED SENDS
            | FLUSH DISTRIBUTED
            | STOP TTL MERGES
            | START TTL MERGES
            | STOP MOVES
            | START MOVES
            | STOP FETCHES
            | START FETCHES
            | STOP REPLICATED SENDS
            | START REPLICATED SENDS
            | STOP REPLICATION QUEUES
            | START REPLICATION QUEUES
            | RESTART REPLICA
        ) objectName clusterClause?
        | (FLUSH LOGS | RELOAD CONFIG) clusterClause?
        | (
            STOP MERGES
            | START MERGES
        ) clusterClause? (tableName | ON VOLUME STRING_)
        | UNFREEZE WITH NAME STRING_
        | SYNC REPLICA tableName (STRICT | LIGHTWEIGHT (FROM STRING_ (COMMA_ STRING_)*)? | PULL)?
    )
    ;

// TRUNCATE statements
truncate: TRUNCATE TEMPORARY? TABLE? ifExists? tableName clusterClause?;

// USE statement
use: USE databaseName;

// WATCH statement
watch: WATCH liveViewName EVENTS? (LIMIT INTEGER_)?;

move
	: MOVE USER identifiers TO accessStorageType                # moveUser
	| MOVE ROLE identifiers TO accessStorageType                # moveRole
	| MOVE QUOTA identifiers TO accessStorageType               # moveQuota
	| MOVE SETTINGS PROFILE identifiers TO accessStorageType    # moveSettings
	| MOVE ROW POLICY identifiers TO accessStorageType          # moveRowPolicy
    ;

grant
	: GRANT clusterClause? (objectPrivilegeClause | rolePrivilegeClause)
    ;

objectPrivilegeClause
	: privileges toObjectClause objectWithClause*
    ;

rolePrivilegeClause
	: identifiers toObjectClause roleWithClause*
    ;

privileges
	: objectPrivileges onObjectClause
    | databaseLevelPrivileges onDatabaseClause
    | globalLevelPrivileges
    ;

objectPrivileges
	: (
        columnLevelPrivileges
        | tableLevelPrivileges
        | viewLevelPrivileges
        | dictionaryLevelPrivileges
    ) (COMMA_ objectPrivileges)*
	;

onDatabaseClause
	: ON (databaseName | ASTERISK_)
	;

//table|db.*|*.*|*
onObjectClause
	: ON (
	    tableName
	    | owner DOT_ASTERISK_
	    | ASTERISK_ DOT_ASTERISK_?
	)
	;

toObjectClause
	: TO identifierOrCurrentUsers
	;

objectWithClause
	: WITH (REPLACE|GRANT) OPTION
	;

roleWithClause
	: WITH (ADMIN|GRANT) OPTION
	;

columnLevelPrivileges
	: (
        SELECT
        | INSERT
        | ALTER (TABLE | COLUMN)?
        | ALTER? (
            UPDATE
            | DELETE
            | (ADD | DROP | MODIFY | COMMENT | CLEAR | RENAME)? COLUMN
        )
        | SHOW COLUMNS?
        | ALL
    ) expr? (COMMA_ columnLevelPrivileges)*
	;

tableLevelPrivileges
	: (
        ALTER? (
            INDEX
            | CONSTRAINT
            | ORDER BY
            | MODIFY ORDER BY
            | (ADD | DROP | MATERIALIZE | CLEAR) INDEX
            | (ADD | DROP) CONSTRAINT
            | (MATERIALIZE | MODIFY)? TTL
            | SETTINGS | MODIFY? SETTING
            | MOVE (PARTITION | PART)
            | ( FETCH | FREEZE) PARTITION
        )
        | (CREATE | DROP) TABLE?
        | TRUNCATE
        | OPTIMIZE
        | SHOW TABLES
        | SYSTEM FLUSH?
        | SYSTEM? (
            | TTL? MERGES | FETCHES | MOVES | (DISTRIBUTED | REPLICATED ) SENDS | REPLICATION QUEUES
            | SYNC REPLICA | RESTART REPLICA | FLUSH DISTRIBUTED
        )
        | (START|STOP) (TTL? MERGES | FETCHES | MOVES | (DISTRIBUTED | REPLICATED ) SENDS | REPLICATION QUEUES)
        | SYSTEM (START|STOP)? SENDS
        | (START|STOP) SENDS
    ) (COMMA_ tableLevelPrivileges)*
	;

viewLevelPrivileges
	: ((CREATE | DROP) VIEW) (COMMA_ viewLevelPrivileges)*
	;

dictionaryLevelPrivileges
	: (
        (CREATE | DROP) DICTIONARY
        | SHOW DICTIONARIES
        | DICTGET
    ) (COMMA_ dictionaryLevelPrivileges)*
	;

databaseLevelPrivileges
	: (
        (CREATE | DROP) DATABASE
        | SHOW DATABASES
    ) (COMMA_ databaseLevelPrivileges)*
	;

globalLevelPrivileges
	: (
        ACCESS MANAGEMENT
        | KILL QUERY
        | CREATE (USER | ROLE | ROW? POLICY | QUOTA | SETTINGS? PROFILE | TEMPORARY TABLE)
        | DROP (USER | ROLE | ROW? POLICY | QUOTA | SETTINGS? PROFILE)
        | ALTER (USER | ROLE | ROW? POLICY | QUOTA | SETTINGS? PROFILE)
        | SHOW_USERS | SHOW_ROLES | SHOW_ROW_POLICIES | SHOW_QUOTAS | SHOW_SETTINGS_PROFILES
        | SHOW CREATE (USER | ROLE | ROW? POLICY | QUOTA | SETTINGS? PROFILE)
        | SHOW (POLICIES | PROFILES)
        | SYSTEM KILL
        | SYSTEM (RELOAD | FLUSH)?
        | SYSTEM? (
            SHUTDOWN
            | DROP systemCache CACHE?
            | RELOAD (CONFIG |DICTIONARY|EMBEDDED DICTIONARIES|DICTIONARIES)
            | FLUSH LOGS
        )
        | INTROSPECTION FUNCTIONS? | ADDRESS_TO_LINE | ADDRESS_TO_SYMBOL | DEMANGLE
        | SOURCES | AZURE | FILE | HDFS | HIVE | JDBC | KAFKA | MONGO | MYSQL | NATS | ODBC | POSTGRES | POSTGRES | REDIS | REMOTE | S3 | SQLITE | URL
    ) (COMMA_ globalLevelPrivileges)*
	;

revoke
	: REVOKE clusterClause? (privileges | (ADMIN OPTION FOR)? identifiers) fromObjectClause
    ;

fromObjectClause
	: FROM (
        identifierOrCurrentUsers
        | ALL
        | ALL EXCEPT identifiers
    )
	;

ifNotExists
    : IF NOT EXISTS
    ;

ifExists
    : IF EXISTS
    ;

orReplace
    : OR REPLACE
    ;

/***** expr ******/
expr
    : expr andOperator expr
    | expr orOperator expr
    | notOperator expr
    | LP_ expr RP_
    | expr literalExpr
    | booleanPrimary
    ;

literalExpr
    : LBT_ expr RBT_
    | QUESTION_ expr COLON_ expr
    ;

andOperator
    : AND
    ;

orOperator
    : OR | OR_
    ;

notOperator
    : NOT
    ;

booleanPrimary
    : booleanPrimary IS NOT? (NULL | booleanLiterals)
    | DISTINCT predicate
    | booleanPrimary comparisonOperator predicate
    | predicate
    ;

comparisonOperator
    : DEQ_ | EQ_ | NEQ_ | LTE_ | GTE_ | LT_ | GT_
    ;

predicate
    : bitExpr NOT? IN select
    | bitExpr GLOBAL? NOT? IN LP_ expr (COMMA_ expr)* RP_ (AND predicate)?
    | bitExpr NOT? BETWEEN bitExpr AND predicate
    | bitExpr NOT? (LIKE | ILIKE) simpleExpr
    | bitExpr
    ;

bitExpr
    : bitExpr PLUS_ bitExpr
    | bitExpr MINUS_ bitExpr
    | bitExpr ASTERISK_ bitExpr
    | bitExpr SLASH_ bitExpr
    | bitExpr MOD_ bitExpr
    | bitExpr DOT_ bitExpr
    | bitExpr ARROW_ bitExpr
    | simpleExpr
    ;

simpleExpr
    : functionCall                    # functionExpr
    | literals                        # literalsExpr
    | caseExpression                  # caseExpr
    | columnName                      # columnExpr
    | select                          # subqueryExpr
    | simpleExpr OR_ simpleExpr       # orExpr
    | (PLUS_ | MINUS_) simpleExpr     # unaryExpr
    | LBT_ exprs? RBT_                # arrayExpr
    | LP_ exprs RP_                   # tupleExpr
    ;

functionCall
    : CAST LP_ castFunParams RP_                                        # castFunCall
    | EXTRACT LP_ extractFunParams RP_                                  # extractFunCall
    | SUBSTRING LP_ subStringFunParams RP_                              # substringFunCall
    | TRIM LP_ trimFunParams RP_                                        # trimFunCall
    | functionName LP_ DISTINCT? columnArgList? RP_ overClause?         # otherFunCall
    ;

trimFunParams
    : (BOTH | LEADING | TRAILING) STRING_ FROM expr
    ;

subStringFunParams
    : expr FROM expr (FOR expr)?
    ;

extractFunParams
    : intervalUnit FROM expr
    ;

castFunParams
    : expr AS columnTypeExpr
    ;

overClause
    : OVER ((LP_ windowExpr RP_) | identifier)
    ;

exprs
    : expr (COMMA_ expr)*
    ;

caseExpression
    : CASE simpleExpr? caseWhen+ caseElse? END
    ;

caseWhen
    : WHEN expr THEN expr
    ;

caseElse
    : ELSE expr
    ;

// Columns
columnTypeExpr
    : dataTypeName                                                                        // UInt64
    | dataTypeName LP_ columnName columnTypeExpr (COMMA_ columnName columnTypeExpr)* RP_  // Nested
    | dataTypeName LP_ enumValue (COMMA_ enumValue)* RP_                                  // Enum
    | dataTypeName LP_ columnTypeExpr (COMMA_ columnTypeExpr)* RP_                        // Array, Tuple
    | dataTypeName LP_ exprs? RP_                                                         // FixedString(N)
    ;

columnArgList
    : columnArgExpr (COMMA_ columnArgExpr)*
    ;

columnArgExpr
    : columnLambdaExpr | expr
    ;

columnLambdaExpr
	: ( LP_ identifiers RP_
    | identifiers
    ) ARROW_ expr
    ;

assignmentExprList
    : assignmentExpr (COMMA_ assignmentExpr)*
    ;

assignmentExpr
    : columnName EQ_ expr
    ;

// Tables
tableFunctionExpr: identifier LP_ tableArgList? RP_;
tableArgList: tableArgExpr (COMMA_ tableArgExpr)*;
tableArgExpr
    : objectName
    | tableFunctionExpr
    | literals
    ;

// Basics
literals
    : numberLiterals
    | stringLiterals
    | nullValueLiterals
    | booleanLiterals
    | intervalLiterals
    | bindLiterals
    | dateTimeLiterals
    ;

dateTimeLiterals
    : (DATE | TIMESTAMP) stringLiterals
    ;

bindLiterals
    : COLON_ identifier
    ;

intervalLiterals
    : INTERVAL (stringLiterals | stringLiterals intervalUnit | numberLiterals intervalUnit)
    ;

booleanLiterals
    : TRUE | FALSE
    ;

stringLiterals
    : STRING_
    ;

nullValueLiterals
    : NULL
    ;

numberLiterals
    : (PLUS_ | MINUS_)? (FLOATING_LITERAL_ | INTEGER_ | HEX_DIGIT_ | INF | NAN_SQL)
    ;

settingOptions:
    CONST | READONLY | WRITABLE | CHANGEABLE_IN_READONLY
    ;

identifiedByWith:
    PLAINTEXT_PWD | SHA256_PWD | SHA256_HASH | DOUBLE_SHA1_PWD | DOUBLE_SHA1_HASH | BCRYPT_PWD | BCRYPT_HASH
    ;

intervalUnit
    : SECOND | MINUTE | HOUR | DAY | WEEK | MONTH | QUARTER | YEAR
    ;

quotaMax
    : QUERIES | QUERY_SELECTS | QUERY_INSERTS | ERRORS | RESULT_ROWS | RESULT_BYTES | READ_ROWS | READ_BYTES | EXECUTION_TIME
    ;

accessStorageType
    : LOCAL_DIRECTORY | MEMORY | REPLICATED | USERS_XML | LDAP
    ;

systemCache
    : DNS | MARK | UNCOMPRESSED | COMPILED EXPRESSION
    ;

tableColumnPropertyType
    : ALIAS | CODEC | COMMENT | DEFAULT | MATERIALIZED | TTL
    ;

keyword
    // except NULL, INF, NAN_SQL
    : AFTER | ALIAS | ALL | ALTER | AND | ANTI | ANY | ARRAY | AS | ASCENDING | ASOF | AST | ASYNC | ATTACH | BETWEEN | BOTH | BY | CASE
    | CAST | CHECK | CLEAR | CLUSTER | CODEC | COLLATE | COLUMN | COMMENT | CONSTRAINT | CREATE | CROSS | CUBE | CURRENT | DATABASE
    | DATABASES | DATE | DEDUPLICATE | DEFAULT | DELAY | DELETE | DESCRIBE | DESC | DESCENDING | DETACH | DICTIONARIES | DICTIONARY | DISK
    | DISTINCT | DISTRIBUTED | DROP | ELSE | END | ENGINE | EVENTS | EXISTS | EXPLAIN | EXPRESSION | EXTRACT | FETCHES | FINAL | FIRST
    | FLUSH | FOR | FOLLOWING | FORMAT | FREEZE | FROM | FULL | FUNCTION | GLOBAL | GRANULARITY | GROUP | HAVING | HIERARCHICAL | ID
    | IF | ILIKE | IN | INDEX | INJECTIVE | INNER | INSERT | INTERVAL | INTO | IS | IS_OBJECT_ID | JOIN | FALSE | TRUE | KEY
    | KILL | LAST | LAYOUT | LEADING | LEFT | LIFETIME | LIKE | LIMIT | LIVE | LOCAL | LOGS | MATERIALIZE | MATERIALIZED | MAX | MERGES
    | MIN | MODIFY | MOVE | MUTATION | NO | NOT | NULLS | OFFSET | ON | OPTIMIZE | OR | ORDER | OUTER | OUTFILE | OVER | PARTITION
    | POPULATE | PRECEDING | PREWHERE | PRIMARY | RANGE | RELOAD | REMOVE | RENAME | REPLACE | REPLICA | REPLICATED | RIGHT | ROLLUP | ROW
    | ROWS | SAMPLE | SELECT | SEMI | SENDS | SET | SETTINGS | SHOW | SOURCE | START | STOP | SUBSTRING | SYNC | SYNTAX | SYSTEM | TABLE
    | TABLES | TEMPORARY | TEST | THEN | TIES | TIMEOUT | TIMESTAMP | TOTALS | TRAILING | TRIM | TRUNCATE | TO | TOP | TTL | TYPE
    | UNBOUNDED | UNION | UPDATE | USE | USING | UUID | VALUES | VIEW | VOLUME | WATCH | WHEN | WHERE | WINDOW | WITH
    ;

unreservedWord
    : ADD | ALL | AND | ANTI | ANY | ARRAY | AS | ASCENDING | ASOF | AST | ASYNC | ATTACH | BETWEEN | BOTH | BY | CASE | CAST | CHECK | CLEAR | CLUSTER
    | CODEC | COLLATE | COLUMN | COMMENT | CONSTRAINT | CREATE | CROSS | CUBE | DATABASE | DATABASES | DATE | DEDUPLICATE | DEFAULT | DELAY | DELETE | DESC | DESCENDING
    | DESCRIBE | DETACH | DICTIONARIES | DICTIONARY | DISK | DETACHED | DISTINCT | DISTRIBUTED | DROP | ELSE | END | ENGINE | EVENTS | EXISTS | EXPLAIN | EXPRESSION | EXTRACT
    | FETCHES | FINAL | FIRST | FLUSH | FOR | FORGET | FORMAT | FREEZE | UNFREEZE | FROM | FULL | FUNCTION | GLOBAL | GRANULARITY | GROUP | HAVING | HIERARCHICAL | ID
    | IF | ILIKE | IN | INDEX | INF | INJECTIVE | INNER | INSERT | INTERVAL | INTO | IS | IS_OBJECT_ID | JOIN | KEY | KILL | LAST | LAYOUT | LEADING | LEFT | LIFETIME | LIKE
    | LIMIT | LIVE | LOCAL | LOGS | MATERIALIZE | MATERIALIZED | MAX | MERGES | MIN | MODIFY | MOVE | MUTATION | NAN_SQL | NO | NOT | NULLS |OFFSET
    | ON | OPTIMIZE | OR | ORDER | OUTER | OUTFILE | PART | PARTITION | POPULATE | PREWHERE | PRIMARY | PROJECTION | QUERY | RANGE | REFRESH | RESET | RELOAD | REMOVE
    | RENAME | REPLACE | REPLICA | REPLICATED | RIGHT | ROLLUP | SAMPLE | SELECT | SEMI | SENDS | SET | SETTING | SETTINGS | SHOW | SOURCE | START | STOP | SUBSTRING
    | SYNC | SYNTAX | SYSTEM | TABLE | TABLES | TEMPORARY | TEST | THEN | TIES | TIMEOUT | TIMESTAMP | TO | TOP | TOTALS | TRAILING | TRIM | TRUNCATE | TTL | TYPE | UNION | UPDATE
    | USE | USING | UUID | VALUES | VIEW | VOLUME | WATCH | WHEN | WHERE | WITH | FALSE | TRUE | FOLLOWING | PRECEDING | CURRENT | OVER | ROWS | ROW
    | UNBOUNDED | WINDOW | NAME | FETCH | USER | IDENTIFIED | AUTHENTICATION | METHODS | NEW | PLAINTEXT_PWD | BCRYPT_PWD | BCRYPT_HASH | SHA256_PWD | SHA256_HASH
    | DOUBLE_SHA1_PWD | DOUBLE_SHA1_HASH | NO_PASSWORD | LDAP | KERBEROS | SSL | SSH_KEY | HTTP | SERVER | REALM | CN | SAN | SCHEME | HOST | NONE | REGEXP | IP | ROLE
    | EXCEPT | GRANTEES | VALID | UNTIL | READONLY | WRITABLE | PROFILE | PROFILES | QUOTA | KEYED | RANDOMIZED | LIMITS | TRACKING | ONLY | QUERIES| QUERY_SELECTS
    | QUERY_INSERTS | ERRORS | RESULT_ROWS | RESULT_BYTES | READ_ROWS | READ_BYTES | EXECUTION_TIME | CONST | CHANGEABLE_IN_READONLY | POLICY | PERMISSIVE | RESTRICTIVE
    | EMBEDDED | DNS | MARK | UNCOMPRESSED | COMPILED | CACHE | ZKPATH | CONFIG | SHUTDOWN | MOVES | REPLICATION | QUEUES | STRICT | LIGHTWEIGHT | PULL | RESTART | REPLICAS
    | PROCESSLIST | GRANTS | IMPLICIT | CURRENT_USER | TREE | PLAN | PIPELINE | ESTIMATE | OVERRIDE | RUN_PASSES | DUMP_PASSES | PASSES | COLUMNS | GRANT | SHOW_USERS
    | SHOW_ROLES | SHOW_ROW_POLICIES | SHOW_QUOTAS | SHOW_SETTINGS_PROFILES | ADMIN | INTROSPECTION | ADDRESS_TO_LINE | ADDRESS_TO_SYMBOL | DEMANGLE | SOURCES | URL | SQLITE
    | S3  | AZURE | FILE | HDFS | HIVE | JDBC | KAFKA | MONGO | MYSQL | NATS | ODBC | POSTGRES | RABBITMQ | REDIS | REMOTE | DICTGET | OPTION | ACCESS | MANAGEMENT | POLICIES
    | FUNCTIONS | PERMANENTLY | EMPTY | NAMED | COLLECTION | FORCE | EXCHANGE | PARALLEL | LOCAL_DIRECTORY | MEMORY | USERS_XML | UNDROP | REVOKE | MERGE_TREE | REPLICATED_TREE
    | REPLICATED_COLLAPSING_MERGETREE | REPLACING_TREE | SUMMING_TREE | AGGREGATING_TREE | COLLAPSING_TREE | VERSIONED_TREE | GRAPHITE_TREE | VERSIONED_COLLAPS
    | GRAPHITE_MERGE_TREE | LOG | STRIPE_LOG | TINY_LOG | AZURE_STORAGE | DELTA_LAKE | EMBEDDED_ROCKSDB | EXTERNAL_DISTRIBUTED | TIME_SERIES | HUDI | ICEBERG_LOCAL
    | MATERIALIZED_POSTGRESQL | MONGODB | AZURE_QUEUE | S3_QUEUE | MERGE | EXECUTABLE | ATOMIC | LAZY | BUFFER | GENERATE_RANDOM | KEEPER_MAP | FILE_LOG | POSTGRESQL
    | CLONE | EPHEMERAL | ASSUME | STRUCTURE | LIFETIME_MIN | LIFETIME_MAX | RANGE_MIN | RANGE_MAX | DEFINER | SQL | SECURITY | INVOKER | EVERY | RANDOMIZE | DEPENDS
    | APPEND | WATERMARK | ALLOWED_LATENESS | STRICTLY_ASCENDING | STATISTICS | APPLY | DELETED | MASK | LOCALHOST | OVERRIDABLE | INTERSECT | NEXT | QUALIFY
    | RECURSIVE | INFILE | PASTE
    ;

engineType
    : MERGE_TREE | REPLICATED_TREE | REPLICATED_COLLAPSING_MERGETREE | REPLACING_TREE | SUMMING_TREE | AGGREGATING_TREE | COLLAPSING_TREE
    | VERSIONED_TREE | GRAPHITE_TREE  | VERSIONED_COLLAPS | GRAPHITE_MERGE_TREE | LOG | STRIPE_LOG | TINY_LOG | AZURE_STORAGE | DELTA_LAKE
    | EMBEDDED_ROCKSDB | EXTERNAL_DISTRIBUTED | TIME_SERIES | HDFS | HIVE | HUDI | ICEBERG_LOCAL | JDBC | KAFKA | MATERIALIZED_POSTGRESQL
    | MONGODB | MYSQL | NATS | ODBC | POSTGRESQL | RABBITMQ  | REDIS | S3 | AZURE_QUEUE | S3_QUEUE | SQLITE | DISTRIBUTED | DICTIONARY
    | MERGE | EXECUTABLE | FILE | JOIN | URL | ATOMIC | LAZY | REPLICATED | MEMORY | BUFFER | GENERATE_RANDOM | KEEPER_MAP | FILE_LOG
    ;

globalLocalClause
    : GLOBAL | LOCAL
    ;

joinType
    : LEFT | RIGHT | FULL | CROSS | INNER | PASTE
    ;

joinRange
    : OUTER | SEMI | ANTI | ANY | ALL | ASOF
    ;

// |intervalUnit| can't be an alias, otherwise 'INTERVAL 1 SOMETHING' becomes ambiguous.
alias
    : IDENTIFIER_
    | DOUBLE_QUOTED_TEXT
    | BQUOTA_STRING
    | keyword
    | unreservedWord
    | QUESTION_
    ;

identifier
    : alias
    | intervalUnit
    ;

// NULL can be only 'Null' here.
identifierOrNull
    : identifier | NULL
    ;

identifiers
    : identifier (COMMA_ identifier)*
    ;

identifierOrCurrentUsers
    : (identifier | CURRENT_USER) (COMMA_ (identifier | CURRENT_USER))*
    ;

enumValue
    : STRING_ EQ_ numberLiterals
    ;

engineTypeIdentifier
    : identifierOrNull | engineType
    ;

ipAddress
    : LOCALHOST
    | MOD_
    | (INTEGER_ DOT_ INTEGER_ DOT_ INTEGER_ DOT_ INTEGER_)
    | identifier (DOT_ identifier)*
    | STRING_
    ;

keyedByIdentifier
    : username
    | ipAddress
    | STRING_
    | identifier
    | (identifier | STRING_) COMMA_ username
    | (identifier | STRING_) COMMA_ ipAddress
    ;

/** object name **/
name
    : identifier
    ;

databaseName
    : identifier
    ;

username
    : identifier (AT_ ipAddress )?
    ;

usernameOrRoleList
    : username (COMMA_ username)*
    ;

policyName
    : identifier
    ;

profileName
    : identifier
    ;

roleName
    : identifier
    ;

quotaName
    : identifier
    ;

namedCollectionName
    : identifier
    ;

owner
    : identifier
    ;

tableName
    : (owner DOT_)? name
    ;

viewName
    : (owner DOT_)? name
    ;

objectName
    : (owner DOT_)? name
    ;

materializedViewName
    : (owner DOT_)? name
    ;

liveViewName
    : (owner DOT_)? name
    ;

dictionaryName
    : (owner DOT_)? name
    ;

functionName
    : (owner DOT_)? name
    ;

windowViewName
    : (owner DOT_)? name
    ;

columnName
    : (owner DOT_)* name
    ;

columnNamesWithParentheses
    : LP_ columnNames RP_
    ;

columnNames
    : columnName (COMMA_ columnName)*
    ;

indexName
    : (owner DOT_)? name
    ;

constraintName
    : identifier
    ;

projectionName
    : (owner DOT_)? name
    ;

dataTypeName
    : identifier
    ;