lexer grammar ClickHouseLexer;

options {
    caseInsensitive = true;
}

ADD                             : 'ADD';
AFTER                           : 'AFTER';
ALIAS                           : 'ALIAS';
ALL                             : 'ALL';
ALTER                           : 'ALTER';
AND                             : 'AND';
ANTI                            : 'ANTI';
ANY                             : 'ANY';
ARRAY                           : 'ARRAY';
AS                              : 'AS';
ASCENDING                       : 'ASC'|'ASCENDING';
ASOF                            : 'ASOF';
AST                             : 'AST';
ASYNC                           : 'ASYNC';
ATTACH                          : 'ATTACH';
BETWEEN                         : 'BETWEEN';
BOTH                            : 'BOTH';
BY                              : 'BY';
CASE                            : 'CASE';
CAST                            : 'CAST';
CHECK                           : 'CHECK';
CLEAR                           : 'CLEAR';
CLUSTER                         : 'CLUSTER';
CODEC                           : 'CODEC';
COLLATE                         : 'COLLATE';
COLUMN                          : 'COLUMN';
COMMENT                         : 'COMMENT';
CONSTRAINT                      : 'CONSTRAINT';
CREATE                          : 'CREATE';
CROSS                           : 'CROSS';
CUBE                            : 'CUBE';
DATABASE                        : 'DATABASE';
DATABASES                       : 'DATABASES';
DATE                            : 'DATE';
DAY                             : 'DAY';
DEDUPLICATE                     : 'DEDUPLICATE';
DEFAULT                         : 'DEFAULT';
DELAY                           : 'DELAY';
DELETE                          : 'DELETE';
DESC                            : 'DESC';
DESCENDING                      : 'DESCENDING';
DESCRIBE                        : 'DESCRIBE';
DETACH                          : 'DETACH';
DICTIONARIES                    : 'DICTIONARIES';
DICTIONARY                      : 'DICTIONARY';
DISK                            : 'DISK';
DETACHED                        : 'DETACHED';
DISTINCT                        : 'DISTINCT';
DISTRIBUTED                     : 'DISTRIBUTED';
DROP                            : 'DROP';
ELSE                            : 'ELSE';
END                             : 'END';
ENGINE                          : 'ENGINE';
EVENTS                          : 'EVENTS';
EXISTS                          : 'EXISTS';
EXPLAIN                         : 'EXPLAIN';
EXPRESSION                      : 'EXPRESSION';
EXTRACT                         : 'EXTRACT';
FETCHES                         : 'FETCHES';
FINAL                           : 'FINAL';
FIRST                           : 'FIRST';
FLUSH                           : 'FLUSH';
FOR                             : 'FOR';
FORGET                          : 'FORGET';
FORMAT                          : 'FORMAT';
FREEZE                          : 'FREEZE';
UNFREEZE                        : 'UNFREEZE';
FROM                            : 'FROM';
FULL                            : 'FULL';
FUNCTION                        : 'FUNCTION';
GLOBAL                          : 'GLOBAL';
GRANULARITY                     : 'GRANULARITY';
GROUP                           : 'GROUP';
HAVING                          : 'HAVING';
HIERARCHICAL                    : 'HIERARCHICAL';
HOUR                            : 'HOUR';
ID                              : 'ID';
IF                              : 'IF';
ILIKE                           : 'ILIKE';
IN                              : 'IN';
INDEX                           : 'INDEX';
INF                             : 'INF'|'INFINITY';
INJECTIVE                       : 'INJECTIVE';
INNER                           : 'INNER';
INSERT                          : 'INSERT';
INTERVAL                        : 'INTERVAL';
INTO                            : 'INTO';
IS                              : 'IS';
IS_OBJECT_ID                    : 'IS_OBJECT_ID';
JOIN                            : 'JOIN';
KEY                             : 'KEY';
KILL                            : 'KILL';
LAST                            : 'LAST';
LAYOUT                          : 'LAYOUT';
LEADING                         : 'LEADING';
LEFT                            : 'LEFT';
LIFETIME                        : 'LIFETIME';
LIKE                            : 'LIKE';
LIMIT                           : 'LIMIT';
LIVE                            : 'LIVE';
LOCAL                           : 'LOCAL';
LOGS                            : 'LOGS';
MATERIALIZE                     : 'MATERIALIZE';
MATERIALIZED                    : 'MATERIALIZED';
MAX                             : 'MAX';
MERGES                          : 'MERGES';
MIN                             : 'MIN';
MINUTE                          : 'MINUTE';
MODIFY                          : 'MODIFY';
MONTH                           : 'MONTH';
MOVE                            : 'MOVE';
MUTATION                        : 'MUTATION';
NAN_SQL                         : 'NAN'; // conflicts with macro NAN
NO                              : 'NO';
NOT                             : 'NOT';
NULL                            : 'NULL'; // conflicts with macro NULL
NULLS                           : 'NULLS';
OFFSET                          : 'OFFSET';
ON                              : 'ON';
OPTIMIZE                        : 'OPTIMIZE';
OR                              : 'OR';
ORDER                           : 'ORDER';
OUTER                           : 'OUTER';
OUTFILE                         : 'OUTFILE';
PART                            : 'PART';
PARTITION                       : 'PARTITION';
POPULATE                        : 'POPULATE';
PREWHERE                        : 'PREWHERE';
PRIMARY                         : 'PRIMARY';
PROJECTION                      : 'PROJECTION';
QUARTER                         : 'QUARTER';
QUERY                           : 'QUERY';
RANGE                           : 'RANGE';
REFRESH                         : 'REFRESH';
RESET                           : 'RESET';
RELOAD                          : 'RELOAD';
REMOVE                          : 'REMOVE';
RENAME                          : 'RENAME';
REPLACE                         : 'REPLACE';
REPLICA                         : 'REPLICA';
REPLICATED                      : 'REPLICATED';
RIGHT                           : 'RIGHT';
ROLLUP                          : 'ROLLUP';
SAMPLE                          : 'SAMPLE';
SECOND                          : 'SECOND';
SELECT                          : 'SELECT';
SEMI                            : 'SEMI';
SENDS                           : 'SENDS';
SET                             : 'SET';
SETTING                         : 'SETTING';
SETTINGS                        : 'SETTINGS';
SHOW                            : 'SHOW';
SOURCE                          : 'SOURCE';
START                           : 'START';
STOP                            : 'STOP';
SUBSTRING                       : 'SUBSTRING';
SYNC                            : 'SYNC';
SYNTAX                          : 'SYNTAX';
SYSTEM                          : 'SYSTEM';
TABLE                           : 'TABLE';
TABLES                          : 'TABLES';
TEMPORARY                       : 'TEMPORARY';
TEST                            : 'TEST';
THEN                            : 'THEN';
TIES                            : 'TIES';
TIMEOUT                         : 'TIMEOUT';
TIMESTAMP                       : 'TIMESTAMP';
TO                              : 'TO';
TOP                             : 'TOP';
TOTALS                          : 'TOTALS';
TRAILING                        : 'TRAILING';
TRIM                            : 'TRIM';
TRUNCATE                        : 'TRUNCATE';
TTL                             : 'TTL';
TYPE                            : 'TYPE';
UNION                           : 'UNION';
UPDATE                          : 'UPDATE';
USE                             : 'USE';
USING                           : 'USING';
UUID                            : 'UUID';
VALUES                          : 'VALUES';
VIEW                            : 'VIEW';
VOLUME                          : 'VOLUME';
WATCH                           : 'WATCH';
WEEK                            : 'WEEK';
WHEN                            : 'WHEN';
WHERE                           : 'WHERE';
WITH                            : 'WITH';
YEAR                            : 'YEAR' |'YYYY';
FALSE                           : 'false';
TRUE                            : 'true';
FOLLOWING                       : 'FOLLOWING';
PRECEDING                       : 'PRECEDING';
CURRENT                         : 'CURRENT';
OVER                            : 'OVER';
ROWS                            : 'ROWS';
ROW                             : 'ROW';
UNBOUNDED                       : 'UNBOUNDED';
WINDOW                          : 'WINDOW';
NAME                            : 'NAME';
FETCH                           : 'FETCH';
USER                            : 'USER';
IDENTIFIED                      : 'IDENTIFIED';
AUTHENTICATION                  : 'AUTHENTICATION';
METHODS                         : 'METHODS';
NEW                             : 'NEW';
PLAINTEXT_PWD                   : 'plaintext_password';
BCRYPT_PWD                      : 'bcrypt_password';
BCRYPT_HASH                     : 'bcrypt_hash';
SHA256_PWD                      : 'sha256_password';
SHA256_HASH                     : 'sha256_hash';
DOUBLE_SHA1_PWD                 : 'double_sha1_password';
DOUBLE_SHA1_HASH                : 'double_sha1_hash';
NO_PASSWORD                     : 'NO_PASSWORD';
LDAP                            : 'ldap';
KERBEROS                        : 'kerberos';
SSL                             : 'ssl_certificate';
SSH_KEY                         : 'ssh_key';
HTTP                            : 'http';
SERVER                          : 'SERVER';
REALM                           : 'REALM';
CN                              : 'CN';
SAN                             : 'SAN';
SCHEME                          : 'SCHEME';
HOST                            : 'HOST';
NONE                            : 'NONE';
REGEXP                          : 'REGEXP';
IP                              : 'IP';
ROLE                            : 'ROLE';
EXCEPT                          : 'EXCEPT';
GRANTEES                        : 'GRANTEES';
VALID                           : 'VALID';
UNTIL                           : 'UNTIL';
READONLY                        : 'READONLY';
WRITABLE                        : 'WRITABLE';
PROFILE                         : 'PROFILE';
PROFILES                        : 'PROFILES';
QUOTA                           : 'QUOTA';
KEYED                           : 'KEYED';
RANDOMIZED                      : 'RANDOMIZED';
LIMITS                          : 'LIMITS';
TRACKING                        : 'TRACKING';
ONLY                            : 'ONLY';
QUERIES                         : 'queries';
QUERY_SELECTS                   : 'query_selects';
QUERY_INSERTS                   : 'query_inserts';
ERRORS                          : 'errors';
RESULT_ROWS                     : 'result_rows';
RESULT_BYTES                    : 'result_bytes';
READ_ROWS                       : 'read_rows';
READ_BYTES                      : 'read_bytes';
EXECUTION_TIME                  : 'execution_time';
CONST                           : 'CONST';
CHANGEABLE_IN_READONLY          : 'CHANGEABLE_IN_READONLY';
POLICY                          : 'POLICY';
PERMISSIVE                      : 'PERMISSIVE';
RESTRICTIVE                     : 'RESTRICTIVE';
EMBEDDED                        : 'EMBEDDED';
DNS                             : 'DNS';
MARK                            : 'MARK';
UNCOMPRESSED                    : 'UNCOMPRESSED';
COMPILED                        : 'COMPILED';
CACHE                           : 'CACHE';
ZKPATH                          : 'ZKPATH';
CONFIG                          : 'CONFIG';
SHUTDOWN                        : 'SHUTDOWN';
MOVES                           : 'MOVES';
REPLICATION                     : 'REPLICATION';
QUEUES                          : 'QUEUES';
STRICT                          : 'STRICT';
LIGHTWEIGHT                     : 'LIGHTWEIGHT';
PULL                            : 'PULL';
RESTART                         : 'RESTART';
REPLICAS                        : 'REPLICAS';
PROCESSLIST                     : 'PROCESSLIST';
GRANTS                          : 'GRANTS';
IMPLICIT                        : 'IMPLICIT';
CURRENT_USER                    : 'CURRENT_USER';
TREE                            : 'TREE';
PLAN                            : 'PLAN';
PIPELINE                        : 'PIPELINE';
ESTIMATE                        : 'ESTIMATE';
OVERRIDE                        : 'OVERRIDE';
RUN_PASSES                      : 'run_passes';
DUMP_PASSES                     : 'dump_passes';
PASSES                          : 'passes';
COLUMNS                         : 'COLUMNS';
GRANT                           : 'GRANT';
SHOW_USERS                      : 'SHOW_USERS';
SHOW_ROLES                      : 'SHOW_ROLES';
SHOW_ROW_POLICIES               : 'SHOW_ROW_POLICIES';
SHOW_QUOTAS                     : 'SHOW_QUOTAS';
SHOW_SETTINGS_PROFILES          : 'SHOW_SETTINGS_PROFILES';
ADMIN                           : 'ADMIN';
INTROSPECTION                   : 'INTROSPECTION';
ADDRESS_TO_LINE                 : 'addressToLine';
ADDRESS_TO_SYMBOL               : 'addressToSymbol';
DEMANGLE                        : 'demangle';
SOURCES                         : 'SOURCES';
URL                             : 'URL';
SQLITE                          : 'SQLITE';
S3                              : 'S3';
AZURE                           : 'AZURE';
FILE                            : 'FILE';
HDFS                            : 'HDFS';
HIVE                            : 'HIVE';
JDBC                            : 'JDBC';
KAFKA                           : 'KAFKA';
MONGO                           : 'MONGO';
MYSQL                           : 'MYSQL';
NATS                            : 'NATS';
ODBC                            : 'ODBC';
POSTGRES                        : 'POSTGRES';
RABBITMQ                        : 'RABBITMQ';
REDIS                           : 'REDIS';
REMOTE                          : 'REMOTE';
DICTGET                         : 'dictGet' | 'dictHas' | 'dictGetHierarchy' | 'dictIsIn';
OPTION                          : 'OPTION';
ACCESS                          : 'ACCESS';
MANAGEMENT                      : 'MANAGEMENT';
POLICIES                        : 'POLICIES';
FUNCTIONS                       : 'FUNCTIONS';
PERMANENTLY                     : 'PERMANENTLY';
EMPTY                           : 'EMPTY';
NAMED                           : 'NAMED';
COLLECTION                      : 'COLLECTION';
FORCE                           : 'FORCE';
EXCHANGE                        : 'EXCHANGE';
PARALLEL                        : 'PARALLEL';
LOCAL_DIRECTORY                 : 'local_directory';
MEMORY                          : 'memory';
USERS_XML                       : 'users_xml';
UNDROP                          : 'UNDROP';
REVOKE                          : 'REVOKE';
MERGE_TREE                      : 'MERGETREE';
REPLICATED_TREE                 : 'REPLICATEDMERGETREE';
REPLICATED_COLLAPSING_MERGETREE : 'REPLICATEDCOLLAPSINGMERGETREE';
REPLACING_TREE                  : 'REPLACINGMERGETREE';
SUMMING_TREE                    : 'SUMMINGMERGETREE';
AGGREGATING_TREE                : 'AGGREGATINGMERGETREE';
COLLAPSING_TREE                 : 'COLLAPSINGMERGETREE';
VERSIONED_TREE                  : 'VERSIONEDCOLLAPSINGMERGETREE';
GRAPHITE_TREE                   : 'GRAPHITETREE';
VERSIONED_COLLAPS               : 'VERSIONEDCOLLAPS';
GRAPHITE_MERGE_TREE             : 'GRAPHITEMERGETREE';
LOG                             : 'LOG';
STRIPE_LOG                      : 'STRIPELOG';
TINY_LOG                        : 'TINYLOG';
AZURE_STORAGE                   : 'AZUREBLOBSTORAGE';
DELTA_LAKE                      : 'DELTALAKE';
EMBEDDED_ROCKSDB                : 'EMBEDDEDROCKSDB';
EXTERNAL_DISTRIBUTED            : 'EXTERNALDISTRIBUTED';
TIME_SERIES                     : 'TIMESERIES';
HUDI                            : 'HUDI';
ICEBERG_LOCAL                   : 'ICEBERGLOCAL';
MATERIALIZED_POSTGRESQL         : 'MATERIALIZEDPOSTGRESQL';
MONGODB                         : 'MONGODB';
AZURE_QUEUE                     : 'AZUREQUEUE';
S3_QUEUE                        : 'S3QUEUE';
MERGE                           : 'MERGE';
EXECUTABLE                      : 'EXECUTABLE';
ATOMIC                          : 'ATOMIC';
LAZY                            : 'LAZY';
BUFFER                          : 'BUFFER';
GENERATE_RANDOM                 : 'GENERATERANDOM';
KEEPER_MAP                      : 'KEEPERMAP';
FILE_LOG                        : 'FILELOG';
POSTGRESQL                      : 'POSTGRESQL';
CLONE                           : 'CLONE';
EPHEMERAL                       : 'EPHEMERAL';
ASSUME                          : 'ASSUME';
STRUCTURE                       : 'STRUCTURE';
LIFETIME_MIN                    : 'LIFETIME_MIN';
LIFETIME_MAX                    : 'LIFETIME_MAX';
RANGE_MIN                       : 'RANGE_MIN';
RANGE_MAX                       : 'RANGE_MAX';
DEFINER                         : 'DEFINER';
SQL                             : 'SQL';
SECURITY                        : 'SECURITY';
INVOKER                         : 'INVOKER';
EVERY                           : 'EVERY';
RANDOMIZE                       : 'RANDOMIZE';
DEPENDS                         : 'DEPENDS';
APPEND                          : 'APPEND';
WATERMARK                       : 'WATERMARK';
ALLOWED_LATENESS                : 'ALLOWED_LATENESS';
STRICTLY_ASCENDING              : 'STRICTLY_ASCENDING';
STATISTICS                      : 'STATISTICS';
APPLY                           : 'APPLY';
DELETED                         : 'DELETED';
MASK                            : 'MASK';
LOCALHOST                       : 'LOCALHOST';
OVERRIDABLE                     : 'OVERRIDABLE';
INTERSECT                       : 'INTERSECT';
NEXT                            : 'NEXT';
QUALIFY                         : 'QUALIFY';
RECURSIVE                       : 'RECURSIVE';
INFILE                          : 'INFILE';
PASTE                           : 'PASTE';

DOT_ASTERISK_                   : '.*';
ARROW_                          : '->';
ASTERISK_                       : '*';
BQ_                             : '`';
BACKSLASH_                      : '\\';
COLON_                          : ':';
COMMA_                          : ',';
OR_                             : '||';
MINUS_                          : '-';
DOT_                            : '.';
DEQ_                            : '==';
EQ_                             : '=';
GTE_                            : '>=';
GT_                             : '>';
LBE_                            : '{';
LBT_                            : '[';
LTE_                            : '<=';
LP_                             : '(';
LT_                             : '<';
NEQ_                            : '!=' | '<>';
MOD_                            : '%';
PLUS_                           : '+';
QUESTION_                       : '?';
DQ_                             : '"';
SQ_                             : '\'';
RBE_                            : '}';
RBT_                            : ']';
RP_                             : ')';
SEMI_                           : ';';
SLASH_                          : '/';
UL_                             : '_';
AT_                             : '@';

BLOCK_COMMENT                   : '/*' .*? '*/'                                          -> channel(HIDDEN);
INLINE_COMMENT                  : ('-- ' | '#' | '#!') ~('\n'|'\r')* ('\n' | '\r' | EOF) -> channel(HIDDEN);
WS                              : [\u000B\u000C\t\r\n ]                                  -> skip;  // '\n' can be part of multiline single query

fragment LETTER_                : [A-Z];
fragment OCT_DIGIT_             : [0-7];
fragment INT_                   : [0-9];
fragment HEX_                   : [0-9A-F];
fragment X                      : [X];
fragment E                      : [E];
fragment P                      : [P];

INTEGER_                        : INT_+;
HEX_DIGIT_                      : '0' X HEX_+;
STRING_                         : SQ_ ( ~([\\']) | (BACKSLASH_ .) | (SQ_ SQ_) )* SQ_;
DOUBLE_QUOTED_TEXT              : DQ_ ( ~([\\"]) | (BACKSLASH_ .) | (DQ_ DQ_) )* DQ_;
BQUOTA_STRING                   : BQ_ ( ~([\\`]) | (BACKSLASH_ .) | (BQ_ BQ_) )* BQ_;

IDENTIFIER_
    : [A-Z_$0-9\u0080-\uFFFF]*?[A-Z_$\u0080-\uFFFF]+?[A-Z_$0-9\u0080-\uFFFF]*
    ;

FLOATING_LITERAL_
    : HEX_DIGIT_ DOT_ HEX_* (P | E) (PLUS_ | MINUS_)? INTEGER_
    | HEX_DIGIT_ (P | E) (PLUS_ | MINUS_)? INTEGER_
    | INTEGER_ DOT_ INT_* E (PLUS_ | MINUS_)? INTEGER_
    | DOT_ INTEGER_ E (PLUS_ | MINUS_)? INTEGER_
    | INTEGER_ E (PLUS_ | MINUS_)? INTEGER_
    | DOT_ INTEGER_
    | INTEGER_ DOT_ INTEGER_?  // can't move this to the lexer or it will break nested tuple access: t.1.2
    ;