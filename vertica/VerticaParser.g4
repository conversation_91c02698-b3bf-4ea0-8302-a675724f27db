parser grammar VerticaParser;

options {
	tokenVocab = VerticaLexer;
}
multiRoot
    : root+
    ;

root
:
    ( activateDirectedQuery
     | alterAccessPolicy
     | alterAuthentication
     | alterCaBundle
     | alterDataLeader
     | alterDatabase
     | alterFaultGroup
     | alterAggregateFunction
     | alterFunction
     | alterParserOrSource
     | alterHcatalogSchema
     | alterLibrary
     | alterModel
     | alterNetworkInterface
     | alterNode
     | alterNotifier
     | alterProjection
     | alterProfile
     | alterProfileRename
     | alterResourcePool
     | alterRoleRename
     | alterSchema
     | alterSequence
     | alterSession
     | alterSubnet
     | alterTableRename
     | alterTable
     | alterUser
     | alterView
     | beginTransaction
     | comment
     | commit
     | connect
     | copy
     | copyLocal
     | copyFromVertica
     | createAccessPolicy
     | createAuthentication
     | createCertificate
     | createBranch
     | createExternalTableAsCopy
     | createDirectedQuery
     | createFaultGroup
     | createFlexTable
     | createFlexExternalTableAsCopy
     | createFunction
     | createHcatalogSchema
     | createLibrary
     | createLoadBalanceGroup
     | createLocalTemporaryView
     | createLocation
     | createNetworkAddress
     | createNetworkInterface
     | createNotifier
     | createProcedure
     | createProfile
     | createProjection
     | createProjectionLiveAggregateProjections
     | createProjectionUdtfs
     | createResourcePool
     | createRole
     | createRoutingRule
     | createSchema
     | createSequence
     | createSubnet
     | createTable
     | createTemporaryTable
     | createTextIndex
     | createUser
     | createView
     | delete
     | deactivateDirectedQuery
     | dropDirectedQuery
     | getDirectedQuery
     | saveQuery
     | disconnect
     | dropAccessPolicy
     | dropAggregateFunction
     | dropAnalyticFunction
     | dropAuthentication
     | dropBranch
     | dropFaultGroup
     | dropFunction
     | dropSource
     | dropFilter
     | dropParser
     | dropModel
     | dropLibrary
     | dropLoadBalanceGroup
     | dropNetworkAddress
     | dropNetworkInterface
     | dropNotifier
     | dropProcedure
     | dropProfile
     | dropProjection
     | dropResourcePool
     | dropRole
     | dropRoutingRule
     | dropSchema
     | dropSequence
     | dropSubnet
     | dropTable
     | dropTextIndex
     | dropTransformFunction
     | dropUser
     | dropView
     | end
     | explain
     | grant
     | insert
     | merge
     | profile
     | releaseSavepoint
     | revoke
     | rollback
     | rollbackToSavepoint
     | savepoint
     | select
     | setDatestyle
     | setEscapeStringWarning
     | setIntervalstyle
     | setLocale
     | setRole
     | setSearchPath
     | setSessionAuthorization
     | setSessionAutocommit
     | setSessionCharacteristicsAsTransaction
     | setSessionGraceperiod
     | setSessionIdlesessiontimeout
     | setSessionMemorycap
     | setSessionMultipleactiveresultsets
     | setSessionResourcePool
     | setSessionRuntimecap
     | setSessionTempspacecap
     | setSessionWorkload
     | setStandardConformingStrings
     | setTimeZone
     | show
     | showCurrent
     | showDatabases
     | showNode
     | showSession
     | showUser
     | startTransaction
     | truncateTable
     | update
     | createCaBundle
     | alterLoadBalanceGroup
     | alterNetworkAddress
     | alterProcedure
     | alterRoutingRule
     | alterSchedule
     | alterSubcluster
     | alterTlsConfiguration
     | alterTrigger
     | call
     | createKey
     | createScheduledQuery
     | createTlsConfiguration
     | createTrigger
     | doStatement
     | dropCaBundle
     | dropCertificate
     | dropDataLoader
     | dropKey
     | dropNamespace
     | dropSchedule
     | dropTlsConfiguration
     | dropTrigger
     | executeDataLoader
     | exportOrImport
     | lockTable
     | replicate
     | with
     ) SEMI_? SLASH_? EOF?
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-session-authorization/
setSessionAuthorization
    : SET SESSION AUTHORIZATION (userName | DEFAULT)
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/replicate/
replicate
    : REPLICATE (tableName | schemaName | DQ_ ((DOT_ namespace=identifier DOT_)? tableName) DQ_
                | (INCLUDE incPattern=identifier (EXCLUDE extPattern=identifier)?))
                (FROM |TO) dbname
                (TARGET_NAMESPACE name)?
    ;


//https://docs.vertica.com/24.1.x/en/sql-reference/statements/lock-table/
lockTable
    : LOCK TABLE? tableName (COMMA_ tableName)*
          IN lockType MODE
          NOWAIT?
    ;
lockType
    : SHARE
     | INSERT
     | INSERT VALIDATE
     | SHARE INSERT
     | EXCLUSIVE
     | NOT DELETE
     | USAGE
     | OWNER
    ;
exportOrImport
    : exportToExtra
    | exportToVertica
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/export-to-delimited/
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/export-to-json/
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/export-to-parquet/
exportToExtra
    : EXPORT hints? TO (DELIMITED | JSON | PARQUET)
              (LP_ DIRECTORY EQ_ path (COMMA_ keyValuePair (COMMA_ keyValuePair)*)? RP_)
                  overClause? AS select
    ;



//https://docs.vertica.com/24.1.x/en/sql-reference/statements/execute-data-loader/
executeDataLoader
    : EXECUTE DATA LOADER tableName
        ( WITH FILES (path (EXACT_PATH | EXPAND_GLOB)?) (COMMA_ (path (EXACT_PATH | EXPAND_GLOB)?))* )?
        (FORCE RETRY)?
        (BATCH_SIZE identifier)?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-trigger/
dropTrigger
    : DROP TRIGGER triggerReference (COMMA_ triggerReference)*
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-tls-config/#syntax
dropTlsConfiguration
    : DROP TLS CONFIGURATION identifier
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-schedule/
dropSchedule
    : DROP SCHEDULE scheduleReference (COMMA_ scheduleReference)* CASCADE?

    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-namespace/
dropNamespace
    : DROP NAMESPACE ifNotExistsClause? identifier CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-key/
dropKey
    : DROP KEY ifNotExistsClause? identifier ifNotExistsClause? (COMMA_ identifier)* CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-data-loader/
dropDataLoader
    : DROP DATA LOADER ifNotExistsClause?  tableName
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-certificate/
dropCertificate
    : DROP CERTIFICATE ifNotExistsClause? caCert  (COMMA_ caCert)*  CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-ca-bundle/
dropCaBundle
    : DROP CA BUNDLE ifNotExistsClause? caCert (COMMA_ caCert)* CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/do/
doStatement
    : DO (LANGUAGE lang)?  DOLLAR_ DOLLAR_ scopeAndStructure DOLLAR_ DOLLAR_
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-trigger/
createTrigger
    : CREATE TRIGGER ifNotExistsClause? triggerReference
    (ON SCHEDULE scheduleReference)
    (EXECUTE PROCEDURE procedure AS DEFINER)
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-tls-config/
createTlsConfiguration
    : CREATE TLS CONFIGURATION  identifier configurationClause*
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-schedule/
createScheduledQuery
    : CREATE SCHEDULE ifNotExistsClause? scheduleReference ( usingCronClause | usingDatetimesClause)
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-key/
createKey
    : CREATE (TEMP | TEMPORARY)? KEY name
             TYPE keyType
             (PASSWORD identifier)?
             (LENGTH integerNumber | AS identifier)

    ;
keyType
    : identifier
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-certificate/
createCertificate
    : CREATE (TEMP|TEMPORARY)? CA? CERTIFICATE caCert ( (AS cert=stringLiterals (KEY keyName=stringLiterals)?)
                                                    | ( SUBJECT subject=stringLiterals
                                                        (SIGNED BY singeBy=caCert)? (VALID FOR days=stringLiterals)?
                                                        (EXTENSIONS commaSeparatedKeyValuePairs)? (KEY privateKey=stringLiterals))
                                                   )
    ;

//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/call/
call
    : CALL procedureName parameterList
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-trigger/
alterTrigger
    : ALTER TRIGGER triggerReference triggerClause?
    ;
triggerClause
    : ownerClause
    | setSchemaClause
    | renameClause
    | procedureClause
    ;
procedureClause
    : PROCEDURE TO identifier    ;
triggerReference
    : (owner DOT_)* identifier
    ;
// https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-tls-config/
alterTlsConfiguration
    : ALTER TLS CONFIGURATION identifier (configurationClause)*
    ;
configurationClause
    : (CERTIFICATE (NULL| caCert))
    | (ADD CA CERTIFICATES caCert (COMMA_ caCert)*)
    | (REMOVE CA CERTIFICATES caCert (COMMA_ caCert)*)
    | (CIPHER SUITES identifier (COMMA_ identifier)*)
    | (TLSMODE value)
    | ownerClause
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-subcluster/
alterSubcluster
    : ALTER SUBCLUSTER identifier ( renameClause | (SET DEFAULT))
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-schedule/
alterSchedule
    : ALTER SCHEDULE scheduleReference (
    ownerClause
    | setSchemaClause
    | renameClause
    | usingCronClause
    | usingDatetimesClause
    )
    ;
usingDatetimesClause
    : USING DATETIMES timestampListClause
    ;
timestampListClause
    : LP_ ( identifier (COMMA_ identifier)*)? RP_
    ;
usingCronClause
    : USING CRON identifier
    ;

scheduleReference
    : ( owner DOT_)* identifier
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-routing-rule/
alterRoutingRule
    : ALTER ROUTING RULE (role| (FOR WORKLOAD identifier)) (
    renameClause
    | setRouteClause
    | setGroupClause
    | setWorkloadClause
    | setSubclusterClause
    | setPriorityClause
    )
    ;
setPriorityClause
    : SET PRIORITY TO numberLiterals
    ;
setSubclusterClause
    : SET SUBCLUSTER TO addList
    ;
setWorkloadClause
    : SET WORKLOAD TO identifier
    ;
setGroupClause
    : SET GROUP TO identifier
    ;
setRouteClause
    : SET ROUTE TO identifier
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-procedure-stored/
alterProcedure
    : ALTER PROCEDURE procedureName parameterList
    (
        (SECURITY (DEFINER| INVOKER))
         |renameClause
         | ownerClause
         | setSchemaClause
         | (SOURCE TO procedureSource)
         )
    ;

parameterItem
    : (IN|OUT)? identifier? dataTypes
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-network-address/
alterNetworkAddress
    : ALTER NETWORK ADDRESS identifier (
        renameClause
        | (SET TO address (PORT numberLiterals)?)
        | (ENABLE| DISABLE)
        )
    ;
alterLoadBalanceGroup
    : ALTER LOAD BALANCE GROUP identifier (
    renameClause
    | (SET FILTER TO address)
    | (SET POLICY TO policyClause)
    | (ADD (ADDRESS| (FAULT GROUP)| SUBCLUSTER) addList)
    | (DROP (ADDRESS| (FAULT GROUP)| SUBCLUSTER) dropList)
    )
    ;
policyClause
    : ROUNDROBIN| RANDOM| NONE
    ;
dropList
     : identifier (COMMA_ identifier)*
     ;
addList
    : identifier (COMMA_ identifier)*
    ;
parameterList
    :
    	LP_ ( (identifier|parameterItem) (COMMA_ (identifier|parameterItem))*)? RP_
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-function-statements/alter-parser/
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-function-statements/alter-source/
alterParserOrSource
    : ALTER (PARSER|SOURCE) functionName parameterList?
    (
        ownerClause
        | renameClause
        | setSchemaClause
        | setFencedClause
    )
    ;

setFencedClause
    : SET FENCED booleanLiterals
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-function-statements/alter-aggregate-function/
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-function-statements/alter-filter/
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-function-statements/alter-transform-function/
alterAggregateFunction
    : ALTER (AGGREGATE| ANALYTIC | FILTER | TRANSFORM) FUNCTION functionName parameterList?
    (
        ownerClause
        | renameClause
        | setSchemaClause
        | setFencedClause
    )
    ;
monitoringRetention // 没有具体规则，todo 补全
    : numberLiterals identifier+
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-data-loader/
alterDataLeader
    : ALTER DATA LOADER tableName (
    SET TO copy
    | RETRY LIMIT (NONE| DEFAULT | numberLiterals)
    | RETENTION INTERVAL monitoringRetention
    | renameClause
    | OWNER TO userName
    )
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-ca-bundle/
createCaBundle
    : CREATE CA BUNDLE identifier (CERTIFICATES caCert (COMMA_ caCert)*)?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-ca-bundle/
alterCaBundle
    : ALTER CA BUNDLE name addCertificates? removeCertificates? (OWNER TO userName)?
    ;
removeCertificates
    : REMOVE CERTIFICATES caCert (COMMA_ caCert)*
    ;
addCertificates
    : (ADD CERTIFICATES caCert (COMMA_ caCert)*)
    ;
caCert
    : identifier
    ;
// https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-access-policy/
alterAccessPolicy
:
	ALTER ACCESS POLICY ON tableName
	(
		( FOR COLUMN columnName predicates?)
		| ( FOR ROWS whereClause?)
	)
	(
		enableOrDisable
		| ( COPY TO TABLE tableName)
	)?
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-authentication/
alterAuthentication
:
	ALTER AUTHENTICATION authMethodName
	( enableOrDisable
        | (
            LOCAL | ( HOST ( NO? TLS )? address )
            )
		| renameClause
		| ( METHOD method)
		| (	SET commaSeparatedKeyValuePairs	)
		| (	PRIORITY integerNumber )
	)
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-db/
alterDatabase
    :
        ALTER DATABASE (dbname | DEFAULT)
        (
            (DROP ALL FAULT GROUP)
            | (	EXPORT ON (subnet | DEFAULT))
            | (	RESET STANDBY )
            | (	SET PARAMETER? commaSeparatedKeyValuePairs )
            | (	CLEAR PARAMETER? params	)
        )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-fault-group/
alterFaultGroup
    :
        ALTER FAULT GROUP faultGroup
        (   ( ADD NODE node )
            | (	DROP NODE node)
            | (	ADD FAULT GROUP childFaultGroup = faultGroup )
            | (	DROP FAULT GROUP childFaultGroup = faultGroup )
            | ( RENAME TO newDefaultGroup = faultGroup)
        )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-function-statements/alter-function-scalar/
alterFunction
    : ALTER FUNCTION functionName parameterList?
        (
            ownerClause
            | renameClause
            | setSchemaClause
            | setFencedClause
        )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-hcatalog-schema/
alterHcatalogSchema
    : ALTER HCATALOG SCHEMA schemaName SET hcatalogSchemaParam+
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-library/
alterLibrary
    : ALTER LIBRARY libraryName
        (
         DEPENDS depends = value
            ( LANGUAGE lang )?
        )? AS identifier
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-model/
alterModel
    : ALTER MODEL modelName
        (
            ownerClause
            | renameClause
            | setSchemaClause
            | ((INCLUDE | EXCLUDE | MATERIALIZE )  SCHEMA? PRIVILEGES)
        )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-network-interface/
alterNetworkInterface
    : ALTER NETWORK INTERFACE identifier renameClause
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-node/
alterNode
    : ALTER NODE identifier
        (
            ( EXPORT ON exportOn = identifier )
            | ( IS? nodeType = value )
            | ( REPLACE ( WITH standByNode = value )? )
            | RESET
            | ( SET PARAMETER? commaSeparatedKeyValuePairs )
            | ( CLEAR PARAMETER? params )
        )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/alter-statements/alter-notifier/
alterNotifier
    : ALTER NOTIFIER identifier (notifierParams)*
    ;

notifierParams
    :   enableOrDisable
        | (MAXPAYLOAD value)
        | (MAXMEMORYSIZE value)
        | (TLS CONFIGURATION  value)
        | (TLSMODE value)
        | (CA BUNDLE bundleName=value (CERTIFICATE certName=value)? )
        | (IDENTIFIED BY value)
        | (NO? CHECK COMMITTED)
        | (PARAMETERS value)
    ;

//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-projection/
alterProjection
    :  ALTER PROJECTION projectionName (
            renameClause
            | onPartitionRange
            | (ENABLE | DISABLE)
            | REMOVE PARTITION RANGE
        )
    ;
onPartitionRange
    :  ON PARTITION RANGE BETWEEN minVal=expression AND maxVal=expression
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-profile/
alterProfile
    : ALTER PROFILE identifier LIMIT
        ( passwordParameter )+
    ;

alterProfileRename
    : ALTER PROFILE profileName renameClause
    ;

alterResourcePool
    : ALTER RESOURCE POOL resourcePoolName (FOR ((CURRENT SUBCLUSTER) | SUBCLUSTER identifier))? resourcePoolParam+
    ;

resourcePoolParam
    : (CASCADE TO secondaryPool=value)
      | (CPUAFFINITYMODE	(SHARED	| EXCLUSIVE	| ANY))
      | (CPUAFFINITYSET 	(NONE
                          | (	cpuIndex=value COMMA_ cpuIndex=value)
                          | ( cpuIndex=value MINUS_ cpuIndex=value)
                          | numberLiterals ) )
      | (	EXECUTIONPARALLELISM (numberLiterals | AUTO	))
      | (	MAXCONCURRENCY 	( numberLiterals | identifier| NONE	) )
      | (	MAXMEMORYSIZE (	numberLiterals | identifier | NONE)	)
      | ( MAXQUERYMEMORYSIZE  (numberLiterals | identifier| NONE ))
      | ( MEMORYSIZE (numberLiterals| identifier))
      | (	PLANNEDCONCURRENCY ( numberLiterals	| AUTO))
      | (	PRIORITY ( integerNumber | HOLD ))
      | (	QUEUETIMEOUT ( numberLiterals | identifier | NONE ))
      | (	RUNTIMECAP ( value | identifier	| NONE))
      | (	RUNTIMEPRIORITY	(HIGH | MEDIUM | LOW))
      | (	RUNTIMEPRIORITYTHRESHOLD numberLiterals)
      | (	SINGLEINITIATOR)
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-role/
alterRoleRename
    : ALTER ROLE role renameClause
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-schema/
alterSchema
    : ALTER SCHEMA
        ( ( schemaName ((DEFAULT schemaPrivilegesClause)
                        | (	OWNER TO userName CASCADE? ))
                        | (DISK_QUOTA (value| SET NULL)))
            | (	schemaName	(COMMA_ schemaName	)* RENAME TO newName (COMMA_ newName)*)
        )
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-sequence/
alterSequence
    : ALTER SEQUENCE sequenceName
        ( sequenceParams | renameClause | setSchemaClause| ownerClause )
    ;

sequenceParams
    :  (INCREMENT BY numberLiterals )?
        ( (	MINVALUE numberLiterals ) | (NO MINVALUE))?
        ( (	MAXVALUE numberLiterals ) |	(NO MAXVALUE))?
        ( RESTART WITH numberLiterals)?
        ( (	CACHE DECIMAL_)	|(NO CACHE)	(CYCLE | NO CYCLE))?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-session/
alterSession
    : ALTER SESSION ( ( SET PARAMETER? commaSeparatedKeyValuePairs )
                    | ( CLEAR PARAMETER? params )
                    | ( SET UDPARAMETER (  FOR library )? commaSeparatedKeyValuePairs )
                    | ( CLEAR UDPARAMETER ((( FOR library)? params) | ALL ))
                   )
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-subnet/
alterSubnet
    : ALTER SUBNET identifier renameClause
    ;

alterTableRename
:
	ALTER TABLE tableName
	(
		COMMA_ tableName
	)* renameClause
;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-table/
alterTable
    : ALTER TABLE tableName alterTableItem+
    ;

alterTableItem
    : addColumnSpecification (COMMA_ addColumnSpecification)*
        | addTableContraint
        | alterColumnSpecification
        | alterTableContraint
        | diskQuota
        | dropTableConstraint
        | dropColumnSpecification
        | forceOuter
        | schemaPrivilegesClause
        | ownerClause
        | ( partitionClause REORGANIZE? )
        | removePartitioning
        | renameColmunName
        | renameForTable
        | REORGANIZE
        | setForAlterTable
    ;
setForAlterTable
    : SET (ACTIVEPARTITIONCOUNT expression
                      | IMMUTABLE ROWS
                      | MERGEOUT integerNumber
                      | SCHEMA schemaName
                      | STORAGE loadMethod)
    ;
renameForTable
    : RENAME TO tableName ( COMMA_ tableName)*
    ;
renameColmunName
    : RENAME COLUMN? columnName TO columnName
    ;
removePartitioning
    : REMOVE PARTITIONING
    ;
forceOuter
    : FORCE OUTER numberLiterals
    ;
dropColumnSpecification
    : DROP COLUMN? ( IF EXISTS )? columnName ( CASCADE | RESTRICT )?
    ;
dropTableConstraint
    : DROP CONSTRAINT constraintName ( CASCADE | RESTRICT )?
    ;
diskQuota
    : DISK_QUOTA ( value | SET NULL )
    ;
alterTableContraint
    : ALTER CONSTRAINT constraintName enabledOrDisabled
    ;
addColumnSpecification
    : ADD COLUMN ifNotExistsClause? columnDefinition ((PROJECTIONS (LP_ projectionName (COMMA_ projectionName )* RP_)) | (ALL PROJECTIONS) )?
    ;
addTableContraint
    : ADD tableConstraint
    ;
alterColumnSpecification
    : ALTER COLUMN columnName ( (encodingClause  (PROJECTIONS LP_ projectionName (COMMA_ projectionName )* RP_)) | ((SET | DROP) expression))
    ;

//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-user/
alterUser
    : ALTER USER userName userParams+
    ;

userParams
    : ( (DEFAULT ROLE (NONE| role (COMMA_ role)*| ALL| (ALL EXCEPT role (COMMA_ role)*) ))
        |(RENAME TO value)
      )
      | ( (ACCOUNT (LOCK | UNLOCK	))
         |( GRACEPERIOD ( NONE | value))
         |( IDENTIFIED BY stringLiterals (REPLACE stringLiterals )?)
         |( IDLESESSIONTIMEOUT (NONE| value))
         |( MAXCONNECTIONS ( NONE|(numberLiterals ON (DATABASE| NODE ))))
         |( MEMORYCAP (NONE | stringLiterals) )
         |( PASSWORD EXPIRE )
         | (PROFILE (DEFAULT | stringLiterals))
         | (RESOURCE POOL resourcePoolName )
         | (RUNTIMECAP (NONE | stringLiterals ))
         | (SEARCH_PATH (DEFAULT |(schemaName (COMMA_ schemaName)*)))
         | (SECURITY_ALGORITHM stringLiterals)
         | (TEMPSPACECAP (NONE| value))
        )
      | (SET PARAMETER? keyValuePair (COMMA_ keyValuePair)*)
      | (CLEAR PARAMETER? params)
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-view/
alterView
    : ALTER VIEW
        ( ( viewName OWNER TO owner )
        | ( viewName SET SCHEMA schemaName )
        | (viewName schemaPrivilegesClause )
        | (viewName RENAME TO newName )
        | (viewNameList RENAME TO toViewNameList = viewNameList)
        )
    ;
viewNameList
    : viewName (COMMA_ viewName)*
    ;

//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/begin/
beginTransaction
    : BEGIN ( WORK | TRANSACTION ) (ISOLATION LEVEL isolationLevel)? transactionMode?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/comment-on-statements/comment-on-constraint/
comment
    : commentOnConstraint
    | commentOnFunction
    | commentOnLibrary
    | commentOnNode
    | commentOnProjection
    | commentOnColumn
    | commentOnSchema
    | commentOnSequence
    | commentOnTable
    | commentOnTransformFunction
    | commentOnView

    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/comment-on-statements/comment-on-projection-column/
commentOnColumn
    : COMMENT ON COLUMN columnName commentValue
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/comment-on-statements/comment-on-constraint/
commentOnConstraint
    : COMMENT ON CONSTRAINT constraintName ON tableName commentValue
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/comment-on-statements/comment-on-function/
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/comment-on-statements/comment-on-aggregate-function/
commentOnFunction
    : COMMENT ON (AGGREGATE|ANALYTIC)? FUNCTION functionName parameterList commentValue
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/comment-on-statements/comment-on-transform-function/
commentOnTransformFunction
    : COMMENT ON TRANSFORM FUNCTION functionName parameterList commentValue
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/comment-on-statements/comment-on-library/
commentOnLibrary
    : COMMENT ON LIBRARY libraryName commentValue
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/comment-on-statements/comment-on-node/
commentOnNode
    : COMMENT ON NODE node commentValue
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/comment-on-statements/comment-on-schema/
commentOnSchema
    : COMMENT ON SCHEMA schemaName commentValue
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/comment-on-statements/comment-on-sequence/
commentOnSequence
    : COMMENT ON SEQUENCE sequenceName commentValue
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/comment-on-statements/comment-on-table/
commentOnTable
    : COMMENT ON TABLE tableName commentValue
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/comment-on-statements/comment-on-view/
commentOnView
    : COMMENT ON VIEW viewName commentValue
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/comment-on-statements/comment-on-projection/
commentOnProjection
    : COMMENT ON PROJECTION projectionName commentValue
    ;

commentValue
    : IS ( value | NULL )
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/commit/
commit
    : COMMIT ( WORK | TRANSACTION )?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/connect-to/
connect
    : CONNECT TO VERTICA dbname (USER userName)? (PASSWORD value)? ON host COMMA_
        port (TLS CONFIGURATION identifier)?
        ( TLSMODE PREFER )? (TLSCONFIG tlsConfigName=identifier)?
    ;

copyColumn
    : (columnName ( AS expression )?)
         ( DELIMITER AS? value )?
         ( ENCLOSED BY? value )? ENFORCELENGTH?
         ( (ESCAPE AS? value)|	(NO ESCAPE))?
         ( FILLER dataTypes)?
         ( FORMAT value )?
         ( NULL AS? value)?
         ( TRIM value)?
    ;

columnOption
    : columnName (DELIMITER AS? value	)? (ENCLOSED BY? value)? ENFORCELENGTH? ((ESCAPE AS? value)	|(	NO ESCAPE))? ( FILLER dataTypes )?
        (FORMAT value)? (NULL AS? value)? (	TRIM value)?
    ;

path
    : (identifier (COMMA_ identifier)* (ON (node |	(LP_ node (COMMA_ node)* RP_) | (ANY NODE) | (EACH NODE) ) )? inputFormat?)
        (PARTITION COLUMNS columns)?
    ;

localPath
    : LOCAL ( STDIN | identifier) inputFormat?
    ;

inputFormat
:
	UNCOMPRESSED
	| BZIP
	| GZIP
	| LZO
	| ZSTD
;

verticaSource
    : VERTICA tableName columns?
    ;

udlClause
:
	(
		SOURCE sourceReference LP_ commaSeparatedKeyValuePairs? RP_
	)
	|
	(
		FILTER filterReference LP_ commaSeparatedKeyValuePairs? RP_
	)
	|
	(
		PARSER parserReference LP_ commaSeparatedKeyValuePairs? RP_
	)
;

copyOption
    : (ABORT ON ERROR)
    | (DELIMITER AS? stringLiterals)
	| (ENCLOSED  BY? stringLiterals)
	| ENFORCELENGTH
	| (ERROR TOLERANCE)
	| ((ESCAPE AS? stringLiterals) | (NO ESCAPE))
	| (EXCEPTIONS stringLiterals (ON node)? (COMMA_ stringLiterals (ON node)?)*)
	| (NULL AS? stringLiterals)
	| (RECORD TERMINATOR stringLiterals)
	| (REJECTED DATA ( ( (stringLiterals (ON node )?) (COMMA_ stringLiterals (ON node)?)*) | ( AS TABLE tableName )))
	| (REJECTMAX numberLiterals)
	| (K_SKIP numberLiterals)
	| (K_SKIP BYTES numberLiterals)
	| (STREAM NAME stringLiterals)
	| (TRAILING NULLCOLS)
	| (TRIM value)
	| (WITH? PARSER parserReference (LP_ commaSeparatedKeyValuePairs? RP_))
    | (NO COMMIT)
    | (COLLECTIONOPEN stringLiterals)
	| (COLLECTIONCLOSE stringLiterals)
	| (COLLECTIONDELIMITER stringLiterals)
	| (COLLECTIONNULLELEMENT stringLiterals)
	| (COLLECTIONENCLOSE stringLiterals)
	|
	(
		STORAGE? loadMethod
	)
;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/copy/
copy
    : COPY hints? tableName (LP_ copyColumn (COMMA_ copyColumn)* RP_)?
        ( COLUMN OPTION (LP_ columnOption (COMMA_ columnOption )* RP_ ))?
        ( FROM ( (LOCAL? STDIN value?)
                 | (path	(COMMA_ path)*	)
                | (localPath ( COMMA_ localPath)*)
                | (tableName)
                | (verticaSource)
                )
             ((
               NATIVE VARCHAR?
              | FIXEDWIDTH COLSIZES LP_ integerNumber (COMMA_ integerNumber)* RP_
              | ORC
              | PARQUET
              ) | (WITH? udlClause+ ))?
        )?
        copyOption*
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/copy-local/
copyLocal
    : copy
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/copy-from/
copyFromVertica
    : COPY tableName columns? FROM VERTICA sourceTable=tableName sourceColumns=columns?
        loadMethod?
        ( STREAM NAME identifier)?
        ( NO COMMIT)?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-access-policy/
createAccessPolicy
    : CREATE ACCESS POLICY ON tableName
        (
            ( FOR COLUMN column expressions)
            | ( FOR ROWS WHERE expressions )
        )
        (GRANT TRUSTED)?
        ( ENABLE | DISABLE )
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-authentication/
createAuthentication
    : CREATE AUTHENTICATION authRecordName=authMethodName METHOD authMethod=authMethodName
        ( LOCAL | ( HOST ( NO? TLS )? address ) ) FALLTHROUGH?
    ;

createBranch
:
	CREATE DATA IMMUTABLE BRANCH
	(
		branchIdentifier
	)
	(
		COMMA_ branchIdentifier
	)*
	(
		LIKE value
	)?
;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-external-table-as-copy/
createExternalTableAsCopy
    :
        CREATE EXTERNAL TABLE ifNotExistsClause? tableName columnDefinitionList? schemaPrivilegesClause? AS COPY
        ((SOURCE identifier)? //示例中包含了SOURCE identifier，但文档中未说明
        | (LP_ copyColumn (COMMA_ copyColumn)* RP_)?
            (COLUMN OPTION (LP_ columnOption (COMMA_ columnOption)* RP_))?
            FROM (path (PARTITION COLUMNS columns)?| (WITH? SOURCE source	(LP_ commaSeparatedKeyValuePairs RP_)?)) inputFormat?
            ( NATIVE
                | (FIXEDWIDTH COLSIZES LP_ integerNumber RP_	(COMMA_ LP_ integerNumber RP_)*	)
                | (NATIVE VARCHAR)
                | ORC
                | PARQUET
            )? copyOption*)
    	    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-fault-group/
createFaultGroup
    : CREATE FAULT GROUP identifier
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-flexible-table/
createFlexTable
    : CREATE identifier? ( FLEX | FLEXIBLE) TABLE
        ifNotExistsClause? tableName
        (  (LP_ columnDefinition (COMMA_ columnDefinition)* (COMMA_ tableConstraint)* RP_ )| columnNameList )
        tableExtra*
    ;
tableExtra
    : schemaPrivilegesClause
    | orderByClause
    | segmentationClause
    | ksafeClause
    | partitionClause
    | encodedByClause
    | (AS select)
    | (DISK_QUOTA identifier)
    | loadMethod
    | (( INCLUDING | EXCLUDING ) PROJECTIONS )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-flexible-external-table-as-copy/
createFlexExternalTableAsCopy
    : CREATE  ( FLEX | FLEXIBLE ) EXTERNAL TABLE ifNotExistsClause? tableName
    columnDefinitionList
	schemaPrivilegesClause?
	AS COPY ( LP_ copyColumn ( COMMA_ copyColumn )* RP_ )?
	( FROM ( path (( compressionType ( COMMA_ compressionType )*)? )
	        | (WITH? ( SOURCE source LP_ commaSeparatedKeyValuePairs RP_ )?)
	        )
	         ( FILTER filter LP_ commaSeparatedKeyValuePairs RP_ )?
	 )
		copyOption*

;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-function-statements/create-aggregate-function/
createFunction
    :
        createAggregateFunction
        | createAnalyticFunction
        | createFilter
        | createSqlFunction
        | createUdfFunction
        | createParserFunction
        | createSourceFunction
        | createTransformFunction
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-function-statements/create-transform-function/
createTransformFunction
    : CREATE (  OR REPLACE )? TRANSFORM ifNotExistsClause? functionName AS LANGUAGE lang NAME identifier LIBRARY library ( NOT? FENCED)?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-function-statements/create-source/
createSourceFunction
    : CREATE (  OR REPLACE )? SOURCE ifNotExistsClause? functionName AS LANGUAGE lang NAME identifier LIBRARY library ( NOT? FENCED)?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-function-statements/create-parser/
createParserFunction
    : CREATE ( OR REPLACE )? PARSER ifNotExistsClause? functionName AS
        ( LANGUAGE lang )? NAME identifier LIBRARY library ( NOT? FENCED )?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-function-statements/create-function-scalar/
createUdfFunction
    : CREATE (  OR REPLACE )? FUNCTION ifNotExistsClause functionName AS
        ( LANGUAGE lang )? NAME identifier LIBRARY library ( NOT? FENCED )?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-function-statements/create-function-sql/
createSqlFunction
    : CREATE ( OR REPLACE )? FUNCTION ifNotExistsClause? functionName parameterList RETURN dataTypes AS
        expreDefininition
    ;

expreDefininition
    : BEGIN RETURN expression SEMI_? END
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-function-statements/create-filter/
createFilter
    : CREATE ( OR REPLACE )? FILTER ifNotExistsClause? functionName  AS
        ( LANGUAGE lang )? NAME identifier LIBRARY library ( NOT? FENCED  )?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-function-statements/create-analytic-function/
createAnalyticFunction
    : CREATE ( OR REPLACE )? ANALYTIC FUNCTION ifNotExistsClause? functionName AS
        ( LANGUAGE lang )? NAME identifier LIBRARY library
        ( NOT? FENCED)?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-function-statements/create-aggregate-function/
createAggregateFunction
    : CREATE ( OR REPLACE )? AGGREGATE FUNCTION ifNotExistsClause? functionName AS
        (LANGUAGE lang)? NAME identifier LIBRARY library (NOT? FENCED)?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-hcatalog-schema/
createHcatalogSchema
    : CREATE HCATALOG SCHEMA ifNotExistsClause? schemaName
        (  AUTHORIZATION identifier )?
        ( WITH ( hcatalogSchemaParam )+ )?
    ;

hcatalogSchemaParam
    :  key = (
		HOSTNAME
		| PORT
		| HIVESERVER2_HOSTNAME
		| WEBSERVICE_HOSTNAME
		| WEBSERVICE_PORT
		| WEBHDFS_ADDRESS
		| HCATALOG_SCHEMA
		| HCATALOG_CONNECTION_TIMEOUT
		| HCATALOG_SLOW_TRANSFER_LIMIT
		| HCATALOG_SLOW_TRANSFER_TIME
		| SSL_CONFIG
		| CUSTOM_PARTITIONS
		| HCATALOG_USER
	) EQ_ value
;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-library/
createLibrary
    : CREATE ( OR REPLACE )? LIBRARY libraryName alias ( DEPENDS identifier )? ( LANGUAGE lang )?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-load-balance-group/
createLoadBalanceGroup
    : CREATE LOAD BALANCE GROUP groupName WITH
        (
           ( ADDRESS networaddress ( COMMA_ networaddress )* )
           | ( FAULT GROUP faultGroup ( COMMA_ faultGroup )* FILTER address )
        ) ( POLICY value )?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-local-temporary-view/
createLocalTemporaryView
    : CREATE ( OR REPLACE )? LOCAL ( TEMP | TEMPORARY ) VIEW viewName columns? AS select
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-location/
createLocation
    : CREATE LOCATION stringLiterals
        ( ( NODE node ) | ( ALL NODES ) )? SHARED? ( USAGE stringLiterals )? ( LABEL stringLiterals )? ( LIMIT stringLiterals )?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-network-address/
createNetworkAddress
    : CREATE NETWORK ADDRESS networaddress ON node WITH address (PORT port)? enabledOrDisabled?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-network-interface/
createNetworkInterface
    : CREATE NETWORK INTERFACE networinterface ON node WITH? address (PORT port)? enabledOrDisabled?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-notifier/
createNotifier
    : CREATE NOTIFIER notifier ACTION value notifierParams*
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-procedure-external/
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-procedure-stored/
createProcedure
    : CREATE (OR REPLACE)? PROCEDURE ifNotExistsClause? procedureName parameterList (AS executable = identifier)?
    (LANGUAGE lang)?  (USER userName)?  (SECURITY (DEFINER | INVOKER ))? (AS procedureSource)?
    ;
scopeAndStructure
    : label = identifier? (DECLARE declarations)? BEGIN statements END endLabel=identifier? SEMI_?
    ;
statements
    : statement+
    ;
statement
    : PERFORM? (expression
    | createTable
    | insert
    | select
    | update
    | call )SEMI_?
    ;
declaration
    : variableName = identifier CONSTANT? dataTypes (NOT NULL)? (ASSIGNMENT_OPERATOR_ (expression | statement)) SEMI_?
    | identifier ALIAS FOR variableName = identifier SEMI_?
    ;
declarations
    : declaration+
    ;
procedureSource
    : DOLLAR_ DOLLAR_ scopeAndStructure DOLLAR_ DOLLAR_
    ;
argumentList
    : LP_ ( argumentItem ( COMMA_ argumentItem )* )? RP_
    ;

argumentItem
    : identifier? dataTypes
    ;

createProfile
    : CREATE PROFILE profileName LIMIT passwordParameter+
    ;

projectionClauseItem
    : ( columnName | groupedClause) encodingClause? accessRank?
    ;

ksafeClause
    : KSAFE integerNumber?
    ;

ifNotExistsClause
    : IF NOT? EXISTS
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-projection/standard-projection/
createProjection
    : CREATE PROJECTION ifNotExistsClause? projectionName hints?
        ( LP_ projectionClauseItem ( COMMA_ projectionClauseItem )* RP_
        )? AS simpleSelectQueryClause alias? orderByClause?
        segmentationClause? ksafeClause? onPartitionRange?
    ;

createProjectionLiveAggregateProjections
    : createProjectionLiveAggregateProjectionsGr
        | createProjectionLiveAggregateProjectionsTopstatement
    ;

createProjectionLiveAggregateProjectionsGr
:
	CREATE PROJECTION ifNotExistsClause? projectionName
	(
		LP_ projectionClauseItem
		(
			COMMA_ projectionClauseItem
		)* RP_
	)? AS simpleSelectQueryClause alias? groupByClause ksafeClause?
;

createProjectionLiveAggregateProjectionsTopstatement
    : CREATE PROJECTION ifNotExistsClause? projectionName
        (
            LP_ projectionClauseItem
            (
                COMMA_ projectionClauseItem
            )* RP_
        )? AS simpleSelectQueryClause alias? limitClause overClause
        ksafeClause?
    ;

createProjectionUdtfs
    : CREATE PROJECTION ifNotExistsClause? projectionName
        ( LP_ projectionClauseItem ( COMMA_ projectionClauseItem )* RP_ )?
        AS ( ( batchQuery FROM ( (LP_ prepassQuery RP_ alias?) | (tableName alias?)  ) ) | prepassQuery)
    ;

columns
    : (LP_ columnName? (COMMA_ columnName )* RP_)
      |(columnName ( COMMA_ columnName)*)
    ;

batchQuery
    : SELECT elements COMMA_ functionCall OVER LP_ PARTITION (BATCH | PREPASS)
        BY elements RP_ ( AS columns )?
    ;

prepassQuery
    : SELECT elements COMMA_ functionCall OVER LP_ PARTITION (BATCH | PREPASS)
        BY elements RP_(AS columns)? FROM tableName
    ;
//https://docs.vertica.com/25.1.x/en/sql-reference/statements/create-statements/create-resource-pool/
createResourcePool
    : CREATE RESOURCE POOL resourcePoolName (FOR subcluster)? resourcePoolParam*
    ;
subcluster
    : SUBCLUSTER identifier
    | CURRENT SUBCLUSTER
    ;
createRole
:
	CREATE ROLE role
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-routing-rule/
createRoutingRule
    : CREATE ROUTING RULE ( (role ROUTE address TO groupName)
        | (ROUTE WORKLOAD workloadName=identifier TO SUBCLUSTER subclusterName (COMMA_ subclusterName)*  (PRIORITY priority=identifier)?))
    ;
subclusterName
    : identifier
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-schema/
createSchema
    : CREATE SCHEMA ifNotExistsClause? schemaName
        ( AUTHORIZATION stringLiterals )?
        ( DEFAULT schemaPrivilegesClause )?
        ( DISK_QUOTA quota=identifier)?
    ;

//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-sequence/
createSequence:
	CREATE SEQUENCE ( ifNotExistsClause? sequenceName
	    (INCREMENT BY numberLiterals)?
		(( MINVALUE numberLiterals) |	(NO MINVALUE))?
		(( MAXVALUE numberLiterals) | (NO MAXVALUE))?
		( RESTART WITH? numberLiterals)?
		( START WITH? numberLiterals)?
		((CACHE numberLiterals)|(	NO CACHE))?
		((CYCLE| NO CYCLE))?
	)
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-subnet/
createSubnet
    : CREATE SUBNET identifier WITH stringLiterals
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-table/
createTable
    : createTableDefault
      | createTableAs
      | createTableLike
    ;

// {INCLUDE | EXCLUDE} SCHEMA PRIVILEGES
schemaPrivilegesClause
    : (INCLUDE  | EXCLUDE | MATERIALIZE  | ALL ) SCHEMA? NOT? PRIVILEGES
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-table/
createTableAs
    : CREATE TABLE ifNotExistsClause? tableName columnNameList?
        schemaPrivilegesClause? AS hints? atEpochClause? select encodedByClause? segmentationClause? (DISK_QUOTA value)?
    ;

columnNameList
    : LP_ ( columnNameListItem ( COMMA_ columnNameListItem )* )? RP_
    ;

columnNameListItem
    : columnName encodingClause? accessRank? groupedClause?
    ;

accessRank
    : ACCESSRANK numberLiterals
    ;

groupedClause
    : GROUPED LP_ columnDefinition (COMMA_ columnDefinition)* RP_
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-table/
createTableLike
    : CREATE TABLE ifNotExistsClause? tableName LIKE existingTable=tableName
        tableExtra*
    ;

createTableDefault
    : CREATE TABLE ifNotExistsClause? tableName LP_ columnDefinition (COMMA_ columnDefinition)*
        ( COMMA_ tableConstraint )* RP_ tableExtra*
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-table/table-constraint/
tableConstraint
    : CONSTRAINT constraintName
        (
          ( PRIMARY KEY columns enabledOrDisabled? )
          | ( FOREIGN KEY columns REFERENCES tableName (columns)?)
          | ( UNIQUE columns enabledOrDisabled? )
          | ( CHECK LP_ expression RP_ enabledOrDisabled?)
        )
    ;

columnDefinitionList
    : LP_ ( columnDefinition (  COMMA_ columnDefinition )* )? RP_
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-table/column-definition/
columnDefinition
    : columnName dataTypes? columnConstraint* encodingClause? accessRank?
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-table/
//column‑constraint 指定一个列约束，如下所示：
//  {NULL | NOT NULL}
//  | [ DEFAULT default‑expr ] [ SET USING using‑expr ] } | DEFAULT USING exp}
columnConstraint
    : ( nullOrNotNull )
        | ( DEFAULT expression)
        | ( ( DEFAULT USING expression ) | ( SET USING expression ) )
        | ( (AUTO_INCREMENT | IDENTITY ) ( LP_ value ( COMMA_ value )* RP_ )? )
        | ( CONSTRAINT constraintName )
        | ( CHECK LP_ expression RP_ enabledOrDisabled? )
        | (( PRIMARY KEY enabledOrDisabled? ) | (REFERENCES tableName columns ) )
        | ( UNIQUE enabledOrDisabled? )
        | (nullOrNotNull)
    ;

loadMethod
    : AUTO | DIRECT | TRICKLE
    ;

segmentationClause
    : ( UNSEGMENTED ( ( NODE node ) | ( ALL NODES ) ) )
        |( SEGMENTED BY expression ( ( ALL NODES offsetClause? )
        | ( NODES node ( COMMA_ node )* ) ) )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-temporary-table/
createTemporaryTable
    : createTemporaryTableWithDef
     | createTemporaryTableFromTable
    ;

createTemporaryTableWithDef
    : CREATE ( GLOBAL | LOCAL)?  ( TEMP | TEMPORARY ) TABLE ifNotExistsClause? tableName
        columnDefinitionList
        tableConstraint?
        (  ON COMMIT ( DELETE | PRESERVE ) ROWS )?
        loadMethod?
        ( NO PROJECTION )?
        orderByClause?
        segmentationClause?
        ksafeClause?
        schemaPrivilegesClause?
        (DISK_QUOTA value)?
    ;

createTemporaryTableFromTable
:
	CREATE ( TEMP | TEMPORARY ) TABLE ifNotExistsClause? tableName
	columnNameList?
	( ON COMMIT ( DELETE | PRESERVE ) ROWS )?
	loadMethod?
	AS hints? atEpochClause? select encodedByClause?
	(DISK_QUOTA value)?
;

encodedByClause
    : ENCODED BY columnRef ( COMMA_ columnRef )*
    ;

columnRef
    : ( columnName accessRank? encodingClause? )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-text-index/
createTextIndex
    : CREATE TEXT INDEX txtIndexReference
    ON tableName LP_ uniqueId=identifier COMMA_ textField=dataTypes ( COMMA_ columns )? RP_
     ( STEMMER ( stemmerName=identifier ( LP_ stemmerInputDataType=dataTypes RP_ ) | NONE ) )?
      ( TOKENIZER tokenizerName=identifier ( LP_ tokenizerInputDataType=dataTypes RP_ ))?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-user/
createUser
    : CREATE USER userName userParams*
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-view/
createView
    : CREATE ( OR REPLACE )? VIEW viewName columns? schemaPrivilegesClause? AS
        select alias?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/delete/
delete
    :
        DELETE hints? FROM tableName whereClause?
    ;

//directedQuery
//:
//	activateDirectedQuery
//	| activateDirectedWhere
//	| deactivateDirectedQuery
//	| dropDirectedQuery
//	| getDirectedQuery
//	| saveQuery
//;

//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/activate-directed-query/
activateDirectedQuery
    : ACTIVATE DIRECTED QUERY (identifier | whereClause)
    ;
//https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/create-statements/create-directed-query/
createDirectedQuery
    : CREATE DIRECTED QUERY (OPT | OPTIMIZER | CUSTOM ) identifier  ( COMMENT stringLiterals)?  (OPTVER stringLiterals)? (PSDATE stringLiterals)? select
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/deactivate-directed-query/
deactivateDirectedQuery
    : DEACTIVATE DIRECTED QUERY ( identifier | select | whereClause )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-directed-query/
dropDirectedQuery
    : DROP DIRECTED QUERY ( identifier | whereClause)
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/get-directed-query/
getDirectedQuery
    : GET DIRECTED QUERY select
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/save-query/
saveQuery
    : SAVE QUERY select
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/disconnect/
disconnect
    : DISCONNECT dbname
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-access-policy/
dropAccessPolicy
    : DROP ACCESS POLICY ON tableName FOR ( COLUMN column | ROWS )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-aggregate-function/
dropAggregateFunction
    : DROP AGGREGATE FUNCTION ifNotExistsClause? functionName
        argumentList
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-analytic-function/
dropAnalyticFunction
    : DROP ANALYTIC FUNCTION ifNotExistsClause? functionName argumentList
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-authentication/
dropAuthentication
    : DROP AUTHENTICATION ifNotExistsClause? authMethodName CASCADE?
    ;

dropBranch
:
	DROP BRANCH ifNotExistsClause?
	(
		branchIdentifier
	)
	(
		COMMA_ branchIdentifier
	)* CASCADE?
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-fault-group/
dropFaultGroup
    : DROP FAULT GROUP ifNotExistsClause? faultGroup
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-function/
dropFunction
    : DROP FUNCTION ifNotExistsClause? functionName argumentList
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-source/
dropSource
    : DROP SOURCE sourceReference LP_ RP_
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-filter/
dropFilter
    : DROP FILTER filterReference LP_ RP_
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-parser/
dropParser
    : DROP PARSER parserReference LP_ RP_
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-model/
dropModel
    : DROP MODEL ifNotExistsClause? modelName ( COMMA_ modelName )*
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-library/
dropLibrary
    : DROP LIBRARY ifNotExistsClause? libraryName CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-load-balance-group/
dropLoadBalanceGroup
    : DROP LOAD BALANCE GROUP ifNotExistsClause? groupName CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-network-address/
dropNetworkAddress
    : DROP LOAD? NETWORK ADDRESS ifNotExistsClause? networaddress CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-network-interface/
dropNetworkInterface
:
	DROP NETWORK INTERFACE ifNotExistsClause? networinterface CASCADE?
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-notifier/
dropNotifier
    : DROP NOTIFIER ifNotExistsClause? notifier CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-procedure-external/
dropProcedure
    : DROP PROCEDURE ifNotExistsClause? procedureName argumentList CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-profile/
dropProfile
    : DROP PROFILE ifNotExistsClause? profileName ( COMMA_ profileName )* CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-projection/
dropProjection
    : DROP PROJECTION ifNotExistsClause? projectionName ( COMMA_ projectionName )*  ( CASCADE | RESTRICT )?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-resource-pool/
dropResourcePool
    : DROP RESOURCE POOL resourcePoolName (FOR (SUBCLUSTER subclusterName | CURRENT SUBCLUSTER))?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-role/
dropRole
    : DROP ROLE ifNotExistsClause? role ( COMMA_ role )* CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-routing-rule/
dropRoutingRule
    : DROP ROUTING RULE ( role | FOR WORKLOAD workloadName=identifier)
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-schema/
dropSchema
    : DROP SCHEMA ifNotExistsClause? schemaName (COMMA_ schemaName)* CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-sequence/
dropSequence
    : DROP SEQUENCE ifNotExistsClause? sequenceName ( COMMA_ sequenceName )*
        ( CASCADE  | RESTRICT )?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-subnet/
dropSubnet
    : DROP SUBNET ifNotExistsClause? subnet CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-table/
dropTable
    : DROP TABLE ifNotExistsClause? tableName ( COMMA_ tableName )* CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-text-index/
dropTextIndex
    : DROP TEXT INDEX ifNotExistsClause? txtIndexReference
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-transform-function/
dropTransformFunction
    : DROP TRANSFORM FUNCTION ifNotExistsClause? functionName argumentList
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-user/
dropUser
    : DROP USER ifNotExistsClause? userName ( COMMA_ userName )* CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/drop-statements/drop-view/
dropView
    : DROP VIEW ifNotExistsClause? viewName ( COMMA_ viewName )*
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/end/
end
: END ( WORK | TRANSACTION )?
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/explain/
explain
    : EXPLAIN hints? ( LOCAL | VERBOSE | JSON | ANNOTATED )? (insert | delete | update | select | copy |merge)
    ;


//https://docs.vertica.com/24.1.x/en/data-export/db-export-and-import/exporting-data-to-another-db/
exportToVertica
    : EXPORT hints? TO VERTICA tableName columns?
        ( ( AS select ) |  ( FROM tableName sourceColumns = columns? )
        )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/
grant
    : grantAuthenticationTo
    | grantOnDataLoader
	| grantPrivilegesOnDatabase
	| grantOnKey
	| grantOnLibrary
	| grantOnModel
	| grantOnProcedure
	| grantOnResourcepool
	| grantOnSchema
	| grantOnSequence
	| grantOnLocation
	| grantOnTable
	| grantOnUde
	| grantOnView
	| grantOnRole
	| grantOnTLS
	| grantOnWorkload
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-workload/
grantOnWorkload
    : GRANT USAGE ON WORKLOAD identifier TO grantees
    ;
grantOnTLS
    : GRANT ((USAGE|DROP|ALTER) (COMMA_ (USAGE|DROP|ALTER))*) ON TLS CONFIGURATION
        tlsConfiguration (COMMA_ tlsConfiguration)*
        TO grantees
        withGrantOption?
    ;
tlsConfiguration
    : identifier
//     SERVER
//    | LDAPLINK
//    | LDAPAUTH
//    | DATA_CHANNEL
//    | identifier
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-key/
grantOnKey
    : GRANT ((USAGE|DROP|ALTER) (COMMA_ (USAGE|DROP|ALTER))* | ALL PRIVILEGES) ON KEY name ( COMMA_ name)*
      TO grantees withGrantOption?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-data-loader/
grantOnDataLoader
    : GRANT ((((EXECUTE|ALTER|DROP) (COMMA_ (EXECUTE|ALTER|DROP))*)) | ALL PRIVILEGES?)
        ON DATA LOADER tableName TO grantees withGrantOption?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-view/
grantOnView
    : GRANT ( ((SELECT | ALTER | DROP) ( COMMA_ (SELECT | ALTER | DROP) )*) | (ALL PRIVILEGES? EXTEND?) )
        ON viewName ( COMMA_ viewName )*
        TO grantees withGrantOption?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-user-defined-extension/
grantOnUde
    : GRANT ( ((EXECUTE|ALTER|DROP) (COMMA_ (EXECUTE|ALTER|DROP))*) | ( ALL PRIVILEGES? EXTEND?) )
	    ON ((udxType functionName argumentList ( COMMA_ functionName argumentList )* )
		| ( ALL FUNCTIONS IN SCHEMA schemaName ( COMMA_ schemaName )* ) )
	 TO grantees withGrantOption?
;
udxType
    : FUNCTION
    | ( AGGREGATE FUNCTION )
    | ( ANALYTIC FUNCTION )
    | ( TRANSFORM FUNCTION )
    | FILTER
    | PARSER
    | SOURCE
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-table/
grantOnTable
    : GRANT ( ( schemaPrivilege ( COMMA_ schemaPrivilege )* ) | (ALL PRIVILEGES? EXTEND? ) )
        ON ( ( TABLE? tableName ( COMMA_ tableName )* )
            |
            ( ALL TABLES IN SCHEMA schemaName ( COMMA_ schemaName )* )
        ) TO grantees withGrantOption?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-storage-location/
grantOnLocation
    : GRANT ( ( ( READ | WRITE ) ( COMMA_ ( READ | WRITE ) )* ) | (  ALL PRIVILEGES? ) )
        ON LOCATION path (ON node)
        TO grantees withGrantOption?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-sequence/
grantOnSequence
    : GRANT ( ((SELECT |ALTER |DROP) (COMMA_ (SELECT |ALTER |DROP))*) | ( ALL PRIVILEGES? EXTEND?) )
        ON ( ( SEQUENCE sequenceName ( COMMA_ sequenceName )* )
            | ( ALL SEQUENCES IN SCHEMA schemaName ( COMMA_ schemaReference )* )
        ) TO grantees withGrantOption?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-schema/
grantOnSchema
    : GRANT ( ( schemaPrivilege ( COMMA_ schemaPrivilege )* ) | ( ALL PRIVILEGES? EXTEND? ) )
        ON SCHEMA schemaName (COMMA_ schemaName )*
        TO grantees withGrantOption?
    ;

schemaPrivilege
    : USAGE
        | CREATE
        | SELECT
        | UPDATE
        | INSERT
        | DELETE
        | REFERENCES
        | TRUNCATE
        | ALTER
        | DROP
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-role/
grantOnRole
    : GRANT role (COMMA_ role)* TO grantees withGrantOption?
    ;
//http://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-resource-pool/
grantOnResourcepool
    : GRANT ( USAGE | ( ALL PRIVILEGES? ) )
        ON RESOURCE POOL resourcePoolName ( COMMA_ resourcePoolName )*
        ((FOR SUBCLUSTER subclusterName) | (FOR CURRENT SUBCLUSTER))?
        TO grantees withGrantOption?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-procedure/
grantOnProcedure
    : GRANT ( EXECUTE | ( ALL PRIVILEGES? ))
        ON PROCEDURE procedureName argumentList ( COMMA_ procedureName argumentList )*
        TO grantees withGrantOption?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-model/
grantOnModel
    : GRANT ( ((USAGE | ALTER |DROP) (COMMA_ (USAGE | ALTER |DROP))*) | ( ALL PRIVILEGES? EXTEND?))
        ON MODEL modelName ( COMMA_ modelName )*
        TO grantees withGrantOption?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-library/
grantOnLibrary
    : GRANT ( USAGE | DROP | ( ALL PRIVILEGES? EXTEND?) )
        ON LIBRARY libraryName ( COMMA_ libraryName)*
        TO grantees
        withGrantOption?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-db/
grantPrivilegesOnDatabase
    : GRANT ( (CREATE | TEMP) | ( ALL PRIVILEGES? )) ON DATABASE dbname TO grantees withGrantOption?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/grant-statements/grant-authentication/
grantAuthenticationTo
    :  GRANT AUTHENTICATION method TO grantees
    ;

withGrantOption
:
	(
		WITH GRANT OPTION
	)
;

grantees
    :  grantee  ( COMMA_ grantee )*
    ;

grantee
    : userName | PUBLIC
    ;

//https://docs.vertica.com/24.1.x/en/sql-reference/statements/insert/
insert
    : INSERT hints? INTO tableName columns? insertValues
    ;

insertValues
    : (LP_ insertValues RP_ )
      |(insertDefaultValue
        | ( VALUES LP_ expressions RP_ )
        | with
        | select
        )
    ;
insertDefaultValue
    : DEFAULT VALUES
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/merge/
merge
    : MERGE hints? INTO tableName alias?
        USING (sourceDataset=tableName | select ) alias?
        joinPredicate matchingClause+
    ;

matchingClause
    :
        (
            WHEN MATCHED (AND predicates)? THEN UPDATE
                setAssignment
                (whereClause)?
        )
        |
        (
            WHEN NOT MATCHED ( AND predicates )? THEN INSERT
            columns VALUES LP_ values RP_
            whereClause?
        )
    ;
setAssignment
    : SET assignMent (COMMA_ assignMent)*
    ;
assignMent
    : column EQ_ expression
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/profile/
profile
    : PROFILE (select | insert | update| copy | merge)
    ;

releaseSavepoint
    : RELEASE ( SAVEPOINT)? identifier
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/
revoke
    : revokeAuthentication
    | revokeDataLoader
    | revokeOnDatabase
    | revokeOnKey
    | revokeOnLibrary
    | revokeOnModel
    | revokeOnProdecure
    | revokeOnResourcePool
    | revokeOnRole
    | revokeOnSchema
    | revokeOnSequence
    | revokeOnLocation
    | revokeOnTable
    | revokeOnTLS
    | revokeOnUde
    | revokeOnFunction
    | revokeOnView
    | revokeWorkload
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-workload/
revokeWorkload
    : REVOKE USAGE ON WORKLOAD identifier FROM (userName | role)
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-user-defined-extension/
revokeOnUde
    : REVOKE ( GRANT OPTION FOR )? ( EXECUTE | ALL PRIVILEGES )
         ON ((udxType functionName argumentList ( COMMA_ functionName argumentList )* )
            		| ( ALL FUNCTIONS IN SCHEMA schemaName ( COMMA_ schemaName )* ) )
           FROM grantees withGrantOption?

    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-tls-config/
revokeOnTLS
    : REVOKE (GRANT OPTION FOR) ( ALL | ((USAGE|ALTER|DROP) (COMMA_ (USAGE|ALTER|DROP))*))
          ON TLS CONFIGURATION tlsConfiguration ( COMMA_ tlsConfiguration)*
          FROM grantees
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-key/
revokeOnKey
    : REVOKE (GRANT OPTION FOR)? ( ((USAGE|ALTER|DROP) (COMMA_ (USAGE|ALTER|DROP))*) | (ALL PRIVILEGES?))
               ON KEY name ( COMMA_ name)*
               FROM grantees
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-data-loader/
revokeDataLoader
    : REVOKE (GRANT OPTION FOR)? ( ((EXECUTE|ALTER|DROP) (COMMA_ (EXECUTE|ALTER|DROP))*) | (ALL PRIVILEGES?))
         ON DATA LOADER tableName
         FROM grantees
         CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-view/
revokeOnView
    : REVOKE ( GRANT OPTION FOR )? ( ((SELECT | ALTER |DROP) (COMMA_ (SELECT | ALTER |DROP))*)| ( ALL PRIVILEGES? ) )
        ON viewName ( COMMA_ viewName )*
        FROM grantees CASCADE?
    ;

revokeOnFunction
    : REVOKE ( GRANT OPTION FOR )?
        ( EXECUTE | ( ALL PRIVILEGES? ) )
        ( ( ON ( FUNCTION
                | ( AGGREGATE FUNCTION )
                | ( ANALYTIC FUNCTION )
                | ( TRANSFORM FUNCTION )
                | FILTER
                | PARSER
                | SOURCE
                ) functionName argumentList ( COMMA_ functionName argumentList )*
            )
            |
            ( ALL FUNCTIONS IN SCHEMA schemaName ( COMMA_ schemaName )* )
        ) FROM grantees CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-table/
revokeOnTable
    : REVOKE ( GRANT OPTION FOR )? ( (( SELECT | INSERT | UPDATE | DELETE | REFERENCES | TRUNCATE )
                ( COMMA_ ( SELECT | INSERT | UPDATE | DELETE | REFERENCES | TRUNCATE ) )* ) | ( ALL PRIVILEGES? ) )
                 ON
            ( ( TABLE? tableName ( COMMA_ tableName )* )
            | ( ALL TABLES IN SCHEMA schemaName ( COMMA_ schemaName )* ))
             FROM grantees CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-storage-location/
revokeOnLocation
    : REVOKE ( GRANT OPTION FOR )? ( ( ( READ | WRITE ) ( COMMA_ ( READ | WRITE ) )* ) | ( ALL PRIVILEGES? ) )
            ON LOCATION path (ON node)?
            FROM grantees CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-sequence/
revokeOnSequence
    : REVOKE ( GRANT OPTION FOR )? ( ((SELECT | ALTER |DROP) (COMMA_ (SELECT | ALTER |DROP))*) | ( ALL PRIVILEGES? ) )
        ON ( ( SEQUENCE sequenceName ( COMMA_ sequenceName )* )
            | ( ALL SEQUENCES IN SCHEMA schemaName ( COMMA_ schemaName )* ) )
            FROM grantees CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-schema/
revokeOnSchema
    : REVOKE ( GRANT OPTION FOR )? ( ( schemaPrivilege ( COMMA_ schemaPrivilege )* ) | ( ALL PRIVILEGES? ) )
        ON SCHEMA schemaName (  COMMA_ schemaName )*
        FROM grantees CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-role/
revokeOnRole
    : REVOKE ( ADMIN OPTION FOR )? role ( COMMA_ role )*
        FROM grantees CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-resource-pool/
revokeOnResourcePool
    : REVOKE ( GRANT OPTION FOR )? ( USAGE | ( ALL PRIVILEGES? ) )
        ON RESOURCE POOL resourcePoolName ( COMMA_ resourcePoolName )*
        (FOR subcluster)?
        FROM grantees CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-procedure/
revokeOnProdecure
    : REVOKE ( GRANT OPTION FOR )? ( EXECUTE | ( ALL PRIVILEGES?) )
        ON PROCEDURE procedureName argumentList ( COMMA_ ( procedureName argumentList ) )*
        FROM grantees CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-library/
revokeOnModel
    : REVOKE ( GRANT OPTION FOR )? ( ((USAGE | ALTER| DROP) (COMMA_ (USAGE | ALTER| DROP))*) | ( ALL PRIVILEGES? ) )
        ON MODEL modelName ( COMMA_ modelName )*
        FROM grantees
        CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-library/
revokeOnLibrary
    : REVOKE ( GRANT OPTION FOR )? ( USAGE | ( ALL PRIVILEGES? ))
        ON LIBRARY libraryName ( COMMA_ libraryName)*
        FROM grantees CASCADE?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-db/
revokeOnDatabase
    : REVOKE ( GRANT OPTION FOR )? ( ( ( CREATE | TEMP ) ( COMMA_ ( CREATE | TEMP ) )* ) | ( ALL PRIVILEGES? ) )
        ON DATABASE dbname
        FROM grantees
        CASCADE?
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/revoke-statements/revoke-authentication/
revokeAuthentication
    : REVOKE AUTHENTICATION authMethodName FROM grantees
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/rollback/
rollback
    : ROLLBACK ( WORK | TRANSACTION )?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/rollback-to-savepoint/
rollbackToSavepoint
    : ROLLBACK TO ( SAVEPOINT )? identifier
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/savepoint/
savepoint
    : SAVEPOINT identifier
    ;

with
    : WITH hints? RECURSIVE? commonTable
        (  COMMA_ commonTable )*
    ;
commonTable
    : cteIdentifier columns? AS LP_ select RP_
    ;
cteIdentifier
    : identifier
    ;
//https://docs.vertica.com/24.1.x/en/data-analysis/queries/historical-queries/
atEpochClause
    : ( ( AT EPOCH ( LATEST | numberLiterals ) )
        | ( AT TIME stringLiterals ) )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/select/
select
    : selectQuery
;
selectQuery
    : selectQuery combineType selectQuery
    | (queryBlock | parenthesisSelectQuery) exceptClause? intersectClause? orderByClause? limitClause?
                                                        offsetClause?
                                                        forUpdateClause?
    ;
forUpdateClause
    : FOR UPDATE ( OF tableName ( COMMA_ tableName )* )?
    ;
queryBlock
    : atEpochClause? with? selectClause intoClause? fromClause? whereClause? timeseriesClause?
         groupByClause? havingClause? matchClause?
    ;
parenthesisSelectQuery
    : LP_ selectQuery RP_ (AS? alias)?
    ;
combineType
    : UNION ( ALL | DISTINCT )?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-datestyle/
setDatestyle
: SET DATESTYLE TO
	( ISO | GERMAN | ( SQL ( COMMA_ ( DMY | MDY ) )? ) | ( POSTGRES ( COMMA_ ( MDY | DMY ) )? ) )
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-escape-string-warning/
setEscapeStringWarning
    : SET ESCAPE_STRING_WARNING TO
        (
            ON
            | OFF
        )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-intervalstyle/
setIntervalstyle
    : SET INTERVALSTYLE TO ( PLAIN | UNITS )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-locale/
setLocale
    : SET LOCALE TO value
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-role/
setRole
: SET ROLE ( ( role ( COMMA_ role )* )
		| NONE
		| ALL
		| (ALL EXCEPT  ( role ( COMMA_ role )* ) )
		| DEFAULT
	)
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-search-path/
setSearchPath
: SET SEARCH_PATH ( TO | EQ_ )
	( ( schemaName ( COMMA_ schemaName )* ) | DEFAULT )
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-session-autocommit/
setSessionAutocommit
    : SET SESSION AUTOCOMMIT TO ( ON | OFF )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-session-characteristics-as-transaction/
setSessionCharacteristicsAsTransaction
:
	SET SESSION CHARACTERISTICS AS TRANSACTION
	(
		COMMA_? isolationLevel
		| COMMA_? transactionMode
	)+
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-session-graceperiod/
setSessionGraceperiod
:
	SET SESSION GRACEPERIOD
	( value
		| NONE
		| ( EQ_ DEFAULT )
	)
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-session-idlesessiontimeout/
setSessionIdlesessiontimeout
:
	SET SESSION IDLESESSIONTIMEOUT
	(
		value
		| NONE
		| ( EQ_ DEFAULT )
	)
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-session-memorycap/
setSessionMemorycap
    : SET SESSION MEMORYCAP
        (
            value
            | NONE
            | ( EQ_ DEFAULT )
        )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-session-multipleactiveresultsets/
setSessionMultipleactiveresultsets
:
	SET SESSION MULTIPLEACTIVERESULTSETS TO
	(
		ON
		| OFF
	)
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-session-resource-pool/
setSessionResourcePool
:
	SET SESSION RESOURCE_POOL EQ_
	(
		value
		|
		(
			DEFAULT
		)
	)
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-session-runtimecap/
setSessionRuntimecap
:
	SET SESSION RUNTIMECAP
	(
		value
		| NONE
		|
		(
			EQ_ DEFAULT
		)
	)
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-session-workload/
setSessionWorkload
    :
        SET SESSION WORKLOAD TO? ( value | NONE | ( EQ_ DEFAULT ) )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-session-tempspacecap/
setSessionTempspacecap
:
	SET SESSION TEMPSPACECAP
	(
		value
		| NONE
	)
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-standard-conforming-strings/
setStandardConformingStrings
    : SET STANDARD_CONFORMING_STRINGS TO ( ON | OFF )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/set-statements/set-time-zone/
setTimeZone
    : SET ( ( TIME ZONE ) | TIMEZONE ) TO? INTERVAL? (LOCAL | DEFAULT| value)
    ;


//https://docs.vertica.com/24.1.x/en/sql-reference/statements/show/
show
    : SHOW ( ALL | param )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/show-current/
showCurrent
    : SHOW CURRENT ( ALL | params )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/show-db/
showDatabases
    : SHOW DATABASE dbname  ( ALL | params )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/show-node/
showNode
    : SHOW NODE node ( ALL | params )
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/show-session/
showSession
    : SHOW SESSION (userName| ALL)
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/show-user/
showUser
    : SHOW USER ( ALL | params ) PARAMETER? (params | ALL)
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/start-transaction/
startTransaction
    : START TRANSACTION ( ISOLATION LEVEL isolationLevel transactionMode )?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/truncate-table/
truncateTable
    : TRUNCATE TABLE tableName
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/update/
update
:
	UPDATE hints? tableName alias? SET expressions fromClause?
	whereClause?
;

simpleSelectQueryClause
    : ( LP_ simpleSelectQueryClause RP_ )
        | ( selectClause fromClause )
    ;



offsetClause
:
	OFFSET integerNumber
;

limitClause
    : LIMIT ( integerNumber | ALL )
    ;

selectClause
    : SELECT hints? ( ALL | DISTINCT )? elements
    ;

intersectClause
:
	INTERSECT selectQuery
;

exceptClause
:
	EXCEPT selectQuery
;

fromClause
    : FROM ( dataset ( COMMA_? dataset )* ) tableSample?
    ;

intoClause
:
	(
		INTO TABLE? tableName alias?
	)
	| INTO
	(
		GLOBAL
		| LOCAL
	)?
	(
		TEMP
		| TEMPORARY
	) TABLE? tableName
	(
		ON COMMIT
		(
			PRESERVE
			| DELETE
		) ROWS
	)?
;

timeseriesClause
    : TIMESERIES columnName alias overClause (ORDER BY columns)?
    ;

overClause
    : OVER LP_ ( PARTITION ((BY expression (COMMA_ expression)*)| BEST | NODES | ROW | (LEFT JOIN)))?
        orderByClause? frameClause? RP_ asOverClause?
    ;
frameClause
    : (ROWS | RANGE) ( (BETWEEN startPoint= point AND endPoint=point) | startPoint=point )
    ;
point
    : (UNBOUNDED (PRECEDING | FOLLOWING))
    | (CURRENT ROW)
    | constantValue  (PRECEDING | FOLLOWING)
    ;
constantValue
    : numberLiterals
    ;
asOverClause
    : AS? expression
    ;

groupByClause
    : GROUP BY hints? expressions
    ;

havingClause
:
	HAVING expressions
;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/select/where-clause/
whereClause
    :  WHERE predicates
    ;

orderByClause
    :  ORDER BY orderByItem ( COMMA_ orderByItem)*
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/select/match-clause/
matchClause
    : MATCH LP_ ( PARTITION BY columnName ( COMMA_ columnName )* )? ORDER BY columnName ( COMMA_ columnName )*
        DEFINE ( identifier AS ( expression | predicates) ) ( COMMA_ ( identifier AS ( expression | predicates ) ) )*
         PATTERN patternName=identifier AS LP_ regexp* RP_
        ( ( ROWS MATCH ) ( ( ALL EVENTS ) | ( FIRST EVENT ) ) )? RP_
    ;
regexp
    : identifier (ASTERISK_
    | ASTERISK_ QUESTION_
    | PLUS_
    | PLUS_ QUESTION_
    | QUESTION_
    | QUESTION_ QUESTION_
    | ASTERISK_ PLUS_
    | PLUS_ PLUS_
    | QUESTION_ PLUS_
    | VERTICAL_BAR_)?
    ;
partitionClause
    : PARTITION BY expression groupByClause?
        ( SET? ACTIVEPARTITIONCOUNT numberLiterals )?
    ;
//https://docs.vertica.com/24.1.x/en/sql-reference/statements/create-statements/create-table/column-name-list/
encodingClause
    :  ENCODING (
            AUTO
            | BLOCDICT
            | BLOCK_DICT
            | BLOCKDICT_COMP
            | BZIP_COMP
            | COMMONDELTA_COMP
            | DELTARANGE_COMP
            | DELTAVAL
            | GCDDELTA
            | GZIP_COMP
            | RLE
            | zstandardCompression
        )
    ;
zstandardCompression
    : ZSTD_COMP
    | ZSTD_FAST_COMP
    | ZSTD_HIGH_COMP
    ;
ownerClause
    : OWNER TO owner
    ;

renameClause
    : RENAME TO newName
    ;
newName
    : (owner DOT_)* name
    ;
setSchemaClause
    : SET SCHEMA TO? schemaName
    ;

orderByItem
    : expression (ASC| DESC)? (NULLS (FIRST | LAST | AUTO))?
    ;

predicates
    : ( LP_ predicates RP_ )
      | ( NOT? ( expression | predicate ) ( ( AND | OR | NOT ) predicates )* )
    ;

tableSample
    : TABLESAMPLE LP_ (NUMBER_|INTEGER_) RP_
    ;

dataset
    : ( ( select | joinedTable | tableName ) alias? )
    | ( LP_ dataset RP_ alias? )
    ;

joinedTable
    : ( INNER | ( LEFT OUTER? ) | ( RIGHT OUTER? ) | ( FULL OUTER? ) | NATURAL | CROSS )?
            JOIN tableName alias? hints? tableSample? joinPredicate?
    ;

elements
    : element ( COMMA_ element )*
    ;

element
    : (asteriskExp
    | unAsteriskExp
    | ( expression alias? )
    | expression AS identifier? LP_ ( columnName ( COMMA_ columnName )* ) RP_)
    ;



expressions
:
	(
		expression
	)
	(
		COMMA_
		(
			expression
		)
	)*
;

castExpr
    : DCOLON_ dataTypes
    ;

castOperator
    : ( CAST LP_ expression AS dataTypes RP_ )
	| ( dataTypes stringLiterals )
;

expression
    : ( LP_ ( expression) RP_ castExpr?)
        |(( castOperator
                | functionCall
                | arrayExpr
                | rowTypes
                | numberLiterals
                | caseExp
                | columnName
                | select
                | value
                | raiseExpr
                | setTypes
            ) castExpr?
        )
        | expression (operator expression)+
        | extractExpr
    ;

extractExpr
    : EXTRACT LP_ identifier FROM expression RP_

    ;
raiseExpr
    : RAISE (LOG | INFO | NOTICE | WARNING | EXCEPTION)  literals (COMMA_ expression)* SEMI_?
    ;
arrayExpr
:
	ARRAY LBT_
	(
		expression
		(
			COMMA_ expression
		)*
	)? RBT_
;

predicate
:
	betweenPredicate
	| booleanPredicate
	| columnValuePredicate
	| inPredicate
	| interpolatePredicate
	| likePredicate
	| nullPredicate
;

nullPredicate
:
	expression IS nullOrNotNull
;

likePredicate
:
	expression NOT?
	(
		LIKE
		| ILIKE
		| LIKEB
		| ILIKEB
	) expression
	(
		ESCAPE value
	)?
;

joinPredicate
    : ON expression ( ( AND | OR | NOT ) expression )*
    ;

interpolatePredicate
:
	columnName PREVIOUS VALUE columnName
;

columnValuePredicate
:
	expression operator expression
;

inPredicate
:
	columns IN NOT? LP_ expressions RP_
;



betweenPredicate
:
	expression BETWEEN? expression AND expression
;

booleanPredicate
:
	expression IS NOT?
	(
		booleanLiterals
		| UNKNOWN
	)
;

caseExp
:
	CASE expression? caseWhen+ caseElse? END
;
caseElse
    : ELSE expression
    ;
caseWhen
    : WHEN predicates THEN expression
    ;

customizeFunction
    :
        functionName LP_ ( ( ALL | DISTINCT )? elementWithUsing (  COMMA_ elementWithUsing )* )? RP_ withinGroupOrderByClause? overClause?
    ;
elementWithUsing
    : usingClause |( element usingClause?)
    ;
functionCall
    :(aggregationFunction
      | analyticFunction
      | dateFunction
      | specialFunction
      | sequenceFunction
      | customizeFunction) (DOT_ functionCall)* (DOT_ identifier)?
    ;
sequenceFunction
    : (CURRVAL| NEXTVAL) LP_  sequenceName  RP_
    ;
dateFunction
    : dateFunctionName LP_ (expression (COMMA_ expression)* )? RP_
    ;
dateFunctionName
    : ADD_MONTHS
    | AGE_IN_MONTHS
    | AGE_IN_YEARS
    | CLOCK_TIMESTAMP
    | CURRENT_DATE
    | CURRENT_TIME
    | CURRENT_TIMESTAMP
    | DATE
    | DATE_PART
    | DATE_TRUNC
    | DATEDIFF
    | DAY
    | DAYOFMONTH
    | DAYOFWEEK
    | DAYOFWEEK_ISO
    | DAYOFYEAR
    | DAYS
    | EXTRACT
    | GETDATE
    | GETUTCDATE
    | HOUR
    | ISFINITE
    | JULIAN_DAY
    | LAST_DAY
    | LOCALTIME
    | LOCALTIMESTAMP
    | MICROSECOND
    | MIDNIGHT_SECONDS
    | MINUTE
    | MONTH
    | MONTHS_BETWEEN
    | NEW_TIME
    | NEXT_DAY
    | NOW
    | OVERLAPS
    | QUARTER
    | ROUND
    | SECOND
    | STATEMENT_TIMESTAMP
    | SYSDATE
    | TIME_SLICE
    | TIMEOFDAY
    | TIMESTAMP_ROUND
    | TIMESTAMP_TRUNC
    | TIMESTAMPADD
    | TIMESTAMPDIFF
    | TRANSACTION_TIMESTAMP
    | TRUNC
    | WEEK
    | WEEK_ISO
    | YEAR
    | YEAR_ISO
    ;
analyticFunction
    : analyticFunctionName LP_ (expression (IGNORE NULLS)? (COMMA_ expression (IGNORE NULLS)? )*)? RP_
        withinGroupOrderByClause? overClause?
    ;
analyticFunctionName
    : ARGMAX
    | ARGMIN
    | AVG
    | BOOL_AND
    | BOOL_OR
    | CONDITIONAL_CHANGE_EVENT
    | CONDITIONAL_TRUE_EVENT
    | COUNT
    | CUME_DIST
    | DENSE_RANK
    | EXPONENTIAL_MOVING_AVERAGE
    | FIRST_VALUE
    | LAG
    | LAST_VALUE
    | LEAD
    | MAX
    | MEDIAN
    | MIN
    | NTH_VALUE
    | NTILE
    | PERCENT_RANK
    | PERCENTILE_CONT
    | PERCENTILE_DISC
    | RANK
    | ROW_NUMBER
    | STDDEV
    | STDDEV_POP
    | STDDEV_SAMP
    | SUM
    | VAR_POP
    | VAR_SAMP
    | VARIANCE
    ;
aggregationFunction
    : aggregationFunctionName (
        (LP_ (ALL |distinct)? (expression (COMMA_ expression)* | ASTERISK_)? RP_ withinGroupOrderByClause?)
        | (LP_ expression usingClause (COMMA_ usingClause)*  RP_)
        | (LP_ expression (IGNORE NULLS)? (COMMA_ stringLiterals)? RP_)
        | LP_ orderByClause (COMMA_ orderByClause)* RP_
        )
         overClause? (FROM tableName)?
    ;

withinGroupOrderByClause
    : WITHIN GROUP LP_  orderByClause (COMMA_ orderByClause)* RP_
    ;
aggregationFunctionName
    : APPROXIMATE_COUNT_DISTINCT
    | APPROXIMATE_COUNT_DISTINCT_OF_SYNOPSIS
    | APPROXIMATE_COUNT_DISTINCT_SYNOPSIS
    | APPROXIMATE_COUNT_DISTINCT_SYNOPSIS_MERGE
    | APPROXIMATE_MEDIAN
    | APPROXIMATE_PERCENTILE
    | APPROXIMATE_QUANTILES
    | ARGMAX_AGG
    | ARGMIN_AGG
    | AVG
    | BIT_AND
    | BIT_OR
    | BIT_XOR
    | BOOL_AND
    | BOOL_OR
    | BOOL_XOR
    | CORR
    | COUNT
    | COVAR_POP
    | COVAR_SAMP
    | GROUP_ID
    | GROUPING
    | GROUPING_ID
    | LISTAGG
    | MAX
    | MIN
    | REGR_AVGX
    | REGR_AVGY
    | REGR_COUNT
    | REGR_INTERCEPT
    | REGR_R2
    | REGR_SLOPE
    | REGR_SXX
    | REGR_SXY
    | REGR_SYY
    | STDDEV
    | STDDEV_POP
    | STDDEV_SAMP
    | SUM
    | SUM_FLOAT
    | TS_FIRST_VALUE
    | TS_LAST_VALUE
    | VAR_POP
    | VAR_SAMP
    | VARIANCE
    | (WITHIN GROUP)

    ;
distinct
    : DISTINCT
    ;
specialFunction
    : lambdaFunction
    | extractFunction
    | overlapsFunction
    | insertFunction
    | coalesceFunction
    ;
coalesceFunction
    : COALESCE LP_ expression (COMMA_ expression)* RP_
    ;
insertFunction
    : INSERT LP_ ( ASTERISK_ |(expression (COMMA_ expression)*)) RP_
    ;
overlapsFunction
    : LP_  expression COMMA_ expression RP_ OVERLAPS LP_ expression COMMA_ expression RP_
    ;
extractFunction
    : EXTRACT LP_ identifier FROM expression RP_
    ;
lambdaFunction
    : literals  JSON_EXTRACT_ NOT?  expression
    | LP_ literals (COMMA_  literals)* RP_  JSON_EXTRACT_ NOT? expression
    ;

usingClause
:
	(
		USING PARAMETERS?
		(
			OCTETS
			| CHARACTERS
			| commaSeparatedKeyValuePairs
		)
	)
;

commaSeparatedKeyValuePairs
    : ( LP_ commaSeparatedKeyValuePairs? RP_)
      | (	keyValuePair ( COMMA_ keyValuePair )* )
    ;
// todo

values
:
	(
		value
		(
			COMMA_ value
		)*
	)
	|
	(
		LP_
		(
			value?
			(
				COMMA_ value
			)*
		) RP_
	)
;

keyValuePair
    : param operator value
    ;

hints
    : BLOCK_HINT
    |( OPEN_HINT_ hint (  COMMA_ hint )* CLOSE_HINT_ )
    ;

hint
:
	(
		ALLNODES
	)
	|
	(
		GBYTYPE
		(
			HASH
			| PIPE
		)
	)
	|
	(
		ENABLE_WITH_CLAUSE_MATERIALIZATION
	)
	|
	(
		CREATETYPE LP_ value RP_
	)
	| EARLY_MATERIALIZATION
	| DIRECT
	|
	(
		LABEL LP_ value RP_
	) SYN_JOIN
	| SYNTACTIC_JOIN
	|
	(
		DISTRIB LP_
		(
			value
		)
		(
			COMMA_
			(
				value
			)
		)* RP_
	)
	|
	(
		JTYPE LP_
		(
			value
		) RP_
	)
	|
	(
		UTYPE LP_
		(
			value
		) RP_
	)
	|
	(
		PROJS LP_
		(
			projectionName
			(
				COMMA_ projectionName
			)*
		) RP_
	)
	|
	(
		SKIP_PROJS LP_
		(
			projectionName
			(
				COMMA_ projectionName
			)*
		) RP_
	)
	|
	(
		IGNORECONST LP_ integerNumber RP_
	)
	| VERBATIM
	| expression

;

columnName
    : ( owner DOT_)* column
    ;

tableName
    : (owner DOT_)* name
    ;


sourceReference
    : ( owner DOT_ )* identifier
    ;

filterReference
    : (owner DOT_)? identifier
    ;

parserReference
:
	(
		(
			dbname DOT_
		)?
		(
			schemaName DOT_
		)
	)? parserName
;

projectionName
    :( owner DOT_)* name
    ;

procedureName
    : ( owner DOT_ )* name
    ;

procedure
    : identifier
    ;

functionName
    : (owner DOT_)* function
    ;

libraryName
    : ( owner DOT_ )* identifier
    ;

sequenceName
    : ( owner DOT_ )* identifier
    ;

modelName
    : ( owner DOT_)* identifier
    ;

txtIndexReference
    : ( owner DOT_)* txtIndex
    ;

schemaReference
:
	(
		dbname DOT_
	)? schemaName
;

dbname
    : identifier
    ;

txtIndex
    : identifier
    ;

host
:
 identifier
;

port
    : DECIMAL_
        |INTEGER_
    ;

source
    : identifier
    ;

notifier
:
 identifier
;


filter
:
 identifier
;

parserName
:
 identifier
;



groupName
    : identifier
    ;

networaddress
    : identifier
    ;



branchIdentifier
    :
     identifier numberLiterals?
    ;

role
    : identifier
    ;

lang
    : identifier
    ;

resourcePoolName
    : identifier
    ;

schemaName
    : ( owner DOT_ )* (identifier | PUBLIC)
    ;




params
    : param (COMMA_ param)*
    ;


library
    : identifier
    ;

function
    : HASH | ROLLUP| ANY | identifier
    ;

param
    : AUTOCOMMIT
    | AVAILABLE ROLES
    | AVAILABLE WORKLOADS
    | DATESTYLE
    | ENABLED ROLES
    | ESCAPE_STRING_WARNING
    | GRACEPERIOD
    | IDLESESSIONTIMEOUT
    | INTERVALSTYLE
    | LOCALE
    | MEMORYCAP
    | MULTIPLEACTIVERESULTSETS
    | RESOURCE POOL
    | RUNTIMECAP
    | SEARCH_PATH
    | STANDARD_CONFORMING_STRINGS
    | TEMPSPACECAP
    | TIMEZONE
    | TRANSACTION_ISOLATION
    | TRANSACTION_READ_ONLY
    | WORKLOAD
    | identifier
    ;

node
    :  identifier
    ;



column
    : identifier
    ;

stringLiterals
:
	DOUBLE_QUOTED_TEXT
	| SINGLE_QUOTED_TEXT
	| STRING_
;



numberLiterals
    : MINUS_? (DECIMAL_
        | NUMBER_
        | REAL_
        | INTEGER_)
    ;

integerNumber
    :  DECIMAL_ | INTEGER_
    ;

asteriskExp
    : (tableName | viewName | alias) DOT_ASTERISK_
    ;
unAsteriskExp
    : ASTERISK_
    ;
subnet
    : identifier
    ;

userName
    : identifier
    ;


method
    : identifier
    ;

faultGroup
    :  identifier
    ;

authMethodName
    : identifier
    ;

constraintName
    : identifier
    ;

networinterface
:
 identifier
;

profileName
    : identifier
    ;

value
    : unreservedWord
      | DECIMAL_
      | NUMBER_
      | REAL_
      | IDENTIFIER_
      | DOUBLE_QUOTED_TEXT
      | SINGLE_QUOTED_TEXT
      | ANY_
      | PARAM
      | INTEGER_
      | STRING_
      | TRUE
      | FALSE
        //| ~( SEMI | WHERE | FROM | SELECT | ORDER | GROUP | PARTITION | OVER)
    ;

enableOrDisable
:
	ENABLE
	| DISABLE
;

enabledOrDisabled
    : ENABLED | DISABLED
    ;

nullOrNotNull
    : NOT? NULL
    ;

address
:
	IPV4_ADDR
	| IPV6_ADDR
	| STRING_
;

compressionType
:
	BZIP
	| GZIP
	| LZO
	| UNCOMPRESSED
;

passwordParameter
    : ( PASSWORD_LIFE_TIME
        | PASSWORD_MIN_LIFE_TIME
        | PASSWORD_GRACE_TIME
        | FAILED_LOGIN_ATTEMPTS
        | PASSWORD_LOCTIME
        | PASSWORD_LOCK_TIME
        | PASSWORD_REUSE_MAX
        | PASSWORD_REUSE_TIME
        | PASSWORD_MAX_LENGTH
        | PASSWORD_MIN_LENGTH
        | PASSWORD_MIN_LETTERS
        | PASSWORD_MIN_UPPERCASE_LETTERS
        | PASSWORD_MIN_LOWERCASE_LETTERS
        | PASSWORD_MIN_DIGITS
        | PASSWORD_MIN_SYMBOLS
        | PASSWORD_MIN_CHAR_CHANGE
       ) value
    ;

operator
:
	otherOperator
	| comparisonOperator
	| mathematicalOperator
	| bitwiseOperator
	| booleanOperators
	| walrusOperator
;
walrusOperator
    : ASSIGNMENT_OPERATOR_
    ;

bitwiseOperator
:
	AMPERSAND_
	| VERTICAL_BAR_
	| POUND_
	| TILDE_
	| SIGNED_LEFT_SHIFT_
	| SIGNED_RIGHT_SHIFT_
;

booleanOperators
:
	AND
	| OR
	| NOT
	| IS
	| IS NOT
;

otherOperator
:
	OR_
;

mathematicalOperator
:
	BANG_BANG_
	| AT_
	| SQROOT_
	| CUBEROOT_
	| DIV2_
	| CARET_
	| MOD_
	| PLUS_
	| MINUS_
	| SLASH_
	| ASTERISK_
	| NOT_
;


isolationLevel
    : ( READ COMMITTED )
      | ( SERIALIZABLE )
      | ( REPEATABLE READ )
      |	(READ UNCOMMITTED)
    ;

transactionMode
    : READ (ONLY| WRITE )?
    ;

dataTypes
    : simpleTypes
    | arrayTypes
    | rowTypes
    | mapTypes
    | setTypes
    ;
setTypes
    : SET LBT_ (dataTypes (COMMA_ identifier)?) RBT_ (LP_ value RP_)?
    | SET LBT_ (value (COMMA_ value)*) RBT_
    ;
mapTypes
    : MAP LT_ identifier COMMA_ dataTypes GT_
    ;
simpleTypes
    : ( binaryTypes
      	| booleanTypes
      	| charTypes
      	| dateTypes
      	| apNumericTypes
      	| eNumericTypes
      	| spatialTypes
      	| uuidTypes
      	| otherTypes
      	| TEXT
      	)
      	( LP_ value ( COMMA_ value )? RP_ )?
    ;
rowTypes
    : ROW ( (LP_ column? dataTypes (COMMA_ column? dataTypes)* RP_)
            | ( LP_ field alias? (COMMA_ field alias?)* RP_ (AS name LP_ field (COMMA_ field)* RP_)?)
            )
    ;
field
    : expression
    ;
arrayTypes
    : ARRAY LBT_ ( (dataTypes|value) ( COMMA_ (dataTypes|value ) )* )? RBT_ (LP_ value RP_)?
    ;
binaryTypes
:
	BINARY
	|
	(
		LONG VARBINARY
	)
	| VARBINARY
	| BYTEA
	| RAW
;

booleanTypes
:
	BOOLEAN
;

charTypes
:
	( LONG VARCHAR )
	| CHAR
	| VARCHAR
;

dateTypes
:
	(
		TIME WITH TIMEZONE
	)
	| TIMESTAMPTZ
	| DATE
	| TIME
	| SMALLDATETIME
	|
	(
		TIMESTAMP WITH TIMEZONE
	)
	|
	(
		INTERVAL DAY TO SECOND
	)
	|
	(
		INTERVAL YEAR TO MONTH
	)
	| TIMESTAMP
	| INTERVAL
;

apNumericTypes
:
	(
		DOUBLE PRECISION
	)
	|
	(
		FLOAT LP_ DECIMAL_ RP_
	)
	|
	(
		FLOAT8
	)
	| FLOAT
	|
	(
		REAL
	)
;

eNumericTypes
:
	BIGINT
	| INT8
	| SMALLINT
	| TINYINT
	| DECIMAL
	| NUMERIC
	| NUMBER
	| MONEY
	| INTEGER
	| INT
;

spatialTypes
:
	GEOMETRY
	| GEOGRAPHY
;

uuidTypes
:
	UUID
;

otherTypes
    : IDENTITY
    ;
// 非保留关键字
unreservedWord
:
	ABORT
	| ABSOLUTE
	| ACCESS
	| ACCESSRANK
	| ACCOUNT
	| ACTION
	| ACTIVATE
	| ACTIVEPARTITIONCOUNT
	| ADD
	| ADMIN
	| AFTER
	| AGGREGATE
	| ALSO
	| ALTER
	| ANALYSE
	| ANALYTIC
	| ANALYZE
	| ANNOTATED
	| ANTI
	| ASSERTION
	| ASSIGNMENT
	| AT
	| AUTHENTICATION
	| AUTO
	| AUTO_INCREMENT
	| AVAILABLE
	| BACKWARD
	| BASENAME
	| BATCH
	| BEFORE
	| BEGIN
	| BEST
	| BLOCK
	| BLOCDICT
	| BLOCKDICT_COMP
	| BROADCAST
	| BY
	| BYTEA
	| BYTES
	| BZIP
	| BZIP_COMP
	| CACHE
	| CALLED
	| CASCADE
	| CATALOGPATH
	| CHAIN
	| CHARACTER
	| CHARACTERISTICS
	| CHARACTERS
	| CHECKPOINT
	| CLASS
	| CLEAR
	| CLOSE
	| CLUSTER
	| COLSIZES
	| COLUMNS_COUNT
	| COMMENT
	| COMMIT
	| COMMITTED
	| COMMONDELTA_COMP
	| COMMUNAL
	| COMPLEX
	| CONNECT
	| CONSTRAINTS
	| CONTROL
	| COPY
	| CPUAFFINITYMODE
	| CPUAFFINITYSET
	| CREATEDB
	| CREATEUSER
	| CSV
	| CUBE
	| CURRENT
	| CURSOR
	| CUSTOM
	| CUSTOM_PARTITIONS
	| CYCLE
	| DATA
	| DATABASE
	| DATAPATH
	| DAY
	| DEACTIVATE
	| DEALLOCATE
	| DEC
	| DECLARE
	| DEFAULTS
	| DEFERRED
	| DEFINE
	| DEFINER
	| DELETE
	| DELIMITER
	| DELIMITERS
	| DELTARANGE_COMP
	| DELTARANGE_COMP_SP
	| DELTAVAL
	| DEPENDS
	| DETERMINES
	| DIRECT
	| DIRECTCOLS
	| DIRECTED
	| DIRECTGROUPED
	| DIRECTPROJ
	| DISABLE
	| DISABLED
	| DISCONNECT
	| DISTVALINDEX
	| DO
	| DOMAIN
	| DOUBLE
	| DROP
	| DURABLE
	| EACH
	| ENABLE
	| ENABLED
	| ENCLOSED
	| ENCODING
	| ENCRYPTED
	| ENFORCELENGTH
	| EPHEMERAL
	| EPOCH
	| ERROR
	| ESCAPE
	| EVENT
	| EVENTS
	| EXCEPTION
	| EXCEPTIONS
	| EXCLUDE
	| EXCLUDING
	| EXCLUSIVE
	| EXECUTE
	| EXECUTIONPARALLELISM
	| EXPIRE
	| EXPLAIN
	| EXPORT
	| EXTERNAL
	| FAILED_LOGIN_ATTEMPTS
	| FAULT
	| FENCED
	| FETCH
	| FILESYSTEM
	| FILLER
	| FILTER
	| FIRST
	| FIXEDWIDTH
	| FLEX
	| FLEXIBLE
	| FOLLOWING
	| FORCE
	| FORMAT
	| FORWARD
	| FREEZE
	| FUNCTION
	| FUNCTIONS
	| GCDDELTA
	| GET
	| GLOBAL
	| GRACEPERIOD
	| GROUPED
	| GROUPING
	| GZIP
	| GZIP_COMP
	| HANDLER
	| HCATALOG
	| HCATALOG_CONNECTION_TIMEOUT
	| HCATALOG_DB
	| HCATALOG_SCHEMA
	| HCATALOG_SLOW_TRANSFER_LIMIT
	| HCATALOG_SLOW_TRANSFER_TIME
	| HCATALOG_USER
	| HIGH
	| HIVE_PARTITION_COLS
	| HIVESERVER2_HOSTNAME
	| HOLD
	| HOST
	| HOSTNAME
	| HOUR
	| HOURS
	| IDENTIFIED
	| IDENTITY
	| IDLESESSIONTIMEOUT
	| IF
	| IGNORE
	| IMMEDIATE
	| IMMUTABLE
	| IMPLICIT
	| INCLUDE
	| INCLUDING
	| INCREMENT
	| INDEX
	| INHERITS
	| INPUT
	| INSENSITIVE
	| INSERT
	| INSTEAD
	| INTERFACE
	| INTERPOLATE
	| INVOKER
	| ISOLATION
	| JSON
	| KEY
	| LABEL
	| LANCOMPILER
	| LANGUAGE
	| LARGE
	| LAST
	| LATEST
	| LESS
	| LEVEL
	| LIBRARY
	| LISTEN
	| LOAD
	| LOCAL
	| LOCATION
	| LOCK
	| LONG
	| LOW
	| LZO
	| MANAGED
	| MASK
	| MATCHED
	| MATERIALIZE
	| MAXCONCURRENCY
	| MAXCONCURRENCYGRACE
	| MAXCONNECTIONS
	| MAXMEMORYSIZE
	| MAXPAYLOAD
	| MAXQUERYMEMORYSIZE
	| MAXVALUE
	| MEDIUM
	| MEMORYCAP
	| MEMORYSIZE
	| MERGE
	| MERGEOUT
	| METHOD
	| MICROSECONDS
	| MILLISECONDS
	| MINUTE
	| MINUTES
	| MINVALUE
	| MODE
	| MODEL
	| MONTH
	| MOVE
	| MOVEOUT
	| NAME
	| NATIONAL
	| NATIVE
	| NETWORK
	| NEXT
	| NO
	| NOCREATEDB
	| NOCREATEUSER
	| NODE
	| NODES
	| NOTHING
	| NOTIFIER
	| NOTIFY
	| NOWAIT
	| NULLAWARE
	| NULLCOLS
	| NULLS
	| OBJECT
	| OCTETS
	| OF
	| OFF
	| OIDS
	| OPERATOR
	| OPT
	| OPTIMIZER
	| OPTION
	| OPTVER
	| ORC
	| OTHERS
	| OWNER
	| PARAMETER
	| PARAMETERS
	| PARQUET
	| PARSER
	| PARTIAL
	| PARTITION
	| PARTITIONING
	| PASSWORD
	| PASSWORD_GRACE_TIME
	| PASSWORD_LIFE_TIME
	| PASSWORD_LOCTIME
	| PASSWORD_MAX_LENGTH
	| PASSWORD_MIN_DIGITS
	| PASSWORD_MIN_LENGTH
	| PASSWORD_MIN_LETTERS
	| PASSWORD_MIN_LOWERCASE_LETTERS
	| PASSWORD_MIN_SYMBOLS
	| PASSWORD_MIN_UPPERCASE_LETTERS
	| PASSWORD_REUSE_MAX
	| PASSWORD_REUSE_TIME
	| PATTERN
	| PERCENT
	| PERMANENT
	| PLACING
	| PLANNEDCONCURRENCY
	| POLICY
	| POOL
	| PORT
	| PRECEDING
	| PREPARE
	| PREPASS
	| PRESERVE
	| PREVIOUS
	| PRIOR
	| PRIORITY
	| PRIVILEGES
	| PROCEDURAL
	| PROCEDURE
	| PROFILE
	| PROJECTION
	| PROJECTIONS
	| PSDATE
	| QUERY
	| QUEUETIMEOUT
	| QUOTE
	| RANGE
	| RAW
	| READ
	| RECHECK
	| RECORD
	| RECOVER
	| RECURSIVE
	| REFRESH
	| REINDEX
	| REJECTED
	| REJECTMAX
	| RELATIVE
	| RELEASE
	| REMOVE
	| RENAME
	| REORGANIZE
	| REPEATABLE
	| REPLACE
	| RESET
	| RESOURCE
	| RESTART
	| RESTRICT
	| RESULTS
	| RETURN
	| RETURNREJECTED
	| REVOKE
	| RLE
	| ROLE
	| ROLES
	| ROLLBACK
	| ROLLUP
	| ROWS
	| RULE
	| RUNTIMECAP
	| RUNTIMEPRIORITY
	| RUNTIMEPRIORITYTHRESHOLD
	| SAVE
	| SAVEPOINT
	| SCROLL
	| SEARCH_PATH
	| SECOND
	| SECONDS
	| SECURITY
	| SECURITY_ALGORITHM
	| SEGMENTED
	| SEMI
	| SEMIALL
	| SEQUENCE
	| SEQUENCES
	| SERIALIZABLE
	| SESSION
	| SET
	| SETOF
	| SETS
	| SHARE
	| SHARED
	| SHOW
	| SIMPLE
	| SINGLEINITIATOR
	| SITE
	| SITES
	| K_SKIP
	| SOURCE
	| SPLIT
	| SSL_CONFIG
	| STABLE
	| STANDBY
	| START
	| STATEMENT
	| STATISTICS
	| STDIN
	| STDOUT
	| STEMMER
	| STORAGE
	| STREAM
	| STRENGTH
	| STRICT
	| SUBNET
	| SYSID
	| SYSTEM
	| TABLES
	| TABLESAMPLE
	| TABLESPACE
	| TEMP
	| TEMPLATE
	| TEMPORARY
	| TEMPSPACECAP
	| TERMINATOR
	| THAN
	| TIES
	| TLS
	| TOAST
	| TOKENIZER
	| TOLERANCE
	| TRANSACTION
	| TRANSFORM
	| TRICKLE
	| TRIGGER
	| TRUNCATE
	| TRUSTED
	| TUNING
	| TYPE
	| UDPARAMETER
	| UNCOMMITTED
	| UNCOMPRESSED
	| UNI
	| UNINDEXED
	| UNKNOWN
	| UNLIMITED
	| UNLISTEN
	| UNLOCK
	| UNPACKER
	| UNSEGMENTED
	| UPDATE
	| USAGE
	| VACUUM
	| VALIDATE
	| VALIDATOR
	| VALINDEX
	| VALUE
	| VALUES
	| VARYING
	| VERBOSE
	| VERTICA
	| VIEW
	| VOLATILE
	| WAIT
	| WEBHDFS_ADDRESS
	| WEBSERVICE_HOSTNAME
	| WEBSERVICE_PORT
	| WITHOUT
	| WORK
	| WRITE
	| YEAR
	| ZONE
	| NULL
	| CA
	| A
	| K
	| M
	| G
	| T
	| P
	| E
	| H
	| TO_CHAR
	| PUBLIC
	| FULL
	| DATE
	| ADDRESS
	| FALSE
	| INT
	| TEXT
	| CERTIFICATE
	| EXTRACT
	| CURRENT_TIME
	| CURRENT_DATE
	| CURRENT_TIMESTAMP
	| DATEDIFF
	| LOCALTIME
	| LOCALTIMESTAMP
	| OVERLAPS
	| SYSDATE
	| TIMESTAMPADD
	| TIMESTAMPDIFF
	| HASH
	| COMPRESSION
	| IFDIREXISTS
	| ROWGROUPSIZEMB
	| FILESIZEMB
	| FILEMODE
	| DIRMODE
	| INT96ASTIMESTAMP
	| APPROXIMATE_COUNT_DISTINCT
    | APPROXIMATE_COUNT_DISTINCT_OF_SYNOPSIS
    | APPROXIMATE_COUNT_DISTINCT_SYNOPSIS
    | APPROXIMATE_COUNT_DISTINCT_SYNOPSIS_MERGE
    | APPROXIMATE_MEDIAN
    | APPROXIMATE_PERCENTILE
    | APPROXIMATE_QUANTILES
    | ARGMAX_AGG
    | ARGMIN_AGG
    | AVG
    | BIT_AND
    | BIT_OR
    | BIT_XOR
    | BOOL_AND
    | BOOL_OR
    | BOOL_XOR
    | CORR
    | COUNT
    | COVAR_POP
    | COVAR_SAMP
    | GROUP_ID
    | GROUPING
    | GROUPING_ID
    | LISTAGG
    | MAX
    | MIN
    | REGR_AVGX
    | REGR_AVGY
    | REGR_COUNT
    | REGR_INTERCEPT
    | REGR_R2
    | REGR_SLOPE
    | REGR_SXX
    | REGR_SXY
    | REGR_SYY
    | STDDEV
    | STDDEV_POP
    | STDDEV_SAMP
    | SUM
    | SUM_FLOAT
    | TS_FIRST_VALUE
    | TS_LAST_VALUE
    | VAR_POP
    | VAR_SAMP
    | VARIANCE
    | ARGMAX
    | ARGMIN
    | AVG
    | BOOL_AND
    | BOOL_OR
    | BOOL_XOR
    | CONDITIONAL_CHANGE_EVENT
    | CONDITIONAL_TRUE_EVENT
    | COUNT
    | CUME_DIST
    | DENSE_RANK
    | EXPONENTIAL_MOVING_AVERAGE
    | FIRST_VALUE
    | LAG
    | LAST_VALUE
    | LEAD
    | MAX
    | MEDIAN
    | MIN
    | NTH_VALUE
    | NTILE
    | PERCENT_RANK
    | PERCENTILE_CONT
    | PERCENTILE_DISC
    | RANK
    | ROW_NUMBER
    | STDDEV
    | STDDEV_POP
    | STDDEV_SAMP
    | SUM
    | VAR_POP
    | VAR_SAMP
    | VARIANCE
    | ADD_MONTHS
    | AGE_IN_MONTHS
    | AGE_IN_YEARS
    | CLOCK_TIMESTAMP
    | CURRENT_DATE
    | CURRENT_TIME
    | CURRENT_TIMESTAMP
    | DATE
    | DATE_PART
    | DATE_TRUNC
    | DATEDIFF
    | DAY
    | DAYOFMONTH
    | DAYOFWEEK
    | DAYOFWEEK_ISO
    | DAYOFYEAR
    | DAYS
    | EXTRACT
    | GETDATE
    | GETUTCDATE
    | HOUR
    | ISFINITE
    | JULIAN_DAY
    | LAST_DAY
    | LOCALTIME
    | LOCALTIMESTAMP
    | MICROSECOND
    | MIDNIGHT_SECONDS
    | MINUTE
    | MONTH
    | MONTHS_BETWEEN
    | NEW_TIME
    | NEXT_DAY
    | NOW
    | OVERLAPS
    | QUARTER
    | ROUND
    | SECOND
    | STATEMENT_TIMESTAMP
    | SYSDATE
    | TIME_SLICE
    | TIMEOFDAY
    | TIMESTAMP_ROUND
    | TIMESTAMP_TRUNC
    | TIMESTAMPADD
    | TIMESTAMPDIFF
    | TRANSACTION_TIMESTAMP
    | TRUNC
    | WEEK
    | WEEK_ISO
    | YEAR
    | YEAR_ISO
    | BALANCE

;



// 引用base
literals
    : stringLiterals
    | numberLiterals
    | dateTimeLiterals
    | hexadecimalLiterals
    | bitValueLiterals
    | booleanLiterals
    | nullValueLiterals
    | unreservedWord
    ;




dateTimeLiterals
    : (DATE | TIME | TIMESTAMP) stringLiterals
    | LBE_ identifier stringLiterals RBE_
    ;

hexadecimalLiterals
    : HEX_DIGIT_
    ;

bitValueLiterals
    : BIT_NUM_
    ;

booleanLiterals
    : TRUE | FALSE
    ;

nullValueLiterals
    : NULL
    ;

identifier
    : DOUBLE_QUOTED_TEXT
    | SINGLE_QUOTED_TEXT
    | BQUOTA_STRING
    | IDENTIFIER_
    | (numberLiterals IDENTIFIER_)
    | DEFAULT
    | PARAM
    | ANY_
    | unreservedWord
    | STRING_
    | INTEGER_
    ;



//tableName
//    : (owner DOT_)? name
//    ;

viewName
    : (owner DOT_)* name
    ;




owner
    : identifier
    ;

name
    : identifier
    ;

alias
    : AS? identifier
    ;


expr
    : expr andOperator expr
    | expr orOperator expr
    | notOperator expr
    | LP_ expr RP_
    | booleanPrimary
    ;

andOperator
    : AND | AND_
    ;

orOperator
    : OR | OR_
    ;

notOperator
    : NOT | NOT_
    ;

booleanPrimary
    : booleanPrimary IS NOT? (TRUE | FALSE | UNKNOWN | NULL)
    | (PRIOR | DISTINCT) predicate
    | CONNECT_BY_ROOT predicate
    | booleanPrimary SAFE_EQ_ predicate
    | booleanPrimary comparisonOperator (ALL | ANY) subquery
    | booleanPrimary comparisonOperator predicate
    | predicate
    ;

comparisonOperator
    : EQ_ | GTE_ | GT_ | LTE_ | LT_ | NEQ_ | SAFE_EQ_
    ;

bitExpr
    : bitExpr VERTICAL_BAR_ bitExpr
    | bitExpr AMPERSAND_ bitExpr
    | bitExpr SIGNED_LEFT_SHIFT_ bitExpr
    | bitExpr SIGNED_RIGHT_SHIFT_ bitExpr
    | bitExpr PLUS_ bitExpr
    | simpleExpr
    | bitExpr MINUS_ bitExpr
    | bitExpr ASTERISK_ bitExpr
    | bitExpr SLASH_ bitExpr
    | bitExpr MOD_ bitExpr
    | bitExpr MOD_ bitExpr
    | bitExpr CARET_ bitExpr
    | bitExpr DOT_ bitExpr
    | bitExpr ARROW_ bitExpr
    ;

simpleExpr
    : functionCall #functionExpr
    | literals #literalsExpr
    | simpleExpr OR_ simpleExpr #orExpr
    | (PLUS_ | MINUS_ | TILDE_ | NOT_ | BINARY) simpleExpr #unaryExpr
    | ROW? LP_ expr (COMMA_ expr)* RP_ #rowExpr
    | EXISTS? subquery #subqueryExpr
    | LBE_ identifier expr RBE_ #objectAccessExpr
    | caseExpression #caseExpr
    | columnName #columnExpr
    // | ...
    ;

//functionCall
//    : // aggregationFunction | analyticFunction | specialFunction | regularFunction | xmlFunction
//    ;
caseExpression
    : CASE simpleExpr? caseWhen1+ caseElse1? END
    ;

caseWhen1
    : WHEN expr THEN expr
    ;

caseElse1
    : ELSE expr
    ;

subquery
    : LP_ select RP_
    ;


