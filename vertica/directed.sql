GET DIRECTED QUERY
SELECT employee_first_name, employee_last_name
FROM employee_dimension
WHERE employee_city='Boston' AND job_title='Cashier'
ORDER BY employee_last_name, employee_first_name;

SELECT query_name, is_active
FROM V_CATALOG.DIRECTED_QUERIES
WHERE query_name ILIKE '%findEmployeesCityJobTitle%';

SELECT * FROM DIRECTED_QUERIES
WHERE input_query ILIKE ('%employee_dimension%') AND is_active='t';


EXPLAIN SELECT employee_first_name, employee_last_name
        FROM employee_dimension
        WHERE employee_city='Boston' AND job_title ='Cashier'
        ORDER BY employee_last_name, employee_first_name;

DEACTIVATE DIRECTED QUERY RegionalSalesProducts_JoinTables;

ACTIVATE DIRECTED QUERY RegionalSalesProduct;

DEACTIVATE DIRECTED QUERY WHERE save_plans_version = 21;

SELECT EXPORT_CATALOG('../../export_directedqueries', 'DIRECTED_QUERIES');

SAVE QUERY SELECT employee_dimension.employee_first_name, employee_dimension.employee_last_name
           FROM public.employee_dimension employee_dimension/*+projs('public.employee_dimension')*/
           WHERE ((employee_dimension.employee_city = 'Boston'::varchar(6) /*+:v(1)*/) AND (employee_dimension.job_title = 'Cashier'::varchar(7) /*+:v(2)*/))
           ORDER BY employee_dimension.employee_last_name, employee_dimension.employee_first_name;

CREATE DIRECTED QUERY CUSTOM findEmployeesCityJobTitle_OPT COMMENT 'Optimizer-generated directed query' OPTVER 'Vertica Analytic Database v11.0.1-20210815' PSDATE '2021-08-20 14:53:42.323963' SELECT /*+verbatim*/  employee_dimension.employee_first_name, employee_dimension.employee_last_name
                                                                                                                                                                                                FROM public.employee_dimension employee_dimension/*+projs('public.employee_dimension')*/
                                                                                                                                                                                                WHERE ((employee_dimension.employee_city = 'Boston'::varchar(6) /*+:v(1)*/) AND (employee_dimension.job_title = 'Cashier'::varchar(7) /*+:v(2)*/))
                                                                                                                                                                                                ORDER BY employee_dimension.employee_last_name, employee_dimension.employee_first_name;
ACTIVATE DIRECTED QUERY findEmployeesCityJobTitle_OPT;
