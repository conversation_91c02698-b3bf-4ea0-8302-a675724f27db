ALTER DATABASE DEFAULT SET PARAMETER AccessPolicyManagementSuperuserOnly = 1;
-- https://web.archive.org/web/20240912184029/https://docs.vertica.com/24.1.x/en/admin/db-users-and-privileges/access-policies/managing-access-policies/
ALTER ACCESS POLICY ON public.customer_dimension FOR COLUMN customer_address
    CASE WHEN enabled_role('dbadmin') THEN customer_address
         WHEN enabled_role('administrator') THEN customer_address
         ELSE '**************' END ENABLE;

SELECT policy_type, is_policy_enabled, table_name, column_name, expression FROM access_policy
WHERE table_name = 'public.customer_dimension' AND column_name='customer_address';


ALTER ACCESS POLICY ON customer_dimension FOR ROWS DISABLE;
ALTER ACCESS POLICY ON customer_dimension FOR ROWS ENABLE;

ALTER ACCESS POLICY ON public.customer_dimension FOR COLUMN customer_address DISABLE;
ALTER ACCESS POLICY ON public.customer_dimension FOR COLUMN customer_address ENABLE;

ALTER ACCESS POLICY ON public.emp_dimension FOR ROWS COPY TO TABLE public.regional_managers_dimension;
ALTER ACCESS POLICY ON public.emp_dimension FOR COLUMN employee_key COPY TO TABLE store.store_sales_fact;


ALTER AUTHENTICATION v_ldap DISABLE;
ALTER AUTHENTICATION v_ldap ENABLE;
ALTER AUTHENTICATION v_kerberos RENAME TO K5;

CREATE AUTHENTICATION ident1 METHOD 'ident' LOCAL;
ALTER AUTHENTICATION ident1 SET system_users='user1';

CREATE AUTHENTICATION Ldap1 METHOD 'ldap' HOST '*************';
ALTER AUTHENTICATION Ldap1 SET host='ldap://*************',
   binddn_prefix='cn=', binddn_suffix=',dc=qa_domain,dc=com';
ALTER AUTHENTICATION Ldap2 SET basedn='dc=qa_domain,dc=com',
   binddn='cn=Manager,dc=qa_domain,
   dc=com',search_attribute='cn',bind_password='secret';

CREATE AUTHENTICATION localpwd METHOD 'hash' LOCAL;
ALTER AUTHENTICATION localpwd METHOD 'trust';
ALTER AUTHENTICATION krb_local set realm = 'COMPANY.COM';


--- https://web.archive.org/web/20241009160716/https://docs.vertica.com/24.1.x/en/security-and-authentication/tls-protocol/tls-overview/managing-ca-bundles/
CREATE CA BUNDLE ca_bundle CERTIFICATES root_ca, root_ca2;
SELECT * FROM ca_bundles WHERE name='ca_bundle';
SELECT user_name AS owner_name,
       owner     AS owner_oid,
       b.name    AS bundle_name,
       c.name    AS cert_name
FROM   (SELECT name,
               STRING_TO_ARRAY(certificates) :: array[INT] AS certs
        FROM   ca_bundles) b
           LEFT JOIN certificates c
                     ON CONTAINS(b.certs, c.oid)
           LEFT JOIN users
                     ON user_id = owner
ORDER  BY 1;
ALTER CA BUNDLE ca_bundle ADD CERTIFICATES ca_cert;
SELECT * FROM ca_bundles WHERE name='ca_bundle';
ALTER CA BUNDLE ca_bundle REMOVE CERTIFICATES root_ca2;
SELECT * FROM CA_BUNDLES;
SELECT * FROM ca_bundles WHERE name='ca_bundle';
ALTER CA BUNDLE ca_bundle OWNER TO Alice;
SELECT * FROM ca_bundles WHERE name='ca_bundle';
DROP CA BUNDLE ca_bundle;


ALTER FAULT GROUP parent0 RENAME TO parent100;

ALTER FUNCTION SQL_one (int, int) RENAME TO SQL_two;

ALTER FUNCTION SQL_two (int, int) SET SCHEMA macros;

ALTER FUNCTION SQL_two (int, int) OWNER TO user1;

ALTER HCATALOG SCHEMA hcat SET HOSTNAME='thrift://ms1.example.com:9083,thrift://ms2.example.com:9083';
ALTER HCATALOG SCHEMA hcat SET HCATALOG_USER='admin';
CREATE LIBRARY mylib AS '/path/to/python_udx' DEPENDS '/path/to/python/site-packages' LANGUAGE 'Python';
ALTER LIBRARY myFunctions AS '/home/<USER>/my_new_functions.so';

ALTER LOAD BALANCE GROUP group_2 DROP ADDRESS node03;
ALTER LOAD BALANCE GROUP group_2 ADD ADDRESS node01,node02,node03;

ALTER MODEL my_kmeans_model SET SCHEMA clustering_models;
ALTER MODEL kmeans_model OWNER TO analytics_user;

ALTER NETWORK ADDRESS test_addr RENAME TO alt_node1;
ALTER NETWORK ADDRESS alt_node1 SET TO '*************' PORT 4000;
ALTER NETWORK INTERFACE myNetwork RENAME TO myNewNetwork;

ALTER NODE v_vmart_node0001 EXPORT ON DEFAULT;
ALTER NODE v_vmart_node0001 REPLACE WITH standby1;
ALTER NODE v_vmart_node0001 RESET;
ALTER NODE v_vmart_node0001 SET MaxClientSessions = 0;
ALTER NODE v_vmart_node0001 CLEAR MaxClientSessions;
ALTER NODE v_vmart_node0001 IS EPHEMERAL;
ALTER NOTIFIER my_dc_notifier
    ENABLE
    MAXMEMORYSIZE '2G'
    IDENTIFIED BY 'f8b0278a-3282-4e1a-9c86-e0f3f042a971'
    CHECK COMMITTED;
ALTER NOTIFIER my_notifier TLS CONFIGURATION notifier_tls_config;
ALTER NOTIFIER encrypted_notifier
    DISABLE
    TLS CONFIGURATION kafka_tls_config;
ALTER NOTIFIER encrypted_notifier PARAMETERS
    'sasl.username=user;sasl.password=password;sasl.mechanism=PLAIN;security.protocol=SASL_SSL';
ALTER NOTIFIER encrypted_notifier ENABLE;

ALTER NOTIFIER my_dc_notifier
    ENABLE
    MAXMEMORYSIZE '2G'
    IDENTIFIED BY 'f8b0278a-3282-4e1a-9c86-e0f3f042a971'
    CHECK COMMITTED;
ALTER NOTIFIER my_notifier TLS CONFIGURATION notifier_tls_config;
ALTER PROCEDURE echo_integer(int) SECURITY DEFINER;

CREATE PROCEDURE echo_integer(IN x int) LANGUAGE PLvSQL AS $$
BEGIN
    RAISE INFO 'x is %', x;
END;
$$;
ALTER PROCEDURE echo_integer(int) SECURITY DEFINER;
ALTER PROCEDURE echo_integer(int) SECURITY INVOKER;
--- to 后面解析有问题
ALTER PROCEDURE echo_integer(int) SOURCE TO $$
BEGIN
        RAISE INFO 'the integer is: %', x;
END;
$$;

ALTER PROCEDURE echo_integer(int) OWNER TO u1;
ALTER PROCEDURE echo_integer(int) SET SCHEMA s1;
ALTER PROCEDURE echo_integer(int) RENAME TO echo_int;


ALTER PROFILE sample_profile LIMIT FAILED_LOGIN_ATTEMPTS 3;
ALTER PROFILE sample_profile RENAME TO new_sample_profile;

ALTER PROJECTION foo ON PARTITION RANGE BETWEEN NULL AND NULL;

SELECT export_tables('','public.store_orders');



CREATE TABLE public.store_orders
(
    order_no int,
    order_date timestamp NOT NULL,
    shipper varchar(20),
    ship_date date NOT NULL
);


 CREATE PROJECTION store_orders_p AS SELECT * from store_orders;
 ALTER PROJECTION store_orders_p RENAME to store_orders_new;
 ALTER PROJECTION store_orders_new DISABLE;
 SELECT * FROM store_orders_new;
 ALTER PROJECTION store_orders_new ENABLE;

ALTER RESOURCE POOL ceo_pool PRIORITY 5;
ALTER RESOURCE POOL ceo_pool CASCADE TO second_pool;
ALTER RESOURCE POOL user_0 QUEUETIMEOUT '11 months 50 days 08:32';

ALTER ROLE applicationadministrator RENAME TO appadmin;
ALTER ROUTING RULE etl_rule SET ROUTE TO '***********/24';
ALTER ROUTING RULE etl_rule SET GROUP TO etl_group;
SELECT * FROM routing_rules WHERE NAME = 'etl_rule';
ALTER ROUTING RULE FOR WORKLOAD analytics SET SUBCLUSTER TO `sc_analytics_2`;
ALTER ROUTING RULE FOR WORKLOAD analytics SET WORKLOAD TO reporting;
ALTER ROUTING RULE FOR WORKLOAD reporting SET PRIORITY TO 5;

ALTER SCHEDULE sched1 USING CRON '0 13 * * *';
ALTER SCHEDULE daily_schedule RENAME TO daily_8am_gmt;
ALTER SCHEDULE my_schedule USING DATETIMES('2023-10-01 12:30:00', '2022-11-01 12:30:00');

ALTER SCHEMA S1, S2 RENAME TO S3, S4;
ALTER SCHEMA s1 DEFAULT INCLUDE SCHEMA PRIVILEGES;
ALTER SCHEMA s1 DEFAULT EXCLUDE SCHEMA PRIVILEGES;
ALTER SCHEMA transit.train RENAME TO transit.ferry;
ALTER SCHEMA transit.train DISK_QUOTA set null;
ALTER SEQUENCE my_sequence RESTART WITH 50;
ALTER SEQUENCE s1.my_seq RENAME TO s1.serial;
ALTER SESSION SET UDPARAMETER FOR securelib username='alice';
ALTER SESSION SET ForceUDxFencedMode = 1;
ALTER SESSION CLEAR ForceUDxFencedMode;
ALTER SESSION CLEAR PARAMETER ALL;
ALTER SESSION SET UDPARAMETER FOR MyLibrary RowCount = 25;
ALTER SESSION CLEAR UDPARAMETER FOR MyLibrary RowCount;
ALTER SUBNET mysubnet RENAME TO myNewSubnet;
ALTER TABLE my_table OWNER TO Joe, ADD CONSTRAINT unique_b UNIQUE (b) ENABLED;
ALTER TABLE airport.airline1.flights SET SCHEMA airpot.airline2.flights;

ALTER TABLE store.store_dimension ALTER COLUMN store_region
    ENCODING rle PROJECTIONS (store.store_dimension_p1_b0, store.store_dimension_p2);
ALTER TLS CONFIGURATION LDAPLink REMOVE CA CERTIFICATES ca,ica;

ALTER TRIGGER daily_1am PROCEDURE TO log_user_actions(10, 20);
ALTER TRIGGER daily_1am RENAME TO daily_1am_gmt;
ALTER USER user1 IDENTIFIED BY 'newpassword';
ALTER USER user1 SECURITY_ALGORITHM 'SHA512' IDENTIFIED BY 'newpassword';
ALTER USER user1 DEFAULT ROLE ALL;
ALTER USER user2 DEFAULT ROLE ALL EXCEPT role1;

ALTER USER Yvonne SET PARAMETER UseDepotForWrites = 0;

COMMENT ON AGGREGATE FUNCTION APPROXIMATE_MEDIAN(x FLOAT) IS 'alias of APPROXIMATE_PERCENTILE with 0.5 as its parameter';
COMMENT ON AGGREGATE FUNCTION APPROXIMATE_MEDIAN(x FLOAT) IS NULL;

COMMENT ON COLUMN store.store_sales_fact.transaction_time IS 'GMT';
COMMENT ON COLUMN customer_dimension_vmart_node01.customer_name IS 'Last name only';
COMMENT ON COLUMN store.store_sales_fact.transaction_time IS NULL;
COMMENT ON CONSTRAINT constraint_x ON promotion_dimension IS 'Primary key';
COMMENT ON CONSTRAINT constraint_x ON promotion_dimension IS NULL;
COMMENT ON FUNCTION macros.zerowhennull(x INT) IS 'Returns a 0 if not NULL';
COMMENT ON FUNCTION macros.zerowhennull(x INT) IS NULL;
COMMENT ON LIBRARY MyFunctions IS 'In development';
COMMENT ON LIBRARY MyFunctions IS NULL;
COMMENT ON NODE initiator IS 'Initiator node';
COMMENT ON NODE initiator IS NULL;
COMMENT ON PROJECTION customer_dimension_vmart_node01 IS 'Test data';
COMMENT ON PROJECTION customer_dimension_vmart_node01 IS NULL;
COMMENT ON SCHEMA public  IS 'All users can access this schema';
COMMENT ON SCHEMA public IS NULL;
COMMENT ON SEQUENCE prom_seq IS 'Promotion codes';
COMMENT ON SEQUENCE prom_seq IS NULL;
COMMENT ON TABLE promotion_dimension IS '2011 Promotions';
COMMENT ON TABLE promotion_dimension IS NULL;
COMMENT ON TRANSFORM FUNCTION macros.zerowhennull(x INT) IS 'Returns a 0 if not NULL';
COMMENT ON TRANSFORM FUNCTION macros.zerowhennull(x INT) IS NULL;
COMMENT ON VIEW curr_month_ship IS 'Shipping data for the current month';
COMMENT ON VIEW curr_month_ship IS NULL;

COPY public.customer_dimension (customer_since FORMAT 'YYYY')
    FROM STDIN
    DELIMITER ','
    NULL AS 'null'
    ENCLOSED BY '"';
COPY store.store_dimension
    FROM :input_file
    DELIMITER '|'
    NULL ''
    RECORD TERMINATOR E'\f';
COPY sampletab FROM '/home/<USER>/one.dat', 'home/dbadmin/two.dat';
COPY myTable FROM 'webhdfs:///mydirectory/ofmanyfiles/*.dat';
COPY myTable FROM 'webhdfs:///mydirectory/*_[0-9]';
COPY myTable FROM 'webhdfs:///data/sales/01/*.dat', 'webhdfs:///data/sales/02/*.dat',
    'webhdfs:///data/sales/historical.dat';
COPY sampletab FROM '/data/file.dat' ON ANY NODE;
COPY sampletab FROM '/data/file1.dat', '/data/file2.dat' ON ANY NODE;
COPY sampletab FROM '/data/file1.dat' ON (v_vmart_node0001, v_vmart_node0002),
    '/data/file2.dat' ON (v_vmart_node0003, v_vmart_node0004);
COPY t FROM 'webhdfs://testNS/opt/data/file2.csv';
COPY t FROM 's3://AWS_DataLake/*' ORC;
COPY customer_dimension FROM  VERTICA vmart.customer_dimension;
CREATE AUTHENTICATION localpwd METHOD 'hash' LOCAL;
CREATE AUTHENTICATION v_ldap METHOD 'ldap' HOST TLS '10.0.0.0/23';
CREATE AUTHENTICATION v_kerberos METHOD 'gss' HOST '2001:db8:1::1200/56';
CREATE AUTHENTICATION v_oauth METHOD 'oauth' HOST '0.0.0.0/0';
ALTER AUTHENTICATION v_oauth SET validate_type = 'IDP';
ALTER AUTHENTICATION v_oauth SET client_id = 'vertica';
 ALTER AUTHENTICATION v_oauth SET client_secret = 'client_secret';
ALTER AUTHENTICATION v_oauth SET discovery_url = 'https://***********:8443/realms/myrealm/.well-known/openid-configuration';
ALTER AUTHENTICATION v_oauth SET introspect_url = 'https://***********:8443/realms/myrealm/protocol/openid-connect/token/introspect';


ALTER FUNCTION UDF_one (int, int) RENAME TO UDF_two;
ALTER FUNCTION UDF_two (int, int) SET SCHEMA macros;
ALTER FUNCTION UDF_two (int, int) SET FENCED false;


ALTER SCHEMA ms OWNER TO dbadmin CASCADE;
ALTER SCHEMA ms OWNER TO pat CASCADE;
ALTER SCHEMA S1, S2, temps RENAME TO temps, S1, S2;
ALTER SCHEMA S1, S2 RENAME TO S3, S4;
ALTER SCHEMA s1 DEFAULT INCLUDE SCHEMA PRIVILEGES;
ALTER SCHEMA s1 DEFAULT EXCLUDE SCHEMA PRIVILEGES;
--- https://docs.vertica.com/12.0.x/zh-cn/admin/working-with-native-tables/sequences/named-sequences/altering-sequences/
CREATE SEQUENCE my_sequence START 10;
SELECT NEXTVAL('my_sequence');
ALTER SEQUENCE my_sequence RESTART WITH 50;
SELECT NEXTVAL('my_sequence');

ALTER SEQUENCE s1.my_seq RENAME TO s1.serial;
ALTER SEQUENCE s1.my_seq SET SCHEMA TO s2;
ALTER SEQUENCE s2.serial OWNER TO bertie;

ALTER SUBCLUSTER analytics_cluster SET DEFAULT;
ALTER SUBCLUSTER default_subcluster RENAME TO load_subcluster;
ALTER SUBNET mysubnet RENAME TO myNewSubnet;

--- https://docs.vertica.com/12.0.x/zh-cn/sql-reference/statements/alter-statements/alter-table/projection-column-encoding/
ALTER TABLE store.store_dimension ALTER COLUMN store_region
    ENCODING rle PROJECTIONS (store.store_dimension_p1_b0, store.store_dimension_p2);

select export_objects('','store.store_dimension');


CREATE TABLE store.store_dimension
(
    store_key int NOT NULL,
    store_name varchar(64),
    store_number int,
    store_address varchar(256),
    store_city varchar(64),
    store_state char(2),
    store_region varchar(64)
);

CREATE PROJECTION store.store_dimension_p1
(
 store_key,
 store_name,
 store_number,
 store_address,
 store_city,
 store_state,
 store_region ENCODING RLE
)
AS
SELECT store_dimension.store_key,
       store_dimension.store_name,
       store_dimension.store_number,
       store_dimension.store_address,
       store_dimension.store_city,
       store_dimension.store_state,
       store_dimension.store_region
FROM store.store_dimension
ORDER BY store_dimension.store_key
    SEGMENTED BY hash(store_dimension.store_key) ALL NODES KSAFE 1;

CREATE PROJECTION store.store_dimension_p2
(
 store_key,
 store_name,
 store_number,
 store_address,
 store_city,
 store_state,
 store_region ENCODING RLE
)
AS
SELECT ...;

ALTER TLS CONFIGURATION LDAPLink CERTIFICATE NULL REMOVE CA CERTIFICATES ca, ica;
ALTER TLS CONFIGURATION server CIPHER SUITES
    'DHE-PSK-AES256-CBC-SHA384,
     DHE-PSK-AES128-GCM-SHA256,
     PSK-AES128-CBC-SHA256';

ALTER USER user1 IDENTIFIED BY 'newpassword';
ALTER USER user1 SECURITY_ALGORITHM 'SHA512' IDENTIFIED BY 'newpassword'
CREATE USER user1;
CREATE USER
GRANT role1, role2, role3 to user1;
ALTER USER user1 DEFAULT ROLE ALL;
ALTER USER user2 DEFAULT ROLE ALL EXCEPT role1;

BEGIN TRANSACTION ISOLATION LEVEL READ COMMITTED READ WRITE;
-- https://docs.vertica.com/12.0.x/zh-cn/extending/stored-procedures/executing-stored-procedures/
CALL stored_procedure_name();

CREATE PROCEDURE raiseXY(IN x INT, y VARCHAR) LANGUAGE PLvSQL AS $$
BEGIN
    RAISE NOTICE 'x = %', x;
    RAISE NOTICE 'y = %', y;
    -- some processing statements
END;
$$;

CALL raiseXY(3, 'some string');

COMMENT ON CONSTRAINT constraint_x ON promotion_dimension IS 'Primary key';
COMMENT ON CONSTRAINT constraint_x ON promotion_dimension IS NULL;
COMMENT ON FUNCTION macros.zerowhennull(x INT) IS 'Returns a 0 if not NULL';
COMMENT ON FUNCTION macros.zerowhennull(x INT) IS NULL;
COMMENT ON COLUMN customer_dimension_vmart_node01.customer_name IS 'Last name only';
COMMENT ON COLUMN customer_dimension_vmart_node01.customer_name IS NULL;
COMMENT ON SCHEMA public  IS 'All users can access this schema';
COMMENT ON SCHEMA public IS NULL;
COMMENT ON TABLE promotion_dimension IS '2011 Promotions';
COMMENT ON TABLE promotion_dimension IS NULL;
COMMENT ON COLUMN store.store_sales_fact.transaction_time IS 'GMT';
COMMENT ON COLUMN store.store_sales_fact.transaction_time IS NULL;

COPY t(year AS TO_CHAR(k, 'YYYY')) FROM 'myfile.dat'
COPY t FROM 'file1.txt' ON (v_vmart_node0001, v_vmart_node0002);
COPY sales (f FILLER VARCHAR, price AS f::INT)
    FROM STDIN REJECTED DATA AS TABLE sales_rej;
COPY public.customer_dimension (customer_since FORMAT 'YYYY')
    FROM STDIN
    DELIMITER ','
    NULL AS 'null'
    ENCLOSED BY '"';
COPY store.store_dimension
    FROM :input_file
    DELIMITER '|'
    NULL ''
    RECORD TERMINATOR E'\f';
COPY sampletab FROM '/home/<USER>/one.dat', 'home/dbadmin/two.dat';
COPY myTable FROM 'webhdfs:///mydirectory/ofmanyfiles/*.dat';
COPY myTable FROM 'webhdfs:///mydirectory/*_[0-9]';
COPY myTable FROM 'webhdfs:///data/sales/01/*.dat', 'webhdfs:///data/sales/02/*.dat',
    'webhdfs:///data/sales/historical.dat';
COPY sampletab FROM '/data/file.dat' ON ANY NODE;
COPY sampletab FROM '/data/file1.dat', '/data/file2.dat' ON ANY NODE;
COPY t FROM 'webhdfs:///opt/data/file1.dat';
COPY t FROM 'webhdfs://testNS/opt/data/file2.csv';
COPY t FROM 's3://AWS_DataLake/*' ORC;
CREATE EXTERNAL TABLE records (id int, name varchar(50), created date, region varchar(50))
   AS COPY FROM 'webhdfs:///path/*/*/*'
   PARTITION COLUMNS created, region;
COPY tweets FROM '/myTest/Flexible/DATA/tweets_12.json' PARSER FJSONPARSER();
COPY large_tbl FROM :file delimiter '|';
COPY large_tbl FROM :file ON site01 GZIP delimiter '|';

CREATE AUTHENTICATION localpwd METHOD 'hash' LOCAL;
CREATE AUTHENTICATION v_ldap METHOD 'ldap' HOST TLS '10.0.0.0/23';
CREATE AUTHENTICATION v_kerberos METHOD 'gss' HOST '2001:db8:1::1200/56';

CREATE AUTHENTICATION RejectNoSSL METHOD 'reject' HOST NO TLS '0.0.0.0/0';  --IPv4
CREATE AUTHENTICATION RejectNoSSL METHOD 'reject' HOST NO TLS '::/0';       --IPv6

CREATE EXTERNAL TABLE sales (itemID INT, date DATE, price FLOAT)
    AS COPY FROM 'hdfs:///data/ext1.csv' DELIMITER ',';
CREATE EXTERNAL TABLE records (id int, name varchar(50), created date, region varchar(50))
   AS COPY FROM 's3://datalake/sales/*/*/*'
   PARTITION COLUMNS created, region;
CREATE EXTERNAL TABLE sales (itemID INT, date DATE, price FLOAT)
    AS COPY FROM '/data/sales/*.parquet' PARQUET;

CREATE EXTERNAL TABLE customers (name VARCHAR,
    address ROW(street VARCHAR, city VARCHAR, zipcode INT))
    AS COPY FROM '...' PARQUET;
CREATE LOCATION '/tmp' ALL NODES USAGE 'user';
GRANT ALL ON LOCATION '/tmp' to Bob;
CREATE SOURCE curl AS LANGUAGE 'C++' NAME 'CurlSourceFactory' LIBRARY curllib;
CREATE EXTERNAL TABLE curl_table1 as COPY SOURCE CurlSourceFactory;
CREATE TABLE employees(
                          employeeID INT,
                          personal ROW(
                              name VARCHAR,
                              address ROW(street VARCHAR, city VARCHAR, zipcode INT),
                              taxID INT),
                          department VARCHAR);
CREATE TABLE customers(
                          name VARCHAR,
                          contact ROW(
                              street VARCHAR,
                              city VARCHAR,
                              zipcode INT,
                              email ARRAY[VARCHAR]
                              ),
                          accountid INT );
SELECT ROW('Amy',2,false);
SELECT ROW('Amy',2,false) AS student(name, id, current);
SELECT ROW('Amy' AS name, 2 AS id, false AS current) AS student;
SELECT ARRAY[ROW('Amy' AS name, 2 AS id),ROW('Fred' AS first_name, 4 AS id)];
SELECT ROW('Amy',2.5::int,false::varchar);
SELECT ROW('Howard''s house',2,false);
SELECT id.name, major, GPA FROM students
WHERE id = ROW('alice',119, ARRAY['<EMAIL>','<EMAIL>']);
CREATE EXTERNAL TABLE customers (name VARCHAR,
    address ROW(street VARCHAR, city VARCHAR, zipcode INT))
    AS COPY FROM '...' PARQUET;
SELECT ROW(1, 'joe') > ROW(2, 'bob');
SELECT ROW(1, 'joe') > ROW(2, 'bob', 123);
select row(1, null, 3) = row(1, 2, 3);
SELECT contact::ROW(str VARCHAR, city VARCHAR, zip VARCHAR, email ARRAY[VARCHAR,20]) FROM customers;
CREATE EXTERNAL TABLE store (storeID INT, inventory MAP<INT,VARCHAR(100)>)
    AS COPY FROM '...' PARQUET;

CREATE SOURCE curl AS LANGUAGE 'C++' NAME 'CurlSourceFactory' LIBRARY curllib;
CREATE LIBRARY AggregateFunctions AS '/opt/vertica/sdk/examples/build/AggregateFunctions.so';
CREATE AGGREGATE FUNCTION ag_avg AS LANGUAGE 'C++' NAME 'AverageFactory'
   library AggregateFunctions;
 CREATE AGGREGATE FUNCTION ag_cat AS LANGUAGE 'C++' NAME 'ConcatenateFactory'
   library AggregateFunctions;
CREATE ANALYTIC FUNCTION an_rank AS LANGUAGE 'C++'
   NAME 'RankFactory' LIBRARY AnalyticFunctions;
CREATE FILTER Iconverter AS LANGUAGE 'C++' NAME 'IconverterFactory' LIBRARY IconverterLib;

CREATE FUNCTION myzeroifnull(x INT) RETURN INT
   AS BEGIN
     RETURN (CASE WHEN (x IS NOT NULL) THEN x ELSE 0 END);
END;

CREATE OR REPLACE FUNCTION myzeroifnull(x INT) RETURN INT
   AS BEGIN
     RETURN (CASE WHEN (x IS NULL) THEN 0 ELSE x END);
END;

CREATE PARSER BasicIntegerParser AS LANGUAGE 'C++' NAME 'BasicIntegerParserFactory' LIBRARY BasicIntegerParserLib;
CREATE SOURCE curl AS LANGUAGE 'C++' NAME 'CurlSourceFactory' LIBRARY curllib;
CREATE HCATALOG SCHEMA hcat WITH HOSTNAME='hcathost' PORT=9083
   HCATALOG_SCHEMA='default' HIVESERVER2_HOSTNAME='hs.example.com'
   SSL_CONFIG='/etc/hadoop/conf/ssl-client.xml' HCATALOG_USER='admin';
CREATE HCATALOG SCHEMA hcat
   WITH HOSTNAME='thrift://node1.example.com:9083,thrift://node2.example.com:9083';
CREATE HCATALOG SCHEMA hcat WITH HCATALOG_SCHEMA='default'
    HIVESERVER2_HOSTNAME='hs.example.com'
    CUSTOM_PARTITIONS='yes';
CREATE CA CERTIFICATE imported_validating_ca AS '-----BEGIN CERTIFICATE-----...-----END CERTIFICATE-----';
CREATE CA CERTIFICATE imported_signing_ca AS '-----BEGIN CERTIFICATE-----...-----END CERTIFICATE-----'

CREATE LIBRARY MyFunctions AS '/home/<USER>/my_functions.so';
CREATE LIBRARY MyOtherFunctions AS :libfile;
CREATE LIBRARY SomeFunctions AS 'S3://mybucket/extensions.so';
CREATE LIBRARY DeleteVowelsLib AS '/home/<USER>/JavaLib.jar'
   DEPENDS '/home/<USER>/mylibs/*' LANGUAGE 'Java';
CREATE LIBRARY mylib AS '/path/to/java_udx'
   DEPENDS '/path/to/jars/this.jar:/path/to/jars/that.jar' LANGUAGE 'Java';
CREATE LIBRARY s3lib AS 's3://mybucket/UdlLib.jar'
   DEPENDS '["s3://mybucket/gson-2.3.1.jar"]' LANGUAGE 'Java';

 CREATE NETWORK ADDRESS addr01 ON v_vmart_node0001 WITH '************';
CREATE NETWORK ADDRESS addr02 ON v_vmart_node0002 WITH '************';
CREATE NETWORK ADDRESS addr03 on v_vmart_node0003 WITH '************';
CREATE NETWORK ADDRESS addr04 on v_vmart_node0004 WITH '************';
CREATE LOAD BALANCE GROUP group_1 WITH ADDRESS addr01, addr02;
CREATE LOAD BALANCE GROUP group_2 WITH ADDRESS addr03, addr04;
CREATE LOAD BALANCE GROUP group_some WITH FAULT GROUP fault_1 FILTER
   '************/30';
CREATE LOAD BALANCE GROUP group_all WITH FAULT GROUP fault_1 FILTER
   '0.0.0.0/0';
CREATE LOCAL TEMP VIEW myview AS
SELECT SUM(annual_income), customer_state FROM public.customer_dimension
    WHERE customer_key IN (SELECT customer_key FROM store.store_sales_fact)
    GROUP BY customer_state
    ORDER BY customer_state ASC;
CREATE LOCATION '/home/<USER>/testloc' USAGE 'TEMP' LABEL 'tempfiles';
CREATE LOCATION 'hdfs://hadoopNS/vertica/colddata' ALL NODES SHARED
   USAGE 'data' LABEL 'coldstorage';
CREATE LOCATION '/tmp' ALL NODES USAGE 'user';
CREATE EXTERNAL TABLE ext1 (x integer) AS COPY FROM '/tmp/data/ext1.dat' DELIMITER ',';
CREATE LOCATION 's3://datalake' SHARED USAGE 'USER' LABEL 's3user';
GRANT READ ON LOCATION 's3://datalake' TO ExtUsers;
CREATE NETWORK ADDRESS node01 ON v_vmart_br_node0001 WITH '************';
CREATE NETWORK ADDRESS node02 ON v_vmart_br_node0002 WITH '************';
CREATE NETWORK ADDRESS node03 ON v_vmart_br_node0003 WITH '************';

CREATE NETWORK INTERFACE mynetwork ON v_vmart_node0001 WITH '*********' PORT 456 ENABLED;


CREATE NOTIFIER my_dc_notifier
    ACTION 'kafka://************:9092'
    MAXMEMORYSIZE '1G'
    IDENTIFIED BY 'f8b0278a-3282-4e1a-9c86-e0f3f042a971'
    NO CHECK COMMITTED;
CREATE NOTIFIER my_notifier
    ACTION 'kafka://127.0.0.1:9092'
    MAXMEMORYSIZE '10M'
    PARAMETERS 'queue.buffering.max.ms=1000';
CREATE NOTIFIER encrypted_notifier
    ACTION 'kafka://127.0.0.1:9092'
    MAXMEMORYSIZE '10M'
    TLSMODE 'verify-ca'
    CA BUNDLE ca_bundle;
CREATE PROFILE sample_profile LIMIT PASSWORD_MAX_LENGTH 20;

CREATE PROJECTION tradeproj (stock ENCODING RLE,
   GROUPED(bid ENCODING DELTAVAL, ask))
   AS (SELECT * FROM trades) KSAFE 1;
CREATE PROJECTION product (a, b, product_value) AS
SELECT a, b, a*b FROM values ORDER BY a KSAFE;
CREATE PROJECTION public.employee_dimension_super
    AS SELECT * FROM public.employee_dimension
       ORDER BY employee_key
           SEGMENTED BY hash(employee_key) ALL NODES;
CREATE PROJECTION store.store_dimension_proj (storekey, name, city, state)
             AS SELECT store_key, store_name, store_city, store_state
                FROM store.store_dimension
                         UNSEGMENTED ALL NODES;
CREATE TABLE public.store_orders(order_no int, order_date timestamp NOT NULL, shipper varchar(20), ship_date date);
ALTER TABLE store_orders PARTITION BY order_date::DATE GROUP BY date_trunc('month', (order_date)::DATE);
CREATE PROJECTION ytd_orders AS SELECT * FROM store_orders ORDER BY order_date
    ON PARTITION RANGE BETWEEN date_trunc('year',now())::date AND NULL;

CREATE TABLE values (a INT, b INT);
CREATE PROJECTION product (a, b, product_value) AS
SELECT a, b, a*b FROM values ORDER BY a KSAFE;
CREATE PROJECTION public.employee_dimension_super
    AS SELECT * FROM public.employee_dimension
       ORDER BY employee_key
           SEGMENTED BY hash(employee_key) ALL NODES;

CREATE TABLE clicks(
                       user_id INTEGER,
                       page_id INTEGER,
                       click_time TIMESTAMP NOT NULL);
SELECT page_id, click_time::DATE click_date, COUNT(*) num_clicks FROM clicks
WHERE click_time::DATE = '2015-04-30'
GROUP BY page_id, click_time::DATE ORDER BY num_clicks DESC;
CREATE PROJECTION clicks_agg AS
SELECT page_id, click_time::DATE click_date, COUNT(*) num_clicks FROM clicks
GROUP BY page_id, click_time::DATE KSAFE 1;
SELECT page_id, click_time::DATE click_date, COUNT(*) num_clicks FROM clicks
WHERE click_time::DATE = '2015-04-30' GROUP BY page_id, click_time::DATE
ORDER BY num_clicks DESC;
CREATE TABLE clicks(
                       user_id INTEGER,
                       page_id INTEGER,
                       click_time TIMESTAMP NOT NULL);
SELECT page_id, click_time::DATE click_date, COUNT(*) num_clicks FROM clicks
WHERE click_time::DATE = '2015-04-30'
GROUP BY page_id, click_time::DATE ORDER BY num_clicks DESC;
CREATE PROJECTION clicks_agg AS
SELECT page_id, click_time::DATE click_date, COUNT(*) num_clicks FROM clicks
GROUP BY page_id, click_time::DATE KSAFE 1;
SELECT page_id, click_time::DATE click_date, COUNT(*) num_clicks FROM clicks
WHERE click_time::DATE = '2015-04-30' GROUP BY page_id, click_time::DATE
ORDER BY num_clicks DESC;
CREATE TABLE readings (
                          meter_id INT,
                          reading_date TIMESTAMP,
                          reading_value FLOAT);
SELECT meter_id, reading_date, reading_value FROM readings
                                                      LIMIT 5 OVER (PARTITION BY meter_id ORDER BY reading_date DESC);

CREATE PROJECTION readings_topk (meter_id, recent_date, recent_value)
    AS SELECT meter_id, reading_date, reading_value FROM readings
                                                                    LIMIT 5 OVER (PARTITION BY meter_id ORDER BY reading_date DESC);
SELECT symbol, trade_time last_trade, price last_price FROM (
                                                                SELECT symbol, trade_time, price, ROW_NUMBER()
                                                                    OVER(PARTITION BY symbol ORDER BY trade_time DESC) rn FROM trades) trds WHERE rn <=1;
CREATE PROJECTION trades_topk AS
SELECT symbol, trade_time last_trade, price last_price FROM trades
                                                                LIMIT 1 OVER(PARTITION BY symbol ORDER BY trade_time DESC);
CREATE TABLE trades(
                       symbol CHAR(16) NOT NULL,
                       trade_time TIMESTAMP NOT NULL,
                       price NUMERIC(12,4),
                       volume INT )
    PARTITION BY (EXTRACT(year from trade_time) * 100 +
    EXTRACT(month from trade_time));
INSERT INTO trades VALUES('AAPL','2010-10-10 10:10:10'::TIMESTAMP,100.00,100);
INSERT INTO trades VALUES('AAPL','2010-10-10 10:10:10.3'::TIMESTAMP,101.00,100);
INSERT INTO trades VALUES ('AAPL','2011-10-10 10:10:10.5'::TIMESTAMP,106.1,1000);
INSERT INTO trades VALUES ('AAPL','2011-10-10 10:10:10.2'::TIMESTAMP,105.2,500);
INSERT INTO trades VALUES ('HPQ','2012-10-10 10:10:10.2'::TIMESTAMP,42.01,400);
INSERT INTO trades VALUES ('HPQ','2012-10-10 10:10:10.3'::TIMESTAMP,42.02,1000);
INSERT INTO trades VALUES ('HPQ','2012-10-10 10:10:10.4'::TIMESTAMP,42.05,100);
COMMIT;
CREATE PROJECTION trades_topk_a AS SELECT symbol, trade_time last_trade, price last_price
                                   FROM trades LIMIT 1 OVER(PARTITION BY symbol ORDER BY trade_time DESC);
SELECT symbol, trade_time last_trade, price last_price FROM trades
                                                                LIMIT 1 OVER(PARTITION BY symbol ORDER BY trade_time DESC);
CREATE PROJECTION trades_topk_b
    AS SELECT symbol, trade_time::DATE trade_date, trade_time, price close_price, volume
       FROM trades LIMIT 1 OVER(PARTITION BY symbol, trade_time::DATE ORDER BY trade_time DESC);
SELECT symbol, trade_time::DATE trade_date, trade_time, price close_price, volume
FROM trades LIMIT 1 OVER(PARTITION BY symbol, trade_time::DATE ORDER BY trade_time DESC);
CREATE TABLE documents ( doc_id INT PRIMARY KEY, text VARCHAR(140));
CREATE PROJECTION index_proj (doc_id, text)
     AS SELECT doc_id, text_index(doc_id, text)
    OVER (PARTITION PREPASS BY doc_id) FROM documents;
INSERT INTO documents VALUES
    (100, 'A SQL Query walks into a bar. In one corner of the bar are two tables.
 The Query walks up to the tables and asks - Mind if I join you?');
CREATE TABLE points( point_id INTEGER, point_type VARCHAR(10), coordinates GEOMETRY(100));

CREATE TABLE values (a INT, b INT);
CREATE PROJECTION values_product (a, b, c)
   AS SELECT a, b, a*b FROM values SEGMENTED BY HASH(a) ALL NODES KSAFE;
CREATE TABLE points(point_id INTEGER, lat NUMERIC(12,9), long NUMERIC(12,9));
CREATE PROJECTION points_p1
     AS SELECT point_id, lat, long, zorder(lat, long) zorder FROM points
        ORDER BY zorder(lat, long) SEGMENTED BY hash(point_id) ALL NODES;
CREATE TABLE values (a INT, b INT);
CREATE PROJECTION values_product (a, b, c)
   AS SELECT a, b, a*b FROM values SEGMENTED BY HASH(a) ALL NODES KSAFE;
COPY values FROM STDIN DELIMITER ',' DIRECT;
CREATE PROJECTION store.store_dimension_proj (storekey, name, city, state)
             AS SELECT store_key, store_name, store_city, store_state
                FROM store.store_dimension
                         UNSEGMENTED ALL NODES;
CREATE TABLE public.store_orders(order_no int, order_date timestamp NOT NULL, shipper varchar(20), ship_date date);
ALTER TABLE store_orders PARTITION BY order_date::DATE GROUP BY date_trunc('month', (order_date)::DATE);
CREATE PROJECTION ytd_orders AS SELECT * FROM store_orders ORDER BY order_date
    ON PARTITION RANGE BETWEEN date_trunc('year',now())::date AND NULL;
ALTER TABLE store_orders PARTITION BY order_date::DATE GROUP BY DATE_TRUNC('month', (order_date)::DATE);
CREATE PROJECTION ytd_orders AS SELECT * FROM store_orders ORDER BY order_date
    ON PARTITION RANGE BETWEEN date_trunc('year',now())::date AND NULL;
CREATE PROJECTION last_month_orders AS SELECT * FROM store_orders ORDER BY order_date ON PARTITION RANGE BETWEEN
     '2021-06-01' AND '2021-06-30';
ALTER PROJECTION last_month_orders_b0 ON PARTITION RANGE BETWEEN
     add_months(date_trunc('month', now())::date, -1) AND NULL;
SELECT * from store_orders WHERE order_date BETWEEN
                                     add_months(date_trunc('month', now())::date, -1) AND
                                     add_months(date_trunc('month', now())::date + dayofmonth(now()), -1);
CREATE PROJECTION mtd_orders AS SELECT * FROM store_orders ON PARTITION RANGE BETWEEN
     date_trunc('month', now())::date AND NULL;
CREATE RESOURCE POOL ceo_pool MEMORYSIZE '1800M' PRIORITY 10;
GRANT USAGE ON RESOURCE POOL ceo_pool to ceo_user;
CREATE RESOURCE POOL rp3 RUNTIMECAP '5 minutes';
CREATE RESOURCE POOL rp2 RUNTIMECAP '3 minutes' CASCADE TO rp3;
CREATE RESOURCE POOL rp1 RUNTIMECAP '1 minute' CASCADE TO rp2;
SET SESSION RESOURCE_POOL = rp1;
CREATE RESOURCE POOL dashboard FOR SUBCLUSTER analytics_1;
CREATE ROLE roleA;

ALTER SCHEDULE daily_schedule USING CRON '0 8 * * *';
ALTER SCHEDULE my_schedule USING DATETIMES('2023-10-01 12:30:00', '2022-11-01 12:30:00');
ALTER SCHEDULE daily_schedule RENAME TO daily_8am_gmt;

CREATE SCHEMA s1;
CREATE SCHEMA IF NOT EXISTS s2;
CREATE SCHEMA n1.s3;
CREATE SEQUENCE my_seq START 100;
CREATE TABLE customer(id INTEGER DEFAULT my_seq.NEXTVAL,
                      lname VARCHAR(25),
                      fname VARCHAR(25),
                      membership_card INTEGER
);
CREATE SUBNET mysubnet WITH 'fd9b:1fcc:1dc4:78d3::';
CREATE SUBNET mySubnet WITH '*********';
CREATE TABLE public.Employee_Dimension (
                                           Employee_key                   integer PRIMARY KEY NOT NULL,
                                           Employee_gender                varchar(8) ENCODING RLE,
                                           Courtesy_title                 varchar(8),
                                           Employee_first_name            varchar(64),
                                           Employee_middle_initial        varchar(8),
                                           Employee_last_name             varchar(64)
);

CREATE PROJECTION public.employee_dimension_super
    AS SELECT * FROM public.employee_dimension
       ORDER BY employee_key
           SEGMENTED BY hash(employee_key) ALL NODES;
CREATE PROJECTION store.store_dimension_proj (storekey, name, city, state)
             AS SELECT store_key, store_name, store_city, store_state
                FROM store.store_dimension
                         UNSEGMENTED ALL NODES;
CREATE TABLE testAutoProj(c10 char (10), v1 varchar(140) DEFAULT v2||v3, i int, c5 char(5), v3 varchar (80), d timestamp, v2 varchar(60), c1 char(1));
CREATE PROJECTION public.testAutoProj_b0 /*+basename(testAutoProj),createtype(L)*/
( c10, v1, i, c5, v3, d, v2, c1 )
AS
SELECT testAutoProj.c10,
       testAutoProj.v1,
       testAutoProj.i,
       testAutoProj.c5,
       testAutoProj.v3,
       testAutoProj.d,
       testAutoProj.v2,
       testAutoProj.c1
FROM public.testAutoProj
ORDER BY testAutoProj.c10,
         testAutoProj.v1,
         testAutoProj.i,
         testAutoProj.c5,
         testAutoProj.v3,
         testAutoProj.d,
         testAutoProj.v2,
         testAutoProj.c1
    SEGMENTED BY hash(testAutoProj.i, testAutoProj.c5, testAutoProj.d, testAutoProj.c1, testAutoProj.c10, testAutoProj.v2, testAutoProj.v3, testAutoProj.v1) ALL NODES OFFSET 0;
CREATE TABLE company.store.Premium_Customer
(
    ID IDENTITY ,
    lname varchar(25),
    fname varchar(25),
    store_membership_card int
);
CREATE TABLE All_Customers LIKE Premium_Customer;
CREATE TABLE cust_basic_profile AS SELECT
                                       customer_key, customer_gender, customer_age, marital_status, annual_income, occupation
                                   FROM customer_dimension WHERE customer_age>18 AND customer_gender !='';
CREATE TABLE orders(
                       orderkey    INT,
                       custkey     INT,
                       prodkey     ARRAY[VARCHAR(10)],
                       orderprices ARRAY[DECIMAL(12,2)],
                       orderdate   DATE
);
CREATE TABLE inventory
(store INT, products ROW(name VARCHAR, code VARCHAR));
CREATE SCHEMA internal DISK_QUOTA '10T';
CREATE TABLE airport.airline.flights(
                                        flight_number INT,
                                        leaving_from VARCHAR,
                                        arriving_at VARCHAR,
                                        expected_departure DATE,
                                        gate VARCHAR
);
CREATE TEMP TABLE customer_occupations (name, profession)
   AS SELECT customer_name, occupation FROM customer_dimension;

SELECT certificate, certificate, certificate, LISTAGG(ca_certificate) AS ca_certificates, cipher_suites, mode
FROM tls_configurations
WHERE name='LDAPAuth'
GROUP BY name, owner, certificate, cipher_suites, mode
ORDER BY 1;
SELECT * FROM tls_configurations WHERE name='LDAPAuth';

CREATE PROCEDURE revoke_all_on_table(table_name VARCHAR, user_name VARCHAR)
    LANGUAGE PLvSQL
AS $$
BEGIN
EXECUTE 'REVOKE ALL ON ' || QUOTE_IDENT(table_name) || ' FROM ' || QUOTE_IDENT(user_name);
END;
$$;
CREATE SCHEDULE 24_hours_later USING DATETIMES('2022-12-16 12:00:00');

CREATE USER Fred IDENTIFIED BY 'Mxyzptlk';
GRANT USAGE ON SCHEMA PUBLIC to Fred;
CREATE FLEXIBLE TABLE darkdata();
CREATE FLEX TABLE darkdata1 (date_col date NOT NULL) partition by
  extract('year' from date_col);
CREATE flex external table mountains() AS COPY FROM 'home/release/KData/kmm_ountains.json' PARSER fjsonparser();
DEACTIVATE DIRECTED QUERY RegionalSalesProducts_JoinTables;
DISCONNECT DEFAULT;
DO LANGUAGE PLvSQL $$
DECLARE
x int := 3;
    y varchar := 'some string';
BEGIN
    RAISE NOTICE 'x = %', x;
    RAISE NOTICE 'y = %', y;
END;
$$;
DROP ACCESS POLICY ON customer FOR COLUMN Customer_Number;
DROP ACCESS POLICY ON customer_info FOR ROWS;

DROP AGGREGATE FUNCTION ag_avg(numeric);

DROP ANALYTIC FUNCTION analytic_avg(numeric);
     DROP AUTHENTICATION md5_auth;
CREATE AUTHENTICATION localpwd METHOD 'password' LOCAL;
GRANT AUTHENTICATION localpwd TO jsmith;
DROP AUTHENTICATION localpwd CASCADE;
DROP CERTIFICATE ca_cert CASCADE;
  DROP DIRECTED QUERY WHERE save_plans_version = 21;
DROP FAULT GROUP group2;
DROP FUNCTION macros.zerowhennull(x INT);

DROP KEY k_ca IF EXISTS;
DROP KEY k_client CASCADE;

DROP LIBRARY ml.MyLib CASCADE;

GRANT UDXDEVELOPER TO alice, bob;
SET ROLE UDXDEVELOPER;
GRANT DROP ON LIBRARY ml.mylib to bob;
SET ROLE UDXDEVELOPER;
DROP LIBRARY ml.mylib cascade;
DROP LOAD BALANCE GROUP group_all;
 DROP MODEL mySvmClassModel;

DROP NAMESPACE `analytics` CASCADE;
DROP NETWORK ADDRESS node01;
DROP NOTIFIER requests_issued CASCADE;
DROP PARSER BasicIntegerParser();
DROP PROCEDURE helloplanet(arg1 varchar);
CREATE PROCEDURE raiseXY(IN x INT, y VARCHAR) LANGUAGE PLvSQL AS $$
BEGIN
    RAISE NOTICE 'x = %', x;
    RAISE NOTICE 'y = %', y;
    -- some processing statements
END;
$$;

CALL raiseXY(3, 'some string');
DROP PROCEDURE raiseXY(INT, VARCHAR);
DROP PROFILE sample_profile;
DROP RESOURCE POOL ceo_pool;
DROP RESOURCE POOL dashboard FOR CURRENT SUBCLUSTER;
DROP ROLE appadmin;
DROP ROUTING RULE internal_clients;
DROP ROUTING RULE FOR WORKLOAD analytics;
DROP SCHEDULE monthly_schedule;
DROP SEQUENCE sequential;
DROP SCHEMA S1;
DROP SCHEMA S1 CASCADE;
DROP SOURCE curl();
DROP SUBNET mySubnet;
DROP TEXT INDEX t_text_index;
DROP TRANSFORM FUNCTION macros.tokenize(varchar);
DROP TRANSFORM FUNCTION online.Pagerank();

DROP USER IF EXISTS user1;
DROP USER IF EXISTS user1 CASCADE;

DROP VIEW myview;
DROP ACCESS POLICY ON customer FOR COLUMN Customer_Number;
 DROP ACCESS POLICY ON customer_info FOR ROWS;
EXECUTE DATA LOADER sales_dl
   WITH FILES 's3://MyBucket/sales/2023/09*.dat' EXPAND_GLOB,
              's3://MyBucket/sales/2023/specials.dat' EXACT_PATH;
EXPORT TO DELIMITED(directory = '/mnt/shared_nfs/accounts/rm')
   OVER(PARTITION BY hash)
   AS
SELECT
    account_id,
    json
FROM
    (
        SELECT 1 as account_id, '{}' as json, 0 hash
        UNION ALL
        SELECT 2 as account_id, '{}' as json, 1 hash
    ) a;
EXPORT TO DELIMITED(directory='webhdfs:///user1/data', delimiter=',', addHeader='true')
  AS SELECT * FROM public.sales;
EXPORT TO JSON(directory = '/mnt/shared_nfs/accounts/rm')
   OVER(PARTITION BY hash)
   AS
SELECT
    account_id,
    json
FROM
    (
        SELECT 1 as account_id, '{}' as json, 0 hash
        UNION ALL
        SELECT 2 as account_id, '{}' as json, 1 hash
    ) a;
EXPORT TO PARQUET(directory='webhdfs:///user1/data',
     fileMode='432', dirMode='rwxrw-r-x')
  AS SELECT * FROM public.T1;
EXPORT TO PARQUET(directory='webhdfs:///user3/data')
  OVER(ORDER BY col1) AS SELECT col1 + col1 AS A, col2
                         FROM public.T3;
EXPORT TO PARQUET(directory='gs://DataLake/user2/data')
  OVER(PARTITION BY store.region ORDER BY store.ID)
  AS SELECT sale.price, sale.date, store.ID
     FROM public.sales sale
              JOIN public.vendor store ON sale.distribID = store.ID;
EXPORT TO PARQUET(directory='s3://DataLake/sales_by_region')
   AS SELECT sale.price, sale.date, store.region
      FROM public.sales sale
               JOIN public.vendor store ON sale.distribID = store.ID;
EXPORT TO PARQUET(directory='webhdfs:///user1/data', compression='gzip')
  AS SELECT * FROM public.T1;
EXPORT TO VERTICA testdb.ma_customers(customer_key, customer_name, annual_income)
   AS SELECT customer_key, customer_name, annual_income FROM customer_dimension WHERE customer_state = 'MA';
GET DIRECTED QUERY
SELECT employee_first_name, employee_last_name
FROM employee_dimension
WHERE employee_city='Boston' AND job_title='Cashier'
ORDER BY employee_last_name, employee_first_name;
GRANT AUTHENTICATION v_ldap TO jsmith;
CREATE ROLE DBprogrammer;
GRANT AUTHENTICATION v_gss TO DBprogrammer;
GRANT AUTHENTICATION v_localpwd TO PUBLIC;

CREATE KEY new_ca_key TYPE 'RSA' LENGTH 2048;
 CREATE CA CERTIFICATE new_ca_cert
    SUBJECT '/C=US/ST=Massachusetts/L=Cambridge/O=Micro Focus/OU=Vertica/CN=Vertica example CA'
    VALID FOR 3650
    EXTENSIONS 'authorityKeyIdentifier' = 'keyid:always,issuer', 'nsComment' = 'new CA'
    KEY new_ca_key;

CREATE USER u1;
GRANT USAGE ON KEY new_ca_key TO u1;
GRANT ALTER ON TLS CONFIGURATION data_channel TO u1;

ALTER TLS CONFIGURATION data_channel ADD CA CERTIFICATES new_ca_cert;

-- clean up:
ALTER TLS CONFIGURATION data_channel REMOVE CA CERTIFICATES new_ca_cert;
DROP KEY new_ca_key CASCADE;
DROP USER u1;

GRANT USAGE on RESOURCE POOL Joe_pool FOR SUBCLUSTER sub1 TO Joe;
GRANT USAGE ON RESOURCE POOL Joe_pool TO Joe;
GRANT ALTER ON TLS CONFIGURATION DATA_CHANNEL TO client_server_tls_manager;
GRANT EXECUTE ON FUNCTION myzeroifnull (x INT) TO Bob, Jules, Operator;
GRANT EXECUTE ON TRANSFORM FUNCTION tokenize(VARCHAR) TO Bob, Operator;
GRANT EXECUTE ON SOURCE ExampleSource() TO Alice;
GRANT ALL ON SOURCE ExampleSource() TO Alice;
GRANT ALL ON TRANSFORM FUNCTION Pagerank(z varchar) to dbadmin;
GRANT USAGE ON WORKLOAD analytics TO analytics_role;
GRANT USAGE ON WORKLOAD analytics TO jacob;

INSERT INTO t1 VALUES (101, 102, 103, 104);
INSERT INTO customer VALUES (10, 'male', 'DPR', 'MA', 35);
INSERT INTO start_time VALUES (12, 'film','05:10:00:01');
INSERT INTO retail.t1 (C0, C1) VALUES (1, 1001);
INSERT INTO films SELECT * FROM tmp_films WHERE date_prod < '2004-05-07';
INSERT INTO t1 (col1, col2) VALUES ('abc', (SELECT mycolumn FROM mytable));
INSERT INTO t1 (col1, col2) (SELECT 'abc', mycolumn FROM mytable);
INSERT INTO flex1(a,b) VALUES (1, 'x');
INSERT INTO flex2(a, b) SELECT a, b, '2016-08-10 11:10' c, 'Hello' d, 3.1415 e, f from flex1;
INSERT INTO inventory(product) VALUES(LookUpProducts());
INSERT INTO inventory(product) VALUES(ROW('xbox',165));

LOCK customer_info IN SHARE MODE NOWAIT;
MERGE INTO t2 USING t1 ON t1.a = t2.a
    WHEN MATCHED THEN UPDATE SET b = t1.b
    WHEN NOT MATCHED THEN INSERT (a, b) VALUES (t1.a, t1.b);
MERGE INTO target t USING source s ON t.a = s.a
    WHEN MATCHED THEN UPDATE SET a=s.a + 1, b=s.b, c=s.c - 1
    WHEN NOT MATCHED THEN INSERT(a,b,c) VALUES(s.a, s.b, s.c);
PROFILE SELECT customer_name, annual_income FROM public.customer_dimension WHERE (customer_gender, annual_income) IN (SELECT customer_gender, MAX(annual_income) FROM public.customer_dimension GROUP BY customer_gender);

INSERT INTO product_key VALUES (101);
 SAVEPOINT my_savepoint;
 INSERT INTO product_key VALUES (102);
 RELEASE SAVEPOINT my_savepoint;

REPLICATE INCLUDE ".default_namespace.*.*" FROM source_db;
REPLICATE ".airport.airline.flights" FROM source_db TARGET_NAMESPACE airport2;
REPLICATE INCLUDE "public.*" EXCLUDE '*.customer_*' FROM source_db;

REVOKE AUTHENTICATION v_ldap FROM jsmith;
REVOKE AUTHENTICATION v_gss FROM DBprogrammer;
REVOKE AUTHENTICATION localpwd FROM PUBLIC;
REVOKE CREATE ON DATABASE DEFAULT FROM Fred;
REVOKE TEMP ON DATABASE DEFAULT FROM Fred;

REVOKE USAGE ON KEY new_key FROM u1;
REVOKE USAGE ON MODEL mySvmClassModel FROM Fred;
REVOKE EXECUTE ON PROCEDURE tokenize(varchar) FROM Bob;
REVOKE USAGE ON RESOURCE POOL Joe_pool FROM Joe;
REVOKE USAGE ON RESOURCE POOL Joe_pool FOR SUBCLUSTER sub1 FROM Joe;
REVOKE ALL PRIVILEGES ON TABLE customer_dimension FROM Joe;
REVOKE EXECUTE ON FUNCTION myzeroifnull (x INT) FROM Bob;
REVOKE ALL ON TRANSFORM FUNCTION Pagerank (t float) FROM Doug;
REVOKE EXECUTE ON ALL FUNCTIONS IN SCHEMA zeroschema FROM Bob;
REVOKE EXECUTE ON TRANSFORM FUNCTION tokenize(VARCHAR) FROM Bob;
REVOKE ALL ON SOURCE ExampleSource() FROM Alice;
REVOKE SELECT ON test_view FROM Joe;
REVOKE USAGE ON WORKLOAD analytics FROM analytics_role;
REVOKE USAGE ON WORKLOAD analytics FROM jacob;

INSERT INTO product_key VALUES (101);
SAVEPOINT my_savepoint;
INSERT INTO product_key VALUES (102);
INSERT INTO product_key VALUES (103);
ROLLBACK TO SAVEPOINT my_savepoint;
INSERT INTO product_key VALUES (104);
COMMIT;

INSERT INTO T1 (product_key) VALUES (101);
SAVEPOINT my_savepoint;
INSERT INTO T1 (product_key) VALUES (102);
INSERT INTO T1 (product_key) VALUES (103);
ROLLBACK TO SAVEPOINT my_savepoint;
INSERT INTO T1 (product_key) VALUES (104);
COMMIT;
SELECT product_key FROM T1;

WITH revenue ( vkey, total_revenue ) AS (
    SELECT vendor_key, SUM(total_order_cost)
    FROM store.store_orders_fact
    GROUP BY vendor_key ORDER BY vendor_key)

SELECT v.vendor_name, v.vendor_address, v.vendor_city, r.total_revenue
FROM vendor_dimension v JOIN revenue r ON v.vendor_key = r.vkey
WHERE r.total_revenue = (SELECT MAX(total_revenue) FROM revenue )
ORDER BY vendor_name;

-- define WITH clause
WITH revenue ( vkey, total_revenue ) AS (
    SELECT vendor_key, SUM(total_order_cost)
    FROM store.store_orders_fact
    GROUP BY vendor_key ORDER BY 1)
-- End WITH clause

-- primary query
SELECT v.vendor_name, v.vendor_address, v.vendor_city, r.total_revenue
FROM vendor_dimension v JOIN revenue r ON v.vendor_key = r.vkey
WHERE r.total_revenue = (SELECT MAX(total_revenue) FROM revenue )
ORDER BY vendor_name;

WITH
-- query sale amounts for each region
regional_sales (region, total_sales) AS (
    SELECT sd.store_region, SUM(of.total_order_cost) AS total_sales
    FROM store.store_dimension sd JOIN store.store_orders_fact of ON sd.store_key = of.store_key
GROUP BY store_region ),
-- query previous result set
    top_regions AS (
SELECT region, total_sales
FROM regional_sales ORDER BY total_sales DESC LIMIT 3
    )

-- primary query
-- aggregate sales in top_regions result set
SELECT sd.store_region AS region, pd.department_description AS department, SUM(of.total_order_cost) AS product_sales
FROM store.store_orders_fact of
JOIN store.store_dimension sd ON sd.store_key = of.store_key
    JOIN public.product_dimension pd ON of.product_key = pd.product_key
WHERE sd.store_region IN (SELECT region FROM top_regions)
GROUP BY ROLLUP (region, department) ORDER BY region, product_sales DESC, GROUPING_ID();
CREATE TABLE total_store_sales (store_key int, region VARCHAR(20), store_sales numeric (12,2));

INSERT INTO total_store_sales
WITH store_sales AS (
    SELECT sd.store_key, sd.store_region::VARCHAR(20), SUM (of.total_order_cost)
    FROM store.store_dimension sd JOIN store.store_orders_fact of ON sd.store_key = of.store_key
GROUP BY sd.store_region, sd.store_key ORDER BY sd.store_region, sd.store_key)
SELECT * FROM store_sales;

SELECT * FROM T1
WHERE T1.x IN
      (SELECT MAX(c1) FROM T2
       EXCEPT
       SELECT MAX(cc1) FROM T3
       EXCEPT
       SELECT MAX(d1) FROM T4);

SELECT id, emp_lname FROM Company_A
EXCEPT
SELECT id, emp_lname FROM Company_B;

SELECT id, emp_lname FROM Company_A
EXCEPT
SELECT id, emp_lname FROM Company_B
ORDER BY emp_lname ASC;

SELECT id, emp_lname FROM Company_A
EXCEPT
SELECT id, emp_lname FROM Company_B
EXCEPT
SELECT id, emp_lname FROM Company_C;

SELECT customer_key, customer_name FROM public.customer_dimension
WHERE customer_key IN (SELECT customer_key FROM store.store_sales_fact
                       WHERE sales_dollar_amount > 500
                       EXCEPT
                       SELECT customer_key FROM store.store_sales_fact
                       WHERE tender_type = 'Cash')
  AND customer_state = 'CT';

SELECT customer_name, customer_state FROM customer_dimension TABLESAMPLE(0.5) WHERE customer_state='IL';
SELECT employee_last_name, SUM(vacation_days)
FROM employee_dimension
WHERE employee_last_name ILIKE 'S%'
GROUP BY employee_last_name;
SELECT vendor_region, MAX(deal_size) as "Biggest Deal"
FROM vendor_dimension
GROUP BY vendor_region
HAVING MAX(deal_size) > 900000;
SELECT department, grants, SUM(apply_sum(grant_values))
FROM employees
GROUP BY grants, department;
SELECT * FROM expenses ORDER BY Category, Year;
SELECT Category, Year, SUM(Amount) FROM expenses
GROUP BY CUBE(Category, Year) ORDER BY 1, 2, GROUPING_ID();
SELECT Category, Year, SUM(Amount) FROM expenses
GROUP BY CUBE(Category,Year) HAVING GROUPING(Year)=1;
SELECT Category, Year, SUM (Amount) FROM expenses
GROUP BY CUBE(Category,Year) HAVING GROUPING_ID(Category,Year)<2
ORDER BY 1, 2, GROUPING_ID();
SELECT Category, Year, SUM(Amount) FROM expenses
GROUP BY GROUPING SETS((Category, Year), (Year))
ORDER BY 1, 2, GROUPING_ID();
SELECT Category, Year, SUM(Amount) FROM expenses
GROUP BY GROUPING SETS((Category, Year), (Year), ())
ORDER BY 1, 2, GROUPING_ID();

SELECT Category, Year, SUM(Amount) FROM expenses
GROUP BY ROLLUP(Category, Year) ORDER BY 1,2, GROUPING_ID();
SELECT Category, Year, SUM(Amount) FROM expenses
GROUP BY ROLLUP(Category,Year) HAVING GROUPING(Year)=1
ORDER BY 1, 2, GROUPING_ID();
SELECT Category, Year, SUM(Amount) FROM expenses
GROUP BY ROLLUP(Category,Year) HAVING GROUPING_ID(Category,Year)<3
ORDER BY 1, 2, GROUPING_ID();

SELECT employee_last_name, MAX(annual_salary) as highest_salary FROM employee_dimension
GROUP BY employee_last_name HAVING MAX(annual_salary) > 800000 ORDER BY highest_salary DESC;

SELECT * FROM T1
WHERE T1.x IN
      (SELECT MAX(c1) FROM T2
       INTERSECT
       SELECT MAX(cc1) FROM T3
       INTERSECT
       SELECT MAX(d1) FROM T4);
SELECT id, emp_lname FROM Company_A
INTERSECT
SELECT id, emp_lname FROM Company_B;

SELECT id, emp_lname, sales FROM Company_A
INTERSECT
SELECT id, emp_lname, sales FROM Company_B
ORDER BY sales DESC;

SELECT id, emp_lname, sales FROM Company_A
INTERSECT
(SELECT id, emp_lname, sales FROM company_B WHERE sales > 1000)
ORDER BY sales DESC;

SELECT id, emp_lname FROM Company_A
INTERSECT
SELECT id, emp_lname FROM Company_B
INTERSECT
SELECT id, emp_lname FROM Company_C;

SELECT id, emp_lname FROM Company_A
INTERSECT
SELECT emp_lname, id FROM Company_B;

SELECT customer_key, customer_name from public.customer_dimension
WHERE customer_key IN (SELECT customer_key
                       FROM online_sales.online_sales_fact
                       WHERE sales_dollar_amount > 400
                       INTERSECT
                       SELECT customer_key FROM online_sales.online_sales_fact
                       WHERE sales_dollar_amount > 500)
  AND customer_state = 'CT' ORDER BY customer_key;

SELECT customer_key,customer_name FROM public.customer_dimension
WHERE customer_key IN (SELECT customer_key
                       FROM online_sales.online_sales_fact
                       WHERE sales_dollar_amount > 400
                         AND sales_dollar_amount < 500)
  AND customer_state = 'CT' ORDER BY customer_key;

SELECT * INTO TABLE newTable FROM customer_dimension;

SELECT * INTO TEMP TABLE newTempTable FROM customer_dimension;

SELECT * INTO LOCAL TEMP TABLE newTempTableLocal ON COMMIT PRESERVE ROWS
FROM customer_dimension;

SELECT store_region, store_city||', '||store_state location, store_name, number_of_employees
FROM store.store_dimension WHERE number_of_employees <= 12 ORDER BY store_region, number_of_employees LIMIT 10;

SELECT store_region, store_city||', '||store_state location, store_name, number_of_employees FROM store.store_dimension
                                                                                                      LIMIT 2 OVER (PARTITION BY store_region ORDER BY number_of_employees ASC);
SELECT uid,
       sid,
       ts,
       refurl,
       pageurl,
    action,
    event_name(),
    pattern_id(),
    match_id()
FROM clickstream_log
    MATCH
    (PARTITION BY uid, sid ORDER BY ts
    DEFINE
    Entry    AS RefURL  NOT ILIKE '%website2.com%' AND PageURL ILIKE '%website2.com%',
    Onsite   AS PageURL ILIKE     '%website2.com%' AND Action='V',
    Purchase AS PageURL ILIKE     '%website2.com%' AND Action = 'P'
    PATTERN
    P AS (Entry Onsite* Purchase)
    ROWS MATCH FIRST EVENT);

SELECT customer_name, customer_gender FROM customer_dimension
WHERE occupation='Dancer' AND customer_city = 'San Francisco' ORDER BY customer_name;

SELECT customer_name, customer_gender FROM customer_dimension
WHERE occupation='Dancer' AND customer_city = 'San Francisco' ORDER BY customer_name OFFSET 8;

SELECT PolygonPoint(geom) OVER(PARTITION BY geom)
   AS SEL_0 FROM t ORDER BY geog;

SELECT bid, symbol, TS_FIRST_VALUE(bid) FROM Tickstore
                                                 TIMESERIES slice_time AS '5 seconds' OVER (PARTITION BY symbol ORDER BY ts);

SELECT symbol, AVG(first_bid) as avg_bid FROM (
                                                  SELECT symbol, slice_time, TS_FIRST_VALUE(bid1) AS first_bid
                                                  FROM Tickstore
                                                  WHERE symbol IN ('MSFT', 'IBM')
                                                      TIMESERIES slice_time AS '5 seconds' OVER (PARTITION BY symbol ORDER BY ts)
                                              ) AS resultOfGFI
GROUP BY symbol;

SELECT DISTINCT customer_key, customer_name FROM public.customer_dimension
WHERE customer_key IN
      (SELECT customer_key FROM store.store_sales_fact WHERE sales_dollar_amount > 500
       UNION ALL
       SELECT customer_key FROM online_sales.online_sales_fact WHERE sales_dollar_amount > 500)
  AND customer_state = 'CT';

SELECT name, cuisine FROM restaurants
WHERE CONTAINS(locations,ROW('Pittsburgh', 'PA'));

SELECT EXPLODE(ARRAY[1,2,null,4]) OVER();
SELECT EXPLODE(ARRAY[]::ARRAY[INT]) OVER();
SELECT EXPLODE(NULL::ARRAY[INT]) OVER();

WITH menu_entries AS
         (SELECT name, cuisine,
                 EXPLODE(menu USING PARAMETERS skip_partitioning=true) AS (idx, menu_entry)
          FROM restaurants WHERE CONTAINS(locations,ROW('Pittsburgh', 'PA')))
SELECT name, cuisine, menu_entry FROM menu_entries WHERE cuisine = 'Italian'
UNION ALL
SELECT name, cuisine, menu_entry FROM menu_entries WHERE menu_entry.price <= 10;

SELECT DISTINCT customer_key, customer_name FROM public.customer_dimension
WHERE customer_key IN
      (SELECT customer_key FROM store.store_sales_fact WHERE sales_dollar_amount > 500
       UNION ALL
       SELECT customer_key FROM online_sales.online_sales_fact WHERE sales_dollar_amount > 500)
  AND customer_state = 'CT';

SELECT name, cuisine FROM restaurants
WHERE CONTAINS(locations,ROW('Pittsburgh', 'PA'));
SELECT id, emp_name FROM company_a
UNION DISTINCT SELECT id, emp_name FROM company_b ORDER BY id;
SELECT id, emp_name FROM company_a
UNION ALL SELECT id, emp_name FROM company_b ORDER BY id;

(SELECT id, emp_name, sales FROM company_a ORDER BY sales DESC LIMIT 2)
UNION ALL
(SELECT id, emp_name, sales FROM company_b ORDER BY sales DESC LIMIT 2);

SELECT id, emp_name, sales FROM company_a
UNION
SELECT id, emp_name, sales FROM company_b
ORDER BY sales;

(SELECT 'Company A' as company, dept, SUM(sales) FROM company_a
 GROUP BY dept)
UNION
(SELECT 'Company B' as company, dept, SUM(sales) FROM company_b
 GROUP BY dept)
ORDER BY 1;

SELECT DISTINCT customer_name
FROM customer_dimension
WHERE customer_region = 'East'
  AND customer_name ILIKE 'Amer%';

SELECT customer_name, customer_gender FROM customer_dimension
WHERE occupation='Dancer' AND customer_city = 'San Francisco' ORDER BY customer_name OFFSET 8;

SELECT PolygonPoint(geom) OVER(PARTITION BY geom)
   AS SEL_0 FROM t ORDER BY geom;

SELECT symbol, AVG(first_bid) as avg_bid FROM (
                                                  SELECT symbol, slice_time, TS_FIRST_VALUE(bid1) AS first_bid
                                                  FROM Tickstore
                                                  WHERE symbol IN ('MSFT', 'IBM')
                                                      TIMESERIES slice_time AS '5 seconds' OVER (PARTITION BY symbol ORDER BY ts)
                                              ) AS resultOfGFI
GROUP BY symbol;


WITH RECURSIVE managers (employeeID, employeeName, sectionID, section, lead, leadID)
                   AS (SELECT emp_id, fname||' '||lname, section_id, section_name, section_leader, leader_id
                       FROM personnel.employees WHERE fname||' '||lname = 'Eric Redfield'
                       UNION
                       SELECT emp_id, fname||' '||lname AS employee_name, section_id, section_name, section_leader, leader_id FROM personnel.employees e
                                                                                                                                       JOIN managers m ON m.employeeID = e.leader_id)
SELECT employeeID, employeeName, lead AS 'Reports to', section, leadID from managers ORDER BY sectionID, employeeName;

SET DATESTYLE TO German;
SET DATESTYLE TO SQL;
SET DATESTYLE TO Postgres, MDY;
SET ESCAPE_STRING_WARNING TO OFF;

set role all except role1;
SET ROLE all;
SET SEARCH_PATH TO DEFAULT;
SET SESSION AUTHORIZATION debuguser;
SET SESSION AUTHORIZATION DEFAULT;
SET SESSION AUTOCOMMIT TO on;
SET SESSION TEMPSPACECAP '40%';
SET SESSION TEMPSPACECAP '20G';

SET SESSION WORKLOAD analytics;

SET TIME ZONE TO DEFAULT;
SET TIME ZONE TO 'PST8PDT'; -- Berkeley, California
SET TIME ZONE TO 'Europe/Rome'; -- Italy
SET TIME ZONE TO '-7'; -- UDT offset equivalent to PDT
SET TIME ZONE TO INTERVAL '-08:00 HOURS';

SHOW ALL;
SHOW DATABASE DEFAULT ALL;
SHOW NODE v_vmart_node0001 ALL;

UPDATE fact SET price = price - cost * 80 WHERE cost > 100;
UPDATE retail.customer SET state = 'NH' WHERE CID > 100;
UPDATE result_table r
SET address=n.new_address
    FROM new_addresses n
WHERE r.cust_id = n.new_cust_id;

UPDATE singers SET bands=ARRAY_CAT(bands,ARRAY['something new'])
WHERE lname='Cher';