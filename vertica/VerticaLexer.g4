lexer grammar VerticaLexer;
options {
    caseInsensitive = true;
}
//Keywords
CA: 'CA';
BUNDLE: 'BUNDLE';
CERTIFICATES: 'CERTIFICATES';
LOADER: 'LOADER';
RETRY: 'RETRY';
RETENTION: 'RETENTION';
RANDOM: 'RANDOM';
ROUNDROBIN: 'ROUNDROBIN';
CONFIGURATION: 'CONFIGURATION';
SUBCLUSTER: 'SUBCLUSTER';
CERTIFICATE: 'CERTIFICATE';
WORKLOAD: 'WORKLOAD';
SCHEDULE: 'SCHEDULE';
CRON: 'CRON';
DATETIMES: 'DATETIMES';
DISK_QUOTA: 'DISK_QUOTA';
CIPHER: 'CIPHER';
SUITES: 'SUITES';
ALL:'ALL';
CALL:'CALL';
COLUMNS:'COLUMNS';

AND:'AND';

ANY:'ANY';

ARRAY:'ARRAY';

AS:'AS';

ASC:'ASC';

AUTHORIZATION:'AUTHORIZATION';

BETWEEN:'BETWEEN';

BIGINT:'BIGINT';

BINARY:'BINARY';

BIT:'BIT';

BOOLEAN:'BOOLEAN';

BOTH:'BOTH';

CASE:'CASE';

CAST:'CAST';

CHAR:'CHAR';

CHAR_LENGTH
:'CHAR_LENGTH';

CHARACTER_LENGTH
:'CHARACTER_LENGTH';

CHECK:'CHECK';

COLLATE:'COLLATE';

COLUMN:'COLUMN';

CONSTRAINT:'CONSTRAINT';

CORRELATION:'CORRELATION';

CREATE:'CREATE';

CROSS:'CROSS';

CURRENT_DATABASE
:'CURRENT_DATABASE';

CURRENT_DATE
:'CURRENT_DATE';

CURRENT_SCHEMA
:'CURRENT_SCHEMA';

CURRENT_TIME
:'CURRENT_TIME';

CURRENT_TIMESTAMP
:'CURRENT_TIMESTAMP';

CURRENT_USER
:'CURRENT_USER';

DATEDIFF:'DATEDIFF';

DATETIME:'DATETIME';

DECIMAL:'DECIMAL';

DECODE:'DECODE';

DEFAULT:'DEFAULT';

DEFERRABLE:'DEFERRABLE';

DESC:'DESC';

DISTINCT:'DISTINCT';

ELSE:'ELSE';

ENCODED:'ENCODED';

END:'END';

EXCEPT:'EXCEPT';

EXISTS:'EXISTS';

EXTRACT:'EXTRACT';

FALSE:'FALSE';

FLOAT:'FLOAT';

FOR:'FOR';

FOREIGN:'FOREIGN';

FROM:'FROM';

FULL:'FULL';

GRANT:'GRANT';

GROUP:'GROUP';

HAVING:'HAVING';

ILIKE:'ILIKE';

ILIKEB:'ILIKEB';

IN:'IN';

INITIALLY:'INITIALLY';

INNER:'INNER';

INOUT:'INOUT';

INT:'INT';

INTEGER:'INTEGER';

INTERSECT:'INTERSECT';

INTERVAL:'INTERVAL';

INTERVALYM:'INTERVALYM';

INTO:'INTO';

IS:'IS';

ISNULL:'ISNULL';

JOIN:'JOIN';

KSAFE:'KSAFE';

LEADING:'LEADING';

LEFT:'LEFT';

LIKE:'LIKE';

LIKEB:'LIKEB';

LIMIT:'LIMIT';

LOCALTIME:'LOCALTIME';

LOCALTIMESTAMP:'LOCALTIMESTAMP';

MATCH:'MATCH';

MINUS:'MINUS';

MONEY:'MONEY';

NATURAL:'NATURAL';

NCHAR:'NCHAR';

NEW:'NEW';

NONE:'NONE';

NOT:'NOT';

NOTNULL:'NOTNULL';

NULL:'NULL';

NULLSEQUAL:'NULLSEQUAL';

NUMBER:'NUMBER';

NUMERIC:'NUMERIC';

OFFSET:'OFFSET';

OLD:'OLD';

ON:'ON';

ONLY:'ONLY';

OR:'OR';

ORDER:'ORDER';

OUT:'OUT';

OUTER:'OUTER';

OVER:'OVER';

OVERLAPS:'OVERLAPS';

OVERLAY:'OVERLAY';

PINNED:'PINNED';

POSITION:'POSITION';

PRECISION:'PRECISION';

PRIMARY:'PRIMARY';

REAL:'REAL';

REFERENCES:'REFERENCES';

RIGHT:'RIGHT';

ROW:'ROW';

SCHEMA:'SCHEMA';

SELECT:'SELECT';

SESSION_USER
:'SESSION_USER';

SIMILAR:'SIMILAR';

SMALLDATETIME:'SMALLDATETIME';

SMALLINT:'SMALLINT';

SOME:'SOME';

SUBSTRING:'SUBSTRING';

SYSDATE:'SYSDATE';

TABLE:'TABLE';

TEXT:'TEXT';

THEN:'THEN';

TIME:'TIME';

TIMESERIES:'TIMESERIES';

TIMESTAMP:'TIMESTAMP';

TIMESTAMPADD:'TIMESTAMPADD';

TIMESTAMPDIFF:'TIMESTAMPDIFF';

TIMESTAMPTZ:'TIMESTAMPTZ';

TIMETZ:'TIMETZ';

TIMEZONE:'TIMEZONE';

TINYINT:'TINYINT';

TO:'TO';

TRAILING:'TRAILING';

TREAT:'TREAT';

TRIM:'TRIM';

TRUE:'TRUE';

UNBOUNDED:'UNBOUNDED';

UNION:'UNION';

UNIQUE:'UNIQUE';

USER:'USER';

USING:'USING';

UUID:'UUID';

VARBINARY:'VARBINARY';

VARCHAR:'VARCHAR';

VARCHAR2
:
	'VARCHAR2'
;

WHEN:'WHEN';

WHERE:'WHERE';

WINDOW:'WINDOW';

WITH:'WITH';

WITHIN:'WITHIN';

//Nonreservedkeywords

ABORT:'ABORT';

ABSOLUTE:'ABSOLUTE';

ACCESS:'ACCESS';

ACCESSRANK:'ACCESSRANK';

ACCOUNT:'ACCOUNT';

ACTION:'ACTION';

ACTIVATE:'ACTIVATE';

ACTIVEPARTITIONCOUNT:'ACTIVEPARTITIONCOUNT';

ADD:'ADD';

ADMIN:'ADMIN';

AFTER:'AFTER';

AGGREGATE:'AGGREGATE';

ALSO:'ALSO';

ALTER:'ALTER';

ANALYSE:'ANALYSE';

ANALYTIC:'ANALYTIC';

ANALYZE:'ANALYZE';

ANNOTATED:'ANNOTATED';

ANTI:'ANTI';

ASSERTION:'ASSERTION';

ASSIGNMENT:'ASSIGNMENT';

AT:'AT';

AUTHENTICATION:'AUTHENTICATION';

AUTO:'AUTO';

AUTO_INCREMENT
:'AUTO_INCREMENT';

AVAILABLE:'AVAILABLE';

BACKWARD:'BACKWARD';

BASENAME:'BASENAME';

BATCH:'BATCH';

BEFORE:'BEFORE';

BEGIN:'BEGIN';

BEST:'BEST';

BLOCK:'BLOCK';

BLOCDICT
:'BLOCDICT';

BLOCKDICT_COMP
:'BLOCKDICT_COMP';

BROADCAST:'BROADCAST';

BY:'BY';

BYTEA:'BYTEA';

BYTES:'BYTES';

BZIP:'BZIP';

BZIP_COMP
:'BZIP_COMP';

CACHE:'CACHE';

CALLED:'CALLED';

CASCADE:'CASCADE';

CATALOGPATH:'CATALOGPATH';

CHAIN:'CHAIN';

CHARACTER:'CHARACTER';

CHARACTERISTICS:'CHARACTERISTICS';

CHARACTERS:'CHARACTERS';

CHECKPOINT:'CHECKPOINT';

CLASS:'CLASS';

CLEAR:'CLEAR';

CLOSE:'CLOSE';

CLUSTER:'CLUSTER';

COLSIZES:'COLSIZES';

COLUMNS_COUNT
:'COLUMNS_COUNT';

COMMENT:'COMMENT';

COMMIT:'COMMIT';

COMMITTED:'COMMITTED';

COMMONDELTA_COMP
:'COMMONDELTA_COMP';

COMMUNAL:'COMMUNAL';

COMPLEX:'COMPLEX';

CONNECT:'CONNECT';

CONSTRAINTS:'CONSTRAINTS';

CONTROL:'CONTROL';

COPY:'COPY';

CPUAFFINITYMODE:'CPUAFFINITYMODE';

CPUAFFINITYSET:'CPUAFFINITYSET';

CREATEDB:'CREATEDB';

CREATEUSER:'CREATEUSER';

CSV:'CSV';

CUBE:'CUBE';

CURRENT:'CURRENT';

CURSOR:'CURSOR';

CUSTOM:'CUSTOM';

CUSTOM_PARTITIONS
:'CUSTOM_PARTITIONS';

CYCLE:'CYCLE';

DATA:'DATA';

DATABASE:'DATABASE';

DATAPATH:'DATAPATH';

DAY:'DAY';

DEACTIVATE:'DEACTIVATE';

DEALLOCATE:'DEALLOCATE';

DEC:'DEC';

DECLARE:'DECLARE';

DEFAULTS:'DEFAULTS';

DEFERRED:'DEFERRED';

DEFINE:'DEFINE';

DEFINER:'DEFINER';

DELETE:'DELETE';

DELIMITER:'DELIMITER';

DELIMITERS:'DELIMITERS';

DELTARANGE_COMP
:'DELTARANGE_COMP';

DELTARANGE_COMP_SP
:'DELTARANGE_COMP_SP';

DELTAVAL:'DELTAVAL';

DEPENDS:'DEPENDS';

DETERMINES:'DETERMINES';

DIRECT:'DIRECT';

DIRECTCOLS:'DIRECTCOLS';

DIRECTED:'DIRECTED';

DIRECTGROUPED:'DIRECTGROUPED';

DIRECTPROJ:'DIRECTPROJ';

DISABLE:'DISABLE';

DISABLED:'DISABLED';

DISCONNECT:'DISCONNECT';

DISTVALINDEX:'DISTVALINDEX';

DO:'DO';

DOMAIN:'DOMAIN';

DOUBLE:'DOUBLE';

DROP:'DROP';

DURABLE:'DURABLE';

EACH:'EACH';

ENABLE:'ENABLE';

ENABLED:'ENABLED';

ENCLOSED:'ENCLOSED';

ENCODING:'ENCODING';

ENCRYPTED:'ENCRYPTED';

ENFORCELENGTH:'ENFORCELENGTH';

EPHEMERAL:'EPHEMERAL';

EPOCH:'EPOCH';

ERROR:'ERROR';

ESCAPE:'ESCAPE';

EVENT:'EVENT';

EVENTS:'EVENTS';

EXCEPTION:'EXCEPTION';

EXCEPTIONS:'EXCEPTIONS';

EXCLUDE:'EXCLUDE';

EXCLUDING:'EXCLUDING';

EXCLUSIVE:'EXCLUSIVE';

EXECUTE:'EXECUTE';

EXECUTIONPARALLELISM:'EXECUTIONPARALLELISM';

EXPIRE:'EXPIRE';

EXPLAIN:'EXPLAIN';

EXPORT:'EXPORT';

EXTERNAL:'EXTERNAL';

FAILED_LOGIN_ATTEMPTS
:'FAILED_LOGIN_ATTEMPTS';

FAULT:'FAULT';

FENCED:'FENCED';

FETCH:'FETCH';

FILESYSTEM:'FILESYSTEM';

FILLER:'FILLER';

FILTER:'FILTER';

FIRST:'FIRST';

FIXEDWIDTH:'FIXEDWIDTH';

FLEX:'FLEX';

FLEXIBLE:'FLEXIBLE';

FOLLOWING:'FOLLOWING';

FORCE:'FORCE';

FORMAT:'FORMAT';

FORWARD:'FORWARD';

FREEZE:'FREEZE';

FUNCTION:'FUNCTION';

FUNCTIONS:'FUNCTIONS';

GCDDELTA:'GCDDELTA';

GET:'GET';

GLOBAL:'GLOBAL';

GRACEPERIOD:'GRACEPERIOD';

GROUPED:'GROUPED';

GROUPING:'GROUPING';

GZIP:'GZIP';

GZIP_COMP
:'GZIP_COMP';

HANDLER:'HANDLER';

HCATALOG:'HCATALOG';

HCATALOG_CONNECTION_TIMEOUT
:'HCATALOG_CONNECTION_TIMEOUT';

HCATALOG_DB
:'HCATALOG_DB';

HCATALOG_SCHEMA
:'HCATALOG_SCHEMA';

HCATALOG_SLOW_TRANSFER_LIMIT
:'HCATALOG_SLOW_TRANSFER_LIMIT';

HCATALOG_SLOW_TRANSFER_TIME
:'HCATALOG_SLOW_TRANSFER_TIME';

HCATALOG_USER
:'HCATALOG_USER';

HIGH:'HIGH';

HIVE_PARTITION_COLS
:'HIVE_PARTITION_COLS';

HIVESERVER2_HOSTNAME
:'HIVESERVER2_HOSTNAME';

HOLD:'HOLD';

HOST:'HOST';

HOSTNAME:'HOSTNAME';

HOUR:'HOUR';

HOURS:'HOURS';

IDENTIFIED:'IDENTIFIED';

IDENTITY:'IDENTITY';

IDLESESSIONTIMEOUT:'IDLESESSIONTIMEOUT';

IF:'IF';

IGNORE:'IGNORE';

IMMEDIATE:'IMMEDIATE';

IMMUTABLE:'IMMUTABLE';

IMPLICIT:'IMPLICIT';

INCLUDE:'INCLUDE';

INCLUDING:'INCLUDING';

INCREMENT:'INCREMENT';

INDEX:'INDEX';

INHERITS:'INHERITS';

INPUT:'INPUT';

INSENSITIVE:'INSENSITIVE';

INSERT:'INSERT';

INSTEAD:'INSTEAD';

INTERFACE:'INTERFACE';

INTERPOLATE:'INTERPOLATE';

INVOKER:'INVOKER';

ISOLATION:'ISOLATION';

JSON:'JSON';

KEY:'KEY';

LABEL:'LABEL';

LANCOMPILER:'LANCOMPILER';

LANGUAGE:'LANGUAGE';

LARGE:'LARGE';

LAST:'LAST';

LATEST:'LATEST';

LESS:'LESS';

LEVEL:'LEVEL';

LIBRARY:'LIBRARY';

LISTEN:'LISTEN';

LOAD:'LOAD';

LOCAL:'LOCAL';

LOCATION:'LOCATION';

LOCK:'LOCK';

LONG:'LONG';

LOW:'LOW';

LZO:'LZO';

MANAGED:'MANAGED';

MASK:'MASK';

MATCHED:'MATCHED';

MATERIALIZE:'MATERIALIZE';

MAXCONCURRENCY:'MAXCONCURRENCY';

MAXCONCURRENCYGRACE:'MAXCONCURRENCYGRACE';

MAXCONNECTIONS:'MAXCONNECTIONS';

MAXMEMORYSIZE:'MAXMEMORYSIZE';

MAXPAYLOAD:'MAXPAYLOAD';

MAXQUERYMEMORYSIZE:'MAXQUERYMEMORYSIZE';

MAXVALUE:'MAXVALUE';

MEDIUM:'MEDIUM';

MEMORYCAP:'MEMORYCAP';

MEMORYSIZE:'MEMORYSIZE';

MERGE:'MERGE';

MERGEOUT:'MERGEOUT';

METHOD:'METHOD';

MICROSECONDS:'MICROSECONDS';

MILLISECONDS:'MILLISECONDS';

MINUTE:'MINUTE';

MINUTES:'MINUTES';

MINVALUE:'MINVALUE';

MODE:'MODE';

MODEL:'MODEL';

MONTH:'MONTH';

MOVE:'MOVE';

MOVEOUT:'MOVEOUT';

NAME:'NAME';

NATIONAL:'NATIONAL';

NATIVE:'NATIVE';

NETWORK:'NETWORK';

NEXT:'NEXT';

NO:'NO';

NOCREATEDB:'NOCREATEDB';

NOCREATEUSER:'NOCREATEUSER';

NODE:'NODE';

NODES:'NODES';

NOTHING:'NOTHING';

NOTIFIER:'NOTIFIER';

NOTIFY:'NOTIFY';

NOWAIT:'NOWAIT';

NULLAWARE:'NULLAWARE';

NULLCOLS:'NULLCOLS';

NULLS:'NULLS';

OBJECT:'OBJECT';

OCTETS:'OCTETS';

OF:'OF';

OFF:'OFF';

OIDS:'OIDS';

OPERATOR:'OPERATOR';

OPT:'OPT';

OPTIMIZER:'OPTIMIZER';

OPTION:'OPTION';

OPTVER:'OPTVER';

ORC:'ORC';

OTHERS:'OTHERS';

OWNER:'OWNER';

PARAMETER:'PARAMETER';

PARAMETERS:'PARAMETERS';

PARQUET:'PARQUET';

PARSER:'PARSER';

PARTIAL:'PARTIAL';

PARTITION:'PARTITION';

PARTITIONING:'PARTITIONING';

PASSWORD:'PASSWORD';

PASSWORD_GRACE_TIME
:'PASSWORD_GRACE_TIME';

PASSWORD_LIFE_TIME
:'PASSWORD_LIFE_TIME';

PASSWORD_LOCTIME
:'PASSWORD_LOCTIME';

PASSWORD_MAX_LENGTH
:'PASSWORD_MAX_LENGTH';

PASSWORD_MIN_DIGITS
:'PASSWORD_MIN_DIGITS';

PASSWORD_MIN_LENGTH
:'PASSWORD_MIN_LENGTH';

PASSWORD_MIN_LETTERS
:'PASSWORD_MIN_LETTERS';

PASSWORD_MIN_LOWERCASE_LETTERS
:
	'PASSWORD_MIN_LOWERCASE_LETTERS';

PASSWORD_MIN_SYMBOLS
:'PASSWORD_MIN_SYMBOLS';

PASSWORD_MIN_UPPERCASE_LETTERS
:
	'PASSWORD_MIN_UPPERCASE_LEFTERS';

PASSWORD_REUSE_MAX
:'PASSWORD_REUSE_MAX';

PASSWORD_REUSE_TIME
:'PASSWORD_REUSE_TIME';

PATTERN:'PATTERN';

PERCENT:'PERCENT';

PERMANENT:'PERMANENT';

PLACING:'PLACING';

PLANNEDCONCURRENCY:'PLANNEDCONCURRENCY';

POLICY:'POLICY';

POOL:'POOL';

PORT:'PORT';

PRECEDING:'PRECEDING';

PREPARE:'PREPARE';

PREPASS:'PREPASS';

PRESERVE:'PRESERVE';

PREVIOUS:'PREVIOUS';

PRIOR:'PRIOR';

PRIORITY:'PRIORITY';

PRIVILEGES:'PRIVILEGES';

PROCEDURAL:'PROCEDURAL';

PROCEDURE:'PROCEDURE';

PROFILE:'PROFILE';

PROJECTION:'PROJECTION';

PROJECTIONS:'PROJECTIONS';

PSDATE:'PSDATE';

QUERY:'QUERY';

QUEUETIMEOUT:'QUEUETIMEOUT';

QUOTE:'QUOTE';

RANGE:'RANGE';

RAW:'RAW';

READ:'READ';

RECHECK:'RECHECK';

RECORD:'RECORD';

RECOVER:'RECOVER';

RECURSIVE:'RECURSIVE';

REFRESH:'REFRESH';

REINDEX:'REINDEX';

REJECTED:'REJECTED';

REJECTMAX:'REJECTMAX';

RELATIVE:'RELATIVE';

RELEASE:'RELEASE';

REMOVE:'REMOVE';

RENAME:'RENAME';

REORGANIZE:'REORGANIZE';

REPEATABLE:'REPEATABLE';

REPLACE:'REPLACE';

RESET:'RESET';

RESOURCE:'RESOURCE';

RESTART:'RESTART';

RESTRICT:'RESTRICT';

RESULTS:'RESULTS';

RETURN:'RETURN';

RETURNREJECTED:'RETURNREJECTED';

REVOKE:'REVOKE';

RLE:'RLE';

ROLE:'ROLE';

ROLES:'ROLES';

ROLLBACK:'ROLLBACK';

ROLLUP:'ROLLUP';

ROWS:'ROWS';

RULE:'RULE';

RUNTIMECAP:'RUNTIMECAP';

RUNTIMEPRIORITY:'RUNTIMEPRIORITY';

RUNTIMEPRIORITYTHRESHOLD:'RUNTIMEPRIORITYTHRESHOLD';

SAVE:'SAVE';

SAVEPOINT:'SAVEPOINT';

SCROLL:'SCROLL';

SEARCH_PATH
:'SEARCH_PATH';

SECOND:'SECOND';

SECONDS:'SECONDS';

SECURITY:'SECURITY';

SECURITY_ALGORITHM
:'SECURITY_ALGORITHM';

SEGMENTED:'SEGMENTED';

SEMI:'SEMI';

SEMIALL:'SEMIALL';

SEQUENCE:'SEQUENCE';

SEQUENCES:'SEQUENCES';

SERIALIZABLE:'SERIALIZABLE';

SESSION:'SESSION';

SET:'SET';

SETOF:'SETOF';

SETS:'SETS';

SHARE:'SHARE';

SHARED:'SHARED';

SHOW:'SHOW';

SIMPLE:'SIMPLE';

SINGLEINITIATOR:'SINGLEINITIATOR';

SITE:'SITE';

SITES:'SITES';

K_SKIP:'SKIP';

SOURCE:'SOURCE';

SPLIT:'SPLIT';

SSL_CONFIG
:'SSL_CONFIG';

STABLE:'STABLE';

STANDBY:'STANDBY';

START:'START';

STATEMENT:'STATEMENT';

STATISTICS:'STATISTICS';

STDIN:'STDIN';

STDOUT:'STDOUT';

STEMMER:'STEMMER';

STORAGE:'STORAGE';

STREAM:'STREAM';

STRENGTH:'STRENGTH';

STRICT:'STRICT';

SUBNET:'SUBNET';

SYSID:'SYSID';

SYSTEM:'SYSTEM';

TABLES:'TABLES';

TABLESAMPLE:'TABLESAMPLE';

TABLESPACE:'TABLESPACE';

TEMP:'TEMP';

TEMPLATE:'TEMPLATE';

TEMPORARY:'TEMPORARY';

TEMPSPACECAP:'TEMPSPACECAP';

TERMINATOR:'TERMINATOR';

THAN:'THAN';

TIES:'TIES';

TLS:'TLS';

TOAST:'TOAST';

TOKENIZER:'TOKENIZER';

TOLERANCE:'TOLERANCE';

TRANSACTION:'TRANSACTION';

TRANSFORM:'TRANSFORM';

TRICKLE:'TRICKLE';

TRIGGER:'TRIGGER';

TRUNCATE:'TRUNCATE';

TRUSTED:'TRUSTED';

TUNING:'TUNING';

TYPE:'TYPE';

UDPARAMETER:'UDPARAMETER';

UNCOMMITTED:'UNCOMMITTED';

UNCOMPRESSED:'UNCOMPRESSED';

UNI:'UNI';

UNINDEXED:'UNINDEXED';

UNKNOWN:'UNKNOWN';

UNLIMITED:'UNLIMITED';

UNLISTEN:'UNLISTEN';

UNLOCK:'UNLOCK';

UNPACKER:'UNPACKER';

UNSEGMENTED:'UNSEGMENTED';

UPDATE:'UPDATE';

USAGE:'USAGE';

VACUUM:'VACUUM';

VALIDATE:'VALIDATE';

VALIDATOR:'VALIDATOR';

VALINDEX:'VALINDEX';

VALUE:'VALUE';

VALUES:'VALUES';

VARYING:'VARYING';

VERBOSE:'VERBOSE';

VERTICA:'VERTICA';

VIEW:'VIEW';

VOLATILE:'VOLATILE';

WAIT:'WAIT';

WEBHDFS_ADDRESS
:'WEBHDFS_ADDRESS';

WEBSERVICE_HOSTNAME
:'WEBSERVICE_HOSTNAME';

WEBSERVICE_PORT
:'WEBSERVICE_PORT';

WITHOUT:'WITHOUT';

WORK:'WORK';

WRITE:'WRITE';

YEAR:'YEAR';

ZONE:'ZONE';

//morekeywords

UDPARAMETERS:'UDPARAMETERS';

ALLNODES:'ALLNODES';
//additional

PUBLIC:'PUBLIC';

DIRECTORY:'DIRECTORY';

ROWGROUPSIZEMB:'ROWGROUPSIZEMB';

COMPRESSION:'COMPRESSION';

FILESIZEMB:'FILESIZEMB';

FILEMODE:'FILEMODE';

DIRMODE:'DIRMODE';

ZSTD:'ZSTD';

ROUTE:'ROUTE';

ROUTING:'ROUTING';

ESCAPE_STRING_WARNING
:'ESCAPE_STRING_WARNING';

STANDARD_CONFORMING_STRINGS
:'STANDARD_CONFORMING_STRINGS';

AUTOCOMMIT:'AUTOCOMMIT';

LOCALE:'LOCALE';

MULTIPLEACTIVERESULTSETS:'MULTIPLEACTIVERESULTSETS';

RESOURCE_POOL
:'RESOURCE_POOL';

INTERVALSTYLE:'INTERVALSTYLE';

PLAIN:'PLAIN';

UNITS:'UNITS';

DATESTYLE:'DATESTYLE';

ISO:'ISO';

MDY:'MDY';

POSTGRES:'POSTGRES';

SQL:'SQL';

GERMAN:'GERMAN';

DMY:'DMY';

TLSMODE:'TLSMODE';

PREFER:'PREFER';

INT8
:
	'INT8'
;

FLOAT8
:
	'FLOAT8'
;

GEOMETRY:'GEOMETRY';

GEOGRAPHY:'GEOGRAPHY';

DATE:'DATE';

ADDRESS:'ADDRESS';

BALANCE:'BALANCE';

GBYTYPE:'GBYTYPE';

HASH:'HASH';

PIPE:'PIPE';

SYN_JOIN
:'SYN_JOIN';

ENABLE_WITH_CLAUSE_MATERIALIZATION
:
	'ENABLE_WITH_CLAUSE_MATERIALIZATION';

EARLY_MATERIALIZATION
:'EARLY_MATERIALIZATION';

CREATETYPE:'CREATETYPE';

SYNTACTIC_JOIN
:'SYNTACTIC_JOIN';

BRANCH:'BRANCH';

DATATYPE:'DATATYPE';

VERBATIM:'VERBATIM';

IGNORECONST:'IGNORECONST';

UTYPE:'UTYPE';

JTYPE:'JTYPE';

DISTRIB:'DISTRIB';

PROJS:'PROJS';

SKIP_PROJS
:'SKIP_PROJS';

RAISE   : 'RAISE';
LOG     : 'LOG';
INFO    : 'INFO';
WARNING : 'WARNING';
NOTICE   : 'NOTICE';
CONSTANT : 'CONSTANT';
ALIAS    : 'ALIAS';
PERFORM : 'PERFORM';
PASSWORD_MIN_CHAR_CHANGE : 'PASSWORD_MIN_CHAR_CHANGE';
PASSWORD_LOCK_TIME : 'PASSWORD_LOCK_TIME';
PASSWORD_MIN_LIFE_TIME : 'PASSWORD_MIN_LIFE_TIME';
TLSCONFIG : 'TLSCONFIG';
TO_CHAR : 'TO_CHAR';
COLLECTIONOPEN : 'COLLECTIONOPEN';
COLLECTIONCLOSE : 'COLLECTIONCLOSE';
COLLECTIONDELIMITER : 'COLLECTIONDELIMITER';
COLLECTIONNULLELEMENT : 'COLLECTIONNULLELEMENT';
COLLECTIONENCLOSE : 'COLLECTIONENCLOSE';
FALLTHROUGH : 'FALLTHROUGH';
SUBJECT : 'SUBJECT';
SIGNED : 'SIGNED';
VALID : 'VALID';
EXTENSIONS : 'EXTENSIONS';
MAP : 'MAP';
LENGTH : 'LENGTH';
BLOCK_DICT : 'BLOCK_DICT';
ZSTD_COMP : 'ZSTD_COMP';
ZSTD_FAST_COMP : 'ZSTD_FAST_COMP';
ZSTD_HIGH_COMP : 'ZSTD_HIGH_COMP';
NAMESPACE : 'NAMESPACE';
FILES : 'FILES';
EXACT_PATH : 'EXACT_PATH';
EXPAND_GLOB : 'EXPAND_GLOB';
BATCH_SIZE : 'BATCH_SIZE';
DELIMITED : 'DELIMITED';
IFDIREXISTS : 'IFDIREXISTS';
INT96ASTIMESTAMP : 'INT96ASTIMESTAMP';
EXTEND : 'EXTEND';
REPLICATE : 'REPLICATE';
TARGET_NAMESPACE : 'TARGET_NAMESPACE';
WORKLOADS : 'WORKLOADS';
TRANSACTION_ISOLATION : 'TRANSACTION_ISOLATION';
TRANSACTION_READ_ONLY : 'TRANSACTION_READ_ONLY';
CONNECT_BY_ROOT : 'CONNECT_BY_ROOT';
APPROXIMATE_COUNT_DISTINCT : 'APPROXIMATE_COUNT_DISTINCT';
APPROXIMATE_COUNT_DISTINCT_OF_SYNOPSIS : 'APPROXIMATE_COUNT_DISTINCT_OF_SYNOPSIS';
APPROXIMATE_COUNT_DISTINCT_SYNOPSIS : 'APPROXIMATE_COUNT_DISTINCT_SYNOPSIS';
APPROXIMATE_COUNT_DISTINCT_SYNOPSIS_MERGE : 'APPROXIMATE_COUNT_DISTINCT_SYNOPSIS_MERGE';
APPROXIMATE_MEDIAN : 'APPROXIMATE_MEDIAN';
APPROXIMATE_PERCENTILE : 'APPROXIMATE_PERCENTILE';
APPROXIMATE_QUANTILES : 'APPROXIMATE_QUANTILES';
ARGMAX_AGG : 'ARGMAX_AGG';
ARGMIN_AGG : 'ARGMIN_AGG';
AVG : 'AVG';
BIT_AND : 'BIT_AND';
BIT_OR : 'BIT_OR';
BIT_XOR : 'BIT_XOR';
BOOL_AND : 'BOOL_AND';
BOOL_OR : 'BOOL_OR';
BOOL_XOR : 'BOOL_XOR';
CORR : 'CORR';
COUNT : 'COUNT';
COVAR_POP : 'COVAR_POP';
COVAR_SAMP : 'COVAR_SAMP';
GROUP_ID : 'GROUP_ID';
GROUPING_ID : 'GROUPING_ID';
LISTAGG : 'LISTAGG';
MIN : 'MIN';
MAX : 'MAX';
REGR_AVGX : 'REGR_AVGX';
REGR_AVGY : 'REGR_AVGY';
REGR_COUNT : 'REGR_COUNT';
REGR_INTERCEPT : 'REGR_INTERCEPT';
REGR_R2 : 'REGR_R2';
REGR_SLOPE : 'REGR_SLOPE';
REGR_SXX : 'REGR_SXX';
REGR_SXY : 'REGR_SXY';
REGR_SYY : 'REGR_SYY';
STDDEV : 'STDDEV';
STDDEV_POP : 'STDDEV_POP';
STDDEV_SAMP : 'STDDEV_SAMP';
SUM : 'SUM';
SUM_FLOAT : 'SUM_FLOAT';
TS_FIRST_VALUE : 'TS_FIRST_VALUE';
CONST : 'CONST';
LINEAR : 'LINEAR';
TS_LAST_VALUE : 'TS_LAST_VALUE';
VAR_POP : 'VAR_POP';
VAR_SAMP : 'VAR_SAMP';
VARIANCE : 'VARIANCE';
ARGMAX : 'ARGMAX';
ARGMIN : 'ARGMIN';
CONDITIONAL_CHANGE_EVENT : 'CONDITIONAL_CHANGE_EVENT';
CONDITIONAL_TRUE_EVENT : 'CONDITIONAL_TRUE_EVENT';
CUME_DIST : 'CUME_DIST';
DENSE_RANK : 'DENSE_RANK';
EXPONENTIAL_MOVING_AVERAGE : 'EXPONENTIAL_MOVING_AVERAGE';
FIRST_VALUE : 'FIRST_VALUE';
LAG : 'LAG';
LAST_VALUE : 'LAST_VALUE';
LEAD : 'LEAD';
MEDIAN : 'MEDIAN';
NTH_VALUE : 'NTH_VALUE';
NTILE : 'NTILE';
PERCENT_RANK : 'PERCENT_RANK';
PERCENTILE_CONT : 'PERCENTILE_CONT';
PERCENTILE_DISC : 'PERCENTILE_DISC';
RANK : 'RANK';
ROW_NUMBER : 'ROW_NUMBER';
ADD_MONTHS : 'ADD_MONTHS';
AGE_IN_MONTHS : 'AGE_IN_MONTHS';
AGE_IN_YEARS : 'AGE_IN_YEARS';
CLOCK_TIMESTAMP : 'CLOCK_TIMESTAMP';
DATE_PART : 'DATE_PART';
DATE_TRUNC : 'DATE_TRUNC';
DAYOFMONTH : 'DAYOFMONTH';
DAYOFWEEK : 'DAYOFWEEK';
DAYOFWEEK_ISO :  'DAYOFWEEK_ISO';
DAYOFYEAR : 'DAYOFYEAR';
DAYS : 'DAYS';
GETDATE : 'GETDATE';
GETUTCDATE : 'GETUTCDATE';
ISFINITE : 'ISFINITE';
JULIAN_DAY : 'JULIAN_DAY';
LAST_DAY : 'LAST_DAY';
MICROSECOND : 'MICROSECOND';
MIDNIGHT_SECONDS :  'MIDNIGHT_SECONDS';
MONTHS_BETWEEN : 'MONTHS_BETWEEN';
NEW_TIME : 'NEW_TIME';
NEXT_DAY : 'NEXT_DAY';
NOW : 'NOW';
QUARTER : 'QUARTER';
ROUND : 'ROUND';
STATEMENT_TIMESTAMP : 'STATEMENT_TIMESTAMP';
TIME_SLICE : 'TIME_SLICE';
TIMEOFDAY : 'TIMEOFDAY';
TIMESTAMP_ROUND : 'TIMESTAMP_ROUND';
TIMESTAMP_TRUNC : 'TIMESTAMP_TRUNC';
TRANSACTION_TIMESTAMP : 'TRANSACTION_TIMESTAMP';
TRUNC : 'TRUNC';
WEEK : 'WEEK';
WEEK_ISO : 'WEEK_ISO';
YEAR_ISO : 'YEAR_ISO';
CURRVAL : 'CURRVAL';
NEXTVAL : 'NEXTVAL';
COALESCE : 'COALESCE';
// symbols

AMPERSAND_           : '&';
AND_                 : '&&';
ARROW_               : '=>';
ASSIGNMENT_OPERATOR_ : ':=';
ASTERISK_            : '*';
AT_                  : '@';
BACKSLASH_           : '\\';
BQ_                  : '`';
CARET_               : '^';
COLON_               : ':';
COMMA_               : ',';
DEQ_                 : '==';
DOLLAR_              : '$';
DOT_                 : '.';
DOT_ASTERISK_        : '.*';
DQ_                  : '"';
EQ_                  : '=';
EXPONENT_            : '**';
GT_                  : '>';
GTE_                 : '>=';
LBE_                 : '{';
LBT_                 : '[';
LP_                  : '(';
LT_                  : '<';
LTE_                 : '<=';
MINUS_               : '-';
MOD_                 : '%';
NEQ_                 : '<>' | '!=' | '^=';
NOT_                 : '!';
OR_                  : '||';
PLUS_                : '+';
POUND_               : '#';
QUESTION_            : '?';
RANGE_OPERATOR_      : '..';
RBE_                 : '}';
RBT_                 : ']';
RP_                  : ')';
SAFE_EQ_             : '<=>';
SEMI_                : ';';
SIGNED_LEFT_SHIFT_   : '<<';
SIGNED_RIGHT_SHIFT_  : '>>';
SLASH_               : '/';
SQ_                  : '\'';
TILDE_               : '~';
VERTICAL_BAR_        : '|';
UL_                  : '_';
JSON_EXTRACT_        : '->';




// oracle不存在 -------------------
DCOLON_ : 	'::';
AMP_LT : '&<';
BANG_BANG_ : 	'!!';
SQROOT_ : 	'|/';
CUBEROOT_ : '||/';
DIV2_ :	'//';
//------------------

//other
WS : [ \t\r\n\u3000] + ->skip;
//BLOCK_COMMENT:  '/*' .*? '*/'                           -> channel(HIDDEN);
BLOCK_COMMENT:  '/*' ~'+'
                	(
                		BLOCK_COMMENT
                		| .
                	)*? '*/' -> channel ( HIDDEN )
                ;
INLINE_COMMENT: '--' ~[\r\n]* ('\r'? '\n' | EOF)        -> channel(HIDDEN);



BLOCK_HINT : '/*+' .*? '*/';
INLINE_HINT: '--+' ~[\r\n]* ('\r'? '\n' | EOF);


ERROR_END_BLOCK  : '$error' .*? '$end'                  -> channel(HIDDEN);
IF_END_BLOCK  : '$if' (ERROR_END_BLOCK | .)*? '$end'    -> channel(HIDDEN);

STRING_: SINGLE_QUOTED_TEXT;
//SINGLE_QUOTED_TEXT: (SQ_ ('\\'. | '\'\'' | ~('\'' | '\\'))* SQ_);
//SINGLE_QUOTED_TEXT: SQ_ (~('\'' | '\r' | '\n') | '\'' '\'' | '\r'? '\n')* SQ_;
//DOUBLE_QUOTED_TEXT: (DQ_ ( '\\'. | '""' | ~('"'| '\\') )* DQ_);
NCHAR_TEXT: 'N' STRING_;
UCHAR_TEXT: 'U' STRING_;

INTEGER_: INT_;
NUMBER_: INTEGER_? DOT_? INTEGER_ ('E' (PLUS_ | MINUS_)? INTEGER_)?;
HEX_DIGIT_: '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_;
BIT_NUM_: '0b' ('0' | '1')+ | 'B' SQ_ ('0' | '1')+ SQ_;

A: 'A';
K: 'K';
M: 'M';
G: 'G';
T: 'T';
P: 'P';
E: 'E';
H: 'H';

I_CURSOR : '[CURSOR]' ;
//IDENTIFIER_: [A-Z\u0080-\u2FFF\u3001-\uFF0B\uFF0D-\uFFFF]+[A-Z_$#0-9\u0080-\u2FFF\u3001-\uFF0B\uFF0D-\uFFFF]*;

fragment INT_: [0-9]+;
fragment HEX_: [0-9A-F];


IPV4_ADDR
:
	[']? IPV4_OCTECT DOT_ IPV4_OCTECT DOT_ IPV4_OCTECT DOT_ IPV4_OCTECT [']?
;

IPV6_ADDR
:
	[']? IPV6_OCTECT [:] IPV6_OCTECT [:] IPV6_OCTECT [:] IPV6_OCTECT [:]
	IPV6_OCTECT [:] IPV6_OCTECT [:] IPV6_OCTECT [:] IPV6_OCTECT [']?
;

// types
// comments

OPEN_HINT_ :	'/*+';

CLOSE_HINT_ :  	'*/';

// Identifiers

DOUBLE_QUOTED_TEXT
:
	'E'? '"'
	(
		'\\' .
		| '""'
		| ~( '"' | '\\' )
	)* '"'
;
BQUOTA_STRING: BQ_ ( '\\'. | '``' | ~('`'|'\\'))* BQ_;
PARAM :	':' [\p{Alpha}\p{General_Category=Other_Letter}_]
	[\p{Alnum}\p{General_Category=Other_Letter}_:]*
;

IDENTIFIER_ :	[\p{Alpha}\p{General_Category=Other_Letter}_]
	[\p{Alnum}\p{General_Category=Other_Letter}_]*
;

SINGLE_QUOTED_TEXT :'E'? '\'' ('\\' .| '\'\''| ~( '\'' | '\\' )	)* '\'';



REAL_
: [+-]?
	( DECIMAL_ | NUMBER_ )
	( 'E' [+-]? INT_ )
;

DECIMAL_
 : INTEGER_+ ;

ANY_
:
	.
;

fragment IPV6_OCTECT
:
	HEX_ HEX_ HEX_ HEX_
;

fragment IPV4_OCTECT
:
	[0-9]? [0-9]? [0-9]
;





