
parser grammar ElasticsearchParser;

options
{
    tokenVocab = ElasticsearchLexer;
    superClass = ElasticsearchParserBase;
}
// https://www.elastic.co/guide/en/elasticsearch/reference/current/rest-apis.html
// https://www.elastic.co/docs/api/doc/elasticsearch/
root
    : methodStatement {this.IsNewlineAtPos(-1)}? jsonObject* EOF
    ;
methodStatement
    : (pageStatement
    | getStatement
    | deleteStatement
    | postStatement
    | putStatement
    | headStatement) '?'?
    ;
apiPath
    : API_PATH
    ;
pageStatement
    : PAGE index_name
    ;
getStatement
    : GET '/'? (apiPath
    | desired_balance
    | ingest_database
    | query_rule
    | get_desired_nodes
    | async_search_get
    | async_search_status
    | get_autoscaling_capacity
    | autoscaling_policy
    | cat_aliases
    | cat_allocation
    | cat_component_templates
    | cat_count
    | cat_fielddata
    | cat_health
    | cat_help
    | cat_indices
    | cat_master
    | cat_ml_data_frame_analytics
    | cat_ml_datafeeds
    | cat_ml_jobs
    | cat_ml_trained_models
    | cat_nodeattrs
    | cat_nodes
    | cat_pending_tasks
    | cat_plugins
    | cat_recovery
    | cat_repositories
    | cat_segments
    | cat_shards
    | cat_snapshots
    | cat_tasks
    | cat_templates
    | cat_thread_pool
    | cat_transforms
    | ccr_follow_info
    | ccr_follow_stats
    | ccr_get_auto_follow_pattern
    | ccr_stats
    | cluster_allocation_explain
    | cluster_get_component_template
    | cluster_get_settings
    | cluster_health
    | cluster_info
    | cluster_pending_tasks
    | cluster_remote_info
    | cluster_state
    | cluster_stats
    | count
    | nodes_info
    | dangling_indices_list_dangling_indices
    | enrich_get_policy
    | enrich_stats
    | eql_get
    | eql_get_status
    | eql_search
    | esql_query_async
    | explain
    | features_get_features
    | field_caps
    | fleet_secret
    | fleet_global_checkpoints
    | fleet_msearch
    | fleet_search
    | get
    | get_script
    | get_script_context
    | get_script_languages
    | graph_explore
    | health_report
    | ilm_explain_lifecycle
    | ilm_get_lifecycle
    | ilm_get_status
    | indices_analyze
    | indices_data_streams_stats
    | indices_explain_data_lifecycle
    | indices_field_usage_stats
    | indices_flush
    | indices_get
    | indices_get_alias
    | indices_get_data_lifecycle
    | indices_get_data_stream
    | indices_get_field_mapping
    | indices_get_index_template
    | indices_get_mapping
    | indices_get_settings
    | indices_get_template
    | indices_recovery
    | indices_refresh
    | indices_reload_search_analyzers
    | indices_resolve_index
    | indices_segments
    | indices_shard_stores
    | indices_stats
    | indices_validate_query
    | inference_get_model
    | ingest_geo_ip_stats
    | ingest_get_pipeline
    | ingest_processor_grok
    | ingest_pipeline_simulate
    | ingest_simulate
    | knn_search
    | lifecycle_stats
    | license_get
    | license_get_basic_status
    | license_get_trial_status
    | logstash_get_pipeline
    | mget
    | migration_deprecations
    | migration_get_feature_upgrade_status
    | ml_explain_data_frame_analytics
    | ml_get_buckets
    | ml_get_calendar_events
    | ml_get_calendars
    | ml_get_categories
    | ml_get_data_frame_analytics
    | ml_get_data_frame_analytics_stats
    | ml_get_datafeed_stats
    | ml_get_datafeeds
    | ml_get_filters
    | ml_get_influencers
    | ml_get_job_stats
    | ml_get_jobs
    | ml_get_memory_stats
    | ml_get_model_snapshot_upgrade_stats
    | ml_get_model_snapshots
    | ml_get_overall_buckets
    | ml_get_records
    | ml_get_trained_models
    | ml_get_trained_models_stats
    | ml_info
    | ml_preview_data_frame_analytics
    | ml_preview_datafeed
    | msearch
    | msearch_template
    | mtermvectors
    | nodes_get_repositories_metering_info
    | nodes_hot_threads
    | nodes_stats
    | nodes_usage
    | query_ruleset
    | query_ruleset_list
    | rank_eval
    | render_search_template
    | rollup_get_jobs
    | rollup_get_rollup_caps
    | rollup_get_rollup_index_caps
    | rollup_rollup_search
    | scripts_painless_execute
    | scroll
    | search
    | search_application_get
    | search_application_get_behavioral_analytics
    | search_application_list
    | search_application_search
    | search_mvt
    | search_shards
    | search_template
    | searchable_snapshots_cache_stats
    | searchable_snapshots_stats
    | security_authenticate
    | security_enroll_kibana
    | security_enroll_node
    | security_get_api_key
    | security_get_builtin_privileges
    | security_get_privileges
    | security_get_role
    | security_get_role_mapping
    | security_get_service_accounts
    | security_get_service_credentials
    | security_get_settings
    | security_get_user
    | security_get_user_privileges
    | security_get_user_profile
    | security_grant_api_key
    | security_has_privileges
    | security_has_privileges_user_profile
    | security_query_api_keys
    | security_saml_service_provider_metadata
    | security_suggest_user_profiles
    | shutdown_get_node
    | slm_get_lifecycle
    | slm_get_stats
    | slm_get_status
    | snapshot_get
    | snapshot_get_repository
    | snapshot_status
    | sql_get_async
    | sql_get_async_status
    | sql_query
    | sql_translate
    | ssl_certificates
    | synonyms_get_synonym
    | synonyms_get_synonym_rule
    | synonyms_get_synonyms_sets
    | tasks_get
    | tasks_list
    | terms_enum
    | termvectors
    | text_structure_test_grok_pattern
    | transform_get_transform
    | transform_get_transform_stats
    | transform_preview_transform
    | watcher_get_settings
    | watcher_get_watch
    | watcher_query_watches
    | watcher_stats
    | xpack_info
    | xpack_usage)
    ;
deleteStatement
    : DELETE '/'? (apiPath
    | desired_balance
    | query_rule
    | security_role
    | ingest_database
    | desired_nodes
    | async_search_delete
    | autoscaling_policy
    | ccr_delete_auto_follow_pattern
    | clear_scroll
    | close_point_in_time
    | cluster_delete_component_template
    | cluster_delete_voting_config_exclusions
    | dangling_indices_dangling_index
    | delete
    | delete_script
    | enrich_delete_policy
    | eql_delete
    | esql_query_async_delete
    | fleet_secret
    | ilm_delete_put_lifecycle
    | indices_delete
    | indices_delete_alias
    | indices_delete_data_lifecycle
    | indices_delete_data_stream
    | indices_delete_index_template
    | indices_delete_template
    | inference_delete_model
    | ingest_delete_pipeline
    | license_delete
    | logstash_delete_pipeline
    | ml_delete_calendar
    | ml_delete_calendar_event
    | ml_delete_calendar_job
    | ml_delete_data_frame_analytics
    | ml_delete_datafeed
    | ml_delete_expired_data
    | ml_delete_filter
    | ml_delete_forecast
    | ml_delete_job
    | ml_delete_model_snapshot
    | ml_delete_trained_model
    | ml_delete_trained_model_alias
    | nodes_clear_repositories_metering_archive
    | query_ruleset
    | rollup_delete_job
    | search_application_delete
    | search_application_delete_behavioral_analytics
    | security_delete_privileges
    | security_delete_role
    | security_delete_role_mapping
    | security_delete_service_token
    | security_delete_user
    | security_invalidate_api_key
    | security_invalidate_token
    | shutdown_delete_node
    | slm_delete_lifecycle
    | snapshot_delete
    | snapshot_delete_repositor
    | sql_delete_async
    | synonyms_delete_synonym
    | synonyms_delete_synonym_rule
    | transform_delete_transform
    | watcher_delete_watch)
    ;
postStatement
    : POST '/'? (apiPath
    | prevalidate_node_removal
    | query_ruleset_test
    | security_role
    | async_search_submit
    | bulk
    | ccr_forget_follower
    | ccr_pause_auto_follow_pattern
    | ccr_pause_follow
    | ccr_resume_auto_follow_pattern
    | ccr_resume_follow
    | ccr_unfollow
    | cluster_allocation_explain
    | cluster_post_voting_config_exclusions
    | cluster_put_component_template
    | cluster_reroute
    | count
    | create
    | dangling_indices_dangling_index
    | delete_by_query
    | delete_by_query_rethrottle
    | eql_search
    | esql_query
    | explain
    | features_reset_features
    | field_caps
    | fleet_msearch
    | fleet_post_secret
    | fleet_search
    | graph_explore
    | ilm_migrate_to_data_tiers
    | ilm_move_to_step
    | ilm_remove_policy
    | ilm_retry
    | ilm_start
    | ilm_stop
    | index
    | indices_analyze
    | indices_clear_cache
    | indices_clone
    | indices_close
    | indices_disk_usage
    | indices_downsample
    | indices_flush
    | indices_forcemerge
    | indices_migrate_to_data_stream
    | indices_modify_data_stream
    | indices_open
    | indices_promote_data_stream
    | indices_put_alias
    | indices_put_mapping
    | indices_put_template
    | indices_refresh
    | indices_reload_search_analyzers
    | indices_rollover
    | indices_shrink
    | indices_simulate_index_template
    | indices_simulate_template
    | indices_split
    | indices_unfreeze
    | indices_update_aliases
    | indices_validate_query
    | inference_inference
    | inference_inference_stream
    | ingest_pipeline_simulate
    | ingest_simulate
    | knn_search
    | license_post
    | license_post_start_basic
    | license_post_start_trial
    | mget
    | migration_get_feature_upgrade_status
    | ml_clear_trained_model_deployment_cache
    | ml_close_job
    | ml_estimate_model_memory
    | ml_evaluate_data_frame
    | ml_explain_data_frame_analytics
    | ml_flush_job
    | ml_forecast
    | ml_get_buckets
    | ml_get_calendars
    | ml_get_categories
    | ml_get_influencers
    | ml_get_model_snapshots
    | ml_get_overall_buckets
    | ml_get_records
    | ml_infer_trained_model
    | ml_open_job
    | ml_post_calendar_events
    | ml_post_data
    | ml_preview_data_frame_analytics
    | ml_preview_datafeed
    | ml_reset_job
    | ml_revert_model_snapshot
    | ml_set_upgrade_mode
    | ml_start_data_frame_analytics
    | ml_start_datafeed
    | ml_start_trained_model_deployment
    | ml_stop_data_frame_analytics
    | ml_stop_datafeed
    | ml_stop_trained_model_deployment
    | ml_update_data_frame_analytics
    | ml_update_datafeed
    | ml_update_filter
    | ml_update_job
    | ml_update_model_snapshot
    | ml_update_trained_model_deployment
    | ml_upgrade_job_snapshot
    | ml_validate
    | ml_validate_detector
    | monitoring_bulk
    | msearch
    | msearch_template
    | mtermvectors
    | nodes_reload_secure_settings
    | open_point_in_time
    | put_script
    | rank_eval
    | reindex
    | reindex_rethrottle
    | render_search_template
    | rollup_rollup_search
    | rollup_start_job
    | rollup_stop_job
    | scripts_painless_execute
    | scroll
    | search
    | search_application_post_behavioral_analytics_event
    | search_application_render_query
    | search_application_search
    | search_mvt
    | search_shards
    | search_template
    | searchable_snapshots_clear_cache
    | searchable_snapshots_mount
    | security_activate_user_profile
    | security_bulk_update_api_keys
    | security_change_password
    | security_clear_api_key_cache
    | security_clear_cached_privileges
    | security_clear_cached_realms
    | security_clear_cached_roles
    | security_clear_cached_service_tokens
    | security_create_api_key
    | security_create_cross_cluster_api_key
    | security_create_service_token
    | security_disable_user
    | security_disable_user_profile
    | security_enable_user
    | security_enable_user_profile
    | security_get_token
    | security_has_privileges
    | security_has_privileges_user_profile
    | security_oidc_authenticate
    | security_oidc_logout
    | security_oidc_prepare_authentication
    | security_put_privileges
    | security_put_role
    | security_put_role_mapping
    | security_put_user
    | security_query_api_keys
    | security_saml_authenticate
    | security_saml_complete_logout
    | security_saml_invalidate
    | security_saml_logout
    | security_saml_prepare_authentication
    | security_suggest_user_profiles
    | security_update_user_profile_data
    | slm_execute_retention
    | slm_start
    | slm_stop
    | snapshot_cleanup_repository
    | snapshot_create
    | snapshot_create_repository
    | snapshot_repository_analyze
    | snapshot_restore
    | snapshot_verify_repository
    | sql_clear_cursor
    | sql_query
    | sql_translate
    | tasks_cancel
    | terms_enum
    | termvectors
    | text_structure_find_structure
    | text_structure_test_grok_pattern
    | transform_preview_transform
    | transform_reset_transform
    | transform_schedule_now_transform
    | transform_start_transform
    | transform_stop_transform
    | transform_update_transform
    | transform_upgrade_transforms
    | update
    | update_by_query
    | update_by_query_rethrottle
    | watcher_ack_watch
    | watcher_activate_watch
    | watcher_deactivate_watch
    | watcher_execute_watch
    | watcher_put_watch
    | watcher_query_watches
    | watcher_start
    | watcher_stop)
    ;
putStatement
    : PUT '/'? (apiPath
    | query_rule
    | snapshot_get
    | update_desired_nodes
    | autoscaling_policy
    | ingest_database
    | bulk
    | ccr_follow
    | ccr_put_auto_follow_pattern
    | cluster_put_component_template
    | cluster_put_settings
    | create
    | enrich_execute_policy
    | enrich_put_policy
    | ilm_delete_put_lifecycle
    | index
    | indices_add_block
    | indices_clone
    | indices_create
    | indices_create_data_stream
    | indices_put_alias
    | indices_put_data_lifecycle
    | indices_put_index_template
    | indices_put_mapping
    | indices_put_settings
    | indices_put_template
    | indices_shrink
    | indices_split
    | inference_put_model
    | inference_put_update
    | ingest_put_pipeline
    | license_post
    | logstash_put_pipeline
    | ml_put_calendar
    | ml_put_calendar_job
    | ml_put_data_frame_analytics
    | ml_put_datafeed
    | ml_put_filter
    | ml_put_job
    | ml_put_trained_model
    | ml_put_trained_model_alias
    | ml_put_trained_model_definition_part
    | ml_put_trained_model_vocabulary
    | monitoring_bulk
    | put_script
    | query_ruleset
    | rollup_put_job
    | search_application_put
    | search_application_put_behavioral_analytics
    | security_change_password
    | security_create_api_key
    | security_create_service_token
    | security_disable_user
    | security_disable_user_profile
    | security_enable_user
    | security_enable_user_profile
    | security_put_privileges
    | security_put_role
    | security_put_role_mapping
    | security_put_user
    | security_update_api_key
    | security_update_cross_cluster_api_key
    | security_update_settings
    | security_update_user_profile_data
    | shutdown_put_node
    | slm_execute_lifecycle
    | slm_put_lifecycle
    | snapshot_clone
    | snapshot_create
    | snapshot_create_repository
    | synonyms_put_synonym
    | synonyms_put_synonym_rule
    | transform_put_transform
    | watcher_ack_watch
    | watcher_activate_watch
    | watcher_deactivate_watch
    | watcher_execute_watch
    | watcher_put_watch
    | watcher_update_settings)
    ;
headStatement
    : HEAD '/'? (apiPath
    | cluster_exists_component_template
    | get
    | indices_exists
    | indices_exists_alias
    | indices_exists_index_template
    | indices_exists_template)
    ;

desired_balance
    : INTERNAL_ '/' DESIRED_BALANCE
    ;
desired_nodes
    : INTERNAL_ '/' DESIRED_NODES
    ;
get_desired_nodes
    : INTERNAL_ '/' DESIRED_NODES '/' LATEST_
    ;
prevalidate_node_removal
    : INTERNAL_ '/' PREVALIDATE_NODE_REMOVAL ('?' prevalidate_node_removal_params ('&' prevalidate_node_removal_params)* )?
    ;
prevalidate_node_removal_params
    : param_common
    | param_timeout
    | param_master_timeout
    | param_names
    | param_ids
    | param_external_ids
    ;

param_external_ids
    : EXTERNAL_IDS '=' ids
    ;

param_names
    : NAMES '=' node_name (',' node_name)*
    ;

update_desired_nodes
    : INTERNAL_ '/' DESIRED_NODES '/' history_id '/' version
    ;
async_search_delete
    : ASYNC_SEARCH_ '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
async_search_get
    : ASYNC_SEARCH_ '/' regular_id '/'? ('?' async_search_get_params ('&' async_search_get_params)* )?
    ;
async_search_get_params
    : param_common
    | param_keep_alive
    | param_typed_keys
    | param_wait_for_completion_timeout
    ;
async_search_status
    : ASYNC_SEARCH_ '/' STATUS '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
async_search_submit
    : (index_name '/')? ASYNC_SEARCH_ '/'? ('?' async_search_submit_params ('&' async_search_submit_params)* )?
    ;
async_search_submit_params
    : param_common
    | param_wait_for_completion_timeout
    | param_keep_on_completion
    | param_keep_alive
    | param_allow_no_indices
    | param_allow_partial_search_results
    | param_analyzer
    | param_analyze_wildcard
    | param_batched_reduce_size
    | param_ccs_minimize_roundtrips
    | param_default_operator
    | param_df
    | param_docvalue_fields
    | param_expand_wildcards
    | param_explain
    | param_ignore_throttled
    | param_ignore_unavailable
    | param_lenient
    | param_max_concurrent_shard_requests
    | param_min_compatible_shard_node
    | param_preference
    | param_pre_filter_shard_size
    | param_request_cache
    | param_routing
    | param_scroll
    | param_search_type
    | param_stats
    | param_stored_fields
    | param_suggest_field
    | param_suggest_mode
    | param_suggest_size
    | param_suggest_text
    | param_terminate_after
    | param_timeout
    | param_track_total_hits
    | param_track_scores
    | param_typed_keys
    | param_rest_total_hits_as_int
    | param_version
    | param_source
    | param_source_excludes
    | param_source_includes
    | parma_seq_no_primary_term
    | param_q
    | param_size
    | param_from
    | param_sort
    ;
autoscaling_policy
    : AUTOSCALING_ '/' POLICY '/' policy_name '/'? ('?' param_common ('&' param_common)* )?
    ;
get_autoscaling_capacity
    : AUTOSCALING_ '/' CAPACITY '/'? ('?' param_common ('&' param_common)* )?
    ;
bulk
    : (index_name '/')? BULK_ '/'? ('?' bulk_params ('&' bulk_params)* )?
    ;
bulk_params
    : param_common
    | param_pipeline
    | param_refresh
    | param_routing
    | param_source
    | param_source_excludes
    | param_source_includes
    | param_timeout
    | param_wait_for_active_shards
    | param_require_alias
    ;
// https://www.elastic.co/guide/en/elasticsearch/reference/current/cat-alias.html
cat_aliases
    : CAT_ '/' ALIASES ('/' aliases_name)? '/'? ('?' cat_aliases_params ('&' cat_aliases_params)* )?
    ;
cat_aliases_params
    : cat_common_params
    | param_h
    | param_s
    | param_expand_wildcards
    ;
cat_common_params
    : param_common
    | param_format
    | param_help
    | param_local
    | param_master_timeout
    | param_v
    ;
cat_allocation
    : CAT_ '/' ALLOCATION ('/' node_filters)? '/'? ('?' cat_allocation_params ('&' cat_allocation_params)* )?
    ;
cat_allocation_params
    : cat_common_params
    | param_h
    | param_s
    | param_expand_wildcards
    | param_bytes
    ;
cat_component_templates
    : CAT_ '/' COMPONENT_TEMPLATES ('/' template_name)? '/'? ('?' cat_component_templates_params ('&' cat_component_templates_params)* )?
    ;
cat_component_templates_params
    : cat_common_params
    | param_h
    | param_s
    ;
cat_count
    : CAT_ '/' COUNT ('/' target)? '/'? ('?' cat_count_params ('&' cat_count_params)* )?
    ;
cat_count_params
    : cat_common_params
    | param_h
    | param_s
    ;
cat_fielddata
    : CAT_ '/' FIELDDATA ('/' ids)? '/'? ('?' cat_fielddata_params ('&' cat_fielddata_params)* )?
    ;
cat_fielddata_params
    : cat_common_params
    | param_h
    | param_s
    | param_bytes
    | param_fields
    ;
cat_health
    : CAT_ '/' HEALTH '/'? ('?' cat_health_params ('&' cat_health_params)* )?
    ;
cat_health_params
    : cat_common_params
    | param_h
    | param_s
    | param_time
    | param_ts
    ;
cat_help
    : CAT_ '/'? ('?' cat_help_params ('&' cat_help_params)* )?
    ;
cat_help_params
    : cat_common_params
    | param_h
    | param_s
    ;
cat_indices
    : CAT_ '/' INDICES ('/' target)? '/'? ('?' cat_indices_params ('&' cat_indices_params)* )?
    ;
cat_indices_params
    : cat_common_params
    | param_h
    | param_s
    | param_bytes
    | param_expand_wildcards
    | param_health
    | param_include_unloaded_segments
    | param_pri
    | param_time
    ;
cat_master
    : CAT_ '/' MASTER '/'? ('?' cat_master_params ('&' cat_master_params)* )?
    ;
cat_master_params
    : cat_common_params
    | param_h
    | param_s
    ;
cat_ml_data_frame_analytics
    : CAT_ '/' ML '/' DATA_FRAME '/' ANALYTICS ('/' regular_id)? '/'? ('?' cat_ml_data_frame_analytics_params ('&' cat_ml_data_frame_analytics_params)* )?
    ;
cat_ml_data_frame_analytics_params
    : cat_common_params
    | param_h_analytics
    | param_s_analytics
    | param_allow_no_match
    | param_bytes
    ;
cat_ml_datafeeds
    : CAT_ '/' ML '/' DATAFEEDS ('/' datafeed_id)? '/'? ('?' cat_ml_datafeeds_params ('&' cat_ml_datafeeds_params)* )?
    ;
cat_ml_datafeeds_params
    : cat_common_params
    | param_h_datafeeds
    | param_s_datafeeds
    | param_allow_no_match
    | param_time
    ;
cat_ml_jobs
    : CAT_ '/' ML '/' ANOMALY_DETECTORS ('/' job_id)? '/'? ('?' cat_ml_jobs_params ('&' cat_ml_jobs_params)* )?
    ;
cat_ml_jobs_params
    : cat_common_params
    | param_h_jobs
    | param_s_jobs
    | param_allow_no_match
    | param_bytes
    | param_time
    ;
cat_ml_trained_models
    : CAT_ '/' ML '/' TRAINED_MODELS ('/' model_id)? '/'? ('?' cat_ml_trained_models_params ('&' cat_ml_trained_models_params)* )?
    ;
cat_ml_trained_models_params
    : cat_common_params
    | param_h_trained_models
    | param_s_trained_models
    | param_allow_no_match
    | param_bytes
    | param_time
    | param_from
    | param_size
    ;
cat_nodeattrs
    : CAT_ '/' NODEATTRS '/'? ('?' cat_nodeattrs_params ('&' cat_nodeattrs_params)* )?
    ;
cat_nodeattrs_params
    : cat_common_params
    | param_h
    | param_s
    ;
cat_nodes
    : CAT_ '/' NODES '/'? ('?' cat_nodes_params ('&' cat_nodes_params)* )?
    ;
cat_nodes_params
    : cat_common_params
    | param_h
    | param_s
    | param_bytes
    | param_full_id
    | param_include_unloaded_segments
    ;
cat_pending_tasks
    : CAT_ '/' PENDING_TASKS '/'? ('?' cat_pending_tasks_params ('&' cat_pending_tasks_params)* )?
    ;
cat_pending_tasks_params
    : cat_common_params
    | param_h
    | param_s
    ;

// https://www.elastic.co/guide/en/elasticsearch/reference/current/cat-plugins.html
cat_plugins
    : CAT_ '/' PLUGINS '/'? ('?' cat_plugins_params ('&' cat_plugins_params)* )?
    ;
cat_plugins_params
    : cat_common_params
    | param_h
    | param_s
    ;
cat_recovery
    : CAT_ '/' RECOVERY ('/' target)? '/'? ('?' cat_recovery_params ('&' cat_recovery_params)* )?
    ;
cat_recovery_params
    : cat_common_params
    | param_h
    | param_s
    | param_bytes
    | param_active_only
    | param_detailed
    ;
cat_repositories
    : CAT_ '/' REPOSITORIES '/'? ('?' cat_repositories_params ('&' cat_repositories_params)* )?
    ;
cat_repositories_params
    : cat_common_params
    | param_h
    | param_s
    ;
cat_segments
    : CAT_ '/' SEGMENTS ('/' target)? '/'? ('?' cat_segments_params ('&' cat_segments_params)* )?
    ;
cat_segments_params
    : cat_common_params
    | param_h
    | param_s
    | param_bytes
    ;
cat_shards
    : CAT_ '/' SHARDS ('/' target)? '/'? ('?' cat_shards_params ('&' cat_shards_params)* )?
    ;
cat_shards_params
    : cat_common_params
    | param_h
    | param_s
    | param_bytes
    ;
cat_snapshots
    : CAT_ '/' SNAPSHOTS ('/' repository_name)? '/'? ('?' cat_snapshots_params ('&' cat_snapshots_params)* )?
    ;
cat_snapshots_params
    : cat_common_params
    | param_h
    | param_s
    | param_ignore_unavailable
    ;
cat_tasks
    : CAT_ '/' TASKS '/'? ('?' cat_tasks_params ('&' cat_tasks_params)* )?
    ;
cat_tasks_params
    : cat_common_params
    | param_h
    | param_s
    | param_actions
    | param_detailed
    | param_node_id
    | param_nodes
    | param_parent_task_id
    ;
cat_templates
    : CAT_ '/' TEMPLATES ('/' template_name (',' template_name)*)? '/'? ('?' cat_templates_params ('&' cat_templates_params)* )?
    ;
cat_templates_params
    : cat_common_params
    | param_h
    | param_s
    ;
cat_thread_pool
    : CAT_ '/' THREAD_POOL ('/' thread_pool_name)? '/'? ('?' cat_thread_pool_params ('&' cat_thread_pool_params)* )?
    ;
cat_thread_pool_params
    : cat_common_params
    | param_h
    | param_s
    | param_time
    ;
cat_transforms
    : CAT_ '/' TRANSFORMS ('/' transform_ids)? '/'? ('?' cat_transforms_params ('&' cat_transforms_params)* )?
    ;
cat_transforms_params
    : cat_common_params
    | param_h
    | param_s
    | param_time
    | param_from
    | param_size
    | param_allow_no_match
    ;
ccr_delete_auto_follow_pattern
    : CCR_ '/' AUTO_FOLLOW '/' auto_follow_name '/'? ('?' param_common ('&' param_common)* )?
    ;
ccr_follow
    : follower_index '/' CCR_ '/' FOLLOW '/'? ('?' ccr_follow_params ('&' ccr_follow_params)* )?
    ;
ccr_follow_params
    : param_common
    | param_wait_for_active_shards
    ;
ccr_follow_info
    : follower_index '/' CCR_ '/' (FOLLOW_INFO | INFO) '/'? ('?' param_common ('&' param_common)* )?
    ;
ccr_follow_stats
    : follower_index '/' CCR_ '/' STATS '/'? ('?' param_common ('&' param_common)* )?
    ;
ccr_forget_follower
    : leader_index '/' CCR_ '/' FORGET_FOLLOWER '/'? ('?' param_common ('&' param_common)* )?
    ;
ccr_get_auto_follow_pattern
    : CCR_ '/' AUTO_FOLLOW ('/' auto_follow_name)? '/'? ('?' param_common ('&' param_common)* )?
    ;
ccr_pause_auto_follow_pattern
    : CCR_ '/' AUTO_FOLLOW '/' auto_follow_name '/' PAUSE '/'? ('?' param_common ('&' param_common)* )?
    ;
ccr_pause_follow
    : follower_index '/' CCR_ '/' PAUSE_FOLLOW '/'? ('?' param_common ('&' param_common)* )?
    ;
ccr_put_auto_follow_pattern
    : CCR_ '/' AUTO_FOLLOW '/' auto_follow_name '/'? ('?' param_common ('&' param_common)* )?
    ;
ccr_resume_auto_follow_pattern
    : CCR_ '/' AUTO_FOLLOW '/' auto_follow_name '/' RESUME '/'? ('?' param_common ('&' param_common)* )?
    ;
ccr_resume_follow
    : follower_index '/' CCR_ '/' RESUME_FOLLOW '/'? ('?' param_common ('&' param_common)* )?
    ;
ccr_stats
    : CCR_ '/' STATS '/'? ('?' param_common ('&' param_common)* )?
    ;
ccr_unfollow
    : follower_index '/' CCR_ '/' UNFOLLOW '/'? ('?' param_common ('&' param_common)* )?
    ;
clear_scroll
    : SEARCH_ '/' SCROLL ('/' scroll_id)? '/'? ('?' param_common ('&' param_common)* )?
    ;
close_point_in_time
    : PIT_ '/'? ('?' param_common ('&' param_common)* )?
    ;
cluster_allocation_explain
    : CLUSTER_ '/' ALLOCATION '/' EXPLAIN '/'? ('?' cluster_allocation_explain_params ('&' cluster_allocation_explain_params)* )?
    ;
cluster_allocation_explain_params
    : param_common
    | param_include_disk_info
    | param_include_yes_decisions
    ;
cluster_delete_component_template
    : COMPONENT_TEMPLATE_ '/' component_template_name '/'? ('?' cluster_delete_component_template_params ('&' cluster_delete_component_template_params)* )?
    ;
cluster_delete_component_template_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
cluster_delete_voting_config_exclusions
    : CLUSTER_ '/' VOTING_CONFIG_EXCLUSIONS '/'? ('?' cluster_delete_voting_config_exclusions_params ('&' cluster_delete_voting_config_exclusions_params)* )?
    ;
cluster_delete_voting_config_exclusions_params
    : param_common
    | param_wait_for_removal
    ;
cluster_exists_component_template
    : COMPONENT_TEMPLATE_ '/' component_template_name '/'? ('?' cluster_exists_component_template_params ('&' cluster_exists_component_template_params)* )?
    ;
cluster_exists_component_template_params
    : param_common
    | param_master_timeout
    | param_local
    ;
cluster_get_component_template
    : COMPONENT_TEMPLATE_ ('/' component_template_name)? '/'? ('?' cluster_get_component_template_params ('&' cluster_get_component_template_params)* )?
    ;
cluster_get_component_template_params
    : param_common
    | param_master_timeout
    | param_local
    | param_flat_settings
    | param_include_defaults
    ;
cluster_get_settings
    : CLUSTER_ '/' SETTINGS '/'? ('?' cluster_get_settings_params ('&' cluster_get_settings_params)* )?
    ;
cluster_get_settings_params
    : param_common
    | param_flat_settings
    | param_include_defaults
    | param_master_timeout
    | param_timeout
    ;
cluster_health
    : CLUSTER_ '/' HEALTH ('/' target)? '/'? ('?' cluster_health_params ('&' cluster_health_params)* )?
    ;
cluster_health_params
    : param_common
    | param_expand_wildcards
    | param_level
    | param_local
    | param_master_timeout
    | param_timeout
    | param_wait_for_active_shards
    | param_wait_for_events
    | param_wait_for_nodes
    | param_wait_for_no_initializing_shards
    | param_wait_for_no_relocating_shards
    | param_wait_for_status
    ;
cluster_info
    : INFO_ '/' cluster_info_target (',' cluster_info_target)* '/'? ('?' param_common ('&' param_common)* )?
    ;
cluster_info_target
    : ALL_ | HTTP | INGEST | THREAD_POOL | SCRIPT
    ;
cluster_pending_tasks
    : CLUSTER_ '/' PENDING_TASKS '/'? ('?' cluster_pending_tasks_params ('&' cluster_pending_tasks_params)* )?
    ;
cluster_pending_tasks_params
    : param_common
    | param_master_timeout
    | param_local
    ;
cluster_post_voting_config_exclusions
    : CLUSTER_ '/' VOTING_CONFIG_EXCLUSIONS '/'? ('?' cluster_post_voting_config_exclusions_params ('&' cluster_post_voting_config_exclusions_params)* )?
    ;
cluster_post_voting_config_exclusions_params
    : param_common
    | param_timeout
    | param_node_names
    | param_node_ids
    ;
cluster_put_component_template
    : COMPONENT_TEMPLATE_ '/' component_template_name '/'? ('?' cluster_put_component_template_params ('&' cluster_put_component_template_params)* )?
    ;
cluster_put_component_template_params
    : param_common
    | param_master_timeout
    | param_create
    ;
cluster_put_settings
    : CLUSTER_ '/' SETTINGS '/'? ('?' cluster_put_settings_params ('&' cluster_put_settings_params)* )?
    ;
cluster_put_settings_params
    : param_common
    | param_master_timeout
    | param_timeout
    | param_flat_settings
    ;
cluster_remote_info
    : REMOTE_ '/' INFO '/'? ('?' param_common ('&' param_common)* )?
    ;
cluster_reroute
    : CLUSTER_ '/' REROUTE '/'? ('?' cluster_reroute_params ('&' cluster_reroute_params)* )?
    ;
cluster_reroute_params
    : param_common
    | param_master_timeout
    | param_dry_run
    | param_explain
    | param_metric
    | param_retry_failed
    | param_timeout
    ;
cluster_state
    : CLUSTER_ '/' STATE ('/' metric_path (',' metric_path)* ('/' target)?)? '/'? ('?' cluster_state_params ('&' cluster_state_params)* )?
    ;
metric_path
    : ALL_ | BLOCKS | MASTER_NODE | METADATA | NODES | ROUTING_TABLE | ROUTING_NODES | VERSION
    ;
cluster_state_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_local
    | param_flat_settings
    | param_master_timeout
    | param_ignore_unavailable
    | param_wait_for_metadata_version
    | param_wait_for_timeout
    ;
cluster_stats
    : CLUSTER_ '/' STATS ('/' NODES '/' node_filters)? '/'? ('?' cluster_stats_params ('&' cluster_stats_params)* )?
    ;
cluster_stats_params
    : param_common
    | param_flat_settings
    | param_timeout
    | param_include_remotes
    ;

param_include_remotes
    : INCLUDE_REMOTES ('=' boolean_value)?
    ;

count
    : (target '/')? COUNT_ '/'? ('?' count_params ('&' count_params)* )?
    ;
count_params
    : param_common
    | param_allow_no_indices
    | param_analyzer
    | param_analyze_wildcard
    | param_default_operator
    | param_df
    | param_expand_wildcards
    | param_ignore_throttled
    | param_ignore_unavailable
    | param_lenient
    | param_min_score
    | param_preference
    | param_routing
    | param_terminate_after
    | param_q
    ;
create
    : (index_name '/')? CREATE_ ('/' regular_id)? '/'? ('?' create_params ('&' create_params)* )?
    ;
create_params
    : param_common
    | param_pipeline
    | param_refresh
    | param_routing
    | param_timeout
    | param_version
    | param_version_type
    | param_wait_for_active_shards
    | param_if_seq_no
    | param_if_primary_term
    | param_op_type
    | param_require_alias
    ;
dangling_indices_dangling_index
    : DANGLING_ '/' index_uuid '/'? ('?' dangling_indices_dangling_index_params ('&' dangling_indices_dangling_index_params)* )?
    ;
dangling_indices_dangling_index_params
    : param_common
    | param_accept_data_loss
    | param_master_timeout
    | param_timeout
    ;
dangling_indices_list_dangling_indices
    : DANGLING_ '/'? ('?' param_common ('&' param_common)* )?
    ;
delete
    : index_name '/' (DOC_ | DOC) '/' regular_id '/'? ('?' delete_params ('&' delete_params)* )?
    ;
delete_params
    : param_common
    | param_if_seq_no
    | param_if_primary_term
    | param_refresh
    | param_timeout
    | param_master_timeout
    | param_wait_for_active_shards
    | param_routing
    | param_version
    | param_version_type
    ;
delete_by_query
    : target '/' DELETE_BY_QUERY_ '/'? ('?' delete_by_query_params ('&' delete_by_query_params)* )?
    ;
delete_by_query_params
    : param_common
    | param_allow_no_indices
    | param_analyzer
    | param_analyze_wildcard
    | param_conflicts
    | param_default_operator
    | param_df
    | param_expand_wildcards
    | param_from
    | param_ignore_unavailable
    | param_lenient
    | parma_max_docs
    | param_preference
    | param_refresh
    | param_request_cache
    | param_requests_per_second
    | param_routing
    | param_q
    | param_scroll
    | param_scroll_size
    | param_search_timeout
    | param_search_type
    | param_slices
    | param_sort
    | param_stats
    | param_terminate_after
    | param_timeout
    | param_wait_for_active_shards
    | param_version
    | param_wait_for_completion
    ;
delete_by_query_rethrottle
    : DELETE_BY_QUERY_ '/' task_id '/' RETHROTTLE_ '/'? ('?' delete_by_query_rethrottle_params ('&' delete_by_query_rethrottle_params)* )?
    ;
delete_by_query_rethrottle_params
    : param_common
    | param_requests_per_second
    ;
delete_script
    : SCRIPTS_ '/' ids '/'? ('?' delete_script_params ('&' delete_script_params)* )?
    ;
delete_script_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
enrich_delete_policy
    : ENRICH_ '/' POLICY '/' policy_name '/'? ('?' param_common ('&' param_common)* )?
    ;
enrich_execute_policy
    : ENRICH_ '/' POLICY '/' policy_name '/' EXECUTE_ '/'? ('?' enrich_execute_policy_params ('&' enrich_execute_policy_params)* )?
    ;
enrich_execute_policy_params
    : param_common
    | param_wait_for_completion
    ;
enrich_get_policy
    : ENRICH_ '/' POLICY ('/' policy_name (COMMA policy_name)*)? '/'? ('?' param_common ('&' param_common)* )?
    ;
enrich_put_policy
    : ENRICH_ '/' POLICY '/' policy_name '/'? ('?' param_common ('&' param_common)* )?
    ;
enrich_stats
    : ENRICH_ '/' STATS_ '/'? ('?' param_common ('&' param_common)* )?
    ;
eql_delete
    : EQL_ '/' SEARCH '/' search_id '/'? ('?' param_common ('&' param_common)* )?
    ;
search_id
    : regular_id '='?
    ;

eql_get
    : EQL_ '/' SEARCH '/' search_id '/'? ('?' eql_get_params ('&' eql_get_params)* )?
    ;
eql_get_params
    : param_common
    | param_keep_alive
    | param_wait_for_completion_timeout
    ;
eql_get_status
    : EQL_ '/' SEARCH '/' STATUS '/' search_id '/'? ('?' param_common ('&' param_common)* )?
    ;
eql_search
    : target '/' EQL_ '/' SEARCH '/'? ('?' eql_search_params ('&' eql_search_params)* )?
    ;
eql_search_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_keep_alive
    | param_keep_on_completion
    | param_wait_for_completion_timeout
    ;
esql_query
    : QUERY_ ('/' ASYNC)? '/'? ('?' esql_query_params ('&' esql_query_params)* )?
    ;
esql_query_async
    : QUERY_ '/' ASYNC '/' search_id '/'? ('?' esql_query_params ('&' esql_query_params)* )?
    ;
esql_query_async_delete
    : QUERY '/' ASYNC '/' search_id '/'? ('?' param_common ('&' param_common)* )?
    ;

esql_query_params
    : param_common
    | param_format
    | param_wait_for_completion_timeout
//    | param_delimiter
    ;
explain
    : index_name '/' EXPLAIN_ '/' regular_id '/'? ('?' explain_params ('&' explain_params)* )?
    ;
explain_params
    : param_common
    | param_analyzer
    | param_analyze_wildcard
    | param_default_operator
    | param_df
    | param_lenient
    | param_preference
    | param_routing
    | param_source
    | param_source_excludes
    | param_source_includes
    | param_stored_fields
    | param_q
    ;
features_get_features
    : FEATURES_ '/'? ('?' param_common ('&' param_common)* )?
    ;
features_reset_features
    : FEATURES_ '/' RESET_ '/'? ('?' param_common ('&' param_common)* )?
    ;
field_caps
    : (target '/')? FIELD_CAPS_ '/'? ('?' field_caps_params ('&' field_caps_params)* )?
    ;
field_caps_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_fields
    | param_ignore_unavailable
    | param_include_unmapped
    | param_filters
    | param_types
    ;
fleet_secret
    : FLEET_ '/' SECRET_ '/' regular_id
    ;
fleet_global_checkpoints
    : index_name '/' FLEET_ '/' GLOBAL_CHECKPOINTS '/'? ('?' fleet_global_checkpoints_params ('&' fleet_global_checkpoints_params)* )?
    ;
fleet_global_checkpoints_params
    : param_common
    | param_wait_for_advance
    | param_wait_for_index
    | param_checkpoints
    | param_timeout
    ;
fleet_msearch
    : (index_name '/')? FLEET_ '/' FLEET_MSEARCH_ '/'? ('?' fleet_msearch_params ('&' fleet_msearch_params)* )?
    ;
fleet_msearch_params
    : param_common
    | param_allow_no_indices
    | param_ccs_minimize_roundtrips
    | param_expand_wildcards
    | param_ignore_throttled
    | param_ignore_unavailable
    | param_max_concurrent_searches
    | param_max_concurrent_shard_requests
    | param_pre_filter_shard_size
    | param_search_type
    | param_rest_total_hits_as_int
    | param_typed_keys
    | param_wait_for_checkpoints
    | param_allow_partial_search_results
    ;
fleet_post_secret
    : FLEET_ '/' SECRET
    ;
fleet_search
    : (index_name '/')? FLEET_ '/' FLEET_SEARCH_ '/'? ('?' fleet_search_params ('&' fleet_search_params)* )?
    ;
fleet_search_params
    : param_common
    | param_allow_no_indices
    | param_analyzer
    | param_analyze_wildcard
    | param_batched_reduce_size
    | param_ccs_minimize_roundtrips
    | param_default_operator
    | param_df
    | param_docvalue_fields
    | param_expand_wildcards
    | param_explain
    | param_ignore_throttled
    | param_ignore_unavailable
    | param_lenient
    | param_max_concurrent_shard_requests
    | param_min_compatible_shard_node
    | param_preference
    | param_pre_filter_shard_size
    | param_request_cache
    | param_routing
    | param_scroll
    | param_search_type
    | param_stats
    | param_stored_fields
    | param_suggest_field
    | param_suggest_mode
    | param_suggest_size
    | param_suggest_text
    | param_terminate_after
    | param_timeout
    | param_track_total_hits
    | param_track_scores
    | param_typed_keys
    | param_rest_total_hits_as_int
    | param_version
    | param_source
    | param_source_excludes
    | param_source_includes
    | param_seq_no_primary_term
    | param_q
    | param_size
    | param_from
    | param_sort
    | param_wait_for_checkpoints
    | param_allow_partial_search_results
    ;
get
   : index_name '/' (SOURCE_ | DOC_ | DOC) '/' regular_id '/'? ('?' get_params ('&' get_params)* )?
   ;
get_params
    : param_common
    | param_preference
    | param_realtime
    | param_refresh
    | param_routing
    | param_source
    | param_source_excludes
    | param_source_includes
    | param_stored_fields
    | param_version
    | param_version_type
    ;
get_script
    : SCRIPTS_ '/' regular_id '/'? ('?' get_script_params ('&' get_script_params)* )?
    ;
get_script_params
    : param_common
    | param_master_timeout
    ;
get_script_context
    : SCRIPT_CONTEXT_ '/'? ('?' param_common ('&' param_common)* )?
    ;
get_script_languages
    : SCRIPT_LANGUAGE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
graph_explore
    : index_name '/' GRAPH_ '/' EXPLORE '/'? ('?' graph_explore_params ('&' graph_explore_params)* )?
    ;
graph_explore_params
    : param_common
    | param_routing
    | param_timeout
    ;
health_report
    : HEALTH_REPORT_ ('/' indicator_value)? '/'? ('?' health_report_params ('&' health_report_params)* )?
    ;
indicator_value
    : MASTER_IS_STABLE
    | SHARDS_AVAILABILITY
    | DISK
    | ILM
    | REPOSITORY_INTEGRITY
    | SLM
    | SHARDS_CAPACITY
    ;
health_report_params
    : param_common
    | param_timeout
    | param_verbose
    | param_size
    ;
ilm_delete_put_lifecycle
    : ILM_ '/' POLICY '/' policy_id '/'? ('?' ilm_delete_lifecycle_params ('&' ilm_delete_lifecycle_params)* )?
    ;
ilm_delete_lifecycle_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
ilm_explain_lifecycle
    : target '/' ILM_ '/' EXPLAIN '/'? ('?' ilm_explain_lifecycle_params ('&' ilm_explain_lifecycle_params)* )?
    ;
ilm_explain_lifecycle_params
    : param_common
    | param_only_errors
    | param_only_managed
    | param_master_timeout
    | param_timeout
    ;
ilm_get_lifecycle
    : ILM_ '/' POLICY ('/' policy_id)? '/'? ('?' ilm_delete_lifecycle_params ('&' ilm_delete_lifecycle_params)* )?
    ;
ilm_get_status
    : ILM_ '/' STATUS '/'? ('?' param_common ('&' param_common)* )?
    ;
ilm_migrate_to_data_tiers
    : ILM_ '/' MIGRATE_TO_DATA_TIERS '/'? ('?' ilm_migrate_to_data_tiers_params ('&' ilm_migrate_to_data_tiers_params)* )?
    ;
ilm_migrate_to_data_tiers_params
    : param_common
    | param_dry_run
    ;
ilm_move_to_step
    : ILM_ '/' MOVE '/' index_name '/'? ('?' param_common ('&' param_common)* )?
    ;
ilm_remove_policy
    : target '/' ILM_ '/' REMOVE '/'? ('?' param_common ('&' param_common)* )?
    ;
ilm_retry
    : index_name '/' ILM_ '/' RETRY '/'? ('?' param_common ('&' param_common)* )?
    ;
ilm_start
    : ILM_ '/' START '/'? ('?' ilm_start_params ('&' ilm_start_params)* )?
    ;
ilm_start_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
ilm_stop
    : ILM_ '/' STOP '/'? ('?' ilm_start_params ('&' ilm_start_params)* )?
    ;
index
    : (index_name '/')? (DOC_ | DOC) ('/' regular_id)? '/'? ('?' create_params ('&' create_params)* )? operation_params?
    ;
operation_params
    : UPDATE_
    | BULK_
    | CREATE_
    ;
indices_add_block
    : target '/' BLOCK_ '/' (METADATA | READ | READ_ONLY | WRITE)? '/'? ('?' indices_add_block_params ('&' indices_add_block_params)* )?
    ;
indices_add_block_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_master_timeout
    | param_timeout
    ;
indices_analyze
    : (index_name '/')? ANALYZE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
indices_clear_cache
    : (target '/')? CACHE_ '/' CLEAR '/'? ('?' indices_clear_cache_params ('&' indices_clear_cache_params)* )?
    ;
indices_clear_cache_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_fielddata
    | param_fields
    | param_ignore_unavailable
    | param_query
    | param_request
    ;
indices_clone
    : index_name '/' CLONE_ '/' index_name '/'? ('?' indices_clone_params ('&' indices_clone_params)* )?
    ;
indices_clone_params
    : param_common
    | param_wait_for_active_shards
    | param_master_timeout
    | param_timeout
    ;
indices_close
    : target '/' CLOSE_ '/'? ('?' indices_close_params ('&' indices_close_params)* )?
    ;
indices_close_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_master_timeout
    | param_timeout
    | param_wait_for_active_shards
    ;
indices_create
    : index_name '/'? ('?' indices_create_params ('&' indices_create_params)* )?
    ;
indices_create_params
    : param_common
    | param_master_timeout
    | param_timeout
    | param_wait_for_active_shards
    ;
indices_create_data_stream
    : DATA_STREAM_ '/' data_stream_name '/'? ('?' param_common ('&' param_common)* )?
    ;
indices_data_streams_stats
    : DATA_STREAM_ ('/' data_stream_name)? '/' STATS_ '/'? ('?' indices_data_streams_stats_params ('&' indices_data_streams_stats_params)* )?
    ;
indices_data_streams_stats_params
    : param_common
    | param_expand_wildcards
    ;
indices_delete
    : target '/'? ('?' indices_delete_params ('&' indices_delete_params)* )?
    ;
indices_delete_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_master_timeout
    | param_timeout
    ;
indices_delete_alias
    : target '/' (ALIAS_ | ALIASES_) '/' aliases_name '/'? ('?' indices_delete_alias_params ('&' indices_delete_alias_params)* )?
    ;
indices_delete_alias_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
indices_delete_data_lifecycle
    : DATA_STREAM_ '/' target '/' LIFECYCLE_ '/'? ('?' indices_delete_data_lifecycle_params ('&' indices_delete_data_lifecycle_params)* )?
    ;
indices_delete_data_lifecycle_params
    : param_common
    | param_expand_wildcards
    | param_master_timeout
    | param_timeout
    ;
indices_delete_data_stream
    : DATA_STREAM_ '/' target '/'? ('?' indices_delete_data_stream_params ('&' indices_delete_data_stream_params)* )?
    ;
indices_delete_data_stream_params
    : param_common
    | param_expand_wildcards
    ;
indices_delete_index_template
    : INDEX_TEMPLATE_ '/' template_name '/'? ('?' indices_delete_index_template_params ('&' indices_delete_index_template_params)* )?
    ;
indices_delete_index_template_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
indices_delete_template
    : TEMPLATE_ '/' template_name '/'? ('?' indices_delete_template_params ('&' indices_delete_template_params)* )?
    ;
indices_delete_template_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
indices_disk_usage
    : target '/' DISK_USAGE_ '/'? ('?' indices_disk_usage_params ('&' indices_disk_usage_params)* )?
    ;
indices_disk_usage_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_flush
    | param_ignore_unavailable
    | param_run_expensive_tasks
    ;
indices_downsample
    : index_name '/' DOWNSAMPLE_ '/' index_name '/'? ('?' param_common ('&' param_common)* )?
    ;
indices_exists
    : target '/'? ('?' indices_exists_params ('&' indices_exists_params)* )?
    ;
indices_exists_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_flat_settings
    | param_ignore_unavailable
    | param_include_defaults
    | param_local
    ;
indices_exists_alias
    : (target '/')? ALIAS_ '/' index_name '/'? ('?' indices_exists_alias_params ('&' indices_exists_alias_params)* )?
    ;
indices_exists_alias_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_local
    ;
indices_exists_index_template
    : INDEX_TEMPLATE_ '/' template_name '/'? ('?' indices_exists_index_template_params ('&' indices_exists_index_template_params)* )?
    ;
indices_exists_index_template_params
    : param_common
    | param_master_timeout
    ;
indices_exists_template
    : TEMPLATE_ '/' template_name '/'? ('?' indices_exists_template_params ('&' indices_exists_template_params)* )?
    ;
indices_exists_template_params
    : param_common
    | param_master_timeout
    | param_flat_settings
    | param_local
    ;
indices_explain_data_lifecycle
    : target '/' LIFECYCLE_ '/' EXPLAIN '/'? ('?' indices_explain_data_lifecycle_params ('&' indices_explain_data_lifecycle_params)* )?
    ;
indices_explain_data_lifecycle_params
    : param_common
    | param_include_defaults
    | param_master_timeout
    ;
indices_field_usage_stats
    : target '/' FIELD_USAGE_STATS_ '/'? ('?' indices_field_usage_stats_params ('&' indices_field_usage_stats_params)* )?
    ;
indices_field_usage_stats_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_fields
    | param_master_timeout
    | param_timeout
    | param_wait_for_active_shards
    ;
indices_flush
    : (target '/')? FLUSH_ '/'? ('?' indices_flush_params ('&' indices_flush_params)* )?
    ;
indices_flush_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_force
    | param_ignore_unavailable
    | param_wait_if_ongoing
    ;
indices_forcemerge
    : (target '/')? FORCEMERGE_ '/'? ('?' indices_forcemerge_params ('&' indices_forcemerge_params)* )?
    ;
indices_forcemerge_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_flush
    | param_ignore_unavailable
    | param_max_num_segments
    | param_only_expunge_deletes
    | param_wait_for_completion
    ;
indices_get
    : target '/'? ('?' indices_get_params ('&' indices_get_params)* )?
    ;
indices_get_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_flat_settings
    | param_ignore_unavailable
    | param_include_defaults
    | param_local
    | param_master_timeout
    | param_features
    ;
indices_get_alias
    : (target '/')? ALIAS_ ('/' target)? '/'? ('?' indices_get_alias_params ('&' indices_get_alias_params)* )?
    ;
indices_get_alias_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_local
    ;
indices_get_data_lifecycle
    : DATA_STREAM_ '/' target '/' LIFECYCLE_ '/'? ('?' indices_get_data_lifecycle_params ('&' indices_get_data_lifecycle_params)* )?
    ;
indices_get_data_lifecycle_params
    : param_common
    | param_expand_wildcards
    | param_include_defaults
    ;
indices_get_data_stream
    : DATA_STREAM_ '/' target '/'? ('?' indices_get_data_stream_params ('&' indices_get_data_stream_params)* )?
    ;
indices_get_data_stream_params
    : param_common
    | param_expand_wildcards
    | param_include_defaults
    ;
indices_get_field_mapping
    : (target '/')? MAPPING_ '/' FIELD '/' fields '/'? ('?' indices_get_field_mapping_params ('&' indices_get_field_mapping_params)* )?
    ;
indices_get_field_mapping_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_include_defaults
    | param_local
    ;
indices_get_index_template
    : INDEX_TEMPLATE_ ('/' template_name)? '/'? ('?' indices_get_index_template_params ('&' indices_get_index_template_params)* )?
    ;
indices_get_index_template_params
    : param_common
    | param_flat_settings
    | param_master_timeout
    | param_include_defaults
    | param_local
    ;
indices_get_mapping
    : (target '/')? MAPPING_ '/'? ('?' indices_get_mapping_params ('&' indices_get_mapping_params)* )?
    ;
indices_get_mapping_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_master_timeout
    | param_ignore_unavailable
    | param_local
    ;
indices_get_settings
    : (target '/')? SETTINGS_ ('/' setting_names)? '/'? ('?' indices_get_settings_params ('&' indices_get_settings_params)* )?
    ;
indices_get_settings_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_flat_settings
    | param_ignore_unavailable
    | param_include_defaults
    | param_local
    | param_master_timeout
    ;
indices_get_template
    : TEMPLATE_ ('/' template_names)? '/'? ('?' indices_get_template_params ('&' indices_get_template_params)* )?
    ;
indices_get_template_params
    : param_common
    | param_flat_settings
    | param_master_timeout
    | param_local
    ;
indices_migrate_to_data_stream
    : DATA_STREAM_ '/' MIGRATE_ '/' target '/'? ('?' param_common ('&' param_common)* )?
    ;
indices_modify_data_stream
    : DATA_STREAM_ '/' MODIFY_ '/'? ('?' param_common ('&' param_common)* )?
    ;
indices_open
    : target '/' OPEN_ '/'? ('?' indices_open_params ('&' indices_open_params)* )?
    ;
indices_open_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_master_timeout
    | param_timeout
    | param_wait_for_active_shards
    ;
indices_promote_data_stream
    : DATA_STREAM_ '/' PROMOTE_ '/' target '/'? ('?' param_common ('&' param_common)* )?
    ;
indices_put_alias
    : target '/' (ALIAS_ | ALIASES_) '/' alias_name '/'? ('?' indices_put_alias_params ('&' indices_put_alias_params)* )?
    ;
indices_put_alias_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
indices_put_data_lifecycle
    : DATA_STREAM_ '/' data_stream_names '/' LIFECYCLE_ '/'? ('?' indices_put_data_lifecycle_params ('&' indices_put_data_lifecycle_params)* )?
    ;
indices_put_data_lifecycle_params
    : param_common
    | param_expand_wildcards
    | param_master_timeout
    | param_timeout
    ;
indices_put_index_template
    : INDEX_TEMPLATE_ '/' template_name '/'? ('?' indices_put_index_template_params ('&' indices_put_index_template_params)* )?
    ;
indices_put_index_template_params
    : param_common
    | param_create
    ;
indices_put_mapping
    : target '/' MAPPING_ '/'? ('?' indices_put_mapping_params ('&' indices_put_mapping_params)* )?
    ;
indices_put_mapping_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_master_timeout
    | param_timeout
    | param_write_index_only
    ;
indices_put_settings
    : (target '/')? SETTINGS_ '/'? ('?' indices_put_settings_params ('&' indices_put_settings_params)* )?
    ;
indices_put_settings_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_flat_settings
    | param_ignore_unavailable
    | param_master_timeout
    | param_timeout
    | param_preserve_existing
    ;
indices_put_template
    : TEMPLATE_ '/' template_name '/'? ('?' indices_put_template_params ('&' indices_put_template_params)* )?
    ;
indices_put_template_params
    : param_common
    | param_create
    | param_flat_settings
    | param_master_timeout
    | param_timeout
    | param_order
    ;
indices_recovery
    : (target '/')? RECOVERY_ '/'? ('?' indices_recovery_params ('&' indices_recovery_params)* )?
    ;
indices_recovery_params
    : param_common
    | param_active_only
    | param_detailed
    ;
indices_refresh
    : (target '/')? REFRESH_ '/'? ('?' indices_refresh_params ('&' indices_refresh_params)* )?
    ;
indices_refresh_params
    : param_common
    | param_wait_for_active_shards
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    ;
indices_reload_search_analyzers
    : target '/' RELOAD_SEARCH_ANALYZERS_ '/'? ('?' indices_reload_search_analyzers_params ('&' indices_reload_search_analyzers_params)* )?
    ;
indices_reload_search_analyzers_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    ;
indices_resolve_index
    : RESOLVE_ '/' INDEX '/' target (COMMA target)* '/'? ('?' indices_resolve_index_params ('&' indices_resolve_index_params)* )?
    ;
indices_resolve_index_params
    : param_common
    | param_expand_wildcards
    ;
indices_rollover
    : index_name '/' ROLLOVER_ ('/' index_name)? '/'? ('?' indices_rollover_params ('&' indices_rollover_params)* )?
    ;
indices_rollover_params
    : param_common
    | param_dry_run
    | param_master_timeout
    | param_timeout
    | param_wait_for_active_shards
    | param_lazy
    ;

param_lazy
    : LAZY ('=' boolean_value)?
    ;
indices_segments
    : (target '/')? SEGMENTS_ '/'? ('?' indices_segments_params ('&' indices_segments_params)* )?
    ;
indices_segments_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_verbose
    ;
indices_shard_stores
    : (target '/')? SHARD_STORES_ '/'? ('?' indices_shard_stores_params ('&' indices_shard_stores_params)* )?
    ;
indices_shard_stores_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_status
    ;
indices_shrink
    : index_name '/' SHRINK_ '/' index_name '/'? ('?' indices_shrink_params ('&' indices_shrink_params)* )?
    ;
indices_shrink_params
    : param_common
    | param_master_timeout
    | param_timeout
    | param_wait_for_active_shards
    ;
indices_simulate_index_template
    : INDEX_TEMPLATE_ '/' SIMULATE_INDEX_ '/' index_name '/'? ('?' indices_simulate_index_template_params ('&' indices_simulate_index_template_params)* )?
    ;
indices_simulate_index_template_params
    : param_common
    | param_create
    | param_include_defaults
    | param_master_timeout
    ;
indices_simulate_template
    : INDEX_TEMPLATE_ '/' SIMULATE_ ('/' index_name)? '/'? ('?' indices_simulate_template_params ('&' indices_simulate_template_params)* )?
    ;
indices_simulate_template_params
    : param_common
    | param_create
    | param_include_defaults
    | param_master_timeout
    ;
indices_split
    : index_name '/' SPLIT_ '/' index_name '/'? ('?' indices_split_params ('&' indices_split_params)* )?
    ;
indices_split_params
    : param_common
    | param_master_timeout
    | param_timeout
    | param_wait_for_active_shards
    ;
indices_stats
    : (target '/')? STATS_ ('/' index_metric (',' index_metric)?)? '/'? ('?' indices_stats_params ('&' indices_stats_params)* )?
    ;
index_metric
    : ALL_ | COMPLETION | DOCS | FIELDDATA | FLUSH | GET | INDEXING
    | MERGE | QUERY_CACHE | REFRESH | REQUEST_CACHE | SEARCH | SEGMENTS
    | STORE | TRANSLOG
    ;
indices_stats_params
    : param_common
    | param_completion_fields
    | param_expand_wildcards
    | param_fielddata_fields
    | param_fields
    | param_forbid_closed_indices
    | param_groups
    | param_include_segment_file_sizes
    | param_include_unloaded_segments
    | param_level
    ;
indices_unfreeze
    : index_name '/' UNFREEZE_ '/'? ('?' indices_unfreeze_params ('&' indices_unfreeze_params)* )?
    ;
indices_unfreeze_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_master_timeout
    | param_timeout
    | param_wait_for_active_shards
    ;
indices_update_aliases
    : ALIASES_ '/'? ('?' indices_update_aliases_params ('&' indices_update_aliases_params)* )?
    ;
indices_update_aliases_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
indices_validate_query
    : (target '/')? VALIDATE_ '/' QUERY '/'? ('?' indices_validate_query_params ('&' indices_validate_query_params)* )?
    ;
indices_validate_query_params
    : param_common
    | param_allow_no_indices
    | param_all_shards
    | param_analyzer
    | param_analyze_wildcard
    | param_default_operator
    | param_df
    | param_expand_wildcards
    | param_explain
    | param_ignore_unavailable
    | param_lenient
    | param_rewrite
    | param_q
    ;
inference_delete_model
    : INFERENCE_ ('/' task_type)? '/' model_id '/'? ('?' param_common ('&' param_common)* )?
    ;
task_type
    : regular_id
    ;

inference_get_model
    : INFERENCE_ ('/' task_type)? '/' (model_id | ALL_) '/'? ('?' param_common ('&' param_common)* )?
    ;
inference_inference
    : INFERENCE_ ('/' task_type)? '/' model_id '/'? ('?' param_common ('&' param_common)* )?
    ;
inference_inference_stream
    : INFERENCE_ ('/' task_type)? '/' model_id '/' STREAM_ '/'? ('?' param_common ('&' param_common)* )?
    ;

inference_put_model
    : INFERENCE_ '/' task_type '/' model_id '/'? ('?' param_common ('&' param_common)* )?
    ;

inference_put_update
    : INFERENCE_ ('/' task_type)? '/' model_id '/' UPDATE_ '/'? ('?' param_common ('&' param_common)* )?
    ;

ingest_delete_pipeline
    : INGEST_ '/' PIPELINE '/' pipeline_ids '/'? ('?' ingest_delete_pipeline_params ('&' ingest_delete_pipeline_params)* )?
    ;
ingest_delete_pipeline_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
ingest_geo_ip_stats
    : INGEST_ '/' GEOIP '/' STATS '/'? ('?' param_common ('&' param_common)* )?
    ;
ingest_get_pipeline
    : INGEST_ '/' PIPELINE ('/' pipeline_ids)? '/'? ('?' ingest_get_pipeline_params ('&' ingest_get_pipeline_params)* )?
    ;
ingest_get_pipeline_params
    : param_common
    | param_master_timeout
    | param_summary
    ;
ingest_processor_grok
    : INGEST_ '/' PROCESSOR '/' GROK '/'? ('?' param_common ('&' param_common)* )?
    ;
ingest_put_pipeline
    : INGEST_ '/' PIPELINE '/' pipeline_ids '/'? ('?' ingest_put_pipeline_params ('&' ingest_put_pipeline_params)* )?
    ;
ingest_put_pipeline_params
    : param_common
    | param_master_timeout
    | param_timeout
    | param_if_version
    ;
ingest_pipeline_simulate
    : INGEST_ '/' PIPELINE ('/' pipeline_id)? '/' SIMULATE_ '/'? ('?' ingest_simulate_params ('&' ingest_simulate_params)* )?
    ;
ingest_simulate_params
    : param_common
    | param_verbose
    ;
ingest_simulate
    : INGEST_ ('/' target)? '/' SIMULATE_ '/'? ('?' ingest_simulate_params ('&' ingest_simulate_params)* )?
    ;

ingest_database
    : INGEST_ '/' IP_LOCATION '/' DATABASE ('/' database=regular_id)? '/'? ('?' ingest_database_params ('&' ingest_database_params)* )?
    ;
ingest_database_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
knn_search
    : target '/' KNN_SEARCH_ '/'? ('?' knn_search_params ('&' knn_search_params)* )?
    ;
knn_search_params
    : param_common
    | param_routing
    ;

lifecycle_stats
    : LIFECYCLE_ '/' STATS '/'? ('?' lifecycle_stats_params ('&' lifecycle_stats_params)* )?
    ;

lifecycle_stats_params
    : param_common
    ;

license_delete
    : LICENSE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
license_get
    : LICENSE_ '/'? ('?' license_get_params ('&' license_get_params)* )?
    ;
license_get_params
    : param_common
    | param_accept_enterprise
    | param_local
    ;
license_post
    : LICENSE_ '/'? ('?' license_post_params ('&' license_post_params)* )?
    ;
license_post_params
    : param_common
    | param_acknowledge
    ;
license_get_basic_status
    : LICENSE_ '/' BASIC_STATUS '/'? ('?' param_common ('&' param_common)* )?
    ;
license_get_trial_status
    : LICENSE_ '/' TRIAL_STATUS '/'? ('?' param_common ('&' param_common)* )?
    ;
license_post_start_basic
    : LICENSE_ '/' START_BASIC '/'? ('?' license_post_params ('&' license_post_params)* )?
    ;
license_post_start_trial
    : LICENSE_ '/' START_TRIAL '/'? ('?' license_post_start_trial_params ('&' license_post_start_trial_params)* )?
    ;
license_post_start_trial_params
    : param_common
    | param_acknowledge
    | param_type_query_string
    ;
logstash_delete_pipeline
    : LOGSTASH_ '/' PIPELINE '/' pipeline_id '/'? ('?' param_common ('&' param_common)* )?
    ;
logstash_get_pipeline
    : LOGSTASH_ '/' PIPELINE ('/' pipeline_ids)? '/'? ('?' param_common ('&' param_common)* )?
    ;
logstash_put_pipeline
    : LOGSTASH_ '/' PIPELINE '/' pipeline_id '/'? ('?' param_common ('&' param_common)* )?
    ;
mget
    : (target '/')? MGET_ '/'? ('?' mget_params ('&' mget_params)* )?
    ;
mget_params
    : param_common
    | param_preference
    | param_realtime
    | param_routing
    | param_source
    | param_source_excludes
    | param_source_includes
    | param_stored_fields
    ;
migration_deprecations
    : (target '/')? MIGRATION_ '/' DEPRECATIONS '/'? ('?' param_common ('&' param_common)* )?
    ;
migration_get_feature_upgrade_status
    : MIGRATION_ '/' SYSTEM_FEATURES '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_clear_trained_model_deployment_cache
    : ML_ '/' TRAINED_MODELS '/' model_id '/' DEPLOYMENT '/' CACHE '/' CLEAR_ '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_close_job
    : ML_ '/' ANOMALY_DETECTORS '/' job_ids '/' CLOSE_ '/'? ('?' ml_close_job_params ('&' ml_close_job_params)* )?
    ;
ml_close_job_params
    : param_common
    | param_allow_no_match
    | param_force
    | param_timeout
    ;
ml_delete_calendar
    : ML_ '/' CALENDARS '/' calendar_id '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_delete_calendar_event
    : ML_ '/' CALENDARS '/' calendar_id '/' EVENTS '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_delete_calendar_job
    : ML_ '/' CALENDARS '/' calendar_id '/' JOBS '/' job_ids '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_delete_data_frame_analytics
    : ML_ '/' DATA_FRAME '/' ANALYTICS '/' regular_id '/'? ('?' ml_delete_data_frame_analytics_params ('&' ml_delete_data_frame_analytics_params)* )?
    ;
ml_delete_data_frame_analytics_params
    : param_common
    | param_force
    | param_timeout
    ;
ml_delete_datafeed
    : ML_ '/' DATAFEEDS '/' regular_id '/'? ('?' ml_delete_datafeed_params ('&' ml_delete_datafeed_params)* )?
    ;
ml_delete_datafeed_params
    : param_common
    | param_force
    ;
ml_delete_expired_data
    : ML_ '/' DELETE_EXPIRED_DATA_ ('/' job_id)? '/'? ('?' ml_delete_expired_data_params ('&' ml_delete_expired_data_params)* )?
    ;
ml_delete_expired_data_params
    : param_common
    | param_requests_per_second
    | param_timeout
    ;
ml_delete_filter
    : ML_ '/' FILTERS '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_delete_forecast
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/' FORECAST_ ('/' ids_all)? '/'? ('?' ml_delete_forecast_params ('&' ml_delete_forecast_params)* )?
    ;
ml_delete_forecast_params
    : param_common
    | param_allow_no_forecasts
    | param_timeout
    ;
ml_delete_job
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/'? ('?' ml_delete_job_params ('&' ml_delete_job_params)* )?
    ;
ml_delete_job_params
    : param_common
    | param_force
    | param_delete_user_annotations
    | param_wait_for_completion
    ;
ml_delete_model_snapshot
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/' MODEL_SNAPSHOTS '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_delete_trained_model
    : ML_ '/' TRAINED_MODELS '/' model_id '/'? ('?' ml_delete_trained_model_params ('&' ml_delete_trained_model_params)* )?
    ;
ml_delete_trained_model_params
    : param_common
    | param_force
    ;
ml_delete_trained_model_alias
    : ML_ '/' TRAINED_MODELS '/' model_id '/' MODEL_ALIASES '/' alias_name '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_estimate_model_memory
    : ML_ '/' ANOMALY_DETECTORS '/' ESTIMATE_MODEL_MEMORY_ '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_evaluate_data_frame
    : ML_ '/' DATA_FRAME '/' EVALUATE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_explain_data_frame_analytics
    : ML_ '/' DATA_FRAME '/' ANALYTICS ('/' regular_id)? '/' EXPLAIN_ '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_flush_job
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/' FLUSH_ '/'? ('?' ml_flush_job_params ('&' ml_flush_job_params)* )?
    ;
ml_flush_job_params
    : param_common
    | param_advance_time
    | param_calc_interim
    | param_end
    | param_skip_time
    | param_start
    ;
ml_forecast
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/' FORECAST_ '/'? ('?' ml_forecast_params ('&' ml_forecast_params)* )?
    ;
ml_forecast_params
    : param_common
    | param_duration
    | param_expires_in
    | param_max_model_memory
    ;
ml_get_buckets
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/' RESULTS '/' BUCKETS ('/' regular_id)? '/'? ('?' ml_get_buckets_params ('&' ml_get_buckets_params)* )?
    ;
ml_get_buckets_params
    : param_common
    | param_anomaly_score
    | param_desc
    | param_end
    | param_exclude_interim
    | param_expand
    | param_from
    | param_size
    | param_sort
    | param_start
    ;
ml_get_calendar_events
    : ML_ '/' CALENDARS '/' calendar_ids '/' EVENTS '/'? ('?' ml_get_calendar_events_params ('&' ml_get_calendar_events_params)* )?
    ;
ml_get_calendar_events_params
    : param_common
    | param_end
    | param_job_id
    | param_from
    | param_size
    | param_start
    ;
ml_get_calendars
    : ML_ '/' CALENDARS ('/' calendar_ids)? '/'? ('?' ml_get_calendars_params ('&' ml_get_calendars_params)* )?
    ;
ml_get_calendars_params
    : param_common
    | param_from
    | param_size
    ;
ml_get_categories
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/' RESULTS '/' CATEGORIES ('/' regular_id)? '/'? ('?' ml_get_categories_params ('&' ml_get_categories_params)* )?
    ;
ml_get_categories_params
    : param_common
    | param_from
    | param_size
    | param_partition_field_value
    ;
ml_get_data_frame_analytics
    : ML_ '/' DATA_FRAME '/' ANALYTICS ('/' ids_all)? '/'? ('?' ml_get_data_frame_analytics_params ('&' ml_get_data_frame_analytics_params)* )?
    ;
ml_get_data_frame_analytics_params
    : param_common
    | param_allow_no_match
    | param_from
    | param_size
    | param_exclude_generated
    ;
ml_get_data_frame_analytics_stats
    : ML_ '/' DATA_FRAME '/' ANALYTICS ('/' ids_all)? '/' STATS_ '/'? ('?' ml_get_data_frame_analytics_stats_params ('&' ml_get_data_frame_analytics_stats_params)* )?
    ;
ml_get_data_frame_analytics_stats_params
    : param_common
    | param_allow_no_match
    | param_from
    | param_size
    | param_verbose
    ;
ml_get_datafeed_stats
    : ML_ '/' DATAFEEDS ('/' ids_all)? '/' STATS_ '/'? ('?' ml_get_datafeed_stats_params ('&' ml_get_datafeed_stats_params)* )?
    ;
ml_get_datafeed_stats_params
    : param_common
    | param_allow_no_match
    ;
ml_get_datafeeds
    : ML_ '/' DATAFEEDS ('/' ids_all)? '/'? ('?' ml_get_datafeeds_params ('&' ml_get_datafeeds_params)* )?
    ;
ml_get_datafeeds_params
    : param_common
    | param_allow_no_match
    | param_exclude_generated
    ;
ml_get_filters
    : ML_ '/' FILTERS ('/' ids_all)? '/'? ('?' ml_get_filters_params ('&' ml_get_filters_params)* )?
    ;
ml_get_filters_params
    : param_common
    | param_from
    | param_size
    ;
ml_get_influencers
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/' RESULTS '/' INFLUENCERS '/'? ('?' ml_get_influencers_params ('&' ml_get_influencers_params)* )?
    ;
ml_get_influencers_params
    : param_common
    | param_desc
    | param_end
    | param_exclude_interim
    | param_influencer_score
    | param_from
    | param_size
    | param_sort
    | param_start
    ;
ml_get_job_stats
    : ML_ '/' ANOMALY_DETECTORS ('/' ids_all)? '/' STATS_ '/'? ('?' ml_get_job_stats_params ('&' ml_get_job_stats_params)* )?
    ;
ml_get_job_stats_params
    : param_common
    | param_allow_no_match
    ;
ml_get_jobs
    : ML_ '/' ANOMALY_DETECTORS ('/' ids_all)? '/'? ('?' ml_get_jobs_params ('&' ml_get_jobs_params)* )?
    ;
ml_get_jobs_params
    : param_common
    | param_allow_no_match
    | param_exclude_generated
    ;
ml_get_memory_stats
    : ML_ '/' MEMORY ('/' node_filters)? '/' STATS_ '/'? ('?' ml_get_memory_stats_params ('&' ml_get_memory_stats_params)* )?
    ;
ml_get_memory_stats_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
ml_get_model_snapshot_upgrade_stats
    : ML_ '/' ANOMALY_DETECTORS '/' job_ids '/' MODEL_SNAPSHOTS '/' ids_all '/' UPGRADE_ '/' STATS_ '/'? ('?' ml_get_model_snapshot_upgrade_stats_params ('&' ml_get_model_snapshot_upgrade_stats_params)* )?
    ;
ml_get_model_snapshot_upgrade_stats_params
    : param_common
    | param_allow_no_match
    ;
ml_get_model_snapshots
    : ML_ '/' ANOMALY_DETECTORS '/' job_ids '/' MODEL_SNAPSHOTS ('/' ids_all)? '/'? ('?' ml_get_model_snapshots_params ('&' ml_get_model_snapshots_params)* )?
    ;
ml_get_model_snapshots_params
    : param_common
    | param_allow_no_match
    | param_desc
    | param_end
    | param_from
    | param_size
    | param_sort
    | param_start
    ;
ml_get_overall_buckets
    : ML_ '/' ANOMALY_DETECTORS '/' ids_all '/' RESULTS '/' OVERALL_BUCKETS '/'? ('?' ml_get_overall_buckets_params ('&' ml_get_overall_buckets_params)* )?
    ;
ml_get_overall_buckets_params
    : param_common
    | param_allow_no_match
    | param_bucket_span
    | param_end
    | param_exclude_interim
    | param_overall_score
    | param_top_n
    | param_start
    ;
ml_get_records
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/' RESULTS '/' RECORDS '/'? ('?' ml_get_records_params ('&' ml_get_records_params)* )?
    ;
ml_get_records_params
    : param_common
    | param_desc
    | param_end
    | param_exclude_interim
    | param_from
    | param_record_score
    | param_size
    | param_sort
    | param_start
    ;
ml_get_trained_models
    : ML_ '/' TRAINED_MODELS ('/' ids_all)? '/'? ('?' ml_get_trained_models_params ('&' ml_get_trained_models_params)* )?
    ;
ml_get_trained_models_params
    : param_common
    | param_allow_no_match
    | param_decompress_definition
    | param_exclude_generated
    | param_from
    | param_include
    | param_size
    | param_tags
    ;
ml_get_trained_models_stats
    : ML_ '/' TRAINED_MODELS ('/' ids_all)? '/' STATS_ '/'? ('?' ml_get_trained_models_stats_params ('&' ml_get_trained_models_stats_params)* )?
    ;
ml_get_trained_models_stats_params
    : param_common
    | param_allow_no_match
    | param_from
    | param_size
    ;
ml_infer_trained_model
    : ML_ '/' TRAINED_MODELS '/' model_id ('/' DEPLOYMENT)? '/' INFER_ '/'? ('?' ml_infer_trained_model_params ('&' ml_infer_trained_model_params)* )?
    ;
ml_infer_trained_model_params
    : param_common
    | param_timeout
    ;
ml_info
    : ML_ '/' INFO '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_open_job
    : ML_ '/' ANOMALY_DETECTORS '/' job_ids '/' OPEN_ '/'? ('?' ml_open_job_params ('&' ml_open_job_params)* )?
    ;
ml_open_job_params
    : param_common
    | param_timeout
    ;
ml_post_calendar_events
    : ML_ '/' CALENDARS '/' calendar_ids '/' EVENTS '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_post_data
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/' DATA_ '/'? ('?' ml_post_data_params ('&' ml_post_data_params)* )?
    ;
ml_post_data_params
    : param_common
    | param_reset_end
    | param_reset_start
    ;
ml_preview_data_frame_analytics
    : ML_ '/' DATA_FRAME '/' ANALYTICS ('/' ids_all)? '/' PREVIEW_ '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_preview_datafeed
    : ML_ '/' DATAFEEDS ('/' ids_all)? '/' PREVIEW_ '/'? ('?' ml_post_datafeed_params ('&' ml_post_datafeed_params)* )?
    ;
ml_post_datafeed_params
    : param_common
    | param_timeout
    | param_start
    | param_end
    ;
ml_put_calendar
    : ML_ '/' CALENDARS '/' calendar_id '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_put_calendar_job
    : ML_ '/' CALENDARS '/' calendar_id '/' JOBS '/' ids_all '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_put_data_frame_analytics
    : ML_ '/' DATA_FRAME '/' ANALYTICS '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_put_datafeed
    : ML_ '/' DATAFEEDS '/' datafeed_id '/'? ('?' ml_put_datafeed_params ('&' ml_put_datafeed_params)* )?
    ;
ml_put_datafeed_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_throttled
    | param_ignore_unavailable
    ;
ml_put_filter
    : ML_ '/' FILTERS '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_put_job
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_put_trained_model
    : ML_ '/' TRAINED_MODELS '/' model_id '/'? ('?' ml_put_trained_model_params ('&' ml_put_trained_model_params)* )?
    ;
ml_put_trained_model_params
    : param_common
    | param_defer_definition_decompression
    ;
ml_put_trained_model_alias
    : ML_ '/' TRAINED_MODELS '/' model_id '/' MODEL_ALIASES '/' alias_name '/'? ('?' ml_put_trained_model_alias_params ('&' ml_put_trained_model_alias_params)* )?
    ;
ml_put_trained_model_alias_params
    : param_common
    | param_reassign
    ;
ml_put_trained_model_definition_part
    : ML_ '/' TRAINED_MODELS '/' model_id '/' DEFINITION '/' number_value '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_put_trained_model_vocabulary
    : ML_ '/' TRAINED_MODELS '/' model_id '/' VOCABULARY '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_reset_job
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/' RESET_ '/'? ('?' ml_reset_job_params ('&' ml_reset_job_params)* )?
    ;
ml_reset_job_params
    : param_common
    | param_wait_for_completion
    | param_delete_user_annotations
    ;
ml_revert_model_snapshot
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/' MODEL_SNAPSHOTS '/' regular_id '/' REVERT_ '/'? ('?' ml_revert_model_snapshot_params ('&' ml_revert_model_snapshot_params)* )?
    ;
ml_revert_model_snapshot_params
    : param_common
    | param_delete_intervening_results
    ;
ml_set_upgrade_mode
    : ML_ '/' SET_UPGRADE_MODE '/'? ('?' ml_set_upgrade_mode_params ('&' ml_set_upgrade_mode_params)* )?
    ;
ml_set_upgrade_mode_params
    : param_common
    | param_enabled
    | param_timeout
    ;
ml_start_data_frame_analytics
    : ML_ '/' DATA_FRAME '/' ANALYTICS '/' regular_id '/' START_ '/'? ('?' ml_start_data_frame_analytics_params ('&' ml_start_data_frame_analytics_params)* )?
    ;
ml_start_data_frame_analytics_params
    : param_common
    | param_timeout
    ;
ml_start_datafeed
    : ML_ '/' DATAFEEDS '/' ids_all '/' START_ '/'? ('?' ml_start_datafeed_params ('&' ml_start_datafeed_params)* )?
    ;
ml_start_datafeed_params
    : param_common
    | param_start
    | param_end
    | param_timeout
    ;
ml_start_trained_model_deployment
    : ML_ '/' TRAINED_MODELS '/' model_id '/' DEPLOYMENT '/' START_ '/'? ('?' ml_start_trained_model_deployment_params ('&' ml_start_trained_model_deployment_params)* )?
    ;
ml_start_trained_model_deployment_params
    : param_common
    | param_cache_size
    | param_deployment_id
    | param_number_of_allocations
    | param_priority
    | param_queue_capacity
    | param_threads_per_allocation
    | param_timeout
    | param_wait_for
    ;
ml_stop_data_frame_analytics
    : ML_ '/' DATA_FRAME '/' ANALYTICS '/' ids_all '/' STOP_ '/'? ('?' ml_stop_data_frame_analytics_params ('&' ml_stop_data_frame_analytics_params)* )?
    ;
ml_stop_data_frame_analytics_params
    : param_common
    | param_allow_no_match
    | param_force
    | param_timeout
    ;
ml_stop_datafeed
    : ML_ '/' DATAFEEDS '/' ids_all '/' STOP_ '/'? ('?' ml_stop_datafeed_params ('&' ml_stop_datafeed_params)* )?
    ;
ml_stop_datafeed_params
    : param_common
    | param_allow_no_match
    | param_force
    | param_timeout
    ;
ml_stop_trained_model_deployment
    : ML_ '/' TRAINED_MODELS '/' model_id '/' DEPLOYMENT '/' STOP_ '/'? ('?' ml_stop_trained_model_deployment_params ('&' ml_stop_trained_model_deployment_params)* )?
    ;
ml_stop_trained_model_deployment_params
    : param_common
    | param_allow_no_match
    | param_force
    ;
ml_update_data_frame_analytics
    : ML_ '/' DATA_FRAME '/' ANALYTICS '/' regular_id '/' UPDATE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_update_datafeed
    : ML_ '/' DATAFEEDS '/' datafeed_id '/' UPDATE_ '/'? ('?' ml_update_datafeed_params ('&' ml_update_datafeed_params)* )?
    ;
ml_update_datafeed_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_throttled
    | param_ignore_unavailable
    ;
ml_update_filter
    : ML_ '/' FILTERS '/' regular_id '/' UPDATE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_update_job
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/' UPDATE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_update_model_snapshot
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/' MODEL_SNAPSHOTS '/' regular_id '/' UPDATE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_update_trained_model_deployment
    : ML_ '/' TRAINED_MODELS '/' model_id '/' DEPLOYMENT '/' UPDATE_
    ;
ml_upgrade_job_snapshot
    : ML_ '/' ANOMALY_DETECTORS '/' job_id '/' MODEL_SNAPSHOTS '/' regular_id '/' UPGRADE_ '/'? ('?' ml_upgrade_job_snapshot_params ('&' ml_upgrade_job_snapshot_params)* )?
    ;
ml_upgrade_job_snapshot_params
    : param_common
    | param_wait_for_completion
    | param_timeout
    ;
ml_validate
    : ML_ '/' ANOMALY_DETECTORS '/' VALIDATE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
ml_validate_detector
    : ML_ '/' ANOMALY_DETECTORS '/' VALIDATE_ '/' DETECTOR '/'? ('?' param_common ('&' param_common)* )?
    ;
monitoring_bulk
    : MONITORING_ ('/' ids)? '/' BULK '/'? ('?' monitoring_bulk_params ('&' monitoring_bulk_params)* )?
    ;
monitoring_bulk_params
    : param_common
    | param_system_id
    | param_system_api_version
    | param_interval
    ;
msearch
    : (target '/')? MSEARCH_ '/'? ('?' msearch_params ('&' msearch_params)* )?
    ;
msearch_params
    : param_common
    | param_allow_no_indices
    | param_ccs_minimize_roundtrips
    | param_expand_wildcards
    | param_ignore_throttled
    | param_ignore_unavailable
    | param_max_concurrent_searches
    | param_max_concurrent_shard_requests
    | param_pre_filter_shard_size
    | param_rest_total_hits_as_int
    | param_routing
    | param_search_type
    | param_typed_keys
    ;
msearch_template
    : (target '/')? MSEARCH_ '/' TEMPLATE '/'? ('?' msearch_template_params ('&' msearch_template_params)* )?
    ;
msearch_template_params
    : param_common
    | param_ccs_minimize_roundtrips
    | param_max_concurrent_searches
    | param_search_type
    | param_rest_total_hits_as_int
    | param_typed_keys
    ;
mtermvectors
    : (target '/')? MTERMVECTORS_ '/'? ('?' mtermvectors_params ('&' mtermvectors_params)* )?
    ;
mtermvectors_params
    : param_common
    | param_ids
    | param_field_statistics
    | param_fields
    | param_offsets
    | param_payloads
    | param_positions
    | param_preference
    | param_realtime
    | param_routing
    | param_term_statistics
    | param_version
    | param_version_type
    ;
nodes_clear_repositories_metering_archive
    : NODES_ '/' node_filters '/' REPOSITORIES_METERING_ '/' number_value '/'? ('?' param_common ('&' param_common)* )?
    ;
nodes_get_repositories_metering_info
    : NODES_ '/' node_filters '/' REPOSITORIES_METERING_ '/'? ('?' param_common ('&' param_common)* )?
    ;
nodes_hot_threads
    : NODES_ ('/' node_filters)? '/' HOT_THREADS '/'? ('?' nodes_hot_threads_params ('&' nodes_hot_threads_params)* )?
    ;
nodes_hot_threads_params
    : param_common
    | param_ignore_idle_threads
    | param_interval
    | param_snapshots
    | param_master_timeout
    | param_threads
    | param_timeout
    | param_thread_type
    | param_thread_sort
    ;
nodes_info
    : NODES_ (('/' node_filters)? ('/' INFO)? '/' node_info_metric (',' node_info_metric)*)? '/'? ('?' nodes_info_params ('&' nodes_info_params)* )?
    ;
node_info_metric
    : ALL_ | AGGREGATIONS | HTTP | INDICES | INGEST | JVM | OS | PLUGINS
    | PROCESS | BREAKERS | THREAD_POOL | TRANSPORT
    ;
nodes_info_params
    : param_common
    | param_flat_settings
    | param_master_timeout
    | param_timeout
    ;
nodes_reload_secure_settings
    : NODES_ ('/' node_filters)? '/' RELOAD_SECURE_SETTINGS '/'? ('?' nodes_reload_secure_settings_params ('&' nodes_reload_secure_settings_params)* )?
    ;
nodes_reload_secure_settings_params
    : param_common
    | param_timeout
    ;
nodes_stats
    : NODES_ ('/' node_filters)? '/' STATS ('/' node_stats_metric (',' node_stats_metric)* ('/' node_stats_index_metric (',' node_stats_index_metric)*)?)? '/'? ('?' nodes_stats_params ('&' nodes_stats_params)* )?
    ;
node_stats_metric
    : ADAPTIVE_SELECTION | BREAKER | DISCOVERY | FS | HTTP | INDEXING_PRESSURE
    | INDICES | INGEST | JVM | OS | REPOSITORIES
    | PROCESS | THREAD_POOL | TRANSPORT
    ;
node_stats_index_metric
    : ALL
    | BULK
    | COMPLETION
    | DOCS
    | FIELDDATA
    | FLUSH
    | GET
    | INDEXING
    | MAPPINGS
    | MERGE
    | QUERY_CACHE
    | RECOVERY
    | REFRESH
    | REQUEST_CACHE
    | SEARCH
    | SEGMENTS
    | SHARD_STATS
    | STORE
    | TRANSLOG
    | WARMER
    | DENSE_VECTOR
    ;
nodes_stats_params
    : param_common
    | param_completion_fields
    | param_fielddata_fields
    | param_fields
    | param_groups
    | param_level
    | param_master_timeout
    | param_timeout
    | param_types
    | param_include_unloaded_segments
    ;
nodes_usage
    : NODES_ ('/' node_filters)? '/' USAGE ('/' nodes_usage_metric)? '/'? ('?' nodes_usage_params ('&' nodes_usage_params)* )?
    ;
nodes_usage_metric
    : ALL_ | REST_ACTIONS
    ;
nodes_usage_params
    : param_common
    | param_timeout
    ;
open_point_in_time
    : index_name '/' PIT_ '/'? ('?' open_point_in_time_params ('&' open_point_in_time_params)* )?
    ;
open_point_in_time_params
    : param_common
    | param_keep_alive
    | param_ignore_unavailable
    | param_preference
    | param_routing
    | param_allow_partial_search_results
    | param_expand_wildcards
    ;
put_script
    : SCRIPTS_ '/' regular_id ('/' script_context)? '/'? ('?' put_script_params ('&' put_script_params)* )?
    ;
script_context
    : regular_id
    ;
put_script_params
    : param_common
    | param_timeout
    | param_master_timeout
    ;

query_rule
    : QUERY_RULES_ '/' regular_id '/' RULE_ '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
query_ruleset_test
    : QUERY_RULES_ '/' regular_id '/' TEST_ '/'? ('?' param_common ('&' param_common)* )?
    ;

query_ruleset
    : QUERY_RULES_ '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
query_ruleset_list
    : QUERY_RULES_ '/'? ('?' query_ruleset_list_params ('&' query_ruleset_list_params)* )?
    ;
query_ruleset_list_params
    : param_common
    | param_size
    | param_from
    ;
rank_eval
    : (target '/')? RANK_EVAL_ '/'? ('?' rank_eval_params ('&' rank_eval_params)* )?
    ;
rank_eval_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_search_type
    ;
reindex
    : REINDEX_ '/'? ('?' reindex_params ('&' reindex_params)* )?
    ;
reindex_params
    : param_common
    | param_refresh
    | param_requests_per_second
    | param_scroll
    | param_slices
    | param_timeout
    | param_wait_for_active_shards
    | param_wait_for_completion
    | param_require_alias
    ;
reindex_rethrottle
    : REINDEX_ '/' task_id '/' RETHROTTLE_ '/'? ('?' reindex_rethrottle_params ('&' reindex_rethrottle_params)* )?
    ;
reindex_rethrottle_params
    : param_common
    | param_requests_per_second
    ;
render_search_template
    : RENDER_ '/' TEMPLATE ('/' ids_all)? '/'? ('?' param_common ('&' param_common)* )?
    ;
rollup_delete_job
    : ROLLUP_ '/' JOB '/' job_id '/'? ('?' param_common ('&' param_common)* )?
    ;
rollup_get_jobs
    : ROLLUP_ '/' JOB ('/' job_ids)? '/'? ('?' param_common ('&' param_common)* )?
    ;
rollup_get_rollup_caps
    : ROLLUP_ '/' DATA ('/' ids_all)? '/'? ('?' param_common ('&' param_common)* )?
    ;
rollup_get_rollup_index_caps
    : (target '/')? ROLLUP_ '/' DATA '/'? ('?' param_common ('&' param_common)* )?
    ;
rollup_put_job
    : ROLLUP_ '/' JOB '/' job_id '/'? ('?' param_common ('&' param_common)* )?
    ;
rollup_rollup_search
    : target '/' ROLLUP_SEARCH_ '/'? ('?' rollup_rollup_search_params ('&' rollup_rollup_search_params)* )?
    ;
rollup_rollup_search_params
    : param_common
    | param_rest_total_hits_as_int
    | param_typed_keys
    ;
rollup_start_job
    : ROLLUP_ '/' JOB '/' job_id '/' START_ '/'? ('?' param_common ('&' param_common)* )?
    ;
rollup_stop_job
    : ROLLUP_ '/' JOB '/' job_id '/' STOP_ '/'? ('?' rollup_stop_job_params ('&' rollup_stop_job_params)* )?
    ;
rollup_stop_job_params
    : param_common
    | param_timeout
    | param_wait_for_completion
    ;
scripts_painless_execute
    : SCRIPTS_ '/' PAINLESS '/' EXECUTE '/'? ('?' param_common ('&' param_common)* )?
    ;
scroll
    : SEARCH_ '/' SCROLL ('/' scroll_id)? '/'? ('?' scroll_params ('&' scroll_params)* )?
    ;
scroll_params
    : param_common
    | param_scroll
    | param_scroll_id
    | param_rest_total_hits_as_int
    ;
search
    : (target '/')? SEARCH_ '/'? ('?' search_params ('&' search_params)* )?
    ;
search_params
    : param_common
    | param_allow_no_indices
    | param_allow_partial_search_results
    | param_analyzer
    | param_analyze_wildcard
    | param_batched_reduce_size
    | param_ccs_minimize_roundtrips
    | param_default_operator
    | param_df
    | param_docvalue_fields
    | param_expand_wildcards
    | param_explain
    | param_ignore_throttled
    | param_ignore_unavailable
    | param_lenient
    | param_max_concurrent_shard_requests
    | param_min_compatible_shard_node
    | param_preference
    | param_pre_filter_shard_size
    | param_request_cache
    | param_routing
    | param_scroll
    | param_search_type
    | param_stats
    | param_stored_fields
    | param_suggest_field
    | param_suggest_mode
    | param_suggest_size
    | param_suggest_text
    | param_terminate_after
    | param_timeout
    | param_track_total_hits
    | param_track_scores
    | param_typed_keys
    | param_rest_total_hits_as_int
    | param_version
    | param_source
    | param_source_excludes
    | param_source_includes
    | param_seq_no_primary_term
    | param_q
    | param_size
    | param_from
    | param_sort
    ;
search_application_delete
    : APPLICATION_ '/' SEARCH_APPLICATION '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
search_application_delete_behavioral_analytics
    : APPLICATION_ '/' ANALYTICS '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
search_application_get
    : APPLICATION_ '/' SEARCH_APPLICATION '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
search_application_get_behavioral_analytics
    : APPLICATION_ '/' ANALYTICS ('/' regular_id)? '/'? ('?' param_common ('&' param_common)* )?
    ;
search_application_list
    : APPLICATION_ '/' SEARCH_APPLICATION '/'? ('?' search_application_list_params ('&' search_application_list_params)* )?
    ;
search_application_list_params
    : param_common
    | param_size
    | param_from
    | param_q
    ;
search_application_post_behavioral_analytics_event
    : APPLICATION_ '/' ANALYTICS '/' regular_id '/' EVENT '/' regular_id
    ;
search_application_put
    : APPLICATION_ '/' SEARCH_APPLICATION '/' regular_id '/'? ('?' search_application_put_params ('&' search_application_put_params)* )?
    ;
search_application_put_params
    : param_common
    | param_create
    ;
search_application_put_behavioral_analytics
    : APPLICATION_ '/' ANALYTICS '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
search_application_render_query
    : APPLICATION_ '/' SEARCH_APPLICATION '/' regular_id '/' RENDER_QUERY_
    ;
search_application_search
    : APPLICATION_ '/' SEARCH_APPLICATION '/' regular_id '/' SEARCH_ '/'? ('?' param_common ('&' param_common)* )?
    ;
search_mvt
    : target '/' MVT_ '/' field_name '/' path_zoom '/' path_x '/' path_y '/'? ('?' search_mvt_params ('&' search_mvt_params)* )?
    ;
path_zoom
    : INTEGER
    ;
path_x
    : INTEGER
    ;
path_y
    : INTEGER
    ;
search_mvt_params
    : param_common
    | param_exact_bounds
    | param_extent
    | param_grid_agg
    | param_grid_precision
    | param_grid_type
    | param_size
    | param_with_labels
    ;
search_shards
    : (target '/')? SEARCH_SHARDS_ '/'? ('?' search_shards_params ('&' search_shards_params)* )?
    ;
search_shards_params
    : param_common
    | param_allow_no_indices
    | param_expand_wildcards
    | param_ignore_unavailable
    | param_local
    | param_preference
    | param_routing
    ;
search_template
    : (target '/')? SEARCH_ '/' TEMPLATE '/'? ('?' search_template_params ('&' search_template_params)* )?
    ;
search_template_params
    : param_common
    | param_allow_no_indices
    | param_ccs_minimize_roundtrips
    | param_expand_wildcards
    | param_explain
    | param_ignore_throttled
    | param_ignore_unavailable
    | param_preference
    | param_profile
    | param_routing
    | param_scroll
    | param_search_type
    | param_rest_total_hits_as_int
    | param_typed_keys
    ;
searchable_snapshots_cache_stats
    : SEARCHABLE_SNAPSHOTS_ ('/' node_filters)? '/' CACHE '/' STATS '/'? ('?' searchable_snapshots_cache_stats_params ('&' searchable_snapshots_cache_stats_params)* )?
    ;
searchable_snapshots_cache_stats_params
    : param_common
    | param_master_timeout
    ;
searchable_snapshots_clear_cache
    : (target '/')? SEARCHABLE_SNAPSHOTS_ '/' CACHE '/' CLEAR '/'? ('?' searchable_snapshots_clear_cache_params ('&' searchable_snapshots_clear_cache_params)* )?
    ;
searchable_snapshots_clear_cache_params
    : param_common
    | param_expand_wildcards
    | param_allow_no_indices
    | param_ignore_unavailable
    ;
searchable_snapshots_mount
    : SEARCHABLE_SNAPSHOTS_ '/' regular_id '/' regular_id '/' MOUNT_ '/'? ('?' searchable_snapshots_mount_params ('&' searchable_snapshots_mount_params)* )?
    ;
searchable_snapshots_mount_params
    : param_common
    | param_master_timeout
    | param_wait_for_completion
    | param_storage
    ;
searchable_snapshots_stats
    : (target '/')? SEARCHABLE_SNAPSHOTS_ '/' STATS '/'? ('?' searchable_snapshots_stats_params ('&' searchable_snapshots_stats_params)* )?
    ;
searchable_snapshots_stats_params
    : param_common
    | param_level
    ;
security_activate_user_profile
    : SECURITY_ '/' PROFILE '/' ACTIVATE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
security_authenticate
    : SECURITY_ '/' AUTHENTICATE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
security_bulk_update_api_keys
    : SECURITY_ '/' API_KEY '/' BULK_UPDATE_
    ;
security_change_password
    : SECURITY_ '/' USER ('/' regular_id)? '/' PASSWORD_ '/'? ('?' security_change_password_params ('&' security_change_password_params)* )?
    ;
security_change_password_params
    : param_common
    | param_refresh
    ;
security_clear_api_key_cache
    : SECURITY_ '/' API_KEY '/' ids_all '/' CLEAR_CACHE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
security_clear_cached_privileges
    : SECURITY_ '/' PRIVILEGE '/' ids_all '/' CLEAR_CACHE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
security_clear_cached_realms
    : SECURITY_ '/' REALM '/' ids_all '/' CLEAR_CACHE_ '/'? ('?' security_clear_cached_realms_params ('&' security_clear_cached_realms_params)* )?
    ;
security_clear_cached_realms_params
    : param_common
    | param_usernames
    ;
security_clear_cached_roles
    : SECURITY_ '/' ROLE '/' ids_all '/' CLEAR_CACHE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
security_clear_cached_service_tokens
    : SECURITY_ '/' SERVICE '/' regular_id '/' regular_id '/' CREDENTIAL '/' TOKEN '/' ids_all '/' CLEAR_CACHE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
security_create_api_key
    : SECURITY_ '/' API_KEY '/'? ('?' security_create_api_key_params ('&' security_create_api_key_params)* )?
    ;
security_create_api_key_params
    : param_common
    | param_refresh
    ;
security_create_cross_cluster_api_key
    : SECURITY_ '/' CROSS_CLUSTER '/' API_KEY
    ;
security_create_service_token
    : SECURITY_ '/' SERVICE '/' regular_id '/' regular_id '/' CREDENTIAL '/' TOKEN ('/' regular_id)? '/'? ('?' security_create_api_key_params ('&' security_create_api_key_params)* )?
    ;
security_delete_privileges
    : SECURITY_ '/' PRIVILEGE '/' regular_id '/' regular_id '/'? ('?' security_create_api_key_params ('&' security_create_api_key_params)* )?
    ;
security_delete_role
    : SECURITY_ '/' ROLE '/' regular_id '/'? ('?' security_create_api_key_params ('&' security_create_api_key_params)* )?
    ;
security_delete_role_mapping
    : SECURITY_ '/' ROLE_MAPPING '/' regular_id '/'? ('?' security_create_api_key_params ('&' security_create_api_key_params)* )?
    ;
security_delete_service_token
    : SECURITY_ '/' SERVICE '/' regular_id '/' regular_id '/' CREDENTIAL '/' TOKEN '/' regular_id '/'? ('?' security_create_api_key_params ('&' security_create_api_key_params)* )?
    ;
security_delete_user
    : SECURITY_ '/' USER '/' regular_id '/'? ('?' security_create_api_key_params ('&' security_create_api_key_params)* )?
    ;
security_disable_user
    : SECURITY_ '/' USER '/' regular_id '/' DISABLE_ '/'? ('?' security_create_api_key_params ('&' security_create_api_key_params)* )?
    ;
security_disable_user_profile
    : SECURITY_ '/' PROFILE '/' regular_id '/' DISABLE_ '/'? ('?' security_create_api_key_params ('&' security_create_api_key_params)* )?
    ;
security_enable_user
    : SECURITY_ '/' USER '/' regular_id '/' ENABLE_ '/'? ('?' security_create_api_key_params ('&' security_create_api_key_params)* )?
    ;
security_enable_user_profile
    : SECURITY_ '/' PROFILE '/' regular_id '/' ENABLE_ '/'? ('?' security_create_api_key_params ('&' security_create_api_key_params)* )?
    ;
security_enroll_kibana
    : SECURITY_ '/' ENROLL '/' KIBANA '/'? ('?' param_common ('&' param_common)* )?
    ;
security_enroll_node
    : SECURITY_ '/' ENROLL '/' NODE '/'? ('?' param_common ('&' param_common)* )?
    ;
security_get_api_key
    : SECURITY_ '/' API_KEY '/'? ('?' security_get_api_key_params ('&' security_get_api_key_params)* )?
    ;
security_get_api_key_params
    : param_common
    | param_master_timeout
    | param_id
    | param_name
    | param_owner
    | param_realm_name
    | param_username
    | param_with_limited_by
    | param_active_only
    ;
security_get_builtin_privileges
    : SECURITY_ '/' PRIVILEGE '/' BUILTIN_ '/'? ('?' param_common ('&' param_common)* )?
    ;
security_get_privileges
    : SECURITY_ '/' PRIVILEGE ('/' regular_id ('/' regular_id)?)? '/'? ('?' param_common ('&' param_common)* )?
    ;
security_get_role
    : SECURITY_ '/' ROLE ('/' regular_id)? '/'? ('?' param_common ('&' param_common)* )?
    ;
security_get_role_mapping
    : SECURITY_ '/' ROLE_MAPPING ('/' regular_id)? '/'? ('?' param_common ('&' param_common)* )?
    ;
security_get_service_accounts
    : SECURITY_ '/' SERVICE ('/' regular_id ('/' regular_id)?)? '/'? ('?' param_common ('&' param_common)* )?
    ;
security_get_service_credentials
    : SECURITY_ '/' SERVICE '/' regular_id '/' regular_id '/' CREDENTIAL '/'? ('?' param_common ('&' param_common)* )?
    ;
security_get_settings
    : SECURITY_ '/' SETTINGS
    ;
security_get_token
    : SECURITY_ '/' OAUTH2 '/' TOKEN '/'? ('?' param_common ('&' param_common)* )?
    ;
security_get_user
    : SECURITY_ '/' USER ('/' regular_id)? '/'? ('?' security_get_user_params ('&' security_get_user_params)* )?
    ;
security_get_user_params
    : param_common
    | param_with_profile_uid
    ;
security_get_user_privileges
    : SECURITY_ '/' USER '/' PRIVILEGES_ '/'? ('?' security_get_user_privileges_params ('&' security_get_user_privileges_params)* )?
    ;
security_get_user_privileges_params
    : param_common
    | param_application
    | param_priviledge
    | param_username
    ;
security_get_user_profile
    : SECURITY_ '/' PROFILE '/' regular_id '/'? ('?' security_get_user_profile_params ('&' security_get_user_profile_params)* )?
    ;
security_get_user_profile_params
    : param_common
    | param_data
    ;
security_grant_api_key
    : SECURITY_ '/' API_KEY '/' GRANT '/'? ('?' param_common ('&' param_common)* )?
    ;
security_has_privileges
    : SECURITY_ '/' USER '/' HAS_PRIVILEGES_ '/'? ('?' param_common ('&' param_common)* )?
    ;
security_has_privileges_user_profile
    : SECURITY_ '/' PROFILE '/' HAS_PRIVILEGES_ '/'? ('?' param_common ('&' param_common)* )?
    ;
security_invalidate_api_key
    : SECURITY_ '/' API_KEY '/'? ('?' param_common ('&' param_common)* )?
    ;
security_invalidate_token
    : SECURITY_ '/' OAUTH2 '/' TOKEN '/'? ('?' param_common ('&' param_common)* )?
    ;
security_oidc_authenticate
    : SECURITY_ '/' OIDC '/' AUTHENTICATE
    ;
security_oidc_logout
    : SECURITY_ '/' OIDC '/' LOGOUT
    ;
security_oidc_prepare_authentication
    : SECURITY_ '/' OIDC '/' PREPARE
    ;
security_put_privileges
    : SECURITY_ '/' PRIVILEGE '/'? ('?' security_put_privileges_params ('&' security_put_privileges_params)* )?
    ;
security_put_privileges_params
    : param_common
    | param_refresh
    ;
security_role
    : SECURITY_ '/' ROLE '/'? ('?' security_put_privileges_params ('&' security_put_privileges_params)* )?
    ;

security_put_role
    : SECURITY_ '/' ROLE '/' regular_id '/'? ('?' security_put_privileges_params ('&' security_put_privileges_params)* )?
    ;
security_put_role_mapping
    : SECURITY_ '/' ROLE_MAPPING '/' regular_id '/'? ('?' security_put_privileges_params ('&' security_put_privileges_params)* )?
    ;
security_put_user
    : SECURITY_ '/' USER '/' regular_id '/'? ('?' security_put_privileges_params ('&' security_put_privileges_params)* )?
    ;
security_query_api_keys
    : SECURITY_ '/' QUERY_ '/' API_KEY '/'? ('?' security_query_api_keys_params ('&' security_query_api_keys_params)* )?
    ;
security_query_api_keys_params
    : param_common
    | param_with_limited_by
    ;
security_saml_authenticate
    : SECURITY_ '/' SAML '/' AUTHENTICATE '/'? ('?' param_common ('&' param_common)* )?
    ;
security_saml_complete_logout
    : SECURITY_ '/' SAML '/' COMPLETE_LOGOUT '/'? ('?' param_common ('&' param_common)* )?
    ;
security_saml_invalidate
    : SECURITY_ '/' SAML '/' INVALIDATE '/'? ('?' param_common ('&' param_common)* )?
    ;
security_saml_logout
    : SECURITY_ '/' SAML '/' LOGOUT '/'? ('?' param_common ('&' param_common)* )?
    ;
security_saml_prepare_authentication
    : SECURITY_ '/' SAML '/' PREPARE '/'? ('?' param_common ('&' param_common)* )?
    ;
security_saml_service_provider_metadata
    : SECURITY_ '/' SAML '/' METADATA '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
security_suggest_user_profiles
    : SECURITY_ '/' PROFILE '/' SUGGEST_ '/'? ('?' security_suggest_user_profiles_params ('&' security_suggest_user_profiles_params)* )?
    ;
security_suggest_user_profiles_params
    : param_common
    | param_data
    ;
security_update_api_key
    : SECURITY_ '/' API_KEY '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
security_update_cross_cluster_api_key
    : SECURITY_ '/' CROSS_CLUSTER '/' API_KEY '/' regular_id
    ;
security_update_settings
    : SECURITY_ '/' SETTINGS
    ;
security_update_user_profile_data
    : SECURITY_ '/' PROFILE '/' regular_id '/' DATA_ '/'? ('?' security_update_user_profile_data_params ('&' security_update_user_profile_data_params)* )?
    ;
security_update_user_profile_data_params
    : param_common
    | param_if_seq_no
    | param_if_primary_term
    | param_refresh
    ;
shutdown_delete_node
    : NODES_ '/' node_filter '/' SHUTDOWN '/'? ('?' shutdown_node_params ('&' shutdown_node_params)* )?
    ;
shutdown_node_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
shutdown_get_node
    : NODES_ ('/' node_filter)? '/' SHUTDOWN '/'? ('?' shutdown_node_params ('&' shutdown_node_params)* )?
    ;
shutdown_put_node
    : NODES_ '/' node_filter '/' SHUTDOWN '/'? ('?' shutdown_node_params ('&' shutdown_node_params)* )?
    ;
slm_delete_lifecycle
    : SLM_ '/' POLICY '/' policy_id '/'? ('?' param_common ('&' param_common)* )?
    ;
slm_execute_lifecycle
    : SLM_ '/' POLICY '/' policy_id '/' EXECUTE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
slm_execute_retention
    : SLM_ '/' EXECUTE_RETENTION_ '/'? ('?' param_common ('&' param_common)* )?
    ;
slm_get_lifecycle
    : SLM_ '/' POLICY ('/' policy_id)? '/'? ('?' param_common ('&' param_common)* )?
    ;
slm_get_stats
    : SLM_ '/' STATS '/'? ('?' param_common ('&' param_common)* )?
    ;
slm_get_status
    : SLM_ '/' STATUS '/'? ('?' param_common ('&' param_common)* )?
    ;
slm_put_lifecycle
    : SLM_ '/' POLICY '/' policy_id '/'? ('?' slm_put_lifecycle_params ('&' slm_put_lifecycle_params)* )?
    ;
slm_put_lifecycle_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
slm_start
    : SLM_ '/' START '/'? ('?' param_common ('&' param_common)* )?
    ;
slm_stop
    : SLM_ '/' STOP '/'? ('?' param_common ('&' param_common)* )?
    ;
snapshot_cleanup_repository
    : SNAPSHOT_ '/' regular_id '/' CLEANUP_ '/'? ('?' snapshot_cleanup_repository_params ('&' snapshot_cleanup_repository_params)* )?
    ;
snapshot_cleanup_repository_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
snapshot_clone
    : SNAPSHOT_ '/' regular_id '/' regular_id '/' CLONE_ '/' regular_id '/'? ('?' snapshot_clone_params ('&' snapshot_clone_params)* )?
    ;
snapshot_clone_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
snapshot_create
    : SNAPSHOT_ '/' regular_id '/' regular_id '/' MOUNT_ '/'? ('?' snapshot_create_params ('&' snapshot_create_params)* )?
    ;
snapshot_create_params
    : param_common
    | param_master_timeout
    | param_wait_for_completion
    ;
snapshot_create_repository
    : SNAPSHOT_ '/' regular_id '/'? ('?' snapshot_create_repository_params ('&' snapshot_create_repository_params)* )?
    ;
snapshot_create_repository_params
    : param_common
    | param_master_timeout
    | param_timeout
    | param_verify
    ;
snapshot_delete
    : SNAPSHOT_ '/' regular_id '/' ids '/'? ('?' snapshot_delete_params ('&' snapshot_delete_params)* )?
    ;
snapshot_delete_params
    : param_common
    | param_master_timeout
    ;
snapshot_delete_repositor
    : SNAPSHOT_ '/' regular_id '/'? ('?' snapshot_delete_repositor_params ('&' snapshot_delete_repositor_params)* )?
    ;
snapshot_delete_repositor_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
snapshot_get
    : SNAPSHOT_ '/' ids_all '/' ids_all '/'? ('?' snapshot_get_params ('&' snapshot_get_params)* )?
    ;
snapshot_get_params
    : param_common
    | param_master_timeout
    | param_ignore_unavailable
    | param_verbose
    | param_index_details
    | param_index_names
    | param_include_repository
    | param_sort
    | param_size
    | param_order
    | param_after
    | param_offset
    | param_from_sort_value
    | param_slm_policy_filter
    ;
snapshot_get_repository
    : SNAPSHOT_ ('/' ids_all)? '/'? ('?' snapshot_get_repository_params ('&' snapshot_get_repository_params)* )?
    ;
snapshot_get_repository_params
    : param_common
    | param_master_timeout
    | param_local
    ;
snapshot_repository_analyze
    : SNAPSHOT_ '/' regular_id '/' ANALYZE_ '/'? ('?' snapshot_repository_analyze_params ('&' snapshot_repository_analyze_params)* )?
    ;
snapshot_repository_analyze_params
    : param_common
    | param_timeout
    | param_blob_count
    | max_blob_size
    ;
param_blob_count
    : BLOB_COUNT '=' number_value
    ;
max_blob_size
    : MAX_BLOB_SIZE '=' number_value byte_size_units
    ;

snapshot_restore
    : SNAPSHOT_ '/' regular_id '/' regular_id '/' RESTORE_ '/'? ('?' snapshot_restore_params ('&' snapshot_restore_params)* )?
    ;
snapshot_restore_params
    : param_common
    | param_master_timeout
    | param_wait_for_completion
    ;
snapshot_status
    : SNAPSHOT_ ('/' regular_id ('/' ids_all)?)? '/' STATUS_ '/'? ('?' snapshot_status_params ('&' snapshot_status_params)* )?
    ;
snapshot_status_params
    : param_common
    | param_master_timeout
    | param_ignore_unavailable
    ;
snapshot_verify_repository
    : SNAPSHOT_ '/' regular_id '/' VERIFY_ '/'? ('?' snapshot_verify_repository_params ('&' snapshot_verify_repository_params)* )?
    ;
snapshot_verify_repository_params
    : param_common
    | param_master_timeout
    | param_timeout
    ;
sql_clear_cursor
    : SQL_ '/' CLOSE '/'? ('?' param_common ('&' param_common)* )?
    ;
sql_delete_async
    : SQL_ '/' ASYNC '/' DELETE '/' search_id '/'? ('?' param_common ('&' param_common)* )?
    ;
sql_get_async
    : SQL_ '/' ASYNC '/' search_id '/'? ('?' sql_get_async_params ('&' sql_get_async_params)* )?
    ;
sql_get_async_params
    : param_common
    | param_master_timeout
    | param_delimiter
    | param_format
    | param_keep_alive
    | param_wait_for_completion_timeout
    ;
sql_get_async_status
    : SQL_ '/' ASYNC '/' STATUS '/' search_id '/'? ('?' sql_query_params ('&' sql_query_params)* )?
    ;
sql_query
    : SQL_ '/'? ('?' sql_query_params ('&' sql_query_params)* )?
    ;
sql_query_params
    : param_common
    | param_format
    ;
sql_translate
    : SQL_ '/' TRANSLATE '/'? ('?' param_common ('&' param_common)* )?
    ;
ssl_certificates
    : SSL_ '/' CERTIFICATES '/'? ('?' param_common ('&' param_common)* )?
    ;
synonyms_delete_synonym
    : SYNONYMS_ '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
synonyms_delete_synonym_rule
    : SYNONYMS_ '/' regular_id '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
synonyms_get_synonym
    : SYNONYMS_ '/' regular_id '/'? ('?' synonyms_get_synonym_params ('&' synonyms_get_synonym_params)* )?
    ;
synonyms_get_synonym_params
    : param_common
    | param_master_timeout
    | param_size
    | param_from
    ;
synonyms_get_synonym_rule
    : SYNONYMS_ '/' regular_id '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
synonyms_get_synonyms_sets
    : SYNONYMS_ '/'? ('?' synonyms_get_synonyms_sets_params ('&' synonyms_get_synonyms_sets_params)* )?
    ;
synonyms_get_synonyms_sets_params
    : param_common
    | param_master_timeout
    | param_size
    | param_from
    ;
synonyms_put_synonym
    : SYNONYMS_ '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
synonyms_put_synonym_rule
    : SYNONYMS_ '/' regular_id '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
tasks_cancel
    : TASKS_ ('/' task_id)? '/' CANCEL_ '/'? ('?' tasks_cancel_params ('&' tasks_cancel_params)* )?
    ;
tasks_cancel_params
    : param_common
    | param_actions
    | param_nodes
    | param_parent_task_id
    | param_wait_for_completion
    ;
tasks_get
    : TASKS_ '/' task_id '/'? ('?' tasks_get_params ('&' tasks_get_params)* )?
    ;
tasks_get_params
    : param_common
    | param_timeout
    | param_wait_for_completion
    ;
tasks_list
    : TASKS_ '/'? ('?' tasks_list_params ('&' tasks_list_params)* )?
    ;
tasks_list_params
    : param_common
    | param_actions
    | param_detailed
    | param_group_by
    | param_nodes
    | param_parent_task_id
    | param_master_timeout
    | param_timeout
    | param_wait_for_completion
    ;
terms_enum
    : target '/' TERMS_ENUM_ '/'? ('?' param_common ('&' param_common)* )?
    ;
termvectors
    : index_name '/' TERMVECTORS_ ('/' regular_id)? '/'? ('?' termvectors_params ('&' termvectors_params)* )?
    ;
termvectors_params
    : param_common
    | param_fields
    | param_field_statistics
    | param_offsets
    | param_payloads
    | param_positions
    | param_preference
    | param_realtime
    | param_routing
    | param_term_statistics
    | param_version
    | param_version_type
    ;
text_structure_find_structure
    : TEXT_STRUCTURE_ '/' FIND_STRUCTURE '/'? ('?' text_structure_find_structure_params ('&' text_structure_find_structure_params)* )?
    ;
text_structure_find_structure_params
    : param_common
    | param_charset
    | param_column_names
    | param_delimiter
    | param_ecs_compatibility
    | param_explain
    | param_format
    | param_grok_pattern
    | param_has_header_row
    | param_line_merge_size_limit
    | param_lines_to_sample
    | param_quote
    | param_should_trim_fields
    | param_timeout
    | param_timestamp_field
    | param_timestamp_format
    ;
text_structure_test_grok_pattern
    : TEXT_STRUCTURE_ '/' TEST_GROK_PATTERN '/'? ('?' text_structure_test_grok_pattern_params ('&' text_structure_test_grok_pattern_params)* )?
    ;
text_structure_test_grok_pattern_params
    : param_common
    | param_ecs_compatibility
    ;
transform_delete_transform
    : TRANSFORM_ ('/' regular_id)? '/'? ('?' transform_delete_transform_params ('&' transform_delete_transform_params)* )?
    ;
transform_delete_transform_params
    : param_common
    | param_force
    | param_delete_dest_index
    | param_timeout
    ;
transform_get_transform
    : TRANSFORM_ ('/' transform_ids)? '/'? ('?' transform_get_transform_params ('&' transform_get_transform_params)* )?
    ;
transform_get_transform_params
    : param_common
    | param_allow_no_match
    | param_from
    | param_size
    | param_exclude_generated
    ;
transform_get_transform_stats
    : TRANSFORM_ ('/' transform_ids)? '/' STATS_ '/'? ('?' transform_get_transform_stats_params ('&' transform_get_transform_stats_params)* )?
    ;
transform_get_transform_stats_params
    : param_common
    | param_allow_no_match
    | param_from
    | param_size
    | param_timeout
    ;
transform_preview_transform
    : TRANSFORM_ ('/' regular_id)? '/' PREVIEW_ '/'? ('?' transform_preview_transform_params ('&' transform_preview_transform_params)* )?
    ;
transform_preview_transform_params
    : param_common
    | param_timeout
    ;
transform_put_transform
    : TRANSFORM_ '/' regular_id '/'? ('?' transform_put_transform_params ('&' transform_put_transform_params)* )?
    ;
transform_put_transform_params
    : param_common
    | param_timeout
    | param_defer_validation
    ;
transform_reset_transform
    : TRANSFORM_ '/' regular_id '/' RESET_ '/'? ('?' transform_reset_transform_params ('&' transform_reset_transform_params)* )?
    ;
transform_reset_transform_params
    : param_common
    | param_force
    | param_timeout
    ;
transform_schedule_now_transform
    : TRANSFORM_ '/' regular_id '/' SCHEDULE_NOW_ '/'? ('?' transform_schedule_now_transform_params ('&' transform_schedule_now_transform_params)* )?
    ;
transform_schedule_now_transform_params
    : param_common
    | param_timeout
    ;
transform_start_transform
    : TRANSFORM_ '/' regular_id '/' START_ '/'? ('?' transform_start_transform_params ('&' transform_start_transform_params)* )?
    ;
transform_start_transform_params
    : param_common
    | param_timeout
    | param_from
    ;
transform_stop_transform
    : TRANSFORM_ '/' regular_id '/' STOP_ '/'? ('?' transform_stop_transform_params ('&' transform_stop_transform_params)* )?
    ;
transform_stop_transform_params
    : param_common
    | param_allow_no_match
    | param_force
    | param_timeout
    | param_wait_for_checkpoint
    | param_wait_for_completion
    ;
transform_update_transform
    : TRANSFORM_ '/' regular_id '/' UPDATE_ '/'? ('?' transform_update_transform_params ('&' transform_update_transform_params)* )?
    ;
transform_update_transform_params
    : param_common
    | param_timeout
    | param_defer_validation
    ;
transform_upgrade_transforms
    : TRANSFORM_ '/' UPGRADE_ '/'? ('?' transform_upgrade_transforms_params ('&' transform_upgrade_transforms_params)* )?
    ;
transform_upgrade_transforms_params
    : param_common
    | param_dry_run
    | param_timeout
    ;
update
    : index_name '/' UPDATE_ '/' regular_id '/'? ('?' update_params ('&' update_params)* )?
    ;
update_params
    : param_common
    | param_if_primary_term
    | param_if_seq_no
    | param_lang
    | param_refresh
    | param_require_alias
    | param_retry_on_conflict
    | param_routing
    | param_timeout
    | param_wait_for_active_shards
    | param_source
    | param_source_excludes
    | param_source_includes
    ;
update_by_query
    : index_name '/' UPDATE_BY_QUERY_ '/'? ('?' update_by_query_params ('&' update_by_query_params)* )?
    ;
update_by_query_params
    : param_common
    | param_allow_no_indices
    | param_analyzer
    | param_analyze_wildcard
    | param_conflicts
    | param_default_operator
    | param_df
    | param_expand_wildcards
    | param_from
    | param_ignore_unavailable
    | param_lenient
    | param_max_docs
    | param_pipeline
    | param_preference
    | param_refresh
    | param_request_cache
    | param_requests_per_second
    | param_routing
    | param_scroll
    | param_scroll_size
    | param_search_timeout
    | param_search_type
    | param_slices
    | param_sort
    | param_stats
    | param_terminate_after
    | param_timeout
    | param_version
    | param_version_type
    | param_wait_for_active_shards
    | param_wait_for_completion
    ;
update_by_query_rethrottle
    : UPDATE_BY_QUERY_ '/' task_id '/' RETHROTTLE_ '/'? ('?' update_by_query_rethrottle_params ('&' update_by_query_rethrottle_params)* )?
    ;
update_by_query_rethrottle_params
    : param_common
    | param_requests_per_second
    ;
watcher_ack_watch
    : WATCHER_ '/' WATCH '/' regular_id '/' ACK_ ('/' ids)? '/'? ('?' param_common ('&' param_common)* )?
    ;
watcher_activate_watch
    : WATCHER_ '/' WATCH '/' regular_id '/' ACTIVATE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
watcher_deactivate_watch
    : WATCHER_ '/' WATCH '/' regular_id '/' DEACTIVATE_ '/'? ('?' param_common ('&' param_common)* )?
    ;
watcher_delete_watch
    : WATCHER_ '/' WATCH '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
watcher_execute_watch
    : WATCHER_ '/' WATCH ('/' regular_id)? '/' EXECUTE_ '/'? ('?' watcher_execute_watch_params ('&' watcher_execute_watch_params)* )?
    ;
watcher_execute_watch_params
    : param_common
    | param_debug
    ;
watcher_get_settings
    : WATCHER_ '/' SETTINGS
    ;
watcher_get_watch
    : WATCHER_ '/' WATCH '/' regular_id '/'? ('?' param_common ('&' param_common)* )?
    ;
watcher_put_watch
    : WATCHER_ '/' WATCH '/' regular_id '/'? ('?' watcher_put_watch_params ('&' watcher_put_watch_params)* )?
    ;
watcher_put_watch_params
    : param_common
    | param_active
    | param_if_primary_term
    | param_if_seq_no
    | param_version
    ;
watcher_query_watches
    : WATCHER_ '/' QUERY_ '/' WATCHES '/'? ('?' param_common ('&' param_common)* )?
    ;
watcher_start
    : WATCHER_ '/' START_ '/'? ('?' param_common ('&' param_common)* )?
    ;
watcher_stats
    : WATCHER_ '/' STATS ('/' watcher_stats_metric)? '/'? ('?' watcher_stats_params ('&' watcher_stats_params)* )?
    ;
watcher_stats_metric
    : ALL_ | QUEUED_WATCHES | CURRENT_WATCHES | PENDING_WATCHES
    ;
watcher_stats_params
    : param_common
    | param_emit_stacktraces
    | param_watcher_stats_metric
    ;
watcher_stop
    : WATCHER_ '/' STOP_ '/'? ('?' param_common ('&' param_common)* )?
    ;
watcher_update_settings
    : WATCHER_ '/' SETTINGS '/'? ('?' param_common ('&' param_common)* )?
    ;
xpack_info
    : XPACK_ '/'? ('?' xpack_info_params ('&' xpack_info_params)* )?
    ;
xpack_info_params
    : param_common
    | param_categories
    | param_accept_enterprise
    ;
xpack_usage
    : XPACK_ '/' USAGE '/'? ('?' xpack_usage_params ('&' xpack_usage_params)* )?
    ;
xpack_usage_params
    : param_common
    | param_master_timeout
    ;



param_categories
    : CATEGORIES '=' ids
    ;
param_emit_stacktraces
    : EMBED_STACKTRACES ('=' boolean_value)?
    ;
param_watcher_stats_metric
    : METRIC '=' watcher_stats_metric
    ;
param_active
    : ACTIVE ('=' boolean_value)?
    ;
param_debug
    : DEBUG ('=' boolean_value)?
    ;
param_max_docs
    : MAX_DOCS '=' INTEGER
    ;
param_retry_on_conflict
    : RETRY_ON_CONFLICT '=' INTEGER
    ;
param_lang
    : LANG '=' regular_id
    ;
param_wait_for_checkpoint
    : WAIT_FOR_CHECKPOINT ('=' boolean_value)?
    ;
param_defer_validation
    : DEFER_VALIDATION ('=' boolean_value)?
    ;
param_delete_dest_index
    : DELETE_DEST_INDEX ('=' boolean_value)?
    ;
param_timestamp_format
    : TIMESTAMP_FORMAT '=' regular_id
    ;
param_timestamp_field
    : TIMESTAMP_FIELD '=' regular_id
    ;
param_should_trim_fields
    : SHOULD_TRIM_FIELDS ('=' boolean_value)?
    ;
param_quote
    : QUOTE '=' regular_id
    ;
param_lines_to_sample
    : LINES_TO_SAMPLE '=' INTEGER
    ;
param_line_merge_size_limit
    : LINE_MERGE_SIZE_LIMIT '=' INTEGER
    ;
param_has_header_row
    : HAS_HEADER_ROW ('=' boolean_value)?
    ;
param_grok_pattern
    : GROK_PATTERN '=' regular_id
    ;
param_ecs_compatibility
    : ECS_COMPATIBILITY '=' regular_id
    ;
param_column_names
    : COLUMN_NAMES '=' ids
    ;
param_charset
    : CHARSET '=' regular_id
    ;
param_group_by
    : GROUP_BY '=' (NODES | PARENTS | NONE)
    ;
param_delimiter
    : DELIMITER '=' regular_id
    ;
param_slm_policy_filter
    : SLM_POLICY_FILTER '=' ids
    ;
param_from_sort_value
    : FROM_SORT_VALUE '=' regular_id
    ;
param_offset
    : OFFSET '=' INTEGER
    ;
param_after
    : AFTER '=' regular_id
    ;
param_include_repository
    : INCLUDE_REPOSITORY ('=' boolean_value)?
    ;
param_index_names
    : INDEX_NAMES ('=' boolean_value)?
    ;
param_index_details
    : INDEX_DETAILS ('=' boolean_value)?
    ;
param_verify
    : VERIFY ('=' boolean_value)?
    ;
param_data
    : DATA '=' ids
    ;
param_priviledge
    : PRIVILEGE '=' regular_id
    ;
param_application
    : APPLICATION '=' regular_id
    ;
param_with_profile_uid
    : WITH_PROFILE_UID ('=' boolean_value)?
    ;
param_with_limited_by
    : WITH_LIMITED_BY ('=' boolean_value)?
    ;
param_username
    : USERNAME '=' regular_id
    ;
param_realm_name
    : REALM_NAME '=' regular_id
    ;
param_owner
    : OWNER ('=' boolean_value)?
    ;
param_name
    : NAME '=' regular_id
    ;
param_id
    : ID '=' regular_id
    ;
param_usernames
    : USERNAMES '=' ids
    ;
param_storage
    : STORAGE '=' (FULL_COPY | SHARED_CACHE)
    ;
param_profile
    : PROFILE ('=' boolean_value)?
    ;
param_with_labels
    : WITH_LABELS ('=' boolean_value)?
    ;
param_grid_type
    : GRID_TYPE '=' (GRID | POINT | CENTROID)
    ;
param_grid_precision
    : GRID_PRECISION '=' number_value
    ;
param_grid_agg
    : GRID_AGG '=' (GEOTILE | GEOHEX)
    ;
param_extent
    : EXTENT '=' number_value
    ;
param_exact_bounds
    : EXACT_BOUNDS ('=' boolean_value)?
    ;
param_scroll_id
    : SCROLL_ID '=' scroll_id
    ;
param_thread_sort
    : SORT '=' (CPU | WAIT | BLOCK | GPU | MEM)
    ;
param_thread_type
    : TYPE '=' (CPU | WAIT | BLOCK | GPU | MEM)
    ;
param_threads
    : THREADS '=' number_value
    ;
param_snapshots
    : SNAPSHOTS '=' number_value
    ;
param_ignore_idle_threads
    : IGNORE_IDLE_THREADS ('=' boolean_value)?
    ;
param_term_statistics
    : TERM_STATISTICS ('=' boolean_value)?
    ;
param_positions
    : POSITIONS ('=' boolean_value)?
    ;
param_payloads
    : PAYLOADS ('=' boolean_value)?
    ;
param_offsets
    : OFFSETS ('=' boolean_value)?
    ;
param_field_statistics
    : FIELD_STATISTICS ('=' boolean_value)?
    ;
param_ids
    : IDS '=' ids
    ;
param_interval
    : INTERVAL '=' time_value
    ;
param_system_api_version
    : SYSTEM_API_VERSION '=' regular_id
    ;
param_system_id
    : SYSTEM_ID '=' regular_id
    ;
param_wait_for
    : WAIT_FOR '=' (STARTED | STARTING | FULLY_ALLOCATED)
    ;
param_threads_per_allocation
    : THREADS_PER_ALLOCATION '=' number_value
    ;
param_queue_capacity
    : QUEUE_CAPACITY '=' number_value
    ;
param_priority
    : PRIORITY '=' (NORMAL | LOW)
    ;
param_number_of_allocations
    : NUMBER_OF_ALLOCATION '=' number_value
    ;
param_deployment_id
    : DEPLOYMENT_ID '=' regular_id
    ;
param_cache_size
    : CACHE_SIZE '=' byte_value
    ;
param_enabled
    : ENABLED ('=' boolean_value)?
    ;
param_delete_intervening_results
    : DELETE_INTERVENING_RESULTS ('=' boolean_value)?
    ;
param_reassign
    : REASSIGN ('=' boolean_value)?
    ;
param_defer_definition_decompression
    : DEFER_DEFINITION_DECOMPRESSION ('=' boolean_value)?
    ;
param_reset_start
    : RESET_START '=' regular_id
    ;
param_reset_end
    : RESET_END '=' regular_id
    ;
param_tags
    : TAGS '=' regular_id (',' regular_id)*
    ;
param_include
    : INCLUDE '=' include_value (',' include_value)
    ;
include_value
    : DEFINITION | FEATURE_IMPORTANCE_BASELINE | HYPERPARAMETERS
    | TOTAL_FEATURE_IMPORTANCE | DEFINITION_STATUS
    ;
param_decompress_definition
    : DECOMPRESS_DEFINITION ('=' boolean_value)?
    ;
param_record_score
    : RECORD_SCORE '=' number_value
    ;
param_top_n
    : TOP_N '=' number_value
    ;
param_overall_score
    : OVERALL_SCORE '=' number_value
    ;
param_bucket_span
    : BUCKET_SPAN '=' regular_id
    ;
param_influencer_score
    : INFLUENCER_SCORE '=' number_value
    ;
param_exclude_generated
    : EXCLUDE_GENERATED ('=' boolean_value)?
    ;
param_partition_field_value
    : PARTITION_FIELD_VALUE '=' regular_id
    ;
param_job_id
    : JOB_ID '=' regular_id
    ;
param_expand
    : EXPAND ('=' boolean_value)?
    ;
param_exclude_interim
    : EXCLUDE_INTERIM ('=' boolean_value)?
    ;
param_desc
    : DESC ('=' boolean_value)?
    ;
param_anomaly_score
    : ANOMALY_SCORE '=' number_value
    ;
param_max_model_memory
    : MAX_MODEL_MEMORY '=' byte_value
    ;
param_expires_in
    : EXPIRES_IN '=' time_value
    ;
param_duration
    : DURATION '=' time_value
    ;
param_start
    : START '=' regular_id
    ;
param_skip_time
    : SKIP_TIME '=' regular_id
    ;
param_end
    : END '=' regular_id
    ;
param_advance_time
    : ADVANCE_TIME '=' regular_id
    ;
param_calc_interim
    : CALC_INTERIM ('=' boolean_value)?
    ;
param_delete_user_annotations
    : DELETE_USER_ANNOTATIONS ('=' boolean_value)?
    ;
param_allow_no_forecasts
    : ALLOW_NO_FORECASTS ('=' boolean_value)?
    ;
param_type_query_string
    : TYPE_QUERY_STRING '=' regular_id
    ;
param_acknowledge
    : ACKNOWLEDGE ('=' boolean_value)?
    ;
param_accept_enterprise
    : ACCEPT_ENTERPRISE ('=' boolean_value)?
    ;
param_if_version
    : IF_VERSION '=' INTEGER
    ;
param_summary
    : SUMMARY ('=' boolean_value)?
    ;
param_rewrite
    : REWRITE ('=' boolean_value)?
    ;
param_all_shards
    : ALL_SHARDS ('=' boolean_value)?
    ;
param_include_segment_file_sizes
    : INCLUDE_SEGMENT_FILE_SIZES ('=' boolean_value)?
    ;
param_groups
    : GROUPS '=' ids_all
    ;
param_forbid_closed_indices
    : FORBID_CLOSED_INDICES ('=' boolean_value)?
    ;
param_fielddata_fields
    : FIELDDATA_FIELDS '=' fields
    ;
param_completion_fields
    : COMPLETION_FIELDS '=' fields
    ;
param_status
    : STATUS '=' status_value (',' status_value)*
    ;
status_value
    : GREEN | YELLOW | RED | ALL
    ;
param_order
    : ORDER '=' INTEGER | DESC | ASC
    ;
param_preserve_existing
    : PRESERVE_EXISTING ('=' boolean_value)?
    ;
param_write_index_only
    : WRITE_INDEX_ONLY ('=' boolean_value)?
    ;
param_features
    : FEATURES '=' features_value (',' features_value)*
    ;
features_value
    : ALIASES | MAPPINGS | SETTINGS
    ;
param_only_expunge_deletes
    : ONLY_EXPUNGE_DELETES ('=' boolean_value)?
    ;
param_max_num_segments
    : MAX_NUM_SEGMENTS '=' INTEGER
    ;
param_wait_if_ongoing
    : WAIT_IF_ONGOING ('=' boolean_value)?
    ;
param_force
    : FORCE ('=' boolean_value)?
    ;
param_run_expensive_tasks
    : RUN_EXPENSIVE_TASKS ('=' boolean_value)?
    ;
param_flush
    : FLUSH ('=' boolean_value)?
    ;
param_request
    : REQUEST ('=' boolean_value)?
    ;
param_query
    : QUERY ('=' boolean_value)?
    ;
param_fielddata
    : FIELDDATA ('=' boolean_value)?
    ;
param_only_managed
    : ONLY_MANAGED ('=' boolean_value)?
    ;
param_only_errors
    : ONLY_ERRORS ('=' boolean_value)?
    ;
param_verbose
    : VERBOSE ('=' boolean_value)?
    ;
param_seq_no_primary_term
    : SEQ_NO_PRIMARY_TERM ('=' boolean_value)?
    ;
param_max_concurrent_searches
    : MAX_CONCURRENT_SEARCHES '=' INTEGER
    ;
param_wait_for_checkpoints
    : WAIT_FOR_CHECKPOINTS '=' ids
    ;
param_checkpoints
    : CHECKPOINTS '=' ids
    ;
param_wait_for_index
    : WAIT_FOR_INDEX ('=' boolean_value)?
    ;
param_wait_for_advance
    : WAIT_FOR_ADVANCE ('=' boolean_value)?
    ;
param_filters
    : FILTERS '=' ('+' | '-') (METADATA | PARENT | NESTED | MULTIFIELD)
    ;
param_types
    : TYPES '=' regular_id (',' regular_id)?
    ;
param_include_unmapped
    : INCLUDE_UNMAPPED ('=' boolean_value)?
    ;
param_realtime
    : REALTIME ('=' boolean_value)?
    ;
param_wait_for_completion
    : WAIT_FOR_COMPLETION ('=' boolean_value)?
    ;
param_slices
    : SLICES '=' INTEGER | AUTO
    ;
param_search_timeout
    : SEARCH_TIMEOUT ('=' (time_value | boolean_value))?
    ;
param_scroll_size
    : SCROLL_SIZE '=' INTEGER
    ;
param_requests_per_second
    : REQUESTS_PER_SECOND '=' number_value
    ;
parma_max_docs
    : MAX_DOCS '=' INTEGER
    ;
param_conflicts
    : CONFLICTS '=' (ABORT | PROCEED)
    ;
param_accept_data_loss
    : ACCEPT_DATA_LOSS ('=' boolean_value)?
    ;
param_op_type
    : OP_TYPE '=' (INDEX | CREATE)
    ;
param_if_primary_term
    : IF_PRIMARY_TERM '=' INTEGER
    ;
param_if_seq_no
    : IF_SEQ_NO '=' INTEGER
    ;
param_version_type
    : VERSION_TYPE '=' (INTERNAL | EXTERNAL | EXTERNAL_GTE | FORCE)
    ;
param_min_score
    : MIN_SCORE '=' number_value
    ;
param_wait_for_metadata_version
    : WAIT_FOR_METADATA_VERSION '=' INTEGER
    ;
param_wait_for_timeout
    : WAIT_FOR_TIMEOUT '=' time_value
    ;
param_retry_failed
    : RETRY_FAILED ('=' boolean_value)?
    ;
param_metric
    : METRIC '=' (ALL | BLOCKS | MASTER_NODE | METADATA | NODES | NONE | ROUTING_TABLE | VERSION)
    ;
param_dry_run
    : DRY_RUN ('=' boolean_value)?
    ;
param_create
    : CREATE ('=' boolean_value)?
    ;
param_node_ids
    : NODE_IDS '=' node_filters
    ;
param_node_names
    : NODE_NAMES '=' node_name (',' node_name)*
    ;
param_wait_for_nodes
    : WAIT_FOR_NODES '=' INTEGER
    ;
//compare_number
//    : (compare_operator number_value)
//    | (COMPARE_W '(' number_value ')')
//    ;
//compare_operator
//    : (('>' | '<')? '=') | '<' | '>'
//    ;
param_wait_for_no_relocating_shards
    : WAIT_FOR_NO_RELOCATING_SHARDS ('=' boolean_value)?
    ;
param_wait_for_no_initializing_shards
    : WAIT_FOR_NO_INITIALIZING_SHARDS ('=' boolean_value)?
    ;
param_wait_for_events
    : WAIT_FOR_EVENTS '=' (IMMEDIATE | URGENT | HIGH | LOW | NORMAL | LANGUID)
    ;
param_level
    : LEVEL '=' (CLUSTER | INDICES | SHARDS)
    ;
param_wait_for_status
    : WAIT_FOR_STATUS '=' status_value
    ;
param_include_defaults
    : INCLUDE_DEFAULTS ('=' boolean_value)?
    ;
param_flat_settings
    : FLAT_SETTINGS ('=' boolean_value)?
    ;
param_wait_for_removal
    : WAIT_FOR_REMOVAL ('=' boolean_value)?
    ;
param_include_yes_decisions
    : INCLUDE_YES_DECISIONS ('=' boolean_value)?
    ;
param_include_disk_info
    : INCLUDE_DISK_INFO ('=' boolean_value)?
    ;
param_node_id
    : NODE_ID_ '=' node_filter
    ;
param_nodes
    : NODES '=' node_filters
    ;
param_parent_task_id
    : PARENT_TASK_ID '=' task_id
    ;
param_actions
    : ACTIONS '=' (actions_value | (actions_prefix (',' actions_prefix)*))
    ;
actions_value
    : ids
    ;
actions_prefix
    : (INDICES | CLUSTER | NODES | SNAPSHOT | TASKS | INGEST | ILM) ':' (index_name | '*')
    ;
param_active_only
    : ACTIVE_ONLY ('=' boolean_value)?
    ;
param_detailed
    : DETAILED ('=' boolean_value)?
    ;
param_full_id
    : FULL_ID ('=' boolean_value)?
    ;
param_h_trained_models
    : H_ '=' trained_models_hs_value (',' trained_models_hs_value)*
    ;
param_s_trained_models
    : S_ '=' trained_models_hs_value (',' trained_models_hs_value)*
    ;
trained_models_hs_value
    : CREATE_TIME
    | CREATED_BY | C_
    | DATA_FRAME_ANALYTICS_ID | DF
    | DESCRIPTION | D_
    | HEAP_SIZE
    | ID
    | INGEST_COUNT
    | INGEST_CURRENT
    | INGEST_FAILED
    | INGEST_PIPELINES
    | INGEST_TIME
    | LICENSE | L_
    | OPERATIONS | O_
    | VERSION | V_
    ;
param_h_jobs
    : H_ '=' jobs_hs_value (',' jobs_hs_value)*
    ;
param_s_jobs
    : S_ '=' jobs_hs_value (',' jobs_hs_value)*
    ;
jobs_hs_value
    : BUCKETS_COUNT
    | BUCKETS_TIME_EXP_AVG
    | BUCKETS_TIME_EXP_AVG_HOUR
    | BUCKETS_TIME_MAX
    | BUCKETS_TIME_MIN
    | BUCKETS_TIME_TOTAL
    | DATA_BUCKETS
    | DATA_EARLIEST_RECORD
    | DATA_EMPTY_BUCKETS
    | DATA_INPUT_BYTES
    | DATA_INPUT_FIELDS
    | DATA_INPUT_RECORDS
    | DATA_INVALID_DATES
    | DATA_LAST
    | DATA_LAST_EMPTY_BUCKET
    | DATA_LAST_SPARSE_BUCKET
    | DATA_LATEST_RECORD
    | DATA_MISSING_FIELDS
    | DATA_OUT_OF_ORDER_TIMESTAMPS
    | DATA_PROCESSED_FIELDS
    | DATA_PROCESSED_RECORDS
    | DATA_SPARSE_BUCKETS
    | FORECASTS_MEMORY_AVG
    | FORECASTS_MEMORY_MAX
    | FORECASTS_MEMORY_MIN
    | FORECASTS_MEMORY_TOTAL
    | FORECASTS_RECORDS_AVG
    | FORECASTS_RECORDS_MAX
    | FORECASTS_RECORDS_MIN
    | FORECASTS_RECORDS_TOTAL
    | FORECASTS_TIME_AVG
    | FORECASTS_TIME_MAX
    | FORECASTS_TIME_MIN
    | FORECASTS_TIME_TOTAL
    | FORECASTS_TOTAL | FT
    | ID
    | MODEL_BUCKET_ALLOCATION_FAILUR
    | MODEL_BY_FIELDS
    | MODEL_BYTES | MB
    | MODEL_BYTES_EXCEEDED
    | MODEL_CATEGORIZATION_STATUS
    | MODEL_CATEGORIZED_DOC_COUNT
    | MODEL_DEAD_CATEGORY_COUNT | MDCC
    | MODEL_FAILED_CATEGORY_COUNT
    | MODEL_FREQUENT_CATEGORY_COUNT
    | MODEL_LOG_TIME
    | MODEL_MEMORY_LIMIT
    | MODEL_MEMORY_STATUS
    | MODEL_OVER_FIELDS
    | MODEL_PARTITION_FIELDS
    | MODEL_RARE_CATEGORY_COUNT
    | MODEL_TIMESTAMP
    | MODEL_TOTAL_CATEGORY_COUNT
    | NODE_ADDRESS
    | NODE_EPHEMERAL_ID
    | NODE_ID
    | NODE_NAME
    | OPENED_TIME
    | STATE | S_
    ;
param_h_datafeeds
    : H_ '=' datafeeds_hs_value (',' datafeeds_hs_value)*
    ;
param_s_datafeeds
    : S_ '=' datafeeds_hs_value (',' datafeeds_hs_value)*
    ;
datafeeds_hs_value
    : ASSIGNMENT_EXPLANATION | AE
    | BUCKETS_COUNT
    | ID
    | NODE_ADDRESS
    | NODE_EPHEMERAL_ID
    | NODE_ID
    | NODE_NAME
    | SEARCH_BUCKET_AVG
    | SEARCH_COUNT
    | SEARCH_EXP_AVG_HOUR
    | SEARCH_TIME
    | STATE | S_
    ;
param_allow_no_match
    : ALLOW_NO_MATCH ('=' boolean_value)?
    ;
param_h_analytics
    : H_ '=' analytics_hs_value (',' analytics_hs_value)*
    ;
param_s_analytics
    : S_ '=' analytics_hs_value (',' analytics_hs_value)*
    ;
analytics_hs_value
    : ASSIGNMENT_EXPLANATION | AE
    | CREATE_TIME
    | DESCRIPTION | D_
    | DEST_INDEX
    | FAILURE_REASON
    | ID
    | MODEL_MEMORY_LIMIT
    | NODE_ADDRESS
    | NODE_EPHEMERAL_ID
    | NODE_ID
    | NODE_NAME
    | PROGRESS | P_
    | SOURCE_INDEX
    | STATE | S_
    | TYPE | T_
    | VERSION | V_
    ;
param_pri
    : PRI ('=' boolean_value)?
    ;
param_include_unloaded_segments
    : INCLUDE_UNLOADED_SEGMENTS ('=' boolean_value)?
    ;
param_health
    : HEALTH '=' status_value (',' status_value)
    ;
param_ts
    : TS ('=' boolean_value)?
    ;
param_time
    : TIME '=' time_units
    ;
param_fields
    : FIELDS '=' fields
    ;
param_bytes
    : BYTES '=' byte_size_units
    ;
param_v
    : V_ ('=' boolean_value)?
    ;
param_s
    : S_ '=' ids
    ;
param_h
    : H_ '=' ids
    ;
param_master_timeout
    : MASTER_TIMEOUT '=' time_value
    ;
param_local
    : LOCAL ('=' boolean_value)?
    ;
param_help
    : HELP ('=' boolean_value)?
    ;
param_format
    : FORMAT '=' (TXT | JSON | YAML | CSV | TSV | CBOR | SMILE) // 大部分只支持text
    ;
param_require_alias
    : REQUIRE_ALIAS ('=' boolean_value)?
    ;
param_wait_for_active_shards
    : WAIT_FOR_ACTIVE_SHARDS '=' (number_value | ALL | INDEX_SETTING)
    ;
param_refresh
    : REFRESH ('=' (boolean_value | WAIT_FOR))?
    ;
param_pipeline
    : PIPELINE '=' regular_id
    ;
param_sort
    : SORT '=' ids
    ;
param_from
    : FROM '=' INTEGER
    ;
param_size
    : SIZE '=' (INTEGER | regular_id)
    ;
param_q
    : Q_ '=' regular_id (':' regular_id)?
    ;
parma_seq_no_primary_term
    : SEQ_NO_PRIMARY_TERM ('=' boolean_value)?
    ;
param_source_includes
    : SOURCE_INCLUDES_ '=' fields
    ;
param_source_excludes
    : SOURCE_EXCLUDES_ '=' fields
    ;
param_source
    : SOURCE_ ('=' (boolean_value | regular_id))?
    ;
param_version
    : VERSION ('=' (boolean_value | number_value))?
    ;
param_rest_total_hits_as_int
    : REST_TOTAL_HITS_AS_INT ('=' boolean_value)?
    ;
param_track_scores
    : TRACK_SCORES ('=' boolean_value)?
    ;
param_track_total_hits
    : TRACK_TOTAL_HITS ('=' boolean_value | INTEGER)?
    ;
param_timeout
    : TIMEOUT '=' time_value
    ;
param_terminate_after
    : TERMINATE_AFTER '=' INTEGER
    ;
param_suggest_text
    : SUGGEST_TEXT '=' regular_id
    ;
param_suggest_size
    : SUGGEST_SIZE '=' INTEGER
    ;
param_suggest_mode
    : SUGGEST_MODE '=' (POPULAR | ALWAYS | MISSING)
    ;
param_suggest_field
    : SUGGEST_FIELD '=' field_name
    ;
param_stored_fields
    : param_stored_fields_name | param_stored_fields_boolean
    ;
param_stored_fields_name
    : STORED_FIELDS '=' fields
    ;
param_stored_fields_boolean
    : STORED_FIELDS ('=' boolean_value)?
    ;
param_stats
    : STATS '=' regular_id
    ;
param_search_type
    : SEARCH_TYPE '=' (QUERY_THEN_FETCH | DFS_QUERY_THEN_FETCH)
    ;
param_scroll
    : SCROLL ('=' (time_value | boolean_value))?
    ;
param_routing
    : ROUTING '=' regular_id
    ;
param_request_cache
    : REQUEST_CACHE ('=' boolean_value)?
    ;
param_pre_filter_shard_size
    : PRE_FILTER_SHARD_SIZE '=' number_value
    ;
param_preference
    : PREFERENCE '=' preference_value ('|' preference_value)*
    ;
preference_value
    : ONLY_LOCAL_
    | LOCAL_
    | (ONLY_NODES_ ':' node_filters)
    | (PREFER_NODES_ ':' node_filters)
    | (SHARDS_ ':' INTEGER (',' INTEGER)*)
    | regular_id
    ;
param_min_compatible_shard_node
    : MIN_COMPATIBLE_SHARD_NODE '=' regular_id
    ;
param_max_concurrent_shard_requests
    : MAX_CONCURRENT_SHARD_REQUESTS '=' INTEGER
    ;
param_lenient
    : LENIENT ('=' boolean_value)?
    ;
param_ignore_unavailable
    : IGNORE_UNAVAILABLE ('=' boolean_value)?
    ;
param_ignore_throttled
    : IGNORE_THROTTLED ('=' boolean_value)?
    ;
param_explain
    : EXPLAIN ('=' boolean_value)?
    ;
param_expand_wildcards
    : EXPAND_WILDCARDS '=' (ALL | OPEN | CLOSED | NONE | HIDDEN_)
    ;
param_docvalue_fields
    : DOCVALUE_FIELDS '=' fields
    ;
param_df
    : DF '=' regular_id
    ;
param_default_operator
    : DEFAULT_OPERATOR '=' (AND | OR)
    ;
param_ccs_minimize_roundtrips
    : CCS_MINIMIZE_ROUNDTRIPS ('=' boolean_value)?
    ;
param_batched_reduce_size
    : BATCHED_REDUCE_SIZE '=' INTEGER
    ;
param_analyze_wildcard
    : ANALYZE_WILDCARD ('=' boolean_value)?
    ;
param_analyzer
    : ANALYZER '=' regular_id
    ;
param_allow_partial_search_results
    : ALLOW_PARTIAL_SEARCH_RESULTS ('=' boolean_value)?
    ;
param_allow_no_indices
    : ALLOW_NO_INDICES ('=' boolean_value)?
    ;
param_keep_on_completion
    : KEEP_ON_COMPLETION ('=' boolean_value)?
    ;
param_keep_alive
    : KEEP_ALIVE ('=' (boolean_value | number_value | time_value))?
    ;
param_typed_keys
    : TYPED_KEYS ('=' boolean_value)?
    ;
param_wait_for_completion_timeout
    : WAIT_FOR_COMPLETION_TIMEOUT ('=' (boolean_value | number_value | time_value))?
    ;
param_error_trace
    : ERROR_TRACE ('=' boolean_value)?
    ;
param_filter_path
    : FILTER_PATH '=' ids
    ;
param_human
    : HUMAN ('=' boolean_value)?
    ;
param_pretty
    : PRETTY ('=' boolean_value)?
    ;
param_common
    : param_error_trace
    | param_filter_path
    | param_human
    | param_pretty
    ;

node_filters
    : node_filter (',' node_filter)*
    ;
node_filter
    : node_name | ALL_ | LOCAL_ | MASTER_ | '*'
    | node_type
    | ip_filter
    ;
node_type
    : (MASTER | DATA | INGEST | VOTING_ONLY | ML | COORDINATING_ONLY) ':' boolean_value
    ;
ip_filter
    : IP
    ;
setting_names
    : ids
    ;
data_stream_names
    : ids | '*' | ALL_
    ;
data_stream_name
    : regular_id
    ;
node_name
    : regular_id
    ;
target
    : (index_name (',' index_name)*) | '*' | ALL_
    ;
fields
    : field_name (',' field_name)*
    ;
field_name
    : regular_id
    ;
component_template_name
    : regular_id
    ;
auto_follow_name
    : regular_id
    ;
thread_pool_name
    : regular_id
    ;
template_names
    : (template_name (',' template_name)*) | '*' | ALL_
    ;
template_name
    : regular_id
    ;
aliases_name
    : alias_name (',' alias_name)*
    ;
alias_name
    : regular_id
    ;
policy_name
    : regular_id | '*'
    ;
repository_name
    : regular_id | ALL_
    ;
index_name
    : (cluster ':')? regular_id
    ;

cluster
    : regular_id
    ;
follower_index
    : regular_id
    ;
leader_index
    : regular_id
    ;
boolean_value
    : TRUE | FALSE
    ;
number_value
    : DECIMAL | INTEGER
    ;
// 可以是 0 或 -1
time_value
    : number_value time_units?
    ;
byte_value
    : number_value byte_size_units
    ;
transform_ids
    : ids_all
    ;
calendar_ids
    : ids_all
    ;
calendar_id
    : regular_id
    ;
pipeline_ids
    : (pipeline_id (',' pipeline_id)*) | '*' | ALL_
    ;
pipeline_id
    : regular_id
    ;
index_uuid
    : regular_id
    ;
policy_id
    : regular_id
    ;
scroll_id
    : regular_id
    ;
task_id
    : node_filter ':' task_number
    ;
task_number
    : INTEGER
    ;
model_id
    : regular_id
    ;
job_ids
    : (job_id (',' job_id)*) | '*' | ALL_
    ;
job_id
    : regular_id
    ;
datafeed_id
    : regular_id
    ;
history_id
    : regular_id
    ;
version
    : regular_id
    ;
ids_all
    : ids | '*' | ALL_
    ;
ids
    : regular_id (',' regular_id)*
    ;

time_units
    : 'nanos' | 'micros' | 'ms' | S_ | M_ | H_ | D_
    ;

byte_size_units
    : 'b' | 'kb' | MB | 'gb' | 'tb' | 'pb'
    ;


regular_id
    : REGULAR_ID | DECIMAL | INTEGER
    | ABORT
    | ACCEPT_DATA_LOSS
    | ACCEPT_ENTERPRISE
    | ACKNOWLEDGE
    | ACTIONS
    | ACTIVE
    | ACTIVE_ONLY
    | ADAPTIVE_SELECTION
    | ADVANCE_TIME
    | AE
    | AFTER
    | AGGREGATIONS
    | ALIASES
    | ALL
    | ALL_SHARDS
    | ALLOCATION
    | ALLOW_NO_FORECASTS
    | ALLOW_NO_INDICES
    | ALLOW_NO_MATCH
    | ALLOW_PARTIAL_SEARCH_RESULTS
    | ALWAYS
    | ANALYTICS
    | ANALYZE_WILDCARD
    | ANALYZER
    | AND
    | ANOMALY_DETECTORS
    | ANOMALY_SCORE
    | API_KEY
    | APPLICATION
    | ASC
    | ASSIGNMENT_EXPLANATION
    | ASYNC
    | AUTHENTICATE
    | AUTO
    | AUTO_FOLLOW
    | B_
    | BASIC_STATUS
    | BATCHED_REDUCE_SIZE
    | BLOCK
    | BLOCKS
    | BREAKER
    | BREAKERS
    | BUCKET_SPAN
    | BUCKETS
    | BUCKETS_COUNT
    | BUCKETS_TIME_EXP_AVG
    | BUCKETS_TIME_EXP_AVG_HOUR
    | BUCKETS_TIME_MAX
    | BUCKETS_TIME_MIN
    | BUCKETS_TIME_TOTAL
    | BULK
    | BYTES
    | C_
    | CACHE
    | CACHE_SIZE
    | CALC_INTERIM
    | CALENDARS
    | CAPACITY
    | CATEGORIES
    | CCS_MINIMIZE_ROUNDTRIPS
    | CENTROID
    | CERTIFICATES
    | CHARSET
    | CHECKPOINTS
    | CLEAR
    | CLOSE
    | CLOSED
    | CLUSTER
    | COLUMN_NAMES
    | COMPLETE_LOGOUT
    | COMPLETION
    | COMPLETION_FIELDS
    | COMPONENT_TEMPLATES
    | CONFLICTS
    | COORDINATING_ONLY
    | COUNT
    | CPU
    | CREATE
    | CREATE_TIME
    | CREATED_BY
    | CREDENTIAL
    | CROSS_CLUSTER
    | CURRENT_WATCHES
    | D_
    | DATA
    | DATA_BUCKETS
    | DATA_EARLIEST_RECORD
    | DATA_EMPTY_BUCKETS
    | DATA_FRAME
    | DATA_FRAME_ANALYTICS_ID
    | DATA_INPUT_BYTES
    | DATA_INPUT_FIELDS
    | DATA_INPUT_RECORDS
    | DATA_INVALID_DATES
    | DATA_LAST
    | DATA_LAST_EMPTY_BUCKET
    | DATA_LAST_SPARSE_BUCKET
    | DATA_LATEST_RECORD
    | DATA_MISSING_FIELDS
    | DATA_OUT_OF_ORDER_TIMESTAMPS
    | DATA_PROCESSED_FIELDS
    | DATA_PROCESSED_RECORDS
    | DATA_SPARSE_BUCKETS
    | DATAFEEDS
    | DEBUG
    | DECOMPRESS_DEFINITION
    | DEFAULT_OPERATOR
    | DEFER_DEFINITION_DECOMPRESSION
    | DEFER_VALIDATION
    | DEFINITION
    | DEFINITION_STATUS
    | DELETE_DEST_INDEX
    | DELETE_INTERVENING_RESULTS
    | DELETE_USER_ANNOTATIONS
    | DELIMITER
    | DENSE_VECTOR
    | DEPLOYMENT
    | DEPLOYMENT_ID
    | DEPRECATIONS
    | DESC
    | DESCRIPTION
    | DESIRED_BALANCE
    | DESIRED_NODES
    | DEST_INDEX
    | DETAILED
    | DETECTOR
    | DF
    | DFS_QUERY_THEN_FETCH
    | DISCOVERY
    | DISK
    | DOCS
    | DOCVALUE_FIELDS
    | DRY_RUN
    | DURATION
    | ECS_COMPATIBILITY
    | EMBED_STACKTRACES
    | ENABLED
    | END
    | ENROLL
    | ERROR_TRACE
    | EVENT
    | EVENTS
    | EXACT_BOUNDS
    | EXCLUDE_GENERATED
    | EXCLUDE_INTERIM
    | EXECUTE
    | EXPAND
    | EXPAND_WILDCARDS
    | EXPIRES_IN
    | EXPLAIN
    | EXPLORE
    | EXTENT
    | EXTERNAL
    | EXTERNAL_GTE
    | FAILURE_REASON
    | FALSE
    | FEATURE_IMPORTANCE_BASELINE
    | FEATURES
    | FIELD
    | FIELD_STATISTICS
    | FIELDDATA
    | FIELDDATA_FIELDS
    | FIELDS
    | FILTER_PATH
    | FILTERS
    | FIND_STRUCTURE
    | FLAT_SETTINGS
    | FLUSH
    | FOLLOW
    | FOLLOW_INFO
    | FORBID_CLOSED_INDICES
    | FORCE
    | FORECASTS_MEMORY_AVG
    | FORECASTS_MEMORY_MAX
    | FORECASTS_MEMORY_MIN
    | FORECASTS_MEMORY_TOTAL
    | FORECASTS_RECORDS_AVG
    | FORECASTS_RECORDS_MAX
    | FORECASTS_RECORDS_MIN
    | FORECASTS_RECORDS_TOTAL
    | FORECASTS_TIME_AVG
    | FORECASTS_TIME_MAX
    | FORECASTS_TIME_MIN
    | FORECASTS_TIME_TOTAL
    | FORECASTS_TOTAL
    | FORGET_FOLLOWER
    | FORMAT
    | FROM
    | FROM_SORT_VALUE
    | FS
    | FT
    | FULL_COPY
    | FULL_ID
    | FULLY_ALLOCATED
    | GEOHEX
    | GEOIP
    | GEOTILE
    | GLOBAL_CHECKPOINTS
    | GPU
    | GRANT
    | GREEN
    | GRID
    | GRID_AGG
    | GRID_PRECISION
    | GRID_TYPE
    | GROK
    | GROK_PATTERN
    | GROUP_BY
    | GROUPS
    | H_
    | HAS_HEADER_ROW
    | HEALTH
    | HEAP_SIZE
    | HELP
    | HIDDEN_
    | HIGH
    | HOT_THREADS
    | HTTP
    | HUMAN
    | HYPERPARAMETERS
    | ID
    | IDS
    | IF_PRIMARY_TERM
    | IF_SEQ_NO
    | IF_VERSION
    | IGNORE_IDLE_THREADS
    | IGNORE_THROTTLED
    | IGNORE_UNAVAILABLE
    | ILM
    | IMMEDIATE
    | INCLUDE
    | INCLUDE_DEFAULTS
    | INCLUDE_DISK_INFO
    | INCLUDE_REPOSITORY
    | INCLUDE_SEGMENT_FILE_SIZES
    | INCLUDE_UNLOADED_SEGMENTS
    | INCLUDE_UNMAPPED
    | INCLUDE_YES_DECISIONS
    | INDEX
    | INDEX_DETAILS
    | INDEX_NAMES
    | INDEX_SETTING
    | INDEXING
    | INDEXING_PRESSURE
    | INDICES
    | INFLUENCER_SCORE
    | INFLUENCERS
    | INFO
    | INGEST
    | INGEST_COUNT
    | INGEST_CURRENT
    | INGEST_FAILED
    | INGEST_PIPELINES
    | INGEST_TIME
    | INTERNAL
    | INTERVAL
    | INVALIDATE
    | JOB
    | JOB_ID
    | JOBS
    | JSON
    | JVM
    | KEEP_ALIVE
    | KEEP_ON_COMPLETION
    | KIBANA
    | L_
    | LANG
    | LANGUID
    | LENIENT
    | LEVEL
    | LICENSE
    | LINE_MERGE_SIZE_LIMIT
    | LINES_TO_SAMPLE
    | LOCAL
    | LOGOUT
    | LOW
    | M_
    | MAPPINGS
    | MASTER
    | MASTER_IS_STABLE
    | MASTER_NODE
    | MASTER_TIMEOUT
    | MAX_CONCURRENT_SEARCHES
    | MAX_CONCURRENT_SHARD_REQUESTS
    | MAX_DOCS
    | MAX_MODEL_MEMORY
    | MAX_NUM_SEGMENTS
    | MB
    | MDCC
    | MEM
    | MEMORY
    | MERGE
    | METADATA
    | METRIC
    | MIGRATE_TO_DATA_TIERS
    | MIN_COMPATIBLE_SHARD_NODE
    | MIN_SCORE
    | MISSING
    | ML
    | MODEL_ALIASES
    | MODEL_BUCKET_ALLOCATION_FAILUR
    | MODEL_BY_FIELDS
    | MODEL_BYTES
    | MODEL_BYTES_EXCEEDED
    | MODEL_CATEGORIZATION_STATUS
    | MODEL_CATEGORIZED_DOC_COUNT
    | MODEL_DEAD_CATEGORY_COUNT
    | MODEL_FAILED_CATEGORY_COUNT
    | MODEL_FREQUENT_CATEGORY_COUNT
    | MODEL_LOG_TIME
    | MODEL_MEMORY_LIMIT
    | MODEL_MEMORY_STATUS
    | MODEL_OVER_FIELDS
    | MODEL_PARTITION_FIELDS
    | MODEL_RARE_CATEGORY_COUNT
    | MODEL_SNAPSHOTS
    | MODEL_TIMESTAMP
    | MODEL_TOTAL_CATEGORY_COUNT
    | MOVE
    | MULTIFIELD
    | NAME
    | NESTED
    | NODE
    | NODE_ADDRESS
    | NODE_EPHEMERAL_ID
    | NODE_ID
    | NODE_IDS
    | NODE_NAME
    | NODE_NAMES
    | NODEATTRS
    | NODES
    | NONE
    | NORMAL
    | NULL_
    | NUMBER_OF_ALLOCATION
    | O_
    | OAUTH2
    | OFFSET
    | OFFSETS
    | OIDC
    | ONLY_ERRORS
    | ONLY_EXPUNGE_DELETES
    | ONLY_MANAGED
    | OP_TYPE
    | OPEN
    | OPENED_TIME
    | OPERATIONS
    | OR
    | ORDER
    | OS
    | OVERALL_BUCKETS
    | OVERALL_SCORE
    | OWNER
    | P_
    | PAGE
    | PAINLESS
    | PARENT
    | PARENT_TASK_ID
    | PARENTS
    | PARTITION_FIELD_VALUE
    | PAUSE
    | PAUSE_FOLLOW
    | PAYLOADS
    | PENDING_TASKS
    | PENDING_WATCHES
    | PIPELINE
    | PLUGINS
    | POINT
    | POLICY
    | POPULAR
    | POSITIONS
    | PRE_FILTER_SHARD_SIZE
    | PREFERENCE
    | PREPARE
    | PRESERVE_EXISTING
    | PRETTY
    | PREVALIDATE_NODE_REMOVAL
    | PRI
    | PRIORITY
    | PRIVILEGE
    | PROCEED
    | PROCESS
    | PROCESSOR
    | PROFILE
    | PROGRESS
    | Q_
    | QUERY
    | QUERY_CACHE
    | QUERY_THEN_FETCH
    | QUEUE_CAPACITY
    | QUEUED_WATCHES
    | QUOTE
    | READ
    | READ_ONLY
    | REALM
    | REALM_NAME
    | REALTIME
    | REASSIGN
    | RECORD_SCORE
    | RECORDS
    | RECOVERY
    | RED
    | REFRESH
    | RELOAD_SECURE_SETTINGS
    | REMOVE
    | REPOSITORIES
    | REPOSITORY_INTEGRITY
    | REQUEST
    | REQUEST_CACHE
    | REQUESTS_PER_SECOND
    | REQUIRE_ALIAS
    | REROUTE
    | RESET_END
    | RESET_START
    | REST_ACTIONS
    | REST_TOTAL_HITS_AS_INT
    | RESULTS
    | RESUME
    | RESUME_FOLLOW
    | RETRY
    | RETRY_FAILED
    | RETRY_ON_CONFLICT
    | REWRITE
    | ROLE
    | ROLE_MAPPING
    | ROUTING
    | ROUTING_NODES
    | ROUTING_TABLE
    | RUN_EXPENSIVE_TASKS
    | S_
    | SAML
    | SCRIPT
    | SCROLL
    | SCROLL_ID
    | SCROLL_SIZE
    | SEARCH
    | SEARCH_APPLICATION
    | SEARCH_BUCKET_AVG
    | SEARCH_COUNT
    | SEARCH_EXP_AVG_HOUR
    | SEARCH_TIME
    | SEARCH_TIMEOUT
    | SEARCH_TYPE
    | SECRET
    | SEGMENTS
    | SEQ_NO_PRIMARY_TERM
    | SERVICE
    | SET_UPGRADE_MODE
    | SETTINGS
    | SHARD_STATS
    | SHARDS
    | SHARDS_AVAILABILITY
    | SHARDS_CAPACITY
    | SHARED_CACHE
    | SHOULD_TRIM_FIELDS
    | SHUTDOWN
    | SIZE
    | SKIP_TIME
    | SLICES
    | SLM
    | SLM_POLICY_FILTER
    | SNAPSHOT
    | SNAPSHOTS
    | SORT
    | SOURCE_INDEX
    | START
    | START_BASIC
    | START_TRIAL
    | STARTED
    | STARTING
    | STATE
    | STATS
    | STATUS
    | STOP
    | STORAGE
    | STORE
    | STORED_FIELDS
    | SUGGEST_FIELD
    | SUGGEST_MODE
    | SUGGEST_SIZE
    | SUGGEST_TEXT
    | SUMMARY
    | SYSTEM_API_VERSION
    | SYSTEM_FEATURES
    | SYSTEM_ID
    | T_
    | TAGS
    | TASKS
    | TEMPLATE
    | TEMPLATES
    | TERM_STATISTICS
    | TERMINATE_AFTER
    | TEST_GROK_PATTERN
    | TXT
    | THREAD_POOL
    | THREADS
    | THREADS_PER_ALLOCATION
    | TIME
    | TIMEOUT
    | TIMESTAMP_FIELD
    | TIMESTAMP_FORMAT
    | TOKEN
    | TOP_N
    | TOTAL_FEATURE_IMPORTANCE
    | TRACK_SCORES
    | TRACK_TOTAL_HITS
    | TRAINED_MODELS
    | TRANSFORMS
    | TRANSLATE
    | TRANSLOG
    | TRANSPORT
    | TRIAL_STATUS
    | TRUE
    | TS
    | TYPE
    | TYPE_QUERY_STRING
    | TYPED_KEYS
    | TYPES
    | UNFOLLOW
    | URGENT
    | USAGE
    | USER
    | USERNAME
    | USERNAMES
    | V_
    | VERBOSE
    | VERIFY
    | VERSION
    | VERSION_TYPE
    | VOCABULARY
    | VOTING_CONFIG_EXCLUSIONS
    | VOTING_ONLY
    | WAIT
    | WAIT_FOR
    | WAIT_FOR_ACTIVE_SHARDS
    | WAIT_FOR_ADVANCE
    | WAIT_FOR_CHECKPOINT
    | WAIT_FOR_CHECKPOINTS
    | WAIT_FOR_COMPLETION
    | WAIT_FOR_COMPLETION_TIMEOUT
    | WAIT_FOR_EVENTS
    | WAIT_FOR_INDEX
    | WAIT_FOR_METADATA_VERSION
    | WAIT_FOR_NO_INITIALIZING_SHARDS
    | WAIT_FOR_NO_RELOCATING_SHARDS
    | WAIT_FOR_NODES
    | WAIT_FOR_REMOVAL
    | WAIT_FOR_STATUS
    | WAIT_FOR_TIMEOUT
    | WAIT_IF_ONGOING
    | WARMER
    | WATCH
    | WATCHES
    | WITH_LABELS
    | WITH_LIMITED_BY
    | WITH_PROFILE_UID
    | WRITE
    | WRITE_INDEX_ONLY
    | YAML
    | YELLOW
    ;
jsonObject
    : '{' (jsonPair (',' jsonPair)*)? '}'
    ;
jsonPair
    : DQUOTA_STRING ':' jsonValue
    ;
jsonValue
    : jsonNull
    | jsonBoolean
    | jsonNumber
    | jsonString
    | jsonObject
    | jsonArray
    ;
jsonNull
    : NULL_
    ;
jsonBoolean
    : TRUE
    | FALSE
    ;
jsonNumber
    : number_value
    ;
jsonString
    : DQUOTA_STRING
    | TQUOTA_STRING
    ;
jsonArray
    : '[' (jsonValue (',' jsonValue)*)? ']'
    ;
