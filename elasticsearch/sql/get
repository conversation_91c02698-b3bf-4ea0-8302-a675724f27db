GET /_search?q=kimchy&filter_path=took,hits.hits._id,hits.hits._score
GET /_cluster/state?filter_path=metadata.indices.*.stat*
GET /_cluster/state?filter_path=routing_table.indices.**.state
GET /_count?filter_path=-_shards
GET /_cluster/state?filter_path=metadata.indices.*.state,-metadata.indices.logstash-*
GET /_search?filter_path=hits.hits._source&_source=title&sort=rating:desc
GET my-index-000001/_settings?flat_settings=true
GET my-index-000001/_settings?flat_settings=false
GET /_autoscaling/capacity/
GET /_autoscaling/policy/my_autoscaling_policy
GET _application/analytics/
GET _application/analytics/my_analytics_collection
GET _application/analytics/my*
GET _cat/aliases?v=true
GET /_cat/allocation?v=true
GET _cat/ml/anomaly_detectors?h=id,s,dpr,mb&v=true
GET _cat/component_templates/my-template-*?v=true&s=name
GET /_cat/count/my-index-000001?v=true
GET /_cat/count?v=true
GET _cat/ml/data_frame/analytics?v=true
GET _cat/ml/datafeeds?v=true
GET /_cat/fielddata?v=true&fields=body
GET /_cat/health?v=true
GET /_cat/health?v=true&ts=false
GET /_cat/indices/my-index-*?v=true&s=index
GET /_cat/master?v=true
GET /_cat/nodes?v=true
GET /_cat/nodeattrs?v=true
GET /_cat/nodes?v=true&h=id,ip,port,v,m
GET /_cat/pending_tasks?v=true
GET /_cat/plugins?v=true&s=component&h=name,component,version,description
GET _cat/recovery?v=true
GET _cat/recovery?v=true&h=i,s,t,ty,st,shost,thost,f,fp,b,bp
GET /_cat/repositories?v=true
GET /_cat/segments?v=true
GET _cat/shards
GET _cat/shards/my-index-*
GET _cat/shards?h=index,shard,prirep,state,unassigned.reason
GET /_cat/snapshots/repo1?v=true&s=id
GET _cat/tasks?v=true
GET _cat/templates/my-template-*?v=true&s=name
GET /_cat/thread_pool
GET _cat/ml/trained_models?h=c,o,l,ct,v&v=ture
GET /_cat/transforms?v=true&format=json
GET _cluster/allocation/explain
{
  "index": "my-index-000001",
  "shard": 0,
  "primary": true
}
GET /_cluster/settings
GET /_cluster/health?wait_for_status=yellow&timeout=50s
GET /_cluster/health/my-index-000001?level=shards
GET _health_report
GET _health_report/shards_availability
GET _health_report?verbose=false
GET /_cluster/state/metadata,routing_table/foo,bar
GET /_cluster/state/_all/foo,bar
GET /_cluster/state/blocks
GET /_cluster/stats?human&pretty
GET /_cluster/stats/nodes/node1,node*,master:false
GET /_cluster/stats?include_remotes=true
GET _nodes/usage
GET /_nodes/hot_threads
GET /_nodes/nodeId1,nodeId2/hot_threads
GET /_nodes/process
GET /_nodes/_all/process
GET /_nodes/nodeId1,nodeId2/jvm,process
GET /_nodes/nodeId1,nodeId2/info/jvm,process
GET /_nodes/nodeId1,nodeId2/_all
GET /_nodes/plugins
GET /_nodes/ingest
GET /_nodes/stats/indices
GET /_nodes/stats/os,process
GET /_nodes/********/stats/process
GET /_nodes/stats/indices/fielddata?fields=field1,field2
GET /_nodes/stats/indices/fielddata?level=indices&fields=field1,field2
GET /_nodes/stats/indices/fielddata?level=shards&fields=field1,field2
GET /_nodes/stats/indices/fielddata?fields=field*
GET /_nodes/stats?groups=_all
GET /_nodes/stats/indices?groups=foo,bar
GET /_info/_all
GET /_info/http
GET /_info/ingest
GET /_info/thread_pool
GET /_info/script
GET /_info/http,ingest
GET /_cluster/pending_tasks
GET /_remote/info
GET _tasks
GET _tasks?nodes=nodeId1,nodeId2
GET _tasks?nodes=nodeId1,nodeId2&actions=cluster:*
GET _tasks/oTUltX4IQMOUUVeiohTt8A:124
GET _tasks?parent_task_id=oTUltX4IQMOUUVeiohTt8A:123
GET _tasks?actions=*search&detailed
GET _tasks/oTUltX4IQMOUUVeiohTt8A:12345?wait_for_completion=true&timeout=10s
GET _tasks?group_by=parents
GET _tasks?group_by=none
GET /_internal/desired_nodes/_latest
GET /_internal/desired_balance
GET /_ccr/stats
GET /follower_index/_ccr/stats
GET /follower_index/_ccr/info
GET /_ccr/auto_follow/my_auto_follow_pattern
GET /_data_stream/my-data-stream
GET _data_stream/my-data-stream*
GET /_data_stream/my-data-stream*/_stats?human=true
GET _data_stream/my-data-stream*/_lifecycle
GET .ds-metrics-2023.03.22-000001/_lifecycle/explain
GET _lifecycle/stats?human&pretty
GET my-index-000001/_doc/0
GET my-index-000001/_doc/0?_source=false
GET my-index-000001/_doc/0?_source_includes=*.id&_source_excludes=entities
GET my-index-000001/_doc/0?_source=*.id
GET my-index-000001/_doc/2?routing=user1
GET my-index-000001/_source/1/?_source_includes=*.id&_source_excludes=entities
GET my-index-000001/_doc/1?stored_fields=tags,counter
GET my-index-000001/_doc/2?routing=user1&stored_fields=tags,counter
GET _tasks?detailed=true&actions=*byquery
GET /my-index-000001/_mget
{
  "docs": [
    {
      "_id": "1"
    },
    {
      "_id": "2"
    }
  ]
}

GET /test/_mget?stored_fields=field1,field2
{
  "docs": [
    {
      "_id": "1"
    },
    {
      "_id": "2",
      "stored_fields": [ "field3", "field4" ]
    }
  ]
}

GET /_mget?routing=key1
{
  "docs": [
    {
      "_index": "test",
      "_id": "1",
      "routing": "key2"
    },
    {
      "_index": "test",
      "_id": "2"
    }
  ]
}

GET /my-index-000001/_termvectors/1
GET /my-index-000001/_termvectors/1?fields=message

GET /my-index-000001/_termvectors/1
{
  "fields" : ["text"],
  "offsets" : true,
  "payloads" : true,
  "positions" : true,
  "term_statistics" : true,
  "field_statistics" : true
}
GET /_enrich/policy/my-policy
GET /_enrich/policy/my-policy,other-policy
GET /_enrich/policy
GET /_enrich/_stats
GET /my-data-stream/_eql/search
{
  "query": """
    process where process.name == "regsvr32.exe"
  """
}
GET /_eql/search/FkpMRkJGS1gzVDRlM3g4ZzMyRGlLbkEaTXlJZHdNT09TU2VTZVBoNDM3cFZMUToxMDM=
GET /_eql/search/status/FkpMRkJGS1gzVDRlM3g4ZzMyRGlLbkEaTXlJZHdNT09TU2VTZVBoNDM3cFZMUToxMDM=
GET /_query/async/FkpMRkJGS1gzVDRlM3g4ZzMyRGlLbkEaTXlJZHdNT09TU2VTZVBoNDM3cFZMUToxMDM=
GET /_features
GET /target/_fleet/_fleet_search
GET /_fleet/_fleet_msearch
GET /_analyze
{
  "analyzer" : "standard",
  "text" : "Quick Brown Foxes!"
}

GET /analyze_sample/_analyze
{
  "text" : "this is a test"
}
GET /my-index-000001/_field_usage_stats
GET my-data-stream/_alias/my-alias
GET /_component_template/template_1
GET /my-index-000001/_mapping/field/user
GET publications/_mapping/field/title
GET publications/_mapping/field/author.id,abstract,name
GET publications/_mapping/field/a*
GET /my-index-000001
GET /my-index-000001/_settings
GET /my-index-000001,my-index-000002/_settings
GET /_all/_settings
GET /log_2099_*/_settings
GET /log_2099_-*/_settings/index.number_*
GET /_index_template/template_1
GET /_index_template
GET /_template/template_1,template_2
GET /_template
GET /my-index-000001,my-index-000002/_mapping
GET /*/_mapping
GET /_all/_mapping
GET /_mapping
GET /my-index-000001/_recovery
GET index1,index2/_recovery?human
GET /_recovery?human
GET _recovery?human&detailed=true
GET /my-index-000001/_segments
GET /test1,test2/_segments
GET /_segments
GET /my-index-000001/_shard_stores
GET /test1,test2/_shard_stores
GET /_shard_stores?status=green
GET /my-index-000001/_stats
GET /index1,index2/_stats
GET /_stats/merge,refresh
GET /_stats
GET /_stats/search?groups=group1,group2
GET /_dangling
GET /_resolve/index/my-index-*
GET /_resolve/index/f*,remoteCluster1:bar*?expand_wildcards=all
GET _ilm/policy/my_policy
GET _ilm/status
GET my-index-000001/_ilm/explain?human
GET _inference/sparse_embedding/my-elser-model
GET /_xpack
GET /_ingest/pipeline/my-pipeline-id
GET _ingest/geoip/stats
GET /_ingest/ip_location/database/my-database-id
GET /_license
GET /_license/trial_status
GET /_license/basic_status
GET _logstash/pipeline/my_pipeline
GET _ml/info
GET _ml/memory/_stats?human
GET _ml/anomaly_detectors/low_request_rate/results/buckets
{
  "anomaly_score": 80,
  "start": "1454530200001"
}
GET _ml/calendars/planned-outages
GET _ml/anomaly_detectors/esxi_log/results/categories
{
  "page":{
    "size": 1
  }
}
GET _ml/datafeeds/datafeed-high_sum_total_sales
GET _ml/datafeeds/datafeed-high_sum_total_sales/_stats
GET _ml/anomaly_detectors/high_sum_total_sales/results/influencers
{
  "sort": "influencer_score",
  "desc": true
}
GET _ml/anomaly_detectors/high_sum_total_sales
GET _ml/anomaly_detectors/low_request_rate/_stats
GET _ml/anomaly_detectors/high_sum_total_sales/model_snapshots
{
  "start": "1575402236000"
}
GET _ml/anomaly_detectors/low_request_rate/model_snapshots/_all/_upgrade/_stats
GET _ml/anomaly_detectors/job-*/results/overall_buckets
{
  "overall_score": 80,
  "start": "1403532000000"
}
GET _ml/calendars/planned-outages/events
GET _ml/filters/safe_domains
GET _ml/anomaly_detectors/low_request_rate/results/records
{
  "sort": "record_score",
  "desc": true,
  "start": "1454944100000"
}
GET _ml/datafeeds/datafeed-high_sum_total_sales/_preview
GET _ml/data_frame/analytics/loganalytics
GET _ml/data_frame/analytics/weblog-outliers/_stats
GET _ml/trained_models/
GET _ml/trained_models/_stats
GET /_migration/deprecations
GET /_migration/system_features
GET /_nodes/USpTGYaBSIKbgSUJR2Z9lg/shutdown
GET _query_rules/?from=0&size=3
GET _query_rules/my-ruleset/
GET _query_rules/
GET _query_rules/my-ruleset/_rule/my-rule1
get /my-index-000001/_reload_search_analyzers
GET _rollup/job/sensor
GET _rollup/data/sensor-*
GET /sensor_rollup/_rollup/data
GET /sensor_rollup/_rollup_search
GET _script_context
GET _script_language
GET _scripts/my-stored-script
GET /my-index-000001/_search
GET /_nodes/stats/indices/search
GET my-index/_knn_search
GET /restaurants/_search
GET /_search/scroll
GET example-index/_search
GET my-index/_search/template
GET my-index/_msearch/template
GET /my-index-000001/_search_shards
GET my-index-000001/_msearch
GET /my-index-000001/_count?q=user:kimchy
GET my-index-000001/_validate/query?q=user.id:kimchy
GET /my-index-000001/_explain/0
GET /my-index-000001/_search
GET /_field_caps?fields=rating
GET /my-index-000001/_rank_eval
GET my-index/_mvt/my-geo-field/15/5271/12710
GET _application/search_application/my-app/
GET _application/search_application?from=0&size=3&q=app*
GET _application/search_application/
GET /_searchable_snapshots/cache/stats
GET /my-index/_searchable_snapshots/stats
GET /_security/_authenticate
GET /_security/enroll/kibana
GET /_security/api_key?id=VuaCfGcBCdbkQm-e5aOx&with_limited_by=true
GET /_security/privilege/myapp/read
GET /_security/privilege/myapp/
GET /_security/privilege/_builtin
GET /_security/role_mapping/mapping1
GET /_security/role/my_admin_role
GET /_security/service/elastic/fleet-server
GET /_security/service/elastic/fleet-server/credential
GET /_security/settings
GET /_security/user/_privileges
GET /_security/user/jacknich
GET /_security/user/jacknich?with_profile_uid=true
GET /_security/user/_has_privileges
GET /_security/_query/api_key
GET /_security/saml/metadata/saml1
GET /_ssl/certificates
GET /_security/profile/u_79HkWkwmnBH5gqFKwoxggWPjEBOur1zLPXQPEl1VBW0_0
GET /_security/api_key?id=VuaCfGcBCdbkQm-e5aOx
GET /_snapshot/my_repository
GET /_snapshot/my_repository/my_snapshot
GET _snapshot/_status
GET _slm/policy/daily-snapshots?human
GET _slm/status
GET /_slm/stats
GET _sql/async/FmdMX2pIang3UWhLRU5QS0lqdlppYncaMUpYQ05oSkpTc3kwZ21EdC1tbFJXQToxOTI=?format=json
GET _synonyms/my-synonyms-set
GET _synonyms
GET _synonyms/my-synonyms-set/test-1
GET _text_structure/test_grok_pattern
GET _transform?size=10
GET _transform/ecommerce_transform1
GET _transform/ecommerce-customer-transform/_stats
GET /_xpack/usage
GET _watcher/watch/my_watch
GET _watcher/watch/my_watch
GET _watcher/stats
GET _watcher/stats?metric=current_watches
GET _watcher/stats/current_watches
GET /_watcher/_query/watches
GET /_watcher/settings









