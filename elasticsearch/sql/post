POST /library/_doc?refresh
{"title": "Book #1", "rating": 200.1}

PUT /_autoscaling/policy/my_autoscaling_policy
{
  "roles" : [ "data_hot" ],
  "deciders": {
    "fixed": {
    }
  }
}

POST /my-index-000001/_search?size=surprise_me&error_trace=true

POST _application/analytics/my_analytics_collection/event/search_click
{
  "session": {
    "id": "1797ca95-91c9-4e2e-b1bd-9c38e6f386a9"
  },
  "user": {
    "id": "5f26f01a-bbee-4202-9298-81261067abbd"
  },
  "search":{
    "query": "search term",
    "results": {
      "items": [
        {
          "document": {
            "id": "123",
            "index": "products"
          }
        }
      ],
      "total_results": 10
    },
    "sort": {
      "name": "relevance"
    },
    "search_application": "website"
  },
  "document":{
    "id": "123",
    "index": "products"
  }
}

POST /_cluster/reroute?metric=none
{
  "commands": [
    {
      "move": {
        "index": "test", "shard": 0,
        "from_node": "node1", "to_node": "node2"
      }
    },
    {
      "allocate_replica": {
        "index": "test", "shard": 1,
        "node": "node3"
      }
    }
  ]
}

POST _nodes/reload_secure_settings
{
  "secure_settings_password":"keystore-password"
}

POST _nodes/nodeId1,nodeId2/reload_secure_settings
{
  "secure_settings_password":"keystore-password"
}

POST /_internal/prevalidate_node_removal?names=node1,node2

POST _tasks/oTUltX4IQMOUUVeiohTt8A:12345/_cancel

POST _tasks/_cancel?nodes=nodeId1,nodeId2&actions=*reindex

POST /_cluster/voting_config_exclusions?node_names=nodeName1,nodeName2

PUT /_internal/desired_nodes/Ywkh3INLQcuPT49f6kcppA/100
{
    "nodes" : [
        {
            "settings" : {
                 "node.name" : "instance-000187",
                 "node.external_id": "instance-000187",
                 "node.roles" : ["data_hot", "master"],
                 "node.attr.data" : "hot",
                 "node.attr.logical_availability_zone" : "zone-0"
            },
            "processors" : 8.0,
            "memory" : "58gb",
            "storage" : "2tb"
        }
    ]
}

POST /follower_index/_ccr/pause_follow

POST /follower_index/_ccr/resume_follow
{
  "max_read_request_operation_count" : 1024,
  "max_outstanding_read_requests" : 16,
  "max_read_request_size" : "1024k",
  "max_write_request_operation_count" : 32768,
  "max_write_request_size" : "16k",
  "max_outstanding_write_requests" : 8,
  "max_write_buffer_count" : 512,
  "max_write_buffer_size" : "512k",
  "max_retry_delay" : "10s",
  "read_poll_timeout" : "30s"
}

POST /follower_index/_ccr/unfollow

POST /leader_index/_ccr/forget_follower
{
  "follower_cluster" : "follower_cluster",
  "follower_index" : "follower_index",
  "follower_index_uuid" : "vYpnaWPRQB6mNspmoCeYyA",
  "leader_remote_cluster" : "leader_cluster"
}

POST /_ccr/auto_follow/my_auto_follow_pattern/pause

POST /_ccr/auto_follow/my_auto_follow_pattern/resume

POST /_data_stream/_migrate/my-logs

POST /_data_stream/_promote/my-data-stream

POST _data_stream/_modify
{
  "actions": [
    {
      "remove_backing_index": {
        "data_stream": "my-logs",
        "index": ".ds-my-logs-2099.01.01-000001"
      }
    },
    {
      "add_backing_index": {
        "data_stream": "my-logs",
        "index": "index-to-add"
      }
    }
  ]
}

POST /my-time-series-index/_downsample/my-downsampled-time-series-index
{
    "fixed_interval": "1d"
}

POST my-index-000001/_doc/
{
  "@timestamp": "2099-11-15T13:12:00",
  "message": "GET /search HTTP/1.1 200 1070000",
  "user": {
    "id": "kimchy"
  }
}

POST my-index-000001/_doc?routing=kimchy
{
  "@timestamp": "2099-11-15T13:12:00",
  "message": "GET /search HTTP/1.1 200 1070000",
  "user": {
    "id": "kimchy"
  }
}

POST /my-index-000001/_delete_by_query
{
  "query": {
    "match": {
      "user.id": "elkbee"
    }
  }
}

POST my-index-000001/_delete_by_query?conflicts=proceed
{
  "query": {
    "match_all": {}
  }
}

POST /my-index-000001,my-index-000002/_delete_by_query
{
  "query": {
    "match_all": {}
  }
}

POST my-index-000001/_delete_by_query?routing=1
{
  "query": {
    "range" : {
        "age" : {
           "gte" : 10
        }
    }
  }
}

POST my-index-000001/_delete_by_query?scroll_size=5000
{
  "query": {
    "term": {
      "user.id": "kimchy"
    }
  }
}

POST my-index-000001/_delete_by_query
{
  "query": {
    "term": {
      "user.id": "kimchy"
    }
  },
  "max_docs": 1
}

POST my-index-000001/_search?size=0&filter_path=hits.total
{
  "query": {
    "range": {
      "http.response.bytes": {
        "lt": 2000000
      }
    }
  }
}

POST my-index-000001/_delete_by_query?refresh&slices=5
{
  "query": {
    "range": {
      "http.response.bytes": {
        "lt": 2000000
      }
    }
  }
}

POST my-index-000001/_search?size=0&filter_path=hits.total
{
  "query": {
    "range": {
      "http.response.bytes": {
        "lt": 2000000
      }
    }
  }
}

POST _delete_by_query/r1A2WoRbTwKZ516z6NEs5A:36619/_rethrottle?requests_per_second=-1

POST _tasks/r1A2WoRbTwKZ516z6NEs5A:36619/_cancel

POST test/_update/1
{
  "script" : {
    "source": "ctx._source.counter += params.count",
    "lang": "painless",
    "params" : {
      "count" : 4
    }
  }
}

POST my-index-000001/_update_by_query?conflicts=proceed
{
  "query": {
    "term": {
      "user.id": "kimchy"
    }
  }
}

POST my-index-000001/_update_by_query?routing=1

POST my-index-000001/_update_by_query?scroll_size=100

POST my-index-000001/_update_by_query?pipeline=set-foo

POST _tasks/r1A2WoRbTwKZ516z6NEs5A:36619/_cancel

POST _update_by_query/r1A2WoRbTwKZ516z6NEs5A:36619/_rethrottle?requests_per_second=-1

POST my-index-000001/_search?size=0&q=extra:test&filter_path=hits.total

POST my-index-000001/_update_by_query?refresh&slices=5
{
  "script": {
    "source": "ctx._source['extra'] = 'test'"
  }
}

POST test/_doc?refresh
{
  "text": "words words",
  "flag": "foo"
}

POST test/_search?filter_path=hits.total
{
  "query": {
    "match": {
      "flag": "foo"
    }
  }
}

POST test/_update_by_query?refresh&conflicts=proceed

POST test/_search?filter_path=hits.total
{
  "query": {
    "match": {
      "flag": "foo"
    }
  }
}

POST _bulk
{ "index" : { "_index" : "test", "_id" : "1" } }
{ "field1" : "value1" }
{ "delete" : { "_index" : "test", "_id" : "2" } }
{ "create" : { "_index" : "test", "_id" : "3" } }
{ "field1" : "value3" }
{ "update" : {"_id" : "1", "_index" : "test"} }
{ "doc" : {"field2" : "value2"} }

POST /_bulk?filter_path=items.*.error
{ "update": {"_id": "5", "_index": "index1"} }
{ "doc": {"my_field": "baz"} }
{ "update": {"_id": "6", "_index": "index1"} }
{ "doc": {"my_field": "baz"} }
{ "update": {"_id": "7", "_index": "index1"} }
{ "doc": {"my_field": "baz"} }


POST _reindex
{
  "source": {
    "index": "my-index-000001"
  },
  "dest": {
    "index": "my-new-index-000001"
  }
}

POST /my-index-000001/_mtermvectors
{
   "docs": [
      {
         "_id": "2",
         "fields": [
            "message"
         ],
         "term_statistics": true
      },
      {
         "_id": "1"
      }
   ]
}

POST /_mtermvectors
{
   "docs": [
      {
         "_index": "my-index-000001",
         "doc" : {
            "message" : "test test test"
         }
      },
      {
         "_index": "my-index-000001",
         "doc" : {
           "message" : "Another test ..."
         }
      }
   ]
}

POST /_query
{
  "query": """
    FROM library
    | EVAL year = DATE_TRUNC(1 YEARS, release_date)
    | STATS MAX(page_count) BY year
    | SORT year
    | LIMIT 5
  """
}


POST /_query/async
{
  "query": """
    FROM library
    | EVAL year = DATE_TRUNC(1 YEARS, release_date)
    | STATS MAX(page_count) BY year
    | SORT year
    | LIMIT 5
  """,
  "wait_for_completion_timeout": "2s"
}

POST /_features/_reset

POST clicklogs/_graph/explore
{
  "query": {
    "match": {
      "query.raw": "midi"
    }
  },
  "vertices": [
    {
      "field": "product"
    }
  ],
  "connections": {
    "vertices": [
      {
        "field": "query.raw"
      }
    ]
  }
}

POST _aliases
{
  "actions": [
    {
      "add": {
        "index": "my-data-stream",
        "alias": "my-alias"
      }
    }
  ]
}

POST /my-index-000001/_disk_usage?run_expensive_tasks=true

POST /my-index-000001/_cache/clear
POST /my-index-000001/_cache/clear?fields=foo,bar
POST /_cache/clear
POST /my-index-000001,my-index-000002/_cache/clear
POST /my-index-000001/_clone/cloned-my-index-000001
POST /my_source_index/_clone/my_target_index

POST /my-index-000001/_close
POST /my-index-000001/_flush
POST /my-index-000001,my-index-000002/_flush
POST /_flush
POST /my-index-000001/_forcemerge
POST /my-index-000001,my-index-000002/_forcemerge
POST /_forcemerge
POST /_dangling/zmM4e0JtBkeUjiHD-MihPQ?accept_data_loss=true
POST /my-index-000001/_open
POST /my-index-000001/_refresh
POST my-data-stream/_rollover
POST my-data-stream/_rollover?lazy
POST /my-index-000001/_shrink/shrunk-my-index-000001
POST /my_source_index/_shrink/my_target_index
{
  "settings": {
    "index.routing.allocation.require._name": null,
    "index.blocks.write": null
  }
}
POST /_index_template/_simulate_index/my-index-000001
POST /_index_template/_simulate/template_1
POST /my-index-000001/_split/split-my-index-000001
{
  "settings": {
    "index.number_of_shards": 2
  }
}
POST /my-index-000001/_unfreeze
POST /my-index-000001/_forcemerge?max_num_segments=5

POST /my-index-000001/_doc?refresh=wait_for
{
  "user_id" : 12345
}

POST _ilm/move/my-index-000001
{
  "current_step": {
    "phase": "new",
    "action": "complete",
    "name": "complete"
  },
  "next_step": {
    "phase": "warm",
    "action": "forcemerge",
    "name": "forcemerge"
  }
}

POST my-index-000001/_ilm/remove
POST my-index-000001/_ilm/retry
POST _ilm/start
POST _ilm/stop
POST /_ilm/migrate_to_data_tiers
{
  "legacy_template_to_delete": "global-template",
  "node_attribute": "custom_attribute_name"
}
POST _inference/completion/openai_chat_completions
{
  "input": "What is Elastic?"
}
POST _inference/rerank/cohere_rerank
{
  "input": ["luke", "like", "leia", "chewy","r2d2", "star", "wars"],
  "query": "star wars main character"
}
POST _inference/sparse_embedding/my-elser-model
{
  "input": "The sky above the port was the color of television tuned to a dead channel."
}

POST _inference/completion/openai-completion/_stream
{
  "input": "What is Elastic?"
}

POST /_ingest/pipeline/my-pipeline-id/_simulate
{
  "docs": [
    {
      "_index": "index",
      "_id": "id",
      "_source": {
        "foo": "bar"
      }
    },
    {
      "_index": "index",
      "_id": "id",
      "_source": {
        "foo": "rab"
      }
    }
  ]
}

POST /_ingest/pipeline/_simulate?verbose=true
{
  "pipeline" :
  {
    "description": "_description",
    "processors": [
      {
        "set" : {
          "field" : "field2",
          "value" : "_value2"
        }
      },
      {
        "set" : {
          "field" : "field3",
          "value" : "_value3"
        }
      }
    ]
  },
  "docs": [
    {
      "_index": "index",
      "_id": "id",
      "_source": {
        "foo": "bar"
      }
    },
    {
      "_index": "index",
      "_id": "id",
      "_source": {
        "foo": "rab"
      }
    }
  ]
}

POST /_ingest/_simulate
{
  "docs": [
    {
      "_index": "my-index",
      "_id": "123",
      "_source": {
        "foo": "bar"
      }
    },
    {
      "_index": "my-index",
      "_id": "456",
      "_source": {
        "foo": "rab"
      }
    }
  ]
}

POST /_license/start_trial?acknowledge=true
POST /_license/start_basic
POST /_license/start_basic?acknowledge=true
POST _ml/set_upgrade_mode?enabled=true&timeout=10m
POST _ml/calendars/planned-outages/events
{
  "events" : [
    {"description": "event 1", "start_time": 1513641600000, "end_time": 1513728000000},
    {"description": "event 2", "start_time": 1513814400000, "end_time": 1513900800000},
    {"description": "event 3", "start_time": 1514160000000, "end_time": 1514246400000}
  ]
}
POST _ml/anomaly_detectors/low_request_rate/_close
POST _ml/anomaly_detectors/_estimate_model_memory
{
  "analysis_config": {
    "bucket_span": "5m",
    "detectors": [
      {
        "function": "sum",
        "field_name": "bytes",
        "by_field_name": "status",
        "partition_field_name": "app"
      }
    ],
    "influencers": [ "source_ip", "dest_ip" ]
  },
  "overall_cardinality": {
    "status": 10,
    "app": 50
  },
  "max_bucket_cardinality": {
    "source_ip": 300,
    "dest_ip": 30
  }
}
POST _ml/anomaly_detectors/low_request_rate/_flush
{
  "calc_interim": true
}
POST _ml/anomaly_detectors/low_request_rate/_forecast
{
  "duration": "10d"
}
POST _ml/anomaly_detectors/low_request_rate/_open
{
  "timeout": "35m"
}
POST _ml/anomaly_detectors/asdsss/_data
POST _ml/anomaly_detectors/total-requests/_reset
POST _ml/anomaly_detectors/total-requests/_reset?wait_for_completion=false
POST _ml/anomaly_detectors/low_request_rate/model_snapshots/1637092688/_revert
{
  "delete_intervening_results": true
}
POST _ml/datafeeds/datafeed-low_request_rate/_start
{
  "start": "2019-04-07T18:22:16Z"
}
POST _ml/datafeeds/datafeed-low_request_rate/_stop
{
  "timeout": "30s"
}
POST _ml/datafeeds/datafeed-test-job/_update
{
  "query": {
    "term": {
      "geo.src": "US"
    }
  }
}
POST _ml/filters/safe_domains/_update
{
  "description": "Updated list of domains",
  "add_items": ["*.myorg.com"],
  "remove_items": ["wikipedia.org"]
}
POST _ml/anomaly_detectors/low_request_rate/_update
{
  "description":"An updated job",
  "detectors": {
    "detector_index": 0,
    "description": "An updated detector description"
  },
  "groups": ["kibana_sample_data","kibana_sample_web_logs"],
  "model_plot_config": {
    "enabled": true
  },
  "renormalization_window_days": 30,
  "background_persist_interval": "2h",
  "model_snapshot_retention_days": 7,
  "results_retention_days": 60
}
POST _ml/anomaly_detectors/it_ops_new_logs/model_snapshots/1491852978/_update
{
  "description": "Snapshot 1",
  "retain": true
}
POST _ml/anomaly_detectors/low_request_rate/model_snapshots/1828371/_upgrade?timeout=45m&wait_for_completion=true
POST _ml/data_frame/_evaluate
{
  "index": "my_analytics_dest_index",
  "evaluation": {
    "outlier_detection": {
      "actual_field": "is_outlier",
      "predicted_probability_field": "ml.outlier_score"
    }
  }
}
POST _ml/data_frame/analytics/_explain
{
  "source": {
    "index": "houses_sold_last_10_yrs"
  },
  "analysis": {
    "regression": {
      "dependent_variable": "price"
    }
  }
}
POST _ml/data_frame/analytics/_preview
{
  "config": {
    "source": {
      "index": "houses_sold_last_10_yrs"
    },
    "analysis": {
      "regression": {
        "dependent_variable": "price"
      }
    }
  }
}
POST _ml/data_frame/analytics/loganalytics/_start
POST _ml/data_frame/analytics/loganalytics/_stop
POST _ml/data_frame/analytics/loganalytics/_update
{
  "model_memory_limit": "200mb"
}
POST _ml/trained_models/elastic__distilbert-base-uncased-finetuned-conll03-english/deployment/cache/_clear
POST _ml/trained_models/lang_ident_model_1/_infer
{
  "docs":[{"text": "The fool doth think he is wise, but the wise man knows himself to be a fool."}]
}
POST _ml/trained_models/elastic__distilbert-base-uncased-finetuned-conll03-english/deployment/_start?wait_for=started&timeout=1m
POST _ml/trained_models/my_model_for_search/deployment/_stop
POST _ml/trained_models/elastic__distilbert-base-uncased-finetuned-conll03-english/deployment/_update
{
  "number_of_allocations": 4
}
POST _query_rules/my-ruleset/_test
POST /my-index-000001/_reload_search_analyzers
POST _rollup/job/sensor/_start
POST _rollup/job/sensor/_stop?wait_for_completion=true&timeout=10s
POST /sales*/_async_search?size=0
POST /my-index-000001/_pit?keep_alive=1m
POST /_search
POST /my-index-000001/_pit?keep_alive=1m&allow_partial_search_results=true
POST _render/template
POST my-index-000001/_search
POST stackoverflow/_terms_enum
POST _application/search_application/my-app/_search
POST _application/search_application/my-app/_render_query
POST /_snapshot/my_repository/my_snapshot/_mount?wait_for_completion=true
POST /my-index/_searchable_snapshots/cache/clear
POST /_security/user/jacknich/_password
POST /_security/realm/default_file/_clear_cache
POST /_security/realm/default_file/_clear_cache?usernames=rdeniro,alpacino
POST /_security/realm/default_file,ldap1/_clear_cache
POST /_security/realm/*/_clear_cache
POST /_security/role/my_admin_role/_clear_cache
POST /_security/role/my_admin_role,my_test_role/_clear_cache
POST /_security/role/*/_clear_cache
POST /_security/privilege/myapp/_clear_cache
POST /_security/privilege/myapp,my-other-app/_clear_cache
POST /_security/privilege/*/_clear_cache
POST /_security/api_key/yVGMr3QByxdh1MSaicYx/_clear_cache
POST /_security/api_key/yVGMr3QByxdh1MSaicYx,YoiMaqREw0YVpjn40iMg/_clear_cache
POST /_security/api_key/*/_clear_cache
POST /_security/service/elastic/fleet-server/credential/token/token1/_clear_cache
POST /_security/service/elastic/fleet-server/credential/token/token1,token2/_clear_cache
POST /_security/service/elastic/fleet-server/credential/token/*/_clear_cache
POST /_security/api_key
POST /_security/role_mapping/mapping1
POST /_security/role/my_admin_role
POST /_security/role/cli_or_drivers_minimal
POST /_security/role
POST /_security/user/jacknich
POST /_security/service/elastic/fleet-server/credential/token/token1
POST /_security/delegate_pki
POST /_security/oauth2/token
POST /_security/oidc/prepare
POST /_security/oidc/authenticate
POST /_security/oidc/logout
POST /_security/api_key/_bulk_update
POST /_security/saml/prepare
POST /_security/saml/authenticate
POST /_security/saml/logout
POST /_security/saml/invalidate
POST /_security/saml/complete_logout
POST /_security/profile/_activate
POST /_security/profile/u_79HkWkwmnBH5gqFKwoxggWPjEBOur1zLPXQPEl1VBW0_0/_disable
POST /_security/profile/u_79HkWkwmnBH5gqFKwoxggWPjEBOur1zLPXQPEl1VBW0_0/_enable
POST /_security/profile/_suggest
POST /_security/profile/u_P_0BMHgaOK3p7k-PFWUCbw9dQ-UFjt01oWJ_Dp2PmPc_0/_data
POST /_security/profile/_has_privileges
POST /_security/cross_cluster/api_key
POST /_snapshot/my_repository/_verify
POST /_snapshot/my_repository/_analyze?blob_count=10&max_blob_size=1mb&timeout=120s
POST /_snapshot/my_repository/_verify
POST /_snapshot/my_repository/_cleanup
POST /_snapshot/my_repository/my_snapshot/_restore
POST /_slm/_execute_retention
POST _slm/start
POST /_slm/stop
POST _sql/close
POST _sql?format=txt
POST _sql/translate
POST _transform/_preview
POST _transform/ecommerce_transform/_reset
POST _transform/ecommerce_transform/_schedule_now
POST _transform/ecommerce_transform/_stop
POST _transform/ecommerce_transform/_start
POST _transform/simple-kibana-ecomm-pivot/_update
POST _transform/_upgrade
POST _watcher/watch/my_watch/_execute
POST _watcher/watch/my_watch/_execute
POST _watcher/_start
POST _watcher/_stop










