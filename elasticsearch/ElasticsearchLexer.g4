lexer grammar ElasticsearchLexer;

options {
    caseInsensitive = true;
}


//'GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'PATCH', 'OPTIONS'
DELETE  : 'DELETE';
GET     : 'GET';
HEAD    : 'HEAD';
OPTIONS : 'OPTIONS';
PATCH   : 'PATCH';
POST    : 'POST';
PUT     : 'PUT';


ABORT                           : 'abort';
ACCEPT_DATA_LOSS                : 'accept_data_loss';
ACCEPT_ENTERPRISE               : 'accept_enterprise';
ACK_                            : '_ack';
ACKNOWLEDGE                     : 'acknowledge';
ACTIONS                         : 'actions';
ACTIVATE_                       : '_activate';
ACTIVE                          : 'active';
ACTIVE_ONLY                     : 'active_only';
ADAPTIVE_SELECTION              : 'adaptive_selection';
ADVANCE_TIME                    : 'end_time';
AE                              : 'ae';
AFTER                           : 'after';
AGGREGATIONS                    : 'aggregations';
ALIAS_                          : '_alias';
ALIASES                         : 'aliases';
ALIASES_                        : '_aliases';
ALL                             : 'all';
ALL_                            : '_all';
ALL_SHARDS                      : 'all_shards';
ALLOCATION                      : 'allocation';
ALLOW_NO_FORECASTS              : 'allow_no_forecasts';
ALLOW_NO_INDICES                : 'allow_no_indices';
ALLOW_NO_MATCH                  : 'allow_no_match';
ALLOW_PARTIAL_SEARCH_RESULTS    : 'allow_partial_search_results';
ALWAYS                          : 'always';
ANALYTICS                       : 'analytics';
ANALYZE_                        : '_analyze';
ANALYZE_WILDCARD                : 'analyze_wildcard';
ANALYZER                        : 'analyzer';
AND                             : 'and';
ANOMALY_DETECTORS               : 'anomaly_detectors';
ANOMALY_SCORE                   : 'anomaly_score';
API_KEY                         : 'api_key';
APPLICATION                     : 'application';
APPLICATION_                    : '_application';
ASC                             : 'asc';
ASSIGNMENT_EXPLANATION          : 'assignment_explanation';
ASYNC                           : 'async';
ASYNC_SEARCH_                   : '_async_search';
AUTHENTICATE                    : 'authenticate';
AUTHENTICATE_                   : '_authenticate';
AUTO                            : 'auto';
AUTO_FOLLOW                     : 'auto_follow';
AUTOSCALING_                     : '_autoscaling';
BASIC_STATUS                    : 'basic_status';
BATCHED_REDUCE_SIZE             : 'batched_reduce_size';
BLOCK                           : 'block';
BLOCK_                          : '_block';
BLOCKS                          : 'blocks';
BREAKER                         : 'breaker';
BREAKERS                        : 'breakers';
BUCKET_SPAN                     : 'bucket_span';
BUCKETS                         : 'buckets';
BUCKETS_COUNT                   : 'buckets.count' | 'bucketsCount' | 'bc';
BUCKETS_TIME_EXP_AVG            : 'buckets.time.exp_avg' | 'bucketsTimeExpAvg' | 'btea';
BUCKETS_TIME_EXP_AVG_HOUR       : 'buckets.time.exp_avg_hour' | 'bucketsTimeExpAvgHour' | 'bteah';
BUCKETS_TIME_MAX                : 'buckets.time.max' | 'bucketsTimeMax' | 'btmax';
BUCKETS_TIME_MIN                : 'buckets.time.min' | 'bucketsTimeMin' | 'btmin';
BUCKETS_TIME_TOTAL              : 'buckets.time.total' | 'bucketsTimeTotal' | 'btt';
BUILTIN_                        : '_builtin';
BULK                            : 'bulk';
BULK_                           : '_bulk';
BULK_UPDATE_                    : '_bulk_update';
BYTES                           : 'bytes';
C_                              : 'c';
CACHE                           : 'cache';
CACHE_                          : '_cache';
CACHE_SIZE                      : 'cache_size';
CALC_INTERIM                    : 'calc_interim';
CALENDARS                       : 'calendars';
CANCEL_                         : '_cancel';
CAPACITY                        : 'capacity';
CAT_                            : '_cat';
CATEGORIES                      : 'categories';
CCR_                            : '_ccr';
CCS_MINIMIZE_ROUNDTRIPS         : 'ccs_minimize_roundtrips';
CENTROID                        : 'centroid';
CERTIFICATES                    : 'certificates';
CHARSET                         : 'charset';
CHECKPOINTS                     : 'checkpoints';
CLEANUP_                        : '_cleanup';
CLEAR                           : 'clear';
CLEAR_                          : '_clear';
CLEAR_CACHE_                    : '_clear_cache';
CLONE_                          : '_clone';
CLOSE                           : 'close';
CLOSE_                          : '_close';
CLOSED                          : 'closed';
CLUSTER                         : 'cluster';
CLUSTER_                        : '_cluster';
COLUMN_NAMES                    : 'column_names';
COMPLETE_LOGOUT                 : 'complete_logout';
COMPLETION                      : 'completion';
COMPLETION_FIELDS               : 'completion_fields';
COMPONENT_TEMPLATE_             : '_component_template';
COMPONENT_TEMPLATES             : 'component_templates';
CONFLICTS                       : 'conflicts';
COORDINATING_ONLY               : 'coordinating_only';
COUNT                           : 'count';
COUNT_                          : '_count';
CPU                             : 'cpu';
CREATE                          : 'create';
CREATE_                         : '_create';
CREATE_TIME                     : 'create_time' | 'ct' | 'createTime';
CREATED_BY                      : 'created_by' | 'createdBy';
CREDENTIAL                      : 'credential';
CROSS_CLUSTER                   : 'cross_cluster';
CURRENT_WATCHES                 : 'current_watches';
D_                              : 'd';
DANGLING_                       : '_dangling';
DATA                            : 'data';
DATA_                           : '_data';
DATA_BUCKETS                    : 'data.buckets' | 'dataBuckets' | 'db';
DATA_EARLIEST_RECORD            : 'data.earliest_record' | 'dataEarliestRecord' | 'der';
DATA_EMPTY_BUCKETS              : 'data.empty_buckets' | 'dataEmptyBuckets' | 'deb';
DATA_FRAME                      : 'data_frame';
DATA_FRAME_ANALYTICS_ID         : 'data_frame_analytics_id' | 'dataFrameAnalyticsId';
DATA_INPUT_BYTES                : 'data.input_bytes' | 'dataInputBytes' | 'dib';
DATA_INPUT_FIELDS               : 'data.input_fields' | 'dataInputFields' | 'dif';
DATA_INPUT_RECORDS              : 'data.input_records' | 'dataInputRecords' | 'dir';
DATA_INVALID_DATES              : 'data.invalid_dates' | 'dataInvalidDates' | 'did';
DATA_LAST                       : 'data.last' | 'dataLast' | 'dl';
DATA_LAST_EMPTY_BUCKET          : 'data.last_empty_bucket' | 'dataLastEmptyBucket' | 'dleb';
DATA_LAST_SPARSE_BUCKET         : 'data.last_sparse_bucket' | 'dataLastSparseBucket' | 'dlsb';
DATA_LATEST_RECORD              : 'data.latest_record' | 'dataLatestRecord' | 'dlr';
DATA_MISSING_FIELDS             : 'data.missing_fields' | 'dataMissingFields' | 'dmf';
DATA_OUT_OF_ORDER_TIMESTAMPS    : 'data.out_of_order_timestamps' | 'dataOutOfOrderTimestamps' | 'doot';
DATA_PROCESSED_FIELDS           : 'data.processed_fields' | 'dataProcessedFields' | 'dpf';
DATA_PROCESSED_RECORDS          : 'data.processed_records' | 'dataProcessedRecords' | 'dpr';
DATA_SPARSE_BUCKETS             : 'data.sparse_buckets' | 'dataSparseBuckets' | 'dsb';
DATA_STREAM_                    : '_data_stream';
DATAFEEDS                       : 'datafeeds';
DEACTIVATE_                     : '_deactivate';
DEBUG                           : 'debug';
DECOMPRESS_DEFINITION           : 'decompress_definition';
DEFAULT_OPERATOR                : 'default_operator';
DEFER_DEFINITION_DECOMPRESSION  : 'defer_definition_decompression';
DEFER_VALIDATION                : 'defer_validation';
DEFINITION                      : 'definition';
DEFINITION_STATUS               : 'definition_status';
DELETE_BY_QUERY_                : '_delete_by_query';
DELETE_DEST_INDEX               : 'delete_dest_index';
DELETE_EXPIRED_DATA_            : '_delete_expired_data';
DELETE_INTERVENING_RESULTS      : 'delete_intermediate_results';
DELETE_USER_ANNOTATIONS         : 'delete_user_annotations';
DELIMITER                       : 'delimiter';
DENSE_VECTOR                    : 'dense_vector';
DEPLOYMENT                      : 'deployment';
DEPLOYMENT_ID                   : 'deployment_id';
DEPRECATIONS                    : 'deprecations';
DESC                            : 'desc';
DESCRIPTION                     : 'description';
DESIRED_BALANCE                 : 'desired_balance';
DESIRED_NODES                   : 'desired_nodes';
DEST_INDEX                      : 'dest_index' | 'destIndex' | 'di';
DETAILED                        : 'detailed';
DETECTOR                        : 'detector';
DF                              : 'df';
DFS_QUERY_THEN_FETCH            : 'dfs_query_then_fetch';
DISABLE_                        : '_disable';
DISCOVERY                       : 'discovery';
DISK                            : 'disk';
DISK_USAGE_                     : '_disk_usage';
DOC                             : 'doc';
DOC_                            : '_doc';
DOCS                            : 'docs';
DOCVALUE_FIELDS                 : 'docvalue_fields';
DOWNSAMPLE_                     : '_downsample';
DRY_RUN                         : 'dry_run';
DURATION                        : 'duration';
ECS_COMPATIBILITY               : 'ecs_compatibility';
EMBED_STACKTRACES               : 'embed_stacktraces';
ENABLE_                         : '_enable';
ENABLED                         : 'enabled';
END                             : 'end';
ENRICH_                         : '_enrich';
ENROLL                          : 'enroll';
EQL_                            : '_eql';
ERROR_TRACE                     : 'error_trace';
ESTIMATE_MODEL_MEMORY_          : '_estimate_model_memory';
EVALUATE_                       : '_evaluate';
EVENT                           : 'event';
EVENTS                          : 'events';
EXACT_BOUNDS                    : 'exact_bounds';
EXCLUDE_GENERATED               : 'exclude_generated';
EXCLUDE_INTERIM                 : 'exclude_interim';
EXECUTE                         : 'execute';
EXECUTE_                        : '_execute';
EXECUTE_RETENTION_              : '_execute_retention';
EXPAND                          : 'expand';
EXPAND_WILDCARDS                : 'expand_wildcards';
EXPIRES_IN                      : 'expires_in';
EXPLAIN                         : 'explain';
EXPLAIN_                        : '_explain';
EXPLORE                         : 'explore';
EXTENT                          : 'extent';
EXTERNAL                        : 'external';
EXTERNAL_GTE                    : 'external_gte';
FAILURE_REASON                  : 'failure_reason' | 'failureReason' | 'fr';
FALSE                           : 'false';
FEATURE_IMPORTANCE_BASELINE     : 'feature_importance_baseline';
FEATURES                        : 'features';
FEATURES_                       : '_features';
FIELD                           : 'field';
FIELD_CAPS_                     : '_field_caps';
FIELD_STATISTICS                : 'field_statistics';
FIELD_USAGE_STATS_              : '_field_usage_stats';
FIELDDATA                       : 'fielddata';
FIELDDATA_FIELDS                : 'fielddata_fields';
FIELDS                          : 'fields';
FILTER_PATH                     : 'filter_path';
FILTERS                         : 'filters';
FIND_STRUCTURE                  : 'find_structure';
FLAT_SETTINGS                   : 'flat_settings';
FLEET_                          : '_fleet';
FLEET_MSEARCH_                  : '_fleet_msearch';
FLEET_SEARCH_                   : '_fleet_search';
FLUSH                           : 'flush';
FLUSH_                          : '_flush';
FOLLOW                          : 'follow';
FOLLOW_INFO                     : 'follow_info';
FORBID_CLOSED_INDICES           : 'forbid_closed_indices';
FORCE                           : 'force';
FORCEMERGE_                     : '_forcemerge';
FORECAST_                       : '_forecast';
FORECASTS_MEMORY_AVG            : 'forecasts.memory.avg' | 'forecastsMemoryAvg' | 'fmavg';
FORECASTS_MEMORY_MAX            : 'forecasts.memory.max' | 'forecastsMemoryMax' | 'fmmax';
FORECASTS_MEMORY_MIN            : 'forecasts.memory.min' | 'forecastsMemoryMin' | 'fmmin';
FORECASTS_MEMORY_TOTAL          : 'forecasts.memory.total' | 'forecastsMemoryTotal' | 'fmt';
FORECASTS_RECORDS_AVG           : 'forecasts.records.avg' | 'forecastsRecordsAvg' | 'fravg';
FORECASTS_RECORDS_MAX           : 'forecasts.records.max' | 'forecastsRecordsMax' | 'frmax';
FORECASTS_RECORDS_MIN           : 'forecasts.records.min' | 'forecastsRecordsMin' | 'frmin';
FORECASTS_RECORDS_TOTAL         : 'forecasts.records.total' | 'forecastsRecordsTotal' | 'frt';
FORECASTS_TIME_AVG              : 'forecasts.time.avg' | 'forecastsTimeAvg' | 'ftavg';
FORECASTS_TIME_MAX              : 'forecasts.time.max' | 'forecastsTimeMax' | 'ftmax';
FORECASTS_TIME_MIN              : 'forecasts.time.min' | 'forecastsTimeMin' | 'ftmin';
FORECASTS_TIME_TOTAL            : 'forecasts.time.total' | 'forecastsTimeTotal' | 'ftt';
FORECASTS_TOTAL                 : 'forecasts.total' | 'forecastsTotal';
FORGET_FOLLOWER                 : 'forget_follower';
FORMAT                          : 'format';
FROM                            : 'from';
FROM_SORT_VALUE                 : 'from_sort_value';
FS                              : 'fs';
FT                              : 'ft';
FULL_COPY                       : 'full_copy';
FULL_ID                         : 'full_id';
FULLY_ALLOCATED                 : 'fully_allocated';
GEOHEX                          : 'geohex';
GEOIP                           : 'geoip';
GEOTILE                         : 'geotile';
GLOBAL_CHECKPOINTS              : 'global_checkpoints';
GPU                             : 'gpu';
GRANT                           : 'grant';
GRAPH_                          : '_graph';
GREEN                           : 'green';
GRID                            : 'grid';
GRID_AGG                        : 'grid_agg';
GRID_PRECISION                  : 'grid_precision';
GRID_TYPE                       : 'grid_type';
GROK                            : 'grok';
GROK_PATTERN                    : 'grok_pattern';
GROUP_BY                        : 'group_by';
GROUPS                          : 'groups';
H_                              : 'h';
HAS_HEADER_ROW                  : 'has_header_row';
HAS_PRIVILEGES_                 : '_has_privileges';
HEALTH                          : 'health';
HEALTH_REPORT_                  : '_health_report';
HEAP_SIZE                       : 'heap_size' | 'modelHeapSize' | 'hs';
HELP                            : 'help';
HIDDEN_                         : 'hidden';
HIGH                            : 'high';
HOT_THREADS                     : 'hot_threads';
HTTP                            : 'http';
HUMAN                           : 'human';
HYPERPARAMETERS                 : 'hyperparameters';
ID                              : 'id';
IDS                             : 'ids';
IF_PRIMARY_TERM                 : 'if_primary_term';
IF_SEQ_NO                       : 'if_seq_no';
IF_VERSION                      : 'if_version';
IGNORE_IDLE_THREADS             : 'ignore_idle_threads';
IGNORE_THROTTLED                : 'ignore_throttled';
IGNORE_UNAVAILABLE              : 'ignore_unavailable';
ILM                             : 'ilm';
ILM_                            : '_ilm';
IMMEDIATE                       : 'immediate';
INCLUDE                         : 'include';
INCLUDE_DEFAULTS                : 'include_defaults';
INCLUDE_DISK_INFO               : 'include_disk_info';
INCLUDE_REPOSITORY              : 'include_repository';
INCLUDE_SEGMENT_FILE_SIZES      : 'include_segment_file_sizes';
INCLUDE_UNLOADED_SEGMENTS       : 'include_unloaded_segments';
INCLUDE_UNMAPPED                : 'include_unmapped';
INCLUDE_YES_DECISIONS           : 'include_yes_decisions';
INDEX                           : 'index';
INDEX_DETAILS                   : 'index_details';
INDEX_NAMES                     : 'index_names';
INDEX_SETTING                   : 'index-setting';
INDEX_TEMPLATE_                 : '_index_template';
INDEXING                        : 'indexing';
INDEXING_PRESSURE               : 'indexing_pressure';
INDICES                         : 'indices';
INFER_                          : '_infer';
INFERENCE_                      : '_inference';
INFLUENCER_SCORE                : 'influencer_score';
INFLUENCERS                     : 'influencers';
INFO                            : 'info';
INFO_                           : '_info';
INGEST                          : 'ingest';
INGEST_                         : '_ingest';
INGEST_COUNT                    : 'ingest.count' | 'ingestCount' | 'ic';
INGEST_CURRENT                  : 'ingest.current' | 'ingestCurrent' | 'icurr';
INGEST_FAILED                   : 'ingest.failed' | 'ingestFailed' | 'if';
INGEST_PIPELINES                : 'ingest.pipelines' | 'ingestPipelines' | 'ip';
INGEST_TIME                     : 'ingest.time' | 'ingestTime' | 'it';
INTERNAL                        : 'internal';
INTERNAL_                       : '_internal';
INTERVAL                        : 'interval';
INVALIDATE                      : 'invalidate';
JOB                             : 'job';
JOB_ID                          : 'job_id';
JOBS                            : 'jobs';
JSON                            : 'json';
JVM                             : 'jvm';
KEEP_ALIVE                      : 'keep_alive';
KEEP_ON_COMPLETION              : 'keep_on_completion';
KIBANA                          : 'kibana';
KNN_SEARCH_                     : '_knn_search';
L_                              : 'l';
LANG                            : 'lang';
LANGUID                         : 'languid';
LATEST_                         : '_latest';
LENIENT                         : 'lenient';
LEVEL                           : 'level';
LICENSE                         : 'license';
LICENSE_                        : '_license';
LIFECYCLE_                      : '_lifecycle';
LINE_MERGE_SIZE_LIMIT           : 'line_merge_size_limit';
LINES_TO_SAMPLE                 : 'lines_to_sample';
LOCAL                           : 'local';
LOCAL_                          : '_local';
LOGOUT                          : 'logout';
LOGSTASH_                       : '_logstash';
LOW                             : 'low';
M_                              : 'm';
MAPPING_                        : '_mapping';
MAPPINGS                        : 'mappings';
MASTER                          : 'master';
MASTER_                         : '_master';
MASTER_IS_STABLE                : 'master_is_stable';
MASTER_NODE                     : 'master_node';
MASTER_TIMEOUT                  : 'master_timeout';
MAX_CONCURRENT_SEARCHES         : 'max_concurrent_searches';
MAX_CONCURRENT_SHARD_REQUESTS   : 'max_concurrent_shard_requests';
MAX_DOCS                        : 'max_docs';
MAX_MODEL_MEMORY                : 'max_model_memory';
MAX_NUM_SEGMENTS                : 'max_num_segments';
MB                              : 'mb';
MDCC                            : 'mdcc';
MEM                             : 'mem';
MEMORY                          : 'memory';
MERGE                           : 'merge';
METADATA                        : 'metadata';
METRIC                          : 'metric';
MGET_                           : '_mget';
MIGRATE_                        : '_migrate';
MIGRATE_TO_DATA_TIERS           : 'migrate_to_data_tiers';
MIGRATION_                      : '_migration';
MIN_COMPATIBLE_SHARD_NODE       : 'min_compatible_shard_node';
MIN_SCORE                       : 'min_score';
MISSING                         : 'missing';
ML                              : 'ml';
ML_                             : '_ml';
MODEL_ALIASES                   : 'model_aliases';
MODEL_BUCKET_ALLOCATION_FAILUR  : 'model.bucket_allocation_failure' | 'modelBucketAllocationFailure' | 'mbaf';
MODEL_BY_FIELDS                 : 'model.by_fields' | 'modelByFields' | 'mbf';
MODEL_BYTES                     : 'model.bytes' | 'modelBytes';
MODEL_BYTES_EXCEEDED            : 'model.bytes_exceeded' | 'modelBytesExceeded' | 'mbe';
MODEL_CATEGORIZATION_STATUS     : 'model.categorization_status' | 'modelCategorizationStatus' | 'mcs';
MODEL_CATEGORIZED_DOC_COUNT     : 'model.categorized_doc_count' | 'modelCategorizedDocCount' | 'mcdc';
MODEL_DEAD_CATEGORY_COUNT       : 'model.dead_category_count' | 'modelDeadCategoryCount';
MODEL_FAILED_CATEGORY_COUNT     : 'model.failed_category_count' | 'modelFailedCategoryCount';
MODEL_FREQUENT_CATEGORY_COUNT   : 'model.frequent_category_count' | 'modelFrequentCategoryCount' | 'mfcc';
MODEL_LOG_TIME                  : 'model.log_time' | 'modelLogTime' | 'mlt';
MODEL_MEMORY_LIMIT              : 'model.memory_limit' | 'modelMemoryLimit' | 'mml';
MODEL_MEMORY_STATUS             : 'model.memory_status' | 'modelMemoryStatus' | 'mms';
MODEL_OVER_FIELDS               : 'model.over_fields' | 'modelOverFields' | 'mof';
MODEL_PARTITION_FIELDS          : 'model.partition_fields' | 'modelPartitionFields' | 'mpf';
MODEL_RARE_CATEGORY_COUNT       : 'model.rare_category_count' | 'modelRareCategoryCount' | 'mrcc';
MODEL_SNAPSHOTS                 : 'model_snapshots';
MODEL_TIMESTAMP                 : 'model.timestamp' | 'modelTimestamp' | 'mt';
MODEL_TOTAL_CATEGORY_COUNT      : 'model.total_category_count' | 'modelTotalCategoryCount' | 'mtcc';
MODIFY_                         : '_modify';
MONITORING_                     : '_monitoring';
MOUNT_                          : '_mount';
MOVE                            : 'move';
MSEARCH_                        : '_msearch';
MTERMVECTORS_                   : '_mtermvectors';
MULTIFIELD                      : 'multifield';
MVT_                            : '_mvt';
NAME                            : 'name';
NESTED                          : 'nested';
NODE                            : 'node';
NODE_ADDRESS                    : 'node.address' | 'nodeAddress' | 'na';
NODE_EPHEMERAL_ID               : 'node.ephemeral_id' | 'nodeEphemeralId' | 'ne';
NODE_ID                         : 'node.id' | 'nodeId' | 'ni';
NODE_ID_                        : 'node_id';
NODE_IDS                        : 'node_ids';
NODE_NAME                       : 'node.name' | 'nodeName' | 'nn';
NODE_NAMES                      : 'node_names';
NODEATTRS                       : 'nodeattrs';
NODES                           : 'nodes';
NODES_                          : '_nodes';
NONE                            : 'none';
NORMAL                          : 'normal';
NULL_                           : 'null';
NUMBER_OF_ALLOCATION            : 'number_of_allocation';
O_                              : 'o';
OAUTH2                          : 'oauth2';
OFFSET                          : 'offset';
OFFSETS                         : 'offsets';
OIDC                            : 'oidc';
ONLY_ERRORS                     : 'only_errors';
ONLY_EXPUNGE_DELETES            : 'only_expunge_deletes';
ONLY_LOCAL_                     : '_only_local';
ONLY_MANAGED                    : 'only_managed';
ONLY_NODES_                     : '_only_nodes';
OP_TYPE                         : 'op_type';
OPEN                            : 'open';
OPEN_                           : '_open';
OPENED_TIME                     : 'opened_time' | 'ot';
OPERATIONS                      : 'operations' | 'modelOperations';
OR                              : 'or';
ORDER                           : 'order';
OS                              : 'os';
OVERALL_BUCKETS                 : 'overall_buckets';
OVERALL_SCORE                   : 'overall_score';
OWNER                           : 'owner';
P_                              : 'p';
PAGE                            : 'page';
PAINLESS                        : 'painless';
PARENT                          : 'parent';
PARENT_TASK_ID                  : 'parent_task_id';
PARENTS                         : 'parents';
PARTITION_FIELD_VALUE           : 'partition_field_value';
PASSWORD_                       : '_password';
PAUSE                           : 'pause';
PAUSE_FOLLOW                    : 'pause_follow';
PAYLOADS                        : 'payloads';
PENDING_TASKS                   : 'pending_tasks';
PENDING_WATCHES                 : 'pending_watches';
PIPELINE                        : 'pipeline';
PIT_                            : '_pit';
PLUGINS                         : 'plugins';
POINT                           : 'point';
POLICY                          : 'policy';
POPULAR                         : 'popular';
POSITIONS                       : 'positions';
PRE_FILTER_SHARD_SIZE           : 'pre_filter_shard_size';
PREFER_NODES_                   : '_prefer_nodes';
PREFERENCE                      : 'preference';
PREPARE                         : 'prepare';
PRESERVE_EXISTING               : 'preserve_existing';
PRETTY                          : 'pretty';
PREVALIDATE_NODE_REMOVAL        : 'prevalidate_node_removal';
PREVIEW_                        : '_preview';
PRI                             : 'pri';
PRIORITY                        : 'priority';
PRIVILEGE                       : 'privilege';
PRIVILEGES_                     : '_privileges';
PROCEED                         : 'proceed';
PROCESS                         : 'process';
PROCESSOR                       : 'processor';
PROFILE                         : 'profile';
PROGRESS                        : 'progress';
PROMOTE_                        : '_promote';
Q_                              : 'q';
QUERY                           : 'query';
QUERY_                          : '_query';
QUERY_CACHE                     : 'query_cache';
QUERY_RULES_                    : '_query_rules';
QUERY_THEN_FETCH                : 'query_then_fetch';
QUEUE_CAPACITY                  : 'queue_capacity';
QUEUED_WATCHES                  : 'queued_watches';
QUOTE                           : 'quote';
RANK_EVAL_                      : '_rank_eval';
READ                            : 'read';
READ_ONLY                       : 'read_only';
REALM                           : 'realm';
REALM_NAME                      : 'realm_name';
REALTIME                        : 'realtime';
REASSIGN                        : 'reassign';
RECORD_SCORE                    : 'record_score';
RECORDS                         : 'records';
RECOVERY                        : 'recovery';
RECOVERY_                       : '_recovery';
RED                             : 'red';
REFRESH                         : 'refresh';
REFRESH_                        : '_refresh';
REINDEX_                        : '_reindex';
RELOAD_SEARCH_ANALYZERS_        : '_reload_search_analyzers';
RELOAD_SECURE_SETTINGS          : 'reload_secure_settings';
REMOTE_                         : '_remote';
REMOVE                          : 'remove';
RENDER_                         : '_render';
RENDER_QUERY_                   : '_render_query';
REPOSITORIES                    : 'repositories';
REPOSITORIES_METERING_          : '_repositories_metering';
REPOSITORY_INTEGRITY            : 'repository_integrity';
REQUEST                         : 'request';
REQUEST_CACHE                   : 'request_cache';
REQUESTS_PER_SECOND             : 'requests_per_second';
REQUIRE_ALIAS                   : 'require_alias';
REROUTE                         : 'reroute';
RESET_                          : '_reset';
RESET_END                       : 'reset_end';
RESET_START                     : 'reset_start';
RESOLVE_                        : '_resolve';
REST_ACTIONS                    : 'rest_actions';
REST_TOTAL_HITS_AS_INT          : 'rest_total_hits_as_int';
RESTORE_                        : '_restore';
RESULTS                         : 'results';
RESUME                          : 'resume';
RESUME_FOLLOW                   : 'resume_follow';
RETHROTTLE_                     : '_rethrottle';
RETRY                           : 'retry';
RETRY_FAILED                    : 'retry_failed';
RETRY_ON_CONFLICT               : 'retry_on_conflict';
REVERT_                         : '_revert';
REWRITE                         : 'rewrite';
ROLE                            : 'role';
ROLE_MAPPING                    : 'role_mapping';
ROLLOVER_                       : '_rollover';
ROLLUP_                         : '_rollup';
ROLLUP_SEARCH_                  : '_rollup_search';
ROUTING                         : 'routing';
ROUTING_NODES                   : 'routing_nodes';
ROUTING_TABLE                   : 'routing_table';
RUN_EXPENSIVE_TASKS             : 'run_expensive_tasks';
S_                              : 's';
SAML                            : 'saml';
SCHEDULE_NOW_                   : '_schedule_now';
SCRIPT                          : 'script';
SCRIPT_CONTEXT_                 : '_script_context';
SCRIPT_LANGUAGE_                : '_script_language';
SCRIPTS_                        : '_scripts';
SCROLL                          : 'scroll';
SCROLL_ID                       : 'scroll_id';
SCROLL_SIZE                     : 'scroll_size';
SEARCH                          : 'search';
SEARCH_                         : '_search';
SEARCH_APPLICATION              : 'search_application';
SEARCH_BUCKET_AVG               : 'search.bucket_avg' |'searchBucketAvg' | 'sba';
SEARCH_COUNT                    : 'search.count' | 'searchCount' | 'sc';
SEARCH_EXP_AVG_HOUR             : 'search.exp_avg_hour' | 'searchExpAvgHour' | 'seah';
SEARCH_SHARDS_                  : '_search_shards';
SEARCH_TIME                     : 'search.time' | 'searchTime' | 'st';
SEARCH_TIMEOUT                  : 'search_timeout';
SEARCH_TYPE                     : 'search_type';
SEARCHABLE_SNAPSHOTS_           : '_searchable_snapshots';
SECRET                          : 'secret';
SECRET_                         : '_secret';
SECURITY_                       : '_security';
SEGMENTS                        : 'segments';
SEGMENTS_                       : '_segments';
SEQ_NO_PRIMARY_TERM             : 'seq_no_primary_term';
SERVICE                         : 'service';
SET_UPGRADE_MODE                : 'set_upgrade_mode';
SETTINGS                        : 'settings';
SETTINGS_                       : '_settings';
SHARD_STATS                     : 'shard_stats';
SHARD_STORES_                   : '_shard_stores';
SHARDS                          : 'shards';
SHARDS_                         : '_shards';
SHARDS_AVAILABILITY             : 'shards_availability';
SHARDS_CAPACITY                 : 'shards_capacity';
SHARED_CACHE                    : 'shared_cache';
SHOULD_TRIM_FIELDS              : 'should_trim_fields';
SHRINK_                         : '_shrink';
SHUTDOWN                        : 'shutdown';
SIMULATE_                       : '_simulate';
SIMULATE_INDEX_                 : '_simulate_index';
SIZE                            : 'size';
SKIP_TIME                       : 'skip_time';
SLICES                          : 'slices';
SLM                             : 'slm';
SLM_                            : '_slm';
SLM_POLICY_FILTER               : 'slm_policy_filter';
SNAPSHOT                        : 'snapshot';
SNAPSHOT_                       : '_snapshot';
SNAPSHOTS                       : 'snapshots';
SORT                            : 'sort';
SOURCE_                         : '_source';
SOURCE_EXCLUDES_                : '_source_excludes';
SOURCE_INCLUDES_                : '_source_includes';
SOURCE_INDEX                    : 'source_index' | 'sourceIndex' | 'si';
SPLIT_                          : '_split';
SQL_                            : '_sql';
SSL_                            : '_ssl';
START                           : 'start';
START_                          : '_start';
START_BASIC                     : 'start_basic';
START_TRIAL                     : 'start_trial';
STARTED                         : 'started';
STARTING                        : 'starting';
STATE                           : 'state';
STATS                           : 'stats';
STATS_                          : '_stats';
STATUS                          : 'status';
STATUS_                         : '_status';
STOP                            : 'stop';
STOP_                           : '_stop';
STORAGE                         : 'storage';
STORE                           : 'store';
STORED_FIELDS                   : 'stored_fields';
SUGGEST_                        : '_suggest';
SUGGEST_FIELD                   : 'suggest_field';
SUGGEST_MODE                    : 'suggest_mode';
SUGGEST_SIZE                    : 'suggest_size';
SUGGEST_TEXT                    : 'suggest_text';
SUMMARY                         : 'summary';
SYNONYMS_                       : '_synonyms';
SYSTEM_API_VERSION              : 'system_api_version';
SYSTEM_FEATURES                 : 'system_features';
SYSTEM_ID                       : 'system_id';
T_                              : 't';
TAGS                            : 'tags';
TASKS                           : 'tasks';
TASKS_                          : '_tasks';
TEMPLATE                        : 'template';
TEMPLATE_                       : '_template';
TEMPLATES                       : 'templates';
TERM_STATISTICS                 : 'term_statistics';
TERMINATE_AFTER                 : 'terminate_after';
TERMS_ENUM_                     : '_terms_enum';
TERMVECTORS_                    : '_termvectors';
TEST_GROK_PATTERN               : 'test_grok_pattern';
TXT                            : 'txt';
TEXT_STRUCTURE_                 : '_text_structure';
THREAD_POOL                     : 'thread_pool';
THREADS                         : 'threads';
THREADS_PER_ALLOCATION          : 'threads_per_allocation';
TIME                            : 'time';
TIMEOUT                         : 'timeout';
TIMESTAMP_FIELD                 : 'timestamp_field';
TIMESTAMP_FORMAT                : 'timestamp_format';
TOKEN                           : 'token';
TOP_N                           : 'top_n';
TOTAL_FEATURE_IMPORTANCE        : 'total_feature_importance';
TRACK_SCORES                    : 'track_scores';
TRACK_TOTAL_HITS                : 'track_total_hits';
TRAINED_MODELS                  : 'trained_models';
TRANSFORM_                      : '_transform';
TRANSFORMS                      : 'transforms';
TRANSLATE                       : 'translate';
TRANSLOG                        : 'translog';
TRANSPORT                       : 'transport';
TRIAL_STATUS                    : 'trial_status';
TRUE                            : 'true';
TS                              : 'ts';
TYPE                            : 'type';
TYPE_QUERY_STRING               : 'type_query_string';
TYPED_KEYS                      : 'typed_keys';
TYPES                           : 'types';
UNFOLLOW                        : 'unfollow';
UNFREEZE_                       : '_unfreeze';
UPDATE_                         : '_update';
UPDATE_BY_QUERY_                : '_update_by_query';
UPGRADE_                        : '_upgrade';
URGENT                          : 'urgent';
USAGE                           : 'usage';
USER                            : 'user';
USERNAME                        : 'username';
USERNAMES                       : 'usernames';
V_                              : 'v';
VALIDATE_                       : '_validate';
VERBOSE                         : 'verbose';
VERIFY                          : 'verify';
VERIFY_                         : '_verify';
VERSION                         : 'version';
VERSION_TYPE                    : 'version_type';
VOCABULARY                      : 'vocabulary';
VOTING_CONFIG_EXCLUSIONS        : 'voting_config_exclusions';
VOTING_ONLY                     : 'voting_only';
WAIT                            : 'wait';
WAIT_FOR                        : 'wait_for';
WAIT_FOR_ACTIVE_SHARDS          : 'wait_for_active_shards';
WAIT_FOR_ADVANCE                : 'wait_for_advance';
WAIT_FOR_CHECKPOINT             : 'wait_for_checkpoint';
WAIT_FOR_CHECKPOINTS            : 'wait_for_checkpoints';
WAIT_FOR_COMPLETION             : 'wait_for_completion';
WAIT_FOR_COMPLETION_TIMEOUT     : 'wait_for_completion_timeout';
WAIT_FOR_EVENTS                 : 'wait_for_events';
WAIT_FOR_INDEX                  : 'wait_for_index';
WAIT_FOR_METADATA_VERSION       : 'wait_for_metadata_version';
WAIT_FOR_NO_INITIALIZING_SHARDS : 'wait_for_no_initializing_shards';
WAIT_FOR_NO_RELOCATING_SHARDS   : 'wait_for_no_relocating_shards';
WAIT_FOR_NODES                  : 'wait_for_nodes';
WAIT_FOR_REMOVAL                : 'wait_for_removal';
WAIT_FOR_STATUS                 : 'wait_for_status';
WAIT_FOR_TIMEOUT                : 'wait_for_timeout';
WAIT_IF_ONGOING                 : 'wait_for_ongoing';
WARMER                          : 'warmer';
WATCH                           : 'watch';
WATCHER_                        : '_watcher';
WATCHES                         : 'watches';
WITH_LABELS                     : 'with_labels';
WITH_LIMITED_BY                 : 'with_limited_by';
WITH_PROFILE_UID                : 'with_profile_uid';
WRITE                           : 'write';
WRITE_INDEX_ONLY                : 'write_index_only';
XPACK_                          : '_xpack';
YAML                            : 'yaml';
YELLOW                          : 'yellow';
INCLUDE_REMOTES: 'include_remotes';
NAMES:'names';
EXTERNAL_IDS:'external_ids';
LAZY:'lazy';
STREAM_: '_stream';
IP_LOCATION:'ip_location';
DATABASE:'database';
RULE_:'_rule';
TEST_:'_test';
BLOB_COUNT:'blob_count';
MAX_BLOB_SIZE:'max_blob_size';
CSV:'csv';
TSV:'tsv';
CBOR:'cbor';
SMILE:'smile';


AMPERSAND       : '&';
ASTERISK        : '*';
BAR             : '|';
COLON           : ':';
COMMA           : ',';
DOT             : '.';
EQUALS          : '=';
GREATER_THAN_OP : '>';
LEFT_BRACE      : '{';
LEFT_BRACKET    : '[';
LEFT_PAREN      : '(';
LESS_THAN_OP    : '<';
MINUS_SIGN      : '-';
PLUS_SIGN       : '+';
QUESTION        : '?';
RIGHT_BRACE     : '}';
RIGHT_BRACKET   : ']';
RIGHT_PAREN     : ')';
SEMICOLON       : ';';
SLASH           : '/';

B_: 'b';
KB: 'kb';
GB: 'gb';
TB: 'tb';
PB: 'pb';

NANOS: 'nanos';
MICROS:'micros';
MS:'ms';

//COMPARE_W            : 'ge' | 'le' | 'gt' | 'lt';
//UNIT_LESS_QUANTITIES : 'k' | M_ | 'g' | T_ | P_;
//DISTANCE_UNITS       : 'mi' | 'miles' | 'yd' | 'yards' | FT | 'feet' | 'in' | 'inch' | 'km' | 'kilometers'
//                       | M_ | 'meters' | 'cm' | 'centimeters' | 'mm' | 'millimeters'
//                       | 'NM' | 'nmi' | 'nauticalmiles';


DQUOTA_STRING : '"' (~('"' | '\r' | '\n') | '"' '"')* '"';
TQUOTA_STRING : '"""' .*? '"""';

IP: INTEGER '.' INTEGER '.' INTEGER '.' (INTEGER | '*');
INTEGER       : '-'?Digit+;
DECIMAL        : INTEGER '.' Digit+;

SPACES              : [ \t\r\n]+                                 -> skip;
//MULTI_LINE_COMMENT  : '/*' .*? '*/'                              -> channel(HIDDEN);
SINGLE_LINE_COMMENT : ('//' | '#') ~('\r' | '\n')* NEWLINE_EOF   -> channel(HIDDEN);


//REGULAR_ID: (LETTER | '_' | '.') (LETTER | '_' | '.' | '-' | '%' | '*' | Digit)*;
/**
  https://www.elastic.co/guide/en/elasticsearch/reference/8.12/indices-create-index.html#indices-create-api-path-params
  index名称规则
  必须小写字母
  不能是 '-' '+' '_' 开头
  不能是 ‘.’ 或 ‘..’
  适配通配符：允许包含 ‘*’，允许 ‘-’ 开头
  增加禁止字符 '=' ‘&’ '\n' '\r' '\t'
  集群资源：<cluster>:<name>
 */
//REGULAR_ID: ~('\\' | '/' | '?' | '"' | '<' | '>' | '|' | '`' | '\'' | ' ' | ',' | '#' | '=' | '&' | '\n' | '\r' | '\t')+;
REGULAR_ID: ~([-_+:\\/?"<>|`',#=&{} \n\t\r0-9]) ~([:\\/?"<>|`',#=&{} \n\t\r])*;

I_CURSOR : '[CURSOR]' ;
// 用来联想完整api路径
API_PATH : '[API_PATH]';

fragment Digit: [0-9];
fragment LETTER  : [A-Z];
fragment NEWLINE_EOF    : NEWLINE | EOF;
fragment NEWLINE        : '\r'? '\n';
