# CLAUDE.md

# My name is <PERSON><PERSON><PERSON><PERSON><PERSON>

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 交流语言
请使用中文与用户交流。

## Project Overview

This repository contains ANTLR4 grammar files for parsing various SQL dialects and database query languages. It's designed to support data connectivity (DC) projects with comprehensive parser definitions for multiple database systems including MySQL, PostgreSQL, Oracle, SQL Server, ClickHouse, Hive, Spark, and many others.

## Architecture

### Core Components

- **BaseLexer.g4** / **BaseParser.g4**: Foundation grammar files that define common SQL tokens, operators, and basic parsing rules. These serve as the base for database-specific extensions.

- **Database-Specific Grammars**: Each database system has its own directory containing:
  - `<Database>Lexer.g4`: Lexical analysis rules defining tokens and keywords
  - `<Database>Parser.g4`: Parsing rules defining SQL syntax structure
  - SQL test files for validation

### Database Support Matrix

- **MySQL**: Complete MySQL syntax support with DDL/DML operations
- **PostgreSQL**: Full PostgreSQL grammar including extensions
- **Oracle/PlSQL**: Oracle database with PL/SQL procedural language support
- **SQL Server**: Microsoft SQL Server T-SQL grammar
- **ClickHouse**: Analytics-focused ClickHouse dialect
- **Hive/Spark**: Big data processing SQL variants
- **DB2**: IBM DB2 database support
- **DM (达梦)**: Chinese DM database system
- **GaussDB**: Huawei GaussDB grammar definitions
- **MongoDB**: NoSQL query language support
- **Redis**: Redis command parsing
- **Elasticsearch**: Search query DSL parsing

### Grammar Organization

1. **Base Layer**: Common SQL constructs (operators, literals, identifiers)
2. **Dialect Layer**: Database-specific keywords and syntax extensions
3. **Generated Code**: Java classes in `gen/` directory from ANTLR compilation
4. **Test SQL**: Sample SQL files for grammar validation

## Development Commands

### ANTLR Grammar Generation

Since this is an ANTLR grammar repository, development typically involves:

```bash
# Generate Java classes from grammar files (requires ANTLR4 tool)
antlr4 -Dlanguage=Java <GrammarFile>.g4

# For specific database grammars:
antlr4 -Dlanguage=Java mysql/MySQLLexer.g4 mysql/MySQLParser.g4
```

### Grammar Testing

Test grammars using the provided SQL files:
- Each database directory contains sample SQL files
- Use ANTLR TestRig for grammar validation:
```bash
java org.antlr.v4.gui.TestRig <Grammar> <StartRule> -gui < test.sql
```

## File Structure Patterns

- `<database>/`: Database-specific grammar directory
- `<database>/<Database>Lexer.g4`: Lexer grammar file
- `<database>/<Database>Parser.g4`: Parser grammar file  
- `<database>/sql/`: Test SQL files for the database
- `gen/`: Generated Java parser classes
- `*-dcp/`: Data connectivity platform specific variants

## Grammar Development Guidelines

### Base Grammar Usage
- Always extend from `BaseLexer` and `BaseParser` when possible
- Use `options { tokenVocab = BaseLexer; }` in parser grammars
- Maintain consistency with base token definitions

### Naming Conventions
- Tokens end with underscore: `SELECT_`, `FROM_`, `WHERE_`
- Parser rules use camelCase: `selectStatement`, `whereClause`
- Database prefixes for specific implementations: `MySQLLexer`, `PostgreSQLParser`

### Testing Strategy
- Include comprehensive SQL test files covering all grammar rules
- Test both valid and edge-case SQL constructs
- Validate against real-world database queries

## Branch Information

- **Current branch**: `feat-parser` (feature branch for parser enhancements)
- **Main branch**: `master`
- Recent commits focus on parsing performance optimizations for MySQL and DB2

This repository serves as the core grammar foundation for DC (Data Connectivity) projects, enabling SQL parsing across diverse database ecosystems.