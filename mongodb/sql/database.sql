
db.aggregate( [ {
   $currentOp : { allUsers: true, idleConnections: true } }, {
   $match : { shard: "shard01" }
   }
] )

db.dropDatabase()

db.createCollection(
   "temperatureSensor",
   { changeStreamPreAndPostImages: { enabled: true } }
);

db.createCollection(
   "users",
   { storageEngine: { wiredTiger: { configString: "<option>=<setting>" } } }
)

db.createView(
   "firstYears",
   "students",
   [ { $match: { year: 1 } } ]
)


db.createView(
   "budgetView", "budget",
   [ {
      $match: {
         $expr: {
            $not: {
               $eq: [ { $setIntersection: [ "$allowedRoles", "$$USER_ROLES.role" ] }, [] ]
            }
         }
      }
   } ]
)
