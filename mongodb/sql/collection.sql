db.collection.find( { qty: { $gt: 4 } } );
db.bios.find()
db.bios.find( { _id: 5 } )
db.bios.find( { "name.last": "<PERSON>" } )
db.bios.find(
   { _id: { $in: [ 5, ObjectId("507c35dd8fada716c89d0013") ] } }
)
db.bios.find( { birth: { $gt: new Date('1950-01-01') } } )
db.bios.find(
   { "name.last": { $regex: /^N/ } }
)
db.bios.find( { birth: { $gt: new Date('1940-01-01'), $lt: new Date('1960-01-01') } } )
db.bios.find( {
   birth: { $gt: new Date('1920-01-01') },
   death: { $exists: false }
} )
db.monthlyBudget.find( { $expr: { $gt: [ "$spent" , "$budget" ] } } )
db.bios.find(
    { name: { first: "<PERSON><PERSON><PERSON>", last: "<PERSON><PERSON><PERSON>" } }
)
db.bios.find(
   {
     "name.first": "<PERSON><PERSON><PERSON>",
     "name.last": "<PERSON><PERSON><PERSON>"
   }
)
db.monthlyBudget.insertMany( [
   { _id : 1, category : "food", budget : 400, spent : 450 },
   { _id : 2, category : "drinks", budget : 100, spent : 150 },
   { _id : 3, category : "clothes", budget : 100, spent : 50 },
   { _id : 4, category : "misc", budget : 500, spent : 300 },
   { _id : 5, category : "travel", budget : 200, spent : 650 }
] )
db.bios.find( { contribs: "UNIX" } )
db.bios.find( { contribs: { $in: [ "ALGOL", "Lisp" ]} } )
db.bios.find( { contribs: { $all: [ "ALGOL", "Lisp" ] } } )
db.bios.find( { contribs: { $size: 4 } } )
db.bios.find(
   { "awards.award": "Turing Award" }
)
db.bios.find(
   { awards: { $elemMatch: { award: "Turing Award", year: { $gt: 1980 } } } }
)
db.testbson.find( {}, {}, { bsonRegExp: true } )
db.bios.find( { }, { name: 1, contribs: 1 } )
db.bios.find(
   { contribs: 'OOP' },
   { 'name.first': 0, birth: 0 }
)
db.bios.find(
   { },
   { name: 1, contribs: 1, _id: 0 }
)
db.bios.find(
   { },
   { _id: 0, 'name.last': 1, contribs: { $slice: 2 } } )
db.bios.find(
   { },
   { _id: 0, name: { last: 1 }, contribs: { $slice: 2 } }
)
db.bios.find(
   { },
   {
     _id: 0,
     name: {
        $concat: [
           { $ifNull: [ "$name.aka", "$name.first" ] },
           " ",
           "$name.last"
        ]
     },
     birth: 1,
     contribs: 1,
     awards: { $cond: { if: { $isArray: "$awards" }, then: { $size: "$awards" }, else: 0 } },
     reportDate: { $dateToString: {  date: new Date(), format: "%Y-%m-%d" } },
     reportBy: "hellouser123",
     reportNumber: { $literal: 1 }
   }
)

db.bios.find().sort( { name: 1 } )

db.bios.find().limit( 5 )

db.bios.find().skip( 5 )

db.bios.find( { "name.last": "hopper" } ).collation( { locale: "en_US", strength: 1 } )


db.bios.find().sort( { name: 1 } ).limit( 5 )
db.bios.find().limit( 5 ).sort( { name: 1 } )

db.cakeFlavors.insertMany( [
   { _id: 1, flavor: "chocolate" },
   { _id: 2, flavor: "strawberry" },
   { _id: 3, flavor: "cherry" }
] )

db.cakeFlavors.find(
   { $expr: { $eq: [ "$flavor", "$$targetFlavor" ] } },
   { _id: 0 },
   { let : { targetFlavor: "chocolate" }
} )


db.people.findAndModify({
    query: { name: "Gus", state: "active", rating: 100 },
    sort: { rating: 1 },
    update: { $inc: { score: 1 } },
    upsert: true
})

db.people.findAndModify({
    query: { name: "Pascal", state: "active", rating: 25 },
    sort: { rating: 1 },
    update: { $inc: { score: 1 } },
    upsert: true,
    new: true
})

db.people.findAndModify(
   {
     query: { state: "active" },
     sort: { rating: 1 },
     remove: true
   }
)

db.myColl.findAndModify({
    query: { category: "cafe", status: "a" },
    sort: { category: 1 },
    update: { $set: { status: "Updated" } },
    collation: { locale: "fr", strength: 1 }
});

db.students2.findAndModify( {
   query: {  "_id" : 1 },
   update: [ { $set: { "total" : { $sum: "$grades.grade" } } } ],  // The $set stage is an alias for ``$addFields`` stage
   new: true
} )

db.cakeFlavors.findAndModify( {
   query: {
      $expr: { $eq: [ "$flavor", "$$targetFlavor" ] }
   },
   update: { flavor: "orange" },
   let: { targetFlavor: "cherry" }
} )

db.bios.findOne()

db.bios.findOne(
    { },
    { name: 1, contribs: 1 }
)

db.bios.findOne(
   { contribs: 'OOP' },
   { _id: 0, 'name.first': 0, birth: 0 }
)

db.scores.findOneAndDelete(
   { "name" : "M. Tagnum" }
)

db.scores.findOneAndDelete(
   { name: "A. MacDyver" },
   {
      writeConcern: {
         w : 1,
         j : true,
         wtimeout : 1000
      }
   }
)

db.scores.findOneAndDelete(
   { "name" : "A. MacDyver" },
   { sort : { "points" : 1 } }
)

db.myColl.findOneAndDelete(
   { category: "cafe", status: "a" },
   { collation: { locale: "fr", strength: 1 } }
);

db.scores.findOneAndReplace(
   { "score" : { $lt : 20000 } },
   { "team" : "Observant Badgers", "score" : 20000 }
)

db.scores.findOneAndReplace(
   { "score" : { $lt : 20000 } },
   { "team" : "Observant Badgers", "score" : 20000 },
   { sort: { "score" : 1 } }
)

db.scores.findOneAndReplace(
   { "score" : { $lt : 22250 } },
   { "team" : "Therapeutic Hamsters", "score" : 22250 },
   { sort : { "score" : 1 }, projection: { "_id" : 0, "team" : 1 } }
)

db.myColl.findOneAndReplace(
   { category: "cafe", status: "a" },
   { category: "cafÉ", status: "Replaced" },
   { collation: { locale: "fr", strength: 1 } }
);

db.grades.findOneAndUpdate(
   { "name" : "R. Stiles" },
   { $inc: { "points" : 5 } }
)

db.grades.findOneAndUpdate(
   { "name" : "A. MacDyver" },
   { $inc : { "points" : 5 } },
   { sort : { "points" : 1 } }
)

db.grades.findOneAndUpdate(
   { "name" : "A. MacDyver" },
   { $inc : { "points" : 5 } },
   { sort : { "points" : 1 }, projection: { "assignment" : 1, "points" : 1 } }
)

db.orders.aggregate( [
   { $match: { status: "A" } },
   { $group: { _id: "$cust_id", total: { $sum: "$amount" } } },
   { $sort: { total: -1 } }
] )

db.orders.explain().aggregate( [
   { $match: { status: "A" } },
   { $group: { _id: "$cust_id", total: { $sum: "$amount" } } },
   { $sort: { total: -1 } }
] )

db.orders.aggregate(
                     [
                       { $match: { status: "A" } },
                       { $group: { _id: "$cust_id", total: { $sum: "$amount" } } },
                       { $sort: { total: -1 } },
                       { $limit: 2 }
                     ],
                     {
                       cursor: { batchSize: 0 }
                     }
                   )

db.restaurants.aggregate(
   [ { $match: { status: "A" } }, { $group: { _id: "$category", count: { $sum: 1 } } } ],
   { collation: { locale: "fr", strength: 1 } }
);

db.food.aggregate(
   [ { $sort: { qty: 1 }}, { $match: { category: "cake", qty: 10  } }, { $sort: { type: -1 } } ],
   { hint: { qty: 1, category: 1 } }
)

db.restaurants.aggregate(
   [ { $match: { rating: { $lt: 5 } } } ],
   { readConcern: { level: "majority" } }
)

db.cakeSales.aggregate(
   [
      { $match: {
         $expr: { $gt: [ "$salesTotal", "$$targetTotal" ] }
      } }
   ],
   { let: { targetTotal: 3000 } }
)

db.collection.getIndexes()

db.collection.totalIndexSize()

db.inventory.distinct( "dept" )

db.inventory.distinct( "item.sku", { dept: "A" } )

db.myColl.distinct( "category", {}, { collation: { locale: "fr", strength: 1 } } )

db.collection.dataSize()

db.products.insert( { item: "card", qty: 15 } )

db.products.insert( { _id: 10, item: "box", qty: 20 } )

db.products.insert(
   [
     { _id: 11, item: "pencil", qty: 50, type: "no.2" },
     { item: "pen", qty: 20 },
     { item: "eraser", qty: 25 }
   ]
)

db.products.insert(
   [
     { _id: 20, item: "lamp", qty: 50, type: "desk" },
     { _id: 21, item: "lamp", qty: 20, type: "floor" },
     { _id: 22, item: "bulk", qty: 100 }
   ],
   { ordered: false }
)


db.products.insert(
    { item: "envelopes", qty : 100, type: "Clasp" },
    { writeConcern: { w: 2, wtimeout: 5000 } }
)

db.products.insertMany( [
      { item: "card", qty: 15 },
      { item: "envelope", qty: 20 },
      { item: "stamps" , qty: 30 }
   ] );

 db.products.insertMany( [
      { _id: 10, item: "large box", qty: 20 },
      { _id: 11, item: "small box", qty: 55 },
      { _id: 11, item: "medium box", qty: 30 },
      { _id: 12, item: "envelope", qty: 100},
      { _id: 13, item: "stamps", qty: 125 },
      { _id: 13, item: "tape", qty: 20},
      { _id: 14, item: "bubble wrap", qty: 30}
   ], { ordered: false } );

db.products.insertMany(
      [
         { _id: 10, item: "large box", qty: 20 },
         { _id: 11, item: "small box", qty: 55 },
         { _id: 12, item: "medium box", qty: 30 }
      ],
      { w: "majority", wtimeout: 100 }
   );

db.products.insertOne( { item: "card", qty: 15 } );

db.products.insertOne( { _id: 10, "item" : "packing peanuts", "qty" : 200 } );

db.products.insertOne(
       { "item": "envelopes", "qty": 100, type: "Self-Sealing" },
       { writeConcern: { w : "majority", wtimeout : 100 } }
   );

db.books.update(
   { _id: 1 },
   {
     $inc: { stock: 5 },
     $set: {
       item: "ABC123",
       "info.publisher": "2222",
       tags: [ "software" ],
       "ratings.1": { by: "xyz", rating: 3 }
     }
   }
)

db.books.update(
   { _id: 2 },
   {
     $push: { ratings: { "by" : "jkl", "rating" : 2 } }
   }
)

db.books.update( { _id: 1 }, { $unset: { tags: 1 } } )

db.books.update(
   { stock: { $lte: 10 } },
   { $set: { reorder: true } },
   { multi: true }
)

db.books.update(
   { item: "ZZZ135" },  // Query parameter
   { $set:
      {
         item: "ZZZ135", stock: 5, tags: [ "database" ]  // Replacement document
      }
   },
   { upsert: true }  // Options
)

db.books.update(
   { item: "BLP921" },   // Query parameter
   {                     // Update document
      $set: { reorder: false },
      $setOnInsert: { stock: 10 }
   },
   { upsert: true }      // Options
)

db.books.update(
   { item: "MRQ014", ratings: [2, 5, 3] }, // Query parameter
   [                                       // Aggregation pipeline
      { $replaceRoot: { newRoot: { $mergeObjects: [ { stock: 0 }, "$$ROOT"  ] } } },
      { $set: { avgRating: { $avg: "$ratings" }, tags: [ "fiction", "murder" ], lastModified: "$$NOW" } }
   ],
   { upsert: true }   // Options
)

db.books.update(
   { stock: { $gte: 10 } },        // Query parameter
   {                               // Update document
     $set: { reorder: false, tags: [ "literature", "translated" ] }
   },
   { upsert: true, multi: true }   // Options
)

db.collection.update(
   { "_id.name": "Robert Frost", "_id.uid": 0 },  // Query parameter
   { $set:
      {
         "categories": [ "poet", "playwright" ]  // Replacement document
      }
   },
   { upsert: true }  // Options
)

db.people.update(
   { name: "Andy" },
   { $inc: { score: 1 } },
   {
     upsert: true,
     multi: true
   }
)

db.members.update(
   { },
   [
      { $set: { comments: [ "$commentsSemester1", "$commentsSemester2" ], lastUpdate: "$$NOW" } },
      { $unset: [ "commentsSemester1", "commentsSemester2" ] }
   ],
   { multi: true }
)

db.myColl.update(
   { category: "cafe" },
   { $set: { status: "Updated" } },
   {
     collation: { locale: "fr", strength: 1 },
     multi: true
   }
)

db.employees.updateMany(
   { salary: { $lt: 100000 }, raiseApplied: { $ne: true } },
   { $inc: { salary: 1000 }, $set: { raiseApplied: true } }
)

db.restaurant.updateMany(
      { violations: { $gt: 4 } },
      { $set: { "Review" : true } }
   );

db.students.updateMany(
   { },
   [
      { $set: { comments: [ "$commentsSemester1", "$commentsSemester2" ], lastUpdate: "$$NOW" } },
      { $unset: [ "commentsSemester1", "commentsSemester2" ] }
   ]
)

db.students3.updateMany(
   { },
   [
     { $set: { average : { $trunc: [ { $avg: "$tests" }, 0 ] } , lastUpdate: "$$NOW" } },
     { $set: { grade: { $switch: {
                           branches: [
                               { case: { $gte: [ "$average", 90 ] }, then: "A" },
                               { case: { $gte: [ "$average", 80 ] }, then: "B" },
                               { case: { $gte: [ "$average", 70 ] }, then: "C" },
                               { case: { $gte: [ "$average", 60 ] }, then: "D" }
                           ],
                           default: "F"
     } } } }
   ]
)

db.restaurant.updateMany(
       { "name" : "Pizza Rat's Pizzaria" },
       { $inc: { "violations" : 3}, $set: { "Closed" : true } },
       { w: "majority", wtimeout: 100 }
   );

db.myColl.updateMany(
   { category: "cafe" },
   { $set: { status: "Updated" } },
   { collation: { locale: "fr", strength: 1 } }
);

db.students.updateOne(
   { _id: 1 },
   [
      { $set: { status: "Modified", comments: [ "$commentsSemester1", "$commentsSemester2" ], lastUpdate: "$$NOW" } },
      { $unset: [ "commentsSemester1", "commentsSemester2" ] }
   ]
)

db.myColl.replaceOne(
   { category: "cafe", status: "a" },
   { category: "cafÉ", status: "Replaced" },
   { collation: { locale: "fr", strength: 1 } }
);

db.members.replaceOne(
   { "points": { $lte: 20 }, "status": "P" },
   { "misc1": "using index on status", status: "P", member: "replacement", points: "20"},
   { hint: { status: 1 } }
)

db.restaurantsSort.replaceOne(
   // Find restaurants with a rating of 4
   { rating: 4 },

   // Replace the found restaurant with Clean Eats
   { name: "Clean Eats", rating: 4, violations: 2 },

   // Sort restaurants found by the most violations with a descending sort
   { sort: { violations: -1 } }
)

db.bios.remove( { } )

db.products.remove( { qty: { $gt: 20 } } )

db.products.remove(
    { qty: { $gt: 20 } },
    { writeConcern: { w: "majority", wtimeout: 5000 } }
)

db.products.remove( { qty: { $gt: 20 } }, true )

db.cakeFlavors.remove(
   { $expr: { $eq: [ "$flavor", "$$targetFlavor" ] } },
   { let : { targetFlavor: "strawberry" } }
)

db.orders.deleteOne( { _id: ObjectId("563237a41a4d68582c2509da") } );

db.restaurants.deleteOne(
   { category: "cafe", status: "A" },
   { collation: { locale: "fr", strength: 1 } }
)

db.members.deleteOne(
   { points: { $lte: 20 }, grade: "F" },
   { hint: { grade: 1 } }
)

db.orders.deleteMany( { "stock" : "Brent Crude Futures", "limit" : { $gt : 48.88 } } );

db.restaurants.deleteMany(
   { category: "cafe", status: "A" },
   { collation: { locale: "fr", strength: 1 } }
)

db.members.deleteMany(
   { "points": { $lte: 20 }, "status": "P" },
   { hint: { status: 1 } }
)

db.collection.explain().help()

db.collection.explain().find().help()

db.products.explain().count( { quantity: { $gt: 50 } } )

db.products.explain( "allPlansExecution" ).findAndModify( {
   query: { name: "Tom", state: "active", rating: { $gt: 10 } },
   sort: { rating: 1 },
   update: { $inc: { score: 1 } }
} )

db.products.explain("executionStats").find(
   { quantity: { $gt: 50 }, category: "apparel" }
)

db.products.explain("executionStats").find(
   { quantity: { $gt: 50 }, category: "apparel" }
).sort( { quantity: -1 } ).hint( { category: 1, quantity: -1 } )

db.students.drop()

db.students.drop( { writeConcern: { w: 1 } } )

db.pets.dropIndex( "catIdx" )

db.pets.dropIndex( { "cat" : -1 } )

db.collection.dropIndexes()

db.collection.dropIndexes( { a: 1, b: 1 } )

db.collection.dropIndexes( "a_1_b_1" )

db.collection.dropIndexes( [ "a_1_b_1", "a_1", "a_1__id_-1" ] )

db.myColl.createIndex( { category: 1 }, { collation: { locale: "fr" } } )

db.myColl.createIndex(
   { score: 1, price: 1, category: 1 },
   { collation: { locale: "fr" } } )

db.collection.createIndex( { "state" : 1, "zipcode" : "hashed" } )

db.collection.createIndex(
   { orderDate: 1, category: 1 },
   { name: "date_category_fr", collation: { locale: "fr", strength: 2 } }
)

db.collection.createIndexes(
   [
     {
       "a": 1
     },
     {
       "b": 1
     }
   ],
   {
     unique: true,
     sparse: true,
     expireAfterSeconds: 3600
   }
 )

db.restaurants.createIndexes([{"borough": 1}, {"location": "2dsphere"}])

db.products.createIndexes( [ { "manufacturer": 1}, { "category": 1 } ],
   { collation: { locale: "fr", strength: 2 } })


db.products_catalog.createIndexes(
  [ { "product_attributes.$**" : 1 } ]
)

db.rrecord.renameCollection("record")

db.orders.count()

db.orders.count( { ord_dt: { $gt: new Date('01/01/2012') } } )

db.orders.countDocuments( {}, { hint: "_id_"} )

db.orders.countDocuments( { ord_dt: { $gt: new Date('01/01/2012') } }, { limit: 100 } )

db.orders.estimatedDocumentCount({})




















































































