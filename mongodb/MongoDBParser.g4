parser grammar MongoDBParser;

options {
    tokenVocab = MongoDBLexer;
}

// https://www.mongodb.com/zh-cn/docs/manual/reference/method/

root
    : (collection
    | mgoDatabase
    | use
    | show
    ) ';'? EOF
    ;

collection
    : dbCollectionClause DOT_
    ( findClause
    | findAndModifyClause
    | findOneClause
    | findOneAndDeleteClause
    | findOneAndUpdateClause
    | findOneAndReplaceClause
    | aggregateClause
    | getIndexesClause
    | totalIndexSizeClause
    | distinctClause
    | dataSizeClause
    | insertClause
    | insertOneClause
    | insertManyClause
    | updateClause
    | updateOneClause
    | updateManyClause
    | replaceOneClause
    | removeClause
    | deleteClause
    | deleteOneClause
    | deleteManyClause
    | explainClause
    | dropClause
    | dropIndexClause
    | dropIndexesClause
    | createIndexClause
    | createIndexesClause
    | renameCollectionClause
    | countClause
    | countDocumentsClause
    | estimatedDocumentCountClause
    ) any_value
    ;

mgoDatabase
    : DB DOT_
    ( aggregateClause
    | dropDatabaseClause
    | createCollectionClause
    | createViewClause
    ) any_value
    ;
findClause
    : FIND LP_ rootFilter? (COMMA_ projection (COMMA_ findOptions)? )? RP_ (DOT_ findCursorClause)*
    ;
findAndModifyClause
    : FIND_AND_MODIFY LP_ findAndModifyOptions RP_
    ;
findOneClause
    : FIND_ONE LP_ rootFilter? (COMMA_ projection (COMMA_ findOptions)? )? RP_
    ;
findOneAndDeleteClause
    : FIND_ONE_AND_DELETE LP_ rootFilter (COMMA_ findOneAndDeleteOptions)? RP_
    ;
findOneAndUpdateClause
    : FIND_ONE_AND_UPDATE LP_ rootFilter COMMA_ updateFilter (COMMA_ findOneAndUpdateOptions)? RP_
    ;
findOneAndReplaceClause
    : FIND_ONE_AND_REPLACE LP_ rootFilter COMMA_ replacement (COMMA_ findOneAndReplaceOptions)? RP_
    ;
rootFilter
    : bsonObject
    ;
projection
    : bsonObject
    ;
findOptions
    : commandOperationOptions
    ;
findAndModifyOptions
    : commandOperationOptions
    ;
findOneAndDeleteOptions
    : commandOperationOptions
    ;
updateFilter
    : bsonObject | '[' bsonObject (',' bsonObject)* ','? ']'
    ;
findOneAndUpdateOptions
    : commandOperationOptions
    ;
replacement
    : bsonObject
    ;
findOneAndReplaceOptions
    : commandOperationOptions
    ;
findCursorClause
    : pretty | skip | limit | sort | count | collation | explain | hint | max | min
    ;
pretty
    : PRETTY LP_ RP_
    ;
skip
    : SKIPP LP_ numeric RP_
    ;
limit
    : LIMIT LP_ numeric RP_
    ;
sort
    : SORT LP_ anyObject (',' (string | bsonObject))? RP_
    ;
count
    : COUNT LP_ countOptions? RP_
    ;
countOptions
    : commandOperationOptions
    ;
collation
    : COLLATION LP_ collationOptions RP_
    ;
collationOptions
    : bsonObject
    ;
explain
    : EXPLAIN LP_ anyObject? RP_
    ;
hint
    : HINT LP_ (string | bsonObject)? RP_
    ;
max
    : MAX LP_ bsonObject? RP_
    ;
min
    : MIN LP_ bsonObject? RP_
    ;
getIndexesClause
    : GET_INDEXES '(' ')'
    ;
totalIndexSizeClause
    : TOTAL_INDEX_SIZE '(' ')'
    ;
distinctClause
    : DISTINCT LP_ key=string (',' rootFilter)? (',' commandOperationOptions)? RP_
    ;
commandOperationOptions
    : bsonObject
    ;
dataSizeClause
    : DATA_SIZE '(' ')'
    ;
pipeline
    : bsonArray
    ;
aggregateOptions
    : commandOperationOptions
    ;

explainClause
    : EXPLAIN LP_ verbosity=string? RP_ (DOT_ explainableClause)?
    ;
explainableClause
    : findClause explainFindClause?
    | findAndModifyClause
    | findOneAndDeleteClause
    | findOneAndUpdateClause
    | findOneAndReplaceClause
    | aggregateClause
    | distinctClause
    | removeClause
    | updateClause
    ;
explainFindClause
    : DOT_ (NEXT | FINISH | HELP) LP_ RP_
    ;

use
    : USE identifier
    ;

// https://www.mongodb.com/zh-cn/docs/mongodb-shell/reference/access-mdb-shell-help/
show
    : DB
    | SHOW DBS
    | SHOW TABLES
    | SHOW COLLECTIONS
    | SHOW USERS
    ;


insertClause
    : (INSERT | SAVE) LP_ (bsonObject | bsonArray) (',' insertOneOptions)? RP_
    ;
insertOneClause
    : INSERT_ONE LP_ bsonObject (',' insertOneOptions)? RP_
    ;
insertManyClause
    : INSERT_MANY LP_ bsonArray (',' bulkWriteOptions)? RP_
    ;
insertOneOptions
    : commandOperationOptions
    ;
bulkWriteOptions
    : commandOperationOptions
    ;
updateClause
    : UPDATE LP_ rootFilter ',' updateFilter (',' updateOptions)? RP_
    ;
updateOneClause
    : UPDATE_ONE LP_ rootFilter ',' updateFilter (',' updateOptions)? RP_
    ;
updateManyClause
    : UPDATE_MANY LP_ rootFilter ',' updateFilter (',' updateOptions)? RP_
    ;
updateOptions
    : commandOperationOptions
    ;
replaceOneClause
    : REPLACE_ONE LP_ rootFilter ',' replacement (',' replaceOptions)? RP_
    ;
replaceOptions
    : commandOperationOptions
    ;
removeClause
    : REMOVE LP_ rootFilter (',' (deleteOptions | justOne))? RP_
    ;
justOne
    : boolean | numeric
    ;

deleteClause
    : DELETE LP_ rootFilter (',' deleteOptions)? RP_
    ;
deleteOneClause
    : DELETE_ONE LP_ rootFilter (',' deleteOptions)? RP_
    ;
deleteManyClause
    : DELETE_MANY LP_ rootFilter (',' deleteOptions)? RP_
    ;
deleteOptions
    : commandOperationOptions
    ;
dropClause
    : DROP LP_ dropCollectionOptions? RP_
    ;
dropCollectionOptions
    : commandOperationOptions
    ;
dropIndexClause
    : DROP_INDEX LP_ (bsonObject | string) RP_
    ;
dropIndexesClause
    : DROP_INDEXES LP_ (string | stringArray | bsonObject | bsonArray)? RP_
    ;

dropDatabaseClause
    : DROP_DATABASE LP_ writeConcern? RP_
    ;
writeConcern
    : bsonObject
    ;
createCollectionClause
    : CREATE_COLLECTION LP_ name=string (COMMA_ createCollectionOptions)? RP_
    ;
createCollectionOptions
    : commandOperationOptions
    ;
createViewClause
    : CREATE_VIEW LP_ name=string ',' source=string ',' pipeline (',' createCollectionOptions)? RP_
    ;
createIndexClause
    : CREATE_INDEX LP_ bsonObject (COMMA_ createIndexesOptions)? RP_
    ;
createIndexesOptions
    : commandOperationOptions
    ;
createIndexesClause
    : CREATE_INDEXES LP_ bsonArray (COMMA_ createIndexesOptions)? RP_
    ;
renameCollectionClause
    : RENAME_COLLECTION LP_ string (',' boolean)? RP_
    ;

aggregateClause
    : AGGREGATE LP_ (pipeline (',' aggregateOptions)?)? RP_
    ;

countClause
    : COUNT LP_ (rootFilter (',' countOptions)?)? RP_
    ;
countDocumentsClause
    : COUNT_DOCUMENTS LP_ (rootFilter (',' countDocumentsOptions)?)? RP_
    ;
countDocumentsOptions
    : aggregateOptions
    ;
estimatedDocumentCountClause
    : ESTIMATED_DOCUMENT_COUNT LP_ (rootFilter (',' estimatedDocumentCountOptions)?)? RP_
    ;
estimatedDocumentCountOptions
    : commandOperationOptions
    ;

collectionName
    : identifier | string
    ;
dbCollectionClause
    : DB DOT_ collectionName
    | DB '[' collectionName ']'
    | DB DOT_ GET_COLLECTION LP_ collectionName RP_
    ;

anyObject
    : valueLiteral
    | bsonObject
    | bsonArray
    | regularExpression
    | newObject
    | functionObject
    ;
bsonObject
    : '{' (keyValue | identifier) (',' (keyValue | identifier))* ','? '}'
    | '{' '}'
    ;
bsonArray
    : '[' anyObject (',' anyObject)* ','? ']'
    | '[' ']'
    ;
stringArray
    : '[' string (',' string)* ','? ']'
    | '[' ']'
    ;


keyValue
    : (string | identifier) ':' anyObject
    ;

newObject
    : NEW? identifier '(' any_value ')'
    ;
functionObject
    : FUNCTION '(' any_value ')' '{' any_value '}'
    ;
regularExpression
    : REGULAR_
    ;
valueLiteral
    : numeric | boolean | string | nullValue
    ;
identifier
    : IDENTIFIER | keyword
    ;
nullValue
    : NULL | UNDEFINED
    ;
string
    : SINGLE_QUOTED_TEXT
    | DOUBLE_QUOTED_TEXT
    | BACKSTICK_STRING
    ;

numeric
    : MINUS_? INTEGER_ | MINUS_? NUMBER_ | HEX_DIGIT_ | BIT_NUM_
    ;
boolean
    : TRUE | FALSE
    ;
keyword
    : ADMIN_COMMAND | AGGREGATE | BULK_WRITE | CATCH | COLLATION | COLLECTIONS | COPY_TO | COUNT | COUNT_DOCUMENTS
    | CREATE_COLLECTION | CREATE_INDEX | CREATE_INDEXES | CREATE_VIEW | DATA_SIZE | DB | DBS
    | DELETE | DELETE_MANY | DELETE_ONE
    | DISTINCT | DOLLAR_ALL | DOLLAR_AND | DOLLAR_ELEM_MATCH | DOLLAR_EXISTS | DOLLAR_GT | DOLLAR_GTE | DOLLAR_IN
    | DOLLAR_INC | DOLLAR_LT | DOLLAR_LTE | DOLLAR_OR | DOLLAR_REGEX
    | DOLLAR_SET | DOLLAR_SIZE | DOLLAR_SLICE | DOLLAR_UNSET
    | DROP | DROP_DATABASE | DROP_INDEX | DROP_INDEXES | ENSURE_INDEXE | ESTIMATED_DOCUMENT_COUNT
    | EXPLAIN | FIND | FIND_AND_MODIFY | FIND_ONE | FIND_ONE_AND_DELETE | FIND_ONE_AND_REPLACE | FIND_ONE_AND_UPDATE
    | FINISH | FOREACH | FUNCTION | GET_COLLECTION | GET_INDEXES | GET_SHARD_VERSION
    | HELP | HINT | IF | INSERT | INSERT_MANY | INSERT_ONE | IS_CAPPED | ISO_DATE | LET | LIMIT | MAX | MIN | NEW | NEXT | NUMBER_DECIMAL
    | OBJECT_ID | PRETTY | PRINT_JSON | REMOVE | RENAME_COLLECTION | REPLACE_ONE | RUN_COMMAND | SAVE | SHOW | SKIPP | SORT
    | SYSTEM | SYSTEM_DOT_USERS | SYSTEM_DOT_VERSION | SYSTEM_DOT_VIEWS | TABLES
    | TOTAL_INDEX_SIZE | TRY | TYPE_OF | UPDATE | UPDATE_ONE | UPDATE_MANY | USE | USERS | VAR | VERSION | VIEWS
    ;

any_value: .*?;


