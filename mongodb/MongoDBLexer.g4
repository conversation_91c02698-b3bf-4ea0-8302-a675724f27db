lexer grammar MongoDBLexer;

// Keywords
ADMIN_COMMAND: 'adminCommand';
AGGREGATE: 'aggregate';
BULK_WRITE: 'bulkWrite';
CATCH: 'catch';
COLLATION: 'collation';
COLLECTIONS: 'collections';
COPY_TO: 'copyTo';
COUNT: 'count';
COUNT_DOCUMENTS: 'countDocuments';
CREATE_COLLECTION: 'createCollection';
CREATE_INDEX: 'createIndex';
CREATE_INDEXES: 'createIndexes';
CREATE_VIEW: 'createView';
DATA_SIZE: 'dataSize';
DB: 'db';
DBS: 'dbs';
DELETE: 'delete';
DELETE_MANY: 'deleteMany';
DELETE_ONE: 'deleteOne';
DISTINCT: 'distinct';
DOLLAR_ALL: '$all';
DOLLAR_AND: '$and';
DOLLAR_ELEM_MATCH: '$elemMatch';
DOLLAR_EXISTS: '$exists';
DOLLAR_GT: '$gt';
DOLLAR_GTE: '$gte';
DOLLAR_IN: '$in';
DOLLAR_INC: '$inc';
DOLLAR_LT: '$lt';
DOLLAR_LTE: '$lte';
DOLLAR_OR: '$or';
DOLLAR_REGEX: '$regex';
DOLLAR_SET: '$set';
DOLLAR_SIZE: '$size';
DOLLAR_SLICE: '$slice';
DOLLAR_UNSET: '$unset';
DROP: 'drop';
DROP_DATABASE: 'dropDatabase';
DROP_INDEX: 'dropIndex';
DROP_INDEXES: 'dropIndexes';
ENSURE_INDEXE: 'ensureIndex';
ESTIMATED_DOCUMENT_COUNT: 'estimatedDocumentCount';
EXPLAIN: 'explain';
FIND: 'find';
FIND_AND_MODIFY: 'findAndModify';
FIND_ONE: 'findOne';
FIND_ONE_AND_DELETE: 'findOneAndDelete';
FIND_ONE_AND_REPLACE: 'findOneAndReplace';
FIND_ONE_AND_UPDATE: 'findOneAndUpdate';
FINISH: 'finish';
FOREACH: 'forEach';
FUNCTION: 'function';
GET_COLLECTION: 'getCollection';
GET_INDEXES: 'getIndexes';
GET_SHARD_VERSION: 'getShardVersion';
HELP: 'help';
HINT: 'hint';
IF: 'if';
INSERT: 'insert';
INSERT_MANY: 'insertMany';
INSERT_ONE: 'insertOne';
IS_CAPPED: 'isCapped';
ISO_DATE: 'ISODate';
LET: 'let';
LIMIT: 'limit';
MAX: 'max';
MIN: 'min';
NEW: 'new';
NEXT: 'next';
NUMBER_DECIMAL: 'NumberDecimal';
OBJECT_ID: 'ObjectId';
PRETTY: 'pretty';
PRINT_JSON: 'printjson';
REMOVE: 'remove';
RENAME_COLLECTION: 'renameCollection';
REPLACE_ONE: 'replaceOne';
RUN_COMMAND: 'runCommand';
SAVE: 'save';
SHOW: 'show';
SKIPP: 'skip';
SORT: 'sort';
SYSTEM: 'system';
SYSTEM_DOT_USERS: SYSTEM DOT_ USERS;
SYSTEM_DOT_VERSION: SYSTEM DOT_ VERSION;
SYSTEM_DOT_VIEWS: SYSTEM DOT_ VIEWS;
TABLES: 'tables';
TOTAL_INDEX_SIZE: 'totalIndexSize';
TRY: 'try';
TYPE_OF: 'typeof';
UPDATE: 'update';
UPDATE_ONE: 'updateOne';
UPDATE_MANY: 'updateMany';
USE: 'use';
USERS: 'users';
VAR: 'var';
VERSION: 'version';
VIEWS: 'views';

FALSE: 'false';
TRUE: 'true';
NULL: 'null';
UNDEFINED: 'undefined';


AMPERSAND_           : '&';
AND_                 : '&&';
ARROW_               : '=>';
ASTERISK_            : '*';
BACKSLASH_           : '\\';
BQ_                  : '`';
CARET_               : '^';
COLON_               : ':';
COMMA_               : ',';
DEQ_                 : '==';
DOLLAR_              : '$';
DOT_                 : '.';
DQ_                  : '"';
EQ_                  : '=';
GT_                  : '>';
GTE_                 : '>=';
LBE_                 : '{';
LBT_                 : '[';
LP_                  : '(';
LT_                  : '<';
LTE_                 : '<=';
MINUS_               : '-';
MOD_                 : '%';
NEQ_                 : '!=';
NOT_                 : '!';
OR_                  : '||';
PLUS_                : '+';
POUND_               : '#';
QUESTION_            : '?';
RBE_                 : '}';
RBT_                 : ']';
RP_                  : ')';
SEMI_                : ';';
SIGNED_LEFT_SHIFT_   : '<<';
SIGNED_RIGHT_SHIFT_  : '>>';
SLASH_               : '/';
SQ_                  : '\'';
TILDE_               : '~';
VERTICAL_BAR_        : '|';
UL_                  : '_';


SINGLE_QUOTED_TEXT: (SQ_ ('\\'. | '\'\'' | ~('\'' | '\\'))* SQ_);
DOUBLE_QUOTED_TEXT: (DQ_ ( '\\'. | '""' | ~('"'| '\\') )* DQ_);
BACKSTICK_STRING      : '`' ~'`'* '`';

INTEGER_: INT_+;
NUMBER_: INTEGER_? DOT_? INTEGER_ ('E' (PLUS_ | MINUS_)? INTEGER_)?;
HEX_DIGIT_: '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_;
BIT_NUM_: '0b' ('0' | '1')+ | 'B' SQ_ ('0' | '1')+ SQ_;

REGULAR_ : '/' ( ~[/\n\r] | '\\/' )* '/' ('g'|'i'|'m'|'s')* ;


IDENTIFIER: (DOLLAR_ | UL_)? [a-zA-Z] [a-zA-Z0-9_]*;

fragment INT_: [0-9];
fragment HEX_: [0-9A-Fa-f];

// Comments and whitespace
MULTI_LINE_COMMENT: '/*' .*? '*/' -> skip;
SINGLE_LINE_COMMENT: '//' ~('\n'|'\r')* ('\n' | '\r' | EOF) -> skip;
WHITESPACE: [ \u000B\u000C\t\r\n] -> skip;  // '\n' can be part of multiline single query
