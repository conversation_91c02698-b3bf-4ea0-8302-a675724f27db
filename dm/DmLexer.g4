lexer grammar DmLexer;

import PlSqlLexer;

options {
    caseInsensitive = true;
}


NODE                          : 'NODE';
PASSWORD_POLICY               : 'PASSWORD_POLICY';
DISKSPACE                     : 'DISKSPACE';
SESSION_PER_USER              : 'SESSION_PER_USER';
CONNECT_IDLE_TIME             : 'CONNECT_IDLE_TIME';
FAILED_LOGIN_ATTEMPS          : 'FAILED_LOGIN_ATTEMPS';
MEM_SPACE                     : 'MEM_SPACE';
READ_PER_CALL                 : 'READ_PER_CALL';
READ_PER_SESSION              : 'READ_PER_SESSION';
ALLOW_IP                      : 'ALLOW_IP';
NOT_ALLOW_IP                  : 'NOT_ALLOW_IP';
ALLOW_DATETIME                : 'ALLOW_DATETIME';
NOT_ALLOW_DATETIME            : 'NOT_ALLOW_DATETIME';
DOMAIN                        : 'DOMAIN';
HUGE                          : 'HUGE';
GREAT                         : 'GREAT';
MICRO                         : 'MICRO';
CORRUPT                       : 'CORRUPT';
PENDANT                       : 'PENDANT';
AUTO_INCREMENT                : 'AUTO_INCREMENT';
FILLFACTOR                    : 'FILLFACTOR';
NOBRANCH                      : 'NOBRANCH';
CLUSTERBTR                    : 'CLUSTERBTR';
COUNTER                       : 'COUNTER';
LOGIC                         : 'LOGIC';
EQU                           : 'EQU';
RANDOMLY                      : 'RANDOMLY';
FULLY                         : 'FULLY';
FIELDS                        : 'FIELDS';
DELIMITED                     : 'DELIMITED';
RECORDS                       : 'RECORDS';
PARMS                         : 'PARMS';
NULL_STR                      : 'NULL_STR';
CHARACTER_CODE                : 'CHARACTER_CODE';
GBK                           : 'GBK';
UTF_8                         : 'UTF-8';
SINGLE_BYTE                   : 'SINGLE_BYTE';
EUC_KR                        : 'EUC-KR';
VARBINARY                     : 'VARBINARY';
DATETIME                      : 'DATETIME';
TEXT                          : 'TEXT';
LONGVARCHAR                   : 'LONGVARCHAR';
IMAGE                         : 'IMAGE';
STAT                          : 'STAT';
SECTION                       : 'SECTION';
FILESIZE                      : 'FILESIZE';
DELTA                         : 'DELTA';
PARTIITONS                    : 'PARTIITONS';
ROOT                          : 'ROOT';
LOGICLOG                      : 'LOGICLOG';
SPATIAL                       : 'SPATIAL';
LEXER                         : 'LEXER';
CHINESE_LEXER                 : 'CHINESE_LEXER';
CHINESE_VGRAM_LEXER           : 'CHINESE_VGRAM_LEXER';
CHINESE_FP_LEXER              : 'CHINESE_FP_LEXER';
ENGLISH_LEXER                 : 'ENGLISH_LEXER';
DEFAULT_LEXER                 : 'DEFAULT_LEXER';
AMERICAN                      : 'AMERICAN';
ENGLISH                       : 'ENGLISH';
NLS_TIMESTAMP_FORMAT          : 'NLS_TIMESTAMP_FORMAT';
NLS_TIMESTAMP_TZ_FORMAT       : 'NLS_TIMESTAMP_TZ_FORMAT';
NLS_TIME_FORMAT               : 'NLS_TIME_FORMAT';
NLS_TIME_TZ_FORMAT            : 'NLS_TIME_TZ_FORMAT';
SCHINESE_PINYIN_M             : 'SCHINESE_PINYIN_M';
SCHINESE_STROKE_M             : 'SCHINESE_STROKE_M';
SCHINESE_RADICAL_M            : 'SCHINESE_RADICAL_M';
THAI_CI_AS                    : 'THAI_CI_AS';
KOREAN_M                      : 'KOREAN_M';
CASE_SENSITIVE                : 'CASE_SENSITIVE';
GLOBAL_SESSION_PER_USER       : 'GLOBAL_SESSION_PER_USER';
CORRESPONDING                 : 'CORRESPONDING';
UNCOMMITTED                   : 'UNCOMMITTED';
INTENT                        : 'INTENT';
CS                            : 'CS';
CALCULATE                     : 'CALCULATE';
ABSTRACT                      : 'ABSTRACT';
LOGIN                         : 'LOGIN';
LOGOUT                        : 'LOGOUT';
SERERR                        : 'SERERR';
TIMER                         : 'TIMER';
CHECKPOIN                     : 'CHECKPOIN';
ONCE                          : 'ONCE';
PRINT                         : 'PRINT';
LOCAL_CODE                    : 'LOCAL_CODE';
CONVERT_MODE                  : 'CONVERT_MODE';
BYTES_IN_CHAR                 : 'BYTES_IN_CHAR';
DB_TYPE                       : 'DB_TYPE';
DATA_CHARSET                  : 'DATA_CHARSET';
CASE_OPT                      : 'CASE_OPT';
ROLLBACK_OPTION               : 'ROLLBACK_OPTION';
LSN                           : 'LSN';
PRETTY                        : 'PRETTY';
WRAPPER                       : 'WRAPPER';
UNCONDITIONAL                 : 'UNCONDITIONAL';
CONDITIONAL                   : 'CONDITIONAL';
JSONB                         : 'JSONB';
LEFTARG                       : 'LEFTARG';
RIGHTARG                      : 'RIGHTARG';
RECURSIVE                     : 'RECURSIVE';
LARGE                         : 'LARGE';
LISTAGG2                      : 'LISTAGG2';
TOP                           : 'TOP';
IDENTITY_INSERT               : 'IDENTITY_INSERT';
DDL_CLONE                     : 'DDL_CLONE';
SHADOW                        : 'SHADOW';
CUMULATIVE                    : 'CUMULATIVE';
BASE                          : 'BASE';
BACKUPDIR                     : 'BACKUPDIR';
BACKUPNAME                    : 'BACKUPNAME';
TAPE                          : 'TAPE';
BACKUPINFO                    : 'BACKUPINFO';
SPEED                         : 'SPEED';
MAXPIECESIZE                  : 'MAXPIECESIZE';
COMPRESSED                    : 'COMPRESSED';
TASK                          : 'TASK';
BACKED                        : 'BACKED';
UP                            : 'UP';
SINCE                         : 'SINCE';
INPUT                         : 'INPUT';
TRXID                         : 'TRXID';
DB                            : 'DB';
META                          : 'META';
INFO                          : 'INFO';
TXT                           : 'TXT';
BACKUPSETS                    : 'BACKUPSETS';
DMINI                         : 'DMINI';
OVERWRITE                     : 'OVERWRITE';
MAPPED                        : 'MAPPED';
BAK_MAGIC                     : 'BAK_MAGIC';
ARCHIVEDIR                    : 'ARCHIVEDIR';


INLINE_COMMENT: ('--' | '//') ~[\r\n]* ('\r'? '\n' | EOF)    -> channel(HIDDEN);