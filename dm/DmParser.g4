parser grammar DmParser;

import PlSqlParser;

options {
    tokenVocab = DmLexer;
}

/**
* DM
*   DDL @see https://eco.dameng.com/document/dm/zh-cn/pm/definition-statement.html
*   DML @see https://eco.dameng.com/document/dm/zh-cn/pm/insertion-deletion-modification.html
*/

root
    : (select
    | insert
    | update
    | delete
    | createView
    | createTable
    | alterTable
    | dropTable
    | truncateTable
    | lockTable
    | createIndex
    | dropIndex
    | alterIndex
    | commit
    | rollback
    | setTransaction
    | savepoint
    | grant
    | revoke
    | createUser
    | dropUser
    | alterUser
    | createRole
    | dropRole
    | alterRole
    | setRole
    | call
    | merge
    | alterSynonym
    | alterSession
    | alterDatabase
    | alterSystem
    | setConstraints
    | analyze
    | associateStatistics
    | disassociateStatistics
    | audit
    | noAudit
    | comment
    | flashbackDatabase
    | flashbackTable
    | purge
    | rename
    | createDatabase
    | createDatabaseLink
    | createDimension
    | alterDimension
    | dropDimension
    | createFunction
    | dropDatabaseLink
    | dropDirectory
    | dropView
    | dropTrigger
    | alterView
    | alterTrigger
    | createEdition
    | alterDatabaseLink
    | alterDatabaseDictionary
    | createSynonym
    | createDirectory
    | dropSynonym
    | dropPackage
    | dropEdition
    | dropTableSpace
    | dropOutline
    | dropDatabase
    | alterOutline
    | alterAnalyticView
    | alterAttributeDimension
    | createSequence
    | alterSequence
    | alterPackage
    | createContext
    | createSPFile
    | createPFile
    | createControlFile
    | createFlashbackArchive
    | alterFlashbackArchive
    | dropFlashbackArchive
    | createDiskgroup
    | dropDiskgroup
    | createRollbackSegment
    | dropRollbackSegment
    | createLockdownProfile
    | dropLockdownProfile
    | createInmemoryJoinGroup
    | alterInmemoryJoinGroup
    | dropInmemoryJoinGroup
    | createRestorePoint
    | dropRestorePoint
    | dropOperator
    | dropType
    | dropTypeBody
    | alterLibrary
    | alterMaterializedZonemap
    | alterJava
    | alterAuditPolicy
    | alterCluster
    | alterOperator
    | alterProfile
    | alterRollbackSegment
    | alterDiskgroup
    | alterIndexType
    | createMaterializedView
    | createMaterializedViewLog
    | alterMaterializedView
    | alterMaterializedViewLog
    | alterFunction
    | alterHierarchy
    | alterLockdownProfile
    | alterPluggableDatabase
    | explain
    | createProcedure
    | dropProcedure
    | alterProcedure
    | dropIndexType
    | dropPluggableDatabase
    | dropJava
    | dropLibrary
    | dropMaterializedView
    | dropMaterializedViewLog
    | dropMaterializedZonemap
    | dropContext
    | alterResourceCost
    | alterRole
    | createTablespace
    | alterTablespace
    | dropSequence
    | dropProfile
    | dropFunction
    | dropCluster
    | systemAction
    | alterType
    | createType
    | createTypeBody
    | createCluster
    | createJava
    | plsqlBlock
    | createLibrary
    | switch
    | createProfile
    | createTrigger
    | show
    | spool
    | createOperator
    | createPackage
    | createPackageBody
    | createSchema
    // 下面的是DM的语法
    | dropSchema
    | createDomain
    | dropDomain
    | setTimeZone
    | statTable
    | statIndex
    | statColumn
    | refreshMatViewStmt
    | releaseSavePoint
    | createClass
    | createClassBody
    | alterClass
    | dropClass
    | setIdentity         //属于DML
    | backupDatabase
    | backupTableSpace
    | backupTable
    | backupActiveLog
    | restoreTable
    | checkBackup
    | removeBackup
    | restoreDatabase
    | restoreTableSpace
    | restoreArchiveLog
    ) SEMI_? SLASH_? EOF
    ;

dropSchema
    : DROP SCHEMA ifExists? schemaName cascadeOrRestrict?
    ;

createDomain
    : CREATE DOMAIN ifNotExists? domainName AS? dataType ((DEFAULT expr) | inlineConstraint)?
    ;

dropDomain
    : DROP DOMAIN ifExists? domainName cascadeOrRestrict?
    ;

domainName
    : (owner DOT_)? name
    ;


className
    : (owner DOT_)? name
    ;

setTimeZone
    : SET TIME ZONE (LOCAL | stringLiterals | intervalLiterals)
    ;

// 设置索引生成统计信息。
statIndex
    : STAT INTEGER_ (SIZE INTEGER_)? ON INDEX indexName GLOBAL?
    ;

// 设置列生成统计信息。
statColumn
    : STAT INTEGER_ (SIZE INTEGER_)? ON tableName columnNames GLOBAL?
    ;

// 设置表生成统计信息。
statTable
    : STAT ON tableName GLOBAL?
    ;

// https://eco.dameng.com/document/dm/zh-cn/pm/materialized-view.html#7.4%20%E7%89%A9%E5%8C%96%E8%A7%86%E5%9B%BE%E7%9A%84%E5%88%B7%E6%96%B0
refreshMatViewStmt
    : REFRESH MATERIALIZED VIEW materializedViewName (FAST | ( COMPLETE| FORCE ) (USING (DEFAULT | TRUNCATE | DELETE))?)?
    ;

releaseSavePoint
    : RELEASE SAVEPOINT savepointName
    ;

// DM https://eco.dameng.com/document/dm/zh-cn/pm/class-type.html#12.2%20JAVA%20CLASS%20%E7%B1%BB%E5%9E%8B
createClass
    : CREATE (OR REPLACE)? CLASS ifNotExists? className dmEncryptionOrCalculate (UNDER className)? (finalClause | instantiableClause)*
    invokerRightsClause? (AS | IS) classItemList+ END className?
    ;

// DM
classItemList
    : elementSpecification SEMI_
    | declareSection
    ;

// DM
createClassBody
    : CREATE (OR REPLACE)? CLASS BODY className dmEncryptionOrCalculate (AS | IS) classBodyItemList+ initializeSection? END className?
    ;

// DM
classBodyItemList
    : inheritanceClauses? MAP? (MEMBER | STATIC)? (functionDefinition | procedureDefinition)
    ;

// DM
alterClass
    : ALTER CLASS className COMPILE DEBUG?
    ;

// DM
dropClass
    : DROP CLASS BODY? ifExists? className cascadeOrRestrict?
    ;

// DM
setIdentity
    : SET IDENTITY_INSERT tableName (ON (WITH REPLACE NULL)? | OFF)
    ;

/** backup and restore start  */
//https://eco.dameng.com/document/dm/zh-cn/pm/backup-restore-combat.html#3.2.2.1%20%E6%95%B0%E6%8D%AE%E5%BA%93%E5%A4%87%E4%BB%BD
backupDatabase
    : BACKUP DATABASE (backupType | databaseBackupType)? backupWithClause? backupNameClause? backupClause*
    ;

backupType
    : FULL
    | INCREMENT (FROM LSN expr | CUMULATIVE? BASE ON BACKUPSET pathString)?
    ;

databaseBackupType
    : FULL? (DDL_CLONE | SHADOW)
    ;

backupWithClause
    : WITH BACKUPDIR pathString (COMMA_ pathString)*
    ;

backupNameClause
    : (TO | BACKUPNAME) backupName
    ;

backupClause
    : backupPathClause deviceTypeClause?
    | backupInfoClause
    | maxPieceSizeClause
    | backupLimitClause
    | backupIdentifiedByClause
    | backupCompressClause
    | backupTraceClause
    | backupTaskClause
    ;

backupPathClause
    : (BACKUPSET | FORMAT) pathString
    ;

deviceTypeClause
    : DEVICE TYPE (DISK | TAPE) deviceParamsClause?
    ;

deviceParamsClause
    : PARMS deviceParams = stringLiterals
    ;

backupInfoClause
    : BACKUPINFO stringLiterals
    ;

maxPieceSizeClause
    : MAXPIECESIZE INTEGER_
    ;

backupLimitClause
    : LIMIT (
        READ SPEED INTEGER_ (WRITE SPEED INTEGER_)?
        | WRITE SPEED INTEGER_
    )
    ;

backupIdentifiedByClause
    : IDENTIFIED BY password (WITH ENCRYPTION INTEGER_)? (ENCRYPT WITH name)?
    ;

backupCompressClause
    : COMPRESSED (LEVEL INTEGER_)? (WITHOUT (LOG | MIRROR))*
    ;

backupTraceClause
    : TRACE FILE pathString (TRACE LEVEL INTEGER_)?
    ;

backupTaskClause
    : TASK THREAD INTEGER_ (PARALLEL INTEGER_ (READ SIZE INTEGER_)?)?
    ;

// https://eco.dameng.com/document/dm/zh-cn/pm/backup-restore-combat.html#3.2.2.2%20%E8%A1%A8%E7%A9%BA%E9%97%B4%E5%A4%87%E4%BB%BD
backupTableSpace
    : BACKUP TABLESPACE tablespaceName backupType? backupWithClause? backupNameClause? backupClause*
    ;

// https://eco.dameng.com/document/dm/zh-cn/pm/backup-restore-combat.html#3.2.2.3%20%E8%A1%A8%E5%A4%87%E4%BB%BD
backupTable
    : BACKUP TABLE tableName backupNameClause? backupClause*
    ;

// https://eco.dameng.com/document/dm/zh-cn/pm/backup-restore-combat.html#3.2.2.4%20%E5%BD%92%E6%A1%A3%E5%A4%87%E4%BB%BD
backupActiveLog
    : BACKUP (ARCHIVE LOG | ARCHIVELOG) backupActiveLogType? notBackupSpec? backupWithClause? deleteInputClause? backupDatabaseClause
    backupNameClause? backupClause*
    ;

backupActiveLogType
    : ALL
    | (FROM | UNTIL) LSN expr
    | LSN BETWEEN expr AND expr
    | (FROM | UNTIL) TIME STRING_
    | TIME BETWEEN STRING_ AND STRING_
    ;

notBackupSpec
    : NOT BACKED UP (INTEGER_ TIMES | SINCE TIME STRING_)?
    ;

deleteInputClause
    : DELETE INPUT
    ;

backupDatabaseClause
    : DATABASE pathString
    ;

//https://eco.dameng.com/document/dm/zh-cn/pm/backup-restore-combat.html#3.2.5.1%20%E8%A1%A8%E8%BF%98%E5%8E%9F
restoreTable
    : RESTORE TABLE tableName STRUCT? (KEEP TRXID)? restoreFromClause restoreTableClause*
    ;

restoreFromClause
    : FROM BACKUPSET? pathString
    | FROM (BACKUPNAME | BACKUP) backupName
    ;

restoreTableClause
    : deviceTypeClause
    | backupIdentifiedByClause
    | backupTraceClause
    ;

//https://eco.dameng.com/document/dm/zh-cn/pm/backup-restore-combat.html#3.3.5%20%E6%95%B0%E6%8D%AE%E5%BA%93%E8%BF%98%E5%8E%9F
restoreDatabase
    : RESTORE DATABASE restoreType? restoreFromClause restoreDatabaseClause*
    ;

restoreType
    : pathString (TO SHADOW)? (WITH CHECK)? (REUSE DMINI)? (WITHOUT SPACE)? (WITHOUT MIRROR)? (AUTO EXTEND)? OVERWRITE?
    | TO pathString (TO SHADOW)? (WITH CHECK)? OVERWRITE? (WITHOUT MIRROR)?
    ;

restoreDatabaseClause
    : restoreTableSpaceClause
    | RENAME TO STRING_
    | USE BAK_MAGIC INTEGER_
    ;

//https://eco.dameng.com/document/dm/zh-cn/pm/backup-restore-combat.html#3.3.6%20%E8%A1%A8%E7%A9%BA%E9%97%B4%E8%BF%98%E5%8E%9F
restoreTableSpace
    : RESTORE (DATABASE pathString)? TABLESPACE tablespaceName (WITH CHECK)? (DATAFILE fileSpecifications)? restoreFromClause restoreTableSpaceClause*
    ;

restoreTableSpaceClause
    : deviceTypeClause
    | backupIdentifiedByClause
    | backupWithClause
    | backupTaskClause
    | MAPPED FILE pathString
    | resUntil
    ;

resUntil
    : UNTIL TIME STRING_
    | UNTIL LSN INTEGER_
    ;

// https://eco.dameng.com/document/dm/zh-cn/pm/backup-restore-combat.html#3.3.7%20%E5%BD%92%E6%A1%A3%E8%BF%98%E5%8E%9F
restoreArchiveLog
    : RESTORE (ARCHIVE LOG | ARCHIVELOG) (WITH CHECK)? restoreFromClause restoreArchiveLogClause* restoreArchiveLogTo
    ;

restoreArchiveLogClause
    : deviceTypeClause | backupIdentifiedByClause | backupTaskClause | backupActiveLogType
    ;

restoreArchiveLogTo
    : TO (ARCHIVEDIR | DATABASE) STRING_ (OVERWRITE INTEGER_)?
    ;

//https://eco.dameng.com/document/dm/zh-cn/pm/backup-restore-combat.html#3.3.4.3%20%E5%A4%87%E4%BB%BD%E9%9B%86%E6%A0%A1%E9%AA%8C
checkBackup
    : CHECK BACKUP pathString deviceTypeClause? backupDatabaseClause? backupIdentifiedByClause? (LEVEL INTEGER_)?
    ;

//https://eco.dameng.com/document/dm/zh-cn/pm/backup-restore-combat.html#3.3.4.4%20%E5%A4%87%E4%BB%BD%E9%9B%86%E5%88%A0%E9%99%A4
removeBackup
    : REMOVE backupSetType? BACKUPSET pathString deviceTypeClause? databaseBakDirListStmt? (CASCADE | UNTIL TIME STRING_ | BEFORE INTEGER_)?
    ;

databaseBakDirListStmt
    : backupDatabaseClause | backupWithClause
    ;

backupSetType
    : DATABASE
    | TABLESPACE tablespaceName
    | TABLE tableName
    | ARCHIVELOG
    | ARCHIVE LOG
    ;

backupName
    : identifier
    ;

// https://eco.dameng.com/document/dm/zh-cn/pm/backup-restore-combat.html#3.3.4.2%20%E5%A4%87%E4%BB%BD%E9%9B%86%E6%9F%A5%E7%9C%8B
backupInfoTypeClause
    : INFO (DB | META | FILE | TABLESPACE | TABLE)
    ;

toFile
    : TO pathString (FORMAT TXT | XML)?
    ;
/** backup and restore end  */

showOptions
    : systemVariable
    | ALL
    | CON_ID
    | CON_NAME
    | EDITION
    | (BTI | BTITLE)
    | (ERR | ERRORS) ((ANALYTIC VIEW | ATTRIBUTE DIMENSION | HIERARCHY | FUNCTION | PROCEDURE | PACKAGE | PACKAGE BODY | TRIGGER  | VIEW | TYPE | TYPE BODY | DIMENSION | JAVA CLASS) (schema DOT_)? name)?
    | HISTORY
    | LNO
    | LOBPREFETCH
    | (PARAMETER | PARAMETERS) parameterName?
    | PDBS
    | PNO
    | (RECYC | RECYCLEBIN) originalName?
    | (REL | RELEASE)
    | (REPF | REPFOOTER)
    | (REPH | REPHEADER)
    | (ROWPREF | ROWPREFETCH)
    | SGA
    | (SPOO | SPOOL)
    | (SPPARAMETER | SPPARAMETERS) parameterName?
    | SQLCODE
    | (STATEMENTC | STATEMENTCACHE)
    | (TTI | TLE)
    | USER
    | XQUERY
    | BACKUPSET pathString deviceTypeClause? dmRecursiveClause databaseBakDirListStmt? backupInfoTypeClause? toFile?  //DM
    | BACKUPSETS deviceTypeClause? databaseBakDirListStmt           // DM
    ;

dmRecursiveClause
    : RECURSIVE?
    ;

explain
    : EXPLAIN ((AS name)? FOR)? (insert | delete | update | select)
    ;

createSchema
    : CREATE SCHEMA schemaName (AUTHORIZATION username)? ddlGrantClause*
    ;

ddlGrantClause
    : createTable | createView | grant | createIndex | createSequence | createDatabaseLink | createSynonym | createDirectory | createDomain
    | alterTable | createProcedure | createFunction | createTrigger | createPackage | createPackageBody | createType
    | createTypeBody | createMaterializedView | createMaterializedViewLog | createOperator | createClass | createClassBody
    ;

createUseridentifiedSegment
    : BY password hashCipher?  // DM
    | identifiedExternallyOption
    | identifiedGloballyOption
    ;

hashCipher
    : HASH WITH name (SALT| NO SALT)?
    ;

tablespaceOption
    : DEFAULT INDEX? TABLESPACE GROUP? (tablespaceName | NULL)
    ;

quotaOption
    : QUOTA (sizeClause | UNLIMITED) (ON tablespaceName)?
    ;

profileOption
    : PROFILE profileName
    | DROP PROFILE     // DM
    | LIMIT resourceLimitList // DM
    ;

// DM
resourceLimitList
    : resourceLimit (COMMA_? resourceLimit)*
    ;

// DM
resourceLimit
    : SESSION_PER_USER resourceLimitValue
    | GLOBAL_SESSION_PER_USER resourceLimitValue
    | CONNECT_IDLE_TIME resourceLimitValue
    | FAILED_LOGIN_ATTEMPS resourceLimitValue
    | FAILED_LOGIN_ATTEMPTS resourceLimitValue
    | PASSWORD_LIFE_TIME resourceLimitValue
    | PASSWORD_REUSE_TIME resourceLimitValue
    | PASSWORD_REUSE_MAX resourceLimitValue
    | PASSWORD_LOCK_TIME resourceLimitValue
    | PASSWORD_GRACE_TIME resourceLimitValue
    | CPU_PER_CALL resourceLimitValue
    | CPU_PER_SESSION resourceLimitValue
    | MEM_SPACE resourceLimitValue
    | READ_PER_CALL resourceLimitValue
    | READ_PER_SESSION resourceLimitValue
    | CONNECT_TIME resourceLimitValue
    | INACTIVE_ACCOUNT_TIME resourceLimitValue
    ;

// DM
resourceLimitValue
    : UNLIMITED
    | DEFAULT
    | INTEGER_
    ;

// DM
passwordPolicyOption
    : PASSWORD_POLICY INTEGER_
    ;

// DM
encryptByOption
    : ENCRYPT BY password
    ;

// DM 空间限制子句
spaceLimitClause
    : DISKSPACE (LIMIT sizeClause | UNLIMITED)
    ;

// DM
readOnlyOption
    : NOT? READ ONLY
    ;

// DM
allowIpOption
    : (ALLOW_IP | NOT_ALLOW_IP) (NULL | STRING_ (COMMA_ STRING_)*)
    ;

// DM
allowDtOption
    : (ALLOW_DATETIME | NOT_ALLOW_DATETIME) (NULL | | STRING_ (COMMA_ STRING_)*)
    ;

alterUser
    : ALTER USER ((username (IDENTIFIED (BY password (REPLACE password)?
    | EXTERNALLY (AS CERTIFICATE_DN | AS KERBEROS_PRINCIPAL_NAME)?
    | GLOBALLY AS (STRING_ | SQ_ AZURE_ROLE EQ_ identifier SQ_ | SQ_ IAM_GROUP_NAME EQ_ identifier SQ_))
    | NO AUTHENTICATION
    | DEFAULT COLLATION collationName
    | DEFAULT INDEX? TABLESPACE tablespaceName
    | LOCAL? TEMPORARY TABLESPACE tablespaceName tablespaceGroupName
    | QUOTA (sizeClause | UNLIMITED) ON tablespaceName
    | PROFILE profileName
    | DEFAULT ROLE (NONE | roleName (COMMA_ roleName)* |  allClause )
    | PASSWORD EXPIRE
    | ACCOUNT (LOCK | UNLOCK)
    | ENABLE EDITIONS (FOR editionType (COMMA_ editionType)*)? FORCE?
    | HTTP? DIGEST (ENABLE | DISABLE)
    | CONTAINER EQ_ (CURRENT | ALL)
    | passwordPolicyOption    // DM
    | encryptByOption         // DM
    | spaceLimitClause        // DM
    | readOnlyOption          // DM
    | profileOption           // DM
    | allowIpOption           // DM
    | allowDtOption          // DM
    | ON SCHEMA schemaName  // DM
    | containerDataClause)*) | username (COMMA_ username)* proxyClause*)
    ;

insertSingleTable
    : insertIntoClause (insertValuesClause | selectSubquery | DEFAULT VALUES | TABLE dmlTableExprClause) returningClause? errorLoggingClause?
    ;

returningClause
    : (RETURN | RETURNING) exprs (BULK COLLECT)? INTO dataItem (COMMA_ dataItem)*
    ;

dmlTableClause
    : (tableName | viewName) dbLinkClause?
    | tableName INDEX indexName             // DM
    | tableName PARTITION partitionName     // DM
    ;

dbLinkClause
    : (AT_ | LINK) dbLink
    ;

subqueryRestrictionClause
    : WITH (READ ONLY | (LOCAL | CASCADED)? CHECK OPTION) (CONSTRAINT constraintName)?
    ;

update
    : withClause? UPDATE hint? updateSpecification aliasClause? updateSetClause whereClause? returningClause? errorLoggingClause?
    ;

delete
    : withClause? DELETE hint? topClause? FROM? deleteSpecification aliasClause? whereClause? returningClause? errorLoggingClause? rowLimitingClause?
    ;

combineType
    : (UNION ALL? | INTERSECT | MINUS | EXCEPT) duplicateSpecification? (CORRESPONDING (BY columnNames)?)?
    ;

plsqlDeclarations
    : RECURSIVE? (functionDeclaration | procedureDeclaration | FUNCTION plsqlFunctionSource | PROCEDURE procedureDefinition)+
    ;

selectList
    : topClause? (unqualifiedShorthand | selectProjection) (COMMA_ selectProjection)*
    ;

topClause
    : TOP INTEGER_ (COMMA_ INTEGER_)? (PERCENT | WITH TIES)*
    ;

fromClauseOption
    : selectTableReference
    | inlineAnalyticView
    | (regularFunction | xmlTableFunction) alias?
    | arrayFromClause   //DM
    ;
// DM
arrayFromClause
    : ARRAY pexpPfx rangeFromTo? aliasClause?
    ;
// DM
rangeFromTo
    : TOP INTEGER_
    | FROM INTEGER_ TO INTEGER_
    ;
// DM
pexpPfx
    : pexpPfx DOT_ (name dbLinkClause? | key | ASTERISK_)  ((LBT_ exprs RBT_)+)?
    | pexpPfx LP_ optArgumentList RP_ ( keepClause |  withinClause )?  ((LBT_ exprs RBT_)+)?
    | pexpPfx DOT_ ( EXECUTE | EXTRACT  ) LP_ optArgumentList RP_ ((LBT_ exprs RBT_)+)?
    | XMLPARSE LP_ identifier expr identifier? RP_ ((LBT_ exprs RBT_)+)?
    | XMLELEMENT LP_ (identifier | EVALNAME expr) (COMMA_ xmlAttributes)? RP_ ((LBT_ exprs RBT_)+)?
    | XMLAGG LP_ expr orderByClause? RP_ ((LBT_ exprs RBT_)+)?
    | pexpcinsert ((LBT_ exprs RBT_)+)?
    | NEW STRUCT? dataType LBT_ expr RBT_ (LBT_ (COMMA_+)? RBT_)? (LBE_ (exprs COMMA_?)? RBE_)?
    | CAST? LP_ expr AS dataType RP_
    | CAST LP_ COLLECT LP_ ( ALL | DISTINCT | UNIQUE)? expr orderByClause? RP_ AS dataType RP_
    | QUESTION_
    | COLON_ INTEGER_
    | COLON_ LT_ INTEGER_ name? GT_
    | CARET_ QUESTION_ CARET_
    ;
// DM
pexpcinsert
    : name dbLinkClause?
    | NEW name LP_ optArgumentList RP_
    | COLON_ (variableName | NEW) DOT_ columnName
    | NEW DOT_ columnName
    | name dbLinkClause? MOD_ BULK (EXCEPTION | ROWCOUNT)
    ;
// DM
key
    : FIRST
    | NEXT
    | PRIOR
    | DELETE
    | GET
    | SET
    | EXISTS
    | TRIM
    | ROWNUM
    | ROWS
    ;
// DM
optArgumentList
    : mixedParam (COMMA_ mixedParam)* jsonFunTail
    | booleanPrimary FROM expr (FOR expr)?
    | ( ALL | DISTINCT | UNIQUE) expr
    | ASTERISK_
    | PLUS_
    | selectSubquery
    ;
// DM
jsonFunTail
    : (RETURNING dataType dataTypeLength?)? pretty? arrayWrapper? jsonEmptyErrorOptions
    ;
// DM
pretty
    : ASCII | PRETTY ASCII?
    ;
// DM
wrapperFlag
    : ARRAY? WRAPPER
    ;
// DM
arrayWrapper
    : (WITHOUT | WITH) (UNCONDITIONAL | CONDITIONAL)? wrapperFlag
    ;
// DM
mixedParam
    : booleanPrimary (FORMAT JSON)? aliasClause?
    | parameterName ARROW_ booleanPrimary
    ;


lateralClause
    : LATERAL? LP_ selectSubquery subqueryRestrictionClause? RP_ derivedTableOption?
    ;

derivedTableOption
    : aliasClause (LP_ columnName (COMMA_ columnName)* RP_)?
    ;

queryTableExprTableClause
    : tableName (mofifiedExternalTable | partitionExtClause | dbLinkClause | queryTableExprAnalyticClause | indexExtClause)?
    ;

// DM
indexExtClause
    : INDEX indexName
    ;

forUpdateClause
    : FOR UPDATE (OF forUpdateClauseList)? ((NOWAIT | WAIT INTEGER_) | SKIP_SYMBOL LOCKED)?
    | FOR READ ONLY
    ;

rowLimitingClause
    : rowOffsetClause rowFetchClause?
    | rowFetchClause
    | limitClause
    ;

// DM
limitClause
    : LIMIT INTEGER_ (COMMA_ INTEGER_)?
    | LIMIT INTEGER_ OFFSET INTEGER_
    | OFFSET INTEGER_ LIMIT INTEGER_
    ;

lockmodeClause
    : (ROW | INTENT) (SHARE | EXCLUSIVE)
    | SHARE (UPDATE | (ROW | INTENT) EXCLUSIVE)?
    | EXCLUSIVE
    ;

createIndex
    : CREATE (OR REPLACE)? (CLUSTER|NOT PARTIAL)? createIndexSpecification? INDEX ifNotExists? indexName ON createIndexDefinitionClause usableSpecification? invalidationSpecification?
    ;

bindDefinitionClause
    : BINDING bindDefinition (COMMA_ bindDefinition)*
    | LP_ FUNCTION functionName COMMA_ operatorObjectType RP_
    ;

operatorObjectType
    : LEFTARG dataType (COMMA_ RIGHTARG dataType)?
    | RIGHTARG dataType (COMMA_ LEFTARG dataType)?
    ;

objectBaseTypeDef
    : (IS | AS) (objectTypeDef | varrayTypeSpec | nestedTableTypeSpec | recordTypeDef | arrayTypeSpec)
    ;

// DM
recordTypeDef
    : RECORD LP_ (variableName dataType SEMI_)* RP_
    ;

// DM
arrayTypeSpec
    : ARRAY dataType stringLiterals
    ;

objectTypeDef
    : OBJECT (UNDER typeName)? (LP_ typeDefinitionItem (COMMA_ typeDefinitionItem)* RP_)? (finalClause | instantiableClause | persistableClause)*
    ;

nestedTableTypeSpec
    : TABLE OF typeSpec
    | assocArrayTypeDef
    ;

alterIndex
    : ALTER CONTEXT? INDEX indexName alterIndexInformationClause
    ;

dropIndex
    : DROP CONTEXT? INDEX ifExists? indexName ONLINE? FORCE? invalidationSpecification? (ON tableName)?
    ;

truncateTable
    : TRUNCATE TABLE tableName materializedViewLogClause? dropReuseClause? partitionClause? CASCADE?
    ;

// DM
partitionClause
    : PARTITION (partitionName | LP_ partitionName RP_)
    ;

createDefinitionClause
    : dmTableProperties* deferredSegmentCreation? dmCompressClause? parallelClause? advancedLogClause? addLogClause? AS select addLogClause? distributeClause? autoIncrementClause?  // as
    | (LP_ relationalProperties RP_ dmTableProperties* deferredSegmentCreation? dmCompressClause? parallelClause? advancedLogClause? rowMovementClause? addLogClause? logClause? distributeClause? autoIncrementClause?)  // 基表定义2
    | LP_ relationalProperties RP_ externalTableFromClause           // 外部表定义
    ;

createTableSpecification
    : ((GLOBAL | PRIVATE) TEMPORARY | SHARDED | DUPLICATED | IMMUTABLE? BLOCKCHAIN | IMMUTABLE | EXTERNAL | HUGE)?
    ;

// DM
externalTableFromClause
    : FROM (
        pathString
        | DATAFILE pathString (PARMS LP_ externalTableParameter RP_)?
        | DEFAULT DIRECTORY directoryName LOCATION LP_ pathString RP_
        | DATAFILE DEFAULT DIRECTORY directoryName LOCATION LP_ pathString RP_ (PARMS LP_ externalTableParameter RP_)?
    )
    ;

// DM
externalTableParameter
    : (FIELDS DELIMITED BY expr
    | RECORDS DELIMITED BY expr
    | ERRORS INTEGER_
    | BADFILE pathString
    | LOG pathString
    | NULL_STR NULL
    | SKIP_SYMBOL INTEGER_
    | CHARACTER_CODE (GBK | UTF_8 | SINGLE_BYTE | EUC_KR)) (COMMA_ externalTableParameter)*
    ;

// DM
dmTableProperties
    : TABLESPACE tablespaceName
    | ON COMMIT (DELETE | PRESERVE) ROWS
    | storageClause
    | spaceLimitClause
    | tablePartitioningClauses
    ;

// DM
dmCompressClause
    : COMPRESS columnNames?
    | COMPRESS dmCompessOptions? EXCEPT columnNames
    | COMPRESS dmCompessOptions? LP_ columnName dmCompessOptions? (COMMA_ columnName dmCompessOptions?)* RP_
    ;

// DM
dmCompessOptions
    : (LEVEL INTEGER_)? (FOR STRING_)?
    ;

// DM
advancedLogClause
    : WITH ADVANCED LOG
    ;

// DM
addLogClause
    : ADD LOGIC LOG
    ;

// DM
distributeClause
    : DISTRIBUTED (
        RANDOMLY
        | FULLY
        | BY HASH? columnNames
        | BY RANGE columnNames (LP_ (rangeValuesClause ON name) (COMMA_ (rangeValuesClause ON name))* RP_)
        | BY LIST columnNames (LP_ (listValuesClause ON name) (COMMA_ (listValuesClause ON name))* RP_)
    )
    ;

// DM
logClause
    : LOG (NONE | LAST | ALL)
    ;

autoIncrementClause
    : AUTO_INCREMENT EQ_? INTEGER_
    ;

columnDefinition
    : columnName dataType dmColumnDefintionClause? storageClause? tableEncryptClause? (COMMENT STRING_)?
    ;

virtualColumnDefinition
    : columnName dataType? (GENERATED ALWAYS)? AS LP_ expr RP_ VIRTUAL? VISIBLE? dmColumnDefintionClause? storageClause? tableEncryptClause? (COMMENT STRING_)?
    ;

// DM
dmColumnDefintionClause
    : DEFAULT (ON NULL)? expr colConstraint?
    | identityClause colConstraint?
    | colConstraint (DEFAULT (ON NULL)? expr | identityClause)?
    ;

 // DM
uniqueSpec
    : UNIQUE
    | CLUSTER UNIQUE? KEY
    | NOT? NULL
    | primaryKey
    | NOT? CLUSTER primaryKey
    ;

 // DM
colConstraint
    : (CONSTRAINT constraintName)?
    ( uniqueSpec uniqueUsing?
    | (FOREIGN KEY)? referencesClause
    | CHECK LP_ expr RP_
    | NOT VISIBLE
    ) constraintState? (COMMA_ colConstraint)*
    ;

 // DM
uniqueUsing
    : USING INDEX TABLESPACE (tablespaceName | DEFAULT)
    ;

// DM
tableEncryptClause
    : ENCRYPT (withEncrypt hashCipher)?
    | ENCRYPT (WITH name)? MANUAL (
        (BY WRAPPED? password)? USER LP_ username (COMMA_ username)* RP_
        | BY WRAPPED password
    )? hashCipher?
    ;

// DM
withEncrypt
    : (WITH name)? AUTO (BY WRAPPED? password)?
    ;

identityClause
    : GENERATED (ALWAYS | BY DEFAULT (ON NULL)?) AS IDENTITY identifyOptions
    | IDENTITY (LP_ INTEGER_ COMMA_ INTEGER_ RP_)?      // DM
    | AUTO_INCREMENT                                    // DM
    ;

referencesClause
    : REFERENCES PENDANT? tableName columnNames? matchOptions? onOptions? (WITH INDEX)?
    ;

// DM
matchOptions
    : MATCH (FULL | PARTIAL | NONE)
    ;

// DM
onOptions
    : (ON DELETE refAction
    | ON UPDATE refAction) onOptions?
    ;

// DM
refAction
    : CASCADE | SET NULL | NO ACTION | SET DEFAULT
    ;

outOfLineConstraint
    : (CONSTRAINT constraintName)?
    (uniqueSpec columnNames uniqueUsing?
    | FOREIGN KEY columnNames referencesClause
    | CHECK LP_ expr RP_
    ) constraintState?
    ;

createIndexSpecification
    : UNIQUE | BITMAP | MULTIVALUE | SPATIAL | CONTEXT | ARRAY
    ;

// 原为可以选循环，改为单项非循环，使用时后面拼'*'
indexAttributes
    : physicalAttributesClause | loggingClause | ONLINE | TABLESPACE (tablespaceName | DEFAULT) | indexCompression
    | (SORT | NOSORT) | REVERSE | (VISIBLE | INVISIBLE) | partialIndexClause | parallelClause | GLOBAL
    | (UNUSABLE | storageClause | lexerClause | SYNC TRANSACTION?)     // DM
    ;

// DM
lexerClause
    : LEXER (CHINESE_LEXER | CHINESE_LEXER | CHINESE_FP_LEXER | ENGLISH_LEXER | DEFAULT_LEXER)
    ;

indexProperties
    : ((globalPartitionedIndex | localPartitionedIndex | indexAttributes | dmPartitionedIndex)+ | INDEXTYPE IS (domainIndexClause | xmlIndexClause))?
    ;

dmPartitionedIndex
    : GLOBAL? tablePartitioningClauses
    ;

alterDefinitionClause
    : (alterTableProperties
    | columnClauses
    | MODIFY spaceLimitClause  // DM
    | moveTableClause
    | constraintClauses
    | alterTablePartitioning invalidationSpecification?
    | alterExternalTable)?
    ;

alterTableProperties
    : ((physicalAttributesClause
    | loggingClause
    | tableCompression
    | inmemoryTableClause
    | ilmClause
    | supplementalTableLogging
    | allocateExtentClause
    | deallocateUnusedClause
    | (CACHE | NOCACHE)
    | upgradeTableClause
    | recordsPerBlockClause
    | parallelClause
    | rowMovementClause
    | logicalReplicationClause
    | flashbackArchiveClause)+ | renameTableSpecification)? alterIotClauses? alterXMLSchemaClause?
    | shrinkClause
    | READ ONLY
    | READ WRITE
    | REKEY encryptionSpecification
    | DEFAULT COLLATION collationName
    | NO? ROW ARCHIVAL
    | ADD attributeClusteringClause
    | MODIFY CLUSTERING clusteringWhen? zonemapClause?
    | DROP CLUSTERING
    | (
        (WITH | WITHOUT) COUNTER     // DM
        | MODIFY PATH pathString
        | DROP (IDENTITY | AUTO_INCREMENT)
        | ADD IDENTITY LP_ expr COMMA_ expr RP_
        | autoIncrementClause
        | externalTableDataProps
        | ENABLE USING LONG ROW
        | (ADD | DROP) LOGICLOG
        | (WITHOUT | TRUNCATE | WITH) ADVANCED LOG
        | SET STAT (NONE | SYNCHRONOUS | ASYNCHRONOUS)? ((ON | EXCEPT) columnNames)
        | REFRESH STAT
        | FORCE COLUMN STORAGE
        | LOCK (PARTITIONS | ROOT)
        | REBUILD SECTION
    )
    ;

operateColumnClause
    : addColumnSpecification | modifyColumnSpecification | dropColumnClause | (REBUILD COLUMNS) | alterColumnClause
    ;

// DM
alterColumnClause
    : ALTER COLUMN? columnName (
        SET DEFAULT expr
        | SET NOT? NULL
        | DROP DEFAULT
        | RENAME TO columnName
        | SET NOT? VISIBLE
        | SET STAT NONE?
        | WITH DELTA
    )
    ;

addColumnSpecification
    : ADD COLUMN? ifNotExists? (LP_ columnOrVirtualDefinitions RP_ | columnOrVirtualDefinitions) columnProperties?
    ;

constraintClauses
    : addConstraintSpecification | modifyConstraintClause | renameConstraintClause | dropConstraintClause+ | stateConstraintClause
    ;

// DM
stateConstraintClause
    : (ENABLE | DISABLE) CONSTRAINT constraintName constraintState? cascadeOrRestrict?
    ;

modifyConstraintClause
    : MODIFY constraintOption (constraintState | TO outOfLineConstraint) cascadeOrRestrict?
    ;

cascadeOrRestrict
    : CASCADE | RESTRICT
    ;

rebuildClause
    : (
        REBUILD (PARTITION partitionName | SUBPARTITION subpartitionName | REVERSE | NOREVERSE | NOSORT)?
        | (INCREMENT | OPTIMIZE)
    ) ( parallelClause
    | TABLESPACE tablespaceName
    | PARAMETERS LP_ odciParameters RP_
    | ONLINE
    | physicalAttributesClause
    | indexCompression
    | loggingClause
    | lexerClause
    | partialIndexClause)*
    ;

storageClause
    : STORAGE LP_
    ((INITIAL sizeClause
    | NEXT sizeClause
    | MINEXTENTS INTEGER_
    | MAXEXTENTS (INTEGER_ | UNLIMITED)
    | maxsizeClause
    | PCTINCREASE INTEGER_
    | FREELISTS INTEGER_
    | FREELIST GROUPS INTEGER_
    | OPTIMAL (sizeClause | NULL)?
    | BUFFER_POOL (KEEP | RECYCLE | DEFAULT)
    | FLASH_CACHE (KEEP | NONE | DEFAULT)
    | CELL_FLASH_CACHE (KEEP | NONE | DEFAULT)
    | ENCRYPT
    | (
        ON tablespaceName      // DM
        | FILLFACTOR INTEGER_       // DM
        | BRANCH (INTEGER_ | LP_ INTEGER_ COMMA_ INTEGER_ RP_)  // DM
        | NOBRANCH  // DM
        | CLUSTERBTR    // DM
        | (WITH | WITHOUT) (COUNTER | DELTA)  // DM
        | USING LONG ROW    // DM
        | DISABLE USING LONG ROW    // DM
        | STAT NONE     // DM hue table column storage
        | SECTION LP_ sizeClause RP_  // DM hue table
        | FILESIZE LP_ sizeClause RP_   // DM hue table
        | STAT (NONE| SYNCHRONOUS | ASYNCHRONOUS)? (ON | EXCEPT LP_ columnNames RP_)?   // DM hue table
        | LOB ON tablespaceName  // DM 位图连接索引
    )
    ) COMMA_? )+ RP_
    ;

tablePartitioningClauses
    : (rangePartitions
    | listPartitions
    | hashPartitions
    | compositeRangePartitions
    | compositeListPartitions
    | compositeHashPartitions
    | referencePartitioning
    | systemPartitioning
    | consistentHashPartitions
    | consistentHashWithSubpartitions
    | partitionsetClauses) tablePartitioningLockClause
    ;

tablePartitioningLockClause
    : (LOCK (PARTIITONS |ROOT))?
    ;

rangeValuesClause
    : VALUES (EQU OR)? LESS THAN LP_? (literals | MAXVALUE | toDateFunction) (COMMA_ (literals | MAXVALUE | toDateFunction))* RP_?
    ;

alterTablePartitioning
    : modifyTableDefaultAttrs
    | setSubpartitionTemplate
    | modifyTablePartition
    | modifyTableSubpartition
    | moveTablePartition
    | moveTableSubPartition
    | addTablePartition
    | coalesceTablePartition
    | dropTablePartition
    | renamePartitionSubpart
    | alterIntervalPartitioning
    | truncatePartition
    ;

// DM
truncatePartition
    : TRUNCATE (PARTITION | SUBPARTITION) (partitionName | LP_ partitionName RP_)
    ;

alterSessionSetClause
    : SET (alterSessionSetClauseOption | dmAlterSessionSetClauseOption)
    ;

dmAlterSessionSetClauseOption
    : (
        NLS_DATE_LANGUAGE EQ_ (AMERICAN | ENGLISH | stringLiterals)
        | (NLS_DATE_FORMAT | NLS_TIMESTAMP_FORMAT | NLS_TIMESTAMP_TZ_FORMAT | NLS_TIME_FORMAT | NLS_TIME_TZ_FORMAT) EQ_ stringLiterals
        | NLS_SORT EQ_ (BINARY | SCHINESE_PINYIN_M | SCHINESE_STROKE_M | SCHINESE_RADICAL_M | THAI_CI_AS | KOREAN_M | stringLiterals)
        | CASE_SENSITIVE EQ_ (TRUE | FALSE | DEFAULT)
    )
    ;

alterDatabase
    : ALTER databaseClauses
    ( dmCommandClause
    | dmArchiveLogClause
    | startupClauses
    | recoveryClauses
    | databaseFileClauses
    | logfileClauses
    | controlfileClauses
    | standbyDatabaseClauses
    | defaultSettingsClauses
    | instanceClauses
    | securityClause
    | prepareClause
    | dropMirrorCopy
    | lostWriteProtection
    | cdbFleetClauses
    | propertyClause
    )
    ;

// DM
dmCommandClause
    : SUSPEND | NORMAL | PRIMARY | STANDBY
    ;

// DM
dmArchiveLogClause
    : (ADD | DELETE | MODIFY) ARCHIVELOG archiveConfig=STRING_
    | ARCHIVELOG CURRENT
    ;

logfileClauses
    : ((ARCHIVELOG MANUAL? | NOARCHIVELOG )
    | NO? FORCE LOGGING
    | SET STANDBY NOLOGGING FOR (DATA AVAILABILITY | LOAD PERFORMANCE)
    | RENAME (FILE | LOGFILE) fileName (COMMA_ fileName)* TO fileName
    | CLEAR UNARCHIVED? LOGFILE logfileDescriptor (COMMA_ logfileDescriptor)* (UNRECOVERABLE DATAFILE)?
    | addLogfileClauses
    | dropLogfileClauses
    | switchLogfileClause
    | supplementalDbLogging
    | resizeLogfileClauses
    | addNodeLogfileClauses
    )
    ;

// DM
resizeLogfileClauses
    : RESIZE LOGFILE fileName TO sizeClause
    ;

// DM
addNodeLogfileClauses
    : ADD NODE LOGFILE redoLogFileSpec (COMMA_ redoLogFileSpec)*
    ;

alterSystemOption
    : archiveLogClause
    | checkpointClause
    | checkDatafilesClause
    | distributedRecovClauses
    | flushClause
    | endSessionClauses
    | alterSystemSwitchLogfileClause
    | suspendResumeClause
    | quiesceClauses
    | rollingMigrationClauses
    | rollingPatchClauses
    | alterSystemSecurityClauses
    | affinityClauses
    | shutdownDispatcherClause
    | registerClause
    | setClause
    | resetClause
    | relocateClientClause
    | cancelSqlClause
    | flushPasswordfileMetadataCacheClause
    | ARCHIVE LOG CURRENT   // DM
    ;

setParameterClause
    : parameterName EQ_ parameterValue (COMMA_ parameterValue)* alterSystemCommentClause? (DEFERRED | PURGE)? containerCurrentAllClause? scopeClause*
    ;

comment
    : COMMENT ON (
    | AUDIT POLICY policyName
    | COLUMN (tableName | viewName | materializedViewName) DOT_ columnName
    | EDITION editionName
    | INDEXTYPE indexTypeName
    | MATERIALIZED VIEW materializedViewName
    | MINING MODEL modelName
    | OPERATOR operatorName
    | TABLE (tableName | viewName)
    | VIEW viewName
    ) IS STRING_
    ;

flashbackTable
    : FLASHBACK TABLE tableName (COMMA_ tableName)* TO (
    (scnTimestampClause | restorePointClause) ((ENABLE | DISABLE) TRIGGERS)?
    | BEFORE DROP renameToTable?
    | BEFORE TRUNCATE
    )
    ;

scnTimestampLsn
    : SCN | TIMESTAMP | LSN
    ;

// DM https://eco.dameng.com/document/dm/zh-cn/pm/external-link.html
createDatabaseLink
    : CREATE (OR REPLACE)? PUBLIC? LINK ifNotExists? dbLink CONNECT linkType? WITH username IDENTIFIED BY password USING connectString optionClause?
    ;

// DM
linkType
    : stringLiterals
    ;

// DM
optionClause
    : OPTION LP_ dblinkOptions RP_
    ;

// DM
dblinkOptions
    : (
        LOCAL_CODE EQ_ stringLiterals
        | CONVERT_MODE EQ_ INTEGER_
        | BYTES_IN_CHAR EQ_ INTEGER_
        | DB_TYPE EQ_ stringLiterals
        | DATA_CHARSET EQ_ stringLiterals
        | CASE_OPT EQ_ stringLiterals
        | ROLLBACK_OPTION EQ_ INTEGER_
    ) (COMMA_ dblinkOptions)*
    ;

dropDatabaseLink
    : DROP PUBLIC? LINK ifExists? dbLink
    ;

alterSequence
    : ALTER SEQUENCE sequenceName (alterSequenceClause+ | RENAME TO sequenceName)
    ;

dropOperator
    : DROP OPERATOR ifExists? (schemaName DOT_)? operatorName operatorObjectClause FORCE?
    ;

operatorObjectClause
    : LP_ (dataType | NULL) COMMA_ (dataType | NULL) RP_
    ;

// DM  https://eco.dameng.com/document/dm/zh-cn/pm/materialized-view.html#7.1%20%E7%89%A9%E5%8C%96%E8%A7%86%E5%9B%BE%E7%9A%84%E5%AE%9A%E4%B9%89
createMaterializedView
    : CREATE MATERIALIZED VIEW materializedViewName (OF typeName )? materializedViewColumnClause? (materializedViewPrebuiltClause | materializedViewTableClause) materializedViewUsingClause? createMvRefresh? (FOR UPDATE)? ( (DISABLE | ENABLE) QUERY REWRITE )? AS selectSubquery
    ;

materializedViewPrebuiltClause
    : ((FOR tableName)? ON PREBUILT TABLE ( (WITH | WITHOUT) REDUCED PRECISION)? | physicalProperties?  (CACHE | NOCACHE)? parallelClause? buildClause?)
    ;

// DM
materializedViewTableClause
    : (BUILD IMMEDIATE | BUILD DEFERRED)? tablespaceClause? storageClause?
    ;

createMvRefreshOptions
    : ( (FAST | COMPLETE | FORCE) | ON (DEMAND | COMMIT | STATEMENT) | ((START WITH | NEXT) dateValue)+ | WITH (PRIMARY KEY | ROWID) | USING ( DEFAULT (MASTER | LOCAL)? ROLLBACK SEGMENT | (MASTER | LOCAL)? ROLLBACK SEGMENT rb_segment=identifier ) | USING (ENFORCED | TRUSTED) CONSTRAINTS
    | USING (DEFAULT | TRUNCATE | DELETE))
    ;

alterMvRefresh
    : REFRESH ( ((FAST | COMPLETE | FORCE) (START WITH dateValue)? (NEXT dateValue)?)
    | ON DEMAND
    | ON COMMIT
    | START WITH dateValue
    | NEXT dateValue
    | WITH PRIMARY KEY
    | USING DEFAULT (MASTER ROLLBACK SEGMENT)?
    | (USING TRUNCATE | USING DELETE | WITH ROWID)
    | USING MASTER ROLLBACK SEGMENT rollbackSegment
    | USING ENFORCED CONSTRAINTS
    | USING TRUSTED CONSTRAINTS)
    ;

permanentTablespaceAttrs
    : MINIMUM EXTEND sizeClause
    | BLOCKSIZE INTEGER_ capacityUnit?
    | loggingClause
    | FORCE LOGGING
    | tablespaceEncryptionClause
    | defaultTablespaceParams
    | (ONLINE|OFFLINE)
    | extentManagementClause
    | segmentManagementClause
    | flashbackModeClause
    | lostWriteProtection
    | cacheClause
    | tablespaceEncryptClause
    | hugeClause
    | tablespaceStorageClause
    ;

// DM
cacheClause
    : CACHE EQ_ DOUBLE_QUOTED_TEXT
    ;

// DM
tablespaceEncryptClause
    : ENCRYPT WITH name (BY WRAPPED? password)?
    ;

// DM
hugeClause
    : (WITH | ADD) HUGE PATH (fileName | asmFileName) (GREAT | MICRO)?
    ;

// DM
tablespaceStorageClause
    : STORAGE LP_ ON name RP_
    ;

alterTablespace
    : ALTER TABLESPACE tablespaceName
    ( defaultTablespaceParams
    | MINIMUM EXTENT sizeClause
    | RESIZE sizeClause
    | COALESCE
    | SHRINK SPACE (KEEP sizeClause)?
    | RENAME TO newTablespaceName
    | (BEGIN | END) BACKUP
    | datafileTempfileClauses
    | tablespaceLoggingClauses
    | tablespaceGroupClause
    | tablespaceStateClauses
    | autoextendClause
    | flashbackModeClause
    | tablespaceRetentionClause
    | alterTablespaceEncryption
    | lostWriteProtection
    | cacheClause
    | hugeClause
    | OPTIMIZE name
    )
    ;

datafileTempfileClauses
    : ADD (DATAFILE | TEMPFILE) (fileSpecification (COMMA_ fileSpecification)*)?
    | DROP (DATAFILE | TEMPFILE) (fileName | fileNumber)
    | SHRINK TEMPFILE (fileName | fileNumber) (KEEP sizeClause)?
    | RENAME DATAFILE fileName (COMMA_ fileName)* TO fileName (COMMA_ fileName)*
    | (DATAFILE | TEMPFILE) (ONLINE | OFFLINE)
    | RESIZE DATAFILE fileName TO sizeClause  // DM
    | DATAFILE fileName (COMMA_ fileName)* autoextendClause     //DM
    ;

tablespaceStateClauses
    : ONLINE | OFFLINE (NORMAL | TEMPORARY | IMMEDIATE)? | READ (ONLY | WRITE) | (PERMANENT | TEMPORARY) | CORRUPT
    ;

// 适配DM方法声明可为空
mapOrderFunctionSpec
    : (MAP | ORDER) MEMBER functionSpec
    | MAP (MEMBER | STATIC)? functionSpec  // DM
    ;

// Oracle https://docs.oracle.com/en/database/oracle/oracle-database/23/sqlrf/CREATE-JAVA.html
// DM https://eco.dameng.com/document/dm/zh-cn/pm/class-type.html#12.2%20JAVA%20CLASS%20%E7%B1%BB%E5%9E%8B
createJava
    : CREATE (OR REPLACE)? (AND (RESOLVE | COMPILE))? NOFORCE? JAVA ifNotExists?
    ((SOURCE | RESOURCE) NAMED (schemaName DOT_)? primaryName
    | CLASS (SCHEMA schemaName)?) sharingClause? invokerRightsClause? resolveClauses?
    (javaUsingClause | AS sourceText)
    | CREATE (OR REPLACE)? JAVA (PUBLIC | ABSTRACT | FINAL)* CLASS className (EXTENDS className)? sourceText     // DM
    ;

plsqlProcedureSource
    : (schemaName DOT_)? name (
        dmEncryptionOrCalculate parameterDeclarationList? sharingClause? ((defaultCollationClause | invokerRightsClause | accessibleByClause)*)? (IS | AS) (callSpec | declareSection? body)
        | WRAPPED stringLiterals   // DM
    )
    ;

// DM
dmEncryptionOrCalculate
    : (WITH ENCRYPTION)? (FOR CALCULATE)?
    ;

plsqlFunctionSource
    : (schemaName DOT_)? name (dmEncryptionOrCalculate parameterDeclarationList? returnDateType?
    sharingClause? (invokerRightsClause
    | accessibleByClause
    | defaultCollationClause
    | deterministicClause
    | parallelEnableClause
    | resultCacheClause
    | aggregateClause
    | pipelinedClause
    | sqlMacroClause)*
    (
        (IS | AS) (callSpec | declareSection? body)
        | EXTERNAL pathString AND? alias? USING (SINGLE_C | CS | JAVA)  //DM  https://eco.dameng.com/document/dm/zh-cn/pm/external-function.html#10.1.2%20C%20%E5%A4%96%E9%83%A8%E5%87%BD%E6%95%B0%E5%88%9B%E5%BB%BA
    ))
    | WRAPPED stringLiterals  // DM
    ;

statement
    : (SIGNED_LEFT_SHIFT_ label SIGNED_RIGHT_SHIFT_)*
        (assignStatement
        | basicLoopStatement
        | caseStatement
        | closeStatement
        | collectionMethodCall
        | continueStatement
        | cursorForLoopStatement
        | executeImmediateStatement
        | exitStatement
        | fetchStatement
        | forLoopStatement
        | forallStatement
        | gotoStatement
        | ifStatement
        | modifyingStatement // ?
        | nullStatement
        | openStatement
        | openForStatement
        | pipeRowStatement
        | plsqlBlock
        | raiseStatement
        | returnStatement
        | sqlStatementInPlsql
        | whileLoopStatement
        | procedureCall
        | printStatement
        )
    ;

// DM
printStatement
    : PRINT expr SEMI_
    ;

declareItem
    : typeDefinition
    | cursorDeclaration
    | itemDeclaration
    | functionDeclaration
    | procedureDeclaration
    | cursorDefinition
    | functionDefinition
    | procedureDefinition
    | pragma
    | recodeDefinition
    ;

recodeDefinition
    : RECORD LP_ (variableName dataType SEMI_)* RP_ ((ASSIGNMENT_OPERATOR_ | DEFAULT | ASSIGN) expr)? SEMI_
    ;

cursorDefinition
    : CURSOR variableName cursorParameterList? (RETURN rowtype)? IS select SEMI_
    | CURSOR variableName (FOR select)? SEMI_   // DM
    ;

variableDeclaration
    : variableName dataType ((NOT NULL)? (ASSIGNMENT_OPERATOR_ | DEFAULT | ASSIGN) expr)? SEMI_
    | variableName EXCEPTION FOR MINUS_ INTEGER_ (COMMA_ INTEGER_)* SEMI_
    ;

plsqlTriggerSource
    : triggerName dmEncryptionOrCalculate sharingClause? defaultCollationClause? (simpleDmlTrigger | systemTrigger | timeTrigger)
    ;

// DM
timeTrigger
    : AFTER TIMER ON DATABASE (EXECUTE AT stringLiterals)? (WHEN expr)? timeTriggerClause (EXECUTE AT INTEGER_)? (WHEN expr)? triggerBody
    ;

// DM
timeTriggerClause
    : FOR ONCE AT DATETIME expr?
    | timeTriggerRate timeTriggerTimes duaringDate?
    ;

// DM
timeTriggerRate
    : FOR EACH INTEGER_ MONTH DAY INTEGER_ (OF WEEK (INTEGER_ | LAST))?
    | FOR EACH INTEGER_ WEEK INTEGER_ (COMMA_ INTEGER_)*
    | FOR EACH INTEGER_ DAY
    ;

// DM
timeTriggerTimes
    : AT TIME expr
    | duaringTime? FOR EACH INTEGER_ (MINUTE | SECOND)
    ;

// DM
duaringTime
    : NULL
    | FROM TIME expr (TO TIME expr)?
    ;

// DM
duaringDate
    : NULL
    | FROM DATETIME expr (TO DATETIME expr)
    ;

simpleDmlTrigger
    : (BEFORE | AFTER) (dmlEventClause | dmEventClause) dmReferencingClause? forEachClause? dmTriggerOrdringClause? triggerBody
    ;

// DM
dmReferencingClause
    : REFERENCING ((OLD ROW? AS? variableName)? (NEW  ROW? AS? variableName))
    ;

// DM
dmTriggerOrdringClause
    : (FOLLOWS | PRECEDES (triggerName (COMMA_ triggerName)*)?)
    ;

forEachClause
    : FOR EACH ROW
    | FOR EACH STATEMENT
    ;

dmlEventClause
    : dmlEventElement (OR dmlEventElement)* (LOCAL?) ON viewName
    ;

// DM
dmEventClause
    : (
        dmDdlEventElement (OR dmDdlEventElement)*
        | dmSystemEventElement (OR dmSystemEventElement)*
    ) ON name (EXECUTE AT stringLiterals INTEGER_)? (WHEN expr)?
    ;

// DM
dmDdlEventElement
    : DDL | (CREATE | ALTER | DROP | GRANT | REVOKE | TRUNCATE | COMMENT)
    ;

// DM
dmSystemEventElement
    : LOGIN | LOGOUT | SERERR | BACKUP DATABASE | RESTORE DATABASE | AUDIT | NOAUDIT | TIMER | STARTUP | SHUTDOWN | CHECKPOIN
    ;

systemTriggerEvent
    : (dmlEventElement)
    | ddlEvent
    ;

plsqlPackageBodySource
    : packageName dmEncryptionOrCalculate sharingClause? (IS | AS) declareSection initializeSection? END name?
    ;

setTransaction
    : SET TRANSACTION ((READ (ONLY | WRITE)
    | ISOLATION LEVEL (SERIALIZABLE | READ (COMMITTED | UNCOMMITTED))
    | USE ROLLBACK SEGMENT rollbackSegment) (NAME stringLiterals)?
    | NAME stringLiterals)
    ;

writeClause
    : WRITE (WAIT | NOWAIT)? (IMMEDIATE | BATCH)?
    | (IMMEDIATE | BATCH) (WAIT | NOWAIT)?
    | (IMMEDIATE | BATCH)? (WAIT | NOWAIT)
    ;

sqlStatementInPlsql
    : (commit
    | collectionMethodCall
    | delete
    | insert
    | lockTable
    | merge
    | select
    | rollback
    | savepoint
    | setTransaction
    | update
    | releaseSavePoint   // DM
    ) SEMI_
    ;

dateTimeLiterals
    : (DATE | TIME | TIMESTAMP) stringLiterals
    | LBE_ identifier stringLiterals RBE_
    | SYSDATE PLUS_ numberValue
    ;

operatorName
    : (owner DOT_)? name
    | dmoperatorName
    ;

dmoperatorName
    : (owner DOT_)? OPERATOR_
    ;

partitionKeyValue
    : INTEGER_ | dateTimeLiterals | toDateFunction | expr
    ;

subpartitionKeyValue
    : INTEGER_ | dateTimeLiterals | expr
    ;

expr
    : expr andOperator expr
    | expr orOperator expr
    | notOperator expr
    | LP_ expr RP_
    | booleanPrimary
    | expr datetimeExpr
    | multisetExpr
    | builtinFunctionsExpr
    | dataManipulationLanguageExpr
    | jsonExpr
    | operatorExpr
    ;
// DM
operatorExpr
    : (simpleExpr dmoperatorName simpleExpr? | dmoperatorName simpleExpr)
    ;
// DM
jsonExpr
    : stringLiterals TYPE_CAST_ (JSON | JSONB) jsonOperator expr
    ;

jsonOperator
    : JSON_EXTRACT_ | JSON_EXTRACT_TEXT_ | JSONB_CONTAIN_RIGHT_ | MINUS_ | OR_
    ;

booleanPrimary
    : booleanPrimary IS NOT? (TRUE | FALSE | UNKNOWN | NULL | dmIsExpr)
    | booleanPrimary IS NOT? OF TYPE? LP_ (typeName (COMMA_ typeName)*) RP_
    | (PRIOR | DISTINCT) predicate
    | CONNECT_BY_ROOT predicate
    | booleanPrimary SAFE_EQ_ predicate
    | booleanPrimary comparisonOperator (ALL | ANY | (SOME)) subquery
    | booleanPrimary comparisonOperator predicate
    | predicate
    ;

dmIsExpr
    : (functionCall | (JSON (LP_ (STRICT|LAX) RP_)? ((WITH | WITHOUT) UNIQUE KEYS)?))
    ;

aggregationFunctionName
    : MAX | MIN | SUM | COUNT | AVG | GROUPING | LISTAGG | PERCENT_RANK | PERCENTILE_CONT | PERCENTILE_DISC | CUME_DIST | RANK
    | REGR_SLOPE | REGR_INTERCEPT | REGR_COUNT | REGR_R2 | REGR_AVGX | REGR_AVGY | REGR_SXX | REGR_SYY | REGR_SXY
    | COLLECT | CORR | CORR_S | CORR_K | COVAR_POP | COVAR_SAMP | DENSE_RANK | FIRST
    | GROUP_ID | GROUPING_ID | LAST | MEDIAN | STATS_BINOMIAL_TEST | STATS_CROSSTAB | STATS_F_TEST | STATS_KS_TEST
    | STATS_MODE | STATS_MW_TEST | STATS_ONE_WAY_ANOVA | STATS_T_TEST_ONE | STATS_T_TEST_PAIRED | STATS_T_TEST_INDEP
    | STATS_T_TEST_INDEPU | STATS_WSR_TEST | STDDEV | STDDEV_POP | STDDEV_SAMP | VAR_POP | VAR_SAMP | VARIANCE
    | LISTAGG2
    ;

orderByItem
    : (columnName | numberLiterals | expr) (COLLATE columnCollationName)? (ASC | DESC)? (NULLS FIRST | NULLS LAST)?
    ;

dataType
    : characterSetDataType
    | intervalDatatype
    | typeAttribute
    | rowtypeAttribute
    | specialDatatype
    | dataTypeName (LARGE OBJECT)? dataTypeLength? datetimeTypeSuffix?
    | customDataType
    ;

intervalDatatype
    : INTERVAL (DAY | (HOUR | MINUTE)) (LP_ numberValue RP_)? TO SECOND (LP_ numberValue RP_)?  // 支持DM HOUR | MINUTE
    | INTERVAL YEAR (LP_ numberValue RP_)? TO MONTH
    | INTERVAL DAY (LP_ numberValue RP_)? TO (HOUR | MINUTE)    // DM
    | INTERVAL HOUR (LP_ numberValue RP_)? TO MINUTE            // DM
    ;

dataTypeName
    : CHARACTER | CHAR | NCHAR | RAW | VARCHAR | VARCHAR2 | NVARCHAR2 | LONG | LONG RAW | BLOB | CLOB | NCLOB | BINARY_FLOAT | BINARY_DOUBLE
    | BOOLEAN | PLS_INTEGER | BINARY_INTEGER | INTEGER | NUMBER | NATURAL | NATURALN | POSITIVE | POSITIVEN | SIGNTYPE
    | SIMPLE_INTEGER | BFILE | MLSLABEL | UROWID | DATE | TIMESTAMP | TIMESTAMP WITH TIME ZONE | TIMESTAMP WITH LOCAL TIME ZONE
    | INTERVAL DAY TO SECOND | INTERVAL YEAR TO MONTH | JSON | FLOAT | REAL | DOUBLE PRECISION | INT | SMALLINT
    | DECIMAL | NUMERIC | DEC | IDENTIFIER_ | XMLTYPE | ROWID | ANYDATA | ANYTYPE | ANYDATASET | UB2 | SB4 | TIME
    | (DOUBLE | INTERVAL YEAR | VARBINARY | DATETIME | INTERVAL MONTH | INTERVAL DAY | INTERVAL HOUR | INTERVAL MINUTE | INTERVAL SECOND
    | TEXT | LONGVARCHAR | BINARY | IMAGE)
    ;

datetimeTypeSuffix
    : WITH LOCAL? TIME ZONE | TO MONTH | TO SECOND (LP_ numberValue RP_)?
    | WITHOUT TIME ZONE
    ;

dataItem
    : variableName
    | pexpPfx
    ;

createProfile
    : CREATE MANDATORY? PROFILE profileName LIMIT (resourceParameters | passwordParameters | resourceLimitList)+ (CONTAINER EQ_ (CURRENT | ALL))?
    ;

plsqlTypeSource
    : typeName (dmEncryptionOrCalculate | FORCE?) (OID objectIdentifier=stringLiterals)? sharingClause? defaultCollationClause?
     (invokerRightsClause | accessibleByClause)* (objectBaseTypeDef | objectSubTypeDef)
    ;

plsqlTypeBodySource
    : typeName dmEncryptionOrCalculate sharingClause? (IS | AS) typeBodyDecl (COMMA_? typeBodyDecl)* END
    ;

createUserOption
    : collationOption
    | tablespaceOption
    | temporaryOption
    | quotaOption
    | profileOption
    | passwordOption
    | passwordPolicyOption
    | accountOption
    | encryptByOption
    | spaceLimitClause
    | readOnlyOption
    | allowIpOption
    | allowDtOption
    | ENABLE EDITIONS
    | containerOption
    ;

insert
    : withClause? INSERT hint? (insertSingleTable | insertMultiTable)
    ;

modifyColumnSpecification
    : MODIFY (LP_? modifyColProperties (COMMA_ modifyColProperties)* RP_? | modifyColSubstitutable | columnDefinition | virtualColumnDefinition)
    ;

alterProfile
    : ALTER PROFILE profileName LIMIT (resourceParameters | passwordParameters | resourceLimitList)+ (CONTAINER EQ_ (CURRENT | ALL))?
    ;

systemTrigger
    : (BEFORE | AFTER | INSTEAD OF) (systemTriggerEvent (OR systemTriggerEvent)* | databaseEvent (OR databaseEvent)* | dmlEvent) ON LOCAL? ((PLUGGABLE? DATABASE) | (schemaName DOT_)? SCHEMA?) tableName? dmReferencingClause? triggerBody
    ;

plsqlPackageSource
    : packageName dmEncryptionOrCalculate sharingClause? (
      invokerRightsClause | defaultCollationClause | accessibleByClause
    )* (IS | AS) packageItemList+ END name?
    ;

unreservedWord
    : unreservedWord1 | unreservedWord2 | unreservedWord3 | reservedWord | capacityUnit | timeUnit | A | JSONB
    ;

jsonParamsOptions
    : PRETTY
    | ASCII
    | jsonReturningClause
    | jsonWrapperOptions
    | jsonEmptyErrorOptions
    | jsonColumnsClause
    ;

jsonReturningClause
    : RETURNING (VARCHAR | VARCHAR2 | NUMBER | DATE | DATETIME | VARBINARY | CLOB | JSON)
    ;

jsonWrapperOptions
    : WITH (CONDITIONAL | UNCONDITIONAL)? (ARRAY)? WRAPPER
    | WITHOUT (ARRAY)? WRAPPER
    ;

analyticFunction
    : specifiedAnalyticFunctionName = (LEAD | LAG) ((LP_ expr leadLagInfo? RP_ respectOrIgnoreNulls?) | (LP_ expr respectOrIgnoreNulls? leadLagInfo? RP_)) overClause
    | specifiedAnalyticFunctionName = (NTILE | MEDIAN | RATIO_TO_REPORT) LP_ expr RP_ overClause?
    | specifiedAnalyticFunctionName = NTH_VALUE LP_ expr COMMA_ expr RP_ fromFirstOrLast? respectOrIgnoreNulls? overClause
    | specifiedAnalyticFunctionName = (PERCENTILE_CONT | PERCENTILE_DISC | LISTAGG | LISTAGG2) LP_ expr (COMMA_ expr)* RP_ withinClause overClause?
    | specifiedAnalyticFunctionName = (CORR | COVAR_POP | COVAR_SAMP) LP_ expr COMMA_ expr RP_ overClause?
    | specifiedAnalyticFunctionName = (PERCENT_RANK | RANK | ROW_NUMBER) LP_ RP_ overClause
    | analyticFunctionName LP_ dataType* RP_ overClause
    ;
