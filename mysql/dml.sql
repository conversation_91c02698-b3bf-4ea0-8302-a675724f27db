DELETE FROM t1 AS a1 WHERE a1.c1 = 2;
DELETE FROM page_hit ORDER BY timestamp LIMIT 1000000;
DELETE post FROM blog INNER JOIN post WHERE blog.id = post.blog_id;
DELETE FROM t1 WHERE c1 IN (SELECT b.c1 FROM t1 b WHERE b.c2=0);
UPDATE t1 SET c1=c1+1 WHERE c2=(SELECT MAX(c2) FROM t1);
UPDATE table_name SET column1 = value1, column2 = value2 WHERE id=100;
UPDATE tab1, tab2 SET tab1.column1 = value1, tab1.column2 = value2 WHERE tab1.id = tab2.id;
ANALYZE FORMAT=JSON
SELECT COUNT(*)
FROM customer
WHERE
    (SELECT SUM(o_totalprice) FROM orders WHERE o_custkey=c_custkey) > 1000*1000;

-- Test case for now() function issue
INSERT INTO t_order_item (item_id, order_id, user_id, status, creation_date) VALUES (?, ?, ?, 'insert', now());

SELECT * FROM JSON_TABLE('[{"name": 2}]','$[*]' COLUMNS( name INT PATH '$.name' error on empty)) as t
