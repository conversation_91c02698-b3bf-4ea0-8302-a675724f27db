lexer grammar MySQLLexer;

options {
    caseInsensitive = true;
}

ACCOUNT                                : 'ACCOUNT';
ACTION                                 : 'ACTION';
ACTIVE                                 : 'ACTIVE';
ADD                                    : 'ADD';
ADMIN                                  : 'ADMIN';
AFTER                                  : 'AFTER';
AGAINST                                : 'AGAINST';
AGGREGATE                              : 'AGGREGATE';
ALGORITHM                              : 'ALGORITHM';
ALL                                    : 'ALL';
ALTER                                  : 'ALTER';
ALWAYS                                 : 'ALWAYS';
ANALYZE                                : 'ANALYZE';
AND                                    : 'AND';
ANY                                    : 'ANY';
ARRAY                                  : 'ARRAY';
AS                                     : 'AS';
ASC                                    : 'ASC';
ASCII                                  : 'ASCII';
AT                                     : 'AT';
ATTRIBUTE                              : 'ATTRIBUTE';
AUTO_INCREMENT                         : 'AUTO_INCREMENT';
AUTOCOMMIT                             : 'AUTOCOMMIT';
AUTOEXTEND_SIZE                        : 'AUTOEXTEND_SIZE';
AVG                                    : 'AVG';
AVG_ROW_LENGTH                         : 'AVG_ROW_LENGTH';
BACKUP                                 : 'BACKUP';
BEFORE                                 : 'BEFORE';
BEGIN                                  : 'BEGIN';
BETWEEN                                : 'BETWEEN';
BIGINT                                 : 'BIGINT';
BINARY                                 : 'BINARY';
BINLOG                                 : 'BINLOG';
BIT                                    : 'BIT';
BIT_XOR                                : 'BIT_XOR';
BLOB                                   : 'BLOB';
BLOCK                                  : 'BLOCK';
BOOL                                   : 'BOOL';
BOOLEAN                                : 'BOOLEAN';
BOTH                                   : 'BOTH';
BTREE                                  : 'BTREE';
BUCKETS                                : 'BUCKETS';
BY                                     : 'BY';
BYTE                                   : 'BYTE';
CACHE                                  : 'CACHE';
CALL                                   : 'CALL';
CASCADE                                : 'CASCADE';
CASCADED                               : 'CASCADED';
CASE                                   : 'CASE';
CAST                                   : 'CAST';
CATALOG_NAME                           : 'CATALOG_NAME';
CHAIN                                  : 'CHAIN';
CHANGE                                 : 'CHANGE';
CHANGED                                : 'CHANGED';
CHANNEL                                : 'CHANNEL';
CHAR                                   : 'CHAR';
CHAR_VARYING                           : CHAR ' ' VARYING;
CHARACTER                              : 'CHARACTER';
CHARACTER_VARYING                      : CHARACTER ' ' VARYING;
CHARSET                                : 'CHARSET';
CHECK                                  : 'CHECK';
CHECKSUM                               : 'CHECKSUM';
CIPHER                                 : 'CIPHER';
CLASS_ORIGIN                           : 'CLASS_ORIGIN';
CLIENT                                 : 'CLIENT';
CLONE                                  : 'CLONE';
CLOSE                                  : 'CLOSE';
COALESCE                               : 'COALESCE';
CODE                                   : 'CODE';
COLLATE                                : 'COLLATE';
COLLATION                              : 'COLLATION';
COLUMN                                 : 'COLUMN';
COLUMN_FORMAT                          : 'COLUMN_FORMAT';
COLUMN_NAME                            : 'COLUMN_NAME';
COLUMNS                                : 'COLUMNS';
COMMENT                                : 'COMMENT';
COMMIT                                 : 'COMMIT';
COMMITTED                              : 'COMMITTED';
COMPACT                                : 'COMPACT';
COMPLETION                             : 'COMPLETION';
COMPONENT                              : 'COMPONENT';
COMPRESSED                             : 'COMPRESSED';
COMPRESSION                            : 'COMPRESSION';
CONCURRENT                             : 'CONCURRENT';
CONDITION                              : 'CONDITION';
CONNECTION                             : 'CONNECTION';
CONSISTENT                             : 'CONSISTENT';
CONSTRAINT                             : 'CONSTRAINT';
CONSTRAINT_CATALOG                     : 'CONSTRAINT_CATALOG';
CONSTRAINT_NAME                        : 'CONSTRAINT_NAME';
CONSTRAINT_SCHEMA                      : 'CONSTRAINT_SCHEMA';
CONTAINS                               : 'CONTAINS';
CONTEXT                                : 'CONTEXT';
CONTINUE                               : 'CONTINUE';
CONVERT                                : 'CONVERT';
COPY                                   : 'COPY';
COUNT                                  : 'COUNT';
CPU                                    : 'CPU';
CREATE                                 : 'CREATE';
CROSS                                  : 'CROSS';
CUME_DIST                              : 'CUME_DIST';
CURRENT                                : 'CURRENT';
CURRENT_DATE                           : 'CURRENT_DATE';
CURRENT_TIME                           : 'CURRENT_TIME';
CURRENT_TIMESTAMP                      : 'CURRENT_TIMESTAMP';
CURRENT_USER                           : 'CURRENT_USER';
CURSOR                                 : 'CURSOR';
CURSOR_NAME                            : 'CURSOR_NAME';
DATA                                   : 'DATA';
DATABASE                               : 'DATABASE';
DATABASES                              : 'DATABASES';
DATAFILE                               : 'DATAFILE';
DATE                                   : 'DATE';
DATETIME                               : 'DATETIME';
DAY                                    : 'DAY';
DAY_HOUR                               : 'DAY_HOUR';
DAY_MICROSECOND                        : 'DAY_MICROSECOND';
DAY_MINUTE                             : 'DAY_MINUTE';
DAY_SECOND                             : 'DAY_SECOND';
DEALLOCATE                             : 'DEALLOCATE';
DEC                                    : 'DEC';
DECIMAL                                : 'DECIMAL';
DECLARE                                : 'DECLARE';
DEFAULT                                : 'DEFAULT';
DEFAULT_AUTH                           : 'DEFAULT_AUTH';
DEFINER                                : 'DEFINER';
DEFINITION                             : 'DEFINITION';
DELAY_KEY_WRITE                        : 'DELAY_KEY_WRITE';
DELAYED                                : 'DELAYED';
DELETE                                 : 'DELETE';
DELIMITER                              : 'DELIMITER';
DENSE_RANK                             : 'DENSE_RANK';
DESC                                   : 'DESC';
DESCRIBE                               : 'DESCRIBE';
DESCRIPTION                            : 'DESCRIPTION';
DETERMINISTIC                          : 'DETERMINISTIC';
DIAGNOSTICS                            : 'DIAGNOSTICS';
DIRECTORY                              : 'DIRECTORY';
DISABLE                                : 'DISABLE';
DISCARD                                : 'DISCARD';
DISK                                   : 'DISK';
DISTINCT                               : 'DISTINCT';
DISTINCTROW                            : 'DISTINCTROW';
DIV                                    : 'DIV';
DO                                     : 'DO';
DOUBLE                                 : 'DOUBLE';
DROP                                   : 'DROP';
DUAL                                   : 'DUAL';
DUMPFILE                               : 'DUMPFILE';
DUPLICATE                              : 'DUPLICATE';
DYNAMIC                                : 'DYNAMIC';
EACH                                   : 'EACH';
ELSE                                   : 'ELSE';
ELSEIF                                 : 'ELSEIF';
EMPTY                                  : 'EMPTY';
ENABLE                                 : 'ENABLE';
ENCLOSED                               : 'ENCLOSED';
ENCRYPTION                             : 'ENCRYPTION';
END                                    : 'END';
ENDS                                   : 'ENDS';
ENFORCED                               : 'ENFORCED';
ENGINE                                 : 'ENGINE';
ENGINE_ATTRIBUTE                       : 'ENGINE_ATTRIBUTE';
ENGINES                                : 'ENGINES';
ENUM                                   : 'ENUM';
ERROR                                  : 'ERROR';
ERRORS                                 : 'ERRORS';
ESCAPE                                 : 'ESCAPE';
ESCAPED                                : 'ESCAPED';
EVENT                                  : 'EVENT';
EVENTS                                 : 'EVENTS';
EVERY                                  : 'EVERY';
EXCEPT                                 : 'EXCEPT';
EXCHANGE                               : 'EXCHANGE';
EXCLUSIVE                              : 'EXCLUSIVE';
EXECUTE                                : 'EXECUTE';
EXISTS                                 : 'EXISTS';
EXIT                                   : 'EXIT';
EXPANSION                              : 'EXPANSION';
EXPIRE                                 : 'EXPIRE';
EXPLAIN                                : 'EXPLAIN';
EXPORT                                 : 'EXPORT';
EXTENDED                               : 'EXTENDED';
EXTENT_SIZE                            : 'EXTENT_SIZE';
EXTRACT                                : 'EXTRACT';
FAILED_LOGIN_ATTEMPTS                  : 'FAILED_LOGIN_ATTEMPTS';
FALSE                                  : 'FALSE';
FAST                                   : 'FAST';
FAULTS                                 : 'FAULTS';
FETCH                                  : 'FETCH';
FIELDS                                 : 'FIELDS' -> type(COLUMNS);
FILE                                   : 'FILE';
FILE_BLOCK_SIZE                        : 'FILE_BLOCK_SIZE';
FILTER                                 : 'FILTER';
FIRST                                  : 'FIRST';
FIRST_VALUE                            : 'FIRST_VALUE';
FIXED                                  : 'FIXED';
FLOAT                                  : 'FLOAT';
FLUSH                                  : 'FLUSH';
FOLLOWING                              : 'FOLLOWING';
FOLLOWS                                : 'FOLLOWS';
FOR                                    : 'FOR';
FORCE                                  : 'FORCE';
FOREIGN                                : 'FOREIGN';
FORMAT                                 : 'FORMAT';
FOUND                                  : 'FOUND';
FROM                                   : 'FROM';
FULL                                   : 'FULL';
FULLTEXT                               : 'FULLTEXT';
FUNCTION                               : 'FUNCTION';
GENERAL                                : 'GENERAL';
GENERATE                               : 'GENERATE';
GENERATED                              : 'GENERATED';
GEOMCOLLECTION                         : 'GEOMCOLLECTION';
GEOMETRY                               : 'GEOMETRY';
GEOMETRYCOLLECTION                     : 'GEOMETRYCOLLECTION';
GET                                    : 'GET';
GLOBAL                                 : 'GLOBAL';
GRANT                                  : 'GRANT';
GRANTS                                 : 'GRANTS';
GROUP                                  : 'GROUP';
GROUP_CONCAT                           : 'GROUP_CONCAT';
GROUP_REPLICATION                      : 'GROUP_REPLICATION';
GROUPING                               : 'GROUPING';
HANDLER                                : 'HANDLER';
HASH                                   : 'HASH';
HAVING                                 : 'HAVING';
HELP                                   : 'HELP';
HIGH_PRIORITY                          : 'HIGH_PRIORITY';
HISTOGRAM                              : 'HISTOGRAM';
HISTORY                                : 'HISTORY';
HOST                                   : 'HOST';
HOSTS                                  : 'HOSTS';
HOUR                                   : 'HOUR';
HOUR_MICROSECOND                       : 'HOUR_MICROSECOND';
HOUR_MINUTE                            : 'HOUR_MINUTE';
HOUR_SECOND                            : 'HOUR_SECOND';
IDENTIFIED                             : 'IDENTIFIED';
IF                                     : 'IF';
IGNORE                                 : 'IGNORE';
IMPORT                                 : 'IMPORT';
IN                                     : 'IN';
INACTIVE                               : 'INACTIVE';
INDEX                                  : 'INDEX';
INDEXES                                : 'INDEXES';
INFILE                                 : 'INFILE';
INITIAL_SIZE                           : 'INITIAL_SIZE';
INNER                                  : 'INNER';
INNODB                                 : 'INNODB';
INOUT                                  : 'INOUT';
INPLACE                                : 'INPLACE';
INSERT                                 : 'INSERT';
INSERT_METHOD                          : 'INSERT_METHOD';
INSTALL                                : 'INSTALL';
INSTANCE                               : 'INSTANCE';
INSTANT                                : 'INSTANT';
INT                                    : 'INT';
INTEGER                                : 'INTEGER';
INTERSECT                              : 'INTERSECT';
INTERVAL                               : 'INTERVAL';
INTO                                   : 'INTO';
INVISIBLE                              : 'INVISIBLE';
INVOKER                                : 'INVOKER';
IO                                     : 'IO';
IPC                                    : 'IPC';
IS                                     : 'IS';
ISOLATION                              : 'ISOLATION';
ISSUER                                 : 'ISSUER';
ITERATE                                : 'ITERATE';
JOIN                                   : 'JOIN';
JSON                                   : 'JSON';
JSON_ARRAY                             : 'JSON_ARRAY';
JSON_ARRAY_APPEND                      : 'JSON_ARRAY_APPEND';
JSON_ARRAY_INSERT                      : 'JSON_ARRAY_INSERT';
JSON_CONTAINS                          : 'JSON_CONTAINS';
JSON_CONTAINS_PATH                     : 'JSON_CONTAINS_PATH';
JSON_DEPTH                             : 'JSON_DEPTH';
JSON_EXTRACT                           : 'JSON_EXTRACT';
JSON_INSERT                            : 'JSON_INSERT';
JSON_KEYS                              : 'JSON_KEYS';
JSON_LENGTH                            : 'JSON_LENGTH';
JSON_MERGE                             : 'JSON_MERGE';
JSON_MERGE_PATCH                       : 'JSON_MERGE_PATCH';
JSON_MERGE_PRESERVE                    : 'JSON_MERGE_PRESERVE';
JSON_OBJECT                            : 'JSON_OBJECT';
JSON_OVERLAPS                          : 'JSON_OVERLAPS';
JSON_PRETTY                            : 'JSON_PRETTY';
JSON_QUOTE                             : 'JSON_QUOTE';
JSON_REMOVE                            : 'JSON_REMOVE';
JSON_REPLACE                           : 'JSON_REPLACE';
JSON_SCHEMA_VALID                      : 'JSON_SCHEMA_VALID';
JSON_SCHEMA_VALIDATION_REPORT          : 'JSON_SCHEMA_VALIDATION_REPORT';
JSON_SEARCH                            : 'JSON_SEARCH';
JSON_SET                               : 'JSON_SET';
JSON_STORAGE_FREE                      : 'JSON_STORAGE_FREE';
JSON_STORAGE_SIZE                      : 'JSON_STORAGE_SIZE';
JSON_TABLE                             : 'JSON_TABLE';
JSON_TYPE                              : 'JSON_TYPE';
JSON_UNQUOTE                           : 'JSON_UNQUOTE';
JSON_VALID                             : 'JSON_VALID';
JSON_VALUE                             : 'JSON_VALUE';
KEY                                    : 'KEY';
KEY_BLOCK_SIZE                         : 'KEY_BLOCK_SIZE';
KEYS                                   : 'KEYS';
KILL                                   : 'KILL';
LAG                                    : 'LAG';
LANGUAGE                               : 'LANGUAGE';
LAST                                   : 'LAST';
LAST_DAY                               : 'LAST_DAY';
LAST_VALUE                             : 'LAST_VALUE';
LATERAL                                : 'LATERAL';
LEAD                                   : 'LEAD';
LEADING                                : 'LEADING';
LEAVE                                  : 'LEAVE';
LEAVES                                 : 'LEAVES';
LEFT                                   : 'LEFT';
LESS                                   : 'LESS';
LEVEL                                  : 'LEVEL';
LIKE                                   : 'LIKE';
LIMIT                                  : 'LIMIT';
LINEAR                                 : 'LINEAR';
LINES                                  : 'LINES';
LINESTRING                             : 'LINESTRING';
LIST                                   : 'LIST';
LOAD                                   : 'LOAD';
LOCAL                                  : 'LOCAL';
LOCALTIME                              : 'LOCALTIME';
LOCALTIMESTAMP                         : 'LOCALTIMESTAMP';
LOCK                                   : 'LOCK';
LOCKED                                 : 'LOCKED';
LOGFILE                                : 'LOGFILE';
LOGS                                   : 'LOGS';
LONG                                   : 'LONG';
LONG_CHAR_VARYING                      : LONG ' ' CHAR ' ' VARYING;
LONG_VARCHAR                           : LONG ' ' VARCHAR;
LONGBLOB                               : 'LONGBLOB';
LONGTEXT                               : 'LONGTEXT';
LOOP                                   : 'LOOP';
LOW_PRIORITY                           : 'LOW_PRIORITY';
MASTER                                 : 'MASTER';
MASTER_LOG_FILE                        : 'MASTER_LOG_FILE';
MASTER_LOG_POS                         : 'MASTER_LOG_POS';
MATCH                                  : 'MATCH';
MAX                                    : 'MAX';
MAX_CONNECTIONS_PER_HOUR               : 'MAX_CONNECTIONS_PER_HOUR';
MAX_QUERIES_PER_HOUR                   : 'MAX_QUERIES_PER_HOUR';
MAX_ROWS                               : 'MAX_ROWS';
MAX_SIZE                               : 'MAX_SIZE';
MAX_UPDATES_PER_HOUR                   : 'MAX_UPDATES_PER_HOUR';
MAX_USER_CONNECTIONS                   : 'MAX_USER_CONNECTIONS';
MAXVALUE                               : 'MAXVALUE';
MEDIUM                                 : 'MEDIUM';
MEDIUMBLOB                             : 'MEDIUMBLOB';
MEDIUMINT                              : 'MEDIUMINT';
MEDIUMTEXT                             : 'MEDIUMTEXT';
MEMBER                                 : 'MEMBER';
MEMORY                                 : 'MEMORY';
MERGE                                  : 'MERGE';
MESSAGE_TEXT                           : 'MESSAGE_TEXT';
MICROSECOND                            : 'MICROSECOND';
MIDDLEINT                              : 'MIDDLEINT';
MIGRATE                                : 'MIGRATE';
MIN                                    : 'MIN';
MIN_ROWS                               : 'MIN_ROWS';
MINUTE                                 : 'MINUTE';
MINUTE_MICROSECOND                     : 'MINUTE_MICROSECOND';
MINUTE_SECOND                          : 'MINUTE_SECOND';
MOD                                    : 'MOD';
MODE                                   : 'MODE';
MODIFIES                               : 'MODIFIES';
MODIFY                                 : 'MODIFY';
MONTH                                  : 'MONTH';
MULTILINESTRING                        : 'MULTILINESTRING';
MULTIPOINT                             : 'MULTIPOINT';
MULTIPOLYGON                           : 'MULTIPOLYGON';
MUTEX                                  : 'MUTEX';
MYSQL_ADMIN                            : 'MYSQL_ADMIN';
MYSQL_ERRNO                            : 'MYSQL_ERRNO';
MYSQL_MAIN                             : 'MYSQL_MAIN';
NAME                                   : 'NAME';
NAMES                                  : 'NAMES';
NATIONAL                               : 'NATIONAL';
NATIONAL_CHAR                          : NATIONAL ' ' CHAR;
NATIONAL_CHAR_VARYING                  : NATIONAL ' ' CHAR_VARYING;
NATURAL                                : 'NATURAL';
NCHAR                                  : 'NCHAR';
NESTED                                 : 'NESTED';
NEVER                                  : 'NEVER';
NEXT                                   : 'NEXT';
NO                                     : 'NO';
NO_WRITE_TO_BINLOG                     : 'NO_WRITE_TO_BINLOG';
NODEGROUP                              : 'NODEGROUP';
NONE                                   : 'NONE';
NOT                                    : 'NOT';
NOWAIT                                 : 'NOWAIT';
NTH_VALUE                              : 'NTH_VALUE';
NTILE                                  : 'NTILE';
NULL                                   : 'NULL';
NULLS                                  : 'NULLS';
NUMBER                                 : 'NUMBER';
NUMERIC                                : 'NUMERIC';
NVARCHAR                               : 'NVARCHAR';
OF                                     : 'OF';
OFF                                    : 'OFF';
OFFSET                                 : 'OFFSET';
OJ                                     : 'OJ';
OLD                                    : 'OLD';
ON                                     : 'ON';
ONE                                    : 'ONE';
ONLY                                   : 'ONLY';
OPEN                                   : 'OPEN';
OPTIMIZE                               : 'OPTIMIZE';
OPTIMIZER_COSTS                        : 'OPTIMIZER_COSTS';
OPTION                                 : 'OPTION';
OPTIONAL                               : 'OPTIONAL';
OPTIONALLY                             : 'OPTIONALLY';
OPTIONS                                : 'OPTIONS';
OR                                     : 'OR';
ORDER                                  : 'ORDER';
ORDINALITY                             : 'ORDINALITY';
ORGANIZATION                           : 'ORGANIZATION';
OTHERS                                 : 'OTHERS';
OUT                                    : 'OUT';
OUTER                                  : 'OUTER';
OUTFILE                                : 'OUTFILE';
OVER                                   : 'OVER';
OWNER                                  : 'OWNER';
PACK_KEYS                              : 'PACK_KEYS';
PAGE                                   : 'PAGE';
PARSER                                 : 'PARSER';
PARTIAL                                : 'PARTIAL';
PARTITION                              : 'PARTITION';
PARTITIONING                           : 'PARTITIONING';
PARTITIONS                             : 'PARTITIONS';
PASSWORD                               : 'PASSWORD';
PASSWORD_LOCK_TIME                     : 'PASSWORD_LOCK_TIME';
PATH                                   : 'PATH';
PERCENT_RANK                           : 'PERCENT_RANK';
PERSIST                                : 'PERSIST';
PERSIST_ONLY                           : 'PERSIST_ONLY';
PHASE                                  : 'PHASE';
PLUGIN                                 : 'PLUGIN';
PLUGIN_DIR                             : 'PLUGIN_DIR';
PLUGINS                                : 'PLUGINS';
POINT                                  : 'POINT';
POLYGON                                : 'POLYGON';
PORT                                   : 'PORT';
POSITION                               : 'POSITION';
PRECEDES                               : 'PRECEDES';
PRECEDING                              : 'PRECEDING';
PRECISION                              : 'PRECISION';
PREPARE                                : 'PREPARE';
PRESERVE                               : 'PRESERVE';
PREV                                   : 'PREV';
PRIMARY                                : 'PRIMARY';
PRIVILEGES                             : 'PRIVILEGES';
PROCEDURE                              : 'PROCEDURE';
PROCESS                                : 'PROCESS';
PROCESSLIST                            : 'PROCESSLIST';
PROFILE                                : 'PROFILE';
PROFILES                               : 'PROFILES';
PROXY                                  : 'PROXY';
PURGE                                  : 'PURGE';
QUARTER                                : 'QUARTER';
QUERY                                  : 'QUERY';
QUICK                                  : 'QUICK';
RANDOM                                 : 'RANDOM';
RANGE                                  : 'RANGE';
RANK                                   : 'RANK';
READ                                   : 'READ';
READS                                  : 'READS';
REAL                                   : 'REAL';
REBUILD                                : 'REBUILD';
RECOVER                                : 'RECOVER';
RECURSIVE                              : 'RECURSIVE';
REDO_BUFFER_SIZE                       : 'REDO_BUFFER_SIZE';
REDO_LOG                               : 'REDO_LOG';
REDUNDANT                              : 'REDUNDANT';
REFERENCE                              : 'REFERENCE';
REFERENCES                             : 'REFERENCES';
REGEXP                                 : 'REGEXP';
RELAY                                  : 'RELAY';
RELAY_LOG_FILE                         : 'RELAY_LOG_FILE';
RELAY_LOG_POS                          : 'RELAY_LOG_POS';
RELAY_THREAD                           : 'RELAY_THREAD';
RELAYLOG                               : 'RELAYLOG';
RELEASE                                : 'RELEASE';
RELOAD                                 : 'RELOAD';
REMOVE                                 : 'REMOVE';
RENAME                                 : 'RENAME';
REORGANIZE                             : 'REORGANIZE';
REPAIR                                 : 'REPAIR';
REPEAT                                 : 'REPEAT';
REPEATABLE                             : 'REPEATABLE';
REPLACE                                : 'REPLACE';
REPLICA                                : 'REPLICA';
REPLICAS                               : 'REPLICAS';
REPLICATE_DO_DB                        : 'REPLICATE_DO_DB';
REPLICATE_DO_TABLE                     : 'REPLICATE_DO_TABLE';
REPLICATE_IGNORE_DB                    : 'REPLICATE_IGNORE_DB';
REPLICATE_IGNORE_TABLE                 : 'REPLICATE_IGNORE_TABLE';
REPLICATE_REWRITE_DB                   : 'REPLICATE_REWRITE_DB';
REPLICATE_WILD_DO_TABLE                : 'REPLICATE_WILD_DO_TABLE';
REPLICATE_WILD_IGNORE_TABLE            : 'REPLICATE_WILD_IGNORE_TABLE';
REPLICATION                            : 'REPLICATION';
REQUIRE                                : 'REQUIRE';
RESET                                  : 'RESET';
RESIGNAL                               : 'RESIGNAL';
RESOURCE                               : 'RESOURCE';
RESPECT                                : 'RESPECT';
RESTART                                : 'RESTART';
RESTRICT                               : 'RESTRICT';
RESUME                                 : 'RESUME';
RETAIN                                 : 'RETAIN';
RETURN                                 : 'RETURN';
RETURNED_SQLSTATE                      : 'RETURNED_SQLSTATE';
RETURNING                              : 'RETURNING';
RETURNS                                : 'RETURNS';
REUSE                                  : 'REUSE';
REVERSE                                : 'REVERSE';
REVOKE                                 : 'REVOKE';
RIGHT                                  : 'RIGHT';
RLIKE                                  : 'RLIKE';
ROLE                                   : 'ROLE';
ROLLBACK                               : 'ROLLBACK';
ROLLUP                                 : 'ROLLUP';
ROTATE                                 : 'ROTATE';
ROUTINE                                : 'ROUTINE';
ROW                                    : 'ROW';
ROW_COUNT                              : 'ROW_COUNT';
ROW_FORMAT                             : 'ROW_FORMAT';
ROW_NUMBER                             : 'ROW_NUMBER';
ROWS                                   : 'ROWS';
RTREE                                  : 'RTREE';
SAVEPOINT                              : 'SAVEPOINT';
SCHEDULE                               : 'SCHEDULE';
SCHEMA                                 : 'SCHEMA';
SCHEMA_NAME                            : 'SCHEMA_NAME';
SCHEMAS                                : 'SCHEMAS';
SECOND                                 : 'SECOND';
SECOND_MICROSECOND                     : 'SECOND_MICROSECOND';
SECONDARY                              : 'SECONDARY';
SECONDARY_ENGINE                       : 'SECONDARY_ENGINE';
SECONDARY_ENGINE_ATTRIBUTE             : 'SECONDARY_ENGINE_ATTRIBUTE';
SECURITY                               : 'SECURITY';
SELECT                                 : 'SELECT';
SEPARATOR                              : 'SEPARATOR';
SERIAL                                 : 'SERIAL';
SERIALIZABLE                           : 'SERIALIZABLE';
SERVER                                 : 'SERVER';
SESSION                                : 'SESSION';
SET                                    : 'SET';
SHARE                                  : 'SHARE';
SHARED                                 : 'SHARED';
SHOW                                   : 'SHOW';
SHUTDOWN                               : 'SHUTDOWN';
SIGNAL                                 : 'SIGNAL';
SIGNED                                 : 'SIGNED';
SIGNED_INT                             : SIGNED ' ' INT;
SIGNED_INTEGER                         : SIGNED ' ' INTEGER;
SIMPLE                                 : 'SIMPLE';
SKIP_SYMBOL                            : 'SKIP';
SLAVE                                  : 'SLAVE';
SLOW                                   : 'SLOW';
SMALLINT                               : 'SMALLINT';
SNAPSHOT                               : 'SNAPSHOT';
SOCKET                                 : 'SOCKET';
SONAME                                 : 'SONAME';
SOUNDS                                 : 'SOUNDS';
SOURCE                                 : 'SOURCE';
SPATIAL                                : 'SPATIAL';
SQL                                    : 'SQL';
SQL_AFTER_GTIDS                        : 'SQL_AFTER_GTIDS';
SQL_AFTER_MTS_GAPS                     : 'SQL_AFTER_MTS_GAPS';
SQL_BEFORE_GTIDS                       : 'SQL_BEFORE_GTIDS';
SQL_BIG_RESULT                         : 'SQL_BIG_RESULT';
SQL_BUFFER_RESULT                      : 'SQL_BUFFER_RESULT';
SQL_CALC_FOUND_ROWS                    : 'SQL_CALC_FOUND_ROWS';
SQL_NO_CACHE                           : 'SQL_NO_CACHE';
SQL_SMALL_RESULT                       : 'SQL_SMALL_RESULT';
SQL_THREAD                             : 'SQL_THREAD';
SQLEXCEPTION                           : 'SQLEXCEPTION';
SQLSTATE                               : 'SQLSTATE';
SQLWARNING                             : 'SQLWARNING';
SSL                                    : 'SSL';
STACKED                                : 'STACKED';
START                                  : 'START';
STARTING                               : 'STARTING';
STARTS                                 : 'STARTS';
STATS_AUTO_RECALC                      : 'STATS_AUTO_RECALC';
STATS_PERSISTENT                       : 'STATS_PERSISTENT';
STATS_SAMPLE_PAGES                     : 'STATS_SAMPLE_PAGES';
STATUS                                 : 'STATUS';
STOP                                   : 'STOP';
STORAGE                                : 'STORAGE';
STORED                                 : 'STORED';
STRAIGHT_JOIN                          : 'STRAIGHT_JOIN';
STRING                                 : 'STRING';
SUBCLASS_ORIGIN                        : 'SUBCLASS_ORIGIN';
SUBJECT                                : 'SUBJECT';
SUBPARTITION                           : 'SUBPARTITION';
SUBPARTITIONS                          : 'SUBPARTITIONS';
SUBSTR                                 : 'SUBSTR';
SUBSTRING                              : 'SUBSTRING';
MID                                    : 'MID';
SUM                                    : 'SUM';
SUPER                                  : 'SUPER';
SUSPEND                                : 'SUSPEND';
SWAPS                                  : 'SWAPS';
SWITCHES                               : 'SWITCHES';
SYSTEM                                 : 'SYSTEM';
TABLE                                  : 'TABLE';
TABLE_NAME                             : 'TABLE_NAME';
TABLES                                 : 'TABLES';
TABLESPACE                             : 'TABLESPACE';
TEMPORARY                              : 'TEMPORARY';
TEMPTABLE                              : 'TEMPTABLE';
TERMINATED                             : 'TERMINATED';
TEXT                                   : 'TEXT';
THAN                                   : 'THAN';
THEN                                   : 'THEN';
THREAD_PRIORITY                        : 'THREAD_PRIORITY';
TIME                                   : 'TIME';
TIMESTAMP                              : 'TIMESTAMP';
TIMESTAMP_ADD                          : 'TIMESTAMP_ADD';
TIMESTAMP_DIFF                         : 'TIMESTAMP_DIFF';
TIMESTAMPDIFF                          : 'TIMESTAMPDIFF';
TINYBLOB                               : 'TINYBLOB';
TINYINT                                : 'TINYINT';
TINYTEXT                               : 'TINYTEXT';
TLS                                    : 'TLS';
TO                                     : 'TO';
TRADITIONAL                            : 'TRADITIONAL';
TRAILING                               : 'TRAILING';
TRANSACTION                            : 'TRANSACTION';
TREE                                   : 'TREE';
TRIGGER                                : 'TRIGGER';
TRIGGERS                               : 'TRIGGERS';
TRIM                                   : 'TRIM';
TRUE                                   : 'TRUE';
TRUNCATE                               : 'TRUNCATE';
TYPE                                   : 'TYPE';
UL_BINARY                              : '_BINARY';
UNBOUNDED                              : 'UNBOUNDED';
UNCOMMITTED                            : 'UNCOMMITTED';
UNDEFINED                              : 'UNDEFINED';
UNDO                                   : 'UNDO';
UNDO_BUFFER_SIZE                       : 'UNDO_BUFFER_SIZE';
UNDOFILE                               : 'UNDOFILE';
UNICODE                                : 'UNICODE';
UNINSTALL                              : 'UNINSTALL';
UNION                                  : 'UNION';
UNIQUE                                 : 'UNIQUE';
UNKNOWN                                : 'UNKNOWN';
UNLOCK                                 : 'UNLOCK';
UNSIGNED                               : 'UNSIGNED';
UNSIGNED_INT                           : UNSIGNED ' ' INT;
UNSIGNED_INTEGER                       : UNSIGNED ' ' INTEGER;
UNTIL                                  : 'UNTIL';
UPDATE                                 : 'UPDATE';
UPGRADE                                : 'UPGRADE';
USAGE                                  : 'USAGE';
USE                                    : 'USE';
USE_FRM                                : 'USE_FRM';
USER                                   : 'USER';
USER_RESOURCES                         : 'USER_RESOURCES';
USING                                  : 'USING';
UTC_TIME                               : 'UTC_TIME';
UTC_TIMESTAMP                          : 'UTC_TIMESTAMP';
VALIDATION                             : 'VALIDATION';
VALUE                                  : 'VALUE';
VALUES                                 : 'VALUES';
VARBINARY                              : 'VARBINARY';
VARCHAR                                : 'VARCHAR';
VARIABLES                              : 'VARIABLES';
VARYING                                : 'VARYING';
VCPU                                   : 'VCPU';
VIEW                                   : 'VIEW';
VIRTUAL                                : 'VIRTUAL';
VISIBLE                                : 'VISIBLE';
WAIT                                   : 'WAIT';
WARNINGS                               : 'WARNINGS';
WEEK                                   : 'WEEK';
WEIGHT_STRING                          : 'WEIGHT_STRING';
WHEN                                   : 'WHEN';
WHERE                                  : 'WHERE';
WHILE                                  : 'WHILE';
WINDOW                                 : 'WINDOW';
WITH                                   : 'WITH';
WITHOUT                                : 'WITHOUT';
WORK                                   : 'WORK';
WRAPPER                                : 'WRAPPER';
WRITE                                  : 'WRITE';
X509                                   : 'X509';
XA                                     : 'XA';
XID                                    : 'XID';
XML                                    : 'XML';
XOR                                    : 'XOR';
YEAR                                   : 'YEAR';
YEAR_MONTH                             : 'YEAR_MONTH';
ZEROFILL                               : 'ZEROFILL';
ZONE                                   : 'ZONE';
AUTHENTICATION_FIDO                    : 'AUTHENTICATION_FIDO';
FACTOR                                 : 'FACTOR';
NOW                                    : 'NOW';
EXCEPTION                              : 'EXCEPTION';
CURSOR_SYM                             : 'CURSOR_SYM';
WHEN_SYM                               : 'WHEN_SYM';
THEN_SYM                               : 'THEN_SYM';

// maridb
BODY                   : 'BODY';
CATALOG                : 'CATALOG';
CLUSTERING             : 'CLUSTERING';
CURRENT_ROLE           : 'CURRENT_ROLE';
CYCLE                  : 'CYCLE';
ENCRYPTED              : 'ENCRYPTED';
ENCRYPTION_KEY_ID      : 'ENCRYPTION_KEY_ID';
HARD                   : 'HARD';
ID                     : 'ID';
IETF_QUOTES            : 'IETF_QUOTES';
IGNORED                : 'IGNORED';
INCREMENT              : 'INCREMENT';
MAX_STATEMENT_TIME     : 'MAX_STATEMENT_TIME';
MINVALUE               : 'MINVALUE';
MYSQL                  : 'MYSQL';
NOCOPY                 : 'NOCOPY';
NOMAXVALUE             : 'NOMAXVALUE';
NOMINVALUE             : 'NOMINVALUE';
OLD_PASSWORD           : 'OLD_PASSWORD';
ONLINE                 : 'ONLINE';
OVERLAPS               : 'OVERLAPS';
PACKAGE                : 'PACKAGE';
PAGE_CHECKSUM          : 'PAGE_CHECKSUM';
PAGE_COMPRESSED        : 'PAGE_COMPRESSED';
PAGE_COMPRESSION_LEVEL : 'PAGE_COMPRESSION_LEVEL';
PERIOD                 : 'PERIOD';
PERSISTENT             : 'PERSISTENT';
PORTION                : 'PORTION';
PUBLIC                 : 'PUBLIC';
REF_SYSTEM_ID          : 'REF_SYSTEM_ID';
SEQUENCE               : 'SEQUENCE';
SLAVES                 : 'SLAVES';
SOFT                   : 'SOFT';
SYSTEM_TIME            : 'SYSTEM_TIME';
TRANSACTIONAL          : 'TRANSACTIONAL';
VECTOR                 : 'VECTOR';
VERSIONING             : 'VERSIONING';
VIA                    : 'VIA';
YES                    : 'YES';


AMPERSAND_              : '&';
AND_                    : '&&';
ASSIGNMENT_             : ':=';
ASTERISK_               : '*';
AT_                     : '@';
BACKSLASH_              : '\\';
BQ_                     : '`';
CARET_                  : '^';
COLON_                  : ':';
COMMA_                  : ',';
DEQ_                    : '==';
DOT_                    : '.';
DOT_ASTERISK_           : '.*';
DQ_                     : '"';
EQ_                     : '=';
GT_                     : '>';
GTE_                    : '>=';
JSON_SEPARATOR          : '->';
JSON_UNQUOTED_SEPARATOR : '->>';
LBE_                    : '{';
LBT_                    : '[';
LP_                     : '(';
LT_                     : '<';
LTE_                    : '<=';
MINUS_                  : '-';
MOD_                    : '%';
NEQ_                    : '<>' | '!=';
NOT_                    : '!';
OR_                     : '||';
PLUS_                   : '+';
POUND_                  : '#';
QUESTION_               : '?';
RBE_                    : '}';
RBT_                    : ']';
RP_                     : ')';
SAFE_EQ_                : '<=>';
SEMI_                   : ';';
SIGNED_LEFT_SHIFT_      : '<<';
SIGNED_RIGHT_SHIFT_     : '>>';
SLASH_                  : '/';
SQ_                     : '\'';
TILDE_                  : '~';
VERTICAL_BAR_           : '|';
G : 'G';

WS: [ \t\r\n] + -> skip;

BLOCK_COMMENT:  '/*' .*? '*/' -> channel(HIDDEN);
INLINE_COMMENT: (('-- ' | '#') ~[\r\n]* ('\r'? '\n' | EOF) | '--' ('\r'? '\n' | EOF)) -> channel(HIDDEN);

FILESIZE_LITERAL: INT_NUM_ ('K'|'M'|'G'|'T');

SINGLE_QUOTED_TEXT: SQ_ ('\\'. | '\'\'' | ~('\'' | '\\'))* SQ_;

DOUBLE_QUOTED_TEXT: DQ_ ( '\\'. | '""' | ~('"'| '\\') )* DQ_;

BQUOTA_STRING: BQ_ ( '\\'. | '``' | ~('`'|'\\'))* BQ_;

NCHAR_TEXT: 'N' SINGLE_QUOTED_TEXT;

UNDERSCORE_CHARSET: '_' [a-z0-9]+;

NUMBER_: INT_NUM_| FLOAT_NUM_| DECIMAL_NUM_;

INT_NUM_: DIGIT+;

FLOAT_NUM_: INT_NUM_? DOT_? INT_NUM_ 'E' (PLUS_ | MINUS_)? INT_NUM_;

DECIMAL_NUM_: INT_NUM_? DOT_ INT_NUM_;

HEX_DIGIT_: '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_ | 'X' SQ_ + SQ_;

BIT_NUM_: '0b' ('0' | '1')+ | 'B' SQ_ ('0' | '1')+ SQ_;

I_CURSOR : '[CURSOR]' ;
IDENTIFIER_
    : [a-z_$0-9\u0080-\uFFFF]*?[a-z_$\u0080-\uFFFF]+?[a-z_$0-9\u0080-\uFFFF]*
    ;

IP_ADDRESS: INT_NUM_ DOT_ INT_NUM_ DOT_ INT_NUM_ DOT_ INT_NUM_;

NOT_SUPPORT_
    : 'not support'
    ;


fragment DIGIT: [0-9];

fragment HEX_: [0-9a-f];
