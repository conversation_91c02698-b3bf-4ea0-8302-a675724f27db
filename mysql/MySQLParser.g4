
parser grammar MySQLParser;

options {
    tokenVocab = MySQLLexer;
}
multiRoot
    : root+
    ;
root
    : (select
    | insert
    | update
    | delete
    | replace
    | binlog
    | createTable
    | alterStatement
    | repairTable
    | dropTable
    | truncateTable
    | createIndex
    | dropIndex
    | createProcedure
    | dropProcedure
    | createFunction
    | dropFunction
    | createDatabase
    | dropDatabase
    | createEvent
    | dropEvent
    | createLogfileGroup
    | dropLogfileGroup
    | createServer
    | dropServer
    | createView
    | dropView
    | createTrigger
    | dropTrigger
    | alterResourceGroup
    | createResourceGroup
    | dropResourceGroup
    | prepare
    | executeStmt
    | deallocate
    | setTransaction
    | beginTransaction
    | setAutoCommit
    | commit
    | rollback
    | savepoint
    | grant
    | revoke
    | createUser
    | dropUser
    | alterUser
    | renameUser
    | createRole
    | dropRole
    | setDefaultRole
    | setRole
    | createSRSStatement
    | dropSRSStatement
    | flush
    | getDiagnosticsStatement
    | groupReplication
    | handlerStatement
    | help
    | importStatement
    | install
    | kill
    | loadStatement
    | lock
    | cacheIndex
    | loadIndexInfo
    | optimizeTable
    | purgeBinaryLog
    | releaseSavepoint
    | resetStatement
    | setPassword
    | setTransaction
    | setResourceGroup
    | resignalStatement
    | signalStatement
    | restart
    | shutdown
    | begin
    | use
    | explain
    | doStatement
    | show
    | setVariable
    | setCharacter
    | call
    | changeMasterTo
    | changeReplicationFilter
    | checkTable
    | checksumTable
    | clone
    | changeReplicationSourceTo
    | startSlave
    | stopSlave
    | resetSlave
    | startReplica
    | stopReplica
    | resetReplica
    | analyzeTable
    | renameTable
    | uninstall
    | unlock
    | xaBegin
    | xaPrepare
    | xaCommit
    | xaRollback
    | xaEnd
    | xaRecovery
    | createLoadableFunction
    | createTablespace
    | alterTablespace
    | dropTablespace
    | delimiter
    | checkView //mariadb
    | repairView //mariadb
    | createCatalog
    | createPackage
    | createPackageBody
    ) (BACKSLASH_ G)? SEMI_?  EOF?
    ;
createPackageBody
    : CREATE orReplace? ownerStatement? PACKAGE BODY packageName (AS | IS)? packageImplementationDeclareSection (packageImplementationExecutableSection|initializeSection) END partitionName?
    ;
initializeSection
    : BEGIN validStatement+ (EXCEPTION exceptionHandler+)?
    ;
packageImplementationDeclareSection
    : packageImplementationItemDeclaration+ packageImplementationRoutineDefinition*
    | packageImplementationRoutineDefinition+
    ;
packageImplementationItemDeclaration
    : variableDeclaration SEMI_?
    ;
variableDeclaration
    : DECLARE? internalVariableName (COMMA_ internalVariableName)* dataType (ASSIGNMENT_ expr)?  columnAttribute?
    ;
packageImplementationRoutineDefinition
    : FUNCTION packageSpecificationFunction packageImplementationFunctionBody? SEMI_?
    | PROCEDURE packageSpecificationProcedure packageImplementationProcedureBody? SEMI_?
    ;
packageImplementationProcedureBody
    : (AS | IS )? packageRoutineBody procedureName?
    ;
packageImplementationFunctionBody
    : (AS | IS )? packageRoutineBody functionName?
    ;
packageRoutineBody
    : packageRoutineDeclarations? packageBeginStatement
    ;
packageRoutineDeclarations
    : packageRoutineDeclaration SEMI_ (packageRoutineDeclaration SEMI_?)*
    ;
packageRoutineDeclaration
    : variableDeclaration
    | conditionName CONDITION FOR conditionValue
    | userExceptionName EXCEPTION
    | CURSOR_SYM cursorName (LP_ cursorFormalParameter (COMMA_ cursorFormalParameter)* LP_)? IS select
    ;
cursorFormalParameter  //todo 未找到定义
    : identifier  dataType
    ;
packageImplementationExecutableSection
    : END
    | packageBeginStatement
    ;
packageBeginStatement
    : BEGIN validStatement* (EXCEPTION exceptionHandlers)? END
    ;
exceptionHandlers
    : exceptionHandler+
    ;
exceptionHandler
    : (WITH| WHEN_SYM) conditionValue (COMMA_? conditionValue)* (THEN | THEN_SYM) validStatement*
    ;

createPackage
    : CREATE orReplace? ownerStatement? PACKAGE ifNotExists? packageName packageCharacteristicList? (AS | IS)? packageSpecificationElementList? END partitionName?
    ;
packageSpecificationElementList
    : packageSpecificationElement (COMMA_? packageSpecificationElement)*
    ;
packageSpecificationElement
    : FUNCTION packageSpecificationFunction
    | PROCEDURE packageSpecificationProcedure
    ;
packageSpecificationFunction
    : functionName (LP_ (funcParameter (COMMA_ funcParameter)*)? RP_)? ( RETURN | RETURNS) dataType packageRoutineCharacteristic* SEMI_?
    ;
packageRoutineCharacteristic
    : routineOption routineBody?
    ;
packageSpecificationProcedure
    : procedureName (LP_ (procedureParameter (COMMA_ procedureParameter)*)? RP_)? packageRoutineCharacteristic* SEMI_?
    ;

packageCharacteristicList
    : packageCharacteristic (COMMA_? packageCharacteristic)*
    ;
packageCharacteristic
    : commentOption
    | SQL SECURITY (DEFINER | INVOKER)
    ;
createCatalog
    : CREATE CATALOG ifNotExists? catalogName createDatabaseSpecification_*
    ;
repairView
    : REPAIR (NO_WRITE_TO_BINLOG | LOCAL)? VIEW viewNames (FROM MYSQL)?
    ;
checkView
    : CHECK VIEW viewName
    ;

use
    : USE databaseName
    ;

help
    : HELP textOrIdentifier
    ;

explain
    : (DESC | DESCRIBE | EXPLAIN | ANALYZE)
    ( tableName (columnName | textString)?
    | explainType? (explainableStatement | FOR CONNECTION connectionId)
    | ANALYZE (FORMAT EQ_ TREE)? (select | delete | update | insert))
//    | ANALYZE (FORMAT EQ_ TREE)? (select | delete | update | insert))
    ;

fromDatabase
    : (FROM | IN) databaseName
    ;

fromTable
    : (FROM | IN) tableName
    ;

showLike
    : LIKE stringLiterals
    ;

showWhereClause
    : WHERE expr
    ;

showFilter
    : showLike | showWhereClause
    ;

showProfileType
    : ALL | BLOCK IO | CONTEXT SWITCHES | CPU | IPC | MEMORY | PAGE FAULTS | SOURCE | SWAPS
    ;

setVariable
    : SET optionValueList
    ;

optionValueList
    : optionValueNoOptionType (COMMA_ optionValue)*
    | optionType (internalVariableName EQ_ setExprOrDefault) (COMMA_ optionValue)*
    ;

optionValueNoOptionType
    : internalVariableName equal setExprOrDefault
    | userVariable equal expr
    | setSystemVariable equal setExprOrDefault
    | NAMES (equal expr | charsetName collateClause? | DEFAULT)
    ;

equal
    : EQ_ | ASSIGNMENT_
    ;

optionValue
    : optionType internalVariableName EQ_ setExprOrDefault | optionValueNoOptionType
    ;

showBinaryLogs
    : SHOW (BINARY | MASTER) LOGS
    ;

showBinlogEvents
    : SHOW BINLOG EVENTS (IN logName)? (FROM NUMBER_)? limitClause?
    ;

showCharacterSet
    : SHOW CHARACTER SET showFilter?
    ;

showCollation
    : SHOW COLLATION showFilter?
    ;

showColumns
    : SHOW EXTENDED? FULL? (COLUMNS | FIELDS) fromTable fromDatabase? showFilter?
    ;

showCreateDatabase
    : SHOW CREATE (DATABASE | SCHEMA) ifNotExists? databaseName
    ;

showCreateEvent
    : SHOW CREATE EVENT eventName
    ;

showCreateFunction
    : SHOW CREATE FUNCTION functionName
    ;

showCreateProcedure
    : SHOW CREATE PROCEDURE procedureName
    ;

showCreateTable
    : SHOW CREATE TABLE tableName
    ;

showCreateTrigger
    : SHOW CREATE TRIGGER triggerName
    ;

showCreateUser
    : SHOW CREATE USER username?
    ;

showCreateView
    : SHOW CREATE VIEW viewName
    ;

showDatabases
    : SHOW (DATABASES | SCHEMAS) showFilter?
    ;

showEngine
    : SHOW ENGINE engineRef (STATUS | MUTEX)
    ;

showEngines
    : SHOW STORAGE? ENGINES
    ;

showErrors
    : SHOW (COUNT LP_ ASTERISK_ RP_)? ERRORS limitClause?
    ;

showEvents
    : SHOW EVENTS fromDatabase? showFilter?
    ;

showFunctionCode
    : SHOW FUNCTION CODE functionName
    ;

showFunctionStatus
    : SHOW FUNCTION STATUS showFilter?
    ;

showGrants
    : SHOW GRANTS (FOR (username | roleName) (USING roleName (COMMA_ roleName)*)?)?
    ;

showIndex
    : SHOW EXTENDED? (INDEX | INDEXES | KEYS) fromTable fromDatabase? showWhereClause?
    ;

showMasterStatus
    : SHOW MASTER STATUS
    ;

showOpenTables
    : SHOW OPEN TABLES fromDatabase? showFilter?
    ;

showPlugins
    : SHOW PLUGINS
    ;

showPrivileges
    : SHOW PRIVILEGES
    ;

showProcedureCode
    : SHOW PROCEDURE CODE procedureName
    ;

showProcedureStatus
    : SHOW PROCEDURE STATUS showFilter?
    ;

showProcesslist
    : SHOW FULL? PROCESSLIST
    ;

showProfile
    : SHOW PROFILE (showProfileType (COMMA_ showProfileType)*)? (FOR QUERY NUMBER_)? limitClause?
    ;

showProfiles
    : SHOW PROFILES
    ;

showRelaylogEvent
    : SHOW RELAYLOG EVENTS (IN logName)? (FROM NUMBER_)? limitClause? (FOR CHANNEL channelName)?
    ;

showReplicas
    : SHOW REPLICAS
    ;

showSlaveHosts
    : SHOW SLAVE HOSTS
    ;

showReplicaStatus
    : SHOW REPLICA STATUS (FOR CHANNEL channelName)?
    ;

showSlaveStatus
    : SHOW SLAVE STATUS (FOR CHANNEL channelName)?
    ;

showStatus
    : SHOW (GLOBAL | SESSION)? STATUS showFilter?
    ;

showTableStatus
    : SHOW TABLE STATUS fromDatabase? showFilter?
    ;

showTables
    : SHOW EXTENDED? FULL? TABLES fromDatabase? showFilter?
    ;

showTriggers
    : SHOW TRIGGERS fromDatabase? showFilter?
    ;

showVariables
    : SHOW (GLOBAL | SESSION)? VARIABLES showFilter?
    ;

showWarnings
    : SHOW (COUNT LP_ ASTERISK_ RP_)? WARNINGS limitClause?
    ;

showCharset
    : SHOW CHARSET
    ;

setCharacter
    : SET (CHARACTER SET | CHARSET) (charsetName | DEFAULT)
    ;

clone
    : CLONE cloneAction
    ;

cloneAction
    : LOCAL DATA DIRECTORY EQ_? cloneDir
    | INSTANCE FROM cloneInstance IDENTIFIED BY string_ (DATA DIRECTORY EQ_? cloneDir)? (REQUIRE NO? SSL)?
    ;

createLoadableFunction
    : CREATE orReplace? AGGREGATE? FUNCTION ifNotExists? functionName RETURNS (STRING | INTEGER | INT | REAL | DECIMAL | DEC) SONAME shardLibraryName
    ;

install
    : installComponent | installPlugin
    ;

uninstall
    :uninstallComponent | uninstallPlugin
    ;

installComponent
    : INSTALL COMPONENT componentName (COMMA_ componentName)*
    ;

installPlugin
    : INSTALL PLUGIN pluginName SONAME shardLibraryName
    ;

uninstallComponent
    : UNINSTALL COMPONENT componentName (COMMA_ componentName)*
    ;

uninstallPlugin
    : UNINSTALL PLUGIN pluginName
    ;

analyzeTable
    : ANALYZE (NO_WRITE_TO_BINLOG | LOCAL)? tableOrTables tableList histogram? persistentOption?
    ;
persistentOption    //mariadb
    : PERSISTENT FOR
         ( ALL
           | COLUMNS LP_ columnNames? RP_ INDEXES LP_ indexNameList? RP_
         )

    ;
histogram
    : UPDATE HISTOGRAM ON columnNames (WITH NUMBER_ BUCKETS | USING DATA string_)?
    | DROP HISTOGRAM ON columnNames
    ;

checkTable
    : CHECK tableOrTables tableList checkTableOption?
    ;

checkTableOption
    : FOR UPGRADE | QUICK | FAST | MEDIUM | EXTENDED | CHANGED
    ;

checksumTable
    : CHECKSUM tableOrTables tableList (QUICK | EXTENDED)?
    ;

optimizeTable
    : OPTIMIZE (NO_WRITE_TO_BINLOG | LOCAL)? tableOrTables tableList waitOption?
    ;

repairTable
    : REPAIR (NO_WRITE_TO_BINLOG | LOCAL)? tableOrTables tableList QUICK? EXTENDED? USE_FRM? FORCE?
    ;

alterResourceGroup
    : ALTER RESOURCE GROUP groupName (VCPU EQ_? vcpuSpec (COMMA_ vcpuSpec)*)? (THREAD_PRIORITY EQ_? NUMBER_)?
    (ENABLE | DISABLE FORCE?)?
    ;

vcpuSpec
    : NUMBER_ | NUMBER_ MINUS_ NUMBER_
    ;

createResourceGroup
    : CREATE RESOURCE GROUP groupName TYPE EQ_ (SYSTEM | USER) (VCPU EQ_? vcpuSpec (COMMA_ vcpuSpec)*)?
    (THREAD_PRIORITY EQ_? numberLiterals)? (ENABLE | DISABLE)?
    ;

dropResourceGroup
    : DROP RESOURCE GROUP groupName FORCE?
    ;

setResourceGroup
    : SET RESOURCE GROUP groupName (FOR NUMBER_ (COMMA_ NUMBER_)*)?
    ;

binlog
    : BINLOG stringLiterals
    ;

cacheIndex
    : CACHE INDEX (cacheTableIndexList (COMMA_ cacheTableIndexList)* | tableName PARTITION LP_ partitionList RP_) IN (identifier | DEFAULT)
    ;

cacheTableIndexList
    : tableName ((INDEX | KEY) LP_ indexName (COMMA_ indexName)* RP_)?
    ;

partitionList
    : partitionName (COMMA_ partitionName)* | ALL
    ;

flush
    : FLUSH (NO_WRITE_TO_BINLOG | LOCAL)? (flushOption (COMMA_ flushOption)* | tablesOption)
    ;

flushOption
    : BINARY LOGS | ENGINE LOGS | ERROR LOGS | GENERAL LOGS | HOSTS | LOGS | PRIVILEGES | OPTIMIZER_COSTS
    | RELAY LOGS (FOR CHANNEL channelName)? | SLOW LOGS | STATUS | USER_RESOURCES
    ;

tablesOption
    : (TABLES | TABLE)
    | (TABLES | TABLE) tableName (COMMA_ tableName)*
    | (TABLES | TABLE) WITH READ LOCK
    | (TABLES | TABLE) tableName (COMMA_ tableName)* WITH READ LOCK
    | (TABLES | TABLE) tableName (COMMA_ tableName)* FOR EXPORT
    ;

kill
    : KILL (HARD | SOFT)? (CONNECTION | (QUERY ID?) | USER)? AT_? (IDENTIFIER_|username)
    ;

loadIndexInfo
    : LOAD INDEX INTO CACHE loadTableIndexList (COMMA_ loadTableIndexList)*
    ;

loadTableIndexList
    : tableName (PARTITION LP_ partitionList RP_)? ((INDEX | KEY) LP_ indexName (COMMA_ indexName)* RP_)? (IGNORE LEAVES)?
    ;

resetStatement
    : RESET resetOption (COMMA_ resetOption)*
    | resetPersist
    ;

resetOption
    : MASTER (TO binaryLogFileIndexNumber)?
    | SLAVE ALL? channelOption?
    | REPLICA
    | QUERY CACHE
    ;

resetPersist
    : RESET PERSIST (ifExists? identifier)?
    ;

restart
    : RESTART
    ;

shutdown
    : SHUTDOWN (WAIT FOR ALL (SLAVES | REPLICAS))?
    ;

explainType
    : (FORMAT EQ_ formatName | EXTENDED | PARTITIONS)
    ;

explainableStatement
    : select | delete | insert | replace | update
    ;

formatName
    : TRADITIONAL | JSON | TREE
    ;

delimiter
    : DELIMITER delimiterName
    ;

show
    : showDatabases
    | showTables
    | showTableStatus
    | showBinaryLogs
    | showColumns
    | showIndex
    | showCreateDatabase
    | showCreateTable
    | showBinlogEvents
    | showCharacterSet
    | showCollation
    | showCreateEvent
    | showCreateFunction
    | showCreateProcedure
    | showCreateTrigger
    | showCreateUser
    | showCreateView
    | showEngine
    | showEngines
    | showCharset
    | showErrors
    | showEvents
    | showFunctionCode
    | showFunctionStatus
    | showGrants
    | showMasterStatus
    | showPlugins
    | showOpenTables
    | showPrivileges
    | showProcedureCode
    | showProcesslist
    | showProfile
    | showProcedureStatus
    | showProfiles
    | showSlaveHosts
    | showSlaveStatus
    | showRelaylogEvent
    | showStatus
    | showTriggers
    | showWarnings
    | showVariables
    | showReplicas
    | showReplicaStatus
    | showExplain
    ;

showExplain
    : SHOW EXPLAIN (FORMAT EQ_ JSON)? FOR NUMBER_
    ;
grant
    : GRANT roleOrPrivileges TO (userList| userSpecification) withGrantOption # grantRoleOrPrivilegeTo
    | GRANT roleOrPrivileges ON aclType? grantIdentifier TO (userList| userSpecification)  withGrantOption grantAs? # grantRoleOrPrivilegeOnTo
    | GRANT ALL PRIVILEGES? ON aclType? grantIdentifier TO (userList| userSpecification) withGrantOption grantAs? # grantRoleOrPrivilegeOnTo
    | GRANT PROXY ON username TO (userList| userSpecification) withGrantOption # grantProxy
    ;
userSpecification
    : username userAuthOption?
    | PUBLIC
    ;
revoke
    : REVOKE roleOrPrivileges FROM userList # revokeFrom
    | REVOKE roleOrPrivileges ON aclType? grantIdentifier FROM userList # revokeOnFrom
    | REVOKE ALL PRIVILEGES? ON aclType? grantIdentifier FROM userList # revokeOnFrom
    | REVOKE ALL PRIVILEGES? COMMA_ GRANT OPTION FROM userList # revokeFrom
    | REVOKE PROXY ON username FROM userList # revokeOnFrom
    ;

userList
    : username (COMMA_ username)*
    ;

roleOrPrivileges
    : roleOrPrivilege (COMMA_ roleOrPrivilege)*
    ;

roleOrPrivilege
    : roleIdentifierOrText (LP_ columnNames RP_)? # roleOrDynamicPrivilege
    | roleIdentifierOrText AT_ textOrIdentifier  # roleAtHost
    | SELECT (LP_ columnNames RP_)?  # staticPrivilegeSelect
    | INSERT (LP_ columnNames RP_)?  # staticPrivilegeInsert
    | UPDATE (LP_ columnNames RP_)?  # staticPrivilegeUpdate
    | REFERENCES (LP_ columnNames RP_)?  # staticPrivilegeReferences
    | DELETE  # staticPrivilegeDelete
    | USAGE  # staticPrivilegeUsage
    | INDEX  # staticPrivilegeIndex
    | ALTER  # staticPrivilegeAlter
    | CREATE  # staticPrivilegeCreate
    | DROP  # staticPrivilegeDrop
    | EXECUTE  # staticPrivilegeExecute
    | RELOAD  # staticPrivilegeReload
    | SHUTDOWN  # staticPrivilegeShutdown
    | PROCESS  # staticPrivilegeProcess
    | FILE  # staticPrivilegeFile
    | GRANT OPTION  # staticPrivilegeGrant
    | SHOW DATABASES  # staticPrivilegeShowDatabases
    | SUPER  # staticPrivilegeSuper
    | CREATE TEMPORARY TABLES  # staticPrivilegeCreateTemporaryTables
    | LOCK TABLES  # staticPrivilegeLockTables
    | REPLICATION SLAVE  # staticPrivilegeReplicationSlave
    | REPLICATION CLIENT  # staticPrivilegeReplicationClient
    | CREATE VIEW  # staticPrivilegeCreateView
    | SHOW VIEW  # staticPrivilegeShowView
    | CREATE ROUTINE  # staticPrivilegeCreateRoutine
    | ALTER ROUTINE  # staticPrivilegeAlterRoutine
    | CREATE USER  # staticPrivilegeCreateUser
    | EVENT  # staticPrivilegeEvent
    | TRIGGER  # staticPrivilegeTrigger
    | CREATE TABLESPACE  # staticPrivilegeCreateTablespace
    | CREATE ROLE  # staticPrivilegeCreateRole
    | DROP ROLE  # staticPrivilegeDropRole
    ;

aclType
    : TABLE | FUNCTION | PROCEDURE
    | PACKAGE | PACKAGE BODY // mariaDb
    ;

grantIdentifier
    : ASTERISK_ # grantLevelGlobal
    | ASTERISK_ DOT_ASTERISK_ # grantLevelGlobal
    | databaseName DOT_ASTERISK_ # grantLevelDatabaseGlobal
    | tableName # grantLevelTable
    ;
// https://mariadb.com/kb/en/create-user/
createUser
    : CREATE (OR REPLACE)? USER ifNotExists? createUserList defaultRoleClause? requireClause? connectOptions? accountLockPasswordExpireOptions? createUserOption? (AND userAuthOption)*
    ;

createUserOption
    : COMMENT string_ | ATTRIBUTE jsonAttribute = string_
    ;

createUserEntry
    : username # createUserEntryNoOption
    | username IDENTIFIED BY PASSWORD? string_ # createUserEntryIdentifiedBy
    | username IDENTIFIED BY RANDOM PASSWORD # createUserEntryIdentifiedBy
    | username IDENTIFIED WITH textOrIdentifier # createUserEntryIdentifiedWith
    | username IDENTIFIED WITH textOrIdentifier AS string_ # createUserEntryIdentifiedWith
    | username IDENTIFIED WITH textOrIdentifier BY string_ # createUserEntryIdentifiedWith
    | username IDENTIFIED WITH textOrIdentifier BY RANDOM PASSWORD # createUserEntryIdentifiedWith
    | username IDENTIFIED (WITH | VIA)                             // VIA is MariaDB-specific only
         authenticationRule (OR authenticationRule)* # moduleAuthOption // OR is MariaDB-specific only

    ;
authenticationRule
    : authPlugin ((BY | USING | AS) stringLiterals)? # module
    | authPlugin (USING | AS) passwordFunctionClause # passwordModuleOption // MariaDB
    ;
passwordFunctionClause
    : (PASSWORD | OLD_PASSWORD) '(' simpleExpr ')'
    ;
authPlugin
    : uid
    | stringLiterals
    ;
uid
    : simpleId
    | BQUOTA_STRING
    | stringLiterals
    ;

simpleId
    : IDENTIFIER_
    ;
createUserList
    : createUserEntry (COMMA_ createUserEntry)*
    ;

defaultRoleClause
    : DEFAULT ROLE roleName (COMMA_ roleName)*
    ;

requireClause
    : REQUIRE (NONE | tlsOption (AND? tlsOption)*)
    ;

connectOptions
    : WITH connectOption connectOption*
    ;

accountLockPasswordExpireOptions
    : accountLockPasswordExpireOption+
    ;

accountLockPasswordExpireOption
    : ACCOUNT (LOCK | UNLOCK)
    | PASSWORD EXPIRE (DEFAULT | NEVER | INTERVAL NUMBER_ DAY)?
    | PASSWORD HISTORY (DEFAULT | NUMBER_)
    | PASSWORD REUSE INTERVAL (DEFAULT | NUMBER_ DAY)
    | PASSWORD REQUIRE CURRENT (DEFAULT | OPTIONAL)?
    | FAILED_LOGIN_ATTEMPTS NUMBER_
    | PASSWORD_LOCK_TIME (NUMBER_ | UNBOUNDED)
    ;

alterUser
    : ALTER USER ifExists? alterUserList requireClause? connectOptions? accountLockPasswordExpireOptions? alterOperation?
    | ALTER USER ifExists? USER LP_ RP_ userFuncAuthOption
    | ALTER USER ifExists? username DEFAULT ROLE (NONE | ALL | roleName (COMMA_ roleName)*)
    ;

alterUserEntry
    : username userAuthOption?
    ;

alterUserList
    : alterUserEntry (COMMA_ alterUserEntry)*
    ;

alterOperation
    : (ADD | MODIFY | DROP | SET) factoryOperation
    ;

factoryOperation
    : NUMBER_ FACTOR (IDENTIFIED WITH authentication_fido)?
    ;

authentication_fido
    : AUTHENTICATION_FIDO
    ;


dropUser
    : DROP USER ifExists? username (COMMA_ username)*
    ;

createRole
    : CREATE (OR REPLACE)? ROLE ifNotExists? roleName (COMMA_ roleName)* (WITH ADMIN (CURRENT_USER | CURRENT_ROLE | username |roleName))?
    ;

dropRole
    : DROP ROLE ifExists? roleName (COMMA_ roleName)*
    ;

renameUser
    : RENAME USER oldUsere=username TO newUser=username (COMMA_ oldUser=username TO newUser=username)*
    ;

setDefaultRole
    : SET DEFAULT ROLE (NONE | ALL | roleName (COMMA_ roleName)*) ((TO|FOR) username (COMMA_ username)*)?
    ;

setRole
    : SET ROLE (DEFAULT | NONE | ALL | ALL EXCEPT roles | roles)
    ;

setPassword
    : SET PASSWORD (FOR username)? authOption (REPLACE string_)? (RETAIN CURRENT PASSWORD)?
    ;

authOption
    : EQ_ stringLiterals | TO RANDOM | EQ_ PASSWORD LP_ stringLiterals RP_
    ;

withGrantOption
    : (REQUIRE (NONE | tlsOption (AND? tlsOption)*))? (WITH withOption*)?
    ;
withOption
    : GRANT OPTION
    | connectOption
    | ADMIN OPTION
    ;
roles
    : roleName (COMMA_ roleName)*
    ;

grantAs
    : AS username withRoles?
    ;

withRoles
    : WITH ROLE (DEFAULT | NONE | ALL | ALL EXCEPT roles | roles)
    ;

userAuthOption
    : identifiedBy
    | identifiedWith
    | DISCARD OLD PASSWORD
    ;

identifiedBy
    : IDENTIFIED BY PASSWORD? (string_ | RANDOM PASSWORD) (REPLACE string_)? (RETAIN CURRENT PASSWORD)?
    ;

identifiedWith
    : IDENTIFIED WITH (pluginName | authentication_fido)
    | IDENTIFIED WITH pluginName BY (string_ | RANDOM PASSWORD) (REPLACE stringLiterals)? (RETAIN CURRENT PASSWORD)?
    | IDENTIFIED WITH pluginName AS textStringHash (RETAIN CURRENT PASSWORD)?
    | IDENTIFIED (WITH | VIA)                             // VIA is MariaDB-specific only
          authenticationRule (OR authenticationRule)*  // OR is MariaDB-specific only
    ;

connectOption
    : MAX_QUERIES_PER_HOUR NUMBER_
    | MAX_UPDATES_PER_HOUR NUMBER_
    | MAX_CONNECTIONS_PER_HOUR NUMBER_
    | MAX_USER_CONNECTIONS NUMBER_
    | MAX_STATEMENT_TIME NUMBER_    // MariaDB
    ;

tlsOption
    : SSL | X509 | CIPHER string_ | ISSUER string_ | SUBJECT string_
    ;

userFuncAuthOption
    : identifiedBy | DISCARD OLD PASSWORD
    ;

insert
    : INSERT insertSpecification INTO? tableName partitionNames? (insertValuesClause | setAssignmentsClause | insertSelectClause) onDuplicateKeyClause? returningClause?
    ;

insertSpecification
    : (LOW_PRIORITY | DELAYED | HIGH_PRIORITY)? IGNORE?
    ;

insertValuesClause
    : (LP_ fields? RP_ )? (VALUES | VALUE) (assignmentValues (COMMA_ assignmentValues)* | rowConstructorList) valueReference?
    ;

fields
    : insertIdentifier (COMMA_ insertIdentifier)*
    ;

insertIdentifier
    : columnName | tableName DOT_ASTERISK_
    ;

insertSelectClause
    : valueReference? (LP_ fields? RP_)? select
    ;

onDuplicateKeyClause
    : (AS identifier derivedColumns?)?  ON DUPLICATE KEY UPDATE assignment (COMMA_ assignment)*
    ;

valueReference
    : AS alias derivedColumns?
    ;

derivedColumns
    : LP_ alias (COMMA_ alias)* RP_
    ;

replace
    : REPLACE replaceSpecification? INTO? tableName partitionNames? (replaceValuesClause | setAssignmentsClause | replaceSelectClause) returningClause?
    ;

replaceSpecification
    : LOW_PRIORITY | DELAYED
    ;

replaceValuesClause
    : (LP_ fields? RP_)? (VALUES | VALUE) (assignmentValues (COMMA_ assignmentValues)* | rowConstructorList) valueReference?
    ;

replaceSelectClause
    : valueReference? (LP_ fields? RP_)? select
    ;

update
    : withClause? UPDATE updateSpecification_ tableReferences partitionNames? portionOption? setAssignmentsClause whereClause? orderByClause? limitClause?
    ;

updateSpecification_
    : LOW_PRIORITY? IGNORE?
    ;

assignment
    : columnName EQ_ assignmentValue
    ;

setAssignmentsClause
    : valueReference? SET assignment (COMMA_ assignment)*
    ;

assignmentValues
    : LP_ assignmentValue (COMMA_ assignmentValue)* RP_
    | LP_ RP_
    ;

assignmentValue
    : blobValue | expr | DEFAULT
    ;

blobValue
    : UL_BINARY string_
    ;

delete
    : DELETE deleteSpecification (singleTableClause | multipleTablesClause) whereClause? orderByClause? limitClause? returningClause?
    | DELETE HISTORY singleTableClause (BEFORE SYSTEM_TIME (TIMESTAMP|TRANSACTION)? expr)
    ;

deleteSpecification
    : LOW_PRIORITY? QUICK? IGNORE?
    ;

singleTableClause
    : FROM tableName partitionNames? portionOption? (AS? alias)?
    ;
portionOption    //mariaDB
    : FOR PORTION OF identifier FROM expr1 = expr TO expr2 = expr
    ;
multipleTablesClause
    : tableAliasRefList FROM tableReferences | FROM tableAliasRefList USING tableReferences
    ;

select
    : queryExpression
    ;

queryExpression
    : queryExpression combineClause queryExpression
    | withClause? (queryPrimary | parenthesisQueryExpression) orderByClause? limitClause?
    (selectIntoExpression lockClauseList? | lockClauseList selectIntoExpression?)?
    ;

combineClause
    : (INTERSECT | UNION | EXCEPT) (ALL | DISTINCT)?
    ;

parenthesisQueryExpression
    : LP_ queryExpression RP_
    ;

queryPrimary
    : querySpecification
    | tableValueConstructor
    | tableStatement
    ;

querySpecification
    : SELECT selectSpecification* projections selectIntoExpression? fromClause? whereClause? groupByClause? havingClause? windowClause?
    ;

call
    : CALL (owner DOT_)? identifier (LP_ (expr (COMMA_ expr)*)? RP_)?
    ;

doStatement
    : DO expr (AS? alias)? (COMMA_ expr (AS? alias)?)*
    ;

handlerStatement
    : handlerOpenStatement | handlerReadIndexStatement | handlerReadStatement | handlerCloseStatement
    ;

handlerOpenStatement
    : HANDLER tableName OPEN (AS? identifier)?
    ;

handlerReadIndexStatement
    : HANDLER tableName READ indexName ( comparisonOperator LP_ identifier RP_ | (FIRST | NEXT | PREV | LAST) )
    whereClause? limitClause?
    ;

handlerReadStatement
    : HANDLER tableName READ (FIRST | NEXT)
    whereClause? limitClause?
    ;

handlerCloseStatement
    : HANDLER tableName CLOSE
    ;

importStatement
    : IMPORT TABLE FROM textString (COMMA_ textString)?
    ;

loadStatement
    : loadDataStatement | loadXmlStatement
    ;

loadDataStatement
    : LOAD DATA
      (LOW_PRIORITY | CONCURRENT)? LOCAL?
      INFILE string_
      (REPLACE | IGNORE)?
      INTO TABLE tableName partitionNames?
      (CHARACTER SET identifier)?
      (COLUMNS selectFieldsInto+ )?
      ( LINES selectLinesInto+ )?
      ( IGNORE numberLiterals (LINES | ROWS) )?
      fieldOrVarSpec?
      (setAssignmentsClause)?
    ;

loadXmlStatement
    : LOAD XML
      (LOW_PRIORITY | CONCURRENT)? LOCAL?
      INFILE string_
      (REPLACE | IGNORE)?
      INTO TABLE tableName
      (CHARACTER SET identifier)?
      (ROWS IDENTIFIED BY LT_ string_ GT_)?
      ( IGNORE numberLiterals (LINES | ROWS) )?
      fieldOrVarSpec?
      (setAssignmentsClause)?
    ;

tableStatement
    : TABLE tableName
    ;

tableValueConstructor
    : VALUES rowConstructorList
    ;

rowConstructorList
    : ROW assignmentValues (COMMA_ ROW assignmentValues)*
    ;

withClause
    : WITH RECURSIVE? cteClause (COMMA_ cteClause)*
    ;

cteClause
    : alias (LP_ columnNames RP_)? AS subquery
    ;

selectSpecification
    : duplicateSpecification | HIGH_PRIORITY | STRAIGHT_JOIN | SQL_SMALL_RESULT | SQL_BIG_RESULT | SQL_BUFFER_RESULT | SQL_NO_CACHE | SQL_CALC_FOUND_ROWS
    ;

duplicateSpecification
    : ALL | DISTINCT | DISTINCTROW
    ;

projections
    : (unqualifiedShorthand | projection) (COMMA_ projection)*
    ;

projection
    : expr (AS? alias)? | qualifiedShorthand
    ;

unqualifiedShorthand
    : ASTERISK_
    ;

qualifiedShorthand
    : tableName DOT_ASTERISK_
    ;

fromClause
    : FROM (DUAL | tableReferences)
    ;

tableReferences
    : tableReference (COMMA_ tableReference)*
    ;

escapedTableReference
    : tableFactor joinedTable*
    ;

tableReference
    : (tableFactor | LBE_ OJ escapedTableReference RBE_) joinedTable*
    ;

tableFactor
    : tableName partitionNames? (AS? alias)? indexHintList?
    | expr (AS? alias)?

    | LATERAL? subquery AS? alias (LP_ columnNames RP_)?
    | LP_ tableReferences RP_
    ;

partitionNames
    : PARTITION LP_ partitionName (COMMA_ partitionName)* RP_
    ;

indexHintList
    : indexHint (indexHint)*
    ;

indexHint
    : USE (INDEX | KEY) indexHintClause LP_ (indexNameList)? RP_
    | (IGNORE | FORCE) (INDEX | KEY) indexHintClause LP_ indexNameList RP_
    ;

indexHintClause
    : (FOR (JOIN | ORDER BY | GROUP BY))?
    ;

indexNameList
    : indexName (COMMA_ indexName)*
    ;

joinedTable
    : innerJoinType tableReference joinSpecification?
    | outerJoinType tableReference joinSpecification
    | naturalJoinType tableFactor
    ;

innerJoinType
    : (INNER | CROSS)? JOIN
    | STRAIGHT_JOIN
    ;

outerJoinType
    : (LEFT | RIGHT) OUTER? JOIN
    ;

naturalJoinType
    : NATURAL INNER? JOIN
    | NATURAL (LEFT | RIGHT) OUTER? JOIN
    ;

joinSpecification
    : ON expr | USING LP_ columnNames RP_
    ;

whereClause
    : WHERE expr
    ;

groupByClause
    : GROUP BY orderByItem (COMMA_ orderByItem)* (WITH ROLLUP)?
    ;

havingClause
    : HAVING expr
    ;

limitClause
    : LIMIT ((limitOffset COMMA_)? limitRowCount | limitRowCount OFFSET limitOffset)
    ;

limitRowCount
    : numberLiterals | parameterMarker
    ;

limitOffset
    : numberLiterals | parameterMarker
    ;

windowClause
    : WINDOW windowItem (COMMA_ windowItem)*
    ;

windowItem
    : identifier AS windowSpecification
    ;

subquery
    : LP_ select RP_
    ;

selectLinesInto
    : STARTING BY string_ | TERMINATED BY string_
    ;

selectFieldsInto
    : TERMINATED BY string_ | OPTIONALLY? ENCLOSED BY string_ | ESCAPED BY string_
    ;

selectIntoExpression
    : INTO variable (COMMA_ variable )* | INTO DUMPFILE string_
    | (INTO OUTFILE string_ (CHARACTER SET charsetName)?(COLUMNS selectFieldsInto+)? (LINES selectLinesInto+)?)
    ;

lockClause
    : FOR lockStrength tableLockingList? lockedRowAction?
    | LOCK IN SHARE MODE
    ;

lockClauseList
    : lockClause+
    ;

lockStrength
    : UPDATE | SHARE
    ;

lockedRowAction
    : SKIP_SYMBOL LOCKED | NOWAIT
    ;

tableLockingList
    : OF tableAliasRefList
    ;

tableIdentOptWild
    : tableName DOT_ASTERISK_?
    ;

tableAliasRefList
    : tableIdentOptWild (COMMA_ tableIdentOptWild)*
    ;

returningClause
    : RETURNING targetList
    ;

targetList
    : projection (COMMA_ projection)*
    ;

alterStatement
    : alterTable
    | alterDatabase
    | alterProcedure
    | alterFunction
    | alterEvent
    | alterView
    | alterLogfileGroup
    | alterInstance
    | alterServer
    | alterSequence
    ;
alterSequence
    : ALTER SEQUENCE ifExists? functionName (INCREMENT (BY | EQ_ ) NUMBER_ )? minvalueOption? maxvalueOption? cacheOption? cycleOption? restartOption?
    ;
restartOption
    : RESTART ((WITH|EQ_)? NUMBER_)?
    ;
cycleOption
    : NO CYCLE
    ;
cacheOption
    : CACHE EQ_? NUMBER_
    ;
minvalueOption
    : MINVALUE EQ_? NUMBER_
    | NO MINVALUE
    | NOMINVALUE
    ;
maxvalueOption
    : MAXVALUE EQ_? NUMBER_
    | NO MAXVALUE
    | NOMAXVALUE
    ;
createTable
    : CREATE orReplace? TEMPORARY? TABLE ifNotExists? tableName (createDefinitionClause? createTableOptions? createPartitionOptions? duplicateAsQueryExpression? | createLikeClause)
    ;
orReplace
    : OR REPLACE
    ;
createPartitionOptions
    : PARTITION BY partitionTypeDef (PARTITIONS NUMBER_)? subPartitions? (LP_ partitionDefinitions RP_)?
    ;

partitionTypeDef
    : partitionLinearHash
    | partitionLinearKey
    | (RANGE | LIST) (LP_ bitExpr RP_ | COLUMNS LP_ columnNames RP_ )
    | SYSTEM_TIME (INTERVAL numberLiterals intervalUnit)? (LIMIT NUMBER_)?
    ;

subPartitions
    : SUBPARTITION BY LINEAR? ( partitionLinearHash | partitionLinearKey ) (SUBPARTITIONS NUMBER_)?
    ;
partitionLinearHash
    : LINEAR? HASH LP_ bitExpr RP_
    ;
partitionLinearKey
    : LINEAR? KEY (ALGORITHM EQ_ NUMBER_)? LP_ columnNames  RP_
    ;

duplicateAsQueryExpression
    : (REPLACE | IGNORE)? AS? LP_? select RP_?
    ;

alterTable
    : ALTER ONLINE? IGNORE? TABLE ifExists? tableName (WITH NUMBER_| NOWAIT)? alterTableOptions? alterPartitionOptions? createPartitionOptions? indexOption?
    ;

alterTableOptions
    : (alterTableOption | createTableOption) (COMMA_ (alterTableOption | createTableOption))*
    ;
alterTableOption
    : ADD COLUMN? ifNotExists? (columnDefinitionClause place? | LP_ columnDefinitionClause (COMMA_ columnDefinitionClause)* RP_)  # addColumn
    | ADD tableConstraintDef  # addTableConstraint
    | CHANGE COLUMN? ifExists? columnName columnDefinitionClause place?  # changeColumn
    | MODIFY COLUMN? ifExists? columnName columnTypeAttribute place?   # modifyColumn
    | DROP (COLUMN? ifExists? columnInternalRef=identifier restrict?
        | FOREIGN KEY columnName
        | CONSTRAINT? PRIMARY KEY
        | keyOrIndex ifExists? indexName
        | CHECK identifier
        | FOREIGN KEY ifExists? columnName
        | CONSTRAINT identifier)  # alterTableDrop
    | DISABLE KEYS  # disableKeys
    | ENABLE KEYS   # enableKeys
    | ALTER COLUMN? columnName (SET DEFAULT (LP_ expr RP_| literals)| SET visibility | DROP DEFAULT) # alterColumn
    | ALTER keyOrIndex indexName NOT? visibility  # alterIndex
    | ALTER CHECK constraintName NOT? ENFORCED  # alterCheck
    | ALTER CONSTRAINT constraintName NOT? ENFORCED # alterConstraint
    | defaultCharset collateClause? # alterTableDefaultCharset
    | defaultCollation  # alterTableDefaultCollation
    | (DISCARD | IMPORT) TABLESPACE # alterTableTablespace
    | RENAME COLUMN oldColumn TO newColumn  # renameColumn
    | RENAME (TO | AS)? tableName # alterRenameTable
    | RENAME keyOrIndex indexName TO indexName  # renameIndex
    | CONVERT TO charset charsetName collateClause?  # alterConvert
    | FORCE  # alterTableForce
    | ORDER BY alterOrderList  # alterTableOrder
    | algorithmOption # alterAlgorithm
    | lockOption # alterLock
    | withValidation # alterValidation
    | CONVERT TABLE identifier TO partitionDefinition ((WITH | WITHOUT) VALIDATION)?   #alterConvertTable //mariaDb
    | CONVERT PARTITION partitionName TO TABLE tableName    #alterConvertPartition
    | ADD SYSTEM VERSIONING     #addSystemVersioning
    | DROP SYSTEM VERSIONING    #dropSystemVersioning
    ;

alterOrderList
    : columnRef direction? (COMMA_ columnRef direction?)*
    ;

columnRef
    : identifier (DOT_ identifier)? (DOT_ identifier)?
    ;

tableConstraintDef
    : keyOrIndex ifNotExists? indexName? indexTypeClause? keyListWithExpression indexOption*
    | (FULLTEXT | SPATIAL | VECTOR) keyOrIndex? ifNotExists? indexName? keyListWithExpression indexOption*
    | constraintClause? PRIMARY KEY ifNotExists? indexTypeClause? keyListWithExpression indexOption*
    | constraintClause? UNIQUE keyOrIndex? ifNotExists? indexName? indexTypeClause? keyListWithExpression indexOption*
    | constraintClause? FOREIGN KEY ifNotExists? indexName? keyParts referenceDefinition
    | checkConstraintDefinition
    | PERIOD FOR (timePeriodName | SYSTEM_TIME)? (LP_ startColumnName = columnName COMMA_ endColumnName = columnName RP_)   //mariaDb
    ;
timePeriodName
    : stringLiterals
    ;
withValidation
    : (WITH | WITHOUT) VALIDATION
    ;

alterPartitionOptions
    : alterPartitionOption+
    ;
alterPartitionOption
    : ADD PARTITION LP_ partitionDefinitions RP_
    | ADD PARTITION PARTITIONS NUMBER_
    | DROP PARTITION identifierList
    | DISCARD PARTITION allOrPartitionNameList TABLESPACE
    | IMPORT PARTITION allOrPartitionNameList TABLESPACE
    | TRUNCATE PARTITION allOrPartitionNameList
    | COALESCE PARTITION NUMBER_
    | REORGANIZE PARTITION identifierList INTO LP_ partitionDefinitions RP_
    | EXCHANGE PARTITION identifier WITH TABLE tableName withValidation?
    | ANALYZE PARTITION allOrPartitionNameList
    | CHECK PARTITION allOrPartitionNameList
    | OPTIMIZE PARTITION allOrPartitionNameList
    | REBUILD PARTITION allOrPartitionNameList
    | REPAIR PARTITION allOrPartitionNameList
    | REMOVE ( PARTITION | PARTITIONING)
    ;

constraintClause
    : CONSTRAINT constraintName?
    ;

restrict
    : RESTRICT | CASCADE
    ;

dropTable
    : DROP TEMPORARY? tableOrTables ifExists? tableList waitOption? restrict?
    ;
waitOption
    : WAIT NUMBER_
    | NOWAIT
    ;
dropIndex
    : DROP INDEX indexName (ON tableName)? algorithmOptionAndLockOption?
    ;

algorithmOptionAndLockOption
    : lockOption algorithmOption?
    | algorithmOption lockOption?
    ;
algorithmOption
    : ALGORITHM EQ_? (DEFAULT | INSTANT | INPLACE | COPY | NOCOPY)
    ;

lockOption
    : LOCK EQ_? (DEFAULT | NONE | SHARED | EXCLUSIVE)
    ;

truncateTable
    : TRUNCATE TABLE? tableName waitOption?
    ;

createIndex
    : CREATE orReplace? createIndexSpecification? INDEX ifNotExists? indexName indexTypeClause? ON tableName  keyListWithExpression waitOption? indexOption? algorithmOptionAndLockOption?
    ;

createDatabase
    : CREATE orReplace? (DATABASE | SCHEMA) ifNotExists? databaseName createDatabaseSpecification_*
    ;

alterDatabase
    : ALTER (DATABASE | SCHEMA) databaseName? alterDatabaseSpecification_*
    ;

createDatabaseSpecification_
    : defaultCharset
    | defaultCollation
    | defaultEncryption
    | commentOption //mariaDb
    ;
commentOption
    : COMMENT EQ_? stringLiterals
    ;
alterDatabaseSpecification_
    : createDatabaseSpecification_
    | READ ONLY EQ_? (DEFAULT | NUMBER_)
    | UPGRADE DATA DIRECTORY NAME
    ;

dropDatabase
    : DROP (DATABASE | SCHEMA) ifExists? databaseName
    ;

alterInstance
    : ALTER INSTANCE instanceAction
    ;

instanceAction
    : (ENABLE | DISABLE) INNODB REDO_LOG
    | ROTATE INNODB MASTER KEY
    | ROTATE BINLOG MASTER KEY
    | RELOAD TLS (FOR CHANNEL channel)? (NO ROLLBACK ON ERROR)?
    ;

channel
    : MYSQL_MAIN | MYSQL_ADMIN
    ;

createEvent
    : CREATE orReplace? ownerStatement? EVENT ifNotExists? eventName
      ON SCHEDULE scheduleExpression
      (ON COMPLETION NOT? PRESERVE)?
      (ENABLE | DISABLE | DISABLE ON SLAVE)?
      (COMMENT string_)?
      DO routineBody
    ;

alterEvent
    : ALTER ownerStatement? EVENT eventName
      (ON SCHEDULE scheduleExpression)?
      (ON COMPLETION NOT? PRESERVE)?
      (RENAME TO newEventName)? (ENABLE | DISABLE | DISABLE ON SLAVE)?
      (COMMENT string_)?
      (DO routineBody)?
    ;

dropEvent
    :  DROP EVENT ifExists? eventName
    ;

createFunction
    : CREATE orReplace? ownerStatement? AGGREGATE?
      FUNCTION ifNotExists? functionName LP_ funcParameter? (COMMA_ funcParameter)* RP_
      RETURNS dataType
      routineOption*
      routineBody
    ;
funcParameter
    : ( IN | OUT | INOUT | IN OUT )? identifier (IN | OUT | INOUT | IN OUT)? dataType
    ;
alterFunction
    : ALTER FUNCTION functionName routineOption*
    ;

dropFunction
    : DROP FUNCTION ifExists? functionName
    ;

createProcedure
    : CREATE ownerStatement?
      PROCEDURE procedureName LP_ procedureParameter? (COMMA_ procedureParameter)* RP_
      routineOption*
      routineBody
    ;

alterProcedure
    : ALTER PROCEDURE procedureName routineOption*
    ;

dropProcedure
    : DROP PROCEDURE ifExists? procedureName
    ;

createServer
    : CREATE SERVER serverName
      FOREIGN DATA WRAPPER wrapperName
      OPTIONS LP_ serverOption (COMMA_ serverOption)* RP_
    ;

alterServer
    : ALTER SERVER serverName OPTIONS
      LP_ serverOption (COMMA_ serverOption)* RP_
    ;

dropServer
    : DROP SERVER ifExists? serverName
    ;

createView
    : CREATE (OR REPLACE)?
      (ALGORITHM EQ_ (UNDEFINED | MERGE | TEMPTABLE))?
      ownerStatement?
      (SQL SECURITY (DEFINER | INVOKER))?
      VIEW ifNotExists? viewName (LP_ columnNames RP_)?
      AS select
      (WITH (CASCADED | LOCAL)? CHECK OPTION)?
    ;

alterView
    : ALTER (ALGORITHM EQ_ (UNDEFINED | MERGE | TEMPTABLE))?
      ownerStatement?
      (SQL SECURITY (DEFINER | INVOKER))?
      VIEW viewName (LP_ columnNames RP_)?
      AS select
      (WITH (CASCADED | LOCAL)? CHECK OPTION)?
    ;

dropView
    : DROP VIEW ifExists? viewNames restrict?
    ;

createTablespace
    : CREATE UNDO? TABLESPACE identifier (createTablespaceInnodb | createTablespaceNdb | createTablespaceInnodbAndNdb)*
    ;

createTablespaceInnodb
    : FILE_BLOCK_SIZE EQ_ fileSizeLiteral
    | ENCRYPTION EQ_ y_or_n=string_
    | ENGINE_ATTRIBUTE EQ_? jsonAttribute = string_
    ;

createTablespaceNdb
    : USE LOGFILE GROUP identifier
    | EXTENT_SIZE EQ_? fileSizeLiteral
    | INITIAL_SIZE EQ_? fileSizeLiteral
    | MAX_SIZE EQ_? fileSizeLiteral
    | NODEGROUP EQ_? identifier
    | WAIT
    | COMMENT EQ_? string_
    ;

createTablespaceInnodbAndNdb
    : ADD DATAFILE string_
    | AUTOEXTEND_SIZE EQ_? fileSizeLiteral
    | ENGINE EQ_? engineRef
    ;

alterTablespace
    : alterTablespaceInnodb | alterTablespaceNdb
    ;

alterTablespaceNdb
    : ALTER UNDO? TABLESPACE tablespace=identifier
      (ADD | DROP) DATAFILE string_
      (INITIAL_SIZE EQ_? fileSizeLiteral)?
      WAIT? (RENAME TO renameTableSpace=identifier)?
      (ENGINE EQ_? identifier)?
    ;

alterTablespaceInnodb
    : ALTER UNDO? TABLESPACE tablespace=identifier
      (SET (ACTIVE | INACTIVE))?
      (AUTOEXTEND_SIZE EQ_? fileSizeLiteral)?
      (ENCRYPTION EQ_? y_or_n=string_)?
      (ENGINE_ATTRIBUTE EQ_? jsonAttribute = string_)?
      (RENAME TO renameTablespace=identifier)?
      (ENGINE EQ_? identifier)?
    ;

dropTablespace
    : DROP UNDO? TABLESPACE identifier (ENGINE EQ_? identifier)?
    ;

createLogfileGroup
    : CREATE LOGFILE GROUP identifier
      ADD UNDOFILE string_
      (INITIAL_SIZE EQ_? fileSizeLiteral)?
      (UNDO_BUFFER_SIZE EQ_? fileSizeLiteral)?
      (REDO_BUFFER_SIZE EQ_? fileSizeLiteral)?
      (NODEGROUP EQ_? identifier)?
      WAIT?
      (COMMENT EQ_? string_)?
      (ENGINE EQ_? identifier)?
    ;

alterLogfileGroup
    : ALTER LOGFILE GROUP identifier
      ADD UNDOFILE string_
      (INITIAL_SIZE EQ_? fileSizeLiteral)?
      WAIT?
      (ENGINE EQ_? identifier)?
    ;

dropLogfileGroup
    : DROP LOGFILE GROUP identifier (ENGINE EQ_? identifier)?
    ;

createTrigger
    :  CREATE orReplace? ownerStatement? TRIGGER ifNotExists? triggerName triggerTime triggerEvent ON tableName FOR EACH ROW triggerOrder? routineBody
    ;

dropTrigger
    : DROP TRIGGER ifExists? triggerName
    ;

renameTable
    : RENAME (TABLE | TABLES) ifExists? renameTableClause (COMMA_ renameTableClause)*
    ;
renameTableClause
    : oldName=tableName waitOption? TO newName=tableName
    ;
createDefinitionClause
    : LP_ createDefinitionItem (COMMA_ createDefinitionItem)* RP_
    ;

createDefinitionItem
    : columnDefinitionClause
    | tableConstraintDef
    ;

columnDefinitionClause
    : name columnTypeAttribute
    ;

columnTypeAttribute
    : dataType columnAttribute*
    ;

columnAttribute
    : NOT? NULL
    | DEFAULT (literals | now | LP_ expr RP_| simpleExpr)
    | visibility
    | ON UPDATE now
    | AUTO_INCREMENT
    | UNIQUE KEY?
    | PRIMARY? KEY
    | COMMENT comment = string_
    | collateClause
    | COLUMN_FORMAT (FIXED| DYNAMIC| DEFAULT)
    | ENGINE_ATTRIBUTE EQ_? string_
    | SECONDARY_ENGINE_ATTRIBUTE EQ_? string_
    | STORAGE (DISK | MEMORY)
    | referenceDefinition
    | checkConstraintDefinition
    | (GENERATED ALWAYS)? AS (LP_ expr RP_)? (VIRTUAL | STORED)?
    | value = ENGINE_ATTRIBUTE EQ_? jsonAttribute = string_
    | (WITH | WITHOUT) SYSTEM VERSIONING
    | REF_SYSTEM_ID EQ_ identifier
    | precision
    | NOT NULL ENABLE
    | (VIRTUAL | PERSISTENT | STORED)
    ;

checkConstraintDefinition
    : constraintClause? CHECK LP_ expr RP_ (NOT? ENFORCED)?
    ;

referenceDefinition
    : REFERENCES tableName keyParts (MATCH FULL | MATCH PARTIAL | MATCH SIMPLE)? onUpdateDelete?
    ;

onUpdateDelete
    : ON UPDATE referenceOption (ON DELETE referenceOption)?
    | ON DELETE referenceOption (ON UPDATE referenceOption)?
    ;

referenceOption
    : RESTRICT | CASCADE | SET NULL | NO ACTION | SET DEFAULT
    ;

indexTypeClause
    : USING (BTREE | HASH | RTREE)
    ;

keyParts
    : LP_ keyPart (COMMA_ keyPart)* RP_
    ;

keyPart
    : columnName fieldLength? direction?
    ;

keyPartWithExpression
    : keyPart | LP_ expr RP_ direction?
    ;

keyListWithExpression
    : LP_ keyPartWithExpression (COMMA_ keyPartWithExpression)* RP_
    ;

indexOption
    : KEY_BLOCK_SIZE EQ_? NUMBER_
    | indexTypeClause
    | WITH PARSER identifier
    | COMMENT EQ_? stringLiterals
    | visibility
    | ENGINE_ATTRIBUTE EQ_? jsonAttribute = string_
    | SECONDARY_ENGINE_ATTRIBUTE EQ_? jsonAttribute = string_
    | CLUSTERING EQ_ (YES |NO)
    | (IGNORED | NOT IGNORED)
    ;

visibility
    : VISIBLE | INVISIBLE
    ;

createLikeClause
    : LP_? LIKE tableName RP_?
    ;

createIndexSpecification
    : UNIQUE | FULLTEXT | SPATIAL
    ;

createTableOptions
    : createTableOption (COMMA_? createTableOption)*
    ;

createTableOption
    : AUTOEXTEND_SIZE EQ_? NUMBER_
    | AUTO_INCREMENT EQ_? NUMBER_
    | AVG_ROW_LENGTH EQ_? NUMBER_
    | defaultCharset
    | CHECKSUM EQ_? NUMBER_
    | defaultCollation
    | COMMENT EQ_? string_
    | COMPRESSION EQ_? textString
    | CONNECTION EQ_? textString
    | (DATA | INDEX) DIRECTORY EQ_? textString
    | DELAY_KEY_WRITE EQ_? NUMBER_
    | ENCRYPTION EQ_? textString
    | STORAGE? ENGINE EQ_? engineRef
    | ENGINE_ATTRIBUTE EQ_? jsonAttribute = string_
    | INSERT_METHOD EQ_? method = (NO| FIRST| LAST)
    | KEY_BLOCK_SIZE EQ_? NUMBER_
    | MAX_ROWS EQ_? NUMBER_
    | MIN_ROWS EQ_? NUMBER_
    | PACK_KEYS EQ_? ternaryOption=(NUMBER_ | DEFAULT)
    | PASSWORD EQ_? string_
    | ROW_FORMAT EQ_? format = (DEFAULT | DYNAMIC | FIXED | COMPRESSED | REDUNDANT | COMPACT)
    | START TRANSACTION
    | SECONDARY_ENGINE_ATTRIBUTE EQ_? jsonAttribute = string_
    | (STATS_AUTO_RECALC | STATS_PERSISTENT | STATS_SAMPLE_PAGES) EQ_? ternaryOption=(NUMBER_ | DEFAULT)
    | UNION EQ_? LP_ tableList RP_
    | tablespaceOption
    | option = AUTOEXTEND_SIZE EQ_? fileSizeLiteral
    | ENCRYPTED EQ_ (YES | NO)  // mariaDB
    | ENCRYPTION_KEY_ID EQ_? NUMBER_
    | IETF_QUOTES EQ_? (YES | NO)
    | INDEX DIRECTORY EQ_ stringLiterals
    | PAGE_CHECKSUM EQ_? NUMBER_
    | PAGE_COMPRESSED EQ_? NUMBER_
    | PAGE_COMPRESSION_LEVEL EQ_? NUMBER_
    | SEQUENCE EQ_? NUMBER_
    | TRANSACTIONAL EQ_? NUMBER_
    | WITH SYSTEM VERSIONING
    ;
tablespaceOption
    : TABLESPACE tablespaceName = identifier (STORAGE DISK)?
    | (TABLESPACE tablespaceName = identifier)? STORAGE MEMORY
    ;
createSRSStatement
    : CREATE OR REPLACE SPATIAL REFERENCE SYSTEM NUMBER_ srsAttribute*
    | CREATE SPATIAL REFERENCE SYSTEM ifNotExists? NUMBER_ srsAttribute*
    ;

dropSRSStatement
    : DROP SPATIAL REFERENCE SYSTEM ifNotExists? NUMBER_
    ;

srsAttribute
    : NAME string_
    | DEFINITION string_
    | ORGANIZATION string_ IDENTIFIED BY NUMBER_
    | DESCRIPTION string_
    ;

place
    : FIRST | AFTER columnName
    ;

partitionDefinitions
    : partitionDefinition (COMMA_ partitionDefinition)*
    ;

partitionDefinition
    : PARTITION? partitionName
    (VALUES (LESS THAN partitionLessThanValue | IN LP_ partitionValueList RP_))?
    partitionDefinitionOption
    (LP_ subpartitionDefinition (COMMA_ subpartitionDefinition)* RP_)?
    ;

partitionLessThanValue
    : LP_ (expr | partitionValueList) RP_ | MAXVALUE
    ;

partitionValueList
    : expr (COMMA_ expr)*
    ;

partitionDefinitionOption
    : (STORAGE? ENGINE EQ_? identifier)?
    (COMMENT EQ_? string_)?
    (DATA DIRECTORY EQ_? string_)?
    (INDEX DIRECTORY EQ_? string_)?
    (MAX_ROWS EQ_? NUMBER_)?
    (MIN_ROWS EQ_? NUMBER_)?
    (TABLESPACE EQ_? identifier)?
    (NODEGROUP EQ_? NUMBER_)?   //mariaDB
    ;

subpartitionDefinition
    : SUBPARTITION identifier partitionDefinitionOption
    ;

ownerStatement
    : DEFINER EQ_ (username | CURRENT_USER ( LP_ RP_)? | roleName | CURRENT_ROLE)
    ;

scheduleExpression
    : AT timestampValue (PLUS_ intervalExpression)*
    | EVERY intervalValue
      (STARTS timestampValue (PLUS_ intervalExpression)*)?
      (ENDS timestampValue (PLUS_ intervalExpression)*)?
    ;

timestampValue
    : CURRENT_TIMESTAMP | stringLiterals | numberLiterals | expr
    ;

routineBody
    : simpleStatement | compoundStatement
    ;

serverOption
    : HOST string_
    | DATABASE string_
    | USER string_
    | PASSWORD string_
    | SOCKET string_
    | OWNER string_
    | PORT numberLiterals
    ;

routineOption
    : COMMENT string_
    | LANGUAGE SQL
    | NOT? DETERMINISTIC
    | (CONTAINS SQL | NO SQL | READS SQL DATA | MODIFIES SQL DATA)
    | SQL SECURITY (DEFINER | INVOKER)
    ;

procedureParameter
    : (IN | OUT | INOUT | IN OUT)? identifier (IN | OUT | INOUT | IN OUT)? dataType
    ;

fileSizeLiteral
    : FILESIZE_LITERAL | numberLiterals
    ;

simpleStatement
    : validStatement
    ;

compoundStatement
    : beginStatement
    ;

validStatement
    : (createTable | alterTable | dropTable | dropDatabase | truncateTable
    | insert | replace | update | delete | select | call
    | createView | prepare | executeStmt | commit | deallocate | startTransaction
    | setVariable | beginStatement | declareStatement | flowControlStatement | cursorStatement | conditionHandlingStatement | procedureCall | expr) SEMI_?
    ;

procedureCall
    : (packageName DOT_)? procedureName (LP_ (parame=expression (COMMA_ parame=expression)*)? RP_)? SEMI_
    ;
expression
    : expr
    ;
beginStatement
    : (labelName COLON_)? BEGIN validStatement* END labelName? SEMI_?
    ;

declareStatement
    : DECLARE variable (COMMA_ variable)* dataType (DEFAULT simpleExpr)*
    ;

flowControlStatement
    : caseStatement | ifStatement | iterateStatement | leaveStatement | loopStatement | repeatStatement | returnStatement | whileStatement
    ;

caseStatement
    : CASE expr?
      (WHEN expr THEN validStatement+)+
      (ELSE validStatement+)?
      END CASE
    ;

ifStatement
    : IF expr THEN validStatement+
      (ELSEIF expr THEN validStatement+)*
      (ELSE validStatement+)?
      END IF
    ;

iterateStatement
    : ITERATE labelName
    ;

leaveStatement
    : LEAVE labelName
    ;

loopStatement
    : (labelName COLON_)? LOOP
      validStatement+
      END LOOP labelName?
    ;

repeatStatement
    : (labelName COLON_)? REPEAT
      validStatement+
      UNTIL expr
      END REPEAT labelName?
    ;

returnStatement
    : RETURN expr
    ;

whileStatement
    : (labelName COLON_)? WHILE expr DO
      validStatement+
      END WHILE labelName?
    ;

cursorStatement
    : cursorCloseStatement | cursorDeclareStatement | cursorFetchStatement | cursorOpenStatement
    ;

cursorCloseStatement
    : CLOSE cursorName
    ;

cursorDeclareStatement
    : DECLARE cursorName CURSOR FOR select
    ;

cursorFetchStatement
    : FETCH ((NEXT)? FROM)? cursorName INTO variable (COMMA_ variable)*
    ;

cursorOpenStatement
    : OPEN cursorName
    ;

conditionHandlingStatement
    : declareConditionStatement | declareHandlerStatement | getDiagnosticsStatement | resignalStatement | signalStatement
    ;

declareConditionStatement
    : DECLARE conditionName CONDITION FOR conditionValue
    ;

declareHandlerStatement
    : DECLARE handlerAction HANDLER FOR conditionValue (COMMA_ conditionValue)* validStatement
    ;

getDiagnosticsStatement
    : GET (CURRENT | STACKED)? DIAGNOSTICS (
        (statementInformationItem (COMMA_ statementInformationItem)*
        | (CONDITION conditionNumber conditionInformationItem (COMMA_ conditionInformationItem)*))
    )
    ;

statementInformationItem
    : variable EQ_ statementInformationItemName
    ;

conditionInformationItem
    : variable EQ_ conditionInformationItemName
    ;

conditionNumber
    : variable | numberLiterals
    ;

statementInformationItemName
    : NUMBER
    | ROW_COUNT
    ;

conditionInformationItemName
    : CLASS_ORIGIN
    | SUBCLASS_ORIGIN
    | RETURNED_SQLSTATE
    | MESSAGE_TEXT
    | MYSQL_ERRNO
    | CONSTRAINT_CATALOG
    | CONSTRAINT_SCHEMA
    | CONSTRAINT_NAME
    | CATALOG_NAME
    | SCHEMA_NAME
    | TABLE_NAME
    | COLUMN_NAME
    | CURSOR_NAME
    ;

handlerAction
    : CONTINUE | EXIT | UNDO
    ;

conditionValue
    : numberLiterals | SQLSTATE (VALUE)? stringLiterals | conditionName | SQLWARNING | NOT FOUND | SQLEXCEPTION | OTHERS
    ;

resignalStatement
    : RESIGNAL conditionValue?
      (SET signalInformationItem (COMMA_ signalInformationItem)*)?
    ;

signalStatement
    : SIGNAL conditionValue
      (SET signalInformationItem (COMMA_ signalInformationItem)*)?
    ;

signalInformationItem
    : conditionInformationItemName EQ_ expr
    ;

prepare
    : PREPARE identifier FROM (stringLiterals | userVariable)
    ;

executeStmt
    : EXECUTE identifier (USING executeVarList)?
    ;

executeVarList
    : userVariable (COMMA_ userVariable)*
    ;

deallocate
    : (DEALLOCATE | DROP) PREPARE identifier
    ;

setTransaction
    : SET optionType? TRANSACTION transactionCharacteristics
    ;

setAutoCommit
    : SET (AT_? AT_)? optionType? DOT_? AUTOCOMMIT EQ_ autoCommitValue=(NUMBER_ | ON | OFF)
    ;

beginTransaction
    : BEGIN WORK? | startTransaction
    ;

startTransaction
    : START TRANSACTION (transactionCharacteristic (COMMA_ transactionCharacteristic)*)?
    ;

transactionCharacteristic
    : WITH CONSISTENT SNAPSHOT | transactionAccessMode
    ;

commit
    : COMMIT WORK? optionChain? optionRelease?
    ;

rollback
    : ROLLBACK (WORK? TO SAVEPOINT? identifier | WORK? optionChain? optionRelease?)
    ;

savepoint
    : SAVEPOINT identifier
    ;

begin
    : BEGIN WORK?
    ;

lock
    : LOCK (INSTANCE FOR BACKUP | ((TABLES | TABLE) tableLock (COMMA_ tableLock)*))
    ;

unlock
    : UNLOCK (INSTANCE | TABLE | TABLES)
    ;

releaseSavepoint
    : RELEASE SAVEPOINT identifier
    ;

optionChain
    : AND NO? CHAIN
    ;

optionRelease
    : NO? RELEASE
    ;

tableLock
    : tableName (AS? alias)? lockType
    ;

lockType
    : READ LOCAL? | LOW_PRIORITY? WRITE
    ;

xaBegin
    : XA (START | BEGIN) xid (JOIN | RESUME)?
    ;

xaPrepare
    : XA PREPARE xid
    ;

xaCommit
    : XA COMMIT xid (ONE PHASE)?
    ;

xaRollback
    : XA ROLLBACK xid
    ;

xaEnd
    : XA END xid (SUSPEND (FOR MIGRATE)?)?
    ;

xaRecovery
    : XA RECOVER (CONVERT XID)?
    ;

xid
    : gtrid=textString (COMMA_ bqual=textString (COMMA_ formatID=NUMBER_)?)?
    ;

changeMasterTo
    : CHANGE MASTER TO masterDefs  channelOption?
    ;

changeReplicationFilter
    : CHANGE REPLICATION FILTER filterDefs channelOption?
    ;

changeReplicationSourceTo
    : CHANGE REPLICATION SOURCE TO changeReplicationSourceOptionDefs channelOption?
    ;

startSlave
    : START SLAVE threadTypes? utilOption? connectionOptions* channelOption?
    ;

stopSlave
    : STOP SLAVE threadTypes channelOption*
    ;

resetSlave
    : STOP SLAVE ALL? channelOption?
    ;

startReplica
    : START REPLICA threadTypes? utilOption? connectionOptions* channelOption?
    ;

stopReplica
    : STOP REPLICA threadTypes? channelOption?
    ;

resetReplica
    : RESET REPLICA ALL? channelOption?
    ;

groupReplication
    : startGroupReplication | stopGroupReplication
    ;

startGroupReplication
    : START GROUP_REPLICATION
    ;

stopGroupReplication
    : STOP GROUP_REPLICATION
    ;

purgeBinaryLog
    : PURGE (BINARY | MASTER) LOGS (TO logName | BEFORE datetimeExpr)
    ;

threadTypes
    : threadType+
    ;

threadType
    : RELAY_THREAD | SQL_THREAD
    ;

utilOption
    : UNTIL ((SQL_BEFORE_GTIDS | SQL_AFTER_GTIDS) EQ_ identifier
    | MASTER_LOG_FILE EQ_ string_ COMMA_ MASTER_LOG_POS EQ_ NUMBER_
    | RELAY_LOG_FILE EQ_ string_ COMMA_ RELAY_LOG_POS  EQ_ NUMBER_
    | SQL_AFTER_MTS_GAPS)
    ;

connectionOptions
    : (USER | PASSWORD | DEFAULT_AUTH | PLUGIN_DIR) EQ_ string_
    ;

masterDefs
    : masterDef (COMMA_ masterDef)*
    ;

masterDef
    : identifier EQ_ (literals | identifier | LP_ ignoreServerIds? RP_)
    ;

ignoreServerIds
    : ignoreServerId (COMMA_ ignoreServerId)
    ;

ignoreServerId
    : NUMBER_
    ;

filterDefs
    : filterDef (COMMA_ filterDef)*
    ;

filterDef
    : REPLICATE_DO_DB EQ_ LP_ databaseNames? RP_
    | REPLICATE_IGNORE_DB EQ_ LP_ databaseNames? RP_
    | REPLICATE_DO_TABLE EQ_ LP_ tableList? RP_
    | REPLICATE_IGNORE_TABLE EQ_ LP_ tableList? RP_
    | REPLICATE_WILD_DO_TABLE EQ_ LP_ wildTables? RP_
    | REPLICATE_WILD_IGNORE_TABLE EQ_ LP_ wildTables? RP_
    | REPLICATE_REWRITE_DB EQ_ LP_ databasePairs? RP_
    ;

wildTables
    : wildTable (COMMA_ wildTable)*
    ;

wildTable
    : string_
    ;

changeReplicationSourceOptionDefs
    : changeReplicationSourceOption (COMMA_ changeReplicationSourceOption)*
    ;

changeReplicationSourceOption
    : identifier EQ_ (literals | identifier | LP_ ignoreServerIds? RP_)
    ;







// TODO ========================  base ===========================

parameterMarker
    : QUESTION_
    ;

literals
    : stringLiterals
    | numberLiterals
    | temporalLiterals
    | hexadecimalLiterals
    | bitValueLiterals
    | booleanLiterals
    | nullValueLiterals
    ;

string_
    : DOUBLE_QUOTED_TEXT | SINGLE_QUOTED_TEXT
    ;

stringLiterals
    : (UNDERSCORE_CHARSET | UL_BINARY )? string_ | NCHAR_TEXT
    ;

numberLiterals
    : (PLUS_ | MINUS_)? NUMBER_
    ;

temporalLiterals
    : (DATE | TIME | TIMESTAMP) textString
    ;

hexadecimalLiterals
    : UNDERSCORE_CHARSET? UL_BINARY? HEX_DIGIT_ collateClause?
    ;

bitValueLiterals
    : UNDERSCORE_CHARSET? BIT_NUM_ collateClause?
    ;

booleanLiterals
    : TRUE | FALSE
    ;

nullValueLiterals
    : NULL
    ;

collationName
    : textOrIdentifier | BINARY
    ;

identifier
    : IDENTIFIER_
    | DOUBLE_QUOTED_TEXT
    | UNDERSCORE_CHARSET
    | BQUOTA_STRING
    | I_CURSOR
    | unreservedWord
    ;
unreservedWord
    : ACTION | MYSQL | ACCOUNT | ACTIVE | ADMIN | AFTER | AGAINST | AGGREGATE | ALGORITHM | ALWAYS | ANY | ARRAY | AT
    | ATTRIBUTE | AUTOEXTEND_SIZE | AUTO_INCREMENT | AVG_ROW_LENGTH | AVG | BACKUP | BEFORE | BINLOG | BIT
    | BLOCK | BOOLEAN | BOOL | BTREE | BUCKETS | CASCADED | CATALOG_NAME | CHAIN | CHANGED | CHANNEL | CIPHER
    | CLASS_ORIGIN | CLIENT | CLOSE | COALESCE | CODE | COLLATION | COLUMNS | COLUMN_FORMAT | COLUMN_NAME
    | COMMITTED | COMPACT | COMPLETION | COMPONENT | COMPRESSED | COMPRESSION | CONCURRENT | CONNECTION
    | CONSISTENT | CONSTRAINT_CATALOG | CONSTRAINT_NAME | CONSTRAINT_SCHEMA | CONTEXT | CPU | CREATE | CURRENT
    | CURSOR_NAME | DATAFILE | DATA | DATETIME | DATE | DAY | DAY_MINUTE | DEFAULT_AUTH | DEFAULT | DEFINER
    | DEFINITION | DELAY_KEY_WRITE | DESCRIPTION | DIAGNOSTICS | DIRECTORY | DISABLE | DISCARD | DISK | DUMPFILE
    | DUPLICATE | DROP | DYNAMIC | ENABLE | ENCRYPTION | ENDS | ENFORCED | ENGINES | ENGINE | ENGINE_ATTRIBUTE
    | ENUM | ERRORS | ERROR | ESCAPE | EVENTS | EVERY | EXCHANGE  | EXPANSION | EXPIRE | EXPORT | EXTENDED
    | EXTENT_SIZE | FAILED_LOGIN_ATTEMPTS | FAST | FAULTS | FILE_BLOCK_SIZE | FILTER | FIRST | FIXED | FOLLOWING
    | FORMAT | FOUND | FULL | GENERAL | GEOMETRYCOLLECTION | GEOMETRY
    | GRANTS | GROUP_REPLICATION | HASH | HISTOGRAM | HISTORY | HOSTS | HOST | HOUR | IDENTIFIED
    | INACTIVE | INDEXES | INITIAL_SIZE | INSERT_METHOD | INSTANCE | INVISIBLE | INVOKER
    | IO | IPC | ISOLATION | ISSUER | JSON | JSON_VALUE | KEY | KEYS | KEY_BLOCK_SIZE | LAST | LEAVES | LESS
    | LEVEL | LINESTRING | LIST | LOCKED  | LOGFILE | LOGS | MASTER_LOG_FILE
    | MASTER_LOG_POS | MASTER | MAX_CONNECTIONS_PER_HOUR | MAX_QUERIES_PER_HOUR | MAX_ROWS | MAX_SIZE
    | MAX_UPDATES_PER_HOUR | MAX_USER_CONNECTIONS | MEDIUM | MEMBER | MEMORY | MERGE | MESSAGE_TEXT | MICROSECOND
    | MIGRATE | MINUTE | MIN_ROWS | MODE | MODIFY | MONTH | MULTILINESTRING | MULTIPOINT | MULTIPOLYGON | MUTEX
    | MYSQL_ERRNO | NAMES | NAME | NATIONAL | NCHAR | NESTED | NEVER | NEXT | NODEGROUP | NOW | NOWAIT
    | NULLS | NUMBER | NVARCHAR | ON | OFF | OFFSET | OJ | OLD | ONE | ONLY | OPEN | OPTIONAL | OPTIONS | MODIFIES
    | ORDINALITY | ORGANIZATION | OTHERS | OWNER | PACK_KEYS | PAGE | PARSER | PARTIAL | PARTITIONING | PARTITIONS
    | PASSWORD | PASSWORD_LOCK_TIME | PATH | PHASE | PLUGINS | PLUGIN_DIR | PLUGIN | POINT | POLYGON | PORT
    | PRECEDING | PRESERVE | PREV | PRIVILEGES | PROCESSLIST | PROFILES | PROFILE
    | QUARTER | QUERY | QUICK | RANDOM | RANK | REBUILD | RECOVER | REDO_BUFFER_SIZE | REDUNDANT
    | REFERENCE | RELAY | RELAYLOG | RELAY_LOG_FILE | RELAY_LOG_POS | RELAY_THREAD | REMOVE | REORGANIZE
    | REPEATABLE | REPLICATE_DO_DB | REPLICATE_DO_TABLE | REPLICATE_IGNORE_DB | REPLICATE_IGNORE_TABLE
    | REPLICATE_REWRITE_DB | REPLICATE_WILD_DO_TABLE | REPLICATE_WILD_IGNORE_TABLE
    | USER_RESOURCES | RESPECT | RESUME | RETAIN | RETURNED_SQLSTATE
    | RETURNING | RETURNS | REUSE | REVERSE | ROLE | ROLLUP | ROTATE | ROUTINE | ROW_COUNT | ROW_FORMAT | RTREE
    | SCHEDULE | SCHEMA_NAME | SECONDARY_ENGINE | SECONDARY_ENGINE_ATTRIBUTE | SECONDARY
    | SECOND | SECURITY | SERIALIZABLE | SERIAL | SERVER | SHARE | SIMPLE | SKIP_SYMBOL | SLOW
    | SNAPSHOT | SOCKET | SONAME | SOUNDS | SOURCE | SQL_AFTER_GTIDS | SQL_AFTER_MTS_GAPS | SQL_BEFORE_GTIDS
    | SQL_BUFFER_RESULT | SQL_NO_CACHE | SQL_THREAD | STACKED | STARTS | STATS_AUTO_RECALC
    | STATS_PERSISTENT | STATS_SAMPLE_PAGES | STATUS | STORAGE | STRING | SUBCLASS_ORIGIN
    | SUBJECT | SUBPARTITIONS | SUBPARTITION | SUSPEND | SWAPS | SWITCHES | SYSTEM | TABLE | TABLES
    | TABLESPACE | TABLE_NAME | TEMPORARY | TEMPTABLE | TEXT | THAN | THREAD_PRIORITY
    | TIMESTAMP_ADD | TIMESTAMP_DIFF | TIMESTAMP | TIME | TLS | TRANSACTION | TRIGGERS | TYPE | UNBOUNDED
    | UNCOMMITTED | UNDEFINED | UNDOFILE | UNDO_BUFFER_SIZE | UNKNOWN | UNTIL | UPGRADE | USER | USE_FRM | VALIDATION
    | VALUE | VARIABLES | VCPU | VIEW | VISIBLE | WAIT | WARNINGS | WEEK | WEIGHT_STRING | WITHOUT | WORK | WRAPPER
    | X509 | XID | XML | YEAR | YEAR_MONTH | CONDITION | DESCRIBE | PUBLIC | G | ID | EXECUTE
    | RESTART | SHUTDOWN | ASCII | BEGIN | BYTE | CACHE | CHARSET | CHECKSUM | CLONE
    | COMMENT | COMMIT | CONTAINS | DEALLOCATE | DO | END | FLUSH | FOLLOWS | HANDLER
    | HELP | IMPORT | INSTALL | LANGUAGE | NO | PRECEDES | PREPARE | REPAIR | RESET
    | ROLLBACK | SAVEPOINT | SIGNED | SLAVE | START | STOP | TRUNCATE | UNICODE | UNINSTALL
    | XA |EVENT | FILE | NONE | PROCESS | PROXY | RELOAD | REPLICATION | RESOURCE | SUPER
    | GLOBAL | LOCAL | PERSIST | PERSIST_ONLY | SESSION | MAX | MIN | SUM | COUNT | GROUP_CONCAT
    | CAST | POSITION | SUBSTRING | SUBSTR | MID | EXTRACT | TRIM | LAST_DAY | TRADITIONAL
    | TREE | MYSQL_ADMIN | INSTANT | INPLACE | COPY | UL_BINARY | AUTOCOMMIT | INNODB | MEMORY
    | REDO_LOG | LAST_VALUE | PRIMARY | MAXVALUE | BIT_XOR | KILL | LOOP | NATURAL | RANGE
    | MYSQL_MAIN | UTC_TIME | UTC_TIMESTAMP | UTC_TIMESTAMP
    ;

textOrIdentifier
    : identifier | string_ | ipAddress
    ;

ipAddress
    : IP_ADDRESS
    ;

variable
    : userVariable | systemVariable
    ;

userVariable
    : AT_ textOrIdentifier
    | textOrIdentifier
    ;

systemVariable
    : AT_ AT_ (systemVariableScope=(GLOBAL | SESSION | LOCAL) DOT_)? rvalueSystemVariable
    ;

rvalueSystemVariable
    : textOrIdentifier
    | textOrIdentifier DOT_ identifier
    ;

setSystemVariable
    : AT_ AT_ (optionType DOT_)? internalVariableName
    ;

optionType
    : GLOBAL | PERSIST | PERSIST_ONLY | SESSION | LOCAL
    ;

internalVariableName
    : identifier
    | DEFAULT DOT_ identifier
    | identifier DOT_ identifier
    ;

setExprOrDefault
    : expr | DEFAULT | ALL | ON | BINARY | ROW | SYSTEM
    ;

transactionCharacteristics
    : transactionAccessMode (COMMA_ isolationLevel)?
    | isolationLevel (COMMA_ transactionAccessMode)?
    ;

isolationLevel
    : ISOLATION LEVEL isolationTypes
    ;

isolationTypes
    : REPEATABLE READ | READ COMMITTED | READ UNCOMMITTED | SERIALIZABLE
    ;

transactionAccessMode
    : READ (WRITE | ONLY)
    ;

databaseName
    : identifier
    ;

databaseNames
    : databaseName (COMMA_ databaseName)*
    ;

charsetName
    : textOrIdentifier | BINARY | DEFAULT
    ;

databasePairs
    : databasePair (COMMA_ databasePair)*
    ;

databasePair
    : LP_ databaseName COMMA_ databaseName RP_
    ;

tableName
    : (owner DOT_)? name
    ;

columnName
    : (owner DOT_)* name
    ;

indexName
    : (owner DOT_)? name
    ;

constraintName
    : identifier
    ;

oldColumn
    : columnName
    ;

newColumn
    : columnName
    ;

delimiterName
    : textOrIdentifier | ('\\'. | ~('\'' | '"' | '`' | '\\'))+
    ;

userIdentifierOrText
    : textOrIdentifier (AT_ textOrIdentifier)?
    ;

username
    : userIdentifierOrText | CURRENT_USER (LP_ RP_)?
    ;

eventName
    : (owner DOT_)? identifier
    ;

newEventName
    : eventName
    ;

serverName
    : textOrIdentifier
    ;

wrapperName
    : textOrIdentifier
    ;

functionName
    : (owner DOT_)? name
    ;

procedureName
    : (owner DOT_)? name
    ;

viewName
    : (owner DOT_)? name
    ;

owner
    : identifier
    ;

alias
    : textOrIdentifier
    ;

name
    : identifier
    ;

tableList
    : tableName (COMMA_ tableName)*
    ;

viewNames
    : viewName (COMMA_ viewName)*
    ;

columnNames
    : columnName (COMMA_ columnName)*
    ;

groupName
    : identifier
    ;

shardLibraryName
    : stringLiterals
    ;

componentName
    : string_
    ;

pluginName
    : textOrIdentifier
    ;

hostname
    : string_
    ;

port
    : NUMBER_
    ;

cloneInstance
    : username AT_ hostname COLON_ port
    ;

cloneDir
    : string_
    ;

channelName
    : identifier (DOT_ identifier)?
    ;

logName
    : stringLiterals
    ;

roleName
    : roleIdentifierOrText (AT_ textOrIdentifier)?
    ;

roleIdentifierOrText
    : identifier | string_
    ;

engineRef
    : textOrIdentifier
    ;

triggerName
    : (owner DOT_)? name
    ;

triggerTime
    : BEFORE | AFTER
    ;

tableOrTables
    : TABLE | TABLES
    ;

partitionName
    : identifier
    ;

identifierList
    : identifier (COMMA_ identifier)*
    ;

allOrPartitionNameList
    : ALL | identifierList
    ;

triggerEvent
    : INSERT | UPDATE | DELETE
    ;

triggerOrder
    : (FOLLOWS | PRECEDES) triggerName
    ;

expr
    : booleanPrimary
    | expr andOperator expr
    | expr orOperator expr
    | expr XOR expr
    | notOperator expr
    ;

andOperator
    : AND | AND_
    ;

orOperator
    : OR | OR_
    ;

notOperator
    : NOT | NOT_
    ;

booleanPrimary
    : booleanPrimary IS NOT? (TRUE | FALSE | UNKNOWN | NULL)
    | booleanPrimary SAFE_EQ_ predicate
    | booleanPrimary MEMBER OF LP_ (expr) RP_
    | booleanPrimary comparisonOperator predicate
    | booleanPrimary comparisonOperator (ALL | ANY) subquery
    | booleanPrimary assignmentOperator predicate
    | predicate
    ;

assignmentOperator
    : EQ_ | ASSIGNMENT_
    ;

comparisonOperator
    : EQ_ | GTE_ | GT_ | LTE_ | LT_ | NEQ_
    ;

predicate
    : bitExpr NOT? IN subquery
    | bitExpr NOT? IN LP_ expr (COMMA_ expr)* RP_
    | bitExpr NOT? BETWEEN bitExpr AND predicate
    | bitExpr SOUNDS LIKE bitExpr
    | bitExpr NOT? LIKE simpleExpr (ESCAPE simpleExpr)?
    | bitExpr NOT? (REGEXP | RLIKE) bitExpr
    | bitExpr
    ;

bitExpr
    : bitExpr VERTICAL_BAR_ bitExpr
    | bitExpr AMPERSAND_ bitExpr
    | bitExpr SIGNED_LEFT_SHIFT_ bitExpr
    | bitExpr SIGNED_RIGHT_SHIFT_ bitExpr
    | bitExpr PLUS_ bitExpr
    | bitExpr MINUS_ bitExpr
    | bitExpr ASTERISK_ bitExpr
    | bitExpr SLASH_ bitExpr
    | bitExpr DIV bitExpr
    | bitExpr MOD bitExpr
    | bitExpr MOD_ bitExpr
    | bitExpr CARET_ bitExpr
    | simpleExpr
    ;

simpleExpr
    : functionCall
    | parameterMarker
    | literals
    | columnName
    | simpleExpr collateClause
    | variable
    | (PLUS_ | MINUS_ | TILDE_ | notOperator | BINARY) simpleExpr
    | ROW? LP_ expr (COMMA_ expr)* RP_
    | EXISTS? subquery
    | LBE_ identifier expr RBE_
    | path (RETURNING dataType)? onEmpty? onError?
    | matchExpression
    | caseExpression
    | intervalExpression
    ;

path
    : string_
    ;

onEmpty
    : (NULL | ERROR | DEFAULT literals) ON EMPTY
    ;

onError
    : (NULL | ERROR | DEFAULT literals) ON ERROR
    ;


functionCall
    : aggregationFunction | specialFunction | jsonFunction | regularFunction | udfFunction
    ;

udfFunction
    : functionName LP_ (expr (COMMA_ expr)*)? RP_
    ;

aggregationFunction
    : aggregationFunctionName LP_ distinct? (expr (COMMA_ expr)* | ASTERISK_)? collateClause? RP_ overClause?
    ;

jsonFunction
    : jsonTableFunction
    | jsonFunctionName LP_ (expr (COMMA_ expr)*)? RP_
    | columnName (JSON_SEPARATOR | JSON_UNQUOTED_SEPARATOR) path
    ;

jsonTableFunction
    : JSON_TABLE LP_ expr COMMA_ path jsonTableColumns RP_
    ;

jsonTableColumns
    : COLUMNS LP_ jsonTableColumn (COMMA_ jsonTableColumn)* RP_
    ;

jsonTableColumn
    : name FOR ORDINALITY
    | name dataType PATH path jsonTableColumnOnEmpty? jsonTableColumnOnError?
    | name dataType EXISTS PATH string_ path
    | NESTED PATH? path COLUMNS
    ;

jsonTableColumnOnEmpty
    : (NULL | DEFAULT string_ | ERROR) ON EMPTY
    ;

jsonTableColumnOnError
    : (NULL | DEFAULT string_ | ERROR) ON ERROR
    ;

jsonFunctionName
    : JSON_ARRAY | JSON_ARRAY_APPEND |  JSON_ARRAY_INSERT |  JSON_CONTAINS
    | JSON_CONTAINS_PATH | JSON_DEPTH | JSON_EXTRACT | JSON_INSERT | JSON_KEYS | JSON_LENGTH | JSON_MERGE | JSON_MERGE_PATCH
    | JSON_MERGE_PRESERVE | JSON_OBJECT | JSON_OVERLAPS | JSON_PRETTY | JSON_QUOTE | JSON_REMOVE | JSON_REPLACE
    | JSON_SCHEMA_VALID | JSON_SCHEMA_VALIDATION_REPORT | JSON_SEARCH | JSON_SET | JSON_STORAGE_FREE | JSON_STORAGE_SIZE
    | JSON_TYPE | JSON_UNQUOTE | JSON_VALID | JSON_VALUE | MEMBER OF
    ;

aggregationFunctionName
    : MAX | MIN | SUM | COUNT | AVG | BIT_XOR | GROUP_CONCAT
    ;

distinct
    : DISTINCT
    ;

overClause
    : OVER (windowSpecification | identifier)
    ;

windowSpecification
    : LP_ identifier? (PARTITION BY expr (COMMA_ expr)*)? orderByClause? frameClause? RP_
    ;

frameClause
    : (ROWS | RANGE) (frameStart | frameBetween)
    ;

frameStart
    : CURRENT ROW | UNBOUNDED PRECEDING | UNBOUNDED FOLLOWING | expr PRECEDING | expr FOLLOWING
    ;

frameEnd
    : frameStart
    ;

frameBetween
    : BETWEEN frameStart AND frameEnd
    ;

specialFunction
    : castFunction
    | convertFunction
    | currentUserFunction
    | charFunction
    | extractFunction
    | groupConcatFunction
    | positionFunction
    | substringFunction
    | trimFunction
    | valuesFunction
    | weightStringFunction
    | windowFunction
    | groupingFunction
    | timeStampDiffFunction
    ;

currentUserFunction
    : CURRENT_USER (LP_ RP_)?
    ;

groupingFunction
    : GROUPING LP_ expr (COMMA_ expr)* RP_
    ;

timeStampDiffFunction
    : TIMESTAMPDIFF LP_ intervalUnit COMMA_ expr COMMA_ expr RP_
    ;

groupConcatFunction
    : GROUP_CONCAT LP_ distinct? (expr (COMMA_ expr)* | ASTERISK_)? (orderByClause)? (SEPARATOR expr)? RP_
    ;

windowFunction
    : funcName = (ROW_NUMBER | RANK | DENSE_RANK | CUME_DIST | PERCENT_RANK) LP_ RP_ windowingClause
    | funcName = NTILE (simpleExpr) windowingClause
    | funcName = (LEAD | LAG) LP_ expr leadLagInfo? RP_ nullTreatment? windowingClause
    | funcName = (FIRST_VALUE | LAST_VALUE) LP_ expr RP_ nullTreatment? windowingClause
    | funcName = NTH_VALUE LP_ expr COMMA_ simpleExpr RP_ (FROM (FIRST | LAST))? nullTreatment? windowingClause
    ;

windowingClause
    : OVER (windowName=identifier | windowSpecification)
    ;

leadLagInfo
    : COMMA_ (NUMBER_ | QUESTION_) (COMMA_ expr)?
    ;

nullTreatment
    : (RESPECT | IGNORE) NULLS
    ;

castFunction
    : CAST LP_ expr AS castType ARRAY? RP_
    | CAST LP_ expr AT TIME ZONE expr AS DATETIME typeDatetimePrecision? RP_
    ;

convertFunction
    : CONVERT LP_ expr COMMA_ castType RP_
    | CONVERT LP_ expr USING charsetName RP_
    ;

castType
    : castTypeName = BINARY fieldLength?
    | castTypeName = CHAR fieldLength? charsetWithOptBinary?
    | (castTypeName = NCHAR | castTypeName = NATIONAL_CHAR) fieldLength?
    | castTypeName = (SIGNED | SIGNED_INT | SIGNED_INTEGER)
    | castTypeName = (UNSIGNED | UNSIGNED_INT | UNSIGNED_INTEGER)
    | castTypeName = DATE
    | castTypeName = TIME typeDatetimePrecision?
    | castTypeName = DATETIME typeDatetimePrecision?
    | castTypeName = DECIMAL (fieldLength | precision)?
    | castTypeName = JSON
    | castTypeName = REAL
    | castTypeName = DOUBLE PRECISION
    | castTypeName = FLOAT precision?
    ;

positionFunction
    : POSITION LP_ expr IN expr RP_
    ;

substringFunction
    : (SUBSTRING | SUBSTR | MID) LP_ expr FROM NUMBER_ (FOR NUMBER_)? RP_
    | (SUBSTRING | SUBSTR | MID) LP_ expr COMMA_ NUMBER_ (COMMA_ NUMBER_)? RP_
    ;

extractFunction
    : EXTRACT LP_ intervalUnit FROM expr RP_
    ;

charFunction
    : CHAR LP_ expr (COMMA_ expr)* (USING charsetName)? RP_
    ;

trimFunction
    : TRIM LP_ ((LEADING | BOTH | TRAILING) expr? FROM)? expr RP_
    | TRIM LP_ (expr FROM)? expr RP_
    ;

valuesFunction
    : VALUES LP_ columnNames RP_
    ;

weightStringFunction
    : WEIGHT_STRING LP_ expr (AS dataType)? levelClause? RP_
    ;

levelClause
    : LEVEL (levelInWeightListElement (COMMA_ levelInWeightListElement)* | NUMBER_ MINUS_ NUMBER_)
    ;

levelInWeightListElement
    : NUMBER_ direction? REVERSE?
    ;

regularFunction
    : completeRegularFunction
    | shorthandRegularFunction
    ;

shorthandRegularFunction
    : CURRENT_DATE | CURRENT_TIME (LP_ NUMBER_? RP_)? | CURRENT_TIMESTAMP | LAST_DAY | LOCALTIME | LOCALTIMESTAMP
    ;

completeRegularFunction
    : regularFunctionName (LP_ (expr (COMMA_ expr)* | ASTERISK_)? RP_)
    ;

regularFunctionName
    : IF | LOCALTIME | LOCALTIMESTAMP | REPLACE | INSERT | INTERVAL | MOD
    | DATABASE | SCHEMA | LEFT | RIGHT | DATE | DAY | GEOMETRYCOLLECTION | REPEAT
    | LINESTRING | MULTILINESTRING | MULTIPOINT | MULTIPOLYGON | POINT | POLYGON
    | TIME | TIMESTAMP | TIMESTAMP_ADD | TIMESTAMP_DIFF | DATE | CURRENT_TIMESTAMP
    | CURRENT_DATE | CURRENT_TIME | UTC_TIMESTAMP// | functionName
    ;

matchExpression
    : MATCH (columnNames | LP_ columnNames RP_ ) AGAINST LP_ expr matchSearchModifier? RP_
    ;

matchSearchModifier
    : IN NATURAL LANGUAGE MODE | IN NATURAL LANGUAGE MODE WITH QUERY EXPANSION | IN BOOLEAN MODE | WITH QUERY EXPANSION
    ;

caseExpression
    : CASE expr? caseWhen+ caseElse? END
    ;

datetimeExpr
    : expr
    ;

binaryLogFileIndexNumber
    : NUMBER_
    ;

caseWhen
    : WHEN expr THEN expr
    ;

caseElse
    : ELSE expr
    ;

intervalExpression
    : INTERVAL intervalValue
    ;

intervalValue
    : expr intervalUnit
    ;

intervalUnit
    : MICROSECOND | SECOND | MINUTE | HOUR | DAY | WEEK | MONTH
    | QUARTER | YEAR | SECOND_MICROSECOND | MINUTE_MICROSECOND | MINUTE_SECOND | HOUR_MICROSECOND | HOUR_SECOND
    | HOUR_MINUTE | DAY_MICROSECOND | DAY_SECOND | DAY_MINUTE | DAY_HOUR | YEAR_MONTH
    ;

orderByClause
    : ORDER BY orderByItem (COMMA_ orderByItem)*
    ;

orderByItem
    : (numberLiterals | expr) direction?
    ;

dataType
    : dataTypeName = (INTEGER | INT | TINYINT | SMALLINT | MIDDLEINT | MEDIUMINT | BIGINT) fieldLength? fieldOptions?
    | (dataTypeName = REAL | dataTypeName = DOUBLE PRECISION?) precision? fieldOptions?
    | dataTypeName = (FLOAT | DECIMAL | DEC | NUMERIC | FIXED) (fieldLength | precision)? fieldOptions?
    | dataTypeName = BIT fieldLength?
    | dataTypeName = (BOOL | BOOLEAN)
    | dataTypeName = CHAR fieldLength? charsetWithOptBinary?
    | (dataTypeName = NCHAR | dataTypeName = NATIONAL_CHAR) fieldLength? BINARY?
    | dataTypeName = (SIGNED | SIGNED_INT | SIGNED_INTEGER)
    | dataTypeName = BINARY fieldLength?
    | (dataTypeName = CHAR_VARYING | dataTypeName = CHARACTER_VARYING | dataTypeName = VARCHAR) fieldLength charsetWithOptBinary?
    | (dataTypeName = NATIONAL VARCHAR | dataTypeName = NVARCHAR | dataTypeName = NCHAR VARCHAR | dataTypeName = NATIONAL_CHAR_VARYING | dataTypeName = NCHAR VARYING) fieldLength BINARY?
    | dataTypeName = VARBINARY fieldLength?
    | dataTypeName = YEAR fieldLength? fieldOptions?
    | dataTypeName = DATE
    | dataTypeName = TIME typeDatetimePrecision?
    | dataTypeName = (UNSIGNED | UNSIGNED_INT | UNSIGNED_INTEGER)
    | dataTypeName = TIMESTAMP typeDatetimePrecision?
    | dataTypeName = DATETIME typeDatetimePrecision?
    | dataTypeName = TINYBLOB
    | dataTypeName = BLOB fieldLength?
    | dataTypeName = (MEDIUMBLOB | LONGBLOB)
    | dataTypeName = LONG VARBINARY
    | dataTypeName = (LONG_CHAR_VARYING | LONG_VARCHAR) charsetWithOptBinary?
    | dataTypeName = TINYTEXT charsetWithOptBinary?
    | dataTypeName = TEXT fieldLength? charsetWithOptBinary?
    | dataTypeName = MEDIUMTEXT charsetWithOptBinary?
    | dataTypeName = LONGTEXT charsetWithOptBinary?
    | dataTypeName = ENUM stringList charsetWithOptBinary?
    | dataTypeName = SET stringList charsetWithOptBinary?
    | dataTypeName = (SERIAL | JSON | GEOMETRY | GEOMCOLLECTION | GEOMETRYCOLLECTION | POINT | MULTIPOINT | LINESTRING | MULTILINESTRING | POLYGON | MULTIPOLYGON)
    ;

stringList
    : LP_ textString (COMMA_ textString)* RP_
    ;

textString
    : string_
    | HEX_DIGIT_
    | BIT_NUM_
    ;

textStringHash
    : string_ | HEX_DIGIT_
    ;

fieldOptions
    : (UNSIGNED | SIGNED | ZEROFILL)+
    ;

precision
    : LP_ NUMBER_ (COMMA_ NUMBER_)? RP_
    ;

typeDatetimePrecision
    : LP_ NUMBER_ RP_
    ;

charsetWithOptBinary
    : ascii
    | unicode
    | BYTE
    | charset charsetName BINARY? (COLLATE collationName DETERMINISTIC?)?
    | BINARY (charset charsetName)?
    ;

ascii
    : ASCII BINARY?
    | BINARY ASCII
    ;

unicode
    : UNICODE BINARY?
    | BINARY UNICODE
    ;

charset
    : (CHAR | CHARACTER) SET
    | CHARSET
    ;

defaultCollation
    : DEFAULT? COLLATE EQ_? collationName
    ;

defaultEncryption
    : DEFAULT? ENCRYPTION EQ_? string_
    ;

defaultCharset
    : DEFAULT? charset EQ_? charsetName
    ;

now
    : ( NOW | CURRENT_TIMESTAMP | LOCALTIME | LOCALTIMESTAMP) (LP_ NUMBER_? RP_)?
    ;

direction
    : ASC | DESC
    | WITHOUT OVERLAPS // mariadb 非语法树，example中sql兼容 ？
    ;

keyOrIndex
    : KEY | INDEX
    ;

fieldLength
    : LP_ length=NUMBER_ RP_
    ;

collateClause
    : COLLATE (collationName | parameterMarker)
    ;

fieldOrVarSpec
    : LP_ (userVariable (COMMA_ userVariable)*)? RP_
    ;

ifNotExists
    : IF NOT EXISTS
    ;

ifExists
    : IF EXISTS
    ;

connectionId
    : NUMBER_
    ;

labelName
    : identifier
    ;

cursorName
    : identifier
    ;

conditionName
    : identifier
    ;

channelOption
    : FOR CHANNEL string_
    ;
catalogName
    : (owner DOT_)? identifier
    ;
packageName
    : (owner DOT_)? identifier
    ;
userExceptionName
    : (owner DOT_)? identifier
    ;
