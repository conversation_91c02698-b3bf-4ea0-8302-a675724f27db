CREATE USER foo2@test IDENTIFIED VIA pam;
CREATE USER foo2@test IDENTIFIED VIA pam USING 'mariadb';
CREATE OR REPLACE USER foo2@test IDENTIFIED BY 'password';
CREATE USER IF NOT EXISTS foo2@test IDENTIFIED BY 'password';
CREATE USER foo2@test IDENTIFIED BY 'mariadb';
CREATE USER foo2@test IDENTIFIED BY PASSWORD '*54958E764CE10E50764C2EECBB71D01F08549980';
CREATE USER foo2@test IDENTIFIED VIA pam;
CREATE USER foo2@test IDENTIFIED VIA pam USING 'mariadb';
CREATE USER safe@'%' IDENTIFIED VIA ed25519 USING PASSWORD('secret');
CREATE USER safe@'%' IDENTIFIED VIA ed25519 USING PASSWORD('secret') OR unix_socket;
CREATE USER 'someone'@'localhost' WITH
    MAX_USER_CONNECTIONS 10
    MAX_QUERIES_PER_HOUR 200;
CREATE USER 'maria'@'*************/*************';
CREATE USER ''@'***********';
ALTER USER CURRENT_USER() IDENTIFIED BY 'mariadb';
ALTER USER 'bob'@'localhost' IDENTIFIED VIA mysql_native_password
  USING PASSWORD('pwd2');
ALTER USER foo2@test IDENTIFIED BY 'mariadb';
ALTER USER foo2@test
  IDENTIFIED BY PASSWORD '*54958E764CE10E50764C2EECBB71D01F08549980';
ALTER USER foo2@test IDENTIFIED VIA pam;
ALTER USER foo2@test IDENTIFIED VIA pam USING 'mariadb';
ALTER USER safe@'%' IDENTIFIED VIA ed25519 USING PASSWORD('secret');
ALTER USER 'alice'@'%'
 REQUIRE SUBJECT '/CN=alice/O=My Dom, Inc./C=US/ST=Oregon/L=Portland' AND
 ISSUER '/C=FI/ST=Somewhere/L=City/ O=Some Company/CN=Peter Parker/emailAddress=<EMAIL>'
 AND CIPHER 'SHA-DES-CBC3-EDH-RSA';
ALTER USER 'someone'@'localhost' WITH
    MAX_USER_CONNECTIONS 10
    MAX_QUERIES_PER_HOUR 200;
ALTER USER 'monty'@'localhost' PASSWORD EXPIRE INTERVAL 120 DAY;
ALTER USER 'monty'@'localhost' PASSWORD EXPIRE NEVER;
ALTER USER 'monty'@'localhost' PASSWORD EXPIRE DEFAULT;
ALTER USER 'marijn'@'localhost' ACCOUNT LOCK;
DROP USER foo2@localhost,foo2@'127.%';
DROP USER IF EXISTS bob;
GRANT USAGE ON *.* TO 'user123'@'%'
  IDENTIFIED VIA PAM using 'mariadb' require ssl;
GRANT USAGE ON *.* TO 'user123'@'%' IDENTIFIED BY '';
GRANT ALL PRIVILEGES ON *.* TO 'dba'@'%' ;
GRANT PROXY ON 'dba'@'%' TO ''@'%';
GRANT EXECUTE ON PROCEDURE mysql.create_db TO maintainer;
GRANT PROXY ON 'dba'@'localhost' TO 'bob'@'localhost';
GRANT ALL PRIVILEGES ON *.* TO 'alice'@'localhost' IDENTIFIED BY PASSWORD '*2470C0C06DEE42FD1618BB99005ADCA2EC9D1E19';
GRANT PROXY ON 'dba'@'localhost' TO 'bob'@'localhost';
GRANT ALL PRIVILEGES ON *.* TO 'alice'@'localhost' IDENTIFIED BY PASSWORD '*2470C0C06DEE42FD1618BB99005ADCA2EC9D1E19' WITH GRANT OPTION;
GRANT USAGE ON *.* TO foo2@test IDENTIFIED BY 'mariadb';
GRANT USAGE ON *.* TO foo2@test IDENTIFIED BY
  PASSWORD '*54958E764CE10E50764C2EECBB71D01F08549980';
GRANT USAGE ON *.* TO foo2@test IDENTIFIED VIA pam;
GRANT USAGE ON *.* TO foo2@test IDENTIFIED VIA pam USING 'mariadb';
CREATE USER safe@'%' IDENTIFIED VIA ed25519
  USING PASSWORD('secret');
CREATE USER safe@'%' IDENTIFIED VIA ed25519
  USING PASSWORD('secret') OR unix_socket;
GRANT USAGE ON *.* TO 'someone'@'localhost' WITH
    MAX_USER_CONNECTIONS 0
    MAX_QUERIES_PER_HOUR 200;
GRANT USAGE ON *.* TO 'alice'@'%'
  REQUIRE SUBJECT '/CN=alice/O=My Dom, Inc./C=US/ST=Oregon/L=Portland'
  AND ISSUER '/C=FI/ST=Somewhere/L=City/ O=Some Company/CN=Peter Parker/emailAddress=<EMAIL>'
  AND CIPHER 'SHA-DES-CBC3-EDH-RSA';
GRANT journalist TO berengar WITH ADMIN OPTION;
GRANT ALL PRIVILEGES ON  *.* to 'alexander'@'localhost' WITH GRANT OPTION;
RENAME USER 'donald' TO 'duck'@'localhost', 'mickey' TO 'mouse'@'localhost';
RENAME USER 'foo'@'*******' TO 'foo'@'***********';
REVOKE SUPER ON *.* FROM 'alexander'@'localhost';
SET PASSWORD FOR 'bob'@'%.loc.gov' = PASSWORD('newpass');
SET PASSWORD FOR 'bob'@localhost = PASSWORD("");
CREATE ROLE developer WITH ADMIN lorinda@localhost;
CREATE ROLE journalist;
CREATE OR REPLACE ROLE journalist;
CREATE ROLE IF NOT EXISTS journalist;
DROP ROLE journalist;
DROP ROLE IF EXISTS journalist;
SET ROLE staff;
SET ROLE NONE;
SET DEFAULT ROLE journalist;
SET DEFAULT ROLE NONE;
SET DEFAULT ROLE journalist FOR taniel;
SET DEFAULT ROLE NONE FOR taniel;
SHOW GRANTS;
SHOW GRANTS FOR CURRENT_USER;
SHOW GRANTS FOR CURRENT_USER();
SHOW GRANTS FOR public;
SHOW CREATE USER 'monty'@'localhost';
SHOW CREATE USER foo4@test\G;
ALTER TABLE t1
ALTER INDEX index_name NOT INVISIBLE;
ALTER TABLE t1 ALTER b SET DEFAULT 'hello';
ALTER TABLE x DROP COLUMN a;
ALTER TABLE t1 MODIFY a BIGINT UNSIGNED AUTO_INCREMENT;
ALTER TABLE t1 CHANGE a b BIGINT UNSIGNED AUTO_INCREMENT;
ALTER TABLE t1 ALTER b SET DEFAULT 'hello';
ALTER TABLE t1 RENAME COLUMN c_old TO c_new;
ALTER TABLE t1 RENAME INDEX i_old TO i_new;
ALTER TABLE t_order ADD column6 TEXT;
ALTER TABLE t_order ADD column7 TINYTEXT;
ALTER TABLE t_order ADD column8 MEDIUMTEXT;
ALTER TABLE t_order ADD column9 LONGTEXT;
ALTER TABLE account_ledger
    ADD CONSTRAINT is_balanced
        CHECK((debit_amount + credit_amount) = 0);
ALTER TABLE table_name
DROP CONSTRAINT constraint_name;
ALTER TABLE t DROP CONSTRAINT is_unique;
ALTER TABLE tab_name ENGINE = InnoDB;
ALTER TABLE tab_name FORCE;
ALTER TABLE x FORCE;
ALTER TABLE t1
    ENGINE = InnoDB
    COMMENT = 'First of three tables containing usage info';
ALTER TABLE t1 DROP x, ADD x2 INT,  CHANGE y y2 INT;
ALTER TABLE t1 CHANGE a b bigint unsigned auto_increment;
ALTER TABLE t1 MODIFY x bigint unsigned;
ALTER TABLE rooms DROP INDEX u;
ALTER TABLE rooms ADD UNIQUE INDEX u(room_number);
ALTER TABLE rooms ADD PRIMARY KEY(room_number, p WITHOUT OVERLAPS);
ALTER USER foo2@test IDENTIFIED BY 'mariadb';
ALTER SERVER s OPTIONS (USER 'sally');
ALTER USER 'monty'@'localhost' PASSWORD EXPIRE INTERVAL 120 DAY;
ALTER USER 'monty'@'localhost' PASSWORD EXPIRE NEVER;
ALTER USER 'monty'@'localhost' PASSWORD EXPIRE DEFAULT;

ALTER USER 'alice'@'%'
 REQUIRE SUBJECT '/CN=alice/O=My Dom, Inc./C=US/ST=Oregon/L=Portland' AND
 ISSUER '/C=FI/ST=Somewhere/L=City/ O=Some Company/CN=Peter Parker/emailAddress=<EMAIL>'
 AND CIPHER 'SHA-DES-CBC3-EDH-RSA';
ALTER USER safe@'%' IDENTIFIED VIA ed25519 USING PASSWORD('secret');
ALTER USER foo2@test IDENTIFIED VIA pam USING 'mariadb';
ALTER USER foo2@test IDENTIFIED VIA pam;
ALTER USER foo2@test
  IDENTIFIED BY PASSWORD '*54958E764CE10E50764C2EECBB71D01F08549980';
ALTER USER CURRENT_USER() IDENTIFIED BY 'mariadb';
ALTER VIEW v AS SELECT a, a*3 AS a2 FROM t;

ANALYZE TABLE tbl PERSISTENT FOR ALL;
ANALYZE TABLE tbl PERSISTENT FOR COLUMNS (col1,col2) INDEXES (idx1,idx2);
ANALYZE TABLE tbl PERSISTENT FOR COLUMNS (col1,col2) INDEXES ();
ANALYZE TABLE tbl PERSISTENT FOR COLUMNS () INDEXES ();
ANALYZE TABLE tbl;
CHECK TABLE y EXTENDED;
CHECK TABLE t1 EXTENDED;
CHECK TABLE t2 EXTENDED;
CREATE TABLE v2 LIKE v;
CREATE OR REPLACE TABLE x (d DATE DEFAULT '0000-00-00');
CREATE OR REPLACE TABLE y LIKE x;
create or replace table t1 (x int)
  partition by range(x) (
    partition p1 values less than (10),
    partition p2 values less than (20),
    partition p3 values less than (30),
    partition p4 values less than (40),
    partition p5 values less than (50),
    partition pn values less than maxvalue);
create or replace table t1 (x int)
  partition by range(x) (
    p1 values less than (10),
    p2 values less than (20),
    p3 values less than (30),
    p4 values less than (40),
    p5 values less than (50),
    pn values less than maxvalue);
create table if not exists test (
    a bigint auto_increment primary key,
    name varchar(128) charset utf8,
    key name (name(32))
    ) engine=InnoDB default charset latin1;
CREATE TABLE t1(    --  MariaDB 10.2.1 only.
   a int DEFAULT (1+1),
   b int DEFAULT (a+1),
   expires DATETIME DEFAULT(NOW() + INTERVAL 1 YEAR),
   x BLOB DEFAULT USER()
);

RENAME TABLE t1 TO tmp_table,
    t2 TO t1,
    tmp_table TO t2;
RENAME TABLE db1.t TO db2.t;

PREPARE stmt FROM "REPLACE INTO t2 SET id2=3, animal2='Fox' RETURNING f2(id2),
UPPER(animal2)";
SHOW COLUMNS FROM mytable FROM mydb;
SHOW COLUMNS FROM mydb.mytable;
SHOW COLUMNS FROM employees WHERE Type LIKE 'Varchar%';
SHOW CREATE TABLE t\G;
SHOW INDEXES FROM employees_example\G;
SHOW EXPLAIN FOR 1;
CACHE INDEX t1, t2, t3 IN hot_cache;
DESCRIBE city;
CREATE VIEW v AS SELECT a, a*2 AS a2 FROM t;
CREATE OR REPLACE VIEW v AS SELECT a, a*2 AS a2 FROM t;
CREATE VIEW IF NOT EXISTS v AS SELECT a, a*2 AS a2 FROM t;
create catalog cat1;
CREATE DEFINER=`root`@`localhost` TRIGGER increment_animal
  AFTER INSERT ON animals FOR EACH ROW
UPDATE animal_count SET animal_count.animals = animal_count.animals+1;
CREATE OR REPLACE DEFINER=`root`@`localhost` TRIGGER increment_animal
  AFTER INSERT ON animals  FOR EACH ROW
UPDATE animal_count SET animal_count.animals = animal_count.animals+1;
CREATE DEFINER=`root`@`localhost` TRIGGER IF NOT EXISTS increment_animal
  AFTER INSERT ON animals FOR EACH ROW
UPDATE animal_count SET animal_count.animals = animal_count.animals+1;
CREATE EVENT myevent
    ON SCHEDULE AT CURRENT_TIMESTAMP + INTERVAL 1 HOUR
    DO
UPDATE myschema.mytable SET mycol = mycol + 1;
CREATE OR REPLACE EVENT myevent
    ON SCHEDULE AT CURRENT_TIMESTAMP + INTERVAL 1 HOUR
    DO
UPDATE myschema.mytable SET mycol = mycol + 1;
CREATE EVENT IF NOT EXISTS myevent
    ON SCHEDULE AT CURRENT_TIMESTAMP + INTERVAL 1 HOUR
    DO
UPDATE myschema.mytable SET mycol = mycol + 1;
CREATE FUNCTION hello (s CHAR(20))
    RETURNS CHAR(50) DETERMINISTIC
    RETURN CONCAT('Hello, ',s,'!');
CREATE FUNCTION hello2 (s CHAR(20))
    RETURNS CHAR(50) CHARACTER SET 'utf8' COLLATE 'utf8_bin' DETERMINISTIC
  RETURN CONCAT('Hello, ',s,'!');
CREATE FUNCTION IF NOT EXISTS jsoncontains_path RETURNS integer SONAME 'ha_connect.so';
CREATE OR REPLACE FUNCTION jsoncontains_path RETURNS integer SONAME 'ha_connect.so';

CREATE UNIQUE INDEX HomePhone ON Employees(Home_Phone);
CREATE INDEX xi ON xx5 (x);
CREATE OR REPLACE INDEX xi ON xx5 (x);
CREATE INDEX IF NOT EXISTS xi ON xx5 (x);
CREATE UNIQUE INDEX u ON rooms (room_number, p WITHOUT OVERLAPS);

CREATE OR REPLACE PACKAGE employee_tools AS
  FUNCTION getSalary(eid INT) RETURN DECIMAL(10,2);
  PROCEDURE raiseSalary(eid INT, amount DECIMAL(10,2));
  PROCEDURE raiseSalaryStd(eid INT);
  PROCEDURE hire(ename TEXT, esalary DECIMAL(10,2));
END;

CREATE OR REPLACE PACKAGE pkg
  PROCEDURE p1();
  FUNCTION f1() RETURNS INT;
END;

CREATE OR REPLACE PACKAGE employee_tools AS
  FUNCTION getSalary(eid INT) RETURN DECIMAL(10,2);
  PROCEDURE raiseSalary(eid INT, amount DECIMAL(10,2));
  PROCEDURE raiseSalaryStd(eid INT);
  PROCEDURE hire(ename TEXT, esalary DECIMAL(10,2));
END;

CREATE PACKAGE BODY employee_tools AS
  -- package body variables
  stdRaiseAmount DECIMAL(10,2):=500;

  -- private routines
  PROCEDURE log (eid INT, ecmnt TEXT) AS
BEGIN
INSERT INTO employee_log (id, cmnt) VALUES (eid, ecmnt);
END;

  -- public routines
  PROCEDURE hire(ename TEXT, esalary DECIMAL(10,2)) AS
    eid INT;
BEGIN
INSERT INTO employee (name, salary) VALUES (ename, esalary);
eid:= last_insert_id();
    log(eid, 'hire ' || ename);
END;

  FUNCTION getSalary(eid INT) RETURN DECIMAL(10,2) AS
    nSalary DECIMAL(10,2);
BEGIN
SELECT salary INTO nSalary FROM employee WHERE id=eid;
log(eid, 'getSalary id=' || eid || ' salary=' || nSalary);
RETURN nSalary;
END;

  PROCEDURE raiseSalary(eid INT, amount DECIMAL(10,2)) AS
BEGIN
UPDATE employee SET salary=salary+amount WHERE id=eid;
log(eid, 'raiseSalary id=' || eid || ' amount=' || amount);
END;

  PROCEDURE raiseSalaryStd(eid INT) AS
BEGIN
    raiseSalary(eid, stdRaiseAmount);
    log(eid, 'raiseSalaryStd id=' || eid);
END;

BEGIN
  -- This code is executed when the current session
  -- accesses any of the package routines for the first time
  log(0, 'Session ' || connection_id() || ' ' || current_user || ' started');
END;
