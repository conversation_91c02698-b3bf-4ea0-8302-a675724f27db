/*
 T-SQL (Transact-SQL, MSSQL) grammar.
 */

// $antlr-format alignTrailingComments true, columnLimit 150, maxEmptyLinesToKeep 1, reflowComments false, useTab false
// $antlr-format allowShortRulesOnASingleLine true, allowShortBlocksOnASingleLine true, minEmptyLines 0, alignSemicolons ownLine
// $antlr-format alignColons trailing, singleLineOverrulesHangingColon true, alignLexerCommands true, alignLabels true, alignTrailers true

lexer grammar SqlServerLexer;

// Basic keywords (from https://msdn.microsoft.com/en-us/library/ms189822.aspx)

options {
    caseInsensitive = true;
}

ABORT                                         : 'ABORT';
ABORT_AFTER_WAIT                              : 'ABORT_AFTER_WAIT';
ABSENT                                        : 'ABSENT';
ABSOLUTE                                      : 'ABSOLUTE';
ACCELERATED_DATABASE_RECOVERY                 : 'ACCELERATED_DATABASE_RECOVERY';
ACCELERATED_PLAN_FORCING                      : 'ACCELERATED_PLAN_FORCING';
ACCENT_SENSITIVITY                            : 'ACCENT_SENSITIVITY';
ACCESS                                        : 'ACCESS';
ACTION                                        : 'ACTION';
ACTIVATION                                    : 'ACTIVATION';
ACTIVE                                        : 'ACTIVE';
ADD                                           : 'ADD';
ADDRESS                                       : 'ADDRESS';
ADMINISTER                                    : 'ADMINISTER';
AES                                           : 'AES';
AES_128                                       : 'AES_128';
AES_192                                       : 'AES_192';
AES_256                                       : 'AES_256';
AFFINITY                                      : 'AFFINITY';
AFTER                                         : 'AFTER';
AGGREGATE                                     : 'AGGREGATE';
ALGORITHM                                     : 'ALGORITHM';
ALL                                           : 'ALL';
ALL_SPARSE_COLUMNS                            : 'ALL_SPARSE_COLUMNS';
ALLOW_CONNECTIONS                             : 'ALLOW_CONNECTIONS';
ALLOW_ENCRYPTED_VALUE_MODIFICATIONS           : 'ALLOW_ENCRYPTED_VALUE_MODIFICATIONS';
ALLOW_MULTIPLE_EVENT_LOSS                     : 'ALLOW_MULTIPLE_EVENT_LOSS';
ALLOW_PAGE_LOCKS                              : 'ALLOW_PAGE_LOCKS';
ALLOW_ROW_LOCKS                               : 'ALLOW_ROW_LOCKS';
ALLOW_SINGLE_EVENT_LOSS                       : 'ALLOW_SINGLE_EVENT_LOSS';
ALLOW_SNAPSHOT_ISOLATION                      : 'ALLOW_SNAPSHOT_ISOLATION';
ALLOWED                                       : 'ALLOWED';
ALTER                                         : 'ALTER';
ALWAYS                                        : 'ALWAYS';
AND                                           : 'AND';
ANONYMOUS                                     : 'ANONYMOUS';
ANSI_DEFAULTS                                 : 'ANSI_DEFAULTS';
ANSI_NULL_DEFAULT                             : 'ANSI_NULL_DEFAULT';
ANSI_NULL_DFLT_OFF                            : 'ANSI_NULL_DFLT_OFF';
ANSI_NULL_DFLT_ON                             : 'ANSI_NULL_DFLT_ON';
ANSI_NULLS                                    : 'ANSI_NULLS';
ANSI_PADDING                                  : 'ANSI_PADDING';
ANSI_WARNINGS                                 : 'ANSI_WARNINGS';
ANY                                           : 'ANY';
APPEND                                        : 'APPEND';
APPLICATION                                   : 'APPLICATION';
APPLICATION_LOG                               : 'APPLICATION_LOG';
APPLY                                         : 'APPLY';
APPROX_PERCENTILE_CONT                        : 'APPROX_PERCENTILE_CONT';
APPROX_PERCENTILE_DISC                        : 'APPROX_PERCENTILE_DISC';
ARITHABORT                                    : 'ARITHABORT';
ARITHIGNORE                                   : 'ARITHIGNORE';
ARRAY                                         : 'ARRAY';
AS                                            : 'AS';
ASC                                           : 'ASC';
ASSEMBLY                                      : 'ASSEMBLY';
ASYMMETRIC                                    : 'ASYMMETRIC';
ASYNC_STATS_UPDATE_WAIT_AT_LOW_PRIORITY       : 'ASYNC_STATS_UPDATE_WAIT_AT_LOW_PRIORITY';
ASYNCHRONOUS_COMMIT                           : 'ASYNCHRONOUS_COMMIT';
AT                                            : 'AT';
ATOMIC                                        : 'ATOMIC';
AUDIT                                         : 'AUDIT';
AUDIT_GUID                                    : 'AUDIT_GUID';
AUTHENTICATE                                  : 'AUTHENTICATE';
AUTHENTICATION                                : 'AUTHENTICATION';
AUTHORIZATION                                 : 'AUTHORIZATION';
AUTO                                          : 'AUTO';
AUTO_CLEANUP                                  : 'AUTO_CLEANUP';
AUTO_CLOSE                                    : 'AUTO_CLOSE';
AUTO_CREATE_STATISTICS                        : 'AUTO_CREATE_STATISTICS';
AUTO_DROP                                     : 'AUTO_DROP';
AUTO_SHRINK                                   : 'AUTO_SHRINK';
AUTO_UPDATE_STATISTICS                        : 'AUTO_UPDATE_STATISTICS';
AUTO_UPDATE_STATISTICS_ASYNC                  : 'AUTO_UPDATE_STATISTICS_ASYNC';
AUTOGROW_ALL_FILES                            : 'AUTOGROW_ALL_FILES';
AUTOGROW_SINGLE_FILE                          : 'AUTOGROW_SINGLE_FILE';
AUTOMATED_BACKUP_PREFERENCE                   : 'AUTOMATED_BACKUP_PREFERENCE';
AUTOMATIC                                     : 'AUTOMATIC';
AUTOMATIC_TUNING                              : 'AUTOMATIC_TUNING';
AVAILABILITY                                  : 'AVAILABILITY';
AVAILABILITY_MODE                             : 'AVAILABILITY_MODE';
AVG                                           : 'AVG';
BACKUP                                        : 'BACKUP';
BACKUP_PRIORITY                               : 'BACKUP_PRIORITY';
BACKUP_STORAGE_REDUNDANCY                     : 'BACKUP_STORAGE_REDUNDANCY';
BASE64                                        : 'BASE64';
BASIC                                         : 'BASIC';
BATCH_MODE_ADAPTIVE_JOINS                     : 'BATCH_MODE_ADAPTIVE_JOINS';
BATCH_MODE_MEMORY_GRANT_FEEDBACK              : 'BATCH_MODE_MEMORY_GRANT_FEEDBACK';
BATCH_MODE_ON_ROWSTORE                        : 'BATCH_MODE_ON_ROWSTORE';
BATCH_SIZE                                    : 'BATCH_SIZE';
BEFORE                                        : 'BEFORE';
BEGIN                                         : 'BEGIN';
BEGIN_DIALOG                                  : 'BEGIN_DIALOG';
BETWEEN                                       : 'BETWEEN';
BIGINT                                        : 'BIGINT';
BINARY                                        : 'BINARY';
BINDING                                       : 'BINDING';
BIT                                           : 'BIT';
BLOCK                                         : 'BLOCK';
BLOCKERS                                      : 'BLOCKERS';
BLOCKSIZE                                     : 'BLOCKSIZE';
BOOLEAN                                       : 'BOOLEAN';
BOTH                                          : 'BOTH';
BREAK                                         : 'BREAK';
BROKER                                        : 'BROKER';
BROKER_INSTANCE                               : 'BROKER_INSTANCE';
BROWSE                                        : 'BROWSE';
BUCKET_COUNT                                  : 'BUCKET_COUNT';
BUFFER                                        : 'BUFFER';
BUFFERCOUNT                                   : 'BUFFERCOUNT';
BULK                                          : 'BULK';
BULK_LOGGED                                   : 'BULK_LOGGED';
BY                                            : 'BY';
CACHE                                         : 'CACHE';
CALL                                          : 'CALL';
CALLED                                        : 'CALLED';
CALLER                                        : 'CALLER';
CASCADE                                       : 'CASCADE';
CASCADED                                      : 'CASCADED';
CASE                                          : 'CASE';
CAST                                          : 'CAST';
CATALOG                                       : 'CATALOG';
CATCH                                         : 'CATCH';
CE_FEEDBACK                                   : 'CE_FEEDBACK';
CERTIFICATE                                   : 'CERTIFICATE';
CHANGE                                        : 'CHANGE';
CHANGE_RETENTION                              : 'CHANGE_RETENTION';
CHANGE_TRACKING                               : 'CHANGE_TRACKING';
CHAR                                          : 'CHAR';
CHARACTER                                     : 'CHARACTER';
CHECK                                         : 'CHECK';
CHECK_EXPIRATION                              : 'CHECK_EXPIRATION';
CHECK_POLICY                                  : 'CHECK_POLICY';
CHECKPOINT                                    : 'CHECKPOINT';
CHECKSUM                                      : 'CHECKSUM';
CLASSIFIER_FUNCTION                           : 'CLASSIFIER_FUNCTION';
CLEANUP                                       : 'CLEANUP';
CLEANUP_POLICY                                : 'CLEANUP_POLICY';
CLEAR                                         : 'CLEAR';
CLOSE                                         : 'CLOSE';
CLUSTER                                       : 'CLUSTER';
CLUSTER_TYPE                                  : 'CLUSTER_TYPE';
CLUSTERED                                     : 'CLUSTERED';
COLLATE                                       : 'COLLATE';
COLLATION                                     : 'COLLATION';
COLLECTION                                    : 'COLLECTION';
COLUMN                                        : 'COLUMN';
COLUMN_ENCRYPTION_KEY                         : 'COLUMN_ENCRYPTION_KEY';
COLUMN_MASTER_KEY                             : 'COLUMN_MASTER_KEY';
COLUMN_SET                                    : 'COLUMN_SET';
COLUMNS                                       : 'COLUMNS';
COLUMNSTORE                                   : 'COLUMNSTORE';
COLUMNSTORE_ARCHIVE                           : 'COLUMNSTORE_ARCHIVE';
COMMIT                                        : 'COMMIT';
COMMITTED                                     : 'COMMITTED';
COMPATIBILITY_LEVEL                           : 'COMPATIBILITY_LEVEL';
COMPRESS_ALL_ROW_GROUPS                       : 'COMPRESS_ALL_ROW_GROUPS';
COMPRESSION                                   : 'COMPRESSION';
COMPRESSION_DELAY                             : 'COMPRESSION_DELAY';
CONCAT                                        : 'CONCAT';
CONCAT_NULL_YIELDS_NULL                       : 'CONCAT_NULL_YIELDS_NULL';
CONFIGURATION                                 : 'CONFIGURATION';
CONFIGURATION_ONLY                            : 'CONFIGURATION_ONLY';
CONNECT                                       : 'CONNECT';
CONNECTION                                    : 'CONNECTION';
CONNECTION_OPTIONS                            : 'CONNECTION_OPTIONS';
CONSTRAINT                                    : 'CONSTRAINT';
CONTAINED                                     : 'CONTAINED';
CONTAINMENT                                   : 'CONTAINMENT';
CONTAINS                                      : 'CONTAINS';
CONTENT                                       : 'CONTENT';
CONTEXT                                       : 'CONTEXT';
CONTINUE                                      : 'CONTINUE';
CONTINUE_AFTER_ERROR                          : 'CONTINUE_AFTER_ERROR';
CONTRACT                                      : 'CONTRACT';
CONTRACT_NAME                                 : 'CONTRACT_NAME';
CONTROL                                       : 'CONTROL';
CONVERSATION                                  : 'CONVERSATION';
CONVERT                                       : 'CONVERT';
COOKIE                                        : 'COOKIE';
COPY_ONLY                                     : 'COPY_ONLY';
COUNT                                         : 'COUNT';
CPU                                           : 'CPU';
CREATE                                        : 'CREATE';
CREATE_NEW                                    : 'CREATE_NEW';
CREATION_DISPOSITION                          : 'CREATION_DISPOSITION';
CREDENTIAL                                    : 'CREDENTIAL';
CROSS                                         : 'CROSS';
CRYPTOGRAPHIC                                 : 'CRYPTOGRAPHIC';
CURRENT                                       : 'CURRENT';
CURRENT_USER                                  : 'CURRENT_USER';
CURSOR                                        : 'CURSOR';
CURSOR_CLOSE_ON_COMMIT                        : 'CURSOR_CLOSE_ON_COMMIT';
CURSOR_DEFAULT                                : 'CURSOR_DEFAULT';
CUSTOM                                        : 'CUSTOM';
CYCLE                                         : 'CYCLE';
DATA                                          : 'DATA';
DATA_COMPRESSION                              : 'DATA_COMPRESSION';
DATA_CONSISTENCY_CHECK                        : 'DATA_CONSISTENCY_CHECK';
DATA_DELETION                                 : 'DATA_DELETION';
DATA_FLUSH_INTERVAL_SECONDS                   : 'DATA_FLUSH_INTERVAL_SECONDS';
DATA_RETENTION                                : 'DATA_RETENTION';
DATA_SOURCE                                   : 'DATA_SOURCE';
DATABASE                                      : 'DATABASE';
DATABASE_MIRRORING                            : 'DATABASE_MIRRORING';
DATABASE_NAME                                 : 'DATABASE_NAME';
DATASPACE                                     : 'DATASPACE';
DATE                                          : 'DATE';
DATE_CORRELATION_OPTIMIZATION                 : 'DATE_CORRELATION_OPTIMIZATION';
DATE_FORMAT                                   : 'DATE_FORMAT';
DATEFIRST                                     : 'DATEFIRST';
DATEFORMAT                                    : 'DATEFORMAT';
DATEPART                                      : 'DATEPART';
DATETIME                                      : 'DATETIME';
DATETIME2                                     : 'DATETIME2';
DATETIMEOFFSET                                : 'DATETIMEOFFSET';
DAY                                           : 'DAY';
DAYS                                          : 'DAYS';
DB_CHAINING                                   : 'DB_CHAINING';
DB_FAILOVER                                   : 'DB_FAILOVER';
DBNAME                                        : 'DBNAME';
DDL                                           : 'DDL';
DEALLOCATE                                    : 'DEALLOCATE';
DECIMAL                                       : 'DECIMAL';
DECLARE                                       : 'DECLARE';
DECRYPTION                                    : 'DECRYPTION';
DEFAULT                                       : 'DEFAULT';
DEFAULT_DATABASE                              : 'DEFAULT_DATABASE';
DEFAULT_FULLTEXT_LANGUAGE                     : 'DEFAULT_FULLTEXT_LANGUAGE';
DEFAULT_LANGUAGE                              : 'DEFAULT_LANGUAGE';
DEFAULT_SCHEMA                                : 'DEFAULT_SCHEMA';
DEFERRED_COMPILATION_TV                       : 'DEFERRED_COMPILATION_TV';
DEFINER                                       : 'DEFINER';
DEFINITION                                    : 'DEFINITION';
DELAY                                         : 'DELAY';
DELAYED_DURABILITY                            : 'DELAYED_DURABILIT';
DELETE                                        : 'DELETE';
DELETED                                       : 'DELETED';
DELIMITEDTEXT                                 : 'DELIMITEDTEXT';
DELTA                                         : 'DELTA';
DENSE_RANK                                    : 'DENSE_RANK';
DENY                                          : 'DENY';
DES                                           : 'DES';
DESC                                          : 'DESC';
DESCRIPTION                                   : 'DESCRIPTION';
DESX                                          : 'DESX';
DETERMINISTIC                                 : 'DETERMINISTIC';
DHCP                                          : 'DHCP';
DIAGNOSTICS                                   : 'DIAGNOSTICS';
DIALOG                                        : 'DIALOG';
DIFFERENTIAL                                  : 'DIFFERENTIAL';
DIRECTORY_NAME                                : 'DIRECTORY_NAME';
DISABLE                                       : 'DISABLE';
DISABLE_BROKER                                : 'DISABLE_BROKER';
DISABLE_OPTIMIZED_PLAN_FORCING                : 'DISABLE_OPTIMIZED_PLAN_FORCING';
DISABLED                                      : 'DISABLED';
DISK                                          : 'DISK';
DISTINCT                                      : 'DISTINCT';
DISTRIBUTED                                   : 'DISTRIBUTED';
DISTRIBUTION                                  : 'DISTRIBUTION';
DO                                            : 'DO';
DOCUMENT                                      : 'DOCUMENT';
DOP_FEEDBACK                                  : 'DOP_FEEDBACK';
DOUBLE                                        : 'DOUBLE';
DROP                                          : 'DROP';
DROP_EXISTING                                 : 'DROP_EXISTING';
DURABILITY                                    : 'DURABILITY';
DYNAMIC                                       : 'DYNAMIC';
EDITION                                       : 'EDITION';
ELASTIC_POOL                                  : 'ELASTIC_POOL';
ELEMENTS                                      : 'ELEMENTS';
ELEVATE_ONLINE                                : 'ELEVATE_ONLINE';
ELEVATE_RESUMABLE                             : 'ELEVATE_RESUMABLE';
ELSE                                          : 'ELSE';
EMERGENCY                                     : 'EMERGENCY';
EMPTY                                         : 'EMPTY';
ENABLE                                        : 'ENABLE';
ENABLE_BROKER                                 : 'ENABLE_BROKER';
ENABLED                                       : 'ENABLED';
ENCLAVE_COMPUTATIONS                          : 'ENCLAVE_COMPUTATIONS';
ENCODING                                      : 'ENCODING';
ENCRYPTED                                     : 'ENCRYPTED';
ENCRYPTED_VALUE                               : 'ENCRYPTED_VALUE';
ENCRYPTION                                    : 'ENCRYPTION';
ENCRYPTION_TYPE                               : 'ENCRYPTION_TYPE';
END                                           : 'END';
ENDPOINT                                      : 'ENDPOINT';
ENDPOINT_URL                                  : 'ENDPOINT_URL';
ENVIRONMENT_VARIABLES                         : 'ENVIRONMENT_VARIABLES';
ERROR                                         : 'ERROR';
ERROR_BROKER_CONVERSATIONS                    : 'ERROR_BROKER_CONVERSATIONS';
ESCAPE                                        : 'ESCAPE';
EVENT                                         : 'EVENT';
EVENT_RETENTION_MODE                          : 'EVENT_RETENTION_MODE';
EXCEPT                                        : 'EXCEPT';
EXEC                                          : 'EXEC';
EXEC_QUERY_STATS_FOR_SCALAR_FUNCTIONS         : 'EXEC_QUERY_STATS_FOR_SCALAR_FUNCTIONS';
EXECUTABLE                                    : 'EXECUTABLE';
EXECUTABLE_FILE                               : 'EXECUTABLE_FILE';
EXECUTE                                       : 'EXECUTE';
EXECUTION_COUNT                               : 'EXECUTION_COUNT';
EXISTS                                        : 'EXISTS';
EXPAND                                        : 'EXPAND';
EXPIREDATE                                    : 'EXPIREDATE';
EXPIRY_DATE                                   : 'EXPIRY_DATE';
EXPLAIN                                       : 'EXPLAIN';
EXPLICIT                                      : 'EXPLICIT';
EXTENSION                                     : 'EXTENSION';
EXTERNAL                                      : 'EXTERNAL';
EXTERNAL_ACCESS                               : 'EXTERNAL_ACCESS';
EXTERNAL_MONITOR                              : 'EXTERNAL_MONITOR';
EXTERNALPUSHDOWN                              : 'EXTERNALPUSHDOWN';
FAIL_OPERATION                                : 'FAIL_OPERATION';
FAIL_UNSUPPORTED                              : 'FAIL_UNSUPPORTED';
FAILOVER                                      : 'FAILOVER';
FAILOVER_MODE                                 : 'FAILOVER_MODE';
FAILURE_CONDITION_LEVEL                       : 'FAILURE_CONDITION_LEVEL';
FALSE                                         : 'FALSE';
FAN_IN                                        : 'FAN_IN';
FAST                                          : 'FAST';
FAST_FORWARD                                  : 'FAST_FORWARD';
FEDERATED_SERVICE_ACCOUNT                     : 'FEDERATED_SERVICE_ACCOUNT';
FETCH                                         : 'FETCH';
FIELD_TERMINATOR                              : 'FIELD_TERMINATOR';
FILE                                          : 'FILE';
FILE_FORMAT                                   : 'FILE_FORMAT';
FILE_NAME                                     : 'FILE_NAME';
FILE_SNAPSHOT                                 : 'FILE_SNAPSHOT';
FILEGROUP                                     : 'FILEGROUP';
FILEGROWTH                                    : 'FILEGROWTH';
FILELISTONLY                                  : 'FILELISTONLY';
FILENAME                                      : 'FILENAME';
FILEPATH                                      : 'FILEPATH';
FILESTREAM                                    : 'FILESTREAM';
FILESTREAM_ON                                 : 'FILESTREAM_ON';
FILETABLE                                     : 'FILETABLE';
FILETABLE_COLLATE_FILENAME                    : 'FILETABLE_COLLATE_FILENAME';
FILETABLE_DIRECTORY                           : 'FILETABLE_DIRECTORY';
FILETABLE_FULLPATH_UNIQUE_CONSTRAINT_NAME     : 'FILETABLE_FULLPATH_UNIQUE_CONSTRAINT_NAME';
FILETABLE_NAMESPACE                           : 'FILETABLE_NAMESPACE';
FILETABLE_PRIMARY_KEY_CONSTRAINT_NAME         : 'FILETABLE_PRIMARY_KEY_CONSTRAINT_NAME';
FILETABLE_STREAMID_UNIQUE_CONSTRAINT_NAME     : 'FILETABLE_STREAMID_UNIQUE_CONSTRAINT_NAME';
FILLFACTOR                                    : 'FILLFACTOR';
FILTER                                        : 'FILTER';
FILTER_COLUMN                                 : 'FILTER_COLUMN';
FILTER_PREDICATE                              : 'FILTER_PREDICATE';
FIRST                                         : 'FIRST';
FIRST_ROW                                     : 'FIRST_ROW';
FIRST_VALUE                                   : 'FIRST_VALUE';
FLOAT                                         : 'FLOAT';
FMTONLY                                       : 'FMTONLY';
FOLLOWING                                     : 'FOLLOWING';
FOR                                           : 'FOR';
FORCE                                         : 'FORCE';
FORCE_FAILOVER_ALLOW_DATA_LOSS                : 'FORCE_FAILOVER_ALLOW_DATA_LOSS';
FORCE_LAST_GOOD_PLAN                          : 'FORCE_LAST_GOOD_PLAN';
FORCE_SERVICE_ALLOW_DATA_LOSS                 : 'FORCE_SERVICE_ALLOW_DATA_LOSS';
FORCED                                        : 'FORCED';
FORCEPLAN                                     : 'FORCEPLAN';
FORCESCAN                                     : 'FORCESCAN';
FORCESEEK                                     : 'FORCESEEK';
FOREIGN                                       : 'FOREIGN';
FORMAT                                        : 'FORMAT';
FORMAT_OPTIONS                                : 'FORMAT_OPTIONS';
FORWARD_ONLY                                  : 'FORWARD_ONLY';
FROM                                          : 'FROM';
FULL                                          : 'FULL';
FULLSCAN                                      : 'FULLSCAN';
FULLTEXT                                      : 'FULLTEXT';
FUNCTION                                      : 'FUNCTION';
GB                                            : 'GB';
GENERATED                                     : 'GENERATED';
GEO                                           : 'GEO';
GEOGRAPHY                                     : 'GEOGRAPHY';
GEOMETRY                                      : 'GEOMETRY';
GET                                           : 'GET';
GET_TRANSMISSION_STATUS                       : 'GET_TRANSMISSION_STATUS';
GLOBAL                                        : 'GLOBAL';
GLOBAL_TEMPORARY_TABLE_AUTO_DROP              : 'GLOBAL_TEMPORARY_TABLE_AUTO_DROP';
GO                                            : 'GO';
GOTO                                          : 'GOTO';
GOVERNOR                                      : 'GOVERNOR';
GRANT                                         : 'GRANT';
GRAPH                                         : 'GRAPH';
GROUP                                         : 'GROUP';
GROUP_MAX_REQUESTS                            : 'GROUP_MAX_REQUESTS';
HADR                                          : 'HADR';
HARDWARE_OFFLOAD                              : 'HARDWARE_OFFLOAD';
HASH                                          : 'HASH';
HASHED                                        : 'HASHED';
HAVING                                        : 'HAVING';
HEADERONLY                                    : 'HEADERONLY';
HEALTH_CHECK_TIMEOUT                          : 'HEALTH_CHECK_TIMEOUT';
HEAP                                          : 'HEAP';
HIDDEN_                                       : 'HIDDEN';
HIERARCHYID                                   : 'HIERARCHYID';
HIGH                                          : 'HIGH';
HINT                                          : 'HINT';
HISTORY_RETENTION_PERIOD                      : 'HISTORY_RETENTION_PERIOD';
HISTORY_TABLE                                 : 'HISTORY_TABLE';
HOLDLOCK                                      : 'HOLDLOCK';
HONOR_BROKER_PRIORITY                         : 'HONOR_BROKER_PRIORITY';
HOUR                                          : 'HOUR';
HOURS                                         : 'HOURS';
HYBRID_BUFFER_POOL                            : 'HYBRID_BUFFER_POOL';
IDENTITY                                      : 'IDENTITY';
IDENTITY_CACHE                                : 'IDENTITY_CACHE';
IDENTITY_INSERT                               : 'IDENTITY_INSERT';
IDENTITY_VALUE                                : 'IDENTITY_VALUE';
IF                                            : 'IF';
IGNORE                                        : 'IGNORE';
IGNORE_CONSTRAINTS                            : 'IGNORE_CONSTRAINTS';
IGNORE_DUP_KEY                                : 'IGNORE_DUP_KEY';
IGNORE_NONCLUSTERED_COLUMNSTORE_INDEX         : 'IGNORE_NONCLUSTERED_COLUMNSTORE_INDEX';
IGNORE_TRIGGERS                               : 'IGNORE_TRIGGERS';
IMAGE                                         : 'IMAGE';
IMMEDIATE                                     : 'IMMEDIATE';
IMPERSONATE                                   : 'IMPERSONATE';
IMPLICIT_TRANSACTIONS                         : 'IMPLICIT_TRANSACTIONS';
IMPORTANCE                                    : 'IMPORTANCE';
IN                                            : 'IN';
INBOUND                                       : 'INBOUND';
INCLUDE                                       : 'INCLUDE';
INCLUDE_NULL_VALUES                           : 'INCLUDE_NULL_VALUES';
INCREMENT                                     : 'INCREMENT';
INCREMENTAL                                   : 'INCREMENTAL';
INDEX                                         : 'INDEX';
INFINITE                                      : 'INFINITE';
INIT                                          : 'INIT';
INITIATOR                                     : 'INITIATOR';
INLINE                                        : 'INLINE';
INNER                                         : 'INNER';
INPUT                                         : 'INPUT';
INSERT                                        : 'INSERT';
INSERTED                                      : 'INSERTED';
INSTANCE                                      : 'INSTANCE';
INSTEAD                                       : 'INSTEAD';
INT                                           : 'INT';
INTEGER                                       : 'INTEGER';
INTERLEAVED_EXECUTION_TVF                     : 'INTERLEAVED_EXECUTION_TVF';
INTERSECT                                     : 'INTERSECT';
INTERVAL                                      : 'INTERVAL';
INTERVAL_LENGTH_MINUTES                       : 'INTERVAL_LENGTH_MINUTES';
INTO                                          : 'INTO';
IO                                            : 'IO';
IP                                            : 'IP';
IS                                            : 'IS';
ISOLATE_SECURITY_POLICY_CARDINALITY           : 'ISOLATE_SECURITY_POLICY_CARDINALITY';
ISOLATION                                     : 'ISOLATION';
JOB                                           : 'JOB';
JOIN                                          : 'JOIN';
JSON                                          : 'JSON';
JSON_ARRAY                                    : 'JSON_ARRAY';
JSON_OBJECT                                   : 'JSON_OBJECT';
KB                                            : 'KB';
KEEP                                          : 'KEEP';
KEEP_CDC                                      : 'KEEP_CDC';
KEEP_REPLICATION                              : 'KEEP_REPLICATION';
KEEPDEFAULTS                                  : 'KEEPDEFAULTS';
KEEPFIXED                                     : 'KEEPFIXED';
KEEPIDENTITY                                  : 'KEEPIDENTITY';
KERBEROS                                      : 'KERBEROS';
KEY                                           : 'KEY';
KEY_PATH                                      : 'KEY_PATH';
KEY_SOURCE                                    : 'KEY_SOURCE';
KEY_STORE_PROVIDER_NAME                       : 'KEY_STORE_PROVIDER_NAME';
KEYS                                          : 'KEYS';
KEYSET                                        : 'KEYSET';
KILL                                          : 'KILL';
LABEL                                         : 'LABEL';
LABELONLY                                     : 'LABELONLY';
LANGUAGE                                      : 'LANGUAGE';
LAST                                          : 'LAST';
LAST_NODE                                     : 'LAST_NODE';
LAST_QUERY_PLAN_STATS                         : 'LAST_QUERY_PLAN_STATS';
LAST_VALUE                                    : 'LAST_VALUE';
LEADING                                       : 'LEADING';
LEDGER                                        : 'LEDGER';
LEDGER_DIGEST_STORAGE_ENDPOINT                : 'LEDGER_DIGEST_STORAGE_ENDPOINT';
LEFT                                          : 'LEFT';
LEGACY_CARDINALITY_ESTIMATION                 : 'LEGACY_CARDINALITY_ESTIMATION';
LEVEL                                         : 'LEVEL';
LIBRARY                                       : 'LIBRARY';
LIFETIME                                      : 'LIFETIME';
LIGHTWEIGHT_QUERY_PROFILING                   : 'LIGHTWEIGHT_QUERY_PROFILING';
LIKE                                          : 'LIKE';
LIMIT                                         : 'LIMIT';
LINKED                                        : 'LINKED';
LINUX                                         : 'LINUX';
LIST                                          : 'LIST';
LISTENER                                      : 'LISTENER';
LISTENER_IP                                   : 'LISTENER_IP';
LISTENER_URL                                  : 'LISTENER_URL';
LOADHISTORY                                   : 'LOADHISTORY';
LOB_COMPACTION                                : 'LOB_COMPACTION';
LOCAL                                         : 'LOCAL';
LOCAL_SERVICE_NAME                            : 'LOCAL_SERVICE_NAME';
LOCALTIME                                     : 'LOCALTIME';
LOCALTIMESTAMP                                : 'LOCALTIMESTAMP';
LOCATION                                      : 'LOCATION';
LOCK_ESCALATION                               : 'LOCK_ESCALATION';
LOG                                           : 'LOG';
LOGIN                                         : 'LOGIN';
LOOP                                          : 'LOOP';
LOW                                           : 'LOW';
MANUAL                                        : 'MANUAL';
MARK                                          : 'MARK';
MASK                                          : 'MASK';
MASKED                                        : 'MASKED';
MASTER                                        : 'MASTER';
MATCH                                         : 'MATCH';
MATCHED                                       : 'MATCHED';
MAX                                           : 'MAX';
MAX_CPU_PERCENT                               : 'MAX_CPU_PERCENT';
MAX_DISPATCH_LATENCY                          : 'MAX_DISPATCH_LATENCY';
MAX_DOP                                       : 'MAX_DOP';
MAX_DURATION                                  : 'MAX_DURATION';
MAX_EVENT_SIZE                                : 'MAX_EVENT_SIZE';
MAX_FILES                                     : 'MAX_FILES';
MAX_GRANT_PERCENT                             : 'MAX_GRANT_PERCENT';
MAX_MEMORY                                    : 'MAX_MEMORY';
MAX_MEMORY_PERCENT                            : 'MAX_MEMORY_PERCENT';
MAX_OUTSTANDING_IO_PER_VOLUME                 : 'MAX_OUTSTANDING_IO_PER_VOLUME';
MAX_PLANS_PER_QUERY                           : 'MAX_PLANS_PER_QUERY';
MAX_PROCESSES                                 : 'MAX_PROCESSES';
MAX_QUEUE_READERS                             : 'MAX_QUEUE_READERS';
MAX_ROLLOVER_FILES                            : 'MAX_ROLLOVER_FILES';
MAX_SIZE                                      : 'MAX_SIZE';
MAX_STORAGE_SIZE_MB                           : 'MAX_STORAGE_SIZE_MB';
MAXDOP                                        : 'MAXDOP';
MAXRECURSION                                  : 'MAXRECURSION';
MAXSIZE                                       : 'MAXSIZE';
MAXTRANSFERSIZE                               : 'MAXTRANSFERSIZE';
MAXVALUE                                      : 'MAXVALUE';
MB                                            : 'MB';
MEDIADESCRIPTION                              : 'MEDIADESCRIPTION';
MEDIANAME                                     : 'MEDIANAME';
MEDIAPASSWORD                                 : 'MEDIAPASSWORD';
MEDIUM                                        : 'MEDIUM';
MEMBER                                        : 'MEMBER';
MEMORY_GRANT_FEEDBACK_PERCENTILE_GRANT        : 'MEMORY_GRANT_FEEDBACK_PERCENTILE_GRANT';
MEMORY_GRANT_FEEDBACK_PERSISTENCE             : 'MEMORY_GRANT_FEEDBACK_PERSISTENCE';
MEMORY_OPTIMIZED                              : 'MEMORY_OPTIMIZED';
MEMORY_OPTIMIZED_DATA                         : 'MEMORY_OPTIMIZED_DATA';
MEMORY_OPTIMIZED_ELEVATE_TO_SNAPSHOT          : 'MEMORY_OPTIMIZED_ELEVATE_TO_SNAPSHOT';
MEMORY_PARTITION_MODE                         : 'MEMORY_PARTITION_MODE';
MERGE                                         : 'MERGE';
MESSAGE                                       : 'MESSAGE';
MESSAGE_FORWARD_SIZE                          : 'MESSAGE_FORWARD_SIZE';
MESSAGE_FORWARDING                            : 'MESSAGE_FORWARDING';
METADATA_ONLY                                 : 'METADATA_ONLY';
MICROSECOND                                   : 'MICROSECOND';
MIGRATION_STATE                               : 'MIGRATION_STATE';
MIN                                           : 'MIN';
MIN_GRANT_PERCENT                             : 'MIN_GRANT_PERCENT';
MINUTE                                        : 'MINUTE';
MINUTES                                       : 'MINUTES';
MINVALUE                                      : 'MINVALUE';
MIRROR                                        : 'MIRROR';
MIRROR_ADDRESS                                : 'MIRROR_ADDRESS';
MIXED_PAGE_ALLOCATION                         : 'MIXED_PAGE_ALLOCATION';
MOD                                           : 'MOD';
MODE                                          : 'MODE';
MODIFY                                        : 'MODIFY';
MONEY                                         : 'MONEY';
MONTH                                         : 'MONTH';
MONTHS                                        : 'MONTHS';
MOVE                                          : 'MOVE';
MS_XPRESS                                     : 'MS_XPRESS';
MULTI_USER                                    : 'MULTI_USER';
MUST_CHANGE                                   : 'MUST_CHANGE';
NAME                                          : 'NAME';
NAMED                                         : 'NAMED';
NAMES                                         : 'NAMES';
NATIVE_COMPILATION                            : 'NATIVE_COMPILATION';
NATURAL                                       : 'NATURAL';
NCHAR                                         : 'NCHAR';
NEGOTIATE                                     : 'NEGOTIATE';
NESTED_TRIGGERS                               : 'NESTED_TRIGGERS';
NEW_ACCOUNT                                   : 'NEW_ACCOUNT';
NEW_BROKER                                    : 'NEW_BROKER';
NEW_PASSWORD                                  : 'NEW_PASSWORD';
NEXT                                          : 'NEXT';
NO                                            : 'NO';
NO_CHECKSUM                                   : 'NO_CHECKSUM';
NO_COMPRESSION                                : 'NO_COMPRESSION';
NO_EVENT_LOSS                                 : 'NO_EVENT_LOSS';
NO_PERFORMANCE_SPOOL                          : 'NO_PERFORMANCE_SPOOL';
NO_TRUNCATE                                   : 'NO_TRUNCATE';
NO_WAIT                                       : 'NO_WAIT';
NOCHECK                                       : 'NOCHECK';
NOCOUNT                                       : 'NOCOUNT';
NOEXEC                                        : 'NOEXEC';
NOEXPAND                                      : 'NOEXPAND';
NOFORMAT                                      : 'NOFORMAT';
NOINIT                                        : 'NOINIT';
NOLOCK                                        : 'NOLOCK';
NON_TRANSACTED_ACCESS                         : 'NON_TRANSACTED_ACCESS';
NONCLUSTERED                                  : 'NONCLUSTERED';
NONE                                          : 'NONE';
NORECOMPUTE                                   : 'NORECOMPUTE';
NORECOVERY                                    : 'NORECOVERY';
NORESET                                       : 'NORESET';
NOREWIND                                      : 'NOREWIND';
NOSKIP                                        : 'NOSKIP';
NOT                                           : 'NOT';
NOTIFICATION                                  : 'NOTIFICATION';
NOTIFICATIONS                                 : 'NOTIFICATIONS';
NOUNLOAD                                      : 'NOUNLOAD';
NOWAIT                                        : 'NOWAIT';
NTEXT                                         : 'NTEXT';
NTILE                                         : 'NTILE';
NTLM                                          : 'NTLM';
NULL                                          : 'NULL';
NULLS                                         : 'NULLS';
NUMANODE                                      : 'NUMANODE';
NUMERIC                                       : 'NUMERIC';
NUMERIC_ROUNDABORT                            : 'NUMERIC_ROUNDABORT';
NVARCHAR                                      : 'NVARCHAR';
OBJECT                                        : 'OBJECT';
OBJECT_ID                                     : 'OBJECT_ID';
OF                                            : 'OF';
OFF                                           : 'OFF';
OFF_WITHOUT_DATA_RECOVERY                     : 'OFF_WITHOUT_DATA_RECOVERY';
OFFLINE                                       : 'OFFLINE';
OFFSET                                        : 'OFFSET';
OLD_ACCOUNT                                   : 'OLD_ACCOUNT';
OLD_PASSWORD                                  : 'OLD_PASSWORD';
ON                                            : 'ON';
ON_FAILURE                                    : 'ON_FAILURE';
ONLINE                                        : 'ONLINE';
ONLY                                          : 'ONLY';
OPEN                                          : 'OPEN';
OPEN_EXISTING                                 : 'OPEN_EXISTING';
OPENDATASOURCE                                : 'OPENDATASOURCE';
OPENJSON                                      : 'OPENJSON';
OPENQUERY                                     : 'OPENQUERY';
OPENROWSET                                    : 'OPENROWSET';
OPERATION_MODE                                : 'OPERATION_MODE';
OPERATIONS                                    : 'OPERATIONS';
OPTIMISTIC                                    : 'OPTIMISTIC';
OPTIMIZE                                      : 'OPTIMIZE';
OPTIMIZE_FOR_AD_HOC_WORKLOADS                 : 'OPTIMIZE_FOR_AD_HOC_WORKLOADS';
OPTIMIZE_FOR_SEQUENTIAL_KEY                   : 'OPTIMIZE_FOR_SEQUENTIAL_KEY';
OPTIMIZED_PLAN_FORCING                        : 'OPTIMIZED_PLAN_FORCING';
OPTIMIZED_SP_EXECUTESQL                       : 'OPTIMIZED_SP_EXECUTESQL';
OPTION                                        : 'OPTION';
OR                                            : 'OR';
ORC                                           : 'ORC';
ORDER                                         : 'ORDER';
OUT                                           : 'OUT';
OUTBOUND                                      : 'OUTBOUND';
OUTER                                         : 'OUTER';
OUTPUT                                        : 'OUTPUT';
OVER                                          : 'OVER';
OVERRIDE                                      : 'OVERRIDE';
OWNER                                         : 'OWNER';
OWNERSHIP                                     : 'OWNERSHIP';
PAD_INDEX                                     : 'PAD_INDEX';
PAGE                                          : 'PAGE';
PAGE_VERIFY                                   : 'PAGE_VERIFY';
PAGECOUNT                                     : 'PAGECOUNT';
PAGLOCK                                       : 'PAGLOCK';
PARAMETER_SENSITIVE_PLAN_OPTIMIZATION         : 'PARAMETER_SENSITIVE_PLAN_OPTIMIZATION';
PARAMETER_SNIFFING                            : 'PARAMETER_SNIFFING';
PARAMETERIZATION                              : 'PARAMETERIZATION';
PARAMETERS                                    : 'PARAMETERS';
PARQUET                                       : 'PARQUET';
PARSEONLY                                     : 'PARSEONLY';
PARSER_VERSION                                : 'PARSER_VERSION';
PARTIAL                                       : 'PARTIAL';
PARTITION                                     : 'PARTITION';
PARTITIONS                                    : 'PARTITIONS';
PARTNER                                       : 'PARTNER';
PASSWORD                                      : 'PASSWORD';
PATH                                          : 'PATH';
PAUSE                                         : 'PAUSE';
PAUSED                                        : 'PAUSED';
PAUSED_RESUMABLE_INDEX_ABORT_DURATION_MINUTES : 'PAUSED_RESUMABLE_INDEX_ABORT_DURATION_MINUTES';
PER_CPU                                       : 'PER_CPU';
PER_NODE                                      : 'PER_NODE';
PERCENT                                       : 'PERCENT';
PERIOD                                        : 'PERIOD';
PERMISSION_SET                                : 'PERMISSION_SET';
PERSIST_SAMPLE_PERCENT                        : 'PERSIST_SAMPLE_PERCENT';
PERSISTED                                     : 'PERSISTED';
PERSISTENT_LOG_BUFFER                         : 'PERSISTENT_LOG_BUFFER';
PERSISTENT_VERSION_STORE_FILEGROUP            : 'PERSISTENT_VERSION_STORE_FILEGROUP';
PLAN                                          : 'PLAN';
PLATFORM                                      : 'PLATFORM';
POISON_MESSAGE_HANDLING                       : 'POISON_MESSAGE_HANDLING';
POLICY                                        : 'POLICY';
POOL                                          : 'POOL';
POPULATION                                    : 'POPULATION';
PORT                                          : 'PORT';
POSITION                                      : 'POSITION';
PRECEDING                                     : 'PRECEDING';
PRECISION                                     : 'PRECISION';
PREDICATE                                     : 'PREDICATE';
PRESERVE                                      : 'PRESERVE';
PRIMARY                                       : 'PRIMARY';
PRIMARY_ROLE                                  : 'PRIMARY_ROLE';
PRINT                                         : 'PRINT';
PRIOR                                         : 'PRIOR';
PRIORITY                                      : 'PRIORITY';
PRIORITY_LEVEL                                : 'PRIORITY_LEVEL';
PRIVATE                                       : 'PRIVATE';
PRIVILEGES                                    : 'PRIVILEGES';
PROC                                          : 'PROC';
PROCEDURE                                     : 'PROCEDURE';
PROCEDURE_CACHE                               : 'PROCEDURE_CACHE';
PROCEDURE_NAME                                : 'PROCEDURE_NAME';
PROCESS                                       : 'PROCESS';
PROFILE                                       : 'PROFILE';
PROPERTY                                      : 'PROPERTY';
PROPERTY_DESCRIPTION                          : 'PROPERTY_DESCRIPTION';
PROPERTY_INT_ID                               : 'PROPERTY_INT_ID';
PROPERTY_SET_GUID                             : 'PROPERTY_SET_GUID';
PROVIDER                                      : 'PROVIDER';
PROVIDER_KEY_NAME                             : 'PROVIDER_KEY_NAME';
PUSHDOWN                                      : 'PUSHDOWN';
QUARTER                                       : 'QUARTER';
QUERY                                         : 'QUERY';
QUERY_CAPTURE_MODE                            : 'QUERY_CAPTURE_MODE';
QUERY_CAPTURE_POLICY                          : 'QUERY_CAPTURE_POLICY';
QUERY_OPTIMIZER_HOTFIXES                      : 'QUERY_OPTIMIZER_HOTFIXES';
QUERY_STORE                                   : 'QUERY_STORE';
QUERYTRACEON                                  : 'QUERYTRACEON';
QUEUE                                         : 'QUEUE';
QUEUE_DELAY                                   : 'QUEUE_DELAY';
QUOTED_IDENTIFIER                             : 'QUOTED_IDENTIFIER';
RAISERROR                                     : 'RAISERROR';
RANDOMIZED                                    : 'RANDOMIZED';
RANGE                                         : 'RANGE';
RANK                                          : 'RANK';
RAW                                           : 'RAW';
RC2                                           : 'RC2';
RC4                                           : 'RC4';
RC4_128                                       : 'RC4_128';
RCFILE                                        : 'RCFILE';
READ                                          : 'READ';
READ_COMMITTED_SNAPSHOT                       : 'READ_COMMITTED_SNAPSHOT';
READ_ONLY                                     : 'READ_ONLY';
READ_ONLY_ROUTING_LIST                        : 'READ_ONLY_ROUTING_LIST';
READ_ONLY_ROUTING_URL                         : 'READ_ONLY_ROUTING_URL';
READ_WRITE                                    : 'READ_WRITE';
READ_WRITE_FILEGROUPS                         : 'READ_WRITE_FILEGROUPS';
READ_WRITE_ROUTING_URL                        : 'READ_WRITE_ROUTING_URL';
READCOMMITTED                                 : 'READCOMMITTED';
READCOMMITTEDLOCK                             : 'READCOMMITTEDLOCK';
READONLY                                      : 'READONLY';
READPAST                                      : 'READPAST';
READUNCOMMITTED                               : 'READUNCOMMITTED';
READWRITE                                     : 'READWRITE';
REAL                                          : 'REAL';
REBUILD                                       : 'REBUILD';
RECEIVE                                       : 'RECEIVE';
RECOMPILE                                     : 'RECOMPILE';
RECONFIGURE                                   : 'RECONFIGURE';
RECOVERY                                      : 'RECOVERY';
RECURSIVE_TRIGGERS                            : 'RECURSIVE_TRIGGERS';
REFERENCES                                    : 'REFERENCES';
REGENERATE                                    : 'REGENERATE';
REJECT_SAMPLE_VALUE                           : 'REJECT_SAMPLE_VALUE';
REJECT_TYPE                                   : 'REJECT_TYPE';
REJECT_VALUE                                  : 'REJECT_VALUE';
REJECTED_ROW_LOCATION                         : 'REJECTED_ROW_LOCATION';
RELATED_CONVERSATION                          : 'RELATED_CONVERSATION';
RELATED_CONVERSATION_GROUP                    : 'RELATED_CONVERSATION_GROUP';
RELATIVE                                      : 'RELATIVE';
REMOTE                                        : 'REMOTE';
REMOTE_DATA_ARCHIVE                           : 'REMOTE_DATA_ARCHIVE';
REMOTE_PROC_TRANSACTIONS                      : 'REMOTE_PROC_TRANSACTIONS';
REMOTE_SERVICE_NAME                           : 'REMOTE_SERVICE_NAME';
REMOVE                                        : 'REMOVE';
REORGANIZE                                    : 'REORGANIZE';
REPEATABLE                                    : 'REPEATABLE';
REPEATABLEREAD                                : 'REPEATABLEREAD';
REPLACE                                       : 'REPLACE';
REPLICA                                       : 'REPLICA';
REPLICATE                                     : 'REPLICATE';
REPLICATION                                   : 'REPLICATION';
REQUEST_MAX_CPU_TIME_SEC                      : 'REQUEST_MAX_CPU_TIME_SEC';
REQUEST_MAX_MEMORY_GRANT_PERCENT              : 'REQUEST_MAX_MEMORY_GRANT_PERCENT';
REQUEST_MEMORY_GRANT_TIMEOUT_SEC              : 'REQUEST_MEMORY_GRANT_TIMEOUT_SEC';
REQUIRED                                      : 'REQUIRED';
REQUIRED_SYNCHRONIZED_SECONDARIES_TO_COMMIT   : 'REQUIRED_SYNCHRONIZED_SECONDARIES_TO_COMMIT';
RESAMPLE                                      : 'RESAMPLE';
RESERVE_DISK_SPACE                            : 'RESERVE_DISK_SPACE';
RESET                                         : 'RESET';
RESOURCE                                      : 'RESOURCE';
RESOURCE_MANAGER_LOCATION                     : 'RESOURCE_MANAGER_LOCATION';
RESOURCE_POOL                                 : 'RESOURCE_POOL';
RESOURCES                                     : 'RESOURCES';
RESPECT                                       : 'RESPECT';
RESTART                                       : 'RESTART';
RESTORE                                       : 'RESTORE';
RESTRICTED_USER                               : 'RESTRICTED_USER';
RESULT                                        : 'RESULT';
RESUMABLE                                     : 'RESUMABLE';
RESUME                                        : 'RESUME';
RETAINDAYS                                    : 'RETAINDAYS';
RETENTION                                     : 'RETENTION';
RETENTION_PERIOD                              : 'RETENTION_PERIOD';
RETURN                                        : 'RETURN';
RETURNS                                       : 'RETURNS';
REUSE_SYSTEM_DATABASES                        : 'REUSE_SYSTEM_DATABASES';
REVERT                                        : 'REVERT';
REVOKE                                        : 'REVOKE';
REWIND                                        : 'REWIND';
REWINDONLY                                    : 'REWINDONLY';
RIGHT                                         : 'RIGHT';
ROBUST                                        : 'ROBUST';
ROLE                                          : 'ROLE';
ROLLBACK                                      : 'ROLLBACK';
ROOT                                          : 'ROOT';
ROUND_ROBIN                                   : 'ROUND_ROBIN';
ROUTE                                         : 'ROUTE';
ROW                                           : 'ROW';
ROW_MODE_MEMORY_GRANT_FEEDBACK                : 'ROW_MODE_MEMORY_GRANT_FEEDBACK';
ROW_NUMBER                                    : 'ROW_NUMBER';
ROWCOUNT                                      : 'ROWCOUNT';
ROWGUID                                       : 'ROWGUID';
ROWGUIDCOL                                    : 'ROWGUIDCOL';
ROWLOCK                                       : 'ROWLOCK';
ROWS                                          : 'ROWS';
RSA_512                                       : 'RSA_512';
RSA_1024                                      : 'RSA_1024';
RSA_2048                                      : 'RSA_2048';
RSA_3072                                      : 'RSA_3072';
RSA_4096                                      : 'RSA_4096';
RULE                                          : 'RULE';
SAFE                                          : 'SAFE';
SAFETY                                        : 'SAFETY';
SAMPLE                                        : 'SAMPLE';
SAVE                                          : 'SAVE';
SAVEPOINT                                     : 'SAVEPOINT';
SCALEOUTEXECUTION                             : 'SCALEOUTEXECUTION';
SCHEMA                                        : 'SCHEMA';
SCHEMA_AND_DATA                               : 'SCHEMA_AND_DATA';
SCHEMA_ONLY                                   : 'SCHEMA_ONLY';
SCHEMABINDING                                 : 'SCHEMABINDING';
SCHEME                                        : 'SCHEME';
SCOPED                                        : 'SCOPED';
SCRIPT                                        : 'SCRIPT';
SCROLL                                        : 'SCROLL';
SCROLL_LOCKS                                  : 'SCROLL_LOCKS';
SEARCH                                        : 'SEARCH';
SECOND                                        : 'SECOND';
SECONDARY                                     : 'SECONDARY';
SECONDARY_ONLY                                : 'SECONDARY_ONLY';
SECONDARY_ROLE                                : 'SECONDARY_ROLE';
SECONDS                                       : 'SECONDS';
SECRET                                        : 'SECRET';
SECURABLES                                    : 'SECURABLES';
SECURITY                                      : 'SECURITY';
SECURITY_LOG                                  : 'SECURITY_LOG';
SEEDING_MODE                                  : 'SEEDING_MODE';
SELECT                                        : 'SELECT';
SELF                                          : 'SELF';
SEND                                          : 'SEND';
SENT                                          : 'SENT';
SEQUENCE                                      : 'SEQUENCE';
SEQUENCE_NUMBER                               : 'SEQUENCE_NUMBER';
SERDE_METHOD                                  : 'SERDE_METHOD';
SERIALIZABLE                                  : 'SERIALIZABLE';
SERVER                                        : 'SERVER';
SERVICE                                       : 'SERVICE';
SERVICE_BROKER                                : 'SERVICE_BROKER';
SERVICE_NAME                                  : 'SERVICE_NAME';
SERVICE_OBJECTIVE                             : 'SERVICE_OBJECTIVE';
SESSION                                       : 'SESSION';
SESSION_TIMEOUT                               : 'SESSION_TIMEOUT';
SET                                           : 'SET';
SETERROR                                      : 'SETERROR';
SETS                                          : 'SETS';
SETTINGS                                      : 'SETTINGS';
SETUSER                                       : 'SETUSER';
SHORTEST_PATH                                 : 'SHORTEST_PATH';
SHOWPLAN                                      : 'SHOWPLAN';
SHOWPLAN_ALL                                  : 'SHOWPLAN_ALL';
SHOWPLAN_TEXT                                 : 'SHOWPLAN_TEXT';
SHOWPLAN_XML                                  : 'SHOWPLAN_XML';
SHUTDOWN                                      : 'SHUTDOWN';
SID                                           : 'SID';
SIGNATURE                                     : 'SIGNATURE';
SIMPLE                                        : 'SIMPLE';
SINGLE_USER                                   : 'SINGLE_USER';
SIZE                                          : 'SIZE';
SIZE_BASED_CLEANUP_MODE                       : 'SIZE_BASED_CLEANUP_MODE';
SKIP_                                         : 'SKIP';
SMALLDATETIME                                 : 'SMALLDATETIME';
SMALLINT                                      : 'SMALLINT';
SMALLMONEY                                    : 'SMALLMONEY';
SNAPSHOT                                      : 'SNAPSHOT';
SOFTNUMA                                      : 'SOFTNUMA';
SORT_IN_TEMPDB                                : 'SORT_IN_TEMPDB';
SOURCE                                        : 'SOURCE';
SPARSE                                        : 'SPARSE';
SPATIAL_WINDOW_MAX_CELLS                      : 'SPATIAL_WINDOW_MAX_CELLS';
SPECIFICATION                                 : 'SPECIFICATION';
SPLIT                                         : 'SPLIT';
SQL                                           : 'SQL';
SQL_VARIANT                                   : 'SQL_VARIANT';
STALE_CAPTURE_POLICY_THRESHOLD                : 'STALE_CAPTURE_POLICY_THRESHOLD';
STALE_QUERY_THRESHOLD_DAYS                    : 'STALE_QUERY_THRESHOLD_DAYS';
STANDBY                                       : 'STANDBY';
START                                         : 'START';
START_DATE                                    : 'START_DATE';
STARTED                                       : 'STARTED';
STARTUP_STATE                                 : 'STARTUP_STATE';
STATE                                         : 'STATE';
STATIC                                        : 'STATIC';
STATISTICAL_SEMANTICS                         : 'STATISTICAL_SEMANTICS';
STATISTICS                                    : 'STATISTICS';
STATISTICS_INCREMENTAL                        : 'STATISTICS_INCREMENTAL';
STATISTICS_NORECOMPUTE                        : 'STATISTICS_NORECOMPUTE';
STATS                                         : 'STATS';
STATS_STREAM                                  : 'STATS_STREAM';
STATUS                                        : 'STATUS';
STATUSONLY                                    : 'STATUSONLY';
STOP                                          : 'STOP';
STOP_ON_ERROR                                 : 'STOP_ON_ERROR';
STOPAT                                        : 'STOPAT';
STOPATMARK                                    : 'STOPATMARK';
STOPBEFOREMARK                                : 'STOPBEFOREMARK';
STOPLIST                                      : 'STOPLIST';
STOPPED                                       : 'STOPPED';
STRING_AGG                                    : 'STRING_AGG';
STRING_DELIMITER                              : 'STRING_DELIMITER';
SUBJECT                                       : 'SUBJECT';
SUBSCRIBE                                     : 'SUBSCRIBE';
SUBSCRIPTION                                  : 'SUBSCRIPTION';
SUBSTRING                                     : 'SUBSTRING';
SUM                                           : 'SUM';
SUPPORTED                                     : 'SUPPORTED';
SUSPEND                                       : 'SUSPEND';
SUSPEND_FOR_SNAPSHOT_BACKUP                   : 'SUSPEND_FOR_SNAPSHOT_BACKUP';
SWITCH                                        : 'SWITCH';
SYMMETRIC                                     : 'SYMMETRIC';
SYNCHRONOUS_COMMIT                            : 'SYNCHRONOUS_COMMIT';
SYNONYM                                       : 'SYNONYM';
SYSTEM                                        : 'SYSTEM';
SYSTEM_TIME                                   : 'SYSTEM_TIME';
SYSTEM_VERSIONING                             : 'SYSTEM_VERSIONING';
TABLE                                         : 'TABLE';
TABLOCK                                       : 'TABLOCK';
TABLOCKX                                      : 'TABLOCKX';
TAKE                                          : 'TAKE';
TAPE                                          : 'TAPE';
TARGET                                        : 'TARGET';
TARGET_RECOVERY_TIME                          : 'TARGET_RECOVERY_TIME';
TB                                            : 'TB';
TCP                                           : 'TCP';
TEMPDB_METADATA                               : 'TEMPDB_METADATA';
TEMPORAL_HISTORY_RETENTION                    : 'TEMPORAL_HISTORY_RETENTION';
TEXT                                          : 'TEXT';
TEXTIMAGE_ON                                  : 'TEXTIMAGE_ON';
TEXTSIZE                                      : 'TEXTSIZE';
THEN                                          : 'THEN';
THROW                                         : 'THROW';
TIES                                          : 'TIES';
TIME                                          : 'TIME';
TIMEOUT                                       : 'TIMEOUT';
TIMER                                         : 'TIMER';
TIMESTAMP                                     : 'TIMESTAMP';
TINYINT                                       : 'TINYINT';
TO                                            : 'TO';
TOP                                           : 'TOP';
TORN_PAGE_DETECTION                           : 'TORN_PAGE_DETECTION';
TOTAL_COMPILE_CPU_TIME_MS                     : 'TOTAL_COMPILE_CPU_TIME_MS';
TOTAL_EXECUTION_CPU_TIME_MS                   : 'TOTAL_EXECUTION_CPU_TIME_MS';
TRACE                                         : 'TRACE';
TRACK_CAUSALITY                               : 'TRACK_CAUSALITY';
TRACKING                                      : 'TRACKING';
TRAILING                                      : 'TRAILING';
TRAN                                          : 'TRAN';
TRANCOUNT                                     : 'TRANCOUNT';
TRANSACTION                                   : 'TRANSACTION';
TRANSACTION_ID                                : 'TRANSACTION_ID';
TRANSFER                                      : 'TRANSFER';
TRANSFORM_NOISE_WORDS                         : 'TRANSFORM_NOISE_WORDS';
TRIGGER                                       : 'TRIGGER';
TRIM                                          : 'TRIM';
TRIPLE_DES                                    : 'TRIPLE_DES';
TRIPLE_DES_3KEY                               : 'TRIPLE_DES_3KEY';
TRUE                                          : 'TRUE';
TRUNCATE                                      : 'TRUNCATE';
TRUSTWORTHY                                   : 'TRUSTWORTHY';
TRY                                           : 'TRY';
TRY_CAST                                      : 'TRY_CAST';
TRY_CONVERT                                   : 'TRY_CONVERT';
TSQL                                          : 'TSQL';
TSQL_SCALAR_UDF_INLINING                      : 'TSQL_SCALAR_UDF_INLINING';
TWO_DIGIT_YEAR_CUTOFF                         : 'TWO_DIGIT_YEAR_CUTOFF';
TYPE                                          : 'TYPE';
TYPE_WARNING                                  : 'TYPE_WARNING';
UNBOUNDED                                     : 'UNBOUNDED';
UNCHECKED                                     : 'UNCHECKED';
UNCOMMITTED                                   : 'UNCOMMITTED';
UNDEFINED                                     : 'UNDEFINED';
UNION                                         : 'UNION';
UNIQUE                                        : 'UNIQUE';
UNIQUEIDENTIFIER                              : 'UNIQUEIDENTIFIER';
UNKNOWN                                       : 'UNKNOWN';
UNLIMITED                                     : 'UNLIMITED';
UNLOAD                                        : 'UNLOAD';
UNLOCK                                        : 'UNLOCK';
UNMASK                                        : 'UNMASK';
UNSAFE                                        : 'UNSAFE';
UOW                                           : 'UOW';
UPDATE                                        : 'UPDATE';
UPDLOCK                                       : 'UPDLOCK';
URL                                           : 'URL';
USE                                           : 'USE';
USE_TYPE_DEFAULT                              : 'USE_TYPE_DEFAULT';
USED                                          : 'USED';
USER                                          : 'USER';
USING                                         : 'USING';
VALID_XML                                     : 'VALID_XML';
VALIDATION                                    : 'VALIDATION';
VALUE                                         : 'VALUE';
VALUES                                        : 'VALUES';
VARBINARY                                     : 'VARBINARY';
VARCHAR                                       : 'VARCHAR';
VARYING                                       : 'VARYING';
VERBOSE_TRUNCATION_WARNINGS                   : 'VERBOSE_TRUNCATION_WARNINGS';
VERIFYONLY                                    : 'VERIFYONLY';
VIEW                                          : 'VIEW';
VIEW_METADATA                                 : 'VIEW_METADATA';
VIEWS                                         : 'VIEWS';
VISIBILITY                                    : 'VISIBILITY';
WAIT_AT_LOW_PRIORITY                          : 'WAIT_AT_LOW_PRIORITY';
WAIT_STATS_CAPTURE_MODE                       : 'WAIT_STATS_CAPTURE_MODE';
WAITFOR                                       : 'WAITFOR';
WEEK                                          : 'WEEK';
WEEKS                                         : 'WEEKS';
WELL_FORMED_XML                               : 'WELL_FORMED_XML';
WHEN                                          : 'WHEN';
WHEN_SUPPORTED                                : 'WHEN_SUPPORTED';
WHERE                                         : 'WHERE';
WHILE                                         : 'WHILE';
WINDOW                                        : 'WINDOW';
WINDOWS                                       : 'WINDOWS';
WITH                                          : 'WITH';
WITH_RECOMMENDATIONS                          : 'WITH_RECOMMENDATIONS';
WITHIN                                        : 'WITHIN';
WITHOUT                                       : 'WITHOUT';
WITHOUT_ARRAY_WRAPPER                         : 'WITHOUT_ARRAY_WRAPPER';
WITNESS                                       : 'WITNESS';
WORK                                          : 'WORK';
WORKLOAD                                      : 'WORKLOAD';
WSFC                                          : 'WSFC';
XACT_ABORT                                    : 'XACT_ABORT';
XLOCK                                         : 'XLOCK';
XML                                           : 'XML';
XML_COMPRESSION                               : 'XML_COMPRESSION';
XMLDATA                                       : 'XMLDATA';
XMLNAMESPACES                                 : 'XMLNAMESPACES';
XMLSCHEMA                                     : 'XMLSCHEMA';
XOR                                           : 'XOR';
XSINIL                                        : 'XSINIL';
XTP_PROCEDURE_EXECUTION_STATISTICS            : 'XTP_PROCEDURE_EXECUTION_STATISTICS';
XTP_QUERY_EXECUTION_STATISTICS                : 'XTP_QUERY_EXECUTION_STATISTICS';
YEAR                                          : 'YEAR';
YEARS                                         : 'YEARS';
ZONE                                          : 'ZONE';
APPEND_ONLY                     : 'APPEND_ONLY';
ATTACH                          : 'ATTACH';
ATTACH_REBUILD_LOG              : 'ATTACH_REBUILD_LOG';
BACKUP_OPTIONS                  : 'BACKUP_OPTIONS';
CAP_CPU_PERCENT                 : 'CAP_CPU_PERCENT';
CLASSIFICATION                  : 'CLASSIFICATION';
COUNTER                         : 'COUNTER';
CUBE                            : 'CUBE';
DATABASE_SNAPSHOT               : 'DATABASE_SNAPSHOT';
DEPENDENTS                      : 'DEPENDENTS';
EDGE                            : 'EDGE';
FORMAT_TYPE                     : 'FORMAT_TYPE';
GROUPING                        : 'GROUPING';
INSENSITIVE                     : 'INSENSITIVE';
LEDGER_VIEW                     : 'LEDGER_VIEW';
LISTENER_PORT                   : 'LISTENER_PORT';
MAX_IOPS_PER_VOLUME             : 'MAX_IOPS_PER_VOLUME';
MIN_CPU_PERCENT                 : 'MIN_CPU_PERCENT';
MIN_IOPS_PER_VOLUME             : 'MIN_IOPS_PER_VOLUME';
MIN_MEMORY_PERCENT              : 'MIN_MEMORY_PERCENT';
NEWNAME                         : 'NEWNAME';
NODE                            : 'NODE';
OPERATION_TYPE_COLUMN_NAME      : 'OPERATION_TYPE_COLUMN_NAME';
OPERATION_TYPE_DESC_COLUMN_NAME : 'OPERATION_TYPE_DESC_COLUMN_NAME';
PIVOT                           : 'PIVOT';
REDISTRIBUTE                    : 'REDISTRIBUTE';
REDUCE                          : 'REDUCE';
RESTORE_OPTIONS                 : 'RESTORE_OPTIONS';
ROLLUP                          : 'ROLLUP';
SCHEDULER                       : 'SCHEDULER';
SENSITIVITY                     : 'SENSITIVITY';
SEQUENCE_NUMBER_COLUMN_NAME     : 'SEQUENCE_NUMBER_COLUMN_NAME';
TABLESAMPLE                     : 'TABLESAMPLE';
TRACK_COLUMNS_UPDATED           : 'TRACK_COLUMNS_UPDATED';
TRANSACTION_ID_COLUMN_NAME      : 'TRANSACTION_ID_COLUMN_NAME';
UNPIVOT                         : 'UNPIVOT';
WRITE                           : 'WRITE';




VERBOSELOGGING options { caseInsensitive = false; } :'VerboseLogging';
SQLDUMPERFLAGS options { caseInsensitive = false; } :'SqlDumperDumpFlags';
SQLDUMPERPATH options { caseInsensitive = false; } :'SqlDumperDumpPath';
SQLDUMPERTIMEOUT options { caseInsensitive = false; } :'SqlDumperDumpTimeOut';
FAILURECONDITIONLEVEL options { caseInsensitive = false; } :'FailureConditionLevel';
HEALTHCHECKTIMEOUT options { caseInsensitive = false; } :'HealthCheckTimeout';



AND_                : '&&';
OR_                 : '||';
NOT_                : '!';
TILDE_              : '~';
VERTICAL_BAR_       : '|';
AMPERSAND_          : '&';
SIGNED_LEFT_SHIFT_  : '<<';
SIGNED_RIGHT_SHIFT_ : '>>';
CARET_              : '^';
MOD_                : '%';
COLON_              : ':';
DOUBLE_COLON_       : '::';
PLUS_               : '+';
MINUS_              : '-';
ASTERISK_           : '*';
SLASH_              : '/';
BACKSLASH_          : '\\';
DOT_                : '.';
DOUBLE_DOT_         : '..';
DOT_ASTERISK_       : '.*';
SAFE_EQ_            : '<=>';
DEQ_                : '==';
EQ_                 : '=';
NEQ_                : '<>' | '!=';
GT_                 : '>';
GTE_                : '>=';
LT_                 : '<';
LTE_                : '<=';
POUND_              : '#';
LP_                 : '(';
RP_                 : ')';
LBE_                : '{';
RBE_                : '}';
LBT_                : '[';
RBT_                : ']';
COMMA_              : ',';
DQ_                 : '"';
SQ_                 : '\'';
BQ_                 : '`';
QUESTION_           : '?';
AT_                 : '@';
SEMI_               : ';';
DOLLAR_             : '$';

WS: [ \t\r\n]+ -> skip;
BLOCK_COMMENT  : '/*' .*? '*/'                    -> channel(HIDDEN);
INLINE_COMMENT : '--' ~[\r\n]* ('\r'? '\n' | EOF) -> channel(HIDDEN);

DELIMITED_IDENTIFIER_: LBT_ [a-z0-9@$#_<>(),.:\-\\/\u0080-\uFFFF ]+ RBT_ | DOUBLE_QUOTED_TEXT;

SERVICE_IDENTIFIER_: '//' [a-z0-9@$#_().\-\\/\u0080-\uFFFF]+;

STRING_: SINGLE_QUOTED_TEXT;
SINGLE_QUOTED_TEXT: SQ_ ('\'\'' | ~('\''))* SQ_;
DOUBLE_QUOTED_TEXT: DQ_ ('""' | ~('"') )* DQ_;

IPV4_ADDR: DIGIT+ '.' DIGIT+ '.' DIGIT+ '.' DIGIT+;

INT_NUM_: DIGIT+;

FLOAT_NUM_: INT_NUM_? DOT_? INT_NUM_ 'E' (PLUS_ | MINUS_)? INT_NUM_;

// n.  .n  n.n
DECIMAL_NUM_: INT_NUM_? DOT_ INT_NUM_ | INT_NUM_ DOT_ INT_NUM_?;

HEX_DIGIT_: '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_;

BIT_NUM_: '0b' ('0' | '1')+ | 'B' SQ_ ('0' | '1')+ SQ_;

NCHAR_TEXT: 'N' STRING_;

IDENTIFIER_: [a-z_@#$\u0080-\uFFFF][a-z0-9@$#_\u0080-\uFFFF]*;

fragment DIGIT: [0-9];

fragment HEX_: [0-9a-f];
