/*
 T-SQL (Transact-SQL, MSSQL) grammar.
 */

// $antlr-format alignTrailingComments true, columnLimit 150, minEmptyLines 1, maxEmptyLinesToKeep 1, reflowComments false, useTab false
// $antlr-format allowShortRulesOnASingleLine false, allowShortBlocksOnASingleLine true, alignSemicolons hanging, alignColons hanging

parser grammar SqlServerParser;

options {
    tokenVocab = SqlServerLexer;
}

@members {
}

root
    : batch EOF
    ;
// 如果批处理或sqlcmd脚本中的第一个语句，EXECUTE则不是必需的。
batch
    : (executeObject SEMI_?)?  statement* go
    | statement
    ;
statement
    : (select
    | alterApplicationRole
    | alterAssembly
    | alterAsymmetricKey
    | alterAuthorization
    | alterAvailabilityGroup
    | alterBrokerPriority
    | alterCertificate
    | alterColumnEncryptionKey
    | alterCredential
    | alterCryptographicProvider
    | alterDatabase
    | alterDatabaseAuditSpecification
    | alterDatabaseEncryptionKey
    | alterDatabaseScopedConfiguration
    | alterEndpoint
    | alterEventSession
    | alterExternalDataSource
    | alterExternalLanguage
    | alterExternalLibrary
    | alterExternalResourcePool
    | alterFulltextCatalog
    | alterFulltextIndex
    | alterFulltextStoplist
    | alterFunction
    | alterIndex
    | alterLogin
    | alterMasterKey
    | alterMessageType
    | alterPartitionFunction
    | alterPartitionScheme
    | alterProcedure
    | alterQueue
    | alterRemoteServiceBinding
    | alterResourceGovernor
    | alterRole
    | alterRoute
    | alterSchema
    | alterSearchPropertyList
    | alterSecurityPolicy
    | alterSequence
    | alterServerAudit
    | alterServerAuditSpecification
    | alterServerConfiguration
    | alterServerRole
    | alterService
    | alterServiceMasterKey
    | alterSymmetricKey
    | alterTable
    | alterTrigger
    | alterUser
    | alterView
    | alterWorkloadGroup
    | alterXmlSchemaCollection
    | backupCertificate
    | backupDatabase
    | backupGroup
    | backupLog
    | backupMasterKey
    | backupService
    | backupServiceMasterKey
    | backupSymmetricKey
    | beginConversationDialog
    | beginConversationTimer
    | beginDistributedTransaction
    | beginTransaction
    | checkpoint
    | closeCursor
    | closeMasterKey
    | closeSymmetricKey
    | commit
    | commitWork
    | controlOfFlow
    | createAggregate
    | createApplicationRole
    | createAssembly
    | createAsymmetricKey
    | createAvailabilityGroup
    | createBrokerPriority
    | createCertificate
    | createColumnEncryptionKey
    | createColumnMasterKey
    | createColumnstoreIndex
    | createContract
    | createCredential
    | createCryptographicProvider
    | createDatabase
    | createDatabaseAuditSpecification
    | createDatabaseEncryptionKey
    | createDatabaseScopedCredential
    | createDefault
    | createEndpoint
    | createEventNotification
    | createEventSession
    | createExternalDataSource
    | createExternalFileFormat
    | createExternalLanguage
    | createExternalLibrary
    | createExternalResourcePool
    | createExternalTable
    | createFulltextCatalog
    | createFulltextIndex
    | createFulltextStoplist
    | createFunction
    | createIndex
    | createLogin
    | createMasterKey
    | createMessageType
    | createPartitionFunction
    | createPartitionScheme
    | createProcedure
    | createQueue
    | createRemoteServiceBinding
    | createRole
    | createRoute
    | createRule
    | createSchema
    | createSearchPropertyList
    | createSecurityPolicy
    | createSequence
    | createServerAudit
    | createServerAuditSpecification
    | createServerRole
    | createService
    | createStatistics
    | createSymmetricKey
    | createSynonym
    | createTable
    | createTrigger
    | createType
    | createUser
    | createView
    | createWorkloadGroup
    | createXmlSchemaCollection
    | deallocateCursor
    | declare
    | delete
    | deny
    | disableTrigger
    | dropAggregate
    | dropApplicationRole
    | dropAssembly
    | dropAsymmetricKey
    | dropAvailabilityGroup
    | dropBrokerPriority
    | dropCertificate
    | dropColumnEncryptionKey
    | dropColumnMasterKey
    | dropContract
    | dropCredential
    | dropCryptographicProvider
    | dropDatabase
    | dropDatabaseAuditSpecification
    | dropDatabaseEncryptionKey
    | dropDatabaseScopedCredential
    | dropDefault
    | dropEndpoint
    | dropExternalDataSource
    | dropEventNotification
    | dropEventSession
    | dropExternalFileFormat
    | dropExternalLanguage
    | dropExternalLibrary
    | dropExternalResourcePool
    | dropExternalTable
    | dropFulltextCatalog
    | dropFulltextIndex
    | dropFulltextStoplist
    | dropFunction
    | dropIndex
    | dropLogin
    | dropMasterKey
    | dropMessageType
    | dropPartitionFunction
    | dropPartitionScheme
    | dropProcedure
    | dropQueue
    | dropRemoteServiceBinding
    | dropRole
    | dropRoute
    | dropRule
    | dropSchema
    | dropSearchPropertyList
    | dropSecurityPolicy
    | dropSensitivityClassification
    | dropSequence
    | dropServerAudit
    | dropServerAuditSpecification
    | dropServerRole
    | dropService
    | dropSignature
    | dropStatistics
    | dropSymmetricKey
    | dropSynonym
    | dropTable
    | dropTrigger
    | dropType
    | dropUser
    | dropView
    | dropWorkloadGroup
    | dropXmlSchemaCollection
    | enableTrigger
    | endConversation
    | execute
    | executeAs
    | explain
    | fetchCursor
    | getConversation
    | getConversationStatus
    | grant
    | insert
    | kill
    | merge
    | moveConversation
    | openCursor
    | openMasterKey
    | openSymmetricKey
    | print
    | raiseerror
    | receive
    | reconfigure
    | restoreDatabase
    | restoreFilelistonly
    | restoreLabelonly
    | restoreHeaderonly
    | restoreLog
    | restoreMasterKey
    | restoreServiceMasterKey
    | restoreRewindonly
    | restoreSymmetricKey
    | restoreVerifyonly
    | revert
    | revoke
    | rollback
    | rollbackWork
    | savepoint
    | sendConversation
    | setUser
    | setVariable
    | shutdown
    | truncateTable
    | update
    | updateStatistics
    | use
    | alterResourcePool
    | createResourcePool
    | dropResourcePool
    ) SEMI_?
    ;
// https://msdn.microsoft.com/zh-cn/library/ms188037.aspx
go
    : GO (count = number)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/insert-transact-sql
insert
    : withClause? INSERT top? INTO? (tableName | rowSetFunction) (AS? alias)? withTableHint? columnNames? outputClause? (
        insertDefaultValue
        | insertValuesClause
        | select
        | execute
        | derivedTable
    )
    ;

insertDefaultValue
    : DEFAULT VALUES
    ;

insertValuesClause
    : VALUES assignmentValues (COMMA_ assignmentValues)*  optionQueryHintClause?
    ;

derivedTable
    : LP_ VALUES assignmentValues (COMMA_ assignmentValues)* RP_
    | subquery
    | LP_ merge RP_
    | LP_ RP_
    ;
// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/merge-transact-sql
merge
    : withClause? MERGE top? mergeIntoClause withTableHint? (AS? alias)? mergeUsingClause? mergeWhenClause* outputClause? optionQueryHintClause?
    ;

mergeIntoClause
    : INTO? tableReference
    ;

mergeUsingClause
    : USING (tableReference | LP_ tableReference RP_) ON expr
    ;

mergeWhenClause
    : mergeUpdateClause
    | mergeDeleteClause
    | mergeInsertClause
    ;

mergeUpdateClause
    : (WHEN MATCHED | WHEN NOT MATCHED BY SOURCE) (AND expr)? THEN UPDATE setAssignmentsClause
    ;

mergeDeleteClause
    : (WHEN MATCHED | WHEN NOT MATCHED BY SOURCE) (AND expr)? THEN DELETE
    ;

mergeInsertClause
    : WHEN NOT MATCHED (BY TARGET)? (AND expr)? THEN INSERT columnNames? (
        insertDefaultValue
        | VALUES assignmentValues (COMMA_ assignmentValues)*
    )
    ;

assignmentValues
    : LP_ assignmentValue (COMMA_ assignmentValue)* RP_
    | LP_ RP_
    ;

assignmentValue
    : DEFAULT | NULL | expr
    ;

withTableHint
    : WITH LP_ tableHintLimited (COMMA_ tableHintLimited)* RP_
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/execute-transact-sql
execute
    : (EXEC | EXECUTE) (returnStatus=identifier EQ_)? (executeObject | executeString)
    ;

executeString
    : LP_ stringOrIdentifier (PLUS_ stringOrIdentifier)* (COMMA_ (literals | DEFAULT | identifier OUTPUT?))* RP_
    (AS (LOGIN | USER) EQ_ stringLiterals)?
    (AT linkedServerName=identifier)?
    (AT DATA_SOURCE dataSourceName=identifier)?
    ;


executeObject
    : (procedureName | functionName) (executeParameter (COMMA_ executeParameter)*)? (WITH executeOption (COMMA_ executeOption)*)?
    ;

executeParameter
    : variableName (EQ_ executeParameterValue)?
    | executeParameterValue
    ;
executeParameterValue
    : identifier (OUTPUT | OUT)? | DEFAULT | literals
    ;

executeOption
    : RECOMPILE
    | RESULT SETS UNDEFINED
    | RESULT SETS NONE
    | RESULT SETS LP_ resultSetsDefinition (COMMA_ resultSetsDefinition)* RP_
    ;

resultSetsDefinition
    : LP_ resultColumnDef (COMMA_ resultColumnDef)* RP_
    | AS OBJECT (tableName |viewName | functionName)
    | AS TYPE typeName
    | AS FOR XML
    ;

resultColumnDef
    : columnName dataType (COLLATE collationName)? (NOT? NULL)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/queries/update-transact-sql
update
    : withClause? UPDATE top? (rowSetFunction | tableReference) withTableHint? setAssignmentsClause
     outputClause? fromClause? whereClause? optionQueryHintClause?
    ;

setAssignmentsClause
    : SET assignment (COMMA_ assignment)*
    ;

assignment
    : columnName (EQ_ columnName)? assignmentOperator assignmentValue
    | columnName DOT_ WRITE LP_ expr COMMA_ offset=writeArgumentValue  COMMA_ length=writeArgumentValue RP_
    | regularFunction
    ;

writeArgumentValue
    : number | nullValueLiterals
    ;

assignmentOperator
    : (PLUS_ | MINUS_ | ASTERISK_ | SLASH_ | MOD_ | CARET_ | AMPERSAND_ | VERTICAL_BAR_)? EQ_
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/delete-transact-sql
delete
    : withClause? DELETE top? deleteFromClause withTableHint? outputClause? fromClause? whereClause? optionQueryHintClause?
    ;

deleteFromClause
    : FROM? (tableName | rowSetFunction)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/queries/select-transact-sql
select
    : queryExpression
    ;

queryExpression
    : queryExpression combineType queryExpression
    | (querySpecification | parenthesisQueryExpression) orderByClause? forClause? optionQueryHintClause?
    ;
parenthesisQueryExpression
    : LP_ queryExpression RP_
    ;
combineType
    : UNION (ALL)? | EXCEPT | INTERSECT
    ;

querySpecification
    : selectWithClause? SELECT duplicateSpecification? top? projections intoClause? fromClause?
    whereClause? groupByClause? havingClause? windowClause?
    ;
duplicateSpecification
    : ALL
    | DISTINCT
    ;

projections
    : projection (COMMA_ projection)*
    ;

projection
    : qualifiedShorthand
    | unqualifiedShorthand
    | alias EQ_ (columnName | expr)
    | columnName ((DOT_ | DOUBLE_COLON_) regularFunction)? (AS? alias)?
    | expr (AS? alias)?
    ;

top
    : TOP LP_? topNum RP_? PERCENT? (WITH TIES)? (
        ROW_NUMBER LP_ RP_ OVER LP_ orderByClause RP_ (AS? alias)?
    )?
    ;

topNum
    : numberLiterals
    | parameterMarker
    ;

unqualifiedShorthand
    : ASTERISK_
    ;

qualifiedShorthand
    : (tableName | viewName | alias) DOT_ASTERISK_
    ;

intoClause
    : INTO tableName onFileGroup?
    ;

windowClause
    : WINDOW windowItemClause (COMMA_ windowItemClause)*
    ;

windowItemClause
    : windowName AS LP_ referenceWindowName=windowName? partitionByClause? orderByClause? rowRangeClause? RP_
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/queries/from-transact-sql
// https://learn.microsoft.com/zh-cn/sql/t-sql/queries/from-using-pivot-and-unpivot
fromClause
    : FROM tableReferences
    ;

tableReferences
    : tableReference (COMMA_ tableReference)*
    ;

tableReference
    : tableSource (joinedTable+ | pivotedTable | unpivotedTable)?
    ;

tableSource
    : tableName (FOR SYSTEM_TIME systemTime)? (AS? alias)? tablesampleClause? withTableHint?
    | tableName (AS? alias)? columnNames?
    | functionCall (AS? alias)? columnNames?
    | derivedTable (AS? alias)? columnNames?
    | expr (AS? alias)?
//    | LP_ tableReferences RP_
//    | withTempTable
    ;

//withTempTable
//    : WITH LP_ (columnName dataType) (COMMA_ columnName dataType)* RP_ AS alias
//    ;
joinedTable
    : joinType tableSource joinSpecification?
    | LP_ joinedTable RP_
    ;
joinType
    : ((INNER | (LEFT | RIGHT | FULL) OUTER?) joinHint?)? JOIN
    | CROSS JOIN
    | (CROSS | OUTER) APPLY
    ;

joinHint
    : REDUCE | REPLICATE | REDISTRIBUTE | LOOP | HASH | MERGE | REMOTE
    ;

joinSpecification
    : ON expr
//    | USING columnNames
    ;

pivotedTable
    : PIVOT LP_ aggregationFunction  FOR pivot_column=columnName IN columnNames RP_ (AS? alias)?
    ;

unpivotedTable
    : UNPIVOT LP_ value_column=columnName FOR pivot_column=columnName IN columnNames RP_ (AS? alias)?
    ;

systemTime
    : AS OF dateTime
    | FROM start=dateTime TO end=dateTime
    | BETWEEN start=dateTime AND end=dateTime
    | CONTAINED IN LP_ start=dateTime COMMA_ end=dateTime RP_
    | ALL
    ;

dateTime
    : dateTimeLiterals | stringLiterals | identifier
    ;

tablesampleClause
    : TABLESAMPLE SYSTEM? LP_ sample_number=numberLiterals (PERCENT | ROWS)? RP_ (REPEATABLE LP_ repeat_seed=numberLiterals RP_)?
    ;

whereClause
    : WHERE expr
    ;

groupByClause
    : GROUP BY groupByItem (COMMA_ groupByItem)*
    ;

groupByItem
    : rollipOrCube
    | groupingSets
    | columnName | numberLiterals | expr
    ;


groupingSets
    : GROUPING SETS LP_ groupingSetsItem (COMMA_ groupingSetsItem)* RP_
    ;

groupingSetsItem
    : rollipOrCube | expr | LP_ RP_
    ;

rollipOrCube
    : (ROLLUP | CUBE) LP_ expr (COMMA_ expr)* RP_
    ;


havingClause
    : HAVING expr
    ;

subquery
    : LP_ select RP_
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/queries/with-common-table-expression-transact-sql
withClause
    : WITH commonTableExpression (COMMA_ commonTableExpression)*
    ;

commonTableExpression
    : alias columnNames? AS subquery
    ;

outputClause
    : OUTPUT outputWithColumns (INTO outputTableName columnNames?)?
    ;

outputWithColumns
    : (outputWithColumn | scalarExpression) (COMMA_ (outputWithColumn | scalarExpression))*
    ;

scalarExpression
    : expr (AS? alias)?
    ;

outputWithColumn
    : (INSERTED | DELETED) DOT_ name (AS? alias)?
    | outputWithAaterisk
    ;

outputWithAaterisk
    : (INSERTED | DELETED) DOT_ASTERISK_
    ;

outputTableName
    : tableName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/queries/hints-transact-sql-query
queryHint
    : (HASH | ORDER) GROUP
    | (CONCAT | HASH | MERGE) UNION
    | (LOOP | MERGE | HASH) JOIN
    | DISABLE_OPTIMIZED_PLAN_FORCING
    | EXPAND VIEWS
    | FAST number
    | FORCE ORDER
    | (FORCE | DISABLE) EXTERNALPUSHDOWN
    | (FORCE | DISABLE) SCALEOUTEXECUTION
    | IGNORE_NONCLUSTERED_COLUMNSTORE_INDEX
    | KEEP PLAN
    | KEEPFIXED PLAN
    | MAX_GRANT_PERCENT EQ_ DECIMAL_NUM_
    | MIN_GRANT_PERCENT EQ_ DECIMAL_NUM_
    | MAXDOP number
    | MAXRECURSION number
    | NO_PERFORMANCE_SPOOL
    | OPTIMIZE FOR LP_ (optimizeParam (COMMA_ optimizeParam)*) RP_
    | OPTIMIZE FOR UNKNOWN
    | PARAMETERIZATION (SIMPLE | FORCED)
    | QUERYTRACEON number
    | RECOMPILE
    | ROBUST PLAN
    | USE HINT LP_ useHitName (COMMA_ useHitName)* RP_
    | USE PLAN NCHAR_TEXT
    | TABLE HINT LP_ tableName (COMMA_ tableHintLimited)*  RP_
    | FOR TIMESTAMP AS OF STRING_
    | LABEL EQ_ stringLiterals
    ;

optimizeParam
    : variableName (UNKNOWN | EQ_ literals)
    ;
useHitName
    : STRING_
    ;

forClause
    : FOR (BROWSE | forXmlClause | forJsonClause)
    ;

forXmlClause
    : XML (
        (
            RAW (LP_ stringLiterals RP_)? | AUTO) commonDirectivesForXml*
            (COMMA_ (XMLDATA | XMLSCHEMA (LP_ stringLiterals RP_)?))?
            (COMMA_ ELEMENTS (XSINIL | ABSENT)?
        )?
        | EXPLICIT commonDirectivesForXml* (COMMA_ XMLDATA)?
        | PATH (LP_ stringLiterals RP_)? commonDirectivesForXml* (COMMA_ ELEMENTS (XSINIL | ABSENT)?)?
    )
    ;

commonDirectivesForXml
    : COMMA_ (BINARY BASE64 | TYPE | ROOT (LP_ stringLiterals RP_)?)
    ;

forJsonClause
    : JSON (AUTO | PATH) (COMMA_ ROOT (LP_ stringLiterals RP_)?)? (COMMA_ INCLUDE_NULL_VALUES)? (COMMA_ WITHOUT_ARRAY_WRAPPER)?
    ;

selectWithClause
    : WITH (xmlNamespacesClause COMMA_?)? (commonTableExpression (COMMA_ commonTableExpression)*)?
    ;

xmlNamespacesClause
    : XMLNAMESPACES LP_ xmlNamespaceDeclarationItem (COMMA_ xmlNamespaceDeclarationItem)* RP_
    ;

xmlNamespaceDeclarationItem
    : xmlNamespaceUri AS xmlNamespacePrefix
    | xmlDefaultNamespaceDeclarationItem
    ;

xmlNamespaceUri
    : stringLiterals
    ;

xmlNamespacePrefix
    : identifier
    ;

xmlDefaultNamespaceDeclarationItem
    : DEFAULT xmlNamespaceUri
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/begin-transaction-transact-sql
beginTransaction
    : BEGIN (TRAN | TRANSACTION) (
        transactionName (WITH MARK stringLiterals?)?
    )?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/begin-distributed-transaction-transact-sql
beginDistributedTransaction
    : BEGIN DISTRIBUTED (TRAN | TRANSACTION) transactionName?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/commit-transaction-transact-sql
commit
    : COMMIT ((TRAN | TRANSACTION) transactionName?)? (WITH LP_ DELAYED_DURABILITY EQ_ onOffOption RP_)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/commit-work-transact-sql
commitWork
    : COMMIT WORK?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/rollback-transaction-transact-sql
rollback
    : ROLLBACK (TRAN | TRANSACTION) (transactionName | savepointName)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/rollback-work-transact-sql
rollbackWork
    : ROLLBACK WORK?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/save-transaction-transact-sql
savepoint
    : SAVE (TRAN | TRANSACTION) savepointName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/queries/explain-transact-sql
explain
    : EXPLAIN WITH_RECOMMENDATIONS? explainableStatement
    ;

explainableStatement
    : select
    | insert
    | update
    | delete
    | createTableAsSelectClause
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/grant-transact-sql
grant
    : GRANT (grantClassPrivilegesClause | grantClassTypePrivilegesClause)
    ;

grantClassPrivilegesClause
    : classPrivileges (ON onClassClause)? TO principal (COMMA_ principal)* (WITH GRANT OPTION)? (AS principal)?
    ;

grantClassTypePrivilegesClause
    : classTypePrivileges (ON onClassTypeClause)? TO principal (COMMA_ principal)* (WITH GRANT OPTION)?
    ;

classPrivileges
    : privilegeType columnNames? (COMMA_ privilegeType columnNames?)*
    ;

onClassClause
    : (classItem DOUBLE_COLON_)? securable columnNames?
    ;

classTypePrivileges
    : privilegeType (COMMA_ privilegeType)*
    ;

onClassTypeClause
    : (classType DOUBLE_COLON_)? securable
    ;

securable
    : (owner DOT_)? name
    ;

principal
    : userName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/revoke-transact-sql
revoke
    : REVOKE (optionForClause? revokeClassPrivilegesClause | revokeClassTypePrivilegesClause)
    ;

revokeClassPrivilegesClause
    : classPrivileges (ON onClassClause)? (TO | FROM) principal (COMMA_ principal)* (CASCADE)? (
        AS principal
    )?
    ;

revokeClassTypePrivilegesClause
    : classTypePrivileges (ON onClassTypeClause)? (TO | FROM) principal (COMMA_ principal)* (
        CASCADE
    )?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/deny-transact-sql
deny
    : DENY (denyClassPrivilegesClause | denyClassTypePrivilegesClause)
    ;

denyClassPrivilegesClause
    : classPrivileges (ON onClassClause)? TO principal (COMMA_ principal)* (CASCADE)? (
        AS principal
    )?
    ;

denyClassTypePrivilegesClause
    : classTypePrivileges (ON onClassTypeClause)? TO principal (COMMA_ principal)* (CASCADE)?
    ;

optionForClause
    : GRANT OPTION FOR
    ;

// SELECT DISTINCT '| ' + permission_name FROM sys.fn_builtin_permissions (DEFAULT) ORDER BY 1
privilegeType
    : ALL PRIVILEGES?
    | assemblyPermission
    | asymmetricKeyPermission
    | availabilityGroupPermission
    | certificatePermission
    | objectPermission
    | systemObjectPermission
    | databasePermission
    | databasePrincipalPermission
    | databaseScopedCredentialPermission
    | endpointPermission
    | fullTextPermission
    | schemaPermission
    | searchPropertyListPermission
    | serverPermission
    | serverPrincipalPermission
    | serviceBrokerPermission
    | symmetricKeyPermission
    | typePermission
    | xmlSchemaCollectionPermission
    ;

objectPermission
    : ALTER
    | CONTROL
    | DELETE
    | EXECUTE
    | INSERT
    | RECEIVE
    | REFERENCES
    | SELECT
    | TAKE OWNERSHIP
    | UPDATE
    | VIEW CHANGE TRACKING
    | VIEW DEFINITION
    ;

serverPermission
    : ADMINISTER BULK OPERATIONS
    | ALTER (RESOURCES | SETTINGS | TRACE | SERVER STATE)
    | ALTER ANY (
        AVAILABILITY GROUP
        | CONNECTION
        | CREDENTIAL
        | DATABASE
        | ENDPOINT
        | EVENT NOTIFICATION
        | EVENT SESSION
        | LINKED SERVER
        | LOGIN
        | SERVER AUDIT
        | SERVER ROLE
    )
    | AUTHENTICATE SERVER
    | CONNECT ANY DATABASE
    | CONNECT SQL
    | CONTROL SERVER
    | CREATE ANY DATABASE
    | CREATE (
        AVAILABILITY GROUP
        | DDL EVENT NOTIFICATION
        | ENDPOINT
        | SERVER ROLE
        | TRACE EVENT NOTIFICATION
    )
    | EXTERNAL ACCESS ASSEMBLY
    | IMPERSONATE ANY LOGIN
    | SELECT ALL USER SECURABLES
    | SHUTDOWN
    | UNSAFE ASSEMBLY
    | VIEW ANY (DATABASE | DEFINITION)
    | VIEW SERVER STATE
    ;

serverPrincipalPermission
    : CONTROL SERVER?
    | IMPERSONATE
    | VIEW ANY? DEFINITION
    | ALTER
    | ALTER ANY (LOGIN | SERVER ROLE)
    ;

databasePermission
    : ADMINISTER DATABASE BULK OPERATIONS
    | ALTER
    | ALTER TRACE
    | ALTER ANY (
        APPLICATION ROLE
        | ASSEMBLY
        | (SYMMETRIC | ASYMMETRIC | COLUMN ENCRYPTION) KEY
        | CERTIFICATE
        | CONNECTION
        | COLUMN MASTER KEY DEFINITION
        | CONTRACT
        | DATABASE (
            AUDIT
            | DDL TRIGGER
            | EVENT NOTIFICATION
            | EVENT SESSION
            | SCOPED CONFIGURATION
        )?
        | DATASPACE
        | EVENT (NOTIFICATION | SESSION)
        | EXTERNAL (DATA SOURCE | FILE FORMAT | LIBRARY)
        | FULLTEXT CATALOG
        | MASK
        | MESSAGE TYPE
        | REMOTE SERVICE BINDING
        | ROLE
        | ROUTE
        | SERVER AUDIT
        | SCHEMA
        | SECURITY POLICY
        | SERVICE
        | USER
    )
    | AUTHENTICATE SERVER?
    | BACKUP (DATABASE | LOG)
    | CHECKPOINT
    | CONNECT
    | CONNECT REPLICATION?
    | CONTROL SERVER?
    | CREATE (
        AGGREGATE
        | ASSEMBLY
        | (SYMMETRIC | ASYMMETRIC) KEY
        | CERTIFICATE
        | CONTRACT
        | DATABASE
        | DATABASE? DDL EVENT NOTIFICATION
        | DEFAULT
        | EXTERNAL LIBRARY
        | FULLTEXT CATALOG
        | FUNCTION
        | MESSAGE TYPE
        | PROCEDURE
        | QUEUE
        | REMOTE SERVICE BINDING
        | ROLE
        | ROUTE
        | RULE
        | SCHEMA
        | SERVICE
        | SYNONYM
        | TABLE
        | TYPE
        | VIEW
        | XML SCHEMA COLLECTION
    )
    | DELETE
    | EXECUTE
    | EXECUTE ANY? EXTERNAL SCRIPT
    | INSERT
    | KILL DATABASE CONNECTION
    | REFERENCES
    | SELECT
    | SHOWPLAN
    | SUBSCRIBE QUERY NOTIFICATIONS
    | TAKE OWNERSHIP
    | UNMASK
    | UPDATE
    | VIEW ANY COLUMN (MASTER | ENCRYPTION) KEY DEFINITION
    | CREATE ANY (DATABASE | EXTERNAL LIBRARY)
    | VIEW (DATABASE | SERVER) STATE
    | VIEW ANY? DEFINITION
    |
    ;

databasePrincipalPermission
    : databaseUserPermission
    | databaseRolePermission
    | applicationRolePermission
    ;

databaseUserPermission
    : CONTROL
    | IMPERSONATE
    | ALTER
    | VIEW DEFINITION
    | ALTER ANY USER
    ;

databaseRolePermission
    : CONTROL
    | TAKE OWNERSHIP
    | ALTER
    | VIEW DEFINITION
    | ALTER ANY ROLE
    ;

applicationRolePermission
    : CONTROL
    | ALTER
    | VIEW DEFINITION
    | ALTER ANY APPLICATION ROLE
    ;

databaseScopedCredentialPermission
    : CONTROL
    | TAKE OWNERSHIP
    | ALTER
    | REFERENCES
    | VIEW DEFINITION
    ;

schemaPermission
    : ALTER
    | CONTROL
    | CREATE SEQUENCE
    | DELETE
    | EXECUTE
    | INSERT
    | REFERENCES
    | SELECT
    | TAKE OWNERSHIP
    | UPDATE
    | VIEW CHANGE TRACKING
    | VIEW DEFINITION
    | ALTER ANY SCHEMA
    ;

searchPropertyListPermission
    : ALTER
    | CONTROL
    | REFERENCES
    | TAKE OWNERSHIP
    | VIEW DEFINITION
    | ALTER ANY FULLTEXT CATALOG
    ;

serviceBrokerPermission
    : serviceBrokerContractsPermission
    | serviceBrokerMessageTypesPermission
    | serviceBrokerRemoteServiceBindingsPermission
    | serviceBrokerRoutesPermission
    | serviceBrokerServicesPermission
    ;

serviceBrokerContractsPermission
    : CONTROL
    | TAKE OWNERSHIP
    | ALTER
    | REFERENCES
    | VIEW DEFINITION
    | ALTER ANY CONTRACT
    ;

serviceBrokerMessageTypesPermission
    : CONTROL
    | TAKE OWNERSHIP
    | ALTER
    | REFERENCES
    | VIEW DEFINITION
    | ALTER ANY MESSAGE TYPE
    ;

serviceBrokerRemoteServiceBindingsPermission
    : CONTROL
    | TAKE OWNERSHIP
    | ALTER
    | VIEW DEFINITION
    | ALTER ANY REMOTE SERVICE BINDING
    ;

serviceBrokerRoutesPermission
    : CONTROL
    | TAKE OWNERSHIP
    | ALTER
    | VIEW DEFINITION
    | ALTER ANY ROUTE
    ;

serviceBrokerServicesPermission
    : CONTROL
    | TAKE OWNERSHIP
    | SEND
    | ALTER
    | VIEW DEFINITION
    | ALTER ANY SERVICE
    ;

endpointPermission
    : ALTER
    | CONNECT
    | CONTROL SERVER?
    | TAKE OWNERSHIP
    | VIEW ANY? DEFINITION
    | ALTER ANY ENDPOINT
    ;

certificatePermission
    : CONTROL
    | TAKE OWNERSHIP
    | ALTER
    | REFERENCES
    | VIEW DEFINITION
    | ALTER ANY CERTIFICATE
    ;

symmetricKeyPermission
    : ALTER
    | CONTROL
    | REFERENCES
    | TAKE OWNERSHIP
    | VIEW DEFINITION
    | ALTER ANY SYMMETRIC KEY
    ;

asymmetricKeyPermission
    : CONTROL
    | TAKE OWNERSHIP
    | ALTER
    | REFERENCES
    | VIEW DEFINITION
    | ALTER ANY ASYMMETRIC KEY
    ;

assemblyPermission
    : CONTROL
    | TAKE OWNERSHIP
    | ALTER
    | REFERENCES
    | VIEW DEFINITION
    | ALTER ANY ASSEMBLY
    ;

availabilityGroupPermission
    : ALTER
    | CONNECT
    | CONTROL SERVER?
    | TAKE OWNERSHIP
    | VIEW ANY? DEFINITION
    | ALTER ANY AVAILABILITY GROUP
    ;

fullTextPermission
    : fullTextCatalogPermission
    | fullTextStoplistPermission
    ;

fullTextCatalogPermission
    : CONTROL
    | TAKE OWNERSHIP
    | ALTER
    | REFERENCES
    | VIEW DEFINITION
    | ALTER ANY FULLTEXT CATALOG
    ;

fullTextStoplistPermission
    : ALTER
    | CONTROL
    | REFERENCES
    | TAKE OWNERSHIP
    | VIEW DEFINITION
    | ALTER ANY FULLTEXT CATALOG
    ;

typePermission
    : CONTROL
    | EXECUTE
    | REFERENCES
    | TAKE OWNERSHIP
    | VIEW DEFINITION
    ;

xmlSchemaCollectionPermission
    : ALTER
    | CONTROL
    | EXECUTE
    | REFERENCES
    | TAKE OWNERSHIP
    | VIEW DEFINITION
    ;

systemObjectPermission
    : SELECT
    | EXECUTE
    ;

classItem
    : ASSEMBLY
    | ASYMMETRIC KEY
    | AVAILABILITY GROUP
    | CERTIFICATE
    | USER
    | ROLE
    | APPLICATION ROLE
    | DATABASE SCOPED CREDENTIAL
    | ENDPOINT
    | FULLTEXT (CATALOG | STOPLIST)
    | OBJECT
    | SCHEMA
    | SEARCH PROPERTY LIST
    | LOGIN
    | SERVER ROLE
    | CONTRACT
    | MESSAGE TYPE
    | REMOTE SERVICE BINDING
    | ROUTE
    | SERVICE
    | SYMMETRIC KEY
    | SELECT
    | EXECUTE
    | TYPE
    | XML SCHEMA COLLECTION
    | EXTERNAL LANGUAGE
    ;

classType
    : LOGIN
    | DATABASE
    | OBJECT
    | ROLE
    | SCHEMA
    | USER
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/setuser-transact-sql
setUser
    : SETUSER (stringLiterals (WITH NORESET)?)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-user-transact-sql
createUser
    : CREATE USER (
        createUserLoginClause
        | createUserWithPasswordClause
        | createUserFromExternalProvider
        | createUserCannotAuthenticate
    )?
    ;

createUserLoginClause
    : userName ((FOR | FROM) LOGIN loginName=identifier)? (WITH optionsList (COMMA_ optionsList)*)?
    ;

createUserWithPasswordClause
    : userName WITH PASSWORD EQ_ stringLiterals (COMMA_ optionsList (COMMA_ optionsList)*)?
    ;

createUserFromExternalProvider
    : userName FROM EXTERNAL PROVIDER ((WITH OBJECT_ID EQ_ stringLiterals) | (WITH optionsList (COMMA_ optionsList)*))?
    ;

createUserCannotAuthenticate
    : userName (
        WITHOUT LOGIN (WITH optionsList (COMMA_ optionsList)*)?
        | (FOR | FROM) CERTIFICATE identifier
        | (FOR | FROM) ASYMMETRIC KEY identifier
    )
    ;


optionsList
    : DEFAULT_SCHEMA EQ_ schemaName
    | DEFAULT_LANGUAGE EQ_ (NONE | identifier)
    | SID EQ_ sid
    | ALLOW_ENCRYPTED_VALUE_MODIFICATIONS EQ_ onOffOption?
    ;

userName
    : ignoredNameIdentifier
    ;

ignoredNameIdentifier
    : identifier (DOT_ identifier)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-user-transact-sql
dropUser
    : DROP USER ifExists? userName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-user-transact-sql
alterUser
    : ALTER USER userName (WITH setItem (COMMA_ setItem)* | FROM EXTERNAL PROVIDER)
    ;

setItem
    : NAME EQ_ userName
    | DEFAULT_SCHEMA EQ_ (schemaName | NULL)
    | LOGIN EQ_ userName
    | PASSWORD EQ_ stringLiterals (OLD_PASSWORD EQ_ stringLiterals)?
    | DEFAULT_LANGUAGE EQ_ (NONE | identifier)
    | ALLOW_ENCRYPTED_VALUE_MODIFICATIONS EQ_ onOffOption?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-role-transact-sql
createRole
    : CREATE ROLE name (AUTHORIZATION owner)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-role-transact-sql
dropRole
    : DROP ROLE ifExists? name
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-role-transact-sql
alterRole
    : ALTER ROLE roleName=name (ADD MEMBER principal | DROP MEMBER principal | WITH NAME EQ_ name)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-login-transact-sql
createLogin
    : CREATE LOGIN ignoredNameIdentifier (WITH createLoginOptionList | FROM sources)
    ;

createLoginOptionList
    : PASSWORD EQ_ (stringLiterals | hexadecimalLiterals HASHED) MUST_CHANGE?
    (COMMA_ createLoginOptionList2)*
    ;

createLoginOptionList2
    : SID EQ_ sid
    | DEFAULT_DATABASE EQ_ databaseName
    | DEFAULT_LANGUAGE EQ_ identifier
    | CHECK_EXPIRATION EQ_ onOffOption
    | CHECK_POLICY EQ_ onOffOption
    | CREDENTIAL EQ_ identifier
    ;

sid
    : NCHAR_TEXT | HEX_DIGIT_
    ;

sources
    : WINDOWS (WITH windowsOptions (COMMA_ windowsOptions)*)?
    | EXTERNAL PROVIDER
    | CERTIFICATE identifier
    | ASYMMETRIC KEY identifier
    ;

windowsOptions
    : DEFAULT_DATABASE EQ_ databaseName
    | DEFAULT_LANGUAGE EQ_ identifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-login-transact-sql
dropLogin
    : DROP LOGIN ignoredNameIdentifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-login-transact-sql
alterLogin
    : ALTER LOGIN ignoredNameIdentifier (
        statusOptionClause
        | WITH setOptionClause (COMMA_ setOptionClause)*
        | cryptographicCredentialsOptionClause
    )
    ;

statusOptionClause
    : ENABLE
    | DISABLE
    ;

setOptionClause
    : PASSWORD EQ_ (stringLiterals | hexadecimalLiterals HASHED) (
        OLD_PASSWORD EQ_ stringLiterals
        | passwordOptionClause passwordOptionClause?
    )?
    | DEFAULT_DATABASE EQ_ databaseName
    | DEFAULT_LANGUAGE EQ_ identifier
    | NAME EQ_ ignoredNameIdentifier
    | CHECK_POLICY EQ_ onOffOption
    | CHECK_EXPIRATION EQ_ onOffOption
    | CREDENTIAL EQ_ identifier
    | NO CREDENTIAL
    ;

passwordOptionClause
    : MUST_CHANGE
    | UNLOCK
    ;

cryptographicCredentialsOptionClause
    : ADD CREDENTIAL identifier
    | DROP CREDENTIAL identifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/revert-transact-sql
revert
    : REVERT (WITH COOKIE EQ_ variableName)?
    ;

createTable
    : createTableClause
    | createTableAsSelectClause
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-table-transact-sql
// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-table-sql-graph
createTableClause
    : CREATE TABLE tableName (
    fileTableClause? createDefinitionClause fileGroup
    | createDefinitionClause? AS (NODE | EDGE)? fileGroup
    )
    ;

createTableAsSelectClause
    : createTableAsSelect
    | createRemoteTableAsSelect
    ;

createTableAsSelect
    : CREATE TABLE tableName columnNames? withDistributionOption AS select optionQueryHintClause?
    ;

createRemoteTableAsSelect
    : CREATE REMOTE TABLE tableName AT LP_ stringLiterals RP_ (
        WITH LP_ BATCH_SIZE EQ_ number RP_
    )? AS select
    ;

withDistributionOption
    : WITH LP_ distributionOption (COMMA_ tableOption (COMMA_ tableOption)*)? RP_
    ;

fileTableClause
    : AS FILETABLE
    ;

createDefinitionClause
    : createTableDefinitions partitionScheme?
    ;

createTableDefinitions
    : LP_ createTableDefinition (COMMA_ createTableDefinition)* (COMMA_ periodClause)? RP_
    ;

createTableDefinition
    : columnDefinition
    | computedColumnDefinition
    | columnSetDefinition
    | tableConstraint
    | tableIndex
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-index-transact-sql
createIndex
    : CREATE UNIQUE? clusterOption? INDEX indexName ON tableName columnNamesWithSort createIndexClause
    ;

createIndexClause
    : (INCLUDE columnNames)? (WHERE expr)? withIndexOption? indexOnClause? fileStreamOn?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-database-transact-sql
createDatabase
    : CREATE DATABASE databaseName createDatabaseClause
    ;

// https://msdn.microsoft.com/zh-cn/library/ms186755.aspx
createFunction
    : CREATE (OR ALTER)? FUNCTION functionName funcParameters funcReturns
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-function-transact-sql
alterFunction
    : ALTER FUNCTION functionName funcParameters funcReturns
    ;

funcParameters
    : LP_ (funcParameter (COMMA_ funcParameter)*)? RP_
    ;

funcParameter
    : variableName AS? (owner DOT_)? dataType (NOT? NULL)? (EQ_ ignoredIdentifier)? READONLY?
    ;

funcReturns
    : funcScalarReturn
    | funcInlineReturn
    | funcMutiReturn
    | funcCLRScalarReturn
    | funcUserDefinedReturn
    ;

funcMutiReturn
    : RETURNS variableName TABLE createTableDefinitions (
        WITH functionOption (COMMA_ functionOption)*
    )? AS? BEGIN compoundStatement RETURN END
    ;

funcInlineReturn
    : RETURNS TABLE functionOptionClause? AS? RETURN (select | subquery)
    ;

funcScalarReturn
    : RETURNS dataType functionOptionClause? AS? BEGIN compoundStatement RETURN expr SEMI_? END
    ;

funcCLRScalarReturn
    : RETURNS dataType functionOptionClause? AS? EXTERNAL NAME methodSpecifier
    ;

funcUserDefinedReturn
    : RETURNS dataType functionOptionClause? AS? BEGIN ATOMIC WITH procSetOption (COMMA_ procSetOption)* compoundStatement RETURN expr END
    ;

functionOptionClause
    : WITH functionOption (COMMA_ functionOption)*
    ;

compoundStatement
    : statement*
    ;

functionOption
    : ENCRYPTION
    | SCHEMABINDING
    | NATIVE_COMPILATION
    | RETURNS NULL ON NULL INPUT
    | CALLED ON NULL INPUT
    | executeAsClause
    | INLINE EQ_ ( ON | OFF)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-procedure-transact-sql
createProcedure
    : CREATE (OR ALTER)? (PROC | PROCEDURE) procedureName procParameters? createOrAlterProcClause
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-procedure-transact-sql
alterProcedure
    : ALTER (PROC | PROCEDURE) procedureName procParameters? createOrAlterProcClause
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-procedure-transact-sql
dropProcedure
    : DROP (PROC | PROCEDURE) ifExists? procedureName (COMMA_ procedureName)*
    ;

procParameters
    : procParameter (COMMA_ procParameter)*
    | LP_ procParameter (COMMA_ procParameter)* RP_
    ;

procParameter
    : variableName dataType VARYING? (NOT? NULL)? (EQ_ literals)? (OUT | OUTPUT | READONLY)?
    ;

createOrAlterProcClause
    : withCreateProcOption? (FOR REPLICATION)? AS procAsClause
    ;

withCreateProcOption
    : WITH (procOption (COMMA_ procOption)*)?
    ;

procOption
    : ENCRYPTION
    | RECOMPILE
    | executeAsClause
    | NATIVE_COMPILATION
    | SCHEMABINDING
    ;

procAsClause
    : BEGIN? compoundStatement END?
    | EXTERNAL NAME methodSpecifier
    | BEGIN ATOMIC WITH LP_ procSetOption (COMMA_ procSetOption)* RP_ compoundStatement END?
    ;

procSetOption
    : LANGUAGE EQ_ stringLiterals
    | TRANSACTION ISOLATION LEVEL EQ_ ( SNAPSHOT | REPEATABLE READ | SERIALIZABLE)
    | DATEFIRST EQ_ numberLiterals
    | DATEFORMAT EQ_ stringLiterals
    | DELAYED_DURABILITY EQ_ onOffOption
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-view-transact-sql
createView
    : CREATE (OR ALTER)? VIEW viewName createOrAlterViewClause
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-view-transact-sql
alterView
    : ALTER VIEW viewName createOrAlterViewClause
    ;

createOrAlterViewClause
    : (WITH viewAttribute (COMMA_ viewAttribute)*)? AS withClause? select (
        WITH CHECK OPTION
    )?
    ;

viewAttribute
    : ENCRYPTION
    | SCHEMABINDING
    | VIEW_METADATA
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-trigger-transact-sql
createTrigger
    : CREATE (OR ALTER)? TRIGGER triggerName ON triggerTarget createTriggerClause
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-trigger-transact-sql
alterTrigger
    : ALTER TRIGGER triggerName ON triggerTarget createTriggerClause
    ;

triggerTarget
    : tableName
    | viewName
    | ALL SERVER
    | DATABASE
    ;

createTriggerClause
    : (WITH dmlTriggerOption (COMMA_ dmlTriggerOption)*)? (FOR | AFTER | INSTEAD OF)
        triggerOperation (COMMA_ triggerOperation)*  (WITH APPEND)? (NOT FOR REPLICATION)?
        AS (compoundStatement | EXTERNAL NAME methodSpecifier)
    ;

triggerOperation
    : INSERT | UPDATE | DELETE | eventTypeOrGroup
    ;

dmlTriggerOption
    : ENCRYPTION
    | executeAsClause
    | NATIVE_COMPILATION
    | SCHEMABINDING
    ;

methodSpecifier
    : (assemblyName=name DOT_)? (className=name DOT_)? methodName=name
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-queue-transact-sql
createQueue
    : CREATE QUEUE queueName queueSettings? (ON filegroup=identifier | DEFAULT)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-queue-transact-sql
alterQueue
    : ALTER QUEUE queueName (queueSettings | queueAction)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-queue-transact-sql
dropQueue
    : DROP QUEUE queueName
    ;

queueSettings
    : WITH (STATUS EQ_ onOffOption COMMA_?)? (RETENTION EQ_ onOffOption COMMA_?)? (
        ACTIVATION LP_ (
            (
                (STATUS EQ_ onOffOption COMMA_?)?
                (PROCEDURE_NAME EQ_ procedureName COMMA_?)?
                (MAX_QUEUE_READERS EQ_ number COMMA_?)?
                (EXECUTE AS (SELF | OWNER | stringLiterals) COMMA_?)?
            )
            | DROP
        ) RP_ COMMA_?
    )? (POISON_MESSAGE_HANDLING LP_ (STATUS EQ_ onOffOption)? RP_)?
    ;

queueAction
    : REBUILD (WITH LP_ MAXDOP EQ_ number RP_)?
    | REORGANIZE (WITH LOB_COMPACTION EQ_ onOffOption)?
    | MOVE TO filegroup=identifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-sequence-transact-sql
createSequence
    : CREATE SEQUENCE sequenceName createOrAlterSequenceClause*
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-service-transact-sql
createService
    : CREATE SERVICE serviceName (AUTHORIZATION stringLiterals)? ON QUEUE queueName createServiceClause?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-schema-transact-sql
createSchema
    : CREATE SCHEMA schemaNameClause schemaElement*
    ;

schemaNameClause
    : schemaName
    | AUTHORIZATION owner
    | schemaName AUTHORIZATION owner
    ;

schemaElement
    : createTable
    | createView
    | grant
    | revoke
    | deny
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-table-transact-sql
alterTable
    : ALTER TABLE tableName alterDefinitionClause (COMMA_ alterDefinitionClause)*
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-index-transact-sql
alterIndex
    : ALTER INDEX (indexName | ALL) ON tableName alterIndexClause
    ;

alterIndexClause
    : REBUILD (PARTITION EQ_ (ALL | expr))? (
        WITH LP_ indexOption (COMMA_ indexOption)* RP_
    )?
    | DISABLE
    | REORGANIZE (PARTITION EQ_ expr)? (WITH LP_ reorganizeOption RP_)?
    | SET LP_ setIndexOption (COMMA_ setIndexOption)* RP_
    | RESUME (WITH LP_ resumableIndexOptions (COMMA_ resumableIndexOptions)* RP_)?
    | PAUSE
    | ABORT
    ;

reorganizeOption
    : LOB_COMPACTION EQ_ onOffOption
    | COMPRESS_ALL_ROW_GROUPS EQ_ onOffOption
    ;

setIndexOption
    : ALLOW_ROW_LOCKS EQ_ onOffOption
    | ALLOW_PAGE_LOCKS EQ_ onOffOption
    | OPTIMIZE_FOR_SEQUENTIAL_KEY EQ_ onOffOption
    | IGNORE_DUP_KEY EQ_ onOffOption
    | STATISTICS_NORECOMPUTE EQ_ onOffOption
    | COMPRESSION_DELAY EQ_ (expr MINUTES?)
    ;

resumableIndexOptions
    : MAXDOP EQ_ expr
    | MAX_DURATION EQ_ expr MINUTES?
    | lowPriorityLockWait
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-database-transact-sql
alterDatabase
    : ALTER DATABASE (databaseName | CURRENT) alterDatabaseClause*
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-sequence-transact-sql
alterSequence
    : ALTER SEQUENCE sequenceName createOrAlterSequenceClause*
    ;

createOrAlterSequenceClause
    : AS dataType
    | (START | RESTART) WITH expr
    | INCREMENT BY expr
    | MINVALUE expr?
    | NO MINVALUE
    | MAXVALUE expr?
    | NO MAXVALUE
    | CACHE expr
    | NO CACHE
    | NO? CYCLE
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-service-transact-sql
alterService
    : ALTER SERVICE serviceName (ON QUEUE queueName)? alterServiceClause?
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-schema-transact-sql
alterSchema
    : ALTER SCHEMA schemaName TRANSFER (identifier DOUBLE_COLON_)? securableName
    ;

securableName
    : identifier (DOT_ identifier)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-table-transact-sql
dropTable
    : DROP TABLE ifExists? tableName (COMMA_ tableName)*
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-index-transact-sql
dropIndex
    : DROP INDEX ifExists? dropIndexClause (COMMA_ dropIndexClause)*
    ;

dropIndexClause
    : indexName (ON tableName (WITH LP_ dropClusteredIndexOption (COMMA_ dropClusteredIndexOption)* RP_)?)?
    ;

dropClusteredIndexOption
    : MAXDOP EQ_ number
    | ONLINE EQ_ onOffOption
    | MOVE TO (schemaName LP_ columnName RP_ | stringOrIdentifier)
    | FILESTREAM_ON stringOrIdentifier
    ;

dropDatabase
    : DROP DATABASE ifExists? databaseName (COMMA_ databaseName)*
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-function-transact-sql
dropFunction
    : DROP FUNCTION ifExists? functionName (COMMA_ functionName)*
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-view-transact-sql
dropView
    : DROP VIEW ifExists? viewName (COMMA_ viewName)*
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-trigger-transact-sql
dropTrigger
    : DROP TRIGGER ifExists? triggerName (COMMA_ triggerName)* (ON (DATABASE | ALL SERVER))?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-sequence-transact-sql
dropSequence
    : DROP SEQUENCE ifExists? sequenceName (COMMA_ sequenceName)*
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-service-transact-sql
dropService
    : DROP SERVICE serviceName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-schema-transact-sql
dropSchema
    : DROP SCHEMA ifExists? schemaName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/truncate-table-transact-sql
truncateTable
    : TRUNCATE TABLE tableName (WITH LP_ PARTITIONS LP_ partitionExpressions RP_ RP_)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-statistics-transact-sql
createStatistics
    : CREATE STATISTICS statisticsName ON (tableName | indexName) columnNames
    (WHERE expr)?
    statisticsWithClause?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-statistics-transact-sql
dropStatistics
    : DROP STATISTICS statisticsName (COMMA_ statisticsName)*
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/update-statistics-transact-sql
updateStatistics
    : UPDATE STATISTICS (tableName | viewName)
    (LP_ statisticsName (COMMA_ statisticsName)* RP_
    | statisticsName
    )? statisticsWithClause?
    ;

statisticsName
    : (owner DOT_)* name
    ;

statisticsWithClause
    : WITH statisticsOption (COMMA_ statisticsOption)*
    ;

statisticsOption
    : FULLSCAN
    | SAMPLE number (PERCENT | ROWS)
    | PERSIST_SAMPLE_PERCENT EQ_ (ON | OFF)
    | RESAMPLE onPartitionClause?
    | STATS_STREAM EQ_ hexadecimalLiterals
    | ROWCOUNT EQ_ number
    | PAGECOUNT EQ_ number
    | ALL
    | COLUMNS
    | INDEX
    | NORECOMPUTE
    | INCREMENTAL EQ_ onOffOption
    | MAXDOP EQ_ number
    | AUTO_DROP EQ_ onOffOption
    ;

columnDefinition
    : columnName dataType columnDefinitionOption*
    ;

columnDefinitionOption
    : FILESTREAM
    | COLLATE collationName
    | SPARSE
    | MASKED WITH LP_ FUNCTION EQ_ stringLiterals RP_
    | (CONSTRAINT ignoredIdentifier)? DEFAULT expr (WITH VALUES)?
    | IDENTITY (LP_ number COMMA_ number RP_)?
    | NOT FOR REPLICATION
    | GENERATED ALWAYS AS (ROW | TRANSACTION_ID | SEQUENCE_NUMBER) (START | END) HIDDEN_?
    | (CONSTRAINT ignoredIdentifier)? NOT? NULL
    | ROWGUIDCOL
    | ENCRYPTED WITH encryptedOptions
    | columnConstraint (COMMA_ columnConstraint)*
    | columnIndex
    ;

encryptedOptions
    : LP_ COLUMN_ENCRYPTION_KEY EQ_ identifier COMMA_ ENCRYPTION_TYPE EQ_ (
        DETERMINISTIC
        | RANDOMIZED
    ) COMMA_ ALGORITHM EQ_ stringLiterals RP_
    ;

columnConstraint
    : (CONSTRAINT constraintName)? (
        primaryKeyConstraint
        | columnForeignKeyConstraint
        | checkConstraint
    )
    ;

computedColumnConstraint
    : (CONSTRAINT constraintName)? (
        primaryKeyConstraint
        | computedColumnForeignKeyConstraint
        | checkConstraint
    )
    ;

computedColumnForeignKeyConstraint
    : (FOREIGN KEY)? tableName (LP_ columnName RP_)? computedColumnForeignKeyOnAction*
    ;

computedColumnForeignKeyOnAction
    : ON DELETE (NO ACTION | CASCADE)
    | ON UPDATE NO ACTION
    | NOT FOR REPLICATION
    ;

primaryKeyConstraint
    : (primaryKey | UNIQUE) (
        diskTablePrimaryKeyConstraintOption
        | memoryTablePrimaryKeyConstraintOption
    )
    ;

diskTablePrimaryKeyConstraintOption
    : clusterOption? columnNames? primaryKeyWithClause? primaryKeyOnClause?
    ;

clusterOption
    : CLUSTERED
    | NONCLUSTERED
    ;

primaryKeyWithClause
    : WITH (FILLFACTOR EQ_ number | LP_ indexOption (COMMA_ indexOption)* RP_)
    ;

primaryKeyOnClause
    : onSchemaColumn
    | onFileGroup
    ;

onSchemaColumn
    : ON schemaName LP_ columnName RP_
    ;

onFileGroup
    : ON stringOrIdentifier
    ;


memoryTablePrimaryKeyConstraintOption
    : NONCLUSTERED
    | NONCLUSTERED HASH withBucket?
    ;

withBucket
    : WITH LP_ BUCKET_COUNT EQ_ number RP_
    ;

columnForeignKeyConstraint
    : (FOREIGN KEY)? REFERENCES tableName (LP_ columnName RP_)? foreignKeyOnAction*
    ;

foreignKeyOnAction
    : ON (DELETE | UPDATE) foreignKeyOn
    | NOT FOR REPLICATION
    ;

foreignKeyOn
    : NO ACTION
    | CASCADE
    | SET (NULL | DEFAULT)
    ;

checkConstraint
    : CHECK (NOT FOR REPLICATION)? LP_ expr RP_
    ;

columnIndex
    : INDEX indexName clusterOption? withIndexOption? indexOnClause? fileStreamOn?
    ;

withIndexOption
    : WITH LP_ indexOption (COMMA_ indexOption)* RP_
    ;

indexOnClause
    : onSchemaColumn
    | onDefault
    | onFileGroup
    ;

onDefault
    : ON DEFAULT
    ;

fileStreamOn
    : FILESTREAM_ON (schemaName | stringOrIdentifier)
    ;

computedColumnDefinition
    : columnName AS expr (PERSISTED (NOT NULL)?)? computedColumnConstraint?
    ;

columnSetDefinition
    : ignoredIdentifier XML COLUMN_SET FOR ALL_SPARSE_COLUMNS
    ;

tableConstraint
    : (CONSTRAINT constraintName)? (
        tablePrimaryConstraint
        | tableForeignKeyConstraint
        | checkConstraint
        | edgeConstraint
    )
    ;

edgeConstraint
    : connectionClause (ON DELETE (NO ACTION | CASCADE))?
    ;

connectionClause
    : CONNECTION LP_ (nodeAlias TO nodeAlias) (COMMA_ nodeAlias TO nodeAlias)*? RP_
    ;

tablePrimaryConstraint
    : primaryKeyUnique (diskTablePrimaryConstraintOption | memoryTablePrimaryConstraintOption)
    ;

primaryKeyUnique
    : primaryKey
    | UNIQUE
    ;

diskTablePrimaryConstraintOption
    : clusterOption? columnNames primaryKeyWithClause? primaryKeyOnClause?
    ;

memoryTablePrimaryConstraintOption
    : NONCLUSTERED (columnNames | hashWithBucket)
    ;

hashWithBucket
    : HASH columnNames withBucket
    ;

tableForeignKeyConstraint
    : (FOREIGN KEY)? columnNames REFERENCES tableName columnNames foreignKeyOnAction*
    ;

tableIndex
    : INDEX indexName indexNameOption createIndexClause
    ;

indexNameOption
    : UNIQUE? clusterOption? columnNamesWithSort
    | CLUSTERED COLUMNSTORE (ORDER columnNames)?
    | NONCLUSTERED? COLUMNSTORE columnNames
    ;

periodClause
    : PERIOD FOR SYSTEM_TIME LP_ columnName COMMA_ columnName RP_
    ;

partitionScheme
    : ON (schemaName LP_ columnName RP_ | ignoredIdentifier | STRING_)
    ;

fileGroup
    : (ON stringOrIdentifier)?
    (TEXTIMAGE_ON stringOrIdentifier)?
    (FILESTREAM_ON (schemaName | stringOrIdentifier))?
    (WITH tableOptions)?
    ;

tableOptions
    : LP_ tableOption (COMMA_ tableOption)* RP_
    ;

tableOption
    : DATA_COMPRESSION EQ_ (NONE | ROW | PAGE) onPartitionClause?
    | XML_COMPRESSION EQ_ onOffOption onPartitionClause?
    | FILETABLE_DIRECTORY EQ_ ignoredIdentifier
    | FILETABLE_COLLATE_FILENAME EQ_ collationName
    | FILETABLE_PRIMARY_KEY_CONSTRAINT_NAME EQ_ ignoredIdentifier
    | FILETABLE_STREAMID_UNIQUE_CONSTRAINT_NAME EQ_ ignoredIdentifier
    | FILETABLE_FULLPATH_UNIQUE_CONSTRAINT_NAME EQ_ ignoredIdentifier
    | SYSTEM_VERSIONING EQ_ ON onHistoryTableClause?
    | REMOTE_DATA_ARCHIVE EQ_ (ON tableStretchOptions? | OFF migrationState_)
    | dataDelectionOption
    | LEDGER EQ_ (ON (LP_ ledgerOption (COMMA_ ledgerOption)* RP_)?| OFF)
    | tableOperationOption
    | distributionOption
    | dataWareHouseTableOption
    | dataWareHousePartitionOption
    ;

ledgerOption
    : LEDGER_VIEW EQ_ viewName (LP_ ledgerViewOption (COMMA_ ledgerViewOption)* RP_)?
    (COMMA_? APPEND_ONLY EQ_ onOffOption)?
    ;

ledgerViewOption
    : TRANSACTION_ID_COLUMN_NAME EQ_ name
    | SEQUENCE_NUMBER_COLUMN_NAME EQ_ name
    | OPERATION_TYPE_COLUMN_NAME EQ_ name
    | OPERATION_TYPE_DESC_COLUMN_NAME EQ_ name
    ;

dataDelectionOption
    : DATA_DELETION EQ_ ON LP_ FILTER_COLUMN EQ_ columnName COMMA_ RETENTION_PERIOD EQ_ historyRetentionPeriod RP_
    ;

tableStretchOptions
    : LP_ tableStretchOption (COMMA_ tableStretchOption)* RP_
    ;

tableStretchOption
    : (FILTER_PREDICATE EQ_ (NULL | functionCall) COMMA_)? MIGRATION_STATE EQ_ (
        OUTBOUND
        | INBOUND
        | PAUSED
    )
    ;

migrationState_
    : LP_ MIGRATION_STATE EQ_ PAUSED RP_
    ;

tableOperationOption
    : (MEMORY_OPTIMIZED EQ_ ON)
    | (DURABILITY EQ_ (SCHEMA_ONLY | SCHEMA_AND_DATA))
    | (SYSTEM_VERSIONING EQ_ ON onHistoryTableClause?)
    ;

distributionOption
    : DISTRIBUTION EQ_ (HASH LP_ columnName RP_ | ROUND_ROBIN | REPLICATE)
    ;

dataWareHouseTableOption
    : CLUSTERED COLUMNSTORE INDEX
    | CLUSTERED COLUMNSTORE INDEX ORDER columnNames
    | HEAP
    | CLUSTERED INDEX LP_ (columnName (ASC | DESC)?) (COMMA_ (columnName (ASC | DESC)?))* RP_
    ;

dataWareHousePartitionOption
    : PARTITION LP_ columnName RANGE (LEFT | RIGHT)? FOR VALUES LP_ simpleExpr (COMMA_ simpleExpr)* RP_ RP_
    ;

alterDefinitionClause
    : addColumnSpecification
    | modifyColumnSpecification
    | alterDrop
    | alterCheckConstraint
    | alterTableTrigger
    | alterTableChangeTracking
    | alterSwitch
    | alterSet
    | alterTableOption
    | alterRebuild
    | alterFiletableOption
    | alterStretchConfiguration
    | splitOrMergeTable
    ;

splitOrMergeTable
    : (SPLIT | MERGE) RANGE LP_ stringOrNumber RP_
    ;

addColumnSpecification
    : (WITH (CHECK | NOCHECK))? ADD addColumnSpecificationItem (COMMA_ addColumnSpecificationItem)*
    ;

addColumnSpecificationItem
    : columnDefinition
    | computedColumnDefinition
    | columnSetDefinition
    | tableConstraint
    | alterTableTableIndex
    | constraintForColumn
    | generatedColumnNameClause? periodClause
    ;

modifyColumnSpecification
    : ALTER COLUMN columnName
    ( dataType columnDefinitionOption*
    | (ADD | DROP) (ROWGUIDCOL | PERSISTED | NOT FOR REPLICATION | SPARSE | HIDDEN_)
    | (ADD | DROP) MASKED (WITH LP_ FUNCTION EQ_ maskFunction=stringLiterals RP_)?
    ) (WITH LP_ ONLINE EQ_ onOffOption RP_)?
    ;

constraintForColumn
    : (CONSTRAINT constraintName)? DEFAULT simpleExpr FOR columnName
    ;

generatedColumnNameClause
    : generatedColumnName DEFAULT simpleExpr (WITH VALUES)?
    ;

generatedColumnName
    : columnName dataTypeName GENERATED ALWAYS AS (ROW | TRANSACTION_ID | SEQUENCE_NUMBER)
    (START | END)? HIDDEN_? (NOT NULL)? (CONSTRAINT ignoredIdentifier)?
    ;

alterDrop
    : DROP alterDropItem (COMMA_ alterDropItem)*
    ;

alterDropItem
    : alterTableDropConstraint
    | dropColumnSpecification
    | dropIndexSpecification
    | PERIOD FOR SYSTEM_TIME
    ;


alterTableDropConstraint
    : CONSTRAINT? ifExists? dropConstraintName (COMMA_ dropConstraintName)*
    ;

dropConstraintName
    : constraintName dropConstraintWithClause?
    ;

dropConstraintWithClause
    : WITH LP_ dropConstraintOption (COMMA_ dropConstraintOption)* RP_
    ;

dropConstraintOption
    : (
        MAXDOP EQ_ number
        | ONLINE EQ_ onOffOption
        | MOVE TO (schemaName LP_ columnName RP_ | ignoredIdentifier | STRING_)
    )
    ;

onOffOption
    : ON | OFF
    ;

dropColumnSpecification
    : COLUMN ifExists? columnName (COMMA_ columnName)*
    ;

dropIndexSpecification
    : INDEX ifExists? indexName (COMMA_ indexName)*
    ;

alterCheckConstraint
    : WITH? (CHECK | NOCHECK) CONSTRAINT (ALL | (constraintName (COMMA_ constraintName)*))
    ;

alterTableTrigger
    : (ENABLE | DISABLE) TRIGGER (ALL | ignoredIdentifiers)
    ;

alterTableChangeTracking
    : (ENABLE | DISABLE) CHANGE_TRACKING (WITH LP_ TRACK_COLUMNS_UPDATED EQ_ onOffOption RP_)?
    ;
alterSwitch
    : SWITCH (PARTITION expr)? TO tableName (PARTITION expr)? (WITH LP_ lowPriorityLockWait RP_)?
    ;

alterSet
    : SET LP_ (setFileStreamClause | setSystemVersionClause | setDataDeletion) RP_
    ;

setFileStreamClause
    : FILESTREAM_ON EQ_ (identifier | stringLiterals)
    ;

setSystemVersionClause
    : SYSTEM_VERSIONING EQ_ (OFF | ON alterSetOnClause?)
    ;

setDataDeletion
    : DATA_DELETION EQ_ (OFF | ON alterSetDataDeletionOnClause?)
    ;

alterSetDataDeletionOnClause
    : LP_ (FILTER_COLUMN EQ_ columnName)? COMMA_? (RETENTION_PERIOD EQ_ historyRetentionPeriod)? RP_
    ;

alterSetOnClause
    : LP_ (HISTORY_TABLE EQ_ tableName)? dataConsistencyCheckClause? historyRetentionPeriodClause? RP_
    ;

dataConsistencyCheckClause
    : COMMA_? DATA_CONSISTENCY_CHECK EQ_ onOffOption
    ;

historyRetentionPeriodClause
    : COMMA_? HISTORY_RETENTION_PERIOD EQ_ historyRetentionPeriod
    ;

historyRetentionPeriod
    : INFINITE
    | (number (DAY | DAYS | WEEK | WEEKS | MONTH | MONTHS | YEAR | YEARS))
    ;

alterTableTableIndex
    : INDEX indexName (indexNonClusterClause | indexClusterClause)
    ;

indexNonClusterClause
    : NONCLUSTERED (hashWithBucket | columnNamesWithSort alterTableIndexOnClause?)
    ;

alterTableIndexOnClause
    : ON ignoredIdentifier
    | DEFAULT
    ;

indexClusterClause
    : CLUSTERED COLUMNSTORE (WITH COMPRESSION_DELAY EQ_ number MINUTES?)? indexOnClause?
    ;

alterTableOption
    : SET LP_ LOCK_ESCALATION EQ_ (AUTO | TABLE | DISABLE) RP_
    ;

alterFiletableOption
    : (ENABLE | DISABLE) FILETABLE_NAMESPACE
    | SET LP_ FILETABLE_DIRECTORY EQ_ stringLiterals RP_
    ;

alterStretchConfiguration
    : SET LP_ REMOTE_DATA_ARCHIVE
    ( EQ_ ON LP_ tableStretchOptions RP_
    | EQ_ OFF_WITHOUT_DATA_RECOVERY LP_ MIGRATION_STATE EQ_ PAUSED RP_
    | LP_ tableStretchOption (COMMA_ tableStretchOption)* RP_
    ) RP_
    ;

onHistoryTableClause
    : LP_ HISTORY_TABLE EQ_ tableName (COMMA_ DATA_CONSISTENCY_CHECK EQ_ onOffOption)? RP_
    ;

alterRebuild
    : REBUILD (PARTITION EQ_ (ALL | expr))? (
            WITH LP_ indexOption (COMMA_ indexOption)* RP_
        )?
    ;

createDatabaseClause
    : (CONTAINMENT EQ_ (NONE | PARTIAL))? fileDefinitionClause? (COLLATE collationName)?
    (WITH databaseOption (COMMA_ databaseOption)*)?
    ;

fileDefinitionClause
    : ON PRIMARY? fileSpec (COMMA_ fileSpec)* (COMMA_ databaseFileGroup)* databaseLogOns?
    | ON fileSpec (COMMA_ fileSpec)* FOR (ATTACH_REBUILD_LOG | ATTACH (WITH attachDatabaseOption (COMMA_ attachDatabaseOption)*)?)
    | ON fileSpec (COMMA_ fileSpec)* AS SNAPSHOT OF identifier
    ;

attachDatabaseOption
    : ENABLE_BROKER
    | NEW_BROKER
    | ERROR_BROKER_CONVERSATIONS
    | RESTRICTED_USER
    | FILESTREAM LP_ DIRECTORY_NAME EQ_ (stringOrIdentifier | NULL) RP_
    ;

databaseOption
    : FILESTREAM fileStreamOption (COMMA_ fileStreamOption)*
    | DEFAULT_FULLTEXT_LANGUAGE EQ_ ignoredIdentifier
    | DEFAULT_LANGUAGE EQ_ ignoredIdentifier
    | NESTED_TRIGGERS EQ_ (OFF | ON)
    | TRANSFORM_NOISE_WORDS EQ_ (OFF | ON)
    | TWO_DIGIT_YEAR_CUTOFF EQ_ ignoredIdentifier
    | DB_CHAINING (OFF | ON)
    | TRUSTWORTHY (OFF | ON)
    | PERSISTENT_LOG_BUFFER EQ_ ON (DIRECTORY_NAME EQ_ stringLiterals)
    | LEDGER EQ_ onOffOption
    ;

fileStreamOption
    : NON_TRANSACTED_ACCESS EQ_ (OFF | READ_ONLY | FULL)
    | DIRECTORY_NAME EQ_ (stringOrIdentifier | NULL)
    ;

fileSpec
    : LP_ databaseFileSpecOption (COMMA_ databaseFileSpecOption)* RP_
    ;

databaseFileSpecOption
    : NAME EQ_ stringOrIdentifier
    | NEWNAME EQ_ stringOrIdentifier
    | FILENAME EQ_ stringLiterals
    | SIZE EQ_ numberLiterals (KB | MB | GB | TB)?
    | MAXSIZE EQ_ (numberLiterals (KB | MB | GB | TB)? | UNLIMITED)
    | FILEGROWTH EQ_ numberLiterals (KB | MB | GB | TB | MOD_)?
    | OFFLINE
    ;

databaseFileGroup
    : FILEGROUP identifier databaseFileGroupContains? fileSpec (COMMA_ fileSpec)*
    ;

databaseFileGroupContains
    : CONTAINS FILESTREAM DEFAULT? | DEFAULT | CONTAINS MEMORY_OPTIMIZED_DATA
    ;

databaseLogOns
    : LOG ON fileSpec (COMMA_ fileSpec)*
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/declare-local-variable-transact-sql
declare
    : DECLARE (declareVariable | tableVariable)
    ;

declareVariable
    : variable (COMMA_ variable)*
    ;
variable
    : localVariable | cursorVariable
    ;
localVariable
    : variableName AS? dataType (EQ_ bitExpr (COLLATE collationName)?)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/declare-cursor-transact-sql
cursorVariable
    : cursorName INSENSITIVE? SCROLL? (CURSOR | cursorVariableDefinition)
    ;

tableVariable
    : variableName AS? TABLE LP_ tableVariableClause (COMMA_ tableVariableClause)* RP_
    ;

tableVariableClause
    : variableColumnDefinition | variableTableConstraint | columnDefinition
    ;

variableColumnDefinition
    : columnName (dataType | AS expr) (COLLATE collationName)?
    (DEFAULT expr | IDENTITY (LP_ seed=number COMMA_ increment=number RP_)?)?
    ROWGUIDCOL? variableColumnConstraint? columnIndex?
    ;

variableColumnConstraint
    : NOT? NULL (PRIMARY KEY | UNIQUE) (CLUSTERED | NONCLUSTERED)?
    (WITH FILLFACTOR EQ_ number | withIndexOption)? onFileGroup?
    | checkConstraint (COMMA_ checkConstraint)*
    ;

variableTableConstraint
    : (PRIMARY KEY | UNIQUE) clusterOption? columnNamesWithSort (WITH FILLFACTOR EQ_ number | withIndexOption)?
    | tableIndex
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/set-local-variable-transact-sql
// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/set-statements-transact-sql
setVariable
    : SET (
        STATISTICS (IO | TIME | XML | PROFILE) onOffOption
        | ROWCOUNT (identifier | number)
        | TEXTSIZE number
        | TRANSACTION ISOLATION LEVEL (
              READ UNCOMMITTED
              | READ COMMITTED
              | REPEATABLE READ
              | SNAPSHOT
              | SERIALIZABLE
              | number
          )
        | IDENTITY_INSERT tableName onOffOption
        | specialList (COMMA_ specialList)* onOffOption
        | (columnName | LP_ select RP_) DOT_ identifier LP_ literals RP_ // method call
        | regularFunction
        | variableName (DOT_ identifier)? EQ_ (expr | identifier DOT_ identifier | NCHAR_TEXT)
        | variableName compoundOperation expr
        | variableName EQ_ identifier
        | variableName EQ_ cursorVariableDefinition
        | variableName EQ_ LP_ select RP_
        | variableName (identifier | literals | onOffOption)
    )
    ;

specialList
    : ANSI_NULLS
    | QUOTED_IDENTIFIER
    | ANSI_PADDING
    | ANSI_WARNINGS
    | ANSI_DEFAULTS
    | ANSI_NULL_DFLT_OFF
    | ANSI_NULL_DFLT_ON
    | ARITHABORT
    | ARITHIGNORE
    | CONCAT_NULL_YIELDS_NULL
    | CURSOR_CLOSE_ON_COMMIT
    | FMTONLY
    | FORCEPLAN
    | IMPLICIT_TRANSACTIONS
    | NOCOUNT
    | NOEXEC
    | NUMERIC_ROUNDABORT
    | PARSEONLY
    | REMOTE_PROC_TRANSACTIONS
    | SHOWPLAN_ALL
    | SHOWPLAN_TEXT
    | SHOWPLAN_XML
    | XACT_ABORT
    ;

cursorVariableDefinition
    : CURSOR cursorAttributes FOR select (FOR (READ_ONLY | UPDATE (OF name (COMMA_ name)*)?))?
    ;

cursorAttributes
    : (LOCAL | GLOBAL)? (FORWARD_ONLY | SCROLL)? (STATIC | KEYSET | DYNAMIC | FAST_FORWARD)?
     (READ_ONLY | SCROLL_LOCKS | OPTIMISTIC)? (TYPE_WARNING)?
    ;

compoundOperation
    : PLUS_ EQ_
    | MINUS_ EQ_
    | ASTERISK_ EQ_
    | SLASH_ EQ_
    | MOD_ EQ_
    | AMPERSAND_ EQ_
    | CARET_ EQ_
    | VERTICAL_BAR_ EQ_
    ;

alterDatabaseClause
    : MODIFY NAME EQ_ databaseName
    | COLLATE ignoredIdentifier
    | fileAndFilegroupOptions
    | SET alterDatabaseOptionSpec (COMMA_ alterDatabaseOptionSpec)* (WITH termination)?
    | MODIFY LP_ editionOptions (COMMA_ editionOptions)* RP_
    | MODIFY BACKUP_STORAGE_REDUNDANCY EQ_ stringLiterals
    | ADD SECONDARY ON SERVER ignoredIdentifier (
        WITH addSecondaryOption (COMMA_ addSecondaryOption)*
    )?
    | FAILOVER
    | FORCE_FAILOVER_ALLOW_DATA_LOSS
    ;

addSecondaryOption
    : ALLOW_CONNECTIONS EQ_ (ALL | NO)
    | SERVICE_OBJECTIVE EQ_ (
        serviceObjective
        | DATABASE_NAME EQ_ databaseName
        | SECONDARY_TYPE = (GEO | NAMED)
    )
    ;

editionOptions
    : MAXSIZE EQ_ number (MB | GB)
    | EDITION EQ_ stringLiterals
    | SERVICE_OBJECTIVE EQ_ (stringLiterals | serviceObjective)
    ;

serviceObjective
    : stringLiterals
    | ELASTIC_POOL LP_ ignoredIdentifier EQ_ stringLiterals RP_
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-database-transact-sql-set-options
alterDatabaseOptionSpec
    : acceleratedDatabaseRecovery
    | autoOption
    | automaticTuningOption
    | changeTrackingOption
    | containmentOption
    | cursorOption
    | databaseMirroringOption
    | dateCorrelationOptimizationOption
    | dbEncryptionOption
    | dbStateOption
    | dbUpdateOption
    | dbUserAccessOption
    | delayedDurabilityOption
    | externalAccessOption
    | filestreamClause
    | hadrOption
    | mixedPageAllocationOption
    | parameterizationOption
    | queryStoreOptions
    | recoveryOption
    | remoteDataArchiveOption
    | persistentLogBufferOption
    | serviceBrokerOption
    | snapshotOption
    | sqlOption
    | suspendForSnapshotBackupOption
    | targetRecoveryTimeOption
    | termination
    | temporalHistoryRetention
    | dataRetentionPolicy
    ;

dataRetentionPolicy
    : DATA_RETENTION onOffOption
    ;

temporalHistoryRetention
    : TEMPORAL_HISTORY_RETENTION onOffOption
    ;

suspendForSnapshotBackupOption
    : SUSPEND_FOR_SNAPSHOT_BACKUP '=' onOffOption (LP_ MODE EQ_ COPY_ONLY RP_)?
    ;

persistentLogBufferOption
    : PERSISTENT_LOG_BUFFER '=' (ON LP_ DIRECTORY_NAME EQ_ stringLiterals RP_ | OFF)
    ;

remoteDataArchiveOption
    : REMOTE_DATA_ARCHIVE '=' (
    ON '(' SERVER '=' serverName=name ','
        (CREDENTIAL '=' dbScopedCredentialName=name
        | FEDERATED_SERVICE_ACCOUNT '=' onOffOption)
    ')'
    | OFF)
    ;

parameterizationOption
    : PARAMETERIZATION (SIMPLE | FORCED)
    ;

mixedPageAllocationOption
    : MIXED_PAGE_ALLOCATION onOffOption
    ;

filestreamClause
    : FILESTREAM LP_ fileStreamOption RP_
    ;

delayedDurabilityOption
    : DELAYED_DURABILITY EQ_ (DISABLED | ALLOWED | FORCED)
    ;

dbUserAccessOption
    : SINGLE_USER | RESTRICTED_USER | MULTI_USER
    ;

dbUpdateOption
    : READ_ONLY | READ_WRITE
    ;

dbStateOption
    : ONLINE | OFFLINE | EMERGENCY
    ;

dbEncryptionOption
    : ENCRYPTION (ON | OFF | SUSPEND | RESUME)
    ;

dateCorrelationOptimizationOption
    : DATE_CORRELATION_OPTIMIZATION onOffOption
    ;

containmentOption
    : CONTAINMENT EQ_ (NONE | PARTIAL)
    ;
hadrOption
    : HADR (AVAILABILITY GROUP EQ_ (groupName=name | OFF) | SUSPEND | RESUME)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-database-transact-sql-database-mirroring
databaseMirroringOption
    : partnerOption | witnessOption
    ;
partnerOption
    : PARTNER EQ_ (partnerServer=stringLiterals
    | FAILOVER
    | FORCE_SERVICE_ALLOW_DATA_LOSS
    | OFF
    | RESUME
    | SAFETY (FULL | OFF)
    | SUSPEND
    | TIMEOUT number)
    ;
witnessOption
    : WITNESS EQ_ (witnessServer=stringLiterals | OFF)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-database-transact-sql-file-and-filegroup-options
fileAndFilegroupOptions
    : addOrModifyFiles
    | fileSpec
    | addOrModifyFilegroups
    | filegroupUpdatabilityOption
    ;

addOrModifyFilegroups
    : ADD FILEGROUP identifier (CONTAINS FILESTREAM | CONTAINS MEMORY_OPTIMIZED_DATA)?
    | REMOVE FILEGROUP identifier
    | MODIFY FILEGROUP identifier (
        filegroupUpdatabilityOption
        | DEFAULT
        | NAME EQ_ identifier
        | (AUTOGROW_SINGLE_FILE | AUTOGROW_ALL_FILES)
    )
    ;

filegroupUpdatabilityOption
    : (READONLY | READWRITE)
    | (READ_ONLY | READ_WRITE)
    ;

addOrModifyFiles
    : ADD FILE fileSpec (COMMA_ fileSpec)* (TO FILEGROUP identifier)?
    | ADD LOG FILE fileSpec (COMMA_ fileSpec)*
    | REMOVE FILE stringOrIdentifier
    | MODIFY FILE fileSpec
    ;

acceleratedDatabaseRecovery
    : ACCELERATED_DATABASE_RECOVERY EQ_ onOffOption (
        LP_ PERSISTENT_VERSION_STORE_FILEGROUP EQ_ identifier RP_
    )?
    ;

autoOption
    : AUTO_CLOSE onOffOption
    | AUTO_CREATE_STATISTICS (OFF | ON (LP_ INCREMENTAL EQ_ onOffOption RP_)?)
    | AUTO_SHRINK onOffOption
    | AUTO_UPDATE_STATISTICS onOffOption
    | AUTO_UPDATE_STATISTICS_ASYNC onOffOption
    ;

automaticTuningOption
    : AUTOMATIC_TUNING LP_ FORCE_LAST_GOOD_PLAN EQ_ onOffOption RP_
    ;

changeTrackingOption
    : CHANGE_TRACKING (
        EQ_ OFF
        | (EQ_ ON)? (LP_ changeTrackingOptionList (COMMA_ changeTrackingOptionList)* RP_)?
    )
    ;

changeTrackingOptionList
    : AUTO_CLEANUP EQ_ onOffOption
    | CHANGE_RETENTION EQ_ number (DAYS | HOURS | MINUTES)
    ;

cursorOption
    : CURSOR_CLOSE_ON_COMMIT onOffOption
    | CURSOR_DEFAULT (LOCAL | GLOBAL)
    ;

externalAccessOption
    : DB_CHAINING onOffOption
    | TRUSTWORTHY onOffOption
    | DEFAULT_FULLTEXT_LANGUAGE EQ_ stringLiterals
    | DEFAULT_LANGUAGE EQ_ stringLiterals
    | NESTED_TRIGGERS EQ_ (OFF | ON)
    | TRANSFORM_NOISE_WORDS EQ_ (OFF | ON)
    | TWO_DIGIT_YEAR_CUTOFF EQ_ number (COMMA_ number)*
    ;

queryStoreOptions
    : QUERY_STORE (
        EQ_ OFF
        | (EQ_ ON)? (LP_ queryStoreOptionList (COMMA_ queryStoreOptionList)* COMMA_? RP_)?
    )
    ;

queryStoreOptionList
    : OPERATION_MODE EQ_ (READ_WRITE | READ_ONLY)
    | CLEANUP_POLICY EQ_ LP_ STALE_QUERY_THRESHOLD_DAYS EQ_ number RP_
    | DATA_FLUSH_INTERVAL_SECONDS EQ_ number
    | MAX_STORAGE_SIZE_MB EQ_ number
    | INTERVAL_LENGTH_MINUTES EQ_ number
    | SIZE_BASED_CLEANUP_MODE EQ_ (AUTO | OFF)
    | QUERY_CAPTURE_MODE EQ_ (ALL | AUTO | CUSTOM | NONE)
    | MAX_PLANS_PER_QUERY EQ_ number
    | WAIT_STATS_CAPTURE_MODE EQ_ onOffOption
    | QUERY_CAPTURE_POLICY EQ_ LP_ queryCapturePolicyOptionList (
        COMMA_ queryCapturePolicyOptionList
    )* RP_
    ;

queryCapturePolicyOptionList
    : STALE_CAPTURE_POLICY_THRESHOLD EQ_ number (DAYS | HOURS)
    | EXECUTION_COUNT EQ_ number
    | TOTAL_COMPILE_CPU_TIME_MS EQ_ number
    | TOTAL_EXECUTION_CPU_TIME_MS EQ_ number
    ;

recoveryOption
    : RECOVERY (FULL | BULK_LOGGED | SIMPLE)
    | TORN_PAGE_DETECTION onOffOption
    | PAGE_VERIFY (CHECKSUM | TORN_PAGE_DETECTION | NONE)
    ;

sqlOption
    : ANSI_NULL_DEFAULT onOffOption
    | ANSI_NULLS onOffOption
    | ANSI_PADDING onOffOption
    | ANSI_WARNINGS onOffOption
    | ARITHABORT onOffOption
    | COMPATIBILITY_LEVEL EQ_ number
    | CONCAT_NULL_YIELDS_NULL onOffOption
    | NUMERIC_ROUNDABORT onOffOption
    | QUOTED_IDENTIFIER onOffOption
    | RECURSIVE_TRIGGERS onOffOption
    ;

snapshotOption
    : ALLOW_SNAPSHOT_ISOLATION onOffOption
    | READ_COMMITTED_SNAPSHOT onOffOption
    | MEMORY_OPTIMIZED_ELEVATE_TO_SNAPSHOT EQ_ onOffOption
    ;

serviceBrokerOption
    : ENABLE_BROKER
    | DISABLE_BROKER
    | NEW_BROKER
    | ERROR_BROKER_CONVERSATIONS
    | HONOR_BROKER_PRIORITY onOffOption
    ;

targetRecoveryTimeOption
    : TARGET_RECOVERY_TIME EQ_ number (SECONDS | MINUTES)
    ;

termination
    : ROLLBACK AFTER number SECONDS?
    | ROLLBACK IMMEDIATE
    | NO_WAIT
    ;

createServiceClause
    : LP_ (contractName | DEFAULT) (COMMA_ (contractName | DEFAULT))* RP_
    ;

alterServiceClause
    : LP_ alterServiceOptArg (COMMA_ alterServiceOptArg)* RP_
    ;

alterServiceOptArg
    : (ADD | DROP) CONTRACT contractName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/queries/option-clause-transact-sql
optionQueryHintClause
    : OPTION LP_ queryHint (COMMA_ queryHint)* RP_
    ;

controlOfFlow
    : blockStatement
    | breakStatement
    | continueStatement
    | gotoStatement
    | returnStatement
    | ifStatement
    | throwStatement
    | tryCatchStatement
    | waitforStatement
    | whileStatement
    ;
// https://docs.microsoft.com/zh-cn/sql/t-sql/language-elements/begin-end-transact-sql
blockStatement
    : BEGIN statement* END
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/language-elements/break-transact-sql
breakStatement
    : BREAK
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/language-elements/continue-transact-sql
continueStatement
    : CONTINUE
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/language-elements/goto-transact-sql
gotoStatement
    : GOTO identifier
    | identifier COLON_
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/language-elements/return-transact-sql
returnStatement
    : RETURN expr?
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/language-elements/if-else-transact-sql
ifStatement
    : IF expr statement (ELSE statement)?
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/language-elements/throw-transact-sql
throwStatement
    : THROW (throwErrorNumber ',' throwMessage ',' throwState)? ';'?
    ;

throwErrorNumber
    : number | identifier
    ;

throwMessage
    : STRING_ | identifier
    ;

throwState
    : number | identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/language-elements/try-catch-transact-sql
tryCatchStatement
    : BEGIN TRY tryClauses = statement+ END TRY BEGIN CATCH catchClauses = statement* END CATCH
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/language-elements/waitfor-transact-sql
waitforStatement
    : WAITFOR (
        (DELAY | TIME) time
        | LP_ receive RP_ (COMMA_ TIMEOUT time)?
        | LP_ getConversation RP_ (COMMA_ TIMEOUT time)?
    )
    ;
time
    : stringOrNumberOrIdentifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/language-elements/while-transact-sql
whileStatement
    : WHILE expr (statement | breakStatement | continueStatement)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/receive-transact-sql
receive
    :  RECEIVE top? duplicateSpecification? projections FROM queueName intoClause?
    (WHERE conversationHandle EQ_ conversationHandle)?
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/language-elements/print-transact-sql
print
    : PRINT expr (COMMA_ expr)*
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/language-elements/raiserror-transact-sql
raiseerror
    : RAISERROR LP_ msg=literalsOrIdentifier COMMA_ severity = literalsOrIdentifier COMMA_ state = literalsOrIdentifier
        (COMMA_ literalsOrIdentifier)* RP_ (WITH (LOG | SETERROR | NOWAIT))?
    ;
literalsOrIdentifier
    : literals | identifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/checkpoint-transact-sql
checkpoint
    : CHECKPOINT (checkPointDuration = number)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/begin-conversation-timer-transact-sql
beginConversationTimer
    : BEGIN CONVERSATION TIMER '(' conversationHandle ')' TIMEOUT '=' time
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/begin-dialog-conversation-transact-sql
beginConversationDialog
    : BEGIN DIALOG CONVERSATION? conversationHandle FROM SERVICE initiatorServiceName = serviceName
    TO SERVICE targetServiceName=stringLiterals (COMMA_ serviceBrokerGuid=stringLiterals)?
    ON CONTRACT contractName (
        WITH ((RELATED_CONVERSATION | RELATED_CONVERSATION_GROUP) '=' relatedConversation=conversationHandle ','?)?
        (LIFETIME '=' numberOrIdentifier ','?)? (ENCRYPTION '=' onOffOption)?
    )?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/end-conversation-transact-sql
endConversation
    : END CONVERSATION conversationHandle (
        WITH ERROR '=' faliureCode=numberOrIdentifier DESCRIPTION '=' failureText=stringOrIdentifier
        | WITH CLEANUP
    )?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/get-conversation-group-transact-sql
getConversation
    : GET CONVERSATION GROUP groupId=conversationHandle FROM queueName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/get-transmission-status-transact-sql
getConversationStatus
    : GET_TRANSMISSION_STATUS '(' conversationHandle ')'
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/move-conversation-transact-sql
moveConversation
    : MOVE CONVERSATION conversationHandle TO groupId=conversationHandle
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/send-transact-sql
sendConversation
    : SEND ON CONVERSATION LP_? conversationHandle (COMMA_ conversationHandle)* RP_?
    (MESSAGE TYPE messageTypeName)? ('(' messagebody=stringOrIdentifier ')')?
    ;

conversationHandle
    : stringLiterals | identifier
    ;
messageTypeName
    : identifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-contract-transact-sql
createContract
    : CREATE CONTRACT contractName (AUTHORIZATION owner)? LP_ messageSentClause (COMMA_ messageSentClause)* RP_
    ;

messageSentClause
    : (messageTypeName | DEFAULT)? SENT BY (INITIATOR | TARGET | ANY)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-contract-transact-sql
dropContract
    : DROP CONTRACT contractName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/close-transact-sql
closeCursor
    : CLOSE GLOBAL? cursorName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/deallocate-transact-sql
deallocateCursor
    : DEALLOCATE GLOBAL? cursorName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/fetch-transact-sql
fetchCursor
    : FETCH ((NEXT | PRIOR | FIRST | LAST | (ABSOLUTE | RELATIVE) expr)? FROM)? GLOBAL? cursorName
     (INTO variableName (',' variableName)*)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/open-transact-sql
openCursor
    : OPEN GLOBAL? cursorName
    ;

cursorName
    : identifier
    ;

kill
    : KILL (killProcess | killQueryNotification | killStatsJob)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/kill-transact-sql
killProcess
    : sessionId=stringOrNumberOrIdentifier (WITH STATUSONLY)?
    | UOW (WITH STATUSONLY | COMMIT | ROLLBACK)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/kill-query-notification-subscription-transact-sql
killQueryNotification
    : QUERY NOTIFICATION SUBSCRIPTION (ALL | subscriptionId=stringOrNumberOrIdentifier)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/kill-stats-job-transact-sql
killStatsJob
    : STATS JOB jobId=stringOrNumberOrIdentifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-message-type-transact-sql
createMessageType
    : CREATE MESSAGE TYPE messageTypeName (AUTHORIZATION owner)?
     (VALIDATION EQ_ (NONE | EMPTY | WELL_FORMED_XML | VALID_XML WITH SCHEMA COLLECTION schemaCollectionName=name))?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-message-type-transact-sql
alterMessageType
    : ALTER MESSAGE TYPE messageTypeName VALIDATION EQ_ (NONE | EMPTY | WELL_FORMED_XML | VALID_XML WITH SCHEMA COLLECTION schemaCollectionName=name)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-message-type-transact-sql
dropMessageType
    : DROP MESSAGE TYPE messageTypeName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/reconfigure-transact-sql
reconfigure
    : RECONFIGURE (WITH OVERRIDE)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/execute-as-clause-transact-sql
executeAs
    : (EXEC | EXECUTE) AS (clause=(CALLER | SELF | OWNER | STRING_) | contextSpecification)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/execute-as-transact-sql
contextSpecification
    : (LOGIN | USER) EQ_ stringLiterals (WITH (NO REVERT | COOKIE INTO identifier))?
    | CALLER
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/open-symmetric-key-transact-sql
openSymmetricKey
    : OPEN SYMMETRIC KEY keyName DECRYPTION BY decryptionMechanism
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/open-master-key-transact-sql
openMasterKey
    : OPEN MASTER KEY decryptionPassword
    ;

keyName
    : identifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/close-symmetric-key-transact-sql
closeSymmetricKey
    : CLOSE SYMMETRIC KEY keyName
    | CLOSE ALL SYMMETRIC KEYS
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/close-master-key-transact-sql
closeMasterKey
    : CLOSE MASTER KEY
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-symmetric-key-transact-sql
createSymmetricKey
    : CREATE SYMMETRIC KEY keyName (AUTHORIZATION owner)?
    (FROM PROVIDER providerName=identifier)?
    WITH (keyOptions (COMMA_ keyOptions)*)? (ENCRYPTION BY encryptionMechanism (COMMA_ ENCRYPTION BY encryptionMechanism)*)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-master-key-transact-sql
createMasterKey
    : CREATE MASTER KEY encryptionPassword?
    ;
keyOptions
    : KEY_SOURCE EQ_ stringLiterals
    | ALGORITHM EQ_ (DES | TRIPLE_DES | TRIPLE_DES_3KEY | RC2 | RC4 | RC4_128 | DESX | AES_128 | AES_192 | AES_256)
    | IDENTITY_VALUE EQ_ stringLiterals
    | PROVIDER_KEY_NAME EQ_ stringLiterals
    | CREATION_DISPOSITION EQ_ (CREATE_NEW | OPEN_EXISTING)
    ;

encryptionMechanism
    : CERTIFICATE name
    | ASYMMETRIC KEY name
    | SYMMETRIC KEY name
    | PASSWORD EQ_ stringLiterals
    ;

decryptionMechanism
    : CERTIFICATE name (WITH PASSWORD EQ_ stringLiterals)?
    | ASYMMETRIC KEY name (WITH PASSWORD EQ_ stringLiterals)?
    | SYMMETRIC KEY name
    | PASSWORD EQ_ stringLiterals
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-symmetric-key-transact-sql
dropSymmetricKey
    : DROP SYMMETRIC KEY keyName (REMOVE PROVIDER KEY)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-master-key-transact-sql
dropMasterKey
    : DROP MASTER KEY
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-symmetric-key-transact-sql
alterSymmetricKey
    : ALTER SYMMETRIC KEY keyName alterKeyOption (COMMA_ alterKeyOption)*
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-master-key-transact-sql
alterMasterKey
    : ALTER MASTER KEY (regenerateOption | encryptionOption)
    ;

alterKeyOption
    : (ADD | DROP) ENCRYPTION BY encryptionMechanism
    ;

regenerateOption
    : FORCE? REGENERATE WITH encryptionPassword
    ;

encryptionOption
    : (ADD | DROP) ENCRYPTION BY (SERVICE MASTER KEY | PASSWORD EQ_ stringLiterals)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-certificate-transact-sql
createCertificate
    : CREATE CERTIFICATE certificateName (AUTHORIZATION userName)?
    (FROM existingKeys | generateNewKeys ) (ACTIVE FOR BEGIN_DIALOG '=' onOffOption)?
    ;

certificateName
    : identifier
    ;

existingKeys
    : ASSEMBLY name
    | EXECUTABLE? FILE EQ_ pathToFile = stringLiterals (WITH (formatSpec COMMA_)? privateKeySpec)?
    | BINARY EQ_ asnEncodedCertificate=stringOrIdentifier  (WITH privateKeySpec)?
    ;

formatSpec
    : FORMAT EQ_ format=stringLiterals
    ;

privateKeySpec
    : PRIVATE KEY LP_ privateKeySpecItem (COMMA_ privateKeySpecItem)* RP_
    ;

privateKeySpecItem
    : (FILE | BINARY) EQ_ pathOrBits=stringLiterals
    | decryptionPassword
    | encryptionPassword
    | ALGORITHM EQ_ algorithmName = stringLiterals
    ;

decryptionPassword
    : DECRYPTION BY PASSWORD EQ_ stringLiterals
    ;

encryptionPassword
    : ENCRYPTION BY PASSWORD EQ_ stringLiterals
    ;

generateNewKeys
    : encryptionPassword? WITH SUBJECT EQ_ certificateSubjectName = STRING_ (',' dateOptions)*
    ;

dateOptions
    : (START_DATE | EXPIRY_DATE) EQ_ stringLiterals
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-certificate-transact-sql
alterCertificate
    : ALTER CERTIFICATE certificateName (
        REMOVE PRIVATE KEY
        | WITH PRIVATE KEY LP_ alterPrivateKeySpec RP_
        | WITH ACTIVE FOR BEGIN_DIALOG EQ_ onOffOption
    )
    ;

alterPrivateKeySpec
    : (FILE | BINARY) EQ_ stringOrIdentifier (COMMA_ decryptionPassword)? (COMMA_ encryptionPassword)?
    | decryptionPassword (COMMA_ encryptionPassword)?
    | encryptionPassword (COMMA_ decryptionPassword)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-credential-transact-sql
dropCertificate
    : DROP CERTIFICATE certificateName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/shutdown-transact-sql
shutdown
    : SHUTDOWN (WITH NOWAIT)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/language-elements/use-transact-sql
use
    : USE databaseName
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-application-role-transact-sql
alterApplicationRole
    : ALTER APPLICATION ROLE roleName=identifier WITH appRoleSetItem (COMMA_ appRoleSetItem)*
    ;

appRoleSetItem
    : NAME EQ_ roleName=identifier
    | PASSWORD EQ_ stringLiterals
    | DEFAULT_SCHEMA EQ_ schemaName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-assembly-transact-sql
alterAssembly
    : ALTER ASSEMBLY assemblyName=identifier assemblyFromClause? assemblyWithClause? assemblyDropClause? assemblyAddClause?
    ;

assemblyFromClause
    : FROM (stringLiterals | bitValueLiterals)
    ;

assemblyWithClause
    : WITH assemblyOption (COMMA_ assemblyOption)*
    ;

assemblyOption
    : PERMISSION_SET EQ_ (SAFE | EXTERNAL_ACCESS | UNSAFE)
    | VISIBILITY EQ_ onOffOption
    | UNCHECKED DATA
    ;

assemblyDropClause
    : DROP FILE (identifier (COMMA_ identifier)* | ALL)
    ;
assemblyAddClause
    : ADD FILE FROM fileSpecifier (COMMA_ fileSpecifier)*
    ;

fileSpecifier
    : (stringLiterals | bitValueLiterals) (AS fileName)?
    ;

fileName
    : stringOrIdentifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-asymmetric-key-transact-sql
alterAsymmetricKey
    : ALTER ASYMMETRIC KEY keyName asymmetricKeyOption
    ;


asymmetricKeyOption
    : asymmetricKeyPasswordChangeOption  | REMOVE PRIVATE KEY
    ;

asymmetricKeyPasswordChangeOption
    : WITH PRIVATE KEY LP_ passwordOption (COMMA_ passwordOption)? RP_
    ;

passwordOption
    : encryptionPassword | decryptionPassword
    ;


// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-authorization-transact-sql
alterAuthorization
    : ALTER AUTHORIZATION ON (authClassType DOUBLE_COLON_)? entityName TO (principal | SCHEMA OWNER)
    ;
entityName
    : (owner DOT_)* name
    ;
authClassType
    : OBJECT
    | ASSEMBLY
    | ASYMMETRIC KEY
    | AVAILABILITY GROUP
    | CERTIFICATE
    | CONTRACT
    | TYPE
    | DATABASE
    | ENDPOINT
    | FULLTEXT CATALOG
    | FULLTEXT STOPLIST
    | MESSAGE TYPE
    | REMOTE SERVICE BINDING
    | ROLE
    | ROUTE
    | SCHEMA
    | SEARCH PROPERTY LIST
    | SERVER ROLE
    | SERVICE
    | SYMMETRIC KEY
    | XML SCHEMA COLLECTION
    ;


// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-availability-group-transact-sql
alterAvailabilityGroup
    : ALTER AVAILABILITY GROUP groupName=identifier alterAvailabilityGroupClause
    ;

alterAvailabilityGroupClause
    : SET LP_ availabilityGroupOptionSpec RP_
    | ADD DATABASE databaseName
    | REMOVE DATABASE databaseName
    | ADD REPLICA ON replicaSpec
    | MODIFY REPLICA ON replicaSpec
    | REMOVE REPLICA ON serverInstance
    | JOIN
    | JOIN AVAILABILITY GROUP ON availabilityGroupSpec (COMMA_ availabilityGroupSpec)*
    | MODIFY AVAILABILITY GROUP ON availabilityGroupSpec (COMMA_ availabilityGroupSpec)*
    | GRANT CREATE ANY DATABASE
    | DENY CREATE ANY DATABASE
    | FAILOVER
    | FORCE_FAILOVER_ALLOW_DATA_LOSS
    | ADD LISTENER dnsName=stringLiterals LP_ listenerOption RP_
    | MODIFY LISTENER dnsName=stringLiterals LP_ modifyListenerOption RP_
    | RESTART LISTENER dnsName=stringLiterals
    | REMOVE LISTENER dnsName=stringLiterals
    | OFFLINE
    ;

replicaSpec
    : serverInstance WITH LP_ replicaOption (COMMA_ replicaOption)* RP_
    ;

replicaOption
    : ENDPOINT_URL EQ_ stringLiterals
    | AVAILABILITY_MODE EQ_ (SYNCHRONOUS_COMMIT | ASYNCHRONOUS_COMMIT | CONFIGURATION_ONLY)
    | FAILOVER_MODE EQ_ (AUTOMATIC | MANUAL)
    | SEEDING_MODE EQ_ (AUTOMATIC | MANUAL)
    | BACKUP_PRIORITY EQ_ number
    | SECONDARY_ROLE LP_ (ALLOW_CONNECTIONS EQ_ (NO | READ_ONLY | ALL))? COMMA_? (READ_ONLY_ROUTING_URL EQ_ stringLiterals)?  RP_
    | PRIMARY_ROLE LP_
        (ALLOW_CONNECTIONS EQ_ (READ_WRITE | ALL))?
        COMMA_? (READ_ONLY_ROUTING_LIST EQ_ (NONE | (LP_ serverInstance (COMMA_ serverInstance)* RP_)))?
        COMMA_? (READ_WRITE_ROUTING_URL EQ_ serverInstance)?
        RP_
    | SESSION_TIMEOUT EQ_ number
    ;

availabilityGroupSpec
    : agName=stringOrIdentifier WITH LP_ availabilityGroupOption (COMMA_ availabilityGroupOption)* RP_
    ;

availabilityGroupOption
    : (LISTENER_URL | LISTENER) EQ_ stringLiterals
    | AVAILABILITY_MODE EQ_ (SYNCHRONOUS_COMMIT | ASYNCHRONOUS_COMMIT)
    | FAILOVER_MODE EQ_ MANUAL
    | SEEDING_MODE EQ_ (AUTOMATIC | MANUAL)
    ;

listenerOption
    : WITH DHCP (ON LP_ networkSubnetOption RP_)?
    | WITH IP LP_ ipAddressOption (COMMA_ ipAddressOption )* RP_ (COMMA_ PORT EQ_ number )?
    ;
networkSubnetOption
    : STRING_ COMMA_ STRING_
    ;

ipAddressOption
    : LP_ STRING_ (COMMA_ STRING_)? RP_
    ;

modifyListenerOption
    : ADD IP LP_ ipAddressOption RP_
    | PORT EQ_ number
    ;

serverInstance
    : STRING_
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-availability-group-transact-sql
createAvailabilityGroup
    : CREATE AVAILABILITY GROUP groupName=identifier
    WITH LP_ availabilityGroupOptionSpec (COMMA_ availabilityGroupOptionSpec)* RP_
    FOR (DATABASE databaseName (COMMA_ databaseName)*)?
    REPLICA ON replicaSpec (COMMA_ replicaSpec)*
    (AVAILABILITY GROUP ON availabilityGroupSpec (COMMA_ availabilityGroupSpec)*)?
    (LISTENER dnsName=STRING_ LP_ listenerOption RP_)?
    ;

availabilityGroupOptionSpec
    : AUTOMATED_BACKUP_PREFERENCE EQ_ (PRIMARY | SECONDARY_ONLY | SECONDARY | NONE)
    | FAILURE_CONDITION_LEVEL EQ_ number
    | HEALTH_CHECK_TIMEOUT EQ_ number
    | DB_FAILOVER EQ_ onOffOption
    | REQUIRED_SYNCHRONIZED_SECONDARIES_TO_COMMIT EQ_ number
    | ROLE EQ_ SECONDARY
    | (BASIC | DISTRIBUTED | CONTAINED REUSE_SYSTEM_DATABASES?)
    | CLUSTER_TYPE EQ_ (WSFC | EXTERNAL | NONE)
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-broker-priority-transact-sql
createBrokerPriority
    : CREATE BROKER PRIORITY conversationPriorityName = identifier brokerPriorityClause
    ;

brokerPriorityClause
    : FOR CONVERSATION SET LP_ (brokerPriorityOption (COMMA_ brokerPriorityOption)*)? RP_
    ;

brokerPriorityOption
    : CONTRACT_NAME EQ_ (contractName | ANY)
    | LOCAL_SERVICE_NAME EQ_ (localServiceName=identifier | ANY)
    | REMOTE_SERVICE_NAME EQ_ (remoteServiceName=stringLiterals | ANY)
    | PRIORITY_LEVEL EQ_ (priorityValue=number  | DEFAULT)
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-broker-priority-transact-sql
alterBrokerPriority
    : ALTER BROKER PRIORITY conversationPriorityName = identifier brokerPriorityClause
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/drop-broker-priority-transact-sql
dropBrokerPriority
    : DROP BROKER PRIORITY conversationPriorityName = identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-column-encryption-key-transact-sql
alterColumnEncryptionKey
    : ALTER COLUMN ENCRYPTION KEY columnEncryptionKey = identifier (ADD | DROP) VALUE LP_ COLUMN_MASTER_KEY EQ_ columnMasterKeyName = identifier (
        COMMA_ ALGORITHM EQ_ algorithmName = stringLiterals COMMA_ ENCRYPTED_VALUE EQ_ hexadecimalLiterals
    )? RP_
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-column-encryption-key-transact-sql
createColumnEncryptionKey
    : CREATE COLUMN ENCRYPTION KEY columnEncryptionKey = identifier WITH VALUES columnEncryptionKeyOptions (COMMA_ columnEncryptionKeyOptions)*
    ;

columnEncryptionKeyOptions
    : LP_ columnEncryptionKeyOption (COMMA_ columnEncryptionKeyOption)* RP_
    ;

columnEncryptionKeyOption
    : COLUMN_MASTER_KEY EQ_ columnMasterKeyName = identifier
    | ALGORITHM EQ_ algorithmName = stringLiterals
    | ENCRYPTED_VALUE EQ_ hexadecimalLiterals
    ;

dropColumnEncryptionKey
    : DROP COLUMN ENCRYPTION KEY columnEncryptionKey = identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-credential-transact-sql
alterCredential
    : ALTER CREDENTIAL credentialName = identifier WITH IDENTITY EQ_ identityName = stringLiterals (COMMA_ SECRET EQ_ secret = stringLiterals)?
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-credential-transact-sql
createCredential
    : CREATE CREDENTIAL credentialName = identifier WITH IDENTITY EQ_ identityName = stringLiterals
     (COMMA_ SECRET EQ_ secret = stringLiterals)?
     (FOR CRYPTOGRAPHIC PROVIDER providerName = identifier)?
    ;

dropCredential
    : DROP CREDENTIAL credentialName = identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-cryptographic-provider-transact-sql
alterCryptographicProvider
    : ALTER CRYPTOGRAPHIC PROVIDER providerName=identifier (
        FROM FILE EQ_ cryptoProviderDdlFile = stringLiterals
    )? (ENABLE | DISABLE)?
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-cryptographic-provider-transact-sql
createCryptographicProvider
    : CREATE CRYPTOGRAPHIC PROVIDER providerName=identifier FROM FILE EQ_ path_of_DLL = stringLiterals
    ;

dropCryptographicProvider
    : DROP CRYPTOGRAPHIC PROVIDER providerName=identifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-database-audit-specification-transact-sql?view=sql-server-ver16
createDatabaseAuditSpecification
    : CREATE DATABASE AUDIT SPECIFICATION auditSpecificationName = identifier
    (FOR SERVER AUDIT auditName = identifier)?
    (ADD LP_ auditActionSpec (',' auditActionSpec)* RP_)?
    (WITH '(' STATE '=' onOffOption ')')?
    ;

dropDatabaseAuditSpecification
    : DROP DATABASE AUDIT SPECIFICATION auditSpecificationName = identifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-database-audit-specification-transact-sql
alterDatabaseAuditSpecification
    : ALTER DATABASE AUDIT SPECIFICATION auditSpecificationName = identifier
     (FOR SERVER AUDIT auditName = identifier)?
     modifyAuditActionSpec*
     (WITH '(' STATE '=' onOffOption ')')?
    ;
modifyAuditActionSpec
    : (ADD | DROP) LP_ auditActionSpec RP_
    ;
auditActionSpec
    : auditActionSpecification | auditActionGroupName = identifier
    ;

auditActionSpecification
    : actionSpecification (',' actionSpecification)* ON (auditClassName '::')? securable BY principal (',' principal)*
    ;

actionSpecification
    : SELECT
    | INSERT
    | UPDATE
    | DELETE
    | EXECUTE
    | RECEIVE
    | REFERENCES
    ;

auditClassName
    : OBJECT
    | SCHEMA
    | TABLE
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-database-encryption-key-transact-sql
alterDatabaseEncryptionKey
    : ALTER DATABASE ENCRYPTION KEY (regenerateWithAlgorithmClause | encryptionByServerClause)
    ;
regenerateWithAlgorithmClause
    : REGENERATE WITH ALGORITHM EQ_ (AES_128 | AES_192 | AES_256 | TRIPLE_DES_3KEY)
    ;
encryptionByServerClause
    : ENCRYPTION BY SERVER (CERTIFICATE | ASYMMETRIC KEY) encryptorName=name
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-database-encryption-key-transact-sql
createDatabaseEncryptionKey
    : CREATE DATABASE ENCRYPTION KEY WITH ALGORITHM EQ_ (AES_128 | AES_192 | AES_256 | TRIPLE_DES_3KEY)
    encryptionByServerClause
    ;

dropDatabaseEncryptionKey
    : DROP DATABASE ENCRYPTION KEY
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-database-scoped-configuration-transact-sql
alterDatabaseScopedConfiguration
    : ALTER DATABASE SCOPED CONFIGURATION (
    (FOR SECONDARY)? SET setOptions
    | CLEAR PROCEDURE_CACHE planHandle=hexadecimalLiterals?
    )
    ;

setOptions
    : MAXDOP EQ_ (number | PRIMARY)
    | LEGACY_CARDINALITY_ESTIMATION EQ_ ( ON | OFF | PRIMARY)
    | PARAMETER_SNIFFING EQ_ ( ON | OFF | PRIMARY)
    | QUERY_OPTIMIZER_HOTFIXES EQ_ ( ON | OFF | PRIMARY)
    | IDENTITY_CACHE EQ_ ( ON | OFF )
    | INTERLEAVED_EXECUTION_TVF EQ_ ( ON | OFF )
    | BATCH_MODE_MEMORY_GRANT_FEEDBACK EQ_ ( ON | OFF )
    | BATCH_MODE_ADAPTIVE_JOINS EQ_ ( ON | OFF )
    | TSQL_SCALAR_UDF_INLINING EQ_ ( ON | OFF )
    | ELEVATE_ONLINE EQ_ ( OFF | WHEN_SUPPORTED | FAIL_UNSUPPORTED )
    | ELEVATE_RESUMABLE EQ_ ( OFF | WHEN_SUPPORTED | FAIL_UNSUPPORTED )
    | OPTIMIZE_FOR_AD_HOC_WORKLOADS EQ_ ( ON | OFF )
    | XTP_PROCEDURE_EXECUTION_STATISTICS EQ_ ( ON | OFF )
    | XTP_QUERY_EXECUTION_STATISTICS EQ_ ( ON | OFF )
    | ROW_MODE_MEMORY_GRANT_FEEDBACK EQ_ ( ON | OFF )
    | MEMORY_GRANT_FEEDBACK_PERCENTILE_GRANT EQ_ ( ON | OFF )
    | MEMORY_GRANT_FEEDBACK_PERSISTENCE EQ_ ( ON | OFF )
    | BATCH_MODE_ON_ROWSTORE EQ_ ( ON | OFF )
    | DEFERRED_COMPILATION_TV EQ_ ( ON | OFF )
    | ACCELERATED_PLAN_FORCING EQ_ ( ON | OFF )
    | GLOBAL_TEMPORARY_TABLE_AUTO_DROP EQ_ ( ON | OFF )
    | LIGHTWEIGHT_QUERY_PROFILING EQ_ ( ON | OFF )
    | VERBOSE_TRUNCATION_WARNINGS EQ_ ( ON | OFF )
    | LAST_QUERY_PLAN_STATS EQ_ ( ON | OFF )
    | PAUSED_RESUMABLE_INDEX_ABORT_DURATION_MINUTES EQ_ time
    | ISOLATE_SECURITY_POLICY_CARDINALITY  EQ_ ( ON | OFF )
    | EXEC_QUERY_STATS_FOR_SCALAR_FUNCTIONS EQ_ ( ON | OFF )
    | ASYNC_STATS_UPDATE_WAIT_AT_LOW_PRIORITY EQ_ ( ON | OFF )
    | OPTIMIZED_PLAN_FORCING EQ_ ( ON | OFF )
    | DOP_FEEDBACK EQ_ ( ON | OFF )
    | CE_FEEDBACK EQ_ ( ON | OFF )
    | PARAMETER_SENSITIVE_PLAN_OPTIMIZATION EQ_ ( ON | OFF )
    | LEDGER_DIGEST_STORAGE_ENDPOINT EQ_ ( stringLiterals | OFF )
    | OPTIMIZED_SP_EXECUTESQL EQ_ ( ON | OFF )
    ;


// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-endpoint-transact-sql
alterEndpoint
    : ALTER ENDPOINT endpointname = identifier (AUTHORIZATION login = identifier)?
     (STATE EQ_ state = (STARTED | STOPPED | DISABLED))?
     asTcpClause?
     (forTSQLClause| forServiceBrokerClause | forDatabaseMirroringClause)?
    ;

asTcpClause
    : AS TCP LP_ LISTENER_PORT EQ_ number (
      COMMA_ LISTENER_IP EQ_ (ALL | '(' (ipv4 = IPV4_ADDR | ipv6 = STRING_) ')')
    )? RP_
    ;
forTSQLClause
    : FOR TSQL LP_ RP_
    ;

forServiceBrokerClause
    : FOR SERVICE_BROKER LP_ (endpointServiceBrokerOption (COMMA_ endpointServiceBrokerOption)*)? RP_
    ;

endpointServiceBrokerOption
    : endpointAuthenticationClause
    | endpointEncryptionAlogorithmClause
    | endpointMessageForwardingClause
    | endpointMessageForwardSizeClause
    ;

endpointMessageForwardingClause
    : MESSAGE_FORWARDING EQ_ (ENABLED | DISABLED)
    ;

endpointMessageForwardSizeClause
    : MESSAGE_FORWARD_SIZE EQ_ number
    ;

forDatabaseMirroringClause
    : FOR DATABASE_MIRRORING LP_ (endpointMirroringOption (COMMA_ endpointMirroringOption)*)? RP_
    ;

endpointMirroringOption
    : endpointAuthenticationClause
    | endpointEncryptionAlogorithmClause
    | endpointRoleClause
    ;

endpointRoleClause
    : ROLE EQ_ (WITNESS | PARTNER | ALL)
    ;

endpointEncryptionAlogorithmClause
    : ENCRYPTION EQ_ (DISABLED | SUPPORTED | REQUIRED) (ALGORITHM (AES RC4? | RC4 AES?))?
    ;

endpointAuthenticationClause
    : AUTHENTICATION EQ_ (
        WINDOWS (NTLM | KERBEROS | NEGOTIATE)? (CERTIFICATE certName = identifier)?
        | CERTIFICATE certName = identifier (WINDOWS (NTLM | KERBEROS | NEGOTIATE)?)?
    )
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-endpoint-transact-sql
createEndpoint
    : CREATE ENDPOINT endpointname = identifier (AUTHORIZATION login = identifier)?
     (STATE EQ_ state = (STARTED | STOPPED | DISABLED))?
     asTcpClause
     (forTSQLClause| forServiceBrokerClause | forDatabaseMirroringClause)
    ;

dropEndpoint
    : DROP ENDPOINT endpointname = identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-external-data-source-transact-sql
alterExternalDataSource
    : ALTER EXTERNAL DATA SOURCE dataSourceName = identifier SET dataSourceOption (COMMA_ dataSourceOption)*
    ;

dataSourceOption
    : LOCATION EQ_ stringLiterals
    | RESOURCE_MANAGER_LOCATION EQ_ stringLiterals
    | CREDENTIAL EQ_ credentialName = identifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-external-data-source-transact-sql
createExternalDataSource
    : CREATE EXTERNAL DATA SOURCE dataSourceName = identifier WITH LP_ createdataSourceOption (COMMA_ createdataSourceOption)* COMMA_? RP_
    ;
createdataSourceOption
    : LOCATION EQ_ stringLiterals
    | CONNECTION_OPTIONS EQ_ stringLiterals
    | CREDENTIAL EQ_ credentialName = identifier
    | PUSHDOWN EQ_ onOffOption
    ;

dropExternalDataSource
    : DROP EXTERNAL DATA SOURCE dataSourceName = identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-external-library-transact-sql
alterExternalLibrary
    : ALTER EXTERNAL LIBRARY libraryName = identifier (AUTHORIZATION owner)?
    SET libraryfileSpec
    WITH LP_ LANGUAGE EQ_ language=STRING_ RP_
    ;
libraryfileSpec
    : LP_ CONTENT EQ_ (clientLibrary = stringLiterals | hexadecimalLiterals | NONE) (COMMA_ PLATFORM EQ_ (WINDOWS | LINUX))? RP_
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-external-library-transact-sql
createExternalLibrary
    : CREATE EXTERNAL LIBRARY libraryName = identifier (AUTHORIZATION owner)?
    FROM libraryfileSpec (COMMA_ libraryfileSpec)*
    WITH LP_ LANGUAGE EQ_ language=stringLiterals RP_
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-external-library-transact-sql
dropExternalLibrary
    : DROP EXTERNAL LIBRARY libraryName = identifier (AUTHORIZATION owner)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-resource-pool-transact-sql
alterResourcePool
    : ALTER RESOURCE POOL poolName=identifier (WITH LP_ (resourcePoolOption (COMMA_ resourcePoolOption)*)? RP_)?
    ;

resourcePoolOption
    : MIN_CPU_PERCENT EQ_ number
    | MAX_CPU_PERCENT EQ_ number
    | CAP_CPU_PERCENT EQ_ number
    | MIN_MEMORY_PERCENT EQ_ number
    | MAX_MEMORY_PERCENT EQ_ number
    | MIN_IOPS_PER_VOLUME  EQ_ number
    | MAX_IOPS_PER_VOLUME  EQ_ number
    | AFFINITY (SCHEDULER EQ_ (AUTO | LP_ rangeSpec (COMMA_ rangeSpec)* RP_ ) | NUMANODE EQ_ LP_ rangeSpec (COMMA_ rangeSpec)* RP_ )
    ;

rangeSpec
    : number (TO number)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-resource-pool-transact-sql
createResourcePool
    : CREATE RESOURCE POOL poolName=identifier (WITH LP_ (resourcePoolOption (COMMA_ resourcePoolOption)*)? RP_)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-resource-pool-transact-sql
dropResourcePool
    : DROP RESOURCE POOL poolName=identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-external-resource-pool-transact-sql
alterExternalResourcePool
    : ALTER EXTERNAL RESOURCE POOL poolName=identifier externalResourcePoolClause?
    ;

externalResourcePoolClause
    : WITH LP_ (MAX_CPU_PERCENT EQ_ maxCpuPercent = number)?
    (COMMA_? MAX_MEMORY_PERCENT EQ_ maxMemoryPercent = number)?
    (COMMA_? MAX_PROCESSES EQ_ maxProcesses = number)?
    RP_
    ;
// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-external-resource-pool-transact-sql
createExternalResourcePool
    : CREATE EXTERNAL RESOURCE POOL poolName=identifier externalResourcePoolClause?
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/drop-external-resource-pool-transact-sql
dropExternalResourcePool
    : DROP EXTERNAL RESOURCE POOL poolName=identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-fulltext-catalog-transact-sql
alterFulltextCatalog
    : ALTER FULLTEXT CATALOG catalogName=identifier (
        REBUILD (WITH ACCENT_SENSITIVITY EQ_ onOffOption)?
        | REORGANIZE
        | AS DEFAULT
    )
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-fulltext-catalog-transact-sql
createFulltextCatalog
    : CREATE FULLTEXT CATALOG catalogName=identifier
    (ON FILEGROUP filegroup = identifier)?
    (IN PATH rootpath = stringLiterals)?
    (WITH ACCENT_SENSITIVITY EQ_ (ON | OFF))?
    (AS DEFAULT)?
    (AUTHORIZATION owner)?
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/drop-fulltext-catalog-transact-sql
dropFulltextCatalog
    : DROP FULLTEXT CATALOG catalogName=identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-fulltext-stoplist-transact-sql
alterFulltextStoplist
    : ALTER FULLTEXT STOPLIST stoplistName = identifier (
        ADD stopword = stringLiterals LANGUAGE (stringLiterals | number | HEX_DIGIT_)
        | DROP (
            stopword = stringLiterals LANGUAGE (stringLiterals | number | HEX_DIGIT_)
            | ALL (stringLiterals | number | HEX_DIGIT_)?
        )
    )
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-fulltext-stoplist-transact-sql
createFulltextStoplist
    : CREATE FULLTEXT STOPLIST stoplistName = identifier
    (FROM ((databaseName DOT_)? sourceStoplistName = name | SYSTEM STOPLIST))?
    (AUTHORIZATION owner)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-fulltext-stoplist-transact-sql
dropFulltextStoplist
    : DROP FULLTEXT STOPLIST stoplistName = identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-partition-function-transact-sql
alterPartitionFunction
    : ALTER PARTITION FUNCTION partitionFunctionName = identifier LP_ RP_ (SPLIT | MERGE) RANGE LP_ number RP_
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-partition-function-transact-sql
createPartitionFunction
    : CREATE PARTITION FUNCTION partitionFunctionName = identifier LP_ dataType RP_ AS RANGE (LEFT | RIGHT)
    FOR VALUES LP_ (expr (COMMA_ expr)*)? RP_
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-partition-function-transact-sql
dropPartitionFunction
    : DROP PARTITION FUNCTION partitionFunctionName = identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-partition-scheme-transact-sql
alterPartitionScheme
    : ALTER PARTITION SCHEME partitionSchemeName = identifier NEXT USED (fileGroupName = identifier)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-partition-scheme-transact-sql
createPartitionScheme
    : CREATE PARTITION SCHEME partitionSchemeName = identifier AS PARTITION partitionFunctionName = identifier
    ALL? TO LP_ file_group_name=identifier (COMMA_ file_group_name=identifier)*   RP_
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-partition-scheme-transact-sql
dropPartitionScheme
    : DROP PARTITION SCHEME partitionSchemeName = identifier
    ;


// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-remote-service-binding-transact-sql
alterRemoteServiceBinding
    : ALTER REMOTE SERVICE BINDING bindingName = identifier WITH (USER EQ_ userName)?
    (COMMA_ ANONYMOUS EQ_ (ON | OFF))?
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-remote-service-binding-transact-sql
createRemoteServiceBinding
    : CREATE REMOTE SERVICE BINDING bindingName = identifier (AUTHORIZATION owner)?
    TO SERVICE remoteServiceName = STRING_ WITH (USER EQ_ userName)? (COMMA_ ANONYMOUS EQ_ (ON | OFF))?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-remote-service-binding-transact-sql
dropRemoteServiceBinding
    : DROP REMOTE SERVICE BINDING bindingName = identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-resource-governor-transact-sql
alterResourceGovernor
    : ALTER RESOURCE GOVERNOR (
        RECONFIGURE? (WITH LP_ (CLASSIFIER_FUNCTION EQ_ (functionName | NULL))? COMMA_? (MAX_OUTSTANDING_IO_PER_VOLUME EQ_ number)?  RP_)?
        | RESET STATISTICS
        | DISABLE
    )
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-server-audit-transact-sql
alterServerAudit
    : ALTER SERVER AUDIT auditName=identifier (
        (serverAuditToClause? serverAuditWidthClause? serverAuditWhereClause?)
        | REMOVE WHERE
        | MODIFY NAME EQ_ newAuditName=identifier
    )
    ;

serverAuditToClause
    : TO ( FILE LP_ fileOptions (COMMA_ fileOptions)* RP_
        | APPLICATION_LOG
        | SECURITY_LOG
        | URL
        | EXTERNAL_MONITOR
    )
    ;

fileOptions
    : FILEPATH EQ_ filepath = stringLiterals
    | MAXSIZE EQ_ ( number (MB | GB | TB) | UNLIMITED)
    | MAX_ROLLOVER_FILES EQ_(number | UNLIMITED)
    | MAX_FILES EQ_ number
    | RESERVE_DISK_SPACE EQ_ (ON | OFF)
    ;

serverAuditWidthClause
    : WITH LP_ auditOptions (COMMA_ auditOptions)* RP_
    ;

auditOptions
    : QUEUE_DELAY EQ_ number
    | ON_FAILURE EQ_ (CONTINUE | SHUTDOWN | FAIL_OPERATION)
    | STATE EQ_ (ON | OFF)
    | AUDIT_GUID EQ_ identifier
    ;

serverAuditWhereClause
    : WHERE predicateExpression
    ;

predicateExpression
    : NOT? predicateFactor
    | (AND | OR) NOT? predicateFactor
    | COMMA_ predicateExpression
    ;

predicateFactor
    : identifier (EQ_ | NEQ_ | GT_ | GTE_ | LT_ | LTE_ | LIKE) stringOrNumber
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-server-audit-transact-sql
createServerAudit
    : CREATE SERVER AUDIT auditName=identifier serverAuditToClause? serverAuditWidthClause? serverAuditWhereClause?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-server-audit-transact-sql
dropServerAudit
    : DROP SERVER AUDIT auditName=identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-server-audit-specification-transact-sql
alterServerAuditSpecification
    : ALTER SERVER AUDIT SPECIFICATION auditSpecificationName = identifier
    (FOR SERVER AUDIT auditName = identifier)?
    (addOrDropAuditActionGroup (COMMA_ addOrDropAuditActionGroup)*)?
    auditStatusClause?
    ;

addOrDropAuditActionGroup
    : (ADD | DROP) LP_ auditActionGroupName = identifier RP_
    ;

auditStatusClause
    : WITH LP_ STATE EQ_ (ON | OFF) RP_
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-server-audit-specification-transact-sql
createServerAuditSpecification
    : CREATE SERVER AUDIT SPECIFICATION auditSpecificationName = identifier FOR SERVER AUDIT auditName = identifier
    addAuditActionGroup (COMMA_ addAuditActionGroup)* auditStatusClause?
    ;

addAuditActionGroup
    : ADD LP_ auditActionGroupName = identifier RP_
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-server-audit-specification-transact-sql
dropServerAuditSpecification
    : DROP SERVER AUDIT SPECIFICATION auditSpecificationName = identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-server-configuration-transact-sql
alterServerConfiguration
    : ALTER SERVER CONFIGURATION SET
    ( processAffinity
    | diagnosticLog
    | failoverClusterProperty
    | hadrClusterContext
    | bufferPoolExtension
    | softNuma
    | memoryOptimized
    | hardwareOffload
    | suspendForSnapshotBackup
    )
    ;
processAffinity
    : PROCESS AFFINITY
    ( CPU EQ_ (AUTO | cpuRangeSpec (COMMA_ cpuRangeSpec)*)
    | NUMANODE EQ_ numaNodeRangeSpec (COMMA_ numaNodeRangeSpec)*
    )
    ;
cpuRangeSpec
    : cpu_id=number (TO to_cpu_id=number)?
    ;
numaNodeRangeSpec
    : numa_node_id=number (TO to_numa_node_id=number)?
    ;
diagnosticLog
    : DIAGNOSTICS LOG
    ( ON
    | OFF
    | PATH EQ_ (stringLiterals | DEFAULT)
    | MAX_SIZE EQ_ (number MB | DEFAULT)
    | MAX_FILES EQ_ (number | DEFAULT)
    )
    ;
failoverClusterProperty
    : FAILOVER CLUSTER PROPERTY (
    VERBOSELOGGING EQ_ (stringLiterals | DEFAULT)
    | SQLDUMPERFLAGS EQ_ (stringLiterals | DEFAULT)
    | SQLDUMPERPATH EQ_ (stringLiterals | DEFAULT)
    | SQLDUMPERTIMEOUT (stringLiterals | DEFAULT)
    | FAILURECONDITIONLEVEL EQ_ (stringLiterals | DEFAULT)
    | HEALTHCHECKTIMEOUT EQ_ (number | DEFAULT)
    )
    ;
hadrClusterContext
    : HADR CLUSTER CONTEXT EQ_ (stringLiterals | LOCAL)
    ;
bufferPoolExtension
    : BUFFER POOL EXTENSION
    ( ON LP_ FILENAME EQ_ stringLiterals COMMA_ SIZE EQ_ number (KB | MB | GB) RP_
    | OFF
    )
    ;
softNuma
    : SET SOFTNUMA (ON | OFF)
    ;
memoryOptimized
    :  MEMORY_OPTIMIZED
    ( ON
    | OFF
    | TEMPDB_METADATA EQ_ (ON (LP_ RESOURCE_POOL EQ_ resourcePoolName=stringLiterals RP_)? | OFF)
    | HYBRID_BUFFER_POOL EQ_ (ON | OFF)
    )
    ;
hardwareOffload
    : HARDWARE_OFFLOAD (ON | OFF)
    ;
suspendForSnapshotBackup
    : SET SUSPEND_FOR_SNAPSHOT_BACKUP EQ_ (ON | OFF)
    (LP_ GROUP EQ_ LP_ databaseName (COMMA_ databaseName)* RP_ (COMMA_ MODE EQ_ COPY_ONLY)? RP_)?
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-server-role-transact-sql
alterServerRole
    : ALTER SERVER ROLE roleName=identifier (
        (ADD | DROP) MEMBER serverPrincipal = identifier
        | WITH NAME EQ_ newRoleName = identifier
    )
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-server-role-transact-sql
createServerRole
    : CREATE SERVER ROLE roleName=identifier (AUTHORIZATION serverPrincipal = identifier)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-server-role-transact-sql
dropServerRole
    : DROP SERVER ROLE roleName=identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-service-master-key-transact-sql
alterServiceMasterKey
    : ALTER SERVICE MASTER KEY
    ( FORCE? REGENERATE
    | WITH (
        OLD_ACCOUNT EQ_ oldAccountName = STRING_ COMMA_ OLD_PASSWORD EQ_ oldPassword = STRING_
        | NEW_ACCOUNT EQ_ newAccountName = STRING_ COMMA_ NEW_PASSWORD EQ_ newPassword = STRING_
        )
    )
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-workload-group-transact-sql
dropWorkloadGroup
    : DROP WORKLOAD GROUP groupName=identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-workload-group-transact-sql
alterWorkloadGroup
    : ALTER WORKLOAD GROUP groupName=identifier workloadGroupWithClause? (USING poolName = identifier)?
    ;

workloadGroupWithClause
    : WITH LP_ workloadGroupWithOption (COMMA_ workloadGroupWithOption)* RP_
    ;
workloadGroupWithOption
    : IMPORTANCE EQ_ (LOW | MEDIUM | HIGH)
    | REQUEST_MAX_MEMORY_GRANT_PERCENT EQ_ number
    | REQUEST_MAX_CPU_TIME_SEC EQ_ number
    | REQUEST_MEMORY_GRANT_TIMEOUT_SEC EQ_ number
    | MAX_DOP EQ_ number
    | GROUP_MAX_REQUESTS EQ_ number
    ;
// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-workload-group-transact-sql
createWorkloadGroup
    : CREATE WORKLOAD GROUP groupName=identifier workloadGroupWithClause?
    (USING (poolName = identifier)? (COMMA_? EXTERNAL externalPoolName = identifier)?)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-xml-schema-collection-transact-sql?view=sql-server-ver16
alterXmlSchemaCollection
    : ALTER XML SCHEMA COLLECTION (schemaName '.')? identifier ADD stringLiterals
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-xml-schema-collection-transact-sql
dropXmlSchemaCollection
    : DROP XML SCHEMA COLLECTION (schemaName '.')? identifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-xml-schema-collection-transact-sql
createXmlSchemaCollection
    : CREATE XML SCHEMA COLLECTION (schemaName '.')? identifier AS expr
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-aggregate-transact-sql
createAggregate
    : CREATE AGGREGATE aggregateName LP_ identifier dataType (COMMA_ identifier dataType)* RP_
    RETURNS returnType=dataType EXTERNAL NAME assemblyName=identifier (DOT_ className=identifier)?
    ;
aggregateName
    : (owner DOT_)? name
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-aggregate-transact-sql?view=sql-server-ver16
dropAggregate
    : DROP AGGREGATE ifExists? aggregateName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-application-role-transact-sql
createApplicationRole
    : CREATE APPLICATION ROLE applictionRole=identifier WITH
    PASSWORD EQ_ password=STRING_ (COMMA_ DEFAULT_SCHEMA EQ_ schemaName)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-application-role-transact-sql?view=sql-server-ver16
dropApplicationRole
    : DROP APPLICATION ROLE applicationRole=identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-assembly-transact-sql
createAssembly
    : CREATE ASSEMBLY assemblyName = identifier (AUTHORIZATION owner)?
    FROM assemblyFromValue (COMMA_ assemblyFromValue)*
    (WITH PERMISSION_SET EQ_ (SAFE | EXTERNAL_ACCESS | UNSAFE))?
    ;

assemblyFromValue
    : stringLiterals | hexadecimalLiterals | expr
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-assembly-transact-sql?view=sql-server-ver16
dropAssembly
    : DROP ASSEMBLY ifExists? assemblyName=identifier (COMMA_ assemblyName=identifier)* (WITH NO DEPENDENTS)?
    ;


//https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-asymmetric-key-transact-sql
createAsymmetricKey
    : CREATE ASYMMETRIC KEY asymKeyName = identifier (AUTHORIZATION databasePrincipalName = identifier)?
    (FROM asymKeySource)?
    (WITH asymKeyOption (COMMA_ asymKeyOption)*)?
    encryptionPassword?
    ;

asymKeySource
    : FILE EQ_ stringLiterals
    | EXECUTABLE_FILE EQ_ stringLiterals
    | ASSEMBLY identifier
    | PROVIDER identifier
    ;
asymKeyOption
    : ALGORITHM EQ_ (RSA_4096 | RSA_3072 | RSA_2048 | RSA_1024 | RSA_512)
    | PROVIDER_KEY_NAME EQ_ stringLiterals
    | CREATION_DISPOSITION EQ_ (CREATE_NEW | OPEN_EXISTING)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-asymmetric-key-transact-sql?view=sql-server-ver16
dropAsymmetricKey
    : DROP ASYMMETRIC KEY asymKeyName = identifier (REMOVE PROVIDER KEY)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-availability-group-transact-sql?view=sql-server-ver16
dropAvailabilityGroup
    : DROP AVAILABILITY GROUP groupName = identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-columnstore-index-transact-sql?view=sql-server-ver15
createColumnstoreIndex
    : CREATE (CLUSTERED | NONCLUSTERED)? COLUMNSTORE INDEX indexName ON tableName
    (ORDER? columnNames)? (WITH LP_ columnstoreIndexOption (',' columnstoreIndexOption)* RP_)?
    (WHERE expr)?
    (ON identifier (LP_ identifier RP_)?)?
    ;

columnstoreIndexOption
    : DROP_EXISTING '=' onOffOption
    | MAXDOP '=' number
    | ONLINE '=' onOffOption
    | COMPRESSION_DELAY '=' delay = number MINUTES?
    | DATA_COMPRESSION '=' (COLUMNSTORE | COLUMNSTORE_ARCHIVE) onPartitionClause?
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-column-master-key-transact-sql
createColumnMasterKey
    : CREATE COLUMN MASTER KEY keyName WITH LP_ columnMasterKeyOption (COMMA_ columnMasterKeyOption)* RP_
    ;

columnMasterKeyOption
    : KEY_STORE_PROVIDER_NAME EQ_ providerName = stringLiterals
    | KEY_PATH EQ_ keyPath = stringLiterals
    | ENCLAVE_COMPUTATIONS LP_ SIGNATURE EQ_ hexadecimalLiterals RP_
    ;

dropColumnMasterKey
    : DROP COLUMN MASTER KEY keyName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-database-scoped-credential-transact-sql
createDatabaseScopedCredential
    : CREATE DATABASE SCOPED CREDENTIAL credentialName=identifier
      WITH IDENTITY EQ_ stringLiterals (COMMA_ SECRET EQ_ stringLiterals)?
    ;

dropDatabaseScopedCredential
    : DROP DATABASE SCOPED CREDENTIAL credentialName=identifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-default-transact-sql
createDefault
    : CREATE DEFAULT defaultName AS bitExpr //只包含常量值的表达式
    ;

dropDefault
    : DROP DEFAULT ifExists? defaultName (COMMA_ defaultName)*
    ;

defaultName
    : (owner DOT_)? name
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-event-notification-transact-sql
createEventNotification
    : CREATE EVENT NOTIFICATION notificationName = identifier
     ON (SERVER | DATABASE | QUEUE queueName )
     (WITH FAN_IN)? FOR eventTypeOrGroup (COMMA_ eventTypeOrGroup)*
     TO SERVICE broker_service = stringLiterals COMMA_ broker_service_specifier_or_current_database = stringLiterals
    ;

eventTypeOrGroup
    : identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/drop-event-notification-transact-sql
dropEventNotification
    : DROP EVENT NOTIFICATION notificationName = identifier (COMMA_ notificationName = identifier)*
     ON (SERVER | DATABASE | QUEUE queueName)
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/drop-event-session-transact-sql
dropEventSession
    : DROP EVENT SESSION eventSessionName = identifier ON SERVER
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/alter-event-session-transact-sql
alterEventSession
    : ALTER EVENT SESSION eventSessionName = identifier ON SERVER
    (alterEventSessionClause | STATE EQ_ (START | STOP))?
    ;
alterEventSessionClause
    : eventAddOrDropClause (COMMA_ eventAddOrDropClause)* (WITH LP_ eventSessionOption (COMMA_ eventSessionOption)* RP_)?
    ;

eventAddOrDropClause
    : addEventDefinition | dropEvent | eventTargetDefinition | dropTarget
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-event-session-transact-sql
createEventSession
    : CREATE EVENT SESSION eventSessionName = identifier ON (SERVER | DATABASE) createEventSessionClause
    ;

createEventSessionClause
    : addEventDefinition (COMMA_ addEventDefinition)
    (eventTargetDefinition (COMMA_ eventTargetDefinition)*)?
    (WITH LP_ eventSessionOption (COMMA_ eventSessionOption)* RP_)?
    ;

addEventDefinition
    : ADD EVENT eventName (LP_
    ( SET eventCustomizableAttributue = identifier EQ_ stringOrNumber (COMMA_ eventCustomizableAttributue = identifier EQ_ stringOrNumber)*
    | ACTION LP_ eventActionName (COMMA_ eventActionName)* RP_
    | WHERE expr
    )?
    RP_)?
    ;
dropEvent
    : DROP EVENT eventName
    ;
eventActionName
    : (owner DOT_)* name
    ;
eventName
    : (owner DOT_)* name
    ;
eventTargetDefinition
    : ADD TARGET targetName (LP_ SET targetParameterName=identifier EQ_ stringOrNumber (COMMA_ targetParameterName=identifier EQ_ stringOrNumber)* RP_)?
    ;
dropTarget
    : DROP TARGET targetName
    ;
targetName
    : (owner DOT_)* name
    ;
eventSessionOption
    : MAX_MEMORY EQ_ number (KB | MB)?
    | EVENT_RETENTION_MODE EQ_ (ALLOW_SINGLE_EVENT_LOSS | ALLOW_MULTIPLE_EVENT_LOSS | NO_EVENT_LOSS)
    | MAX_DISPATCH_LATENCY EQ_ (number SECONDS | INFINITE)
    | MAX_EVENT_SIZE EQ_ number (KB | MB)?
    | MEMORY_PARTITION_MODE EQ_ (NONE | PER_NODE | PER_CPU)
    | TRACK_CAUSALITY EQ_ (ON | OFF)
    | STARTUP_STATE EQ_ (ON | OFF)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-external-language-transact-sql
createExternalLanguage
    : CREATE EXTERNAL LANGUAGE languageName=identifier (AUTHORIZATION owner)? FROM externalLanguageFileSpec (COMMA_  externalLanguageFileSpec)*
    ;

externalLanguageFileSpec
    : LP_ CONTENT EQ_ (stringLiterals | hexadecimalLiterals)
    COMMA_ FILE_NAME EQ_ stringLiterals
    (COMMA_ PLATFORM EQ_ (WINDOWS | LINUX))?
    (COMMA_ PARAMETERS EQ_ stringLiterals)?
    (COMMA_ ENVIRONMENT_VARIABLES EQ_ stringLiterals)? RP_
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-external-language-transact-sql
alterExternalLanguage
    : ALTER EXTERNAL LANGUAGE languageName=identifier (AUTHORIZATION owner)?
    ( SET externalLanguageFileSpec
    | ADD externalLanguageFileSpec
    | REMOVE PLATFORM (WINDOWS | LINUX)
    )
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-external-language-transact-sql
dropExternalLanguage
    : DROP EXTERNAL LANGUAGE languageName=identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/drop-external-file-format-transact-sql
dropExternalFileFormat
    : DROP EXTERNAL FILE FORMAT externalFileFormatName = identifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-external-file-format-transact-sql
createExternalFileFormat
    : CREATE EXTERNAL FILE FORMAT externalFileFormatName = identifier
      WITH LP_ (formatDelimited | formatRC | formatORC | formatParquet | formatJson | formatDelta) RP_
    ;

formatDelimited
    : FORMAT_TYPE EQ_ DELIMITEDTEXT
    (COMMA_ FORMAT_OPTIONS LP_ formatOption (COMMA_ formatOption)* RP_)?
    (COMMA_ DATA_COMPRESSION EQ_ stringLiterals)?
    ;

formatOption
    : FIELD_TERMINATOR EQ_ stringLiterals
    | STRING_DELIMITER EQ_ stringLiterals
    | FIRST_ROW EQ_ number
    | DATE_FORMAT EQ_ stringLiterals
    | USE_TYPE_DEFAULT EQ_ (TRUE | FALSE)
    | ENCODING EQ_ stringLiterals
    | PARSER_VERSION EQ_ stringLiterals
    ;

formatRC
    : FORMAT_TYPE EQ_ RCFILE COMMA_ SERDE_METHOD EQ_ stringLiterals
    (COMMA_ DATA_COMPRESSION EQ_ stringLiterals)?
    ;
formatORC
    : FORMAT_TYPE EQ_ ORC (COMMA_ DATA_COMPRESSION EQ_ stringLiterals)?
    ;
formatParquet
    : FORMAT_TYPE EQ_ PARQUET (COMMA_ DATA_COMPRESSION EQ_ stringLiterals)?
    ;
formatJson
    : FORMAT_TYPE EQ_ JSON (COMMA_ DATA_COMPRESSION EQ_ stringLiterals)?
    ;
formatDelta
    : FORMAT_TYPE EQ_ DELTA
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/drop-external-table-transact-sql
dropExternalTable
    : DROP EXTERNAL TABLE tableName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-external-table-transact-sql
createExternalTable
    : CREATE EXTERNAL TABLE tableName
      (LP_ externalColumnDefinition (COMMA_ externalColumnDefinition)* RP_)?
      WITH LP_ externalTableWithOption (COMMA_ externalTableWithOption)* RP_
      (AS select)?
    ;

externalTableWithOption
    : LOCATION EQ_ stringLiterals
    | DATA_SOURCE EQ_ dataSourceName = stringOrIdentifier
    | FILE_FORMAT EQ_ fileFormatName = identifier
    | rejectOption
    ;

externalColumnDefinition
    : columnName dataType (COLLATE collationName)? (NOT? NULL)?
    ;
rejectOption
    : REJECT_TYPE EQ_ identifier
    | REJECT_VALUE EQ_ number
    | REJECT_SAMPLE_VALUE EQ_ number
    | REJECTED_ROW_LOCATION EQ_ stringLiterals
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/drop-fulltext-index-transact-sql
dropFulltextIndex
    : DROP FULLTEXT INDEX ON tableName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-fulltext-index-transact-sql
createFulltextIndex
    : CREATE FULLTEXT INDEX ON tableName (LP_ indexColumnSpec (COMMA_ indexColumnSpec)* RP_)?
    KEY INDEX indexName
    (ON catalogFilegroupOption)?
    (WITH (LP_ withOption (COMMA_ withOption)* RP_ | withOption (COMMA_ withOption)*))?
    ;

indexColumnSpec
    : columnName (TYPE COLUMN typeColumnName = identifier)? (LANGUAGE languageTerm)? STATISTICAL_SEMANTICS?
    ;
languageTerm
    : stringLiterals | number | hexadecimalLiterals
    ;
catalogFilegroupOption
    : fulltextCatalogName
    | LP_ fulltextCatalogName COMMA_ FILEGROUP filegroupName RP_
    | LP_ FILEGROUP filegroupName COMMA_ fulltextCatalogName RP_
    | LP_ FILEGROUP filegroupName RP_
    ;
fulltextCatalogName
    : identifier
    ;
filegroupName
    : identifier
    ;
withOption
    : CHANGE_TRACKING EQ_? (MANUAL | AUTO | OFF (COMMA_ NO POPULATION)?)
    | STOPLIST EQ_? (OFF | SYSTEM | stoplist_name = identifier)
    | SEARCH PROPERTY LIST EQ_? (OFF | propertyListName)
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-fulltext-index-transact-sql
alterFulltextIndex
    : ALTER FULLTEXT INDEX ON tableName alterFulltextIndexOption
    ;

alterFulltextIndexOption
    : (ENABLE | DISABLE)
    | SET CHANGE_TRACKING EQ_? (MANUAL | AUTO | OFF)
    | ADD LP_ indexColumnSpec (COMMA_ indexColumnSpec)* RP_ (WITH NO POPULATION)?
    | ALTER COLUMN columnName (ADD | DROP) STATISTICAL_SEMANTICS (WITH NO POPULATION)?
    | DROP LP_ columnName (COMMA_ columnName)* RP_ (WITH NO POPULATION)?
    | START (FULL | INCREMENTAL | UPDATE) POPULATION
    | (STOP | PAUSE | RESUME) POPULATION
    | SET STOPLIST EQ_? (OFF | SYSTEM | stoplist_name = identifier) (WITH NO POPULATION)?
    | SET SEARCH PROPERTY LIST EQ_? (OFF | propertyListName) (WITH NO POPULATION)?
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-route-transact-sql
createRoute
    : CREATE ROUTE routeName = identifier (AUTHORIZATION owner)?
    WITH (SERVICE_NAME EQ_ route_service_name = STRING_ COMMA_)?
    (BROKER_INSTANCE EQ_ broker_instance_identifier = STRING_ COMMA_)?
    (LIFETIME EQ_ number COMMA_)?
    ADDRESS EQ_ STRING_
    (COMMA_ MIRROR_ADDRESS EQ_ STRING_)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-route-transact-sql
alterRoute
    : ALTER ROUTE routeName = identifier WITH
    (SERVICE_NAME EQ_ route_service_name = STRING_ COMMA_?)?
    (BROKER_INSTANCE EQ_ broker_instance_identifier = STRING_ COMMA_?)?
    (LIFETIME EQ_ number COMMA_?)?
    (ADDRESS EQ_ STRING_ COMMA_?)?
    (MIRROR_ADDRESS EQ_ STRING_ COMMA_?)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-route-transact-sql
dropRoute
    : DROP ROUTE routeName = identifier
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-rule-transact-sql
createRule
    : CREATE RULE ruleName AS expr
    ;

ruleName
    : (owner DOT_)? name
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-rule-transact-sql
dropRule
    : DROP RULE ifExists? ruleName (COMMA_ ruleName)*
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-search-property-list-transact-sql
createSearchPropertyList
    : CREATE SEARCH PROPERTY LIST propertyListName (FROM (databaseName DOT_)? sourceListName =name)? (AUTHORIZATION owner)?
    ;

propertyListName
    : name
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-search-property-list-transact-sql
alterSearchPropertyList
    : ALTER SEARCH PROPERTY LIST propertyListName (addProperty | dropProperty)
    ;

addProperty
    : ADD  propertyName = stringLiterals WITH LP_
    propertyOption (COMMA_ propertyOption)*
    RP_
    ;

propertyOption
    : PROPERTY_SET_GUID EQ_ stringLiterals
    | PROPERTY_INT_ID EQ_ number
    | PROPERTY_DESCRIPTION EQ_ stringLiterals
    ;

dropProperty
    : DROP propertyName = stringLiterals
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-search-property-list-transact-sql
dropSearchPropertyList
    : DROP SEARCH PROPERTY LIST propertyListName
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/create-security-policy-transact-sql
createSecurityPolicy
    : CREATE SECURITY POLICY securityPolicyName addPredicateClause (COMMA_ addPredicateClause)*
    (WITH LP_ STATE EQ_ onOffOption COMMA_? (SCHEMABINDING EQ_ onOffOption)? RP_)?
    (NOT FOR REPLICATION)?
    ;

addPredicateClause
    : ADD (FILTER | BLOCK) PREDICATE functionName funcParams ON tableName blockDmlOperation?
    ;

securityPolicyName
    : (owner DOT_)? name
    ;

funcParams
    : LP_ (columnName | expr (COMMA_ (columnName | expr))*)? RP_
    ;

blockDmlOperation
    : AFTER ( INSERT | UPDATE ) | BEFORE ( UPDATE | DELETE )
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/alter-security-policy-transact-sql
alterSecurityPolicy
    : ALTER SECURITY POLICY securityPolicyName (alterSecurityPolicyClause (COMMA_ alterSecurityPolicyClause)*)?
    (WITH LP_ STATE EQ_ onOffOption RP_)? (NOT FOR REPLICATION)?
    ;

alterSecurityPolicyClause
    : addPredicateClause | alterPredicateClause | dropPredicateClause
    ;

alterPredicateClause
    : ALTER (FILTER | BLOCK) PREDICATE functionName funcParams ON tableName blockDmlOperation?
    ;

dropPredicateClause
    : DROP (FILTER | BLOCK) PREDICATE ON tableName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-security-policy-transact-sql
dropSecurityPolicy
    : DROP SECURITY POLICY ifExists? securityPolicyName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-sensitivity-classification-transact-sql
dropSensitivityClassification
    : DROP SENSITIVITY CLASSIFICATION FROM columnName (COMMA_ columnName)*
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-signature-transact-sql
dropSignature
    : DROP COUNTER? SIGNATURE FROM objectName BY cryptoList (COMMA_ cryptoList)*
    ;

cryptoList
    : CERTIFICATE identifier
    | ASYMMETRIC KEY identifier
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-synonym-transact-sql
createSynonym
    : CREATE SYNONYM synonymName FOR objectName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-synonym-transact-sql
dropSynonym
    : DROP SYNONYM ifExists? synonymName
    ;

synonymName
    : (owner DOT_)? name
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/create-type-transact-sql
createType
    : CREATE TYPE typeName (typeFromClause | typeExtrnalClause | typeAsTableClause)
    ;

typeFromClause
    : FROM dataType (NOT? NULL)?
    ;

typeExtrnalClause
    : EXTERNAL NAME assemblyName=identifier (DOT_ className=identifier)?
    ;

typeAsTableClause
    : AS TABLE createTableDefinitions (WITH LP_ tableOption (COMMA_ tableOption)* RP_)?
    ;

typeName
    : (owner DOT_)? name
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/drop-type-transact-sql
dropType
    : DROP TYPE ifExists? typeName
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/disable-trigger-transact-sql
disableTrigger
    : DISABLE TRIGGER (triggerName (COMMA_ triggerName)* | ALL) ON (
        objectName
        | DATABASE
        | ALL SERVER
    )
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/enable-trigger-transact-sql
enableTrigger
    : ENABLE TRIGGER (triggerName (COMMA_ triggerName)* | ALL) ON (
        objectName
        | DATABASE
        | ALL SERVER
    )
    ;

// https://docs.microsoft.com/zh-cn/sql/t-sql/statements/backup-transact-sql
backupDatabase
    : BACKUP DATABASE databaseName
    ( fileOrFilegroup (COMMA_ fileOrFilegroup)*
    | readWriteFilegroups)?
    TO backupDevice (COMMA_ backupDevice)*
    (MIRROR TO backupDevice)*
    (WITH (DIFFERENTIAL | generalWithOption) (COMMA_ (DIFFERENTIAL | generalWithOption))*)?
    ;

backupDevice
    : deviceName=identifier | (DISK | TAPE | URL | DATABASE_SNAPSHOT) EQ_ stringOrIdentifier
    ;

fileOrFilegroup
    : (FILE | FILEGROUP) EQ_ stringOrIdentifier
    ;

readWriteFilegroups
    : READ_WRITE_FILEGROUPS (COMMA_ FILEGROUP EQ_ stringOrIdentifier ( COMMA_ FILEGROUP EQ_ stringOrIdentifier)*)?
    ;

generalWithOption
    : backupSetOptions
    | mediaSetOptions
    | dataTransferOptions
    | errorManagementOptions
    | compatibilityOptions
    | monitoringOptions
    | tapeOptions
    | encryptionOptions
    ;

backupSetOptions
    : COPY_ONLY
    | COMPRESSION (ALGORITHM EQ_ (MS_XPRESS | accelerator_algorithm =identifier))?
    | NO_COMPRESSION
    | DESCRIPTION EQ_ stringOrIdentifier
    | NAME EQ_ backup_set_name = identifier
    | CREDENTIAL
    | ENCRYPTION
    | FILE_SNAPSHOT
    | (EXPIREDATE | RETAINDAYS) EQ_ stringOrIdentifier
    | METADATA_ONLY | SNAPSHOT
    | BACKUP_OPTIONS EQ_ stringOrIdentifier
    ;

mediaSetOptions
    : NOINIT | INIT
    | NOSKIP | SKIP_
    | NOFORMAT | FORMAT
    | MEDIADESCRIPTION EQ_ stringOrIdentifier
    | MEDIANAME EQ_ stringOrIdentifier
    | BLOCKSIZE EQ_ numberOrIdentifier
    ;

dataTransferOptions
    : BUFFERCOUNT EQ_ numberOrIdentifier
    | MAXTRANSFERSIZE EQ_ numberOrIdentifier
    ;

errorManagementOptions
    : NO_CHECKSUM | CHECKSUM
    | STOP_ON_ERROR | CONTINUE_AFTER_ERROR
    ;

compatibilityOptions
    : RESTART
    ;

monitoringOptions
    : STATS EQ_ number
    ;

tapeOptions
    : REWIND | NOREWIND
    | UNLOAD | NOUNLOAD
    ;

encryptionOptions
    : ENCRYPTION LP_ ALGORITHM EQ_ (AES_128 | AES_192 | AES_256 | TRIPLE_DES_3KEY)
    COMMA_ SERVER (CERTIFICATE | ASYMMETRIC KEY) EQ_ encryptorName=identifier RP_
    ;

logSpecificOptions
    : NORECOVERY
    | STANDBY EQ_ identifier
    | NO_TRUNCATE
    ;

backupLog
    : BACKUP LOG databaseName TO backupDevice (COMMA_ backupDevice)*
    (MIRROR TO backupDevice (COMMA_ backupDevice)*)?
    (WITH (generalWithOption | logSpecificOptions) (COMMA_ (generalWithOption | logSpecificOptions))*)?
    ;

backupService
    : BACKUP SERVICE TO backupDevice (COMMA_ backupDevice)*
    (MIRROR TO backupDevice (COMMA_ backupDevice)*)?
    (WITH generalWithOption (COMMA_ generalWithOption)*)?
    ;

backupGroup
    : BACKUP GROUP databaseName (COMMA_ databaseName)*
    TO backupDevice (COMMA_ backupDevice)*
    (MIRROR TO backupDevice (COMMA_ backupDevice)*)?
    (WITH generalWithOption (COMMA_ generalWithOption)*)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/backup-certificate-transact-sql
backupCertificate
    : BACKUP CERTIFICATE certificateName TO FILE EQ_ path_to_file=stringLiterals
    (WITH (formatSpec COMMA_)? privateKeySpec)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/backup-master-key-transact-sql
backupMasterKey
    : BACKUP MASTER KEY TO (FILE | URL) EQ_ path=stringLiterals encryptionPassword
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/backup-service-master-key-transact-sql
backupServiceMasterKey
    : BACKUP SERVICE MASTER KEY TO FILE EQ_ path=stringLiterals encryptionPassword
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/backup-symmetric-key-transact-sql
backupSymmetricKey
    : BACKUP SYMMETRIC KEY keyName TO (FILE | URL) EQ_ path=stringLiterals encryptionPassword
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/restore-statements-transact-sql
restoreDatabase
    : RESTORE DATABASE databaseName
    (fileOrFilegroupOrPages (COMMA_ fileOrFilegroupOrPages)*)?
    (FROM backupDevice (COMMA_ backupDevice)*)?
    (WITH restoreDatabaseOption (COMMA_ restoreDatabaseOption)*)?
    ;

restoreDatabaseOption
    : RECOVERY
    | NORECOVERY
    | PARTIAL
    | STANDBY EQ_ stringOrIdentifier
    | RESTORE_OPTIONS EQ_ stringOrIdentifier
    | restoreGeneralWithOption
    | replicationWithOption
    | changeDataCaptureWithOption
    | fIlestreamWithOption
    | serviceBrokerWithOptions
    | pointInTimeWithOptions
    ;

fileOrFilegroupOrPages
    : fileOrFilegroup | READ_WRITE_FILEGROUPS | PAGE EQ_ pages=stringLiterals
    ;

restoreGeneralWithOption
    : restoreOperationOptions
    | restoreBackupSetOptions
    | restoreMediaSetOptions
    | dataTransferOptions
    | errorManagementOptions
    | restoreMonitoringOptions
    | tapeOptions
    ;

restoreOperationOptions
    : MOVE (logical_file=stringOrIdentifier TO operating_file=stringLiterals) (COMMA_ logical_file=stringOrIdentifier TO operating_file=stringLiterals)*
    | REPLACE
    | RESTART
    | RESTRICTED_USER | CREDENTIAL
    ;

restoreBackupSetOptions
    : FILE EQ_ numberOrIdentifier
    | PASSWORD EQ_ stringOrIdentifier
    | METADATA_ONLY | SNAPSHOT
    | (METADATA_ONLY | SNAPSHOT)? DBNAME EQ_ databaseName
    ;

restoreMediaSetOptions
    : MEDIANAME EQ_ stringOrIdentifier
    | MEDIAPASSWORD EQ_ stringOrIdentifier
    | BLOCKSIZE EQ_ numberOrIdentifier
    ;
restoreMonitoringOptions
    : STATS (EQ_ number)?
    ;

replicationWithOption
    : KEEP_REPLICATION
    ;

changeDataCaptureWithOption
    : KEEP_CDC
    ;

fIlestreamWithOption
    : FILESTREAM LP_ DIRECTORY_NAME EQ_ identifier RP_
    ;

serviceBrokerWithOptions
    : ENABLE_BROKER
    | ERROR_BROKER_CONVERSATIONS
    | NEW_BROKER
    ;

pointInTimeWithOptions
    : STOPAT EQ_ stringOrIdentifier
    | (STOPATMARK | STOPBEFOREMARK) EQ_ stringOrIdentifier (AFTER stringOrIdentifier)?
    ;

restoreLog
    : RESTORE LOG databaseName
    (fileOrFilegroupOrPages (COMMA_ fileOrFilegroupOrPages)*)?
    (FROM backupDevice (COMMA_ backupDevice)*)?
    (WITH restoreLogOption (COMMA_ restoreLogOption)*)?
    ;

restoreLogOption
    : RECOVERY
    | NORECOVERY
    | STANDBY EQ_ stringOrIdentifier
    | restoreGeneralWithOption
    | replicationWithOption
    | pointInTimeWithOptions
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/restore-statements-filelistonly-transact-sql
restoreFilelistonly
    : RESTORE FILELISTONLY FROM backupDevice (WITH restoreFilelistonlyOption (COMMA_ restoreFilelistonlyOption)*)?
    ;

restoreFilelistonlyOption
    : restoreBackupSetOptions
    | restoreMediaSetOptions
    | errorManagementOptions
    | tapeOptions
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/restore-statements-headeronly-transact-sql
restoreHeaderonly
    : RESTORE HEADERONLY FROM backupDevice (WITH restoreFilelistonlyOption (COMMA_ restoreFilelistonlyOption)*)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/restore-statements-labelonly-transact-sql
restoreLabelonly
    : RESTORE LABELONLY FROM backupDevice (WITH restoreLabelonlyOption (COMMA_ restoreLabelonlyOption)*)?
    ;

restoreLabelonlyOption
    : restoreMediaSetOptions
    | errorManagementOptions
    | tapeOptions
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/restore-master-key-transact-sql
restoreMasterKey
    : RESTORE MASTER KEY FROM (FILE | URL) EQ_ path=stringLiterals decryptionPassword encryptionPassword FORCE?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/restore-service-master-key-transact-sql
restoreServiceMasterKey
    : RESTORE SERVICE MASTER KEY FROM FILE EQ_ path=stringLiterals decryptionPassword FORCE?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/restore-symmetric-key-transact-sql
restoreSymmetricKey
    : RESTORE SYMMETRIC KEY keyName FROM (FILE | URL) EQ_ path=stringLiterals decryptionPassword encryptionPassword
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/restore-statements-rewindonly-transact-sql
restoreRewindonly
    : RESTORE REWINDONLY FROM backupDevice (COMMA_ backupDevice)* (WITH (UNLOAD | NOUNLOAD))?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/statements/restore-statements-verifyonly-transact-sql
restoreVerifyonly
    : RESTORE VERIFYONLY FROM backupDevice (COMMA_ backupDevice)*
    (WITH (LOADHISTORY | restoreVerifyonlyOption) (COMMA_ (LOADHISTORY | restoreVerifyonlyOption))* )?
    ;

restoreVerifyonlyOption
    : MOVE (logical_file=stringLiterals TO operating_file=stringLiterals) (COMMA_ logical_file=stringLiterals TO operating_file=stringLiterals)*
    | FILE EQ_ numberOrIdentifier
    | PASSWORD EQ_ stringOrIdentifier
    | restoreMediaSetOptions
    | errorManagementOptions
    | restoreMonitoringOptions
    | tapeOptions
    ;





// TODO ========================================================================

parameterMarker
    : QUESTION_
    ;

literals
    : stringLiterals
    | numberLiterals
    | dateTimeLiterals
    | hexadecimalLiterals
    | bitValueLiterals
    | booleanLiterals
    | nullValueLiterals
    ;

stringLiterals
    : STRING_
    | NCHAR_TEXT
    ;

numberLiterals
    : (PLUS_ | MINUS_)? number
    ;

dateTimeLiterals
    : (DATE | TIME | TIMESTAMP) STRING_
    | LBE_ identifier STRING_ RBE_
    ;
number
    : INT_NUM_ | FLOAT_NUM_ | DECIMAL_NUM_
    ;
hexadecimalLiterals
    : HEX_DIGIT_
    ;

bitValueLiterals
    : BIT_NUM_
    ;

booleanLiterals
    : TRUE
    | FALSE
    ;

nullValueLiterals
    : NULL
    ;

identifier
    : regularIdentifier
    | SERVICE_IDENTIFIER_
    | DELIMITED_IDENTIFIER_
    | parameterMarker
    ;

regularIdentifier
    : IDENTIFIER_
    | unreservedWord
    ;

stringOrIdentifier
    : stringLiterals | identifier
    ;

numberOrIdentifier
    : numberLiterals | identifier
    ;
stringOrNumber
    : stringLiterals | numberLiterals
    ;
stringOrNumberOrIdentifier
    : stringLiterals | numberLiterals | identifier
    ;
unreservedWord
    : TRUNCATE
    | NAME
    | NEWNAME
    | MONEY
    | NOLOCK
    | LEFT
    | VALUES
    | XMLDATA
    | INT
    | IDENTITY
    | PROPERTY
    | FUNCTION
    | TRIGGER
    | LIMIT
    | OFFSET
    | SAVEPOINT
    | BOOLEAN
    | ARRAY
    | LOCALTIME
    | LOCALTIMESTAMP
    | QUARTER
    | WEEK
    | MICROSECOND
    | ENABLE
    | DISABLE
    | BINARY
    | HIDDEN_
    | MOD
    | PARTITION
    | TOP
    | ROW
    | XOR
    | ALWAYS
    | ROLE
    | START
    | ALGORITHM
    | AUTO
    | BLOCKERS
    | CLUSTERED
    | COLUMNSTORE
    | CONTENT
    | CONCAT
    | DATABASE
    | DAYS
    | DENY
    | DETERMINISTIC
    | DISTRIBUTION
    | DOCUMENT
    | DURABILITY
    | ENCRYPTED
    | FILESTREAM
    | FILETABLE
    | FOLLOWING
    | HASH
    | HEAP
    | INBOUND
    | INFINITE
    | LOGIN
    | MASKED
    | MAXDOP
    | MINUTES
    | MONTHS
    | MOVE
    | NOCHECK
    | NONCLUSTERED
    | OBJECT
    | OFF
    | ONLINE
    | OUTBOUND
    | OVER
    | PAGE
    | PARTITIONS
    | PAUSED
    | PERIOD
    | PERSISTED
    | PRECEDING
    | RANDOMIZED
    | RANGE
    | REBUILD
    | REPLICATE
    | REPLICATION
    | RESUMABLE
    | ROWGUIDCOL
    | SAVE
    | SELF
    | SPARSE
    | SWITCH
    | TRAN
    | TRANCOUNT
    | UNBOUNDED
    | YEARS
    | WEEKS
    | ABORT_AFTER_WAIT
    | ALLOW_PAGE_LOCKS
    | ALLOW_ROW_LOCKS
    | ALL_SPARSE_COLUMNS
    | BUCKET_COUNT
    | COLUMNSTORE_ARCHIVE
    | COLUMN_ENCRYPTION_KEY
    | COLUMN_SET
    | COMPRESSION_DELAY
    | DATA_COMPRESSION
    | DATA_CONSISTENCY_CHECK
    | ENCRYPTION_TYPE
    | SYSTEM_TIME
    | SYSTEM_VERSIONING
    | TEXTIMAGE_ON
    | WAIT_AT_LOW_PRIORITY
    | STATISTICS_INCREMENTAL
    | STATISTICS_NORECOMPUTE
    | ROUND_ROBIN
    | SCHEMA_AND_DATA
    | SCHEMA_ONLY
    | SORT_IN_TEMPDB
    | IGNORE_DUP_KEY
    | IMPLICIT_TRANSACTIONS
    | MAX_DURATION
    | MEMORY_OPTIMIZED
    | MIGRATION_STATE
    | PAD_INDEX
    | REMOTE_DATA_ARCHIVE
    | FILESTREAM_ON
    | FILETABLE_COLLATE_FILENAME
    | FILETABLE_DIRECTORY
    | FILETABLE_FULLPATH_UNIQUE_CONSTRAINT_NAME
    | FILETABLE_PRIMARY_KEY_CONSTRAINT_NAME
    | FILETABLE_STREAMID_UNIQUE_CONSTRAINT_NAME
    | FILLFACTOR
    | FILTER_PREDICATE
    | HISTORY_RETENTION_PERIOD
    | HISTORY_TABLE
    | LOCK_ESCALATION
    | DROP_EXISTING
    | ROW_NUMBER
    | CONTROL
    | TAKE
    | OWNERSHIP
    | DEFINITION
    | APPLICATION
    | ASSEMBLY
    | SYMMETRIC
    | ASYMMETRIC
    | SERVER
    | RECEIVE
    | CHANGE
    | TRACE
    | TRACKING
    | RESOURCES
    | SETTINGS
    | STATE
    | AVAILABILITY
    | CREDENTIAL
    | ENDPOINT
    | EVENT
    | NOTIFICATION
    | LINKED
    | AUDIT
    | DDL
    | SQL
    | XML
    | IMPERSONATE
    | SECURABLES
    | AUTHENTICATE
    | EXTERNAL
    | ACCESS
    | ADMINISTER
    | BULK
    | OPERATIONS
    | UNSAFE
    | SHUTDOWN
    | SCOPED
    | CONFIGURATION
    | DATASPACE
    | SERVICE
    | CERTIFICATE
    | CONTRACT
    | ENCRYPTION
    | MASTER
    | DATA
    | SOURCE
    | FILE
    | FORMAT
    | LIBRARY
    | FULLTEXT
    | MASK
    | UNMASK
    | MESSAGE
    | TYPE
    | REMOTE
    | BINDING
    | ROUTE
    | SECURITY
    | POLICY
    | AGGREGATE
    | QUEUE
    | RULE
    | SYNONYM
    | COLLECTION
    | SCRIPT
    | KILL
    | BACKUP
    | LOG
    | SHOWPLAN
    | SUBSCRIBE
    | QUERY
    | NOTIFICATIONS
    | CHECKPOINT
    | SEQUENCE
    | INSTANCE
    | DO
    | DEFINER
    | LOCAL
    | CASCADED
    | NEXT
    | NAME
    | INTEGER
    | TYPE
    | MAX
    | MIN
    | SUM
    | COUNT
    | AVG
    | FIRST
    | DATETIME2
    | OUTPUT
    | INSERTED
    | DELETED
    | KB
    | MB
    | GB
    | TB
    | FILENAME
    | MAXSIZE
    | FILEGROWTH
    | UNLIMITED
    | MEMORY_OPTIMIZED_DATA
    | FILEGROUP
    | NON_TRANSACTED_ACCESS
    | DB_CHAINING
    | TRUSTWORTHY
    | GROUP
    | ROWS
    | DATE
    | DATEPART
    | CAST
    | DAY
    | FORWARD_ONLY
    | KEYSET
    | FAST_FORWARD
    | READ_ONLY
    | SCROLL_LOCKS
    | OPTIMISTIC
    | TYPE_WARNING
    | SCHEMABINDING
    | CALLER
    | OWNER
    | SNAPSHOT
    | REPEATABLE
    | SERIALIZABLE
    | NATIVE_COMPILATION
    | VIEW_METADATA
    | INSTEAD
    | APPEND
    | INCREMENT
    | CACHE
    | MINVALUE
    | MAXVALUE
    | RESTART
    | LOB_COMPACTION
    | COMPRESS_ALL_ROW_GROUPS
    | REORGANIZE
    | RESUME
    | PAUSE
    | ABORT
    | ACCELERATED_DATABASE_RECOVERY
    | PERSISTENT_VERSION_STORE_FILEGROUP
    | IMMEDIATE
    | NO_WAIT
    | TARGET_RECOVERY_TIME
    | SECONDS
    | HONOR_BROKER_PRIORITY
    | ERROR_BROKER_CONVERSATIONS
    | NEW_BROKER
    | DISABLE_BROKER
    | ENABLE_BROKER
    | MEMORY_OPTIMIZED_ELEVATE_TO_SNAPSHOT
    | READ_COMMITTED_SNAPSHOT
    | ALLOW_SNAPSHOT_ISOLATION
    | RECURSIVE_TRIGGERS
    | QUOTED_IDENTIFIER
    | NUMERIC_ROUNDABORT
    | CONCAT_NULL_YIELDS_NULL
    | COMPATIBILITY_LEVEL
    | ARITHABORT
    | ANSI_WARNINGS
    | ANSI_PADDING
    | ANSI_NULLS
    | ANSI_NULL_DEFAULT
    | PAGE_VERIFY
    | CHECKSUM
    | TORN_PAGE_DETECTION
    | BULK_LOGGED
    | RECOVERY
    | TOTAL_EXECUTION_CPU_TIME_MS
    | TOTAL_COMPILE_CPU_TIME_MS
    | STALE_CAPTURE_POLICY_THRESHOLD
    | EXECUTION_COUNT
    | QUERY_CAPTURE_POLICY
    | WAIT_STATS_CAPTURE_MODE
    | MAX_PLANS_PER_QUERY
    | QUERY_CAPTURE_MODE
    | SIZE_BASED_CLEANUP_MODE
    | INTERVAL_LENGTH_MINUTES
    | MAX_STORAGE_SIZE_MB
    | DATA_FLUSH_INTERVAL_SECONDS
    | CLEANUP_POLICY
    | CUSTOM
    | STALE_QUERY_THRESHOLD_DAYS
    | OPERATION_MODE
    | QUERY_STORE
    | CURSOR_DEFAULT
    | GLOBAL
    | CURSOR_CLOSE_ON_COMMIT
    | HOURS
    | CHANGE_RETENTION
    | AUTO_CLEANUP
    | CHANGE_TRACKING
    | AUTOMATIC_TUNING
    | FORCE_LAST_GOOD_PLAN
    | AUTO_UPDATE_STATISTICS_ASYNC
    | AUTO_UPDATE_STATISTICS
    | AUTO_SHRINK
    | AUTO_CREATE_STATISTICS
    | INCREMENTAL
    | AUTO_CLOSE
    | DATA_RETENTION
    | TEMPORAL_HISTORY_RETENTION
    | EDITION
    | MIXED_PAGE_ALLOCATION
    | DISABLED
    | ALLOWED
    | HADR
    | MULTI_USER
    | RESTRICTED_USER
    | SINGLE_USER
    | OFFLINE
    | EMERGENCY
    | SUSPEND
    | DATE_CORRELATION_OPTIMIZATION
    | ELASTIC_POOL
    | SERVICE_OBJECTIVE
    | DATABASE_NAME
    | ALLOW_CONNECTIONS
    | GEO
    | NAMED
    | DATEFIRST
    | BACKUP_STORAGE_REDUNDANCY
    | FORCE_FAILOVER_ALLOW_DATA_LOSS
    | SECONDARY
    | FAILOVER
    | DEFAULT_FULLTEXT_LANGUAGE
    | DEFAULT_LANGUAGE
    | INLINE
    | NESTED_TRIGGERS
    | TRANSFORM_NOISE_WORDS
    | TWO_DIGIT_YEAR_CUTOFF
    | PERSISTENT_LOG_BUFFER
    | DIRECTORY_NAME
    | DATEFORMAT
    | DELAYED_DURABILITY
    | TRANSFER
    | SCHEMA
    | PASSWORD
    | AUTHORIZATION
    | MEMBER
    | SEARCH
    | TEXT
    | SECOND
    | PRECISION
    | VIEWS
    | PROVIDER
    | COLUMNS
    | SUBSTRING
    | RETURNS
    | SIZE
    | CONTAINS
    | MONTH
    | INPUT
    | YEAR
    | TIMESTAMP
    | TRIM
    | USER
    | RIGHT
    | JSON
    | SID
    | OPENQUERY
    | ACTION
    | TARGET
    | HOUR
    | MINUTE
    | TABLE
    | ABSOLUTE
    | ACCELERATED_PLAN_FORCING
    | ACCENT_SENSITIVITY
    | ACTIVATION
    | ACTIVE
    | ADDRESS
    | AES
    | AES_128
    | AES_192
    | AES_256
    | AFFINITY
    | ALLOW_MULTIPLE_EVENT_LOSS
    | ALLOW_SINGLE_EVENT_LOSS
    | ANONYMOUS
    | ANSI_DEFAULTS
    | ANSI_NULL_DFLT_OFF
    | ANSI_NULL_DFLT_ON
    | APPLICATION_LOG
    | ARITHIGNORE
    | ASYNC_STATS_UPDATE_WAIT_AT_LOW_PRIORITY
    | ASYNCHRONOUS_COMMIT
    | AUDIT_GUID
    | AUTHENTICATION
    | AUTOMATED_BACKUP_PREFERENCE
    | AUTOMATIC
    | AVAILABILITY_MODE
    | BACKUP_PRIORITY
    | BASIC
    | BATCH_MODE_ADAPTIVE_JOINS
    | BATCH_MODE_MEMORY_GRANT_FEEDBACK
    | BATCH_MODE_ON_ROWSTORE
    | BEFORE
    | BEGIN_DIALOG
    | BLOCK
    | BLOCKSIZE
    | BREAK
    | BROKER
    | BROKER_INSTANCE
    | BUFFER
    | BUFFERCOUNT
    | CATCH
    | CE_FEEDBACK
    | CLASSIFIER_FUNCTION
    | CLEANUP
    | CLEAR
    | CLUSTER
    | CLUSTER_TYPE
    | COLUMN_MASTER_KEY
    | COMPRESSION
    | CONFIGURATION_ONLY
    | CONNECTION_OPTIONS
    | CONTAINED
    | CONTEXT
    | CONTINUE
    | CONTINUE_AFTER_ERROR
    | CONTRACT_NAME
    | CONVERSATION
    | COPY_ONLY
    | CPU
    | CREATE_NEW
    | CREATION_DISPOSITION
    | CRYPTOGRAPHIC
    | DATA_SOURCE
    | DATABASE_MIRRORING
    | DATE_FORMAT
    | DB_FAILOVER
    | DBNAME
    | DEALLOCATE
    | DECRYPTION
    | DEFERRED_COMPILATION_TV
    | DELAY
    | DELIMITEDTEXT
    | DELTA
    | DENSE_RANK
    | DES
    | DESCRIPTION
    | DESX
    | DHCP
    | DIAGNOSTICS
    | DIALOG
    | DIFFERENTIAL
    | DISABLE_OPTIMIZED_PLAN_FORCING
    | DISK
    | DOP_FEEDBACK
    | ELEVATE_ONLINE
    | ELEVATE_RESUMABLE
    | EMPTY
    | ENABLED
    | ENCLAVE_COMPUTATIONS
    | ENCODING
    | ENCRYPTED_VALUE
    | ENDPOINT_URL
    | ENVIRONMENT_VARIABLES
    | ERROR
    | EVENT_RETENTION_MODE
    | EXEC_QUERY_STATS_FOR_SCALAR_FUNCTIONS
    | EXECUTABLE
    | EXECUTABLE_FILE
    | EXPIREDATE
    | EXPIRY_DATE
    | EXTENSION
    | EXTERNAL_ACCESS
    | EXTERNAL_MONITOR
    | FAIL_OPERATION
    | FAIL_UNSUPPORTED
    | FAILOVER_MODE
    | FAILURE_CONDITION_LEVEL
    | FAN_IN
    | FEDERATED_SERVICE_ACCOUNT
    | FIELD_TERMINATOR
    | FILE_FORMAT
    | FILE_NAME
    | FILE_SNAPSHOT
    | FILELISTONLY
    | FILEPATH
    | FILETABLE_NAMESPACE
    | FILTER
    | FIRST_ROW
    | FMTONLY
    | FORCE_SERVICE_ALLOW_DATA_LOSS
    | FORCEPLAN
    | FORCESCAN
    | FORCESEEK
    | FORMAT_OPTIONS
    | GET
    | GET_TRANSMISSION_STATUS
    | GLOBAL_TEMPORARY_TABLE_AUTO_DROP
    | GOVERNOR
    | GROUP_MAX_REQUESTS
    | HARDWARE_OFFLOAD
    | HEADERONLY
    | HEALTH_CHECK_TIMEOUT
    | HIGH
    | HYBRID_BUFFER_POOL
    | IDENTITY_CACHE
    | IDENTITY_INSERT
    | IDENTITY_VALUE
    | IMPORTANCE
    | INIT
    | INITIATOR
    | INTERLEAVED_EXECUTION_TVF
    | IO
    | IP
    | ISOLATE_SECURITY_POLICY_CARDINALITY
    | JOB
    | KEEP_CDC
    | KEEP_REPLICATION
    | KERBEROS
    | KEY_PATH
    | KEY_SOURCE
    | KEY_STORE_PROVIDER_NAME
    | KEYS
    | LABEL
    | LABELONLY
    | LAST
    | LAST_QUERY_PLAN_STATS
    | LEDGER
    | LEDGER_DIGEST_STORAGE_ENDPOINT
    | LEGACY_CARDINALITY_ESTIMATION
    | LIFETIME
    | LIGHTWEIGHT_QUERY_PROFILING
    | LINUX
    | LISTENER
    | LISTENER_IP
    | LISTENER_URL
    | LOADHISTORY
    | LOCAL_SERVICE_NAME
    | LOCATION
    | LOW
    | MANUAL
    | MAX_CPU_PERCENT
    | MAX_DISPATCH_LATENCY
    | MAX_DOP
    | MAX_EVENT_SIZE
    | MAX_FILES
    | MAX_MEMORY
    | MAX_MEMORY_PERCENT
    | MAX_OUTSTANDING_IO_PER_VOLUME
    | MAX_PROCESSES
    | MAX_QUEUE_READERS
    | MAX_ROLLOVER_FILES
    | MAX_SIZE
    | MAXTRANSFERSIZE
    | MEDIADESCRIPTION
    | MEDIANAME
    | MEDIAPASSWORD
    | MEDIUM
    | MEMORY_GRANT_FEEDBACK_PERCENTILE_GRANT
    | MEMORY_GRANT_FEEDBACK_PERSISTENCE
    | MEMORY_PARTITION_MODE
    | MESSAGE_FORWARD_SIZE
    | MESSAGE_FORWARDING
    | METADATA_ONLY
    | MIRROR
    | MIRROR_ADDRESS
    | MODE
    | MS_XPRESS
    | NEGOTIATE
    | NEW_ACCOUNT
    | NEW_PASSWORD
    | NO_CHECKSUM
    | NO_COMPRESSION
    | NO_EVENT_LOSS
    | NO_TRUNCATE
    | NOCOUNT
    | NOEXEC
    | NOEXPAND
    | NOFORMAT
    | NOINIT
    | NORECOVERY
    | NOREWIND
    | NOSKIP
    | NOUNLOAD
    | NTILE
    | NTLM
    | NUMANODE
    | OBJECT_ID
    | OFF_WITHOUT_DATA_RECOVERY
    | OLD_ACCOUNT
    | ON_FAILURE
    | OPEN_EXISTING
    | OPTIMIZE_FOR_AD_HOC_WORKLOADS
    | OPTIMIZED_PLAN_FORCING
    | OPTIMIZED_SP_EXECUTESQL
    | ORC
    | OVERRIDE
    | PAGECOUNT
    | PARAMETER_SENSITIVE_PLAN_OPTIMIZATION
    | PARAMETER_SNIFFING
    | PARAMETERS
    | PARQUET
    | PARSEONLY
    | PARSER_VERSION
    | PARTNER
    | PAUSED_RESUMABLE_INDEX_ABORT_DURATION_MINUTES
    | PER_CPU
    | PER_NODE
    | PERMISSION_SET
    | PLATFORM
    | POISON_MESSAGE_HANDLING
    | POOL
    | POPULATION
    | PORT
    | PREDICATE
    | PRIMARY_ROLE
    | PRINT
    | PRIOR
    | PRIORITY
    | PRIORITY_LEVEL
    | PRIVATE
    | PROCEDURE_CACHE
    | PROCEDURE_NAME
    | PROCESS
    | PROFILE
    | PROPERTY_DESCRIPTION
    | PROPERTY_INT_ID
    | PROPERTY_SET_GUID
    | PROVIDER_KEY_NAME
    | PUSHDOWN
    | QUERY_OPTIMIZER_HOTFIXES
    | QUEUE_DELAY
    | RAISERROR
    | RANK
    | RC2
    | RC4
    | RC4_128
    | RCFILE
    | READ_ONLY_ROUTING_LIST
    | READ_ONLY_ROUTING_URL
    | READ_WRITE_FILEGROUPS
    | READ_WRITE_ROUTING_URL
    | READUNCOMMITTED
    | RECONFIGURE
    | REGENERATE
    | REJECT_SAMPLE_VALUE
    | REJECT_TYPE
    | REJECT_VALUE
    | REJECTED_ROW_LOCATION
    | RELATED_CONVERSATION
    | RELATED_CONVERSATION_GROUP
    | RELATIVE
    | REMOTE_PROC_TRANSACTIONS
    | REMOTE_SERVICE_NAME
    | REPLACE
    | REPLICA
    | REQUEST_MAX_CPU_TIME_SEC
    | REQUEST_MAX_MEMORY_GRANT_PERCENT
    | REQUEST_MEMORY_GRANT_TIMEOUT_SEC
    | REQUIRED
    | REQUIRED_SYNCHRONIZED_SECONDARIES_TO_COMMIT
    | RESERVE_DISK_SPACE
    | RESET
    | RESOURCE
    | RESOURCE_MANAGER_LOCATION
    | RESOURCE_POOL
    | RESTORE
    | RESULT
    | RETAINDAYS
    | RETENTION
    | REUSE_SYSTEM_DATABASES
    | REWIND
    | REWINDONLY
    | ROW_MODE_MEMORY_GRANT_FEEDBACK
    | ROWCOUNT
    | ROWGUID
    | RSA_512
    | RSA_1024
    | RSA_2048
    | RSA_3072
    | RSA_4096
    | SAFE
    | SAFETY
    | SCHEME
    | SECONDARY_ONLY
    | SECONDARY_ROLE
    | SECRET
    | SECURITY_LOG
    | SEEDING_MODE
    | SENT
    | SEQUENCE_NUMBER
    | SERDE_METHOD
    | SERVICE_BROKER
    | SERVICE_NAME
    | SESSION_TIMEOUT
    | SETERROR
    | SETS
    | SHOWPLAN_ALL
    | SHOWPLAN_TEXT
    | SHOWPLAN_XML
    | SIGNATURE
    | SKIP_
    | SOFTNUMA
    | SPATIAL_WINDOW_MAX_CELLS
    | SPECIFICATION
    | SPLIT
    | STANDBY
    | START_DATE
    | STARTED
    | STARTUP_STATE
    | STATISTICAL_SEMANTICS
    | STATS
    | STATS_STREAM
    | STATUS
    | STATUSONLY
    | STOP
    | STOP_ON_ERROR
    | STOPAT
    | STOPATMARK
    | STOPBEFOREMARK
    | STOPPED
    | STRING_DELIMITER
    | SUBJECT
    | SUBSCRIPTION
    | SUPPORTED
    | SUSPEND_FOR_SNAPSHOT_BACKUP
    | SYNCHRONOUS_COMMIT
    | SYSTEM
    | TAPE
    | TCP
    | TEMPDB_METADATA
    | TEXTSIZE
    | THROW
    | TIMEOUT
    | TIMER
    | TRACK_CAUSALITY
    | TRANSACTION_ID
    | TRIPLE_DES
    | TRIPLE_DES_3KEY
    | TRY
    | TSQL
    | TSQL_SCALAR_UDF_INLINING
    | UNCHECKED
    | UNDEFINED
    | UNLOAD
    | UOW
    | URL
    | USE_TYPE_DEFAULT
    | USED
    | VALID_XML
    | VALIDATION
    | VALUE
    | VERBOSE_TRUNCATION_WARNINGS
    | VERIFYONLY
    | VISIBILITY
    | WAITFOR
    | WELL_FORMED_XML
    | WHEN_SUPPORTED
    | WHILE
    | WINDOW
    | WITNESS
    | WORKLOAD
    | WSFC
    | XACT_ABORT
    | XML_COMPRESSION
    | XTP_PROCEDURE_EXECUTION_STATISTICS
    | XTP_QUERY_EXECUTION_STATISTICS
    | APPEND_ONLY
    | ATTACH
    | ATTACH_REBUILD_LOG
    | BACKUP_OPTIONS
    | CAP_CPU_PERCENT
    | CLASSIFICATION
    | COUNTER
    | CUBE
    | DATABASE_SNAPSHOT
    | DEPENDENTS
    | EDGE
    | FORMAT_TYPE
    | GROUPING
    | INSENSITIVE
    | LEDGER_VIEW
    | LISTENER_PORT
    | MAX_IOPS_PER_VOLUME
    | MIN_CPU_PERCENT
    | MIN_IOPS_PER_VOLUME
    | MIN_MEMORY_PERCENT
    | NEWNAME
    | NODE
    | OPERATION_TYPE_COLUMN_NAME
    | OPERATION_TYPE_DESC_COLUMN_NAME
    | PIVOT
    | REDISTRIBUTE
    | REDUCE
    | RESTORE_OPTIONS
    | ROLLUP
    | SCHEDULER
    | SENSITIVITY
    | SEQUENCE_NUMBER_COLUMN_NAME
    | TABLESAMPLE
    | TRACK_COLUMNS_UPDATED
    | TRANSACTION_ID_COLUMN_NAME
    | UNPIVOT
    | WRITE
    ;

databaseName
    : identifier
    ;

schemaName
    : identifier
    ;

functionName
    : (owner DOT_)* name
    ;

procedureName
    : (owner DOT_)* name // (SEMI_ numberLiterals)? // 用于对相同名称的过程进行分组的可选整数。在 SQL Server的未来版本中将删除此功能。
    ;

viewName
    : (owner DOT_)* name
    ;

triggerName
    : (owner DOT_)? name
    ;

sequenceName
    : (owner DOT_)* name
    ;

tableName
    : (owner DOT_)* name
    | owner DOUBLE_DOT_ name //owner..name 省略架构名，默认为dbo
    ;

queueName
    : (owner DOT_)* name
    ;

contractName
    : name
    ;

serviceName
    : name
    ;

columnName
    : (owner DOT_)* (name | scriptVariableName)
    ;

scriptVariableName
    : DOLLAR_ LP_ name RP_
    | DOLLAR_ (IDENTITY | ROWGUID)
    ;

owner
    : identifier
    ;

name
    : identifier
    ;

columnNames
    : LP_ columnName (COMMA_ columnName)* RP_
    ;

columnNamesWithSort
    : LP_ columnNameWithSort (COMMA_ columnNameWithSort)* RP_
    ;

indexName
    : identifier
    ;

constraintName
    : identifier
    ;

collationName
    : STRING_ | identifier
    ;

alias
    : identifier
    | stringLiterals
    ;

dataTypeLength
    : LP_ (number (COMMA_ number)?)? RP_
    ;

primaryKey
    : PRIMARY KEY
    ;

expr
    : booleanPrimary
    | expr andOperator expr
    | expr orOperator expr
    | expr distinctFrom expr
    | notOperator expr
    | LP_ expr RP_
    ;

andOperator
    : AND
    | AND_
    ;

orOperator
    : OR
    | OR_
    ;

distinctFrom
    : IS NOT? DISTINCT FROM
    ;

notOperator
    : NOT
    | NOT_
    ;

booleanPrimary
    : booleanPrimary IS NOT? (TRUE | FALSE | UNKNOWN | NULL)
    | booleanPrimary SAFE_EQ_ predicate
    | booleanPrimary comparisonOperator predicate
    | booleanPrimary comparisonOperator (ALL | ANY) subquery
    | predicate
    ;

comparisonOperator
    : EQ_
    | GTE_
    | GT_
    | LTE_
    | LT_
    | NEQ_
    ;

predicate
    : bitExpr NOT? IN subquery
    | bitExpr NOT? IN LP_ expr (COMMA_ expr)* RP_
    | bitExpr NOT? BETWEEN bitExpr AND predicate
    | bitExpr NOT? LIKE simpleExpr (ESCAPE simpleExpr)?
    | bitExpr
    ;

bitExpr
    : bitExpr VERTICAL_BAR_ bitExpr
    | bitExpr AMPERSAND_ bitExpr
    | bitExpr SIGNED_LEFT_SHIFT_ bitExpr
    | bitExpr SIGNED_RIGHT_SHIFT_ bitExpr
    | bitExpr PLUS_ bitExpr
    | bitExpr MINUS_ bitExpr
    | bitExpr ASTERISK_ bitExpr
    | bitExpr SLASH_ bitExpr
    | bitExpr MOD_ bitExpr
    | bitExpr CARET_ bitExpr
    | simpleExpr
    ;

simpleExpr
    : functionCall
    | parameterMarker
    | literals
    | columnName
    | variableName
    | simpleExpr OR_ simpleExpr
    | (PLUS_ | MINUS_ | TILDE_ | NOT_ | BINARY | DOLLAR_) simpleExpr
    | CURRENT OF GLOBAL? expr
    | ROW? LP_ expr (COMMA_ expr)* RP_
    | EXISTS? subquery
    | LBT_ expr RBT_
    | LBE_ expr (DOT_ expr)* (COMMA_ expr)* RBE_ (ON (COLUMNS | ROWS))?
    | caseExpression
    | privateExprOfDb
    | matchExpression
    | nextValueFor
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/functions/next-value-for-transact-sql
nextValueFor
    : NEXT VALUE FOR sequenceName overClause?
    ;

functionCall
    : (aggregationFunction
    | specialFunction
    | regularFunction) (DOT_ functionCall)* (DOT_ identifier)?
    ;

aggregationFunction
    : aggregationFunctionName LP_ distinct? (expr (COMMA_ expr)* | ASTERISK_)? RP_ overClause?
    ;

aggregationFunctionName
    : MAX
    | MIN
    | SUM
    | COUNT
    | AVG
    ;

distinct
    : DISTINCT
    ;

specialFunction
    : conversionFunction
    | charFunction
    | openJsonFunction
    | jsonFunction
    | openRowSetFunction
    | windowFunction
    | approxFunction
    | openDatasourceFunction
    | rankingFunctions
    | graphFunction
    | trimFunction
    ;

trimFunction
    : TRIM LP_ ((LEADING | BOTH | TRAILING) expr? FROM)? expr RP_
    | TRIM LP_ (expr FROM)? expr RP_
    ;

graphFunction
    : graphAggFunction
    ;

graphAggFunction
    : graphAggFunctionName LP_ expr (COMMA_ expr)? RP_ WITHIN GROUP LP_ GRAPH PATH RP_
    ;

graphAggFunctionName
    : STRING_AGG
    | LAST_VALUE
    | aggregationFunctionName
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/functions/ranking-functions-transact-sql
rankingFunctions
    : (ROW_NUMBER | DENSE_RANK | RANK ) LP_ RP_ overClause
    | NTILE LP_ expr RP_ overClause
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/functions/opendatasource-transact-sql
openDatasourceFunction
    : (OPENDATASOURCE LP_ expr COMMA_ expr RP_) (DOT_ tableName)?
    ;

approxFunction
    : funcName = (APPROX_PERCENTILE_CONT | APPROX_PERCENTILE_DISC) LP_ expr RP_ WITHIN GROUP LP_ ORDER BY expr (
        ASC
        | DESC
    )? RP_
    ;

conversionFunction
    : castFunction
    | convertFunction
    ;

castFunction
    : (CAST | TRY_CAST) LP_ expr AS dataType RP_
    ;

convertFunction
    : (CONVERT | TRY_CONVERT) LP_ dataType COMMA_ expr (COMMA_ number)? RP_
    ;

jsonFunction
    : jsonObjectFunction
    | jsonArrayFunction
    ;

jsonObjectFunction
    : JSON_OBJECT LP_ (jsonKeyValue (COMMA_ jsonKeyValue)* jsonNullClause?)? RP_
    ;

jsonArrayFunction
    : JSON_ARRAY LP_ expr (COMMA_ expr)* jsonNullClause? RP_
    ;

jsonKeyValue
    : expr COLON_ expr
    ;

jsonNullClause
    : NULL ON NULL
    | ABSENT ON NULL
    ;

charFunction
    : CHAR LP_ expr (COMMA_ expr)* (USING ignoredIdentifier)? RP_
    ;

openJsonFunction
    : OPENJSON LP_ expr (COMMA_ expr)? RP_ openJsonWithclause?
    ;

openJsonWithclause
    : WITH LP_ jsonColumnDefinition (COMMA_ jsonColumnDefinition)* RP_
    ;

jsonColumnDefinition
    : columnName dataType expr? (AS JSON)?
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/functions/openrowset-transact-sql
openRowSetFunction
    : OPENROWSET LP_ expr COMMA_ ((expr SEMI_ expr SEMI_ expr) | expr) COMMA_ (tableName | expr) RP_
    | OPENROWSET LP_ BULK expr (COMMA_ expr)* RP_
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/functions/openquery-transact-sql
openQueryFunction
    : OPENQUERY LP_ expr COMMA_ expr RP_
    ;

rowSetFunction
    : openRowSetFunction
    | openQueryFunction
    | openDatasourceFunction
    ;

regularFunction
    : regularFunctionName LP_ (expr (COMMA_ expr)* | ASTERISK_)? RP_
    ;

regularFunctionName
    : (owner DOT_)* identifier
    | IF
    | LOCALTIME
    | LOCALTIMESTAMP
    | INTERVAL
    ;

caseExpression
    : CASE simpleExpr? caseWhen+ caseElse? END
    ;

caseWhen
    : WHEN expr THEN expr
    ;

caseElse
    : ELSE expr
    ;

privateExprOfDb
    : windowFunction
    | atTimeZoneExpr
    | castExpr
    | convertExpr
    ;

orderByClause
    : ORDER BY orderByItem (COMMA_ orderByItem)* (
        OFFSET expr (ROW | ROWS) (FETCH (FIRST | NEXT) expr (ROW | ROWS) ONLY)?
    )?
    ;

orderByItem
    : (columnName | numberLiterals | expr) (COLLATE identifier)? (ASC | DESC)?
    ;

dataType
    : (ignoredIdentifier DOT_)? dataTypeName (
        dataTypeLength
        | LP_ MAX RP_
        | LP_ (CONTENT | DOCUMENT)? columnName RP_
    )?
    ;

dataTypeName
    : BIGINT
    | NUMERIC
    | BIT
    | SMALLINT
    | DECIMAL
    | SMALLMONEY
    | INT
    | TINYINT
    | MONEY
    | FLOAT
    | REAL
    | DATE
    | DATETIMEOFFSET
    | SMALLDATETIME
    | DATETIME
    | DATETIME2
    | TIME
    | CHAR
    | VARCHAR
    | TEXT
    | NCHAR
    | NVARCHAR
    | NTEXT
    | BINARY
    | VARBINARY
    | IMAGE
    | SQL_VARIANT
    | XML
    | UNIQUEIDENTIFIER
    | HIERARCHYID
    | GEOMETRY
    | GEOGRAPHY
    | CURSOR
    | identifier
    | INTEGER
    ;

atTimeZoneExpr
    : IDENTIFIER_ (WITH TIME ZONE)? STRING_
    ;

castExpr
    : CAST LP_ expr AS dataType (LP_ number RP_)? RP_
    ;

convertExpr
    : CONVERT (dataType (LP_ number RP_)? COMMA_ expr (COMMA_ number)?)
    ;

windowFunction
    : funcName = (FIRST_VALUE | LAST_VALUE) LP_ expr RP_ nullTreatment? overClause
    ;

nullTreatment
    : (RESPECT | IGNORE) NULLS
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/queries/select-over-clause-transact-sql
overClause
    : OVER LP_ windowName? partitionByClause? orderByClause? rowRangeClause? RP_
    | OVER windowName
    ;

partitionByClause
    : PARTITION BY expr (COMMA_ expr)*
    ;

rowRangeClause
    : (ROWS | RANGE) windowFrameExtent
    ;

windowFrameExtent
    : windowFramePreceding
    | windowFrameBetween
    ;

windowFrameBetween
    : BETWEEN windowFrameBound AND windowFrameBound
    ;

windowFrameBound
    : windowFramePreceding
    | windowFrameFollowing
    ;

windowFramePreceding
    : UNBOUNDED PRECEDING
    | number PRECEDING
    | CURRENT ROW
    ;

windowFrameFollowing
    : UNBOUNDED FOLLOWING
    | number FOLLOWING
    | CURRENT ROW
    ;

columnNameWithSort
    : columnName (ASC | DESC)?
    ;

indexOption
    : FILLFACTOR EQ_ number
    | ( PAD_INDEX
      | SORT_IN_TEMPDB
      | IGNORE_DUP_KEY
      | STATISTICS_NORECOMPUTE
      | STATISTICS_INCREMENTAL
      | DROP_EXISTING
      | RESUMABLE
      | ALLOW_ROW_LOCKS
      | ALLOW_PAGE_LOCKS
      | COMPRESSION_DELAY
      | SORT_IN_TEMPDB
      | OPTIMIZE_FOR_SEQUENTIAL_KEY
      ) EQ_ onOffOption
    | (COMPRESSION_DELAY | MAX_DURATION) EQ_ number (MINUTES)?
    | MAXDOP EQ_ number
    | ONLINE EQ_ (ON (LP_ lowPriorityLockWait RP_)? | OFF)
    | DATA_COMPRESSION EQ_ (NONE | ROW | PAGE | COLUMNSTORE | COLUMNSTORE_ARCHIVE) onPartitionClause?
    | XML_COMPRESSION EQ_ onOffOption onPartitionClause?
    ;

onPartitionClause
    : ON PARTITIONS LP_ partitionExpressions RP_
    ;

partitionExpressions
    : partitionExpression (COMMA_ partitionExpression)*
    ;

partitionExpression
    : number
    | numberRange
    ;

numberRange
    : number TO number
    ;

lowPriorityLockWait
    : WAIT_AT_LOW_PRIORITY LP_ MAX_DURATION EQ_ number (MINUTES)? COMMA_ ABORT_AFTER_WAIT EQ_ (
        NONE
        | SELF
        | BLOCKERS
    ) RP_
    ;

ignoredIdentifier
    : IDENTIFIER_
    ;

ignoredIdentifiers
    : ignoredIdentifier (COMMA_ ignoredIdentifier)*
    ;

variableName
    : identifier
    ;

executeAsClause
    : (EXEC | EXECUTE) AS (CALLER | SELF | OWNER | stringLiterals)
    ;

transactionName
    : identifier
    ;

savepointName
    : identifier
    ;

ifExists
    : IF EXISTS
    ;

// https://learn.microsoft.com/zh-cn/sql/t-sql/queries/hints-transact-sql-table
tableHintLimited
    : KEEPIDENTITY
    | KEEPDEFAULTS
    | HOLDLOCK
    | IGNORE_CONSTRAINTS
    | IGNORE_TRIGGERS
    | NOLOCK
    | NOWAIT
    | PAGLOCK
    | READCOMMITTED
    | READCOMMITTEDLOCK
    | READPAST
    | REPEATABLEREAD
    | ROWLOCK
    | SERIALIZABLE
    | SNAPSHOT
    | TABLOCK
    | TABLOCKX
    | UPDLOCK
    | XLOCK
    | FORCESCAN
    | READUNCOMMITTED
    | NOEXPAND
    | SPATIAL_WINDOW_MAX_CELLS EQ_ numberLiterals
    | INDEX (
        '('  indexValue (',' indexValue)* ')'
        | '=' '(' indexValue ')'
        | '=' indexValue // examples in the doc include this syntax
    )
    | FORCESEEK ( '(' indexValue columnNames ')')?
    ;

indexValue
    : literals | identifier
    ;

matchExpression
    : MATCH LP_ (arbitratyLengthMatch | simpleMatch) RP_
    ;

simpleMatch
    : simpleMatchClause* (AND simpleMatch)*
    ;

simpleMatchClause
    : lastNode
    | nodeAlias
    | inEdgePath
    | outEdgePath
    ;

lastNode
    : LAST_NODE LP_ nodeAlias RP_
    ;

arbitratyLengthMatch
    : SHORTEST_PATH LP_ arbitraryLength RP_ (AND arbitraryLength)*
    ;

arbitraryLength
    : arbitraryLengthClause (AND? arbitraryLengthClause)*
    ;

arbitraryLengthClause
    : (lastNode | tableName) LP_? edgeNodeAl+ RP_? alPatternQuantifier?
    | LP_ edgeNodeAl+ RP_ alPatternQuantifier (lastNode | tableName)
    ;

edgeNodeAl
    : nodeAlias edgeAliasPath
    | edgeAliasPath nodeAlias
    ;

edgeAliasPath
    : (LT_ MINUS_ LP_ edgeAlias RP_ MINUS_)
    | (MINUS_ LP_ edgeAlias RP_ MINUS_ GT_)
    ;

outEdgePath
    : MINUS_ LP_ edgeAlias RP_ MINUS_ GT_
    ;

inEdgePath
    : LT_ MINUS_ LP_ edgeAlias RP_ MINUS_
    ;

alPatternQuantifier
    : PLUS_
    | LBE_ number COMMA_ number RBE_
    ;

nodeAlias
    : tableName
    ;

edgeAlias
    : tableName
    ;
objectName
    : (owner DOT_)* name
    ;

windowName
    : name
    ;
