DENY VIEW DEFINITION ON AVAILABILITY GROUP::MyAg TO <PERSON><PERSON><PERSON><PERSON>;

DENY TAKE OWNERSHIP ON AVAILABILITY GROUP::MyAg TO <PERSON><PERSON><PERSON><PERSON>ski
    CASCADE;

DENY REFERENCES TO AuditMonitor;

<PERSON>N<PERSON> CREATE CERTIFICATE TO MelanieK;

DEN<PERSON> VIEW DEFINITION TO CarmineEs CASCADE;

DENY CONTROL ON USER::Wanida TO RolandX;

DENY VIEW DEFINITION ON ROLE::SammamishParking
    TO JinghaoLiu CASCADE;

DENY IMPERSONATE ON USER::HamithaL TO AccountsPayable17;

<PERSON>N<PERSON> TAKE OWNERSHIP ON ENDPOINT::Shipping83 TO PKomosinski
    CASCADE;

DENY VIEW DEFINITION ON ENDPOINT::Mirror7 TO ZArifin;

DENY SELECT ON OBJECT::Person.Address TO RosaQdM;
DENY EXECUTE ON OBJECT::HumanResources.uspUpdateEmployeeHireInfo
    TO Recruiting11;

DENY REFERENCES (BusinessEntityID) ON OBJECT::HumanResources.vEmployee
    TO Wanida CASCADE;

DENY CONNECT SQL TO Annika CASCADE;

DENY CREATE ENDPOINT TO ArifS AS MandarP;

DENY IMPERSONATE ON LOGIN::WanidaBenshoof TO [AdvWorks\YoonM];

DENY VIEW DEFINITION ON LOGIN::EricKurjan TO RMeyyappan
    CASCADE;

DENY VIEW DEFINITION ON SERVER ROLE::Sales TO Auditors ;
DENY ALTER ON SYMMETRIC KEY::SamInventory42 TO HamidS;

DENY EXECUTE ON sys.xp_cmdshell TO public;

DENY VIEW DEFINITION ON TYPE::Telemarketing.PhoneNumber
    TO KhalidR CASCADE;

DENY EXECUTE ON XML SCHEMA COLLECTION::Sales.Invoices4 TO Wanida;









