SET @myvar2 = NEXT VALUE FOR Test.CountBy1 ;

SET @myVar = 'This is a test';


SET @state = N'Oregon';

SET @NewBalance *= 10;
SET @NewBalance = 10;
SET @NewBalance = @NewBalance * 10;
SET @my_variable = my_cursor ;

SET @CursorVar = CURSOR SCROLL DYNAMIC
FOR
SELECT LastName, FirstName
FROM AdventureWorks2022.HumanResources.vEmployee
WHERE LastName like 'B%';

SET @rows = (SELECT COUNT(*) FROM Sales.Customer);

SET @p.X = @p.X + 1.1;

SET @p=point.SetXY(23.5, 23.5);

SET @p.SetXY(22, 23);
SET @myVar = 'This is a test';



SET @rows = (SELECT COUNT(*) FROM dbo.DimCustomer);




































