
REVOKE SELECT ON SCHEMA :: Sales TO Vendors;

<PERSON><PERSON><PERSON><PERSON> GRANT OPTION FOR CONTROL ON AVAILABILITY GROUP::MyAg TO <PERSON><PERSON><PERSON><PERSON>ski
CASCADE

REVOKE REFERENCES FROM AuditMonitor;


<PERSON><PERSON><PERSON><PERSON> VIEW DEFINITION FROM CarmineEs CASCADE;

<PERSON><PERSON><PERSON><PERSON> CONTROL ON USER::Wanida FROM RolandX;

REVOKE VIEW DEFINITION ON ROLE::SammamishParking
    FROM JinghaoLiu CASCADE;

REVOKE IMPERSONATE ON USER::HamithaL FROM AccountsPayable17;

REVOKE VIEW DEFINITION ON ENDPOINT::Mirror7 FROM ZArifin;

REVOKE TAKE OWNERSHIP ON ENDPOINT::Shipping83 FROM PKomosinski
    CASCADE;

REVOKE SELECT ON OBJECT::Person.Address FROM RosaQdM;

REVOKE EXECUTE ON OBJECT::HumanResources.uspUpdateEmployeeHireInfo
    FROM Recruiting11;

<PERSON><PERSON><PERSON><PERSON> REFERENCES (BusinessEntityID) ON OBJECT::HumanResources.vEmployee
    FROM Wanida CASCADE;

R<PERSON><PERSON><PERSON> VIEW SERVER STATE FROM WanidaBenshoof;

REVOKE GRANT OPTION FOR CONNECT SQL FROM JanethEsteves;

REVOKE VIEW DEFINITION ON SERVER ROLE::Sales TO Auditors ;

REVOKE VIEW DEFINITION ON LOGIN::EricKurjan FROM RMeyyappan
    CASCADE;

REVOKE ALTER ON SYMMETRIC KEY::SamInventory42 TO HamidS CASCADE;

REVOKE EXECUTE ON sys.sp_addlinkedserver FROM public;

REVOKE VIEW DEFINITION ON TYPE::Telemarketing.PhoneNumber
    FROM KhalidR CASCADE;


REVOKE EXECUTE ON XML SCHEMA COLLECTION::Sales.Invoices4 FROM Wanida;

























