
GRANT CONNECT ON ENDPOINT::[TSQL Default TCP] TO public; -- Keep existing public permission on default endpoint for demo purpose
GRANT CONNECT ON ENDPOINT::ipv4_endpoint_special
TO login_name;

GRANT EXECUTE EXTERNAL SCRIPT ON EXTERNAL LANGUAGE ::language_name
TO database_principal_name;

GRANT EXECUTE EXTERNAL SCRIPT ON EXTERNAL LANGUAGE ::Java
TO mylogin;

GRANT REFERENCES ON EXTERNAL LANGUAGE::Java to user;
GRANT CREATE EXTERNAL LIBRARY to user123;


GRANT EXECUTE ON TestProc TO TesterRole WITH GRANT OPTION;

GRANT EXECUTE ON TestProc TO User2;
GRANT EXECUTE ON TestProc TO User2 AS TesterRole;


GRANT VIEW DEFINITION ON AVAILABILITY GROUP::MyAg TO <PERSON>Arifin;

GRANT TAKE OWNERSHIP ON AVAILABILITY GROUP::MyAg TO <PERSON><PERSON><PERSON>
    WITH GRANT OPTION;

GRANT CONTROL ON AVAILABILITY GROUP::MyAg TO <PERSON>;

GRA<PERSON> CREATE TABLE TO MelanieK;

GRANT SHOWPLAN TO AuditMonitor;


GRANT CREATE VIEW TO CarmineEs WITH GRANT OPTION;


GRANT CONTROL ON DATABASE::AdventureWorks2022 TO Sarah;
GRANT CONTROL ON USER::Wanida TO RolandX;

GRANT VIEW DEFINITION ON ROLE::SammamishParking
    TO JinghaoLiu WITH GRANT OPTION;

GRANT IMPERSONATE ON USER::HamithaL TO AccountsPayable17;

GRANT VIEW DEFINITION ON ENDPOINT::Mirror7 TO ZArifin;


GRANT TAKE OWNERSHIP ON ENDPOINT::Shipping83 TO PKomosinski
    WITH GRANT OPTION;
GRANT CONTROL
ON FULLTEXT CATALOG :: ProductCatalog
    TO Ted ;
GRANT VIEW DEFINITION
    ON FULLTEXT STOPLIST :: ProductStoplist
    TO Mary ;
GRANT SELECT ON OBJECT::Person.Address TO RosaQdM;

GRANT EXECUTE ON OBJECT::HumanResources.uspUpdateEmployeeHireInfo
    TO Recruiting11;

GRANT REFERENCES (BusinessEntityID) ON OBJECT::HumanResources.vEmployee
    TO Wanida WITH GRANT OPTION;

GRANT SELECT ON Person.Address TO RosaQdM;

GRANT SELECT ON Person.Address TO [AdventureWorks2022\RosaQdM];

GRANT EXECUTE ON dbo.uspGetBillOfMaterials TO newrole ;
GRANT UNMASK ON OBJECT::Data.Membership (email) to OutreachCoordinator;

GRANT SELECT ON SCHEMA :: Person TO WilJo WITH GRANT OPTION;

GRANT INSERT ON SCHEMA :: HumanResources TO guest;

GRANT VIEW DEFINITION
    ON SEARCH PROPERTY LIST :: DocumentTablePropertyList
    TO Mary ;


GRANT CONTROL SERVER TO TerryEminhizer;


GRANT ALTER ANY EVENT NOTIFICATION TO JanethEsteves WITH GRANT OPTION;

GRANT ALTER ANY DATABASE TO ITDevelopers ;

GRANT IMPERSONATE ON LOGIN::WanidaBenshoof to [AdvWorks\YoonM];

GRANT VIEW DEFINITION ON LOGIN::EricKurjan TO RMeyyappan
    WITH GRANT OPTION;

GRANT VIEW DEFINITION ON SERVER ROLE::Sales TO Auditors ;

GRANT ALTER ON SYMMETRIC KEY::SamInventory42 TO HamidS;

GRANT SELECT ON sys.sql_logins TO Sylvester1;
GRANT VIEW SERVER STATE to Sylvester1;


GRANT EXECUTE ON xp_readmail TO Sylvester1;
GRANT VIEW DEFINITION ON TYPE::Telemarketing.PhoneNumber
    TO KhalidR WITH GRANT OPTION;

GRANT EXECUTE ON XML SCHEMA COLLECTION::Sales.Invoices4 TO Wanida;











