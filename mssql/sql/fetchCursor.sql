
FETCH NEXT FROM contact_cursor;

<PERSON><PERSON><PERSON><PERSON> @@FETCH_STATUS = 0
BEGIN
FETCH NEXT FROM contact_cursor;
END
CLOSE contact_cursor;
DEALLOCATE contact_cursor;
GO



FETCH NEXT FROM contact_cursor
INTO @LastName, @FirstName;


WH<PERSON><PERSON> @@FETCH_STATUS = 0
BEGIN
   PRINT 'Contact Name: ' + @FirstName + ' ' +  @LastName
   FETCH NEXT FROM contact_cursor
   INTO @LastName, @FirstName;
END


FETCH LAST FROM contact_cursor;

-- Fetch the row immediately prior to the current row in the cursor.
FETCH PRIOR FROM contact_cursor;

-- Fetch the second row in the cursor.
FETCH ABSOLUTE 2 FROM contact_cursor;

-- Fetch the row that is three rows after the current row.
FETCH RELATIVE 3 FROM contact_cursor;

-- Fetch the row that is two rows prior to the current row.
FETCH RELATIVE -2 FROM contact_cursor;









































