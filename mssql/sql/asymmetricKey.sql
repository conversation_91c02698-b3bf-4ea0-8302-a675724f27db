ALTER ASYMMETRIC KEY PacificSales09
    WITH PRIVATE KEY (
    DECRYPTION BY PASSWORD = '<oldPassword>',
    ENCRYPTION BY PASSWORD = '<enterStrongPasswordHere>');

ALTER ASYMMETRIC KEY PacificSales19 REMOVE PRIVATE KEY;

ALTER ASYMMETRIC KEY PacificSales09 WITH PRIVATE KEY (
    DECRYPTION BY PASSWORD = '<enterStrongPasswordHere>' );

















CREATE ASYMMETRIC KEY PacificSales09
    WITH ALGORITHM = RSA_2048
    ENCRYPTION BY PASSWORD = '<enterStrongPasswordHere>';
GO


CREATE ASYMMETRIC KEY PacificSales19
    AUTHORIZATION Christina
    FROM FILE = 'c:\PacSales\Managers\ChristinaCerts.tmp';
GO

CREATE ASYMMETRIC KEY EKM_askey1
    FROM PROVIDER EKM_Provider1
    WITH
        ALGORITHM = RSA_2048,
        CREATION_DISPOSITION = CREATE_NEW
        , PROVIDER_KEY_NAME  = 'key10_user1' ;
GO












































