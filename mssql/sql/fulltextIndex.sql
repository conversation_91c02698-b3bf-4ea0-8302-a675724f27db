ALTER FULLTEXT INDEX ON table_1 START FULL POPULATION;

ALTER FULLTEXT INDEX ON table_1 SET SEARCH PROPERTY LIST spl_2;

ALTER FULLTEXT INDEX ON table_1
    SET SEARCH PROPERTY LIST OFF WITH NO POPULATION;

ALTER FULLTEXT INDEX ON table_1 SET SEARCH PROPERTY LIST spl_1;


ALTER FULLTEXT INDEX ON HumanResources.JobCandidate
   SET CHANGE_TRACKING MANUAL;

ALTER FULLTEXT INDEX ON Production.Document
   SET SEARCH PROPERTY LIST DocumentPropertyList;

ALTER FULLTEXT INDEX ON Production.Document
   SET SEARCH PROPERTY LIST OFF WITH NO POPULATION;

ALTER FULLTEXT INDEX ON HumanResources.JobCandidate
   START FULL POPULATION;

ALTER FULLTEXT INDEX ON table_name START FULL POPULATION;


CREATE FULLTEXT INDEX ON Production.ProductReview (
    ReviewerName LANGUAGE 1033,
    <PERSON><PERSON><PERSON>ddress LANGUAGE 1033,
    Comments LANGUAGE 1033
) KEY INDEX PK_ProductReview_ProductReviewID ON production_catalog;

CREATE FULLTEXT INDEX ON Production.Document (
    Title LANGUAGE 1033,
    DocumentSummary LANGUAGE 1033,
    Document TYPE COLUMN FileExtension LANGUAGE 1033
) KEY INDEX PK_Document_DocumentID
WITH STOPLIST = SYSTEM,
    SEARCH PROPERTY LIST = DocumentPropertyList,
    CHANGE_TRACKING OFF,
    NO POPULATION;

ALTER FULLTEXT INDEX ON Production.Document SET CHANGE_TRACKING AUTO;


DROP FULLTEXT INDEX ON HumanResources.JobCandidate;































































































































