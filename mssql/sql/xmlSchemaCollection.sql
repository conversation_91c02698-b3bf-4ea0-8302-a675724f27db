CREATE XML SCHEMA COLLECTION ExpenseReportSchema AS
N'<?xml version="1.0" encoding="UTF-16" ?>
  <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
     targetNamespace="https://Adventure-Works.com/schemas/expenseReport"
     xmlns:expense="https://Adventure-Works.com/schemas/expenseReport"
     elementFormDefault="qualified"
   >
    <xsd:complexType name="expenseReportType">
       <xsd:sequence>
         <xsd:element name="EmployeeName" type="xsd:string"/>
         <xsd:element name="EmployeeID" type="xsd:string"/>
         <xsd:element name="ItemDetail"
           type="expense:ItemDetailType" maxOccurs="unbounded"/>
      </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ItemDetailType">
      <xsd:sequence>
        <xsd:element name="Date" type="xsd:date"/>
        <xsd:element name="CostCenter" type="xsd:string"/>
        <xsd:element name="Total" type="xsd:decimal"/>
        <xsd:element name="Currency" type="xsd:string"/>
        <xsd:element name="Description" type="xsd:string"/>
      </xsd:sequence>
    </xsd:complexType>

    <xsd:element name="ExpenseReport" type="expense:expenseReportType"/>

  </xsd:schema>' ;


DROP XML SCHEMA COLLECTION ManuInstructionsSchemaCollection;

CREATE XML SCHEMA COLLECTION MyCollection AS N'
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
<!-- Contents of schema here -->
</xsd:schema>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
<!-- Contents of schema here -->
</xsd:schema>';

CREATE XML SCHEMA COLLECTION mySC AS '
<schema xmlns="http://www.w3.org/2001/XMLSchema">
      <element name="root" type="string"/>
</schema>
';


DROP XML SCHEMA COLLECTION ManuInstructionsSchemaCollection;
















































































































































