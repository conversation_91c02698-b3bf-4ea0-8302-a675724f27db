CREATE FUNCTION dbo.ISOweek (@DATE DATETIME)
    RETURNS INT
WITH EXECUTE AS CALLER
    AS
BEGIN
    DECLARE @ISOweek INT;

    SET @ISOweek = DATEPART(wk, @DATE) + 1 -
        DATEPART(wk, CAST(DATEPART(yy, @DATE) AS CHAR(4)) + '0104');

    --Special cases: Jan 1-3 may belong to the previous year
    IF (@ISOweek = 0)
        SET @ISOweek = dbo.ISOweek(CAST(DATEPART(yy, @DATE) - 1 AS CHAR(4))
           + '12' + CAST(24 + DATEPART(DAY, @DATE) AS CHAR(2))) + 1;

    --Special case: Dec 29-31 may belong to the next year
    IF ((DATEPART(mm, @DATE) = 12)
        AND ((DATEPART(dd, @DATE) - DATEPART(dw, @DATE)) >= 28))
    SET @ISOweek = 1;

RETURN (@ISOweek);
END;
GO


CREATE FUNCTION Sales.ufn_SalesByStore (@storeid INT)
    RETURNS TABLE
    AS
RETURN (
    SELECT P.ProductID, P.Name, SUM(SD.LineTotal) AS 'Total'
    FROM Production.Product AS P
    INNER JOIN Sales.SalesOrderDetail AS SD ON SD.ProductID = P.ProductID
    INNER JOIN Sales.SalesOrderHeader AS SH ON SH.SalesOrderID = SD.SalesOrderID
    INNER JOIN Sales.Customer AS C ON SH.CustomerID = C.CustomerID
    WHERE C.StoreID = @storeid
    GROUP BY P.ProductID, P.Name
);
GO

CREATE FUNCTION dbo.ufn_FindReports (@InEmpID INT)
    RETURNS @retFindReports TABLE (
    EmployeeID INT PRIMARY KEY NOT NULL,
    FirstName NVARCHAR(255) NOT NULL,
    LastName NVARCHAR(255) NOT NULL,
    JobTitle NVARCHAR(50) NOT NULL,
    RecursionLevel INT NOT NULL
    )
    --Returns a result set that lists all the employees who report to the
    --specific employee directly or indirectly.
AS
BEGIN
WITH EMP_cte (
              EmployeeID,
              OrganizationNode,
              FirstName,
              LastName,
              JobTitle,
              RecursionLevel
    ) -- CTE name and columns
         AS (
        -- Get the initial list of Employees for Manager n
        SELECT e.BusinessEntityID,
               OrganizationNode = ISNULL(e.OrganizationNode, CAST('/' AS HIERARCHYID)),
               p.FirstName,
               p.LastName,
               e.JobTitle,
               0
        FROM HumanResources.Employee e
                 INNER JOIN Person.Person p
                            ON p.BusinessEntityID = e.BusinessEntityID
        WHERE e.BusinessEntityID = @InEmpID

        UNION ALL

        -- Join recursive member to anchor
        SELECT e.BusinessEntityID,
               e.OrganizationNode,
               p.FirstName,
               p.LastName,
               e.JobTitle,
               RecursionLevel + 1
        FROM HumanResources.Employee e
                 INNER JOIN EMP_cte
                            ON e.OrganizationNode.GetAncestor(1) = EMP_cte.OrganizationNode
                 INNER JOIN Person.Person p
                            ON p.BusinessEntityID = e.BusinessEntityID
    )
-- Copy the required columns to the result of the function
INSERT @retFindReports
SELECT EmployeeID,
       FirstName,
       LastName,
       JobTitle,
       RecursionLevel
FROM EMP_cte

         RETURN
END;
GO

CREATE FUNCTION [dbo].[len_s] (@str NVARCHAR(4000))
RETURNS BIGINT
AS
EXTERNAL NAME [SurrogateStringFunction].[Microsoft.Samples.SqlServer.SurrogateStringFunction].[LenS];



DROP FUNCTION Sales.fn_SalesByStore;














































































































































