UPDATE Cities
SET Location = CONVERT(Point, '12.3:46.2')
WHERE Name = 'Anchorage';

UPDATE Cities
SET Location.X = 23.5
WHERE Name = 'Anchorage';

WITH cte AS (SELECT * FROM @x)
UPDATE cte   -- cte isn't referenced by the alias.
SET Value = y.Value
    FROM cte AS x  -- cte is assigned an alias.
INNER JOIN @y AS y ON y.ID = x.ID;


UPDATE Person.Address
SET ModifiedDate = GETDATE();

UPDATE Sales.SalesPerson
SET Bonus = 6000, CommissionPct = .10, SalesQuota = NULL;


UPDATE Production.Product
SET Color = N'Metallic Red'
WHERE Name LIKE N'Road-250%' AND Color = N'Red';


UPDATE TOP (10) HumanResources.Employee
SET VacationHours = VacationHours * 1.25 ;

UPDATE HumanResources.Employee
SET VacationHours = VacationHours + 8
    FROM (SELECT TOP 10 BusinessEntityID FROM HumanResources.Employee
     ORDER BY HireDate ASC) AS th
WHERE HumanResources.Employee.BusinessEntityID = th.BusinessEntityID;
GO

WITH Parts(AssemblyID, ComponentID, PerAssemblyQty, EndDate, ComponentLevel) AS
(
    SELECT b.ProductAssemblyID, b.ComponentID, b.PerAssemblyQty,
        b.EndDate, 0 AS ComponentLevel
    FROM Production.BillOfMaterials AS b
    WHERE b.ProductAssemblyID = 800
          AND b.EndDate IS NULL
    UNION ALL
    SELECT bom.ProductAssemblyID, bom.ComponentID, p.PerAssemblyQty,
        bom.EndDate, ComponentLevel + 1
    FROM Production.BillOfMaterials AS bom
        INNER JOIN Parts AS p
        ON bom.ProductAssemblyID = p.ComponentID
        AND bom.EndDate IS NULL
)
UPDATE Production.BillOfMaterials
SET PerAssemblyQty = c.PerAssemblyQty * 2
    FROM Production.BillOfMaterials AS c
JOIN Parts AS d ON c.ProductAssemblyID = d.AssemblyID
WHERE d.ComponentLevel = 0;

UPDATE HumanResources.EmployeePayHistory
SET PayFrequency = 2
WHERE CURRENT OF complex_cursor;


UPDATE Production.Product
SET ListPrice = ListPrice * 2;


UPDATE Production.Product
SET ListPrice += @NewPrice
WHERE Color = N'Red';

UPDATE Production.ScrapReason
SET Name += ' - tool malfunction'
WHERE ScrapReasonID BETWEEN 10 and 12;


UPDATE Sales.SalesPerson
SET SalesYTD = SalesYTD +
               (SELECT SUM(so.SubTotal)
                FROM Sales.SalesOrderHeader AS so
                WHERE so.OrderDate = (SELECT MAX(OrderDate)
                                      FROM Sales.SalesOrderHeader AS so2
                                      WHERE so2.SalesPersonID = so.SalesPersonID)
                  AND Sales.SalesPerson.BusinessEntityID = so.SalesPersonID
                GROUP BY so.SalesPersonID);


UPDATE Production.Location
SET CostRate = DEFAULT
WHERE CostRate > 20.00;

UPDATE Person.vStateProvinceCountryRegion
SET CountryRegionName = 'United States of America'
WHERE CountryRegionName = 'United States';


UPDATE sr
SET sr.Name += ' - tool malfunction'
FROM Production.ScrapReason AS sr
    JOIN Production.WorkOrder AS wo
ON sr.ScrapReasonID = wo.ScrapReasonID
    AND wo.ScrappedQty > 300;

UPDATE @MyTableVar
SET NewVacationHours = e.VacationHours + 20,
    ModifiedDate = GETDATE()
    FROM HumanResources.Employee AS e
WHERE e.BusinessEntityID = EmpID;


UPDATE Sales.SalesPerson
SET SalesYTD = SalesYTD + SubTotal
    FROM Sales.SalesPerson AS sp
JOIN Sales.SalesOrderHeader AS so
ON sp.BusinessEntityID = so.SalesPersonID
    AND so.OrderDate = (SELECT MAX(OrderDate)
    FROM Sales.SalesOrderHeader
    WHERE SalesPersonID = sp.BusinessEntityID);


UPDATE Sales.SalesPerson
SET SalesYTD = SalesYTD +
               (SELECT SUM(so.SubTotal)
                FROM Sales.SalesOrderHeader AS so
                WHERE so.OrderDate = (SELECT MAX(OrderDate)
                                      FROM Sales.SalesOrderHeader AS so2
                                      WHERE so2.SalesPersonID = so.SalesPersonID)
                  AND Sales.SalesPerson.BusinessEntityID = so.SalesPersonID
                GROUP BY so.SalesPersonID);


UPDATE MyLinkedServer.AdventureWorks2022.HumanResources.Department
SET GroupName = N'Public Relations'
WHERE DepartmentID = 4;

UPDATE OPENQUERY (MyLinkedServer, 'SELECT GroupName FROM HumanResources.Department WHERE DepartmentID = 4')
SET GroupName = 'Sales and Marketing';

UPDATE OPENDATASOURCE('SQLNCLI', 'Data Source=<server name>;Integrated Security=SSPI').AdventureWorks2022.HumanResources.Department
SET GroupName = 'Sales and Marketing' WHERE DepartmentID = 4;

UPDATE Production.Document
SET DocumentSummary .WRITE (N'features',28,10)
    OUTPUT deleted.DocumentSummary,
    inserted.DocumentSummary
INTO @MyTableVar
WHERE Title = N'Front Reflector Bracket Installation';

UPDATE Production.Document
SET DocumentSummary = N'Replacing NULL value'
WHERE Title = N'Crank Arm and Tire Maintenance';

UPDATE Production.Document
SET DocumentSummary .WRITE(N'Carefully inspect and maintain the tires and crank arms.',0,NULL)
WHERE Title = N'Crank Arm and Tire Maintenance';


UPDATE Production.Document
SET DocumentSummary .WRITE (NULL, 56, 0)
WHERE Title = N'Crank Arm and Tire Maintenance';

UPDATE Production.ProductPhoto
SET ThumbNailPhoto = (
    SELECT *
    FROM OPENROWSET(BULK 'c:Tires.jpg', SINGLE_BLOB) AS x )
WHERE ProductPhotoID = 1;

UPDATE Archive.dbo.Records
SET [Chart] = CAST('Xray 1' as VARBINARY(max))
WHERE [SerialNumber] = 2;

UPDATE dbo.Cities
SET Location = CONVERT(Point, '12.3:46.2')
WHERE Name = 'Anchorage';

UPDATE dbo.Cities
SET Location.SetXY(23.5, 23.5)
WHERE Name = 'Anchorage';

UPDATE dbo.Cities
SET Location.X = 23.5
WHERE Name = 'Anchorage';

UPDATE Production.Product
WITH (TABLOCK)
SET ListPrice = ListPrice * 1.10
WHERE ProductNumber LIKE 'BK-%';

UPDATE Production.Product
SET ListPrice = ListPrice * 1.10
WHERE ProductNumber LIKE @Product
    OPTION (OPTIMIZE FOR (@Product = 'BK-%') );

UPDATE HumanResources.Employee
SET VacationHours =  VacationHours * 1.25,
    ModifiedDate = GETDATE()
    OUTPUT inserted.BusinessEntityID,
      deleted.VacationHours,
      inserted.VacationHours,
      inserted.ModifiedDate
INTO @MyTableVar
WHERE VacationHours < 10;

UPDATE HumanResources.Employee
SET VacationHours =
        ( CASE
              WHEN SalariedFlag = 0 THEN VacationHours + @NewHours
              ELSE @NewHours
            END
            )
WHERE CurrentFlag = 1;

UPDATE HumanResources.Department
SET Name = N'MyNewName'
WHERE DepartmentID BETWEEN 1 AND 2;


UPDATE DimEmployee
SET EndDate = '2010-12-31', CurrentFlag='False';

UPDATE DimProduct
SET ProductSubcategoryKey = 2
WHERE ProductKey = 313
    OPTION (LABEL = N'label1');

UPDATE YearlyTotalSales
SET YearlySalesAmount=
        (SELECT SUM(SalesAmount) FROM FactInternetSales WHERE OrderDateKey >=20040000 AND OrderDateKey < 20050000)
WHERE Year=2004;

UPDATE dbo.Table2
SET dbo.Table2.ColB = dbo.Table2.ColB + dbo.Table1.ColB
    FROM dbo.Table2
    INNER JOIN dbo.Table1
ON (dbo.Table2.ColA = dbo.Table1.ColA);








