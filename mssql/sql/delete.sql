
DELETE FROM Sales.SalesPersonQuotaHistory;

DELETE FROM Production.ProductCostHistory
WHERE StandardCost > 1000.00;

DELETE Production.ProductCostHistory
WHERE StandardCost BETWEEN 12.00 AND 14.00
      AND EndDate IS NULL;

DELETE FROM HumanResources.EmployeePayHistory
WHERE CURRENT OF complex_cursor;


DELETE FROM Sales.SalesPersonQuotaHistory
WHERE BusinessEntityID IN
      (SELECT BusinessEntityID
       FROM Sales.SalesPerson
       WHERE SalesYTD > 2500000.00);

DELETE FROM Sales.SalesPersonQuotaHistory
    FROM Sales.SalesPersonQuotaHistory AS spqh
INNER JOIN Sales.SalesPerson AS sp
ON spqh.BusinessEntityID = sp.BusinessEntityID
WHERE sp.SalesYTD > 2500000.00;


DELETE spqh
  FROM
        Sales.SalesPersonQuotaHistory AS spqh
    INNER JOIN Sales.SalesPerson AS sp
        ON spqh.BusinessEntityID = sp.BusinessEntityID
  WHERE  sp.SalesYTD > 2500000.00;

DELETE TOP (20)
FROM Purchasing.PurchaseOrderDetail
WHERE DueDate < '20020701';

DELETE FROM Purchasing.PurchaseOrderDetail
WHERE PurchaseOrderDetailID IN
      (SELECT TOP 10 PurchaseOrderDetailID
       FROM Purchasing.PurchaseOrderDetail
       ORDER BY DueDate ASC);

DELETE MyLinkServer.AdventureWorks2022.HumanResources.Department
WHERE DepartmentID > 16;


DELETE OPENQUERY (MyLinkServer, 'SELECT Name, GroupName
FROM AdventureWorks2022.HumanResources.Department
WHERE DepartmentID = 18');

DELETE FROM OPENDATASOURCE('SQLNCLI',
    'Data Source= <server_name>; Integrated Security=SSPI')
    .AdventureWorks2022.HumanResources.Department
WHERE DepartmentID = 17;


DELETE Sales.ShoppingCartItem
OUTPUT DELETED.*
WHERE ShoppingCartID = 20621;

DELETE Production.ProductProductPhoto
OUTPUT DELETED.ProductID,
       p.Name,
       p.ProductModelID,
       DELETED.ProductPhotoID
    INTO @MyTableVar
FROM Production.ProductProductPhoto AS ph
JOIN Production.Product as p
    ON ph.ProductID = p.ProductID
    WHERE p.ProductModelID BETWEEN 120 and 130;


DELETE FROM Table1
WHERE StandardCost > 1000.00;

DELETE FROM Table1
    OPTION ( LABEL = N'label1' );


DELETE FROM dbo.FactInternetSales
WHERE ProductKey IN (
    SELECT T1.ProductKey FROM dbo.DimProduct T1
                                  JOIN dbo.DimProductSubcategory T2
                                       ON T1.ProductSubcategoryKey = T2.ProductSubcategoryKey
    WHERE T2.EnglishProductSubcategoryName = 'Road Bikes' )
    OPTION ( LABEL = N'CustomJoin', HASH JOIN ) ;

DELETE tableA WHERE EXISTS (
SELECT TOP 1 1 FROM tableB tb WHERE tb.col1 = tableA.col1
)


DELETE dbo.Table2
FROM dbo.Table2
    INNER JOIN dbo.Table1
    ON (dbo.Table2.ColA = dbo.Table1.ColA)
    WHERE dbo.Table2.ColA = 1;







