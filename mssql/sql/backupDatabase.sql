BACKUP DATABASE Sales
TO      URL = 's3://10.10.10.10:8787/sqls3backups/sales_01.bak'
,       URL = 's3://10.10.10.10:8787/sqls3backups/sales_02.bak'
,       URL = 's3://10.10.10.10:8787/sqls3backups/sales_03.bak'
WITH    FORMAT
,       STATS               = 10
,       COMPRESSION;


BACKUP DATABASE Sales
TO URL = 'https://mystorageaccount.blob.core.windows.net/myfirstcontainer/Sales-01.bak',
URL = 'https://mystorageaccount.blob.core.windows.net/myfirstcontainer/Sales-02.bak',
URL = 'https://mystorageaccount.blob.core.windows.net/myfirstcontainer/Sales-03.bak',
URL = 'https://mystorageaccount.blob.core.windows.net/myfirstcontainer/Sales-04.bak'
WITH COPY_ONLY;

BACKUP DATABASE Sales
TO URL = 'https://mystorageaccount.blob.core.windows.net/myfirstcontainer/Sales.bak'
WITH STATS = 5;

BACKUP DATABASE AdventureWorks2022 TO DISK='Z:\SQLServerBackups\AdvWorksData.bak'
WITH
    FORMAT,
    COMPRESSION;

BACKUP DATABASE AdventureWorks2022
TO TAPE = '\\.\tape0', TAPE = '\\.\tape1'
MIRROR TO TAPE = '\\.\tape2', TAPE = '\\.\tape3'
WITH
    FORMAT,
    MEDIANAME = 'AdventureWorksSet1';

BACKUP DATABASE AdventureWorks2022
TO TAPE = '\\.\tape0'
MIRROR TO TAPE = '\\.\tape1'
MIRROR TO TAPE = '\\.\tape2'
MIRROR TO TAPE = '\\.\tape3'
WITH
    FORMAT,
    MEDIANAME = 'AdventureWorksSet0';

--Back up the files in SalesGroup1:
BACKUP DATABASE Sales
    FILEGROUP = 'SalesGroup1',
    FILEGROUP = 'SalesGroup2'
    TO DISK = 'Z:\SQLServerBackups\SalesFiles.bck'
    WITH
      DIFFERENTIAL;

--Back up the files in SalesGroup1:
BACKUP DATABASE Sales
    FILEGROUP = 'SalesGroup1',
    FILEGROUP = 'SalesGroup2'
    TO DISK = 'Z:\SQLServerBackups\SalesFiles.bck';

BACKUP DATABASE AdventureWorks2022 TO AdvWorksData;

BACKUP DATABASE AdventureWorks2022
 TO DISK = 'Z:\SQLServerBackups\AdvWorksData.bak'
    WITH FORMAT;

BACKUP DATABASE [AdventureWorks2022]
TO URL  = 's3://datavirtualizationsample.s3.amazonaws.com/backup/AdventureWorks2022.bak'
WITH
  BACKUP_OPTIONS = '{"s3": {"region":"us-west-2"}}' -- REGION AS PARAMETER)
, COMPRESSION, FORMAT, MAXTRANSFERSIZE = 20971520;




























