CREATE TYPE dbo.udt_money FROM varchar(11) NOT NULL;

CREATE TYPE SSN
    FROM VARCHAR(11) NOT NULL;


CREATE TYPE Utf8String
    EXTERNAL NAME utf8string.[Microsoft.Samples.SqlServer.utf8string];


CREATE TYPE LocationTableType AS TABLE (
    LocationName VARCHAR(50),
    CostRate INT
    );

CREATE TYPE InventoryItem AS TABLE (
    [Name] NVARCHAR(50) NOT NULL,
    SupplierId BIGINT NOT NULL,
    Price DECIMAL(18, 4) NULL,
    PRIMARY KEY (Name),
    INDEX IX_InventoryItem_Price(Price)
    );

DROP TYPE ssn ;


























































































