ALTER SERVER AUDIT HIPAA_Audit
WITH (STATE = OFF);

ALTER SERVER AUDIT HIPAA_Audit
MODIFY NAME = HIPAA_Audit_Old;

ALTER SERVER AUDIT HIPAA_Audit_Old
WITH (STATE = ON);

ALTER SERVER AUDIT HIPAA_Audit
TO FILE (FILEPATH ='\\SQLPROD_1\Audit\',
          MAXSIZE = 1000 MB,
          RESERVE_DISK_SPACE=OFF)
WITH (QUEUE_DELAY = 1000,
       ON_FAILURE = CONTINUE);


ALTER SERVER AUDIT [FilterForSensitiveData] WITH (STATE = OFF);

ALTER SERVER AUDIT [FilterForSensitiveData]
WHERE user_defined_event_id = 27;

ALTER SERVER AUDIT [FilterForSensitiveData] WITH (STATE = ON);

ALTER SERVER AUDIT [FilterForSensitiveData] WITH (STATE = OFF)

ALTER SERVER AUDIT [FilterForSensitiveData]
REMOVE WHERE;

ALTER SERVER AUDIT [FilterForSensitiveData] WITH (STATE = ON);


ALTER SERVER AUDIT [FilterForSensitiveData] WITH (STATE = OFF)

ALTER SERVER AUDIT [FilterForSensitiveData]
MODIFY NAME = AuditDataAccess;

ALTER SERVER AUDIT [AuditDataAccess] WITH (STATE = ON);

CREATE SERVER AUDIT HIPAA_Audit TO FILE (
    FILEPATH = '\\SQLPROD_1\Audit\'
);


CREATE SERVER AUDIT HIPAA_Audit TO APPLICATION_LOG
WITH (QUEUE_DELAY = 1000, ON_FAILURE = SHUTDOWN);


CREATE SERVER AUDIT AuditDataAccess TO FILE (FILEPATH = 'C:\SQLAudit\')
    WHERE object_name = 'SensitiveData';


DROP SERVER AUDIT HIPAA_Audit;


































































































































