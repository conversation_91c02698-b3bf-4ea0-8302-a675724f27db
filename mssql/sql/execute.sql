
EXECUTE ('USE AdventureWorks2022; SELECT BusinessEntityID, JobTitle FROM HumanResources.Employee;');


EXECUTE dbo.uspGetEmployeeManagers 6;


EXECUTE dbo.uspGetEmployeeManagers @EmployeeID = 6;

dbo.uspGetEmployeeManagers 6;
GO


dbo.uspGetEmployeeManagers @EmployeeID = 6;
GO


EXECUTE dbo.uspGetWhereUsedProductID 819, @CheckDate;

WHILE (@@FETCH_STATUS <> -1)
BEGIN
EXECUTE ('ALTER INDEX ALL ON ' +
         @schemaname + '.' +
         @tablename + ' REBUILD;');
FETCH NEXT FROM tables_cursor INTO @schemaname, @tablename;
END



EXECUTE
    @retstat = SQLSERVER1.AdventureWorks2022.dbo.uspGetEmployeeManagers
        @BusinessEntityID = 6;


EXECUTE @proc_name;


-- Specifying a value only for one parameter (@p2).
EXECUTE dbo.ProcTestDefaults @p2 = 'A';

-- Specifying a value for the first two parameters.
EXECUTE dbo.ProcTestDefaults 68, 'B';

-- Specifying a value for all three parameters.
EXECUTE dbo.ProcTestDefaults 68, 'C', 'House';

-- Using the DEFAULT keyword for the first parameter.
EXECUTE dbo.ProcTestDefaults
            @p1 = DEFAULT,
    @p2 = 'D';

-- Specifying the parameters in an order different from the order defined in the procedure.
EXECUTE dbo.ProcTestDefaults DEFAULT,
    @p3 = 'Local',
    @p2 = 'E';

-- Using the DEFAULT keyword for the first and third parameters.
EXECUTE dbo.ProcTestDefaults DEFAULT, 'H', DEFAULT;
EXECUTE dbo.ProcTestDefaults DEFAULT, 'I', @p3 = DEFAULT;

EXECUTE sp_addlinkedserver 'SeattleSales', 'SQL Server';
GO


EXECUTE ('CREATE TABLE AdventureWorks2022.dbo.SalesTbl
(SalesID INT, SalesName VARCHAR(10)); ') AT SeattleSales;


EXECUTE dbo.Proc_Test_Defaults @p2 = 'A' WITH RECOMPILE;



EXECUTE
    @returnstatus = dbo.ufnGetSalesOrderStatusText
        @Status = 2;

EXECUTE sp_addlinkedserver
            @server = 'ORACLE',
    @srvproduct = 'Oracle',
    @provider = 'OraOLEDB.Oracle',
    @datasrc = 'ORACLE10';

EXECUTE sp_addlinkedsrvlogin
            @rmtsrvname = 'ORACLE',
    @useself = 'false',
    @locallogin = NULL,
    @rmtuser = 'scott',
    @rmtpassword = 'tiger';

EXECUTE sp_serveroption 'ORACLE', 'rpc out', true;
GO

EXECUTE ('SELECT * FROM scott.emp') AT ORACLE;


EXECUTE ('SELECT * FROM scott.emp WHERE MGR = ?', 7902) AT ORACLE;


EXECUTE ('SELECT * FROM scott.emp WHERE MGR = ?', @v) AT ORACLE;


EXECUTE ('CREATE TABLE Sales.SalesTable (SalesID INT, SalesName VARCHAR(10));')
    AS USER = 'User1';

EXECUTE ('SELECT ProductID, Name
    FROM AdventureWorks2022.Production.Product
    WHERE ProductID = ? ', 952) AT SeattleSales;

EXECUTE sp_addlinkedserver 'SeattleSales', 'SQL Server';

EXECUTE uspGetEmployeeManagers 16 WITH RESULT SETS
((
    [Reporting Level] INT NOT NULL,
    [ID of Employee] INT NOT NULL,
    [Employee First Name] NVARCHAR (50) NOT NULL,
    [Employee Last Name] NVARCHAR (50) NOT NULL,
    [Employee ID of Manager] NVARCHAR (MAX) NOT NULL,
    [Manager First Name] NVARCHAR (50) NOT NULL,
    [Manager Last Name] NVARCHAR (50) NOT NULL
));

EXECUTE Production.ProductList '%tire%' WITH RESULT SETS
(
    -- first result set definition starts here
    (ProductID INT,
    [Name] NAME,
    ListPrice MONEY)
    -- comma separates result set definitions
    ,
    -- second result set definition starts here
    ([Name] NAME,
    NumberOfOrders INT)
);

EXECUTE ( 'SELECT @@SERVERNAME' ) AT DATA_SOURCE my_sql_server;


EXECUTE ('SELECT @@SERVERNAME') AT DATA_SOURCE SqlComputePool;
EXECUTE ('EXECUTE ' + @var);

EXECUTE (@stringVar);

EXECUTE ProcWithParameters N'%arm%', N'Black';
EXECUTE ProcWithParameters
            @name = N'%arm%',
    @color = N'Black';








