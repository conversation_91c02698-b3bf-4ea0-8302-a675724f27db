ALTER LOGIN Mary5 ENABLE;

ALTER LOGIN Mary5 WITH PASSWORD = '<enterStrongPasswordHere>';

ALTER LOGIN Mary5 WITH PASSWORD = '<enterStrongPasswordHere>' OLD_PASSWORD = '<oldWeakPasswordHere>';

ALTER LOGIN Mary5 WITH NAME = John2;

ALTER LOGIN John2 WITH CREDENTIAL = Custodian04;

ALTER LOGIN Mary5 ADD CREDENTIAL EKMProvider1;
ALTER LOGIN [Mary5] WITH PASSWORD = '****' UNLOCK;
ALTER LOGIN [Mary5] WITH CHECK_POLICY = OFF;
ALTER LOGIN [Mary5] WITH CHECK_POLICY = ON;
ALTER LOGIN TestUser WITH PASSWORD = 0x01000CF35567C60BFB41EBDE4CF700A985A13D773D6B45B90900 HASHED;

CREATE LOGIN login_name WITH PASSWORD = '<enterStrongPasswordHere>';

CREATE LOGIN xxx WITH PASSWORD = '<enterStrongPasswordHere>'
    MUST_CHANGE, CHECK_EXPIRATION = ON;

CREATE LOGIN login_name WITH PASSWORD = '<enterStrongPasswordHere>',
    CREDENTIAL = credentialName;

CREATE LOGIN login_name FROM CERTIFICATE certificateName;

CREATE LOGIN [<domainName>\<login_name>] FROM WINDOWS;


CREATE LOGIN TestLogin WITH PASSWORD = 'SuperSecret52&&';


CREATE LOGIN TestLogin
WITH PASSWORD = 'SuperSecret52&&', SID = 0x241C11948AEEB749B0D22646DB1A19F2;

CREATE LOGIN [MyUser]
WITH PASSWORD = '<password>',
DEFAULT_DATABASE = MyDatabase,
CHECK_POLICY = OFF,
CHECK_EXPIRATION = OFF ;

CREATE LOGIN Andreas
  WITH PASSWORD = 0x02000A1A89CD6C6E4C8B30A282354C8EA0860719D5D3AD05E0CAE1952A1C6107A4ED26BEBA2A13B12FAB5093B3CC2A1055910CC0F4B9686A358604E99BB9933C75B4EA48FDEA HASHED;


CREATE LOGIN [<EMAIL>] FROM EXTERNAL PROVIDER

CREATE LOGIN [<EMAIL>] FROM EXTERNAL PROVIDER

CREATE LOGIN [mygroup] FROM EXTERNAL PROVIDER

DROP LOGIN WilliJo;


















































































































