ALTER SEQUENCE Test. TestSeq
    RESTART WITH 100
    INCREMENT BY 50
    MINVALUE 50
    MAXVALUE 200
    NO CYCLE
    NO CACHE
;

ALTER SEQUENCE Test.CountBy1 RESTART WITH 1 ;

ALTER SEQUENCE Test.CountBy1
    CYCLE
    CACHE 20 ;

CREATE SEQUENCE Test.CountBy1
    START WITH 1
    INCREMENT BY 1 ;
GO

CREATE SEQUENCE Test.CountByNeg1
    START WITH 0
    INCREMENT BY -1 ;
GO

CREATE SEQUENCE Test.CountBy1
    START WITH 5
    INCREMENT BY 5 ;
GO

CREATE SEQUENCE Test.ID_Seq
    START WITH 24329
    INCREMENT BY 1 ;
GO


CREATE SEQUENCE Test.TestSequence ;


SELECT * FROM sys.sequences WHERE name = 'TestSequence' ;

CREATE SEQUENCE SmallSeq
    AS smallint ;

CREATE SEQUENCE Test.DecSeq
    AS decimal(3,0)
    START WITH 125
    INCREMENT BY 25
    MINVALUE 100
    MAXVALUE 200
    CYCLE
    CACHE 3
;

DROP SEQUENCE CountBy1 ;



































































































































