ALTER CERTIFICATE Shipping04
    REMOVE PRIVATE KEY;

ALTER CERTIFICATE Shipping11
    WITH PRIVATE KEY (DECRYPTION BY PASSWORD = '95hkjdskghFDGGG4%',
    ENCRYPTION BY PASSWORD = '34958tosdgfkh##38');

ALTER CERTIFICATE Shipping13
    WITH PRIVATE KEY (FILE = 'c:\importedkeys\Shipping13',
    DECRYPTION BY PASSWORD = 'GDFLKl8^^GGG4000%');

ALTER CERTIFICATE Shipping15
    WITH PRIVATE KEY (DECRYPTION BY PASSWORD = '95hk000eEnvjkjy#F%');


CREATE CERTIFICATE Shipping04
   ENCRYPTION BY PASSWORD = 'pGFD4bb925DGvbd2439587y'
   WITH SUBJECT = 'Sammamish Shipping Records',
   EXPIRY_DATE = '20201031';
GO

CREATE CERTIFICATE Shipping11
    FROM FILE = 'c:\Shipping\Certs\Shipping11.cer'
    WITH PRIVATE KEY (FILE = 'c:\Shipping\Certs\Shipping11.pvk',
    DECRYPTION BY PASSWORD = 'sldkflk34et6gs%53#v00');
GO

CREATE CERTIFICATE Shipping19
    FROM EXECUTABLE FILE = 'c:\Shipping\Certs\Shipping19.dll';
GO

CREATE CERTIFICATE Shipping19 FROM ASSEMBLY Shipping19;

CREATE CERTIFICATE Shipping04
   WITH SUBJECT = 'Sammamish Shipping Records';
GO

CREATE CERTIFICATE Shipping04
    FROM FILE = 'c:\storedcerts\shipping04cert.pfx'
    WITH
    FORMAT = 'PFX',
	PRIVATE KEY (
        DECRYPTION BY PASSWORD = '9n34khUbhk$w4ecJH5gh'
	);

DROP CERTIFICATE Shipping04;

























































































