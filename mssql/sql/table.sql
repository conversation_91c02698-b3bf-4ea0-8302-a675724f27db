ALTER TABLE MyTable ALTER COLUMN NullCOl NVARCHAR(20) NOT NULL;

ALTER TABLE table_name
    SET ( REMOTE_DATA_ARCHIVE ( MIGRATION_STATE = INBOUND ) ) ;


ALTER TABLE table_name
    SET ( REMOTE_DATA_ARCHIVE = OFF_WITHOUT_DATA_RECOVERY ( MIGRATION_STATE = PAUSED ) ) ;

ALTER TABLE dbo.doc_exa ADD column_b VARCHAR(20) NULL ;

ALTER TABLE dbo.doc_exc ADD column_b VARCHAR(20) NULL
    CONSTRAINT exb_unique UNIQUE ;

ALTER TABLE dbo.doc_exd WITH NOCHECK
    ADD CONSTRAINT exd_check CHECK (column_a > 1) ;

ALTER TABLE dbo.doc_exz
    ADD CONSTRAINT col_b_def
        DEFAULT 50 FOR column_b ;

ALTER TABLE dbo.doc_exe ADD
    column_b INT IDENTITY
CONSTRAINT column_b_pk PRIMARY KEY,
column_c INT NULL
CONSTRAINT column_c_fk
REFERENCES doc_exe(column_a),
column_d VARCHAR(16) NULL
CONSTRAINT column_d_chk
CHECK
(column_d LIKE '[0-9][0-9][0-9]-[0-9][0-9][0-9][0-9]' OR
column_d LIKE
'([0-9][0-9][0-9]) [0-9][0-9][0-9]-[0-9][0-9][0-9][0-9]'),
column_e DECIMAL(3,3)
CONSTRAINT column_e_default
DEFAULT .081 ;

ALTER TABLE dbo.doc_exf
    ADD AddDate smalldatetime NULL
CONSTRAINT AddDateDflt
DEFAULT GETDATE() WITH VALUES ;

ALTER TABLE Production.TransactionHistoryArchive WITH NOCHECK
    ADD CONSTRAINT PK_TransactionHistoryArchive_TransactionID PRIMARY KEY CLUSTERED (TransactionID)
    WITH (FILLFACTOR = 75, ONLINE = ON, PAD_INDEX = ON);

ALTER TABLE Production.TransactionHistoryArchive WITH NOCHECK
    ADD CONSTRAINT PK_TransactionHistoryArchive_TransactionID PRIMARY KEY CLUSTERED (TransactionID)
    WITH (DATA_COMPRESSION = PAGE);

ALTER TABLE T1
    ADD C5 CHAR(100) SPARSE NULL ;

ALTER TABLE T1
ALTER COLUMN C4 ADD SPARSE ;

ALTER TABLE T1
ALTER COLUMN C4 DROP SPARSE ;

ALTER TABLE T2
    ADD CS XML COLUMN_SET FOR ALL_SPARSE_COLUMNS ;

ALTER TABLE T2
ALTER COLUMN C2 ADD SPARSE ;

ALTER TABLE T2
ALTER COLUMN C3 ADD SPARSE ;

ALTER TABLE Customers ADD
    PromotionCode nvarchar(100)
    ENCRYPTED WITH (COLUMN_ENCRYPTION_KEY = MyCEK,
    ENCRYPTION_TYPE = RANDOMIZED,
    ALGORITHM = 'AEAD_AES_256_CBC_HMAC_SHA_256') ;

ALTER TABLE table1
    ADD CONSTRAINT PK_Constrain PRIMARY KEY CLUSTERED (a)
    WITH (ONLINE = ON, MAXDOP = 2, RESUMABLE = ON, MAX_DURATION = 240);

ALTER TABLE dbo.doc_exb DROP COLUMN column_b ;

ALTER TABLE dbo.doc_exb DROP COLUMN column_c, column_d;

ALTER TABLE dbo.doc_exc DROP my_constraint ;

ALTER TABLE dbo.doc_exc
DROP CONSTRAINT my_constraint, my_pk_constraint, COLUMN column_b ;


ALTER TABLE Production.TransactionHistoryArchive
DROP CONSTRAINT PK_TransactionHistoryArchive_TransactionID
WITH (ONLINE = ON) ;

ALTER TABLE Person.ContactBackup
    ADD CONSTRAINT FK_ContactBackup_Contact FOREIGN KEY (ContactID)
        REFERENCES Person.Person (BusinessEntityID) ;

ALTER TABLE Person.ContactBackup
DROP CONSTRAINT FK_ContactBackup_Contact ;

ALTER TABLE dbo.doc_exy ALTER COLUMN column_a DECIMAL (5, 2) ;

ALTER TABLE dbo.doc_exy ALTER COLUMN col_a varchar(25) ;

ALTER TABLE dbo.doc_exy ALTER COLUMN col_b decimal (10,4) ;

ALTER TABLE T3
ALTER COLUMN C2 varchar(50) COLLATE Latin1_General_BIN ;

ALTER TABLE T3
ALTER COLUMN C2a VARCHAR(50) ENCRYPTED
WITH (COLUMN_ENCRYPTION_KEY = [CEK1], ENCRYPTION_TYPE = Randomized, ALGORITHM = 'AEAD_AES_256_CBC_HMAC_SHA_256') NULL;

ALTER TABLE T1
    REBUILD WITH (DATA_COMPRESSION = PAGE) ;

ALTER TABLE PartitionTable1
    REBUILD PARTITION = 1 WITH (DATA_COMPRESSION =NONE) ;

ALTER TABLE PartitionTable1
    REBUILD PARTITION = ALL
    WITH (DATA_COMPRESSION = PAGE ON PARTITIONS(1)) ;

ALTER TABLE PartitionTable1
    REBUILD PARTITION = 1 WITH (DATA_COMPRESSION = COLUMNSTORE_ARCHIVE) ;


ALTER TABLE PartitionTable1
    REBUILD PARTITION = 1 WITH (DATA_COMPRESSION = COLUMNSTORE) ;

ALTER TABLE PartitionTable SWITCH PARTITION 2 TO NonPartitionTable ;

ALTER TABLE dbo.T1 SET (LOCK_ESCALATION = AUTO) ;

ALTER TABLE Person.Person
    ENABLE CHANGE_TRACKING ;

ALTER TABLE Person.Person
    ENABLE CHANGE_TRACKING
    WITH (TRACK_COLUMNS_UPDATED = ON);

ALTER TABLE dbo.cnst_example CHECK CONSTRAINT salary_cap;

ALTER TABLE dbo.trig_example DISABLE TRIGGER trig1 ;

ALTER TABLE T1
    REBUILD WITH
    (
    PAD_INDEX = ON,
    ONLINE = ON ( WAIT_AT_LOW_PRIORITY ( MAX_DURATION = 4 MINUTES,
    ABORT_AFTER_WAIT = BLOCKERS ) )
    ) ;

ALTER TABLE dbo.doc_exy
ALTER COLUMN column_a DECIMAL (5, 2) WITH (ONLINE = ON) ;


ALTER TABLE InsurancePolicy
    ADD PERIOD FOR SYSTEM_TIME (ValidFrom, ValidTo),
ValidFrom datetime2 GENERATED ALWAYS AS ROW START HIDDEN NOT NULL
    DEFAULT SYSUTCDATETIME(),
ValidTo datetime2 GENERATED ALWAYS AS ROW END HIDDEN NOT NULL
    DEFAULT CONVERT(DATETIME2, '9999-12-31 23:59:59.99999999') ;

ALTER TABLE InsurancePolicy
    SET (SYSTEM_VERSIONING = ON (HISTORY_RETENTION_PERIOD = 1 YEAR)) ;

ALTER TABLE InsurancePolicy
    ADD PERIOD FOR SYSTEM_TIME (ValidFrom, ValidTo),
ValidFrom datetime2 GENERATED ALWAYS AS ROW START HIDDEN NOT NULL
    DEFAULT SYSUTCDATETIME(),
ValidTo datetime2 GENERATED ALWAYS AS ROW END HIDDEN NOT NULL
    DEFAULT CONVERT(DATETIME2, '9999-12-31 23:59:59.99999999') ;


ALTER TABLE InsurancePolicy
    SET (SYSTEM_VERSIONING = ON (HISTORY_RETENTION_PERIOD = 1 YEAR)) ;


-- Adjust the schema for current and history table
-- Change data types for existing period columns
ALTER TABLE ProjectTask ALTER COLUMN [Changed Date] datetime2 NOT NULL ;
ALTER TABLE ProjectTask ALTER COLUMN [Revised Date] datetime2 NOT NULL ;
ALTER TABLE ProjectTaskHistory ALTER COLUMN [Changed Date] datetime2 NOT NULL ;
ALTER TABLE ProjectTaskHistory ALTER COLUMN [Revised Date] datetime2 NOT NULL ;

-- Add SYSTEM_TIME period and set system versioning with linking two existing tables
-- (a certain set of data checks happen in the background)
ALTER TABLE ProjectTask
    ADD PERIOD FOR SYSTEM_TIME ([Changed Date], [Revised Date])

ALTER TABLE ProjectTask
    SET (SYSTEM_VERSIONING = ON (HISTORY_TABLE = dbo.ProjectTaskHistory, DATA_CONSISTENCY_CHECK = ON))

/* Takes schema lock on both tables */
ALTER TABLE Department
    SET (SYSTEM_VERSIONING = OFF) ;
/* expand table schema for temporal table */
ALTER TABLE Department
    ADD Col5 int NOT NULL DEFAULT 0 ;
/* Expand table schema for history table */
ALTER TABLE DepartmentHistory
    ADD Col5 int NOT NULL DEFAULT 0 ;
/* Re-establish versioning again*/
ALTER TABLE Department
    SET (SYSTEM_VERSIONING = ON (HISTORY_TABLE=dbo.DepartmentHistory,
    DATA_CONSISTENCY_CHECK = OFF)) ;

ALTER TABLE Department
    SET (SYSTEM_VERSIONING = OFF) ;

ALTER TABLE Department
DROP PERIOD FOR SYSTEM_TIME ;

ALTER TABLE Customer MERGE RANGE (10);

ALTER TABLE Customer SPLIT RANGE (75);

ALTER TABLE Orders SWITCH PARTITION 1 TO OrdersHistory PARTITION 1;

ALTER TABLE Orders MERGE RANGE ('2004-01-01');

ALTER TABLE OrdersHistory SPLIT RANGE ('2005-01-01');

CREATE TABLE dbo.Employee
(
    EmployeeID INT PRIMARY KEY CLUSTERED
);


CREATE TABLE dbo.PurchaseOrderDetail
(
    PurchaseOrderID INT NOT NULL FOREIGN KEY REFERENCES Purchasing.PurchaseOrderHeader (PurchaseOrderID),
    LineNumber SMALLINT NOT NULL,
    ProductID INT NULL FOREIGN KEY REFERENCES Production.Product (ProductID),
    UnitPrice MONEY NULL,
    OrderQty SMALLINT NULL,
    ReceivedQty FLOAT NULL,
    RejectedQty FLOAT NULL,
    DueDate DATETIME NULL,
    rowguid UNIQUEIDENTIFIER CONSTRAINT DF_PurchaseOrderDetail_rowguid DEFAULT (NEWID()) ROWGUIDCOL NOT NULL,
    ModifiedDate DATETIME CONSTRAINT DF_PurchaseOrderDetail_ModifiedDate DEFAULT (GETDATE()) NOT NULL,
    LineTotal AS ((UnitPrice * OrderQty)),
    StockedQty AS ((ReceivedQty - RejectedQty)),
    CONSTRAINT PK_PurchaseOrderDetail_PurchaseOrderID_LineNumber PRIMARY KEY CLUSTERED (PurchaseOrderID, LineNumber) WITH (IGNORE_DUP_KEY = OFF)
) ON [PRIMARY];

CREATE TABLE HumanResources.EmployeeResumes
(
    LName NVARCHAR (25),
    FName NVARCHAR (25),
    Resume XML(DOCUMENT HumanResources.HRResumeSchemaCollection)
);

CREATE TABLE PartitionTable
(
    col1 INT,
    col2 CHAR (10)
) ON myRangePS1 (col1);

CREATE TABLE dbo.Globally_Unique_Data
(
    GUID UNIQUEIDENTIFIER CONSTRAINT Guid_Default DEFAULT NEWSEQUENTIALID() ROWGUIDCOL,
    Employee_Name VARCHAR (60) CONSTRAINT Guid_PK PRIMARY KEY (GUID)
);

CREATE TABLE dbo.mytable
(
    low INT,
    high INT,
    myavg AS (low + high) / 2
);

CREATE TABLE UDTypeTable
(
    u UTF8STRING,
    ustr AS u.ToString() PERSISTED
);

CREATE TABLE dbo.mylogintable
(
    date_in DATETIME,
    user_id INT,
    myuser_name AS USER_NAME()
);

CREATE TABLE dbo.EmployeePhoto
(
    EmployeeId INT NOT NULL PRIMARY KEY,
    Photo VARBINARY (MAX) FILESTREAM NULL,
    MyRowGuidColumn UNIQUEIDENTIFIER DEFAULT NEWID() ROWGUIDCOL NOT NULL UNIQUE
);

CREATE TABLE dbo.T1
(
    c1 INT,
    c2 NVARCHAR (200)
)
    WITH (DATA_COMPRESSION = ROW);


CREATE TABLE dbo.T1
(
    c1 INT,
    c2 XML
)
    WITH (XML_COMPRESSION = ON);


CREATE TABLE dbo.T1
(
    c1 INT PRIMARY KEY,
    c2 VARCHAR (50) SPARSE NULL
);

CREATE TABLE T1
(
    c1 INT PRIMARY KEY,
    c2 VARCHAR (50) SPARSE NULL,
    c3 INT SPARSE NULL,
    CSet XML COLUMN_SET FOR ALL_SPARSE_COLUMNS
);

CREATE TABLE Department
(
    DepartmentNumber CHAR (10) NOT NULL PRIMARY KEY CLUSTERED,
    DepartmentName VARCHAR (50) NOT NULL,
    ManagerID INT NULL,
    ParentDepartmentNumber CHAR (10) NULL,
    ValidFrom DATETIME2 GENERATED ALWAYS AS ROW START HIDDEN NOT NULL,
    ValidTo DATETIME2 GENERATED ALWAYS AS ROW END HIDDEN NOT NULL,
    PERIOD FOR SYSTEM_TIME (ValidFrom, ValidTo)
)
    WITH (SYSTEM_VERSIONING = ON);

CREATE TABLE Department_History
(
    DepartmentNumber CHAR (10) NOT NULL,
    DepartmentName VARCHAR (50) NOT NULL,
    ManagerID INT NULL,
    ParentDepartmentNumber CHAR (10) NULL,
    ValidFrom DATETIME2 NOT NULL,
    ValidTo DATETIME2 NOT NULL
);

CREATE TABLE Department
(
    DepartmentNumber CHAR (10) NOT NULL PRIMARY KEY CLUSTERED,
    DepartmentName VARCHAR (50) NOT NULL,
    ManagerID INT NULL,
    ParentDepartmentNumber CHAR (10) NULL,
    ValidFrom DATETIME2 GENERATED ALWAYS AS ROW START HIDDEN NOT NULL,
    ValidTo DATETIME2 GENERATED ALWAYS AS ROW END HIDDEN NOT NULL,
    PERIOD FOR SYSTEM_TIME (ValidFrom, ValidTo)
)
    WITH (SYSTEM_VERSIONING = ON (HISTORY_TABLE=dbo.Department_History, DATA_CONSISTENCY_CHECK=ON));

CREATE TABLE dbo.Department
(
    DepartmentNumber CHAR (10) NOT NULL PRIMARY KEY NONCLUSTERED,
    DepartmentName VARCHAR (50) NOT NULL,
    ManagerID INT NULL,
    ParentDepartmentNumber CHAR (10) NULL,
    ValidFrom DATETIME2 GENERATED ALWAYS AS ROW START HIDDEN NOT NULL,
    ValidTo DATETIME2 GENERATED ALWAYS AS ROW END HIDDEN NOT NULL,
    PERIOD FOR SYSTEM_TIME (ValidFrom, ValidTo)
)
    WITH (MEMORY_OPTIMIZED = ON, DURABILITY = SCHEMA_AND_DATA, SYSTEM_VERSIONING = ON (HISTORY_TABLE=History.DepartmentHistory));


CREATE TABLE Department_History
(
    DepartmentNumber CHAR (10) NOT NULL,
    DepartmentName VARCHAR (50) NOT NULL,
    ManagerID INT NULL,
    ParentDepartmentNumber CHAR (10) NULL,
    ValidFrom DATETIME2 NOT NULL,
    ValidTo DATETIME2 NOT NULL
);

CREATE TABLE Department
(
    DepartmentNumber CHAR (10) NOT NULL PRIMARY KEY CLUSTERED,
    DepartmentName VARCHAR (50) NOT NULL,
    ManagerID INT NULL,
    ParentDepartmentNumber CHAR (10) NULL,
    ValidFrom DATETIME2 GENERATED ALWAYS AS ROW START HIDDEN NOT NULL,
    ValidTo DATETIME2 GENERATED ALWAYS AS ROW END HIDDEN NOT NULL,
    PERIOD FOR SYSTEM_TIME (ValidFrom, ValidTo)
)
    WITH (SYSTEM_VERSIONING = ON (HISTORY_TABLE=dbo.Department_History, DATA_CONSISTENCY_CHECK=ON));

CREATE TABLE Customers
(
    CustName NVARCHAR (60)  ENCRYPTED WITH (
       COLUMN_ENCRYPTION_KEY = MyCEK,
       ENCRYPTION_TYPE = RANDOMIZED,
       ALGORITHM = 'AEAD_AES_256_CBC_HMAC_SHA_256'
    ),
    SSN VARCHAR (11) COLLATE Latin1_General_BIN2  ENCRYPTED WITH (
       COLUMN_ENCRYPTION_KEY = MyCEK,
       ENCRYPTION_TYPE = DETERMINISTIC,
       ALGORITHM = 'AEAD_AES_256_CBC_HMAC_SHA_256'
    ),
    Age INT NULL
);

CREATE TABLE t1
(
    c1 INT,
    INDEX IX1 (c1) WHERE c1 > 0
);

CREATE TABLE t1
(
    c1 INT,
    INDEX ix_1 NONCLUSTERED (c1)
);

CREATE TABLE t2
(
    c1 INT,
    c2 INT INDEX ix_1 NONCLUSTERED
);

CREATE TABLE t3
(
    c1 INT,
    c2 INT,
    INDEX ix_1 NONCLUSTERED (c1, c2)
);

CREATE TABLE #tmp
(
    c1 INT,
    c2 INT,
    PRIMARY KEY CLUSTERED ([c1], [c2])
);

CREATE TABLE [dbo].[data_retention_table]
(
    [dbdatetime2] DATETIME2 (7),
    [product_code] INT,
    [value] CHAR (10)
    )
    WITH (DATA_DELETION = ON ( FILTER_COLUMN = [dbdatetime2], RETENTION_PERIOD = 1 WEEKS ) );


CREATE TABLE [HR].[Employees]
(
    EmployeeID INT NOT NULL,
    Salary MONEY NOT NULL
)
    WITH (SYSTEM_VERSIONING = ON, LEDGER = ON);


CREATE TABLE [HR].[Employees]
(
    EmployeeID INT NOT NULL PRIMARY KEY,
    Salary MONEY NOT NULL,
    ValidFrom DATETIME2 GENERATED ALWAYS AS ROW START HIDDEN NOT NULL,
    ValidTo DATETIME2 GENERATED ALWAYS AS ROW END HIDDEN NOT NULL,
    PERIOD FOR SYSTEM_TIME (ValidFrom, ValidTo)
    )
    WITH (SYSTEM_VERSIONING = ON, LEDGER = ON);

CREATE TABLE [HR].[Employees]
(
    EmployeeID INT NOT NULL PRIMARY KEY,
    Salary MONEY NOT NULL,
    StartTransactionId BIGINT GENERATED ALWAYS AS TRANSACTION_ID START HIDDEN NOT NULL,
    EndTransactionId BIGINT GENERATED ALWAYS AS TRANSACTION_ID END HIDDEN NULL,
    StartSequenceNumber BIGINT GENERATED ALWAYS AS SEQUENCE_NUMBER START HIDDEN NOT NULL,
    EndSequenceNumber BIGINT GENERATED ALWAYS AS SEQUENCE_NUMBER END HIDDEN NULL,
    ValidFrom DATETIME2 GENERATED ALWAYS AS ROW START HIDDEN NOT NULL,
    ValidTo DATETIME2 GENERATED ALWAYS AS ROW END HIDDEN NOT NULL,
    PERIOD FOR SYSTEM_TIME (ValidFrom, ValidTo)
    )
    WITH (SYSTEM_VERSIONING = ON (HISTORY_TABLE=[HR].[EmployeesHistory]), LEDGER = ON (LEDGER_VIEW=[HR].[EmployeesLedger] (TRANSACTION_ID_COLUMN_NAME=TransactionId,SEQUENCE_NUMBER_COLUMN_NAME=SequenceNumber,OPERATION_TYPE_COLUMN_NAME=OperationId,OPERATION_TYPE_DESC_COLUMN_NAME=OperationTypeDescription)));

CREATE TABLE [HR].[Employees]
(
    EmployeeID INT NOT NULL,
    Salary MONEY NOT NULL
);

CREATE TABLE [AccessControl].[KeyCardEvents]
(
    EmployeeID INT NOT NULL,
    AccessOperationDescription NVARCHAR (MAX) NOT NULL,
    [Timestamp] DATETIME2 NOT NULL,
    StartTransactionId BIGINT GENERATED ALWAYS AS TRANSACTION_ID START HIDDEN NOT NULL,
    StartSequenceNumber BIGINT GENERATED ALWAYS AS SEQUENCE_NUMBER START HIDDEN NOT NULL
    )
    WITH (LEDGER = ON (LEDGER_VIEW=[AccessControl].[KeyCardEventsLedger] (TRANSACTION_ID_COLUMN_NAME=TransactionId,SEQUENCE_NUMBER_COLUMN_NAME=SequenceNumber,OPERATION_TYPE_COLUMN_NAME=OperationId,OPERATION_TYPE_DESC_COLUMN_NAME=OperationTypeDescription),APPEND_ONLY= ON));

CREATE TABLE friends (
                         id INTEGER PRIMARY KEY,
                         start_date DATe
) AS EDGE;

CREATE TABLE Person (
                        ID INTEGER PRIMARY KEY,
                        name VARCHAR(100),
                        email VARCHAR(100)
) AS NODE;

CREATE TABLE dbo.FriendOf(
                             CONSTRAINT cnt_Person_FriendOf_Person
                                 CONNECTION (dbo.Person TO dbo.Person)
)AS EDGE;


DROP TABLE AdventureWorks2022.dbo.SalesPerson2 ;

DROP TABLE IF EXISTS T1;

























































