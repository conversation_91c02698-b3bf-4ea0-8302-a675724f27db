ALTER RESOURCE POOL [default] WITH (MAX_CPU_PERCENT = 25);

ALTER RESOURCE POOL adhocPool
WITH (
     MIN_CPU_PERCENT = 10,
     MAX_CPU_PERCENT = 20,
     CAP_CPU_PERCENT = 30,
     MIN_MEMORY_PERCENT = 5,
     MAX_MEMORY_PERCENT = 15,
     AFFINITY SCHEDULER = (0 TO 63, 128 TO 191)
     );

CREATE RESOURCE POOL bigPool;

CREATE RESOURCE POOL adhocPool
WITH (
     MIN_CPU_PERCENT = 10,
     MAX_CPU_PERCENT = 20,
     CAP_CPU_PERCENT = 30,
     MIN_MEMORY_PERCENT = 5,
     MAX_MEMORY_PERCENT = 15,
     AFFINITY SCHEDULER = (0 TO 63, 128 TO 191)
     );


CREATE RESOURCE POOL PoolAdmin
WITH (
     MIN_IOPS_PER_VOLUME = 200,
     MAX_IOPS_PER_VOLUME = 1000
     );

DROP RESOURCE POOL big_pool;















