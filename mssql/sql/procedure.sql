ALTER PROCEDURE Purchasing.uspVendorAllInfo
    @Product VARCHAR(25)
    AS
    SET NOCOUNT ON;
SELECT LEFT(v.Name, 25) AS Vendor, LEFT(p.Name, 25) AS 'Product name',
    'Rating' = CASE v.CreditRating
    WHEN 1 THEN 'Superior'
    WHEN 2 THEN 'Excellent'
    WHEN 3 THEN 'Above average'
    WHEN 4 THEN 'Average'
    WHEN 5 THEN 'Below average'
    ELSE 'No rating'
END
, Availability = CASE v.ActiveFlag
        WHEN 1 THEN 'Yes'
        ELSE 'No'
END
FROM Purchasing.Vendor AS v
    INNER JOIN Purchasing.ProductVendor AS pv
      ON v.BusinessEntityID = pv.BusinessEntityID
    INNER JOIN Production.Product AS p
      ON pv.ProductID = p.ProductID
    WHERE p.Name LIKE @Product
    ORDER BY v.Name ASC;

CREATE PROC What_DB_is_this
AS
SELECT DB_NAME() AS ThisDB;


CREATE PROC What_DB_is_that @ID INT
AS
SELECT DB_NAME(@ID) AS ThatDB;


CREATE PROCEDURE dbo.usp_add_kitchen @dept_id INT, @kitchen_count INT NOT NULL
WITH EXECUTE AS OWNER, SCHEMABINDING, NATIVE_COMPILATION
AS
BEGIN ATOMIC WITH (TRANSACTION ISOLATION LEVEL = SNAPSHOT, LANGUAGE = N'us_english')
UPDATE dbo.Departments
SET kitchen_count = ISNULL(kitchen_count, 0) + @kitchen_count
WHERE ID = @dept_id
END;
GO

CREATE PROCEDURE dbo.uspMultipleResults
    AS
SELECT TOP(10) BusinessEntityID, Lastname, FirstName FROM Person.Person;
SELECT TOP(10) CustomerID, AccountNumber FROM Sales.Customer;
GO


CREATE PROCEDURE dbo.GetPhotoFromDB
(
    @ProductPhotoID INT
, @CurrentDirectory NVARCHAR(1024)
, @FileName NVARCHAR(1024)
)
    AS EXTERNAL NAME HandlingLOBUsingCLR.LargeObjectBinary.GetPhotoFromDB;
GO


CREATE PROCEDURE HumanResources.uspGetEmployees
    @LastName NVARCHAR(50),
    @FirstName NVARCHAR(50)
AS

    SET NOCOUNT ON;
SELECT FirstName, LastName, JobTitle, Department
FROM HumanResources.vEmployeeDepartment
WHERE FirstName = @FirstName AND LastName = @LastName;
GO

CREATE PROCEDURE HumanResources.uspGetEmployees2
    @LastName NVARCHAR(50) = N'D%',
    @FirstName NVARCHAR(50) = N'%'
AS
    SET NOCOUNT ON;
SELECT FirstName, LastName, JobTitle, Department
FROM HumanResources.vEmployeeDepartment
WHERE FirstName LIKE @FirstName AND LastName LIKE @LastName;

CREATE PROCEDURE Production.uspGetList @Product VARCHAR(40)
    , @MaxPrice MONEY
    , @ComparePrice MONEY OUTPUT
    , @ListPrice MONEY OUT
AS
    SET NOCOUNT ON;
SELECT p.[Name] AS Product, p.ListPrice AS 'List Price'
FROM Production.Product AS p
         JOIN Production.ProductSubcategory AS s
              ON p.ProductSubcategoryID = s.ProductSubcategoryID
WHERE s.[Name] LIKE @Product AND p.ListPrice < @MaxPrice;
-- Populate the output variable @ListPprice.
SET @ListPrice = (SELECT MAX(p.ListPrice)
    FROM Production.Product AS p
    JOIN Production.ProductSubcategory AS s
      ON p.ProductSubcategoryID = s.ProductSubcategoryID
    WHERE s.[Name] LIKE @Product AND p.ListPrice < @MaxPrice);
-- Populate the output variable @compareprice.
SET @ComparePrice = @MaxPrice;
GO

CREATE PROCEDURE usp_InsertProductionLocation
    @TVP LocationTableType READONLY
    AS
    SET NOCOUNT ON
    INSERT INTO [AdventureWorks2022].[Production].[Location]
       ([Name]
       , [CostRate]
       , [Availability]
       , [ModifiedDate])
SELECT *, 0, GETDATE()
FROM @TVP;
GO

CREATE PROCEDURE dbo.uspCurrencyCursor
    @CurrencyCursor CURSOR VARYING OUTPUT
AS
    SET NOCOUNT ON;
SET @CurrencyCursor = CURSOR
    FORWARD_ONLY STATIC FOR
SELECT CurrencyCode, Name
FROM Sales.Currency;
OPEN @CurrencyCursor;
GO


CREATE PROCEDURE HumanResources.Update_VacationHours
    @NewHours SMALLINT, @Rowcount INT OUTPUT
AS
SET NOCOUNT ON;
UPDATE HumanResources.Employee
SET VacationHours =
        ( CASE
              WHEN SalariedFlag = 0 THEN VacationHours + @NewHours
              ELSE @NewHours
            END
            )
WHERE CurrentFlag = 1;
SET @Rowcount = @@rowcount;


CREATE PROCEDURE Production.uspDeleteWorkOrder ( @WorkOrderID INT )
    AS
SET NOCOUNT ON;
BEGIN TRY
BEGIN TRANSACTION
DELETE FROM Production.WorkOrderRouting
WHERE WorkOrderID = @WorkOrderID;
DELETE FROM Production.WorkOrder
WHERE WorkOrderID = @WorkOrderID;
COMMIT
END TRY
BEGIN CATCH
IF @@TRANCOUNT > 0
    ROLLBACK
  DECLARE @ErrorMessage NVARCHAR(4000), @ErrorSeverity INT;
SELECT @ErrorMessage = ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY();
RAISERROR(@ErrorMessage, @ErrorSeverity, 1);
END CATCH;
GO


CREATE PROCEDURE HumanResources.uspEncryptThis
WITH ENCRYPTION
         AS
SET NOCOUNT ON;
SELECT BusinessEntityID, JobTitle, NationalIDNumber,
       VacationHours, SickLeaveHours
FROM HumanResources.Employee;
GO

CREATE PROCEDURE dbo.uspProductByVendor @Name VARCHAR(30) = '%'
WITH RECOMPILE
AS
    SET NOCOUNT ON;
SELECT v.Name AS 'Vendor name', p.Name AS 'Product name'
FROM Purchasing.Vendor AS v
         JOIN Purchasing.ProductVendor AS pv
              ON v.BusinessEntityID = pv.BusinessEntityID
         JOIN Production.Product AS p
              ON pv.ProductID = p.ProductID
WHERE v.Name LIKE @Name;

CREATE PROCEDURE Purchasing.uspVendorAllInfo
WITH EXECUTE AS CALLER
    AS
SET NOCOUNT ON;
SELECT v.Name AS Vendor, p.Name AS 'Product name',
        v.CreditRating AS 'Rating',
        v.ActiveFlag AS Availability
FROM Purchasing.Vendor v
         INNER JOIN Purchasing.ProductVendor pv
                    ON v.BusinessEntityID = pv.BusinessEntityID
         INNER JOIN Production.Product p
                    ON pv.ProductID = p.ProductID
ORDER BY v.Name ASC;
GO


CREATE PROCEDURE dbo.TruncateMyTable
WITH EXECUTE AS SELF
    AS TRUNCATE TABLE MyDB..MyTable;

CREATE PROCEDURE Get10TopResellers
    AS
BEGIN
SELECT TOP (10) r.ResellerName, r.AnnualSales
FROM DimReseller AS r
ORDER BY AnnualSales DESC, ResellerName ASC;
END
;
GO


CREATE PROCEDURE findjobs a@nm  sysname = NULL
AS
IF @nm IS NULL
BEGIN
        PRINT 'You must give a user name'
        RETURN
END
ELSE
BEGIN
SELECT o.name, o.id, o.uid
FROM sysobjects o INNER JOIN master..syslogins l
                             ON o.uid = l.sid
WHERE l.name = @nm
END;

CREATE PROCEDURE checkstate @param VARCHAR(11)
AS
IF (SELECT StateProvince FROM Person.vAdditionalContactInfo WHERE ContactID = @param) = 'WA'
    RETURN 1
ELSE
    RETURN 2;

CREATE PROCEDURE usp_GetErrorInfo
    AS
SELECT ERROR_NUMBER() AS ErrorNumber,
       ERROR_SEVERITY() AS ErrorSeverity,
       ERROR_STATE() AS ErrorState,
       ERROR_LINE() AS ErrorLine,
       ERROR_PROCEDURE() AS ErrorProcedure,
       ERROR_MESSAGE() AS ErrorMessage;



CREATE PROCEDURE dbo.TimeDelay_hh_mm_ss
(
    @DelayLength char(8)= '00:00:00'
)
    AS
DECLARE @ReturnInfo VARCHAR(255)
IF ISDATE('2000-01-01 ' + @DelayLength + '.000') = 0
BEGIN
SELECT @ReturnInfo = 'Invalid time ' + @DelayLength
    + ',hh:mm:ss, submitted.';
-- This PRINT statement is for testing, not use in production.
PRINT @ReturnInfo
        RETURN(1)
END
BEGIN
    WAITFOR DELAY @DelayLength
SELECT @ReturnInfo = 'A total time of ' + @DelayLength + ',
        hh:mm:ss, has elapsed! Your time is up.'
           -- This PRINT statement is for testing, not use in production.
           PRINT @ReturnInfo;
END;
GO

DROP PROCEDURE
    dbo.uspGetSalesbyMonth,
    dbo.uspUpdateSalesQuotes,
    dbo.uspGetSalesByYear;


DROP PROCEDURE IF EXISTS dbo.uspMyProc;


























































































