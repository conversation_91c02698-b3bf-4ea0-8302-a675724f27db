IF @@OPTIONS & 512 <> 0
    PRINT N'This user has SET NOCOUNT turned ON.';
ELSE
    PRINT N'This user has SET NOCOUNT turned OFF.';
GO

PRINT N'This message was printed on ' + RTRIM(CAST(GETDATE() AS NVARCHAR(30))) + N'.';
GO

IF DB_ID() = 1
    PRINT N'The current database is ''master''.';
ELSE
    PRINT N'The current database is not ''master''.';
GO

PRINT @PrintMessage;











































