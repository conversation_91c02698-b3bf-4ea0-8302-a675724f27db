ALTER VIEW HumanResources.EmployeeHireDate
AS
SELECT p.FirstName, p.LastName, e.HireDate
FROM HumanResources.Employee AS e JOIN Person.Person AS p
                                       ON e.BusinessEntityID = p.BusinessEntityID
WHERE HireDate < CONVERT(DATETIME,'20020101',101) ;

CREATE VIEW vw_sys_servers
AS
SELECT [name]
        FROM sys.servers
        WHERE server_id = 0;

CREATE VIEW hiredate_view
AS
SELECT p.FirstName, p.LastName, e.BusinessEntityID, e.HireDate
FROM HumanResources.Employee e
         JOIN Person.Person AS p ON e.BusinessEntityID = p.BusinessEntityID ;


CREATE VIEW Purchasing.PurchaseOrderReject
            WITH ENCRYPTION
AS
SELECT PurchaseOrderID, ReceivedQty, RejectedQty,
       RejectedQty / ReceivedQty AS RejectRatio, DueDate
FROM Purchasing.PurchaseOrderDetail
WHERE RejectedQty / ReceivedQty > 0
  AND DueDate > CONVERT(DATETIME,'20010630',101) ;

CREATE VIEW dbo.SeattleOnly
AS
SELECT p.LastName, p.FirstName, e.JobTitle, a.City, sp.StateProvinceCode
FROM HumanResources.Employee e
         INNER JOIN Person.Person p
                    ON p.BusinessEntityID = e.BusinessEntityID
         INNER JOIN Person.BusinessEntityAddress bea
                    ON bea.BusinessEntityID = e.BusinessEntityID
         INNER JOIN Person.Address a
                    ON a.AddressID = bea.AddressID
         INNER JOIN Person.StateProvince sp
                    ON sp.StateProvinceID = a.StateProvinceID
WHERE a.City = 'Seattle'
WITH CHECK OPTION ;

CREATE VIEW Sales.SalesPersonPerform
AS
SELECT TOP (100) SalesPersonID, SUM(TotalDue) AS TotalSales
FROM Sales.SalesOrderHeader
WHERE OrderDate > CONVERT(DATETIME,'20001231',101)
GROUP BY SalesPersonID;


CREATE VIEW dbo.all_supplier_view
            WITH SCHEMABINDING
AS
SELECT supplyID, supplier
FROM dbo.SUPPLY1
UNION ALL
SELECT supplyID, supplier
FROM dbo.SUPPLY2
UNION ALL
SELECT supplyID, supplier
FROM dbo.SUPPLY3
UNION ALL
SELECT supplyID, supplier
FROM dbo.SUPPLY4;

CREATE VIEW DimEmployeeBirthDates AS
SELECT FirstName, LastName, BirthDate
FROM DimEmployee;

CREATE VIEW view1
AS
SELECT fis.CustomerKey, fis.ProductKey, fis.OrderDateKey,
       fis.SalesTerritoryKey, dst.SalesTerritoryRegion
FROM FactInternetSales AS fis
         LEFT OUTER JOIN DimSalesTerritory AS dst
                         ON (fis.SalesTerritoryKey=dst.SalesTerritoryKey);


DROP VIEW IF EXISTS dbo.Reorder ;





































































































































