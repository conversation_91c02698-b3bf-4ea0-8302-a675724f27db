ALTER SEARCH PROPERTY LIST CVitaProperties
ADD 'System.Author'
WITH (
      PROPERTY_DESCRIPTION = 'Author or authors of a given document.',
      PROPERTY_SET_GUID   = 'F29F85E0-4FF9-1068-AB91-08002B27B3D9',
      PROPERTY_INT_ID = 4
      );


ALTER SEARCH PROPERTY LIST DocumentPropertyList
   ADD 'Title'
   WITH ( PROPERTY_SET_GUID = 'F29F85E0-4FF9-1068-AB91-08002B27B3D9', PROPERTY_INT_ID = 2,
      PROPERTY_DESCRIPTION = 'System.Title - Title of the item.' );

ALTER SEARCH PROPERTY LIST DocumentPropertyList
    ADD 'Author'
   WITH ( PROPERTY_SET_GUID = 'F29F85E0-4FF9-1068-AB91-08002B27B3D9', PROPERTY_INT_ID = 4,
      PROPERTY_DESCRIPTION = 'System.Author - Author or authors of the item.' );

ALTER SEARCH PROPERTY LIST DocumentPropertyList
    ADD 'Tags'
   WITH ( PROPERTY_SET_GUID = 'F29F85E0-4FF9-1068-AB91-08002B27B3D9', PROPERTY_INT_ID = 5,
      PROPERTY_DESCRIPTION =
          'System.Keywords - Set of keywords (also known as tags) assigned to the item.' );

ALTER SEARCH PROPERTY LIST DocumentPropertyList
DROP 'Comments' ;

CREATE SEARCH PROPERTY LIST DocumentPropertyList;


CREATE SEARCH PROPERTY LIST JobCandidateProperties
FROM AdventureWorks2022.DocumentPropertyList;


DROP SEARCH PROPERTY LIST JobCandidateProperties;












































































































































