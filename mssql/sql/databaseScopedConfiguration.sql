ALTER DATABASE SCOPED CONFIGURATION
SET MAXDOP = 1;

ALTER DATABASE SCOPED CONFIGURATION
FOR SECONDARY
SET MAXDOP = 4;

ALTER DATABASE SCOPED CONFIGURATION
FOR SECONDARY
SET MAXDOP = PRIMARY;

ALTER DATABASE SCOPED CONFIGURATION
FOR SECONDARY
SET LEGACY_CARDINALITY_ESTIMATION = ON;

ALTER DATABASE SCOPED CONFIGURATION
FOR SECONDARY
SET LEGACY_CARDINALITY_ESTIMATION = PRIMARY;

ALTER DATABASE SCOPED CONFIGURATION
SET PARAMETER_SNIFFING = OFF;

ALTER DATABASE SCOPED CONFIGURATION
FOR SECONDARY
SET PARAMETER_SNIFFING = OFF;

ALTER DATABASE SCOPED CONFIGURATION
FOR SECONDARY
SET PARAMETER_SNIFFING = PRIMARY;

ALTER DATABASE SCOPED CONFIGURATION
SET QUERY_OPTIMIZER_HOTFIXES = ON;

ALTER DATABASE SCOPED CONFIGURATION
CLEAR PROCEDURE_CACHE;

ALTER DATABASE SCOPED CONFIGURATION
SET IDENTITY_CACHE = OFF;

ALTER DATABASE SCOPED CONFIGURATION
SET OPTIMIZE_FOR_AD_HOC_WORKLOADS = ON;

ALTER DATABASE SCOPED CONFIGURATION
SET ELEVATE_ONLINE = FAIL_UNSUPPORTED;

ALTER DATABASE SCOPED CONFIGURATION
SET ELEVATE_RESUMABLE = WHEN_SUPPORTED;

ALTER DATABASE SCOPED CONFIGURATION CLEAR PROCEDURE_CACHE 0x06000500F443610F003B7CD12C02000001000000000000000000000000000000000000000000000000000000;

ALTER DATABASE SCOPED CONFIGURATION
SET PAUSED_RESUMABLE_INDEX_ABORT_DURATION_MINUTES = 60;

ALTER DATABASE SCOPED CONFIGURATION
SET LEDGER_DIGEST_STORAGE_ENDPOINT = 'https://mystorage.blob.core.windows.net';

ALTER DATABASE SCOPED CONFIGURATION
SET LEDGER_DIGEST_STORAGE_ENDPOINT = OFF;








