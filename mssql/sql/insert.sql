INSERT INTO #tmpdbs ([dbid], [dbname], [isdone])
SELECT database_id, name, 0 FROM master.sys.databases (NOLOCK) WHERE is_read_only = 0 AND state = 0;

INSERT INTO cci_target WITH (TABLOCK)
SELECT TOP 300000 * FROM staging;

INSERT Test.TestTable (CounterColumn,Name)
    VALUES (NEXT VALUE FOR Test.CountBy1, 'Syed') ;

INSERT INTO Cities (Location)
VALUES ( CONVERT(Point, '12.3:46.2') );

INSERT INTO Cities (Location)
VALUES ( dbo.CreateNewPoint(x, y) );

INSERT INTO Production.UnitMeasure
VALUES (N'FT', N'Feet', '20080414');

INSERT INTO Production.UnitMeasure
VALUES (N'FT2', N'Square Feet ', '20080923'), (N'Y', N'Yards', '20080923')
     , (N'Y3', N'Cubic Yards', '20080923');

INSERT INTO Production.UnitMeasure (Name, UnitMeasureCode,
                                    ModifiedDate)
VALUES (N'Square Yards', N'Y2', GETDATE());


INSERT INTO dbo.T1 (column_4)
VALUES ('Explicit value');
INSERT INTO dbo.T1 (column_2, column_4)
VALUES ('Explicit value', 'Explicit value');
INSERT INTO dbo.T1 (column_2)
VALUES ('Explicit value');
INSERT INTO T1 DEFAULT VALUES;
GO


INSERT INTO T1 (column_1,column_2)
    VALUES (-99, 'Explicit identity value');


INSERT INTO dbo.T1 (column_2)
VALUES (NEWID());
INSERT INTO T1 DEFAULT VALUES;

INSERT INTO dbo.Points (PointValue) VALUES (CONVERT(Point, '3,4'));
INSERT INTO dbo.Points (PointValue) VALUES (CONVERT(Point, '1,5'));
INSERT INTO dbo.Points (PointValue) VALUES (CAST ('1,99' AS Point));



CREATE PROCEDURE dbo.uspGetEmployeeSales
    AS
    SET NOCOUNT ON;
SELECT 'PROCEDURE', sp.BusinessEntityID, c.LastName,
       sp.SalesYTD
FROM Sales.SalesPerson AS sp
         INNER JOIN Person.Person AS c
                    ON sp.BusinessEntityID = c.BusinessEntityID
WHERE sp.BusinessEntityID LIKE '2%'
ORDER BY sp.BusinessEntityID, c.LastName;



INSERT INTO dbo.EmployeeSales
SELECT 'SELECT', sp.BusinessEntityID, c.LastName, sp.SalesYTD
FROM Sales.SalesPerson AS sp
         INNER JOIN Person.Person AS c
                    ON sp.BusinessEntityID = c.BusinessEntityID
WHERE sp.BusinessEntityID LIKE '2%'
ORDER BY sp.BusinessEntityID, c.LastName;

INSERT INTO dbo.EmployeeSales
    EXECUTE dbo.uspGetEmployeeSales;
GO


INSERT INTO HumanResources.NewEmployee
SELECT EmpID, LastName, FirstName, Phone,
       Address, City, StateProvince, PostalCode, CurrentFlag
FROM EmployeeTemp;

WITH EmployeeTemp (EmpID, LastName, FirstName, Phone,
                   Address, City, StateProvince,
                   PostalCode, CurrentFlag)
         AS (SELECT
                 e.BusinessEntityID, c.LastName, c.FirstName, pp.PhoneNumber,
                 a.AddressLine1, a.City, sp.StateProvinceCode,
                 a.PostalCode, e.CurrentFlag
             FROM HumanResources.Employee e
                      INNER JOIN Person.BusinessEntityAddress AS bea
                                 ON e.BusinessEntityID = bea.BusinessEntityID
                      INNER JOIN Person.Address AS a
                                 ON bea.AddressID = a.AddressID
                      INNER JOIN Person.PersonPhone AS pp
                                 ON e.BusinessEntityID = pp.BusinessEntityID
                      INNER JOIN Person.StateProvince AS sp
                                 ON a.StateProvinceID = sp.StateProvinceID
                      INNER JOIN Person.Person as c
                                 ON e.BusinessEntityID = c.BusinessEntityID
    )
INSERT INTO HumanResources.NewEmployee
SELECT EmpID, LastName, FirstName, Phone,
       Address, City, StateProvince, PostalCode, CurrentFlag
FROM EmployeeTemp;
GO

INSERT TOP(5)INTO dbo.EmployeeSales
    OUTPUT inserted.EmployeeID, inserted.FirstName,
        inserted.LastName, inserted.YearlySales
SELECT sp.BusinessEntityID, c.LastName, c.FirstName, sp.SalesYTD
FROM Sales.SalesPerson AS sp
         INNER JOIN Person.Person AS c
                    ON sp.BusinessEntityID = c.BusinessEntityID
WHERE sp.SalesYTD > 250000.00
ORDER BY sp.SalesYTD DESC;

INSERT INTO dbo.EmployeeSales
    OUTPUT inserted.EmployeeID, inserted.FirstName,
        inserted.LastName, inserted.YearlySales
SELECT TOP (5) sp.BusinessEntityID, c.LastName, c.FirstName, sp.SalesYTD
FROM Sales.SalesPerson AS sp
         INNER JOIN Person.Person AS c
                    ON sp.BusinessEntityID = c.BusinessEntityID
WHERE sp.SalesYTD > 250000.00
ORDER BY sp.SalesYTD DESC;


INSERT INTO V1
VALUES ('Row 1',1);


INSERT INTO @MyTableVar (LocationID, CostRate, ModifiedDate)
SELECT LocationID, CostRate, GETDATE()
FROM Production.Location
WHERE CostRate > 0;


INSERT INTO MyLinkServer.AdventureWorks2022.HumanResources.Department (Name, GroupName)
VALUES (N'Public Relations', N'Executive General and Administration');

INSERT OPENQUERY (MyLinkServer,
    'SELECT Name, GroupName
     FROM AdventureWorks2022.HumanResources.Department')
VALUES ('Environmental Impact', 'Engineering');


INSERT INTO OPENDATASOURCE('SQLNCLI',
    'Data Source= <server_name>; Integrated Security=SSPI')
    .AdventureWorks2022.HumanResources.Department (Name, GroupName)
VALUES (N'Standards and Methods', 'Quality Assurance');


INSERT INTO dbo.FastCustomer2009
SELECT T.* FROM Insured_Customers T1 JOIN CarSensor_Data T2
                                          ON (T1.CustomerKey = T2.CustomerKey)
WHERE T2.YearMeasured = 2009 and T2.Speed > 40;

INSERT INTO Sales.SalesHistory WITH (TABLOCK)
    (SalesOrderID,
     SalesOrderDetailID,
     CarrierTrackingNumber,
     OrderQty,
     ProductID,
     SpecialOfferID,
     UnitPrice,
     UnitPriceDiscount,
     LineTotal,
     rowguid,
     ModifiedDate)
SELECT * FROM Sales.SalesOrderDetail;

INSERT INTO HumanResources.Department WITH (IGNORE_TRIGGERS) (Name, GroupName)
SELECT b.Name, b.GroupName
FROM OPENROWSET (
             BULK 'C:SQLFilesDepartmentData.txt',
             FORMATFILE = 'C:SQLFilesBulkloadFormatFile.xml',
             ROWS_PER_BATCH = 15000)AS b ;

INSERT INTO Production.Location WITH (XLOCK)
    (Name, CostRate, Availability)
VALUES ( N'Final Inventory', 15.00, 80.00);

INSERT Production.ScrapReason
    OUTPUT INSERTED.ScrapReasonID, INSERTED.Name, INSERTED.ModifiedDate
        INTO @MyTableVar
VALUES (N'Operator error', GETDATE());

INSERT INTO dbo.EmployeeSales (LastName, FirstName, CurrentSales)
    OUTPUT INSERTED.LastName,
         INSERTED.FirstName,
         INSERTED.CurrentSales
  INTO @MyTableVar
SELECT c.LastName, c.FirstName, sp.SalesYTD
FROM Sales.SalesPerson AS sp
         INNER JOIN Person.Person AS c
                    ON sp.BusinessEntityID = c.BusinessEntityID
WHERE sp.BusinessEntityID LIKE '2%'
ORDER BY c.LastName, c.FirstName;


INSERT INTO EmployeeTitles
SELECT EmployeeKey, LastName, Title
FROM ssawPDW.dbo.DimEmployee
WHERE EndDate IS NULL;

INSERT INTO DimCurrency
VALUES (500, N'C1', N'Currency1')
    OPTION ( LABEL = N'label1' );

INSERT INTO Production.ZeroInventory (DeletedProductID, RemovedOnDate)
SELECT ProductID, GETDATE()
FROM
    (   MERGE Production.ProductInventory AS pi
    USING (SELECT ProductID, SUM(OrderQty) FROM Sales.SalesOrderDetail AS sod
           JOIN Sales.SalesOrderHeader AS soh
           ON sod.SalesOrderID = soh.SalesOrderID
           AND soh.OrderDate = '20070401'
           GROUP BY ProductID) AS src (ProductID, OrderQty)
    ON (pi.ProductID = src.ProductID)
    WHEN MATCHED AND pi.Quantity - src.OrderQty <= 0
        THEN DELETE
    WHEN MATCHED
        THEN UPDATE SET pi.Quantity = pi.Quantity - src.OrderQty
    OUTPUT $action, deleted.ProductID) AS Changes (Action, ProductID)
WHERE Action = 'DELETE';


INSERT INTO dbo.friend VALUES ((SELECT $node_id FROM dbo.Person WHERE name = 'Alice'),
                               (SELECT $node_id FROM dbo.Person WHERE name = 'John'), '9/15/2011');
