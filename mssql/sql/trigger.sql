ALTER TRIGGER Sales.bonus_reminder
ON Sales.SalesPersonQuotaHistory
AFTER INSERT
AS RAISERROR ('Notify Compensation', 16, 10);


CREATE TRIGGER reminder1
    ON Sales.Customer
    AFTER INSERT, UPDATE
                      AS RAISERROR ('Notify Customer Relations', 16, 10);

CREATE TRIGGER reminder2
    ON Sales.Customer
    AFTER INSERT, UPDATE, DELETE
    AS
    EXEC msdb.dbo.sp_send_dbmail
    @profile_name = 'AdventureWorks2022 Administrator',
    @recipients = '<EMAIL>',
    @body = 'Don''t forget to print a report for the sales force.',
    @subject = 'Reminder';

CREATE TRIGGER Purchasing.LowCredit ON Purchasing.PurchaseOrderHeader
    AFTER INSERT
AS
IF (ROWCOUNT_BIG() = 0)
RETURN;
IF EXISTS (SELECT 1
           FROM inserted AS i
           JOIN Purchasing.Vendor AS v
           ON v.BusinessEntityID = i.VendorID
           WHERE v.CreditRating = 5
          )
BEGIN
RAISERROR ('A vendor''s credit rating is too low to accept new
purchase orders.', 16, 1);
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> TRANSACTION;
RETURN
END;

CREATE TRIGGER safety
    ON DATABASE
    FOR DROP_SYNONYM
AS
IF (@@ROWCOUNT = 0)
RETURN;
RAISERROR ('You must disable Trigger "safety" to remove synonyms!', 10, 1)
   ROLLBACK

CREATE TRIGGER ddl_trig_database
    ON ALL SERVER
FOR CREATE_DATABASE
AS
    PRINT 'Database Created.'
SELECT EVENTDATA().value('(/EVENT_INSTANCE/TSQLCommand/CommandText)[1]','nvarchar(max)')

CREATE TRIGGER connection_limit_trigger
    ON ALL SERVER WITH EXECUTE AS 'login_test'
FOR LOGON
AS
BEGIN
IF ORIGINAL_LOGIN()= 'login_test' AND
    (SELECT COUNT(*) FROM sys.dm_exec_sessions
            WHERE is_user_process = 1 AND
                original_login_name = 'login_test') > 3
    ROLLBACK;
END;

DROP TRIGGER safety
    ON DATABASE;

DROP TRIGGER employee_insupd;








































































































































