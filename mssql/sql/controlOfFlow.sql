IF OBJECT_ID(N'TestTran', N'U') IS NOT NULL

IF @@TRANCOUNT = 0
BEGIN
SELECT FirstName, MiddleName
FROM Person.Person
WHERE LastName = 'Adams';
ROLLBACK TRANSACTION;
PRINT N'Rolling back the transaction two times would cause an error.';
END;
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> TRANSACTION;
PRINT N'Rolled back the transaction.';
GO

DECLARE @Iteration INT = 0;
WHILE @Iteration < 10
BEGIN
SELECT FirstName,
       MiddleName
FROM dbo.DimCustomer
WHERE LastName = 'Adams';
SET @Iteration += 1;
END;
go



WHILE (1=1)
BEGIN
   IF EXISTS (SELECT * FROM ##MyTempTable WHERE EventCode = 'Done')
BEGIN
      BREAK;  -- 'Done' row has finally been inserted and detected, so end this loop.
END
   PRINT N'The other process is not yet done.';  -- Re-confirm the non-done status to the console.
   WAITFOR DELAY '00:01:30';  -- Sleep for 90 seconds.
E<PERSON>


DECLARE @sleeptimesec int = 1;
<PERSON><PERSON>AR<PERSON> @startingtime datetime2(2) = getdate();
PRINT N'Sleeping for ' + CAST(@sleeptimesec as varchar(5)) + ' seconds'
WHILE (1=1)
BEGIN

    PRINT N'Sleeping.';
    PRINT datediff(s, getdate(),  @startingtime)

    IF datediff(s, getdate(),  @startingtime) < -@sleeptimesec
BEGIN
            PRINT 'We have finished waiting.';
            BREAK;
END
END;
go



WHILE (
        SELECT AVG(ListPrice)
        FROM Production.Product
        ) < $300
BEGIN
UPDATE Production.Product
SET ListPrice = ListPrice * 2
SELECT MAX(ListPrice)
FROM Production.Product
         IF (
             SELECT MAX(ListPrice)
            FROM Production.Product
        ) > $500
        BREAK
    ELSE
        CONTINUE
END
PRINT 'Too much for the market to bear';
go


DECLARE @EmployeeID AS NVARCHAR(256)
DECLARE @Title AS NVARCHAR(50)
DECLARE Employee_Cursor CURSOR
FOR
SELECT LoginID,
       JobTitle
FROM AdventureWorks2022.HumanResources.Employee
WHERE JobTitle = 'Marketing Specialist';
OPEN Employee_Cursor;
FETCH NEXT
    FROM Employee_Cursor
    INTO @EmployeeID,
    @Title;
WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '   ' + @EmployeeID + '      ' + @Title

    FETCH NEXT
    FROM Employee_Cursor
    INTO @EmployeeID,
        @Title;
END;
CLOSE Employee_Cursor;
DEALLOCATE Employee_Cursor;
GO

WHILE (
        SELECT AVG(ListPrice)
        FROM dbo.DimProduct
        ) < $300
BEGIN
UPDATE dbo.DimProduct
SET ListPrice = ListPrice * 2;
SELECT MAX(ListPrice)
FROM dbo.DimProduct
         IF (
             SELECT MAX(ListPrice)
            FROM dbo.DimProduct
        ) > $500
        BREAK;
END


WHILE @Counter < 10
BEGIN
SELECT @Counter
    SET @Counter = @Counter + 1
    IF @Counter = 4 GOTO Branch_One --Jumps to the first branch.
    IF @Counter = 5 GOTO Branch_Two  --This will never execute.
END
Branch_One:
SELECT 'Jumping To Branch One.'
    GOTO Branch_Three; --This will prevent Branch_Two from executing.
Branch_Two:
SELECT 'Jumping To Branch Two.'
           Branch_Three:
    SELECT 'Jumping To Branch Three.';
go


DECLARE @return_status INT;
EXEC @return_status = checkstate '2';
SELECT 'Return Status' = @return_status;
GO

IF DATENAME(weekday, GETDATE()) IN (N'Saturday', N'Sunday')
SELECT 'Weekend';
ELSE
SELECT 'Weekday';


IF @maxWeight <= (
        SELECT Weight
        FROM DimProduct
        WHERE ProductKey = @productKey
    )
SELECT @productKey AS ProductKey,
       EnglishDescription,
       Weight,
       'This product is too heavy to ship and is only available for pickup.' AS ShippingStatus
FROM DimProduct
WHERE ProductKey = @productKey;
ELSE
SELECT @productKey AS ProductKey,
       EnglishDescription,
       Weight,
       'This product is available for shipping or pickup.' AS ShippingStatus
FROM DimProduct
WHERE ProductKey = @productKey;


BEGIN TRY
INSERT dbo.TestRethrow(ID) VALUES(1);
--  Force error 2627, Violation of PRIMARY KEY constraint to be raised.
    INSERT dbo.TestRethrow(ID) VALUES(1);
END TRY
BEGIN CATCH
PRINT 'In catch block.';
    THROW;
END CATCH;

DECLARE @msg NVARCHAR(2048) = FORMATMESSAGE(60000, 500, N'First string', N'second string');
THROW 60000, @msg, 1;


BEGIN TRY
SELECT 1 / 0;
END TRY
BEGIN CATCH
EXECUTE usp_GetErrorInfo;
END CATCH;

BEGIN TRY
SELECT *
FROM NonexistentTable;
END TRY
BEGIN CATCH
SELECT ERROR_NUMBER() AS ErrorNumber,
       ERROR_MESSAGE() AS ErrorMessage;
END CATCH

BEGIN TRY
EXECUTE usp_ExampleProc;
END TRY
BEGIN CATCH
SELECT ERROR_NUMBER() AS ErrorNumber,
       ERROR_MESSAGE() AS ErrorMessage;
END CATCH;


BEGIN TRY
SELECT 1 / 0;
END TRY
BEGIN CATCH
SELECT ERROR_NUMBER() AS ErrorNumber,
       ERROR_SEVERITY() AS ErrorSeverity,
       ERROR_STATE() AS ErrorState,
       ERROR_PROCEDURE() AS ErrorProcedure,
       ERROR_LINE() AS ErrorLine,
       ERROR_MESSAGE() AS ErrorMessage;
END CATCH;
GO

BEGIN TRANSACTION;
BEGIN TRY
DELETE
FROM Production.Product
WHERE ProductID = 980;
END TRY
BEGIN CATCH
SELECT ERROR_NUMBER() AS ErrorNumber,
       ERROR_SEVERITY() AS ErrorSeverity,
       ERROR_STATE() AS ErrorState,
       ERROR_PROCEDURE() AS ErrorProcedure,
       ERROR_LINE() AS ErrorLine,
       ERROR_MESSAGE() AS ErrorMessage;
IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION;
END CATCH;
IF @@TRANCOUNT > 0
    COMMIT TRANSACTION;
GO


IF OBJECT_ID(N'usp_GetErrorInfo', N'P') IS NOT NULL
DROP PROCEDURE usp_GetErrorInfo;

SET XACT_ABORT ON;
BEGIN TRY
BEGIN TRANSACTION;
DELETE
FROM Production.Product
WHERE ProductID = 980;
COMMIT TRANSACTION;
END TRY
BEGIN CATCH
EXECUTE usp_GetErrorInfo;
    IF (XACT_STATE()) = -1
BEGIN
        PRINT N'The transaction is in an uncommittable state. Rolling back transaction.'
        ROLLBACK TRANSACTION;
END;
    IF (XACT_STATE()) = 1
BEGIN
        PRINT N'The transaction is committable. Committing transaction.'
        COMMIT TRANSACTION;
END;
END CATCH;
GO

EXECUTE sp_add_job @job_name = 'TestJob';
BEGIN
    WAITFOR TIME '22:20';
EXECUTE sp_update_job @job_name = 'TestJob',
        @new_name = 'UpdatedJob';
END;
GO

BEGIN
    WAITFOR DELAY '02:00';
EXECUTE sp_helpdb;
END;
GO

WHILE (
        SELECT AVG(ListPrice)
        FROM Production.Product
        ) < $300
BEGIN
UPDATE Production.Product
SET ListPrice = ListPrice * 2
SELECT MAX(ListPrice)
FROM Production.Product
         IF (
             SELECT MAX(ListPrice)
            FROM Production.Product
        ) > $500
        BREAK
    ELSE
        CONTINUE
END



DECLARE @EmployeeID AS NVARCHAR(256)
DECLARE @Title AS NVARCHAR(50)
DECLARE Employee_Cursor CURSOR
FOR
SELECT LoginID,
       JobTitle
FROM AdventureWorks2022.HumanResources.Employee
WHERE JobTitle = 'Marketing Specialist';
OPEN Employee_Cursor;
FETCH NEXT
    FROM Employee_Cursor
    INTO @EmployeeID,
    @Title;
WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '   ' + @EmployeeID + '      ' + @Title

    FETCH NEXT
    FROM Employee_Cursor
    INTO @EmployeeID,
        @Title;
END;
CLOSE Employee_Cursor;
DEALLOCATE Employee_Cursor;
GO


WHILE (
        SELECT AVG(ListPrice)
        FROM dbo.DimProduct
        ) < $300
BEGIN
UPDATE dbo.DimProduct
SET ListPrice = ListPrice * 2;
SELECT MAX(ListPrice)
FROM dbo.DimProduct
         IF (
             SELECT MAX(ListPrice)
            FROM dbo.DimProduct
        ) > $500
        BREAK;
END













