CREATE STATISTICS ContactMail1
    ON Person.Person (BusinessEntityID, EmailPromotion)
    WITH SAMPLE 5 PERCENT;

CREATE STATISTICS NamePurchase
    ON AdventureWorks2022.Person.Person (BusinessEntityID, EmailPromotion)
    WITH FULLSCAN, NORECOMPUTE;

CREATE STATISTICS ContactPromotion1
    ON Person.Person (BusinessEntityID, LastName, EmailPromotion)
WHERE EmailPromotion = 2
WITH SAMPLE 50 PERCENT;
GO

CREATE STATISTICS CustomerStats1 ON DimCustomer (Customer<PERSON>ey, EmailAddress);

CREATE STATISTICS CustomerStats1 ON DimCustomer (Customer<PERSON><PERSON>, EmailAddress) WITH FULLSCAN;


CREATE STATISTICS NamePurchase
    ON AdventureWorks2022.Person.Person (BusinessEntityID, EmailPromotion)
    WITH FULLSCAN, PERSIST_SAMPLE_PERCENT = ON;


CREATE STATISTICS CustomerStats1 ON DimCustomer (Customer<PERSON>ey, EmailAddress);


CREATE STATISTICS CustomerStatsFullScan
ON DimCustomer (<PERSON>er<PERSON><PERSON>, <PERSON>ailAddress) WITH FULLSCAN;


CREATE STATISTICS CustomerStatsSampleScan
ON DimCustomer (CustomerKey, EmailAddress) WITH SAMPLE 50 PERCENT;

CREATE STATISTICS CustomerStats1 ON DimCustomer (CustomerKey, EmailAddress) WITH AUTO_DROP = ON

DROP STATISTICS dbo.Customer.CustomerStats1;
















































































