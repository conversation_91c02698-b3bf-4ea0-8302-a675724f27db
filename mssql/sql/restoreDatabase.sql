RESTORE DATABASE AdventureWorks2022_1
FROM URL = 's3://datavirtualizationsample.s3.amazonaws.com/backup/AdventureWorks2022.bak'
WITH
  MOVE 'AdventureWorks2022'
  TO 'C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\AdventureWorks2022_1.mdf'
, MOVE 'AdventureWorks2022_log'
  TO 'C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\AdventureWorks2022_1.ldf'
, STATS = 10, RECOVERY
, REPLACE, RESTORE_OPTIONS = '{"s3": {"region":"us-west-2"}}'; -- REGION AS PARAMETER)
GO

RESTORE DATABASE AdventureWorks2022
  FROM AdventureWorks2022Backups;

RESTORE DATABASE AdventureWorks2022
    FROM DISK = 'Z:\SQLServerBackups\AdventureWorks2022.bak'
    WITH FILE = 6,
      NORECOVERY;
RESTORE DATABASE AdventureWorks2022
    FROM DISK = 'Z:\SQLServerBackups\AdventureWorks2022.bak'
    WITH FILE = 9,
      RECOVERY;

-- This database RESTORE halted prematurely due to power failure.
RESTORE DATABASE AdventureWorks2022
    FROM AdventureWorksBackups;
-- Here is the RESTORE RESTART operation.
RESTORE DATABASE AdventureWorks2022
    FROM AdventureWorksBackups WITH RESTART;

RESTORE DATABASE AdventureWorks2022
    FROM AdventureWorksBackups
    WITH NORECOVERY,
      MOVE 'AdventureWorks2022_Data' TO
'C:\Program Files\Microsoft SQL Server\MSSQL13.MSSQLSERVER\MSSQL\Data\NewAdvWorks.mdf',
      MOVE 'AdventureWorks2022_Log'
TO 'C:\Program Files\Microsoft SQL Server\MSSQL13.MSSQLSERVER\MSSQL\Data\NewAdvWorks.ldf';


RESTORE DATABASE TestDB
    FROM AdventureWorksBackups
    WITH MOVE 'AdventureWorks2022_Data' TO 'C:\MySQLServer\testdb.mdf',
    MOVE 'AdventureWorks2022_Log' TO 'C:\MySQLServer\testdb.ldf';

RESTORE DATABASE AdventureWorks2022
    FROM AdventureWorksBackups
    WITH FILE = 3, NORECOVERY;

RESTORE DATABASE AdventureWorks2022 WITH RECOVERY;


RESTORE DATABASE AdventureWorks2022
FROM AdventureWorksBackups
WITH FILE = 3, NORECOVERY;

RESTORE DATABASE AdventureWorks2022
    FROM TAPE = '\\.\tape0';

RESTORE DATABASE MyDatabase
    FILE = 'MyDatabase_data_1',
    FILE = 'MyDatabase_data_2',
    FILEGROUP = 'new_customers'
    FROM MyDatabaseBackups
    WITH
      FILE = 9,
      NORECOVERY;

RESTORE DATABASE MyDatabase WITH RECOVERY;


RESTORE DATABASE AdventureWorks2022 FROM DATABASE_SNAPSHOT = 'AdventureWorks_dbss1800';

RESTORE DATABASE Sales
  FROM URL = 'https://mystorageaccount.blob.core.windows.net/mysecondcontainer/Sales.bak'
  WITH MOVE 'Sales_Data1' to 'https://mystorageaccount.blob.core.windows.net/myfirstcontainer/Sales_Data1.mdf',
  MOVE 'Sales_log' to 'https://mystorageaccount.blob.core.windows.net/myfirstcontainer/Sales_log.ldf',
  STATS = 10;

RESTORE DATABASE Sales
  FROM URL = 'https://mystorageaccount.blob.core.windows.net/mysecondcontainer/Sales.bak'
  WITH MOVE 'Sales_Data1' to 'H:\DATA\Sales_Data1.mdf',
  MOVE 'Sales_log' to 'O:\LOG\Sales_log.ldf',
  STATS = 10;

RESTORE DATABASE Sales
  FROM DISK = 'E:\BAK\Sales.bak'
  WITH MOVE 'Sales_Data1' to 'https://mystorageaccount.blob.core.windows.net/myfirstcontainer/Sales_Data1.mdf',
  MOVE 'Sales_log' to 'https://mystorageaccount.blob.core.windows.net/myfirstcontainer/Sales_log.ldf',
  STATS = 10;

RESTORE DATABASE Sales
  FROM DISK = 'D:\MSSQL\Backup\SalesSnapshotFull.bkm'
  WITH METADATA_ONLY;

RESTORE DATABASE Sales
  FROM DISK = 'D:\MSSQL\Backup\SalesSnapshotFull.bkm'
  WITH METADATA_ONLY,
  NORECOVERY;

RESTORE DATABASE Sales
FROM DISK = 'D:\MSSQL\Backup\SalesSnapshotFull.bkm'
WITH METADATA_ONLY,
    MOVE Sales_Data TO 'D:\MSSQL\Sales.mdf',
    MOVE Sales_Log TO 'D:\MSSQL\Sales_log.ldf';














