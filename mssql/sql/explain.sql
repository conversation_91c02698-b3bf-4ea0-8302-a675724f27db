-- Uses AdventureWorks

EXPLAIN
SELECT CAST (AVG(YearlyIncome) AS int) AS AverageIncome,
       CAST(AVG(FIS.SalesAmount) AS int) AS AverageSales,
       G.StateProvinceName, T.SalesTerritoryGroup
FROM dbo.DimGeography AS G
         JOIN dbo.DimSalesTerritory AS T
              ON G.SalesTerritoryKey = T.SalesTerritoryKey
         JOIN dbo.DimCustomer AS C
              ON G.GeographyKey = C.GeographyKey
         JOIN dbo.FactInternetSales AS FIS
              ON C.CustomerKey = FIS.CustomerKey
WHERE T.SalesTerritoryGroup IN ('North America', 'Pacific')
  AND Gender = 'F'
GROUP BY G.StateProvinceName, T.SalesTerritoryGroup
ORDER BY AVG(YearlyIncome) DESC;
GO

EXPLAIN WITH_RECOMMENDATIONS
select count(*)
from ((select distinct c_last_name, c_first_name, d_date
       from store_sales, date_dim, customer
       where store_sales.ss_sold_date_sk = date_dim.d_date_sk
         and store_sales.ss_customer_sk = customer.c_customer_sk
         and d_month_seq between 1194 and 1194+11)
      except
      (select distinct c_last_name, c_first_name, d_date
       from catalog_sales, date_dim, customer
       where catalog_sales.cs_sold_date_sk = date_dim.d_date_sk
         and catalog_sales.cs_bill_customer_sk = customer.c_customer_sk
         and d_month_seq between 1194 and 1194+11)
     ) top_customers















































