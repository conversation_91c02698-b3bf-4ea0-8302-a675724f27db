ALTER QUEUE ExpenseQueue WITH STATUS = OFF ;

ALTER QUEUE ExpenseQueue
    WITH ACTIVATION (
        PROCEDURE_NAME = new_stored_proc,
        EXECUTE AS SELF) ;

ALTER QUEUE ExpenseQueue WITH ACTIVATION (MAX_QUEUE_READERS = 7) ;

ALTER QUEUE ExpenseQueue
    WITH ACTIVATION (
        PROCEDURE_NAME = AdventureWorks2022.dbo.new_stored_proc ,
        EXECUTE AS 'SecurityAccount') ;

ALTER QUEUE ExpenseQueue WITH RETENTION = ON ;

ALTER QUEUE ExpenseQueue WITH ACTIVATION (DROP) ;

ALTER QUEUE ExpenseQueue REBUILD WITH (MAXDOP = 2)

ALTER QUEUE ExpenseQueue REORGANIZE

ALTER QUEUE ExpenseQueue MOVE TO [NewFilegroup]

CREATE QUEUE ExpenseQueue;


CREATE QUEUE ExpenseQueue WITH STATUS=OFF;


CREATE QUEUE ExpenseQueue
    WITH STATUS=ON,
    ACTIVATION (
        PROCEDURE_NAME = expense_procedure
        , MAX_QUEUE_READERS = 5
        , EXECUTE AS 'ExpenseUser' );



CREATE QUEUE ExpenseQueue
    ON ExpenseWorkFileGroup;

CREATE QUEUE ExpenseQueue
    WITH STATUS = OFF
      , RETENTION = ON
      , ACTIVATION (
          PROCEDURE_NAME = AdventureWorks2022.dbo.expense_procedure
          , MAX_QUEUE_READERS = 10
          , EXECUTE AS SELF )
    ON [DEFAULT];


DROP QUEUE ExpenseQueue ;
























































































































