
CREATE PROCEDURE dbo.usp_Demo
WITH EXECUTE AS 'CompanyDomain\SqlUser1'
    AS
SELECT USER_NAME();

CREATE PROCEDURE dbo.usp_Demo
WITH EXECUTE AS 'SqlUser1'
    AS
SELECT USER_NAME(); -- Shows execution context is set to SqlUser1.
EXECUTE AS CALLER;
SELECT USER_NAME(); -- Shows execution context is set to <PERSON>q<PERSON><PERSON><PERSON><PERSON>, the caller of the module.
REVERT;
SELECT USER_NAME(); -- Shows execution context is set to SqlUser1.
GO

CREATE PROCEDURE HumanResources.uspEmployeesInDepartment @DeptValue INT
    WITH EXECUTE AS OWNER
AS
SET NOCOUNT ON;

SELECT e.BusinessEntityID,
       c.LastName,
       c.FirstName,
       e.JobTitle
FROM Person.Person AS c
         INNER JOIN HumanResources.Employee AS e
                    ON c.BusinessEntityID = e.BusinessEntityID
         INNER JOIN HumanResources.EmployeeDepartmentHistory AS edh
                    ON e.BusinessEntityID = edh.BusinessEntityID
WHERE edh.DepartmentID = @DeptValue
ORDER BY c.LastName,
         c.FirstName;
GO


EXECUTE AS USER = 'user2';

EXECUTE AS USER = 'user1' WITH COOKIE INTO @cookie;









































