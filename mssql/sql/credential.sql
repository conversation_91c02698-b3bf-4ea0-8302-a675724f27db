ALTER CREDENTIAL Saddles WITH IDENTITY = 'RettigB',
    SECRET = 'sdrlk8$40-dksli87nNN8';

ALTER CREDENTIAL Frames WITH IDENTITY = 'Aboulrus8';

DROP CREDENTIAL Saddles;






























CREATE CREDENTIAL AlterEgo WITH IDENTITY = 'Mary5',
    SECRET = '<EnterStrongPasswordHere>';
GO

CREATE CREDENTIAL CredentialForEKM
    WITH IDENTITY='User1OnEKM', SECRET='<EnterStrongPasswordHere>'
    FOR CRYPTOGRAPHIC PROVIDER MyEKMProvider;


CREATE CREDENTIAL Azure_EKM_TDE_cred
    WITH IDENTITY = 'ContosoKeyVault',
    SECRET = '11111111222233334444555555555555SECRET_DBEngine'
    FOR CRYPTOGRAPHIC PROVIDER AzureKeyVault_EKM_Prov ;

CREATE CREDENTIAL ServiceIdentity WITH IDENTITY = 'Managed Identity';
GO

CREATE CREDENTIAL [s3://<endpoint>:<port>/<bucket>]
WITH
        IDENTITY    = 'S3 Access Key',
        SECRET      = '<AccessKeyID>:<SecretKeyID>';

CREATE CREDENTIAL [s3://datavirtualizationsample.s3.us-west-2.amazonaws.com/backup]
WITH
        IDENTITY    = 'S3 Access Key'
,       SECRET      = 'accesskey:secretkey';


CREATE CREDENTIAL [s3://s3.us-west-2.amazonaws.com/datavirtualizationsample/backup]
WITH
        IDENTITY    = 'S3 Access Key'
,       SECRET      = 'accesskey:secretkey';

CREATE CREDENTIAL [https://<mystorageaccountname>.blob.core.windows.net/<container-name>]
    WITH IDENTITY = 'Managed Identity';

CREATE CREDENTIAL [<akv-name>.vault.azure.net]
    WITH IDENTITY = 'Managed Identity'
    FOR CRYPTOGRAPHIC PROVIDER AzureKeyVault_EKM_Prov





























