ALTER EXTERNAL LIBRARY customPackage
SET
  (CONTENT = 'C:\Program Files\Microsoft SQL Server\MSSQL14.MSSQLSERVER\customPackage.zip')
WITH (LANGUAGE = 'R');

ALTER EXTERNAL LIBRARY customLibrary
SET (CONTENT = 0xABC123) WITH (LANGUAGE = 'R');


CREATE EXTERNAL LIBRARY customPackage
FROM (CONTENT = 'C:\Program Files\Microsoft SQL Server\MSSQL14.MSSQLSERVER\customPackage.zip') WITH (LANGUAGE = 'R');

CREATE EXTERNAL LIBRARY packageA
FROM (CONTENT = 'C:\Program Files\Microsoft SQL Server\MSSQL14.MSSQLSERVER\packageA.zip')
WITH (LANGUAGE = 'R');


CREATE EXTERNAL LIBRARY packageB FROM (CONTENT = 'C:\Program Files\Microsoft SQL Server\MSSQL14.MSSQLSERVER\packageB.zip')
WITH (LANGUAGE = 'R');

CREATE EXTERNAL LIBRARY customLibrary FROM (CONTENT = 0xABC123) WITH (LANGUAGE = 'R');

CREATE EXTERNAL LIBRARY customJar
FROM (CONTENT = 'C:\Program Files\Microsoft SQL Server\MSSQL15.MSSQLSERVER\customJar.jar')
WITH (LANGUAGE = 'Java');

CREATE EXTERNAL LIBRARY lazyeval
FROM (CONTENT = 'C:\Program Files\Microsoft SQL Server\MSSQL15.MSSQLSERVER\packageA.zip', PLATFORM = WINDOWS),
(CONTENT = 'C:\Program Files\Microsoft SQL Server\MSSQL15.MSSQLSERVER\packageA.tar.gz', PLATFORM = LINUX)
WITH (LANGUAGE = 'R')

DROP EXTERNAL LIBRARY customPackage;








































































































































