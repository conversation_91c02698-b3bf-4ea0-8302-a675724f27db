ALTER USER Mary5 WITH NAME = Mary51;

ALTER USER Mary51 WITH DEFAULT_SCHEMA = Purchasing;

ALTER USER Philip
WITH NAME = Philipe
, DEFAULT_SCHEMA = Development
, PASSWORD = '<new strong password here>' OLD_PASSWORD = '<old strong password here>'
, DEFAULT_LANGUAGE= French ;

ALTER USER Mai
WITH LOGIN = Mai;

CREATE LOGIN AbolrousHazem
    WITH PASSWORD = '340$Uuxwp7Mcxo7Khy';

CREATE USER AbolrousHazem FOR LOGIN AbolrousHazem;

CREATE USER Wanida FOR LOGIN WanidaBenshoof
    WITH DEFAULT_SCHEMA = Marketing;


CREATE USER JinghaoLiu FOR CERTIFICATE CarnationProduction50;

CREATE USER CustomApp WITHOUT LOGIN ;

CREATE USER Carlo
WITH PASSWORD='RN92piTCh%$!~3K9844 Bl*'
    , DEFAULT_LANGUAGE=[Brazilian]
    , DEFAULT_SCHEMA=[dbo]


CREATE USER [Contoso\Fritz] ;

CREATE USER CarmenW WITH PASSWORD = 'a8ea v*(Rd##+'
, SID = 0x01050000000000090300000063FF0451A9E7664BA705B10E37DDC4B7;

CREATE USER [Chin]
WITH
      DEFAULT_SCHEMA = dbo
    , ALLOW_ENCRYPTED_VALUE_MODIFICATIONS = ON ;

CREATE USER [<EMAIL>] FROM LOGIN [<EMAIL>];

CREATE USER [MS Entra group] FROM LOGIN [MS Entra group];

CREATE USER [<EMAIL>] FROM EXTERNAL PROVIDER;

DROP USER AbolrousHazem;




























































































































