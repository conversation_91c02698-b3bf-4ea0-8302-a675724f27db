BEGIN TRANSACTION;
DELETE FROM HumanResources.JobCandidate
WHERE JobCandidateID = 13;
COMMIT;
go


CREATE TABLE ValueTable (id INT);
BEGIN TRANSACTION;
INSERT INTO ValueTable VALUES(1);
INSERT INTO ValueTable VALUES(2);
ROL<PERSON><PERSON>CK;
go


DECLARE @TranName VARCHAR(20);
SELECT @TranName = 'MyTransaction';

BEGIN TRANSACTION @TranName;
USE AdventureWorks2022;
DELETE FROM AdventureWorks2022.HumanResources.JobCandidate
WHERE JobCandidateID = 13;

COMMIT TRANSACTION @TranName;
GO

BEGIN TRANSACTION CandidateDelete
    WITH MARK N'Deleting a Job Candidate';









































