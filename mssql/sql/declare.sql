DECLARE @myvar1 BIGINT = NEXT VALUE FOR Test.CountBy1
DECLARE @myvar2 BIGINT ;

DECLARE @find VARCHAR(30);


DECLARE @Group nvarchar(50), @Sales MONEY;

DECLARE @MyTableVar TABLE (
    EmpID INT NOT NULL,
    OldVacationHours INT,
    NewVacationHours INT,
    ModifiedDate DATETIME);

DECLARE @MyTableVar TABLE (
    EmpID INT NOT NULL,
    PRIMARY KEY CLUSTERED (EmpID),
    UNIQUE NONCLUSTERED (EmpID),
    INDEX CustomNonClusteredIndex NONCLUSTERED (EmpID)
);

DECLARE @LocationTVP
AS LocationTableType;


DECLARE @find VARCHAR(30);

DECLARE @lastName VARCHAR(30), @firstName VARCHAR(30);

DECLARE vend_cursor CURSOR
    FOR SELECT * FROM Purchasing.Vendor
    OPEN vend_cursor
FETCH NEXT FROM vend_cursor;


DECLARE @vendor_id INT, @vendor_name NVARCHAR(50),
    @message VARCHAR(80), @product NVARCHAR(50);

DECLARE vendor_cursor CURSOR FOR
SELECT VendorID, Name
FROM Purchasing.Vendor
WHERE PreferredVendorStatus = 1
ORDER BY VendorID;

DECLARE product_cursor CURSOR FOR
SELECT v.Name
FROM Purchasing.ProductVendor pv, Production.Product v
WHERE pv.ProductID = v.ProductID AND
    pv.VendorID = @vendor_id



























