parser grammar PlSqlBlockParser;

options {
    tokenVocab = PlSqlBlockLexer;
}

root
    : (createPackage | createPackageBody | createType | createTypeBody) SEMI_? SLASH_? EOF
    ;

createPackageBody
    : CREATE (OR REPLACE)? (EDITIONABLE | NONEDITIONABLE)? PACKAGE BODY plsqlPackageBodySource
    ;
plsqlPackageBodySource
    : (schemaName '.')? name sharingClause? (IS | AS) declareSection initializeSection? END name?
    ;

initializeSection
    : BEGIN statementList (EXCEPTION exceptionHandler+)?
    ;


createPackage
    : CREATE (OR REPLACE)? (EDITIONABLE | NONEDITIONABLE)? PACKAGE plsqlPackageSource
    ;
plsqlPackageSource
    : (schemaName '.')? name (~(IS | AS))*? (IS | AS) declareSection END name?
    ;

packageItemList
    :  declareSection
    ;

packageFunctionDeclaration
    : functionHeading (~SEMI_)*? SEMI_
    ;

packageProcedureDeclaration
    : procedureHeading (~SEMI_)*? SEMI_
    ;


sharingClause
    : SHARING EQ_ (METADATA | NONE)
    ;
exceptionHandler
    : WHEN ((typeName (OR typeName)*)| OTHERS) THEN statementList
    ;
declareSection
    : declareItem+
    ;

declareItem
    : typeDefinition
    | cursorDeclaration
    | itemDeclaration
    | functionDeclaration
    | procedureDeclaration
    | cursorDefinition
    | functionDefinition
    | procedureDefinition
    | pragma
    ;

typeDefinition
    : otherTypeDefinition
    ;
//    type 通配规则
otherTypeDefinition
    : (TYPE | SUBTYPE) (typeName | dataType) (AS | IS) (~SEMI_)*? SEMI_
    ;

cursorDeclaration
    : CURSOR variableName cursorParameterList? RETURN rowtype SEMI_
    ;

cursorParameterList
    : LP_ cursorParameter (COMMA_ cursorParameter)* RP_
    ;
cursorParameter
    : variableName .*?
    ;

rowtype
    : typeName MOD_ ROWTYPE
    | typeName (MOD_ TYPE)?
    ;


itemDeclaration
    : constantDeclaration | exceptionDeclaration | cursorVariableDeclaration
    ;

constantDeclaration
    : variableName CONSTANT (~SEMI_)*? SEMI_
    ;

cursorVariableDeclaration
    : variableName dataType (~SEMI_)*? SEMI_
    ;

exceptionDeclaration
    : variableName EXCEPTION SEMI_
    ;


functionDeclaration
    : functionHeading ((DETERMINISTIC | PIPELINED | PARALLEL_ENABLE | RESULT_CACHE)+)? SEMI_
    ;

functionHeading
    : FUNCTION funName parameterDeclarationList? RETURN dataType
    ;
// 系统包中的函数名有单引号包裹的字符串 STANDARD
funName
    : (owner DOT_)? name | STRING_
    ;

parameterDeclarationList
    : LP_ (parameterDeclaration (COMMA_ parameterDeclaration)*)? RP_
    ;

parameterDeclaration
    : parameterName ~(COMMA_ | RP_)*?
    ;

parameterName
    : identifier
    ;

procedureDeclaration
    : procedureHeading (~(AS|IS|SEMI_))*? SEMI_
    ;

procedureHeading
    : PROCEDURE procedureName parameterDeclarationList?
    ;

procedureName
    : (owner DOT_)? name
    ;


cursorDefinition
    : CURSOR variableName cursorParameterList? (RETURN rowtype)? IS (~SEMI_)*? SEMI_
    ;
functionDefinition
    : functionHeading (~(AS|IS|SEMI_))*?  (IS | AS) (declareSection? body | callSpec)
    ;
procedureDefinition
    : procedureHeading (~(AS|IS|SEMI_))*?  (IS | AS) (callSpec | declareSection? body)
    ;
body
    : BEGIN statementList END (identifier)? SEMI_
    ;

if
    : IF statementList END IF SEMI_
    ;

loop
    : LOOP statementList END LOOP label? SEMI_
    ;

case
    : CASE statementList END CASE label? SEMI_
    ;
statementList
    : statement+
    ;

statement
    : body | if | loop | case | POSITION FROM END | END BACKUP | ~END
    ;

label
    : identifier
    ;
callSpec
    : javaDeclaration | cDeclaration
    ;

javaDeclaration
    : LANGUAGE JAVA NAME STRING_
    ;

cDeclaration
    : (LANGUAGE SINGLE_C | EXTERNAL)
    ((NAME name)? LIBRARY libName | LIBRARY libName (NAME name)?)
    (AGENT IN RP_ argument (COMMA_ argument)* LP_)?
    (WITH CONTEXT)?
    (PARAMETERS LP_ (~RP_)*? RP_)?
    ;

argument
    : identifier
    ;


libName
    : identifier
    ;

pragma
    : otherPragma // 适配文档中不存在的 pragma 语法
    ;

otherPragma
    : PRAGMA (~SEMI_)*? SEMI_
    ;



dataType
    : characterSetDataType
    | intervalDatatype
    | typeAttribute
    | rowtypeAttribute
    | specialDatatype
    | dataTypeName dataTypeLength? datetimeTypeSuffix?
    | customDataType
    ;

dataTypeLength
//    : LP_ ((INTEGER_ | ASTERISK_) (COMMA_ (MINUS_)? INTEGER_)? (CHAR | BYTE)?)? RP_
    : LP_ ~(RP_)*? RP_
//    : LP_ INTEGER_ (COMMA_ INTEGER_)? RP_
    ;
characterSetDataType
    : dataTypeName CHARACTER SET characterSetName ('%' CHARSET)?
    ;
customDataType
    : (dataTypeName DOT_ | REF)? identifier
    ;
specialDatatype
    : NATIONAL dataTypeName VARYING? dataTypeLength
    | SYS DOT_ dataTypeName
    ;
intervalDatatype
    : INTERVAL DAY (LP_ numberValue RP_)? TO SECOND (LP_ numberValue RP_)?
    | INTERVAL YEAR (LP_ numberValue RP_)? TO MONTH
    ;
dataTypeName
    : CHARACTER | CHAR | NCHAR | RAW | VARCHAR | VARCHAR2 | NVARCHAR2 | LONG | LONG RAW | BLOB | CLOB | NCLOB | BINARY_FLOAT | BINARY_DOUBLE
    | BOOLEAN | PLS_INTEGER | BINARY_INTEGER | INTEGER | NUMBER | NATURAL | NATURALN | POSITIVE | POSITIVEN | SIGNTYPE
    | SIMPLE_INTEGER | BFILE | MLSLABEL | UROWID | DATE | TIMESTAMP | TIMESTAMP WITH TIME ZONE | TIMESTAMP WITH LOCAL TIME ZONE
    | INTERVAL DAY TO SECOND | INTERVAL YEAR TO MONTH | JSON | FLOAT | REAL | DOUBLE PRECISION | INT | SMALLINT | INTEGER DETERMINISTIC
    | DECIMAL | NUMERIC | DEC | IDENTIFIER_ | XMLTYPE | ROWID | ANYDATA | ANYTYPE | ANYDATASET | UB2 | SB4 | TIME
    ;

datetimeTypeSuffix
    : WITH LOCAL? TIME ZONE | TO MONTH | TO SECOND (LP_ numberValue RP_)?
    ;

typeAttribute
    : objectName MOD_ TYPE
    ;

rowtypeAttribute
    : objectName MOD_ ROWTYPE
    ;
characterSetName
    : identifier
    ;
numberValue
    : INTEGER_ | NUMBER_
    ;

objectName
    : (owner DOT_)? name
    ;


createType
    : CREATE (OR REPLACE)? (EDITIONABLE | NONEDITIONABLE)? TYPE plsqlTypeSource
    ;

plsqlTypeSource
    : typeName FORCE? (OID objectIdentifier=stringLiterals)? sharingClause? .*? (objectBaseTypeDef | objectSubTypeDef)
    ;

objectBaseTypeDef
    : (IS | AS) (objectTypeDef | varrayTypeSpec | nestedTableTypeSpec)
    ;

objectTypeDef
    : OBJECT (LP_ typeDefinitionItem (COMMA_ typeDefinitionItem)* RP_)? (finalClause | instantiableClause | persistableClause)*
    ;

finalClause
    : NOT? FINAL
    ;

instantiableClause
    : NOT? INSTANTIABLE
    ;

persistableClause
    : NOT? PERSISTABLE
    ;

varrayTypeSpec
    : (VARYING ARRAY | VARRAY) (LP_ INTEGER_ RP_)? OF typeSpec
    ;

nestedTableTypeSpec
    : TABLE OF typeSpec
    ;

typeSpec
    : ((LP_ dataType RP_) | dataType) (NOT NULL)? persistableClause?
    ;

dataTypeDefinition
    : name dataType
    ;

objectSubTypeDef
    : UNDER typeName (LP_ typeDefinitionItem (COMMA_ typeDefinitionItem)* RP_)? (finalClause | instantiableClause)?
    ;

typeDefinitionItem
    : dataTypeDefinition | elementSpecification
    ;
elementSpecification
    : inheritanceClauses? specificationClause+ (COMMA_ restrictReferencesPragma)?
    ;
restrictReferencesPragma
    : PRAGMA RESTRICT_REFERENCES
    LP_ (identifier | DEFAULT) COMMA_
    (RNDS | WNDS | RNPS | WNPS | TRUST)
    (COMMA_ (RNDS | WNDS | RNPS | WNPS | TRUST))* RP_
    ;

inheritanceClauses
    : (NOT? (OVERRIDING | FINAL | INSTANTIABLE))+
    ;

specificationClause
    : subprogramSpec | constructorSpec | mapOrderFunctionSpec
    ;
subprogramSpec
    : (MEMBER | STATIC) (procedureSpec | functionSpec)
    ;

constructorSpec
    : FINAL? INSTANTIABLE? CONSTRUCTOR FUNCTION typeName
    (LP_ (SELF IN OUT dataType COMMA_)? parameterDeclaration (COMMA_ parameterDeclaration)* RP_)?
    RETURN SELF AS RESULT ((AS | IS) callSpec)?
    ;

mapOrderFunctionSpec
    : (MAP | ORDER) MEMBER functionSpec
    ;
functionSpec
    : FUNCTION name parameterDeclarationList? returnClause
    ;

returnClause
    : RETURN dataType ((IS | AS) callSpec)?
    ;
procedureSpec
    : PROCEDURE name parameterDeclarationList? ((IS | AS) callSpec)?
    ;



createTypeBody
    : CREATE (OR REPLACE)? (EDITIONABLE | NONEDITIONABLE)? TYPE BODY plsqlTypeBodySource
    ;

plsqlTypeBodySource
    : typeName sharingClause? (IS | AS) typeBodyDecl (COMMA_? typeBodyDecl)* END
    ;

typeBodyDecl
    : subprogDeclInType | mapOrderFuncDeclaration
    ;

subprogDeclInType
    : procDeclInType | funcDeclInType | constructorDeclaration
    ;

procDeclInType
    : MEMBER? PROCEDURE name parameterDeclarationList? (IS | AS) (declareSection? body | callSpec)
    ;

funcDeclInType
    : MEMBER? FUNCTION name parameterDeclarationList? RETURN dataType
    .*?
    PIPELINED? (IS | AS) (callSpec | declareSection? body)
    ;
constructorDeclaration
    : FINAL? INSTANTIABLE? CONSTRUCTOR FUNCTION typeName
    (LP_ (SELF IN OUT dataType COMMA_)? parameterDeclaration (COMMA_ parameterDeclaration)* RP_)?
    RETURN SELF AS RESULT (AS | IS) (callSpec | declareSection? body)
    ;

mapOrderFuncDeclaration
    : (MAP | ORDER) funcDeclInType
    ;




// TODO
variableName
    : (owner DOT_)? name | stringLiterals
    ;

schemaName
    : identifier
    ;

typeName
    : (owner DOT_)? name
    ;

owner
    : identifier
    ;

name
    : identifier
    ;


stringLiterals
    : STRING_
    | NCHAR_TEXT
    | UCHAR_TEXT
    ;

any
    : identifier | stringLiterals | numberValue
    ;

anyNotSemi
    : any | ~SEMI_
    ;

identifier
    : IDENTIFIER_ | DOUBLE_QUOTED_TEXT | I_CURSOR | QUESTION_ | unreservedKeywords
    ;

unreservedKeywords
    : AGENT
    |  ANYDATA
    |  ANYDATASET
    |  ANYTYPE
    |  ARRAY
    |  BFILE
    |  BINARY_DOUBLE
    |  BINARY_FLOAT
    |  BINARY_INTEGER
    |  CONSTRUCTOR
    |  CONTEXT
    |  DATE
    |  DAY
    |  DEC
    |  DECIMAL
    |  DEFAULT
    |  DETERMINISTIC
    |  DOUBLE
    |  EDITIONABLE
    |  EXCEPTION
    |  EXTERNAL
    |  FINAL
    |  FLOAT
    |  FORCE
    |  IN
    |  INSTANTIABLE
    |  INT
    |  INTEGER
    |  INTERVAL
    |  JAVA
    |  JSON
    |  LANGUAGE
    |  LIBRARY
    |  LOCAL
    |  LONG
    |  MAP
    |  MEMBER
    |  METADATA
    |  MLSLABEL
    |  MONTH
    |  NAME
    |  NATIONAL
    |  NATURAL
    |  NATURALN
    |  NCHAR
    |  NCLOB
    |  NOCOPY
    |  NONE
    |  NONEDITIONABLE
    |  NOT
    |  NULL
    |  NUMBER
    |  NUMERIC
    |  NVARCHAR2
    |  OBJECT
    |  OF
    |  OID
    |  ORDER
    |  OTHERS
    |  OUT
    |  OVERRIDING
    |  PARALLEL_ENABLE
    |  PARAMETERS
    |  PERSISTABLE
    |  PIPELINED
    |  PLS_INTEGER
    |  POSITIVE
    |  POSITIVEN
    |  PRECISION
    |  RAW
    |  REAL
    |  REF
    |  RESTRICT_REFERENCES
    |  RESULT
    |  RESULT_CACHE
    |  RNDS
    |  RNPS
    |  ROWID
    |  ROWTYPE
    |  SB4
    |  SECOND
    |  SELF
    |  SET
    |  SHARING
    |  SIGNTYPE
    |  SIMPLE_INTEGER
    |  SINGLE_C
    |  SMALLINT
    |  SYS
    |  TIME
    |  TIMESTAMP
    |  TO
    |  TRUST
    |  UB2
    |  UNDER
    |  UROWID
    |  VARCHAR
    |  VARCHAR2
    |  VARRAY
    |  VARYING
    |  WITH
    |  WNDS
    |  WNPS
    |  XMLTYPE
    |  YEAR
    |  ZONE
    | POSITION
    | BACKUP
    | REPLACE
    ;
