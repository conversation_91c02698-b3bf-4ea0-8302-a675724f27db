lexer grammar PlSqlBlockLexer;

options {
    caseInsensitive = true;
}

AGENT               : 'AGENT';
ANYDATA             : 'ANYDATA';
ANYDATASET          : 'ANYDATASET';
ANYTYPE             : 'ANYTYPE';
ARRAY               : 'ARRAY';
AS                  : 'AS';
BEGIN               : 'BEGIN';
BFILE               : 'BFILE';
BINARY_DOUBLE       : 'BINARY_DOUBLE';
BINARY_FLOAT        : 'BINARY_FLOAT';
BINARY_INTEGER      : 'BINARY_INTEGER';
BLOB                : 'BLOB';
BODY                : 'BODY';
BOOLEAN             : 'BOOLEAN';
BYTE                : 'BYTE';
CHAR                : 'CHAR';
CHARACTER           : 'CHARACTER';
CHARSET             : 'CHARSET';
CLOB                : 'CLOB';
CONSTANT            : 'CONSTANT';
CONSTRUCTOR         : 'CONSTRUCTOR';
CONTEXT             : 'CONTEXT';
CREATE              : 'CREATE';
CURSOR              : 'CURSOR';
DATE                : 'DATE';
DAY                 : 'DAY';
DEC                 : 'DEC';
DECIMAL             : 'DECIMAL';
DEFAULT             : 'DEFAULT';
DETERMINISTIC       : 'DETERMINISTIC';
DOUBLE              : 'DOUBLE';
EDITIONABLE         : 'EDITIONABLE';
END                 : 'END';
EXCEPTION           : 'EXCEPTION';
EXTERNAL            : 'EXTERNAL';
FINAL               : 'FINAL';
FLOAT               : 'FLOAT';
FORCE               : 'FORCE';
FUNCTION            : 'FUNCTION';
IF                  : 'IF';
IN                  : 'IN';
INSTANTIABLE        : 'INSTANTIABLE';
INT                 : 'INT';
INTEGER             : 'INTEGER';
INTERVAL            : 'INTERVAL';
IS                  : 'IS';
JAVA                : 'JAVA';
JSON                : 'JSON';
LANGUAGE            : 'LANGUAGE';
LIBRARY             : 'LIBRARY';
LOCAL               : 'LOCAL';
LONG                : 'LONG';
LOOP                : 'LOOP';
MAP                 : 'MAP';
MEMBER              : 'MEMBER';
METADATA            : 'METADATA';
MLSLABEL            : 'MLSLABEL';
MONTH               : 'MONTH';
NAME                : 'NAME';
NATIONAL            : 'NATIONAL';
NATURAL             : 'NATURAL';
NATURALN            : 'NATURALN';
NCHAR               : 'NCHAR';
NCLOB               : 'NCLOB';
NOCOPY              : 'NOCOPY';
NONE                : 'NONE';
NONEDITIONABLE      : 'NONEDITIONABLE';
NOT                 : 'NOT';
NULL                : 'NULL';
NUMBER              : 'NUMBER';
NUMERIC             : 'NUMERIC';
NVARCHAR2           : 'NVARCHAR2';
OBJECT              : 'OBJECT';
OF                  : 'OF';
OID                 : 'OID';
OR                  : 'OR';
ORDER               : 'ORDER';
OTHERS              : 'OTHERS';
OUT                 : 'OUT';
OVERRIDING          : 'OVERRIDING';
PACKAGE             : 'PACKAGE';
PARALLEL_ENABLE     : 'PARALLEL_ENABLE';
PARAMETERS          : 'PARAMETERS';
PERSISTABLE         : 'PERSISTABLE';
PIPELINED           : 'PIPELINED';
PLS_INTEGER         : 'PLS_INTEGER';
POSITIVE            : 'POSITIVE';
POSITIVEN           : 'POSITIVEN';
PRAGMA              : 'PRAGMA';
PRECISION           : 'PRECISION';
PROCEDURE           : 'PROCEDURE';
RAW                 : 'RAW';
REAL                : 'REAL';
REF                 : 'REF';
REPLACE             : 'REPLACE';
RESTRICT_REFERENCES : 'RESTRICT_REFERENCES';
RESULT              : 'RESULT';
RESULT_CACHE        : 'RESULT_CACHE';
RETURN              : 'RETURN';
RNDS                : 'RNDS';
RNPS                : 'RNPS';
ROWID               : 'ROWID';
ROWTYPE             : 'ROWTYPE';
SB4                 : 'SB4';
SECOND              : 'SECOND';
SELF                : 'SELF';
SET                 : 'SET';
SHARING             : 'SHARING';
SIGNTYPE            : 'SIGNTYPE';
SIMPLE_INTEGER      : 'SIMPLE_INTEGER';
SINGLE_C            : 'SINGLE_C';
SMALLINT            : 'SMALLINT';
STATIC              : 'STATIC';
SUBTYPE             : 'SUBTYPE';
SYS                 : 'SYS';
TABLE               : 'TABLE';
THEN                : 'THEN';
TIME                : 'TIME';
TIMESTAMP           : 'TIMESTAMP';
TO                  : 'TO';
TRUST               : 'TRUST';
TYPE                : 'TYPE';
UB2                 : 'UB2';
UNDER               : 'UNDER';
UROWID              : 'UROWID';
VARCHAR             : 'VARCHAR';
VARCHAR2            : 'VARCHAR2';
VARRAY              : 'VARRAY';
VARYING             : 'VARYING';
WHEN                : 'WHEN';
WITH                : 'WITH';
WNDS                : 'WNDS';
WNPS                : 'WNPS';
XMLTYPE             : 'XMLTYPE';
YEAR                : 'YEAR';
ZONE                : 'ZONE';
CASE                :'CASE';
POSITION            :'POSITION';
FROM                :'FROM';
BACKUP              :'BACKUP';

































//AMPERSAND_           : '&';
//AND_                 : '&&';
//ARROW_               : '=>';
//ASSIGNMENT_OPERATOR_ : ':=';
ASTERISK_            : '*';
//AT_                  : '@';
//BACKSLASH_           : '\\';
//BQ_                  : '`';
//CARET_               : '^';
//COLON_               : ':';
COMMA_               : ',';
//DEQ_                 : '==';
//DOLLAR_              : '$';
DOT_                 : '.';
//DOT_ASTERISK_        : '.*';
DQ_                  : '"';
EQ_                  : '=';
//EXPONENT_            : '**';
//GT_                  : '>';
//GTE_                 : '>=';
//LBE_                 : '{';
//LBT_                 : '[';
LP_                  : '(';
//LT_                  : '<';
//LTE_                 : '<=';
MINUS_               : '-';
MOD_                 : '%';
//NEQ_                 : '<>' | '!=' | '^=';
//NOT_                 : '!';
//OR_                  : '||';
PLUS_                : '+';
//POUND_               : '#';
QUESTION_            : '?';
//RANGE_OPERATOR_      : '..';
//RBE_                 : '}';
//RBT_                 : ']';
RP_                  : ')';
//SAFE_EQ_             : '<=>';
SEMI_                : ';';
//SIGNED_LEFT_SHIFT_   : '<<';
//SIGNED_RIGHT_SHIFT_  : '>>';
SLASH_               : '/';
SQ_                  : '\'';
//TILDE_               : '~';
//VERTICAL_BAR_        : '|';
//UL_                  : '_';

WS : [ \t\r\n\u3000] + ->skip;

//BLOCK_HINT : '/*+' .*? '*/';
//INLINE_HINT: '--+' ~[\r\n]* ('\r'? '\n' | EOF);

BLOCK_COMMENT:  '/*' .*? '*/'                           -> channel(HIDDEN);
INLINE_COMMENT: '--' ~[\r\n]* ('\r'? '\n' | EOF)        -> channel(HIDDEN);

ERROR_END_BLOCK  : '$error' .*? '$end'                  -> channel(HIDDEN);
IF_END_BLOCK  : '$if' (ERROR_END_BLOCK | .)*? '$end'    -> channel(HIDDEN);

STRING_: SINGLE_QUOTED_TEXT;
SINGLE_QUOTED_TEXT: SQ_ (~('\'' | '\r' | '\n') | '\'' '\'' | '\r'? '\n')* SQ_;
DOUBLE_QUOTED_TEXT: (DQ_ ( '\\'. | '""' | ~('"'| '\\') )* DQ_);
NCHAR_TEXT: 'N' STRING_;
UCHAR_TEXT: 'U' STRING_;
//
INTEGER_: INT_;
NUMBER_: INTEGER_? DOT_? INTEGER_ ('E' (PLUS_ | MINUS_)? INTEGER_)?;
//HEX_DIGIT_: '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_;
//BIT_NUM_: '0b' ('0' | '1')+ | 'B' SQ_ ('0' | '1')+ SQ_;

//A: 'A';
//K: 'K';
//M: 'M';
//G: 'G';
//T: 'T';
//P: 'P';
//E: 'E';
//H: 'H';

I_CURSOR : '[CURSOR]' ;
IDENTIFIER_: [A-Z\u0080-\u2FFF\u3001-\uFF0B\uFF0D-\uFFFF]+[A-Z_$#0-9\u0080-\u2FFF\u3001-\uFF0B\uFF0D-\uFFFF]*;
ANY_: .; // | ASTERISK_ | DOT_ | PLUS_ | MINUS_ | SLASH_ | COMMA_ | DQ_ | EQ_ | QUESTION_ | SQ_ | LP_ | RP_ ;
NOT_SEMI_: ~';';

fragment INT_: [0-9]+;
fragment HEX_: [0-9A-F];

