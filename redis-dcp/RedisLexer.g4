lexer grammar RedisLexer;

//options {
//    caseInsensitive = true;
//}

// Keywords
APPEND: A P P E N D;
ASKING: A S K I N G;
AUTH: A U T H;
BGREWRITEAOF: B G R E W R I T E A O F;
BGSAVE: B G S A V E;
BITCOUNT: B I T C O U N T;
BITFIELD: B I T F I E L D;
BLMOVE: B L M O V E;
BLPOP: B L P O P;
BRPOP: B R P O P;
BRPOPLPUSH: B R P O P L P U S H;
BZPOPMAX: B Z P O P M A X;
BZPOPMIN: B Z P O P M I N;
COMMAND: C O M M A N D;
COMMAND_COUNT: COMMAND COUNT;
COMMAND_GETKEYS: COMMAND GETKEYS;
COMMAND_INFO: COMMAND INFO;
COMMAND_LIST: COMMAND LIST;
CONFIG_GET: CONFIG GET;
CONFIG_RESETSTAT: CONFIG RESETSTAT;
CONFIG_REWRITE: CONFIG REWRITE;
CONFIG_SET: CONFIG SET;
COPY: C O P Y;
DBSIZE: D B S I Z E;
DECR: D E C R;
DECRBY: D E C R B Y;
DEL: D E L;
DISCARD: D I S C A R D;
DUMP: D U M P;
ECHO: E C H O;
EVAL: E V A L;
EXEC: E X E C;
EXISTS: E X I S T S;
EXPIRE: E X P I R E;
EXPIREAT: E X P I R E A T;
EXPIRETIME: E X P I R E T I M E;
FAILOVER: F A I L O V E R;
FLUSHALL: F L U S H A L L;
FLUSHDB: F L U S H D B;
GET: G E T;
GETDEL: G E T D E L;
GETEX: G E T E X;
GETRANGE: G E T R A N G E;
GETSET: G E T S E T;
HDEL: H D E L;
HEXISTS: H E X I S T S;
HGET: H G E T;
HGETALL: H G E T A L L;
HINCRBY: H I N C R B Y;
HINCRBYFLOAT: H I N C R B Y F L O A T;
HKEYS: H K E Y S;
HLEN: H L E N;
HMGET: H M G E T;
HMSET: H M S E T;
HRANDFIELD: H R A N D F I E L D;
HSCAN: H S C A N;
HSET: H S E T;
HSETNX: H S E T N X;
HSTRLEN: H S T R L E N;
HVALS: H V A L S;
INCR: I N C R;
INCRBY: I N C R B Y;
INCRBYFLOAT: I N C R B Y F L O A T;
INFO: I N F O;
KEYS: K E Y S;
LCS: L C S;
LINDEX: L I N D E X;
LINSERT: L I N S E R T;
LLEN: L L E N;
LMOVE: L M O V E;
LMPOP: L M P O P;
LPOP: L P O P;
LPOS: L P O S;
LPUSH: L P U S H;
LPUSHX: L P U S H X;
LRANGE: L R A N G E;
LREM: L R E M;
LSET: L S E T;
LTRIM: L T R I M;
MGET: M G E T;
MIGRATE: M I G R A T E;
MOVE: M O V E;
MSET: M S E T;
MSETNX: M S E T N X;
OBJECT: O B J E C T;
PERSIST: P E R S I S T;
PEXPIRE: P E X  P I R E;
PEXPIREAT: P E X P I R E A T;
PEXPIRETIME: P E X P I R E T I M E;
PING: P I N G;
PSETEX: P S E T E X;
PTTL: P T T L;
PUBLISH: P U B L I S H;
QUIT: Q U I T;
RANDOMKEY: R A N D O M K E Y;
READONLY: R E A D O N L Y;
READWRITE: R E A D W R I T E;
RENAME: R E N A M E;
RENAMENX: R E N A M E N X;
RESET: R E S E T;
RESTORE: R E S T O R E;
RPOP: R P O P;
RPOPLPUSH: R P O P L P U S H;
RPUSH: R P U S H;
RPUSHX: R P U S H X;
SADD: S A D D;
SAVE: S A V E;
SCAN: S C A N;
SCARD: S C A R D;
SDIFF: S D I F F;
SDIFFSTORE: S D I F F S T O R E;
SELECT: S E L E C T;
SET: S E T;
SETEX: S E T E X;
SETNX: S E T N X;
SETRANGE: S E T R A N G E;
SHUTDOWN: S H U T D O W N;
SINTER: S I N T E R;
SINTERCARD: S I N T E R C A R D;
SINTERSTORE: S I N T E  R S T O R E;
SISMEMBER: S I S M E M B  E R;
SMEMBERS: S M E M B E R S;
SMISMEMBER: S M I S M E M B E R;
SMOVE: S M O V E;
SORT: S O R T;
SORT_RO: SORT '_' R O;
SPOP: S P O P;
SRANDMEMBER: S R A N D M E M B E R;
SREM: S R E M;
SSCAN: S S C A N;
STRLEN: S T R L E N;
SUBSCRIBE: S U B S C R I B E;
SUBSTR: S U B S T R;
SUNION: S U N I O N;
SUNIONSTORE: S U N I O N S T O R E;
SYNC: S Y N C;
TIME: T I M E;
TOUCH: T O U C H;
TTL: T T L;
TYPE: T Y P E;
UNLINK: U N L I N K;
WAIT: W A I T;
WAITAOF: W A I T A O F;
ZADD: Z A D D;
ZCARD: Z C A R D;
ZCOUNT: Z C O U N T;
ZDIFF: Z D I F F;
ZDIFFSTORE: Z D I F F S T O R E;
ZINCRBY: Z I N C R B Y;
ZINTER: Z I N T E R;
ZINTERCARD: Z I N T E R C A R D;
ZINTERSTORE: Z I N T E R S T O R E;
ZLEXCOUNT: Z L E X C O U N T;
ZMPOP: Z M P O P;
ZMSCORE: Z M S C O R E;
ZPOPMAX: Z P O P M A X;
ZPOPMIN: Z P O P M I N;
ZRANDMEMBER: Z R A N D M E M B E R;
ZRANGE: Z R A N G E;
ZRANGEBYLEX: Z R A N G E B Y L E X;
ZRANGEBYSCORE: Z R A N G E B Y S C O R E;
ZRANGESTORE: Z R A N G E S T O R E;
ZRANK: Z R A N K;
ZREM: Z R E M;
ZREMRANGEBYLEX: Z R E M R A N G E B Y L E X;
ZREMRANGEBYRANK: Z R E M R A N G E B Y R A N K;
ZREMRANGEBYSCORE: Z R E M R A N G E B Y S C O R E;
ZREVRANGE: Z R E V R A N G E;
ZREVRANGEBYLEX: Z R E V R A N G E B Y  L E X;
ZREVRANGEBYSCORE: Z R E V R A N G E B Y S C O R E;
ZREVRANK: Z R E V R A N K;
ZSCAN: Z S C A N;
ZSCORE: Z S C O R E;
ZUNION: Z U N I O N;
ZUNIONSTORE: Z U N I O N S T O R E;


ABSTTL: A B S T T L;
AFTER: A F T E R;
AGGREGATE: A G G R E G A T E;
ALPHA: A L P H A;
ASC: A S C;
ASYNC: A S Y N C;
AUTH2: AUTH '2';
BEFORE: B E F O R E;
BY: B Y;
BYLEX: B Y L E X;
BYSCORE: B Y S C O R E;
CH: C H;
CONFIG: C O N F I G;
COUNT: C O U N T;
DB: D B;
DESC: D E S C;
ENCODING: E N C O D I N G;
EX: E X;
EXAT: E X A T;
FREQ: F R E Q;
GETKEYS: G E T K E Y S;
GT_: G T;
IDLETIME: I D L E T I M E;
IDX: I D X;
KEEPTTL: K E E P T T L;
LEFT: L E F T;
LEN: L E N;
LIMIT: L I M I T;
LIST: L I S T;
LT_: L T;
MATCH: M A T C H;
MAX: M A X;
MAXLEN: M A X L E N;
MIN: M I N;
MINMATCHLEN: M I N M A T C H L E N;
NX: N X;
PX: P X;
PXAT: P X A T;
RANK: R A N K;
REFCOUNT: R E F C O U N T;
REPLACE: R E P L A C E;
RESETSTAT: R E S E T S T A T;
REV: R E V;
REWRITE: R E W R I T E;
RIGHT: R I G H T;
STORE: S T O R E;
SUM: S U M;
WEIGHTS: W E I G H T S;
WITHMATCHLEN: W I T H M A T C H L E N;
WITHSCORES: W I T H S C O R E S;
WITHVALUES: W I T H V A L U E S;
XX: X X;

JSON_FALSE: 'false';
JSON_TRUE: 'true';

// Tokens
IDENTIFIER
    : (LETTER | SYMBOL | DEC_DIGIT) (LETTER | SYMBOL | DEC_DIGIT)*
    ;

FLOATING_LITERAL
    : DECIMAL_LITERAL DOT DEC_DIGIT*
    | DOT DECIMAL_LITERAL
    | HEXADECIMAL_LITERAL DOT HEX_DIGIT* (P | E) (PLUS | DASH)? DEC_DIGIT+
    | HEXADECIMAL_LITERAL (P | E) (PLUS | DASH)? DEC_DIGIT+
    | DECIMAL_LITERAL DOT DEC_DIGIT* E (PLUS | DASH)? DEC_DIGIT+
    | DOT DECIMAL_LITERAL E (PLUS | DASH)? DEC_DIGIT+
    | DECIMAL_LITERAL E (PLUS | DASH)? DEC_DIGIT+
    ;

OCTAL_LITERAL: '0' OCT_DIGIT+;
DECIMAL_LITERAL: (PLUS | DASH)? DEC_DIGIT+;
HEXADECIMAL_LITERAL: '0' X HEX_DIGIT+;

STRING_LITERAL                : '\'' (~'\'' | '\'\'')* '\'';
DOUBLE_QUOTED_STRING_LITERAL  : '"' ~'"'* '"';
BACKSTICK_STRING_LITERAL      : '`' ~'`'* '`';

// Alphabet and allowed symbols
SYMBOL: DOLLAR | UNDERSCORE | DASH | CARET | ASTERISK | PLUS | LBRACKET | LPAREN | DOT;

ARROW: '->';
ASTERISK: '*';
BACKQUOTE: '`';
BACKSLASH: '\\';
CARET: '^';
COLON: ':';
COMMA: ',';
CONCAT: '||';
DASH: '-';
DOLLAR: '$';
DOT: '.';
EQ_DOUBLE: '==';
EQ_SINGLE: '=';
GE: '>=';
GT: '>';
LBRACE: '{';
LBRACKET: '[';
LE: '<=';
LPAREN: '(';
LT: '<';
NOT_EQ: '!=' | '<>';
PERCENT: '%';
PLUS: '+';
QUERY: '?';
QUOTE_DOUBLE: '"';
QUOTE_SINGLE: '\'';
RBRACE: '}';
RBRACKET: ']';
RPAREN: ')';
SEMICOLON: ';';
SLASH: '/';
UNDERSCORE: '_';

fragment LETTER: [a-zA-Z];
fragment OCT_DIGIT: [0-7];
fragment DEC_DIGIT: [0-9];
fragment HEX_DIGIT: [0-9a-fA-F];

fragment A: [aA];
fragment B: [bB];
fragment C: [cC];
fragment D: [dD];
fragment E: [eE];
fragment F: [fF];
fragment G: [gG];
fragment H: [hH];
fragment I: [iI];
fragment J: [jJ];
fragment K: [kK];
fragment L: [lL];
fragment M: [mM];
fragment N: [nN];
fragment O: [oO];
fragment P: [pP];
fragment Q: [qQ];
fragment R: [rR];
fragment S: [sS];
fragment T: [tT];
fragment U: [uU];
fragment V: [vV];
fragment W: [wW];
fragment X: [xX];
fragment Y: [yY];
fragment Z: [zZ];

// Comments and whitespace
MULTI_LINE_COMMENT: '/*' .*? '*/' -> skip;
SINGLE_LINE_COMMENT: '//' ~('\n'|'\r')* ('\n' | '\r' | EOF) -> skip;
WHITESPACE: [ \u000B\u000C\t\r\n] -> skip;  // '\n' can be part of multiline single query
