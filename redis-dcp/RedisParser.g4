parser grammar RedisParser;

options {
    tokenVocab = RedisLexer;
}

redis_query
    : core_query ';'?
    | stack_query
    ;

core_query
    : generic_query
    | string_query
    | list_query
    | hash_query
    | set_query
    | zset_query
    | connection_management_query
    ;

stack_query
    : any_value
    ;


// core_query
generic_query
    : COPY source destination (DB destination_db)? (REPLACE)?
    | DEL key+
    | DUMP key
    | EXISTS key+
    | EXPIRE key numkeys (NX | XX | GT | LT)?
    | EXPIREAT key numkeys (NX | XX | GT | LT)?
    | EXPIRETIME key
    | KEYS pattern
    | MIGRATE host port key destination_db timeout (COPY)? (REPLACE)? (AUTH password | AUTH2 username password)? (KEYS key+)?
    | MOVE key destination_db
    | OBJECT (ENCODING | FREQ | IDLETIME | REFCOUNT) key
    | PERSIST key
    | PEXPIRE key numkeys (NX | XX | GT | LT)?
    | PEXPIREAT key numkeys (NX | XX | GT | LT)?
    | PEXPIRETIME key
    | PTTL key
    | RANDOMKEY
    | RENAME key newkey
    | RENAMENX key newkey
    | RESTORE key numkeys string (REPLACE)? (ABSTTL)? (IDLETIME numkeys)? (FREQ numkeys)?
    | SCAN cursor (MATCH pattern)? (COUNT count)? (TYPE type)?
    | SORT key (BY pattern)? (LIMIT offset count)? (GET pattern)* (ASC | DESC)? (ALPHA)? (STORE destination)?
    | SORT_RO key (BY pattern)? (LIMIT offset count)? (GET pattern)* (ASC | DESC)? (ALPHA)?
    | TOUCH key+
    | TTL key
    | TYPE key
    | UNLINK key+
    | WAIT numkeys timeout
    | WAITAOF numkeys numkeys timeout
    ;
destination_db
    : numkeys
    ;
host
    : numkeys
    ;
port
    : numkeys
    ;
username
    : (string | identifier)
    ;
password
    : (string | identifier)
    ;
newkey
    : key
    ;
type
    : (string | identifier)
    ;
offset
    : numkeys
    ;

string_query
    : APPEND key value
    | DECR key
    | DECRBY key decrement
    | GET key
    | GETDEL key
    | GETEX key ((EX | PX | EXAT | PXAT) numkeys | PERSIST)?
    | GETRANGE key start end
    | GETSET key value
    | INCR key
    | INCRBY key increment
    | INCRBYFLOAT key increment
    | LCS key1 key2 (LEN)? (IDX)? (MINMATCHLEN numkeys)? (WITHMATCHLEN)?
    | MGET key+
    | MSET (key value)+
    | MSETNX (key value)+
    | PSETEX key numkeys value
    | SET key value (NX | XX)? (GET)? ((EX | PX | EXAT | PXAT) numkeys | KEEPTTL)?
    | SETEX key numkeys value
    | SETNX key value
    | SETRANGE key numkeys value
    | STRLEN key
    | SUBSTR key start end
    ;
decrement
    : numkeys
    ;
increment
    : numkeys
    ;
start
    : numkeys
    ;
end
    : numkeys
    ;
key1
    : key
    ;
key2
    : key
    ;

list_query
    : BLMOVE source destination (LEFT | RIGHT) (LEFT | RIGHT) timeout
    | BLPOP key+ timeout
    | BRPOP key+ timeout
    | BRPOPLPUSH source destination timeout
    | LINDEX key index
    | LINSERT key (BEFORE | AFTER) pivot element
    | LLEN key
    | LMOVE source destination (LEFT | RIGHT) (LEFT | RIGHT)
    | LMPOP numkeys key+ (LEFT | RIGHT) (COUNT numkeys)?
    | LPOP key count?
    | LPOS key element (RANK numkeys)? (COUNT numkeys)? (MAXLEN numkeys)?
    | LPUSH key element+
    | LPUSHX key element+
    | LRANGE key start end
    | LREM key count element
    | LSET key index element
    | LTRIM key start end
    | RPOP key count?
    | RPOPLPUSH source destination
    | RPUSH key element+
    | RPUSHX key element+
    ;
source
    : expr
    ;
destination
    : expr
    ;
timeout
    : numkeys
    ;
index
    : numkeys
    ;
pivot
    : expr
    ;
element
    : expr
    ;
count
    : numkeys
    ;

hash_query
    : HDEL key field+
    | HEXISTS key field
    | HGET key field
    | HGETALL key
    | HINCRBY key field increment
    | HINCRBYFLOAT key field increment
    | HKEYS key
    | HLEN key
    | HMGET key field+
    | HMSET key (field value)+
    | HRANDFIELD key (count WITHVALUES?)?
    | HSCAN key cursor (MATCH pattern)? (COUNT count)?
    | HSET key (field value)+
    | HSETNX key field value
    | HSTRLEN key field
    | HVALS key
    ;
field
    : expr
    ;
cursor
    : numkeys
    ;
pattern
    : (string | identifier)
    ;

set_query
    : SADD key (member ','?)+
    | SCARD key
    | SDIFF key+
    | SDIFFSTORE destination key+
    | SINTER key+
    | SINTERCARD numkeys key+ (LIMIT numkeys)?
    | SINTERSTORE destination key+
    | SISMEMBER key member
    | SMEMBERS key
    | SMISMEMBER key member+
    | SMOVE source destination member
    | SPOP key (count)?
    | SRANDMEMBER key (count)?
    | SREM key member+
    | SSCAN key cursor (MATCH pattern)? (COUNT count)?
    | SUNION key+
    | SUNIONSTORE destination key+
    ;
member
    : string | identifier
    ;

zset_query
    : BZPOPMAX key+ timeout
    | BZPOPMIN key+ timeout
    | ZADD key (NX | XX)? (GT | LT)? (CH)? (INCR)? (score member)+
    | ZCARD key
    | ZCOUNT key min max
    | ZDIFF numkeys key+ (WITHSCORES)?
    | ZDIFFSTORE destination numkeys key+
    | ZINCRBY key increment member
    | ZINTER numkeys key+ (WEIGHTS numkeys+)? (AGGREGATE (SUM | MIN | MAX))? (WITHSCORES)?
    | ZINTERCARD numkeys key+ (LIMIT numkeys)?
    | ZINTERSTORE destination numkeys key+ (WEIGHTS numkeys+)? (AGGREGATE (SUM | MIN | MAX))?
    | ZLEXCOUNT key min? max?
    | ZMPOP numkeys key+ (MIN | MAX) (COUNT count)?
    | ZMSCORE key member+
    | ZPOPMAX key (count)?
    | ZPOPMIN key (count)?
    | ZRANDMEMBER key (count (WITHSCORES)?)?
    | ZRANGE key start end (BYSCORE | BYLEX)? (REV)? (LIMIT offset count)? (WITHSCORES)?
    | ZRANGEBYLEX key min max (LIMIT offset count)?
    | ZRANGEBYSCORE key min max (WITHSCORES)? (LIMIT offset count)?
    | ZRANGESTORE dst src min max (BYSCORE | BYLEX)? (REV)? (LIMIT offset count)?
    | ZRANK key member (WITHSCORES)?
    | ZREM key member+
    | ZREMRANGEBYLEX key min max
    | ZREMRANGEBYRANK key start end
    | ZREMRANGEBYSCORE key min max
    | ZREVRANGE key start end (WITHSCORES)?
    | ZREVRANGEBYLEX key max min (LIMIT offset count)?
    | ZREVRANGEBYSCORE key max min (WITHSCORES)? (LIMIT offset count)?
    | ZREVRANK key member (WITHSCORES)?
    | ZSCAN key cursor (MATCH pattern)? (COUNT count)?
    | ZSCORE key member
    | ZUNION numkeys key+ (WEIGHTS numkeys+)? (AGGREGATE (SUM | MIN | MAX))? (WITHSCORES)?
    | ZUNIONSTORE destination numkeys key+ (WEIGHTS numkeys+)? (AGGREGATE (SUM | MIN | MAX))?
    ;
score
    : numkeys
    ;
min
    : numkeys
    ;
max
    : numkeys
    ;
numkeys
    : numeric_literal | identifier
    ;
dst
    : identifier
    ;
src
    : identifier
    ;

connection_management_query
    : SELECT index
    ;

// basic
key
    : expr
    ;
value
    : expr
    ;

expr
    : literal
    ;

literal
    : numeric_literal
    | JSON_TRUE
    | JSON_FALSE
    | string
    | array_literal
    | object_literal
    | identifier
    | regular_expression
    ;

array_literal
    : '[' expr (',' expr)* ']'
    | '[' ']'
    ;

object_literal
    : '{' (pair_list | identifier_list) '}'
    | '{' '}'
    ;

pair_list
    : pair (',' pair)*
    ;

pair
    : (string | identifier) ':' expr
    ;

identifier_list
    : identifier (',' identifier)*
    ;

regular_expression
    : '/' any_value '/'
    ;

identifier
    : IDENTIFIER | keyword
    ;

string
    : STRING_LITERAL
    | DOUBLE_QUOTED_STRING_LITERAL
    | BACKSTICK_STRING_LITERAL
    ;

numeric_literal
    : DECIMAL_LITERAL
    | FLOATING_LITERAL
    ;

keyword
    : APPEND | ASKING
    ;

any_value: SYMBOL | .*?;
