lexer grammar InformixLexer;

options {
    caseInsensitive = true;
}


WS: [ \t\r\n]+ -> skip;

AAO                   : 'AAO';
ABORT                 : 'ABORT';
ABS                   : 'ABS';
ABSOLUTE              : 'ABSOLUTE';
ACCESS                : 'ACCESS';
ACCESS_METHOD         : 'ACCESS_METHOD';
ACCOUNT               : 'ACCOUNT';
ACOS                  : 'ACOS';
ACOSH                 : 'ACOSH';
ACTION                : 'ACTION';
ACTIVE                : 'ACTIVE';
ADD                   : 'ADD';
ADD_MONTHS            : 'ADD_MONTHS';
ADDRESS               : 'ADDRESS';
AFTER                 : 'AFTER';
AGGREGATE             : 'AGGREGATE';
ALIGNMENT             : 'ALIGNMENT';
ALL                   : 'ALL';
ALL_ROWS              : 'ALL_ROWS';
ALLOCATE              : 'ALLOCATE';
ALTER                 : 'ALTER';
ALWAYS                : 'ALWAYS';
ANALYZE               : 'ANALYZE';
AND                   : 'AND';
ANSI                  : 'ANSI';
ANY                   : 'ANY';
APPEND                : 'APPEND';
ARRAY                 : 'ARRAY';
AS                    : 'AS';
ASC                   : 'ASC';
ASCII                 : 'ASCII';
ASIN                  : 'ASIN';
ASINH                 : 'ASINH';
AT                    : 'AT';
ATAN                  : 'ATAN';
ATAN2                 : 'ATAN2';
ATANH                 : 'ATANH';
ATTACH                : 'ATTACH';
ATTRIBUTES            : 'ATTRIBUTES';
AUDIT                 : 'AUDIT';
AUTHENTICATION        : 'AUTHENTICATION';
AUTHID                : 'AUTHID';
AUTHORIZATION         : 'AUTHORIZATION';
AUTHORIZED            : 'AUTHORIZED';
AUTO                  : 'AUTO';
AUTOFREE              : 'AUTOFREE';
AUTOINCREMENT         : 'AUTOINCREMENT';
AVG                   : 'AVG';
AVOID_EXECUTE         : 'AVOID_EXECUTE';
AVOID_FACT            : 'AVOID_FACT';
BARGROUP              : 'BARGROUP';
BASED                 : 'BASED';
BEFORE                : 'BEFORE';
BEGIN                 : 'BEGIN';
BETWEEN               : 'BETWEEN';
BIGINT                : 'BIGINT';
BIGSERIAL             : 'BIGSERIAL';
BLOB                  : 'BLOB';
BOOL_F                : '\'F\'';
BOOL_T                : '\'T\'';
BOOLEAN               : 'BOOLEAN';
BOTH                  : 'BOTH';
BSON                  : 'BSON';
BSON_GET              : 'BSON_GET';
BSON_SIZE             : 'BSON_SIZE';
BSON_UPDATE           : 'BSON_UPDATE';
BSON_VALUE_BIGINT     : 'BSON_VALUE_BIGINT';
BSON_VALUE_BOOLEAN    : 'BSON_VALUE_BOOLEAN';
BSON_VALUE_DATE       : 'BSON_VALUE_DATE';
BSON_VALUE_DOUBLE     : 'BSON_VALUE_DOUBLE';
BSON_VALUE_INT        : 'BSON_VALUE_INT';
BSON_VALUE_LVARCHAR   : 'BSON_VALUE_LVARCHAR';
BSON_VALUE_OBJECTID   : 'BSON_VALUE_OBJECTID';
BSON_VALUE_TIMESTAMP  : 'BSON_VALUE_TIMESTAMP';
BSON_VALUE_VARCHAR    : 'BSON_VALUE_VARCHAR';
BUCKETS               : 'BUCKETS';
BUFFERED              : 'BUFFERED';
BY                    : 'BY';
BYTE                  : 'BYTE';
C                     : 'C';
CACHE                 : 'CACHE';
CALL                  : 'CALL';
CANNOTHASH            : 'CANNOTHASH';
CARDINALITY           : 'CARDINALITY';
CASCADE               : 'CASCADE';
CASE                  : 'CASE';
CAST                  : 'CAST';
CEIL                  : 'CEIL';
CHAR                  : 'CHAR';
CHAR_LENGTH           : 'CHAR_LENGTH';
CHARACTER             : 'CHARACTER';
CHARACTER_LENGTH      : 'CHARACTER_LENGTH';
CHARINDEX             : 'CHARINDEX';
CHECK                 : 'CHECK';
CHR                   : 'CHR';
CLASS                 : 'CLASS';
CLASS_ORIGIN          : 'CLASS_ORIGIN';
CLOB                  : 'CLOB';
CLOSE                 : 'CLOSE';
CLUSTER               : 'CLUSTER';
COALESCE              : 'COALESCE';
COLLATE               : 'COLLATE';
COLLATION             : 'COLLATION';
COLLECTION            : 'COLLECTION';
COLUMN                : 'COLUMN';
COLUMNS               : 'COLUMNS';
COMBINE               : 'COMBINE';
COMMIT                : 'COMMIT';
COMMITTED             : 'COMMITTED';
COMPONENT             : 'COMPONENT';
COMPONENTS            : 'COMPONENTS';
COMPRESSED            : 'COMPRESSED';
CONCAT                : 'CONCAT';
CONCURRENT            : 'CONCURRENT';
CONFLICT              : 'CONFLICT';
CONNECT               : 'CONNECT';
CONNECTION            : 'CONNECTION';
CONNECTION_NAME       : 'CONNECTION_NAME';
CONSTRAINT            : 'CONSTRAINT';
CONSTRAINTS           : 'CONSTRAINTS';
CONTEXT               : 'CONTEXT';
CONTINUE              : 'CONTINUE';
COS                   : 'COS';
COSH                  : 'COSH';
COSTFUNC              : 'COSTFUNC';
COUNT                 : 'COUNT';
CRCOLS                : 'CRCOLS';
CREATE                : 'CREATE';
CROSS                 : 'CROSS';
CUME_DIST             : 'CUME_DIST';
CURRENT               : 'CURRENT';
CURRENT_DATE          : 'CURRENT_DATE';
CURRENT_ROLE          : 'CURRENT_ROLE';
CURRENT_TIME          : 'CURRENT_TIME';
CURRENT_TIMESTAMP     : 'CURRENT_TIMESTAMP';
CURRENT_USER          : 'CURRENT_USER';
CURRVAL               : 'CURRVAL';
CURSOR                : 'CURSOR';
CYCLE                 : 'CYCLE';
DATA                  : 'DATA';
DATABASE              : 'DATABASE';
DATAFILES             : 'DATAFILES';
DATASKIP              : 'DATASKIP';
DATE                  : 'DATE';
DATETIME              : 'DATETIME';
DAY                   : 'DAY';
DBA                   : 'DBA';
DBDATE                : 'DBDATE';
DBINFO                : 'DBINFO';
DBMONEY               : 'DBMONEY';
DBSA                  : 'DBSA';
DBSECADM              : 'DBSECADM';
DBSERVERNAME          : 'DBSERVERNAME';
DBSSO                 : 'DBSSO';
DEALLOCATE            : 'DEALLOCATE';
DEBUG                 : 'DEBUG';
DEC                   : 'DEC';
DECIMAL               : 'DECIMAL';
DECLARE               : 'DECLARE';
DECODE                : 'DECODE';
DECRYPT_BINARY        : 'DECRYPT_BINARY';
DECRYPT_CHAR          : 'DECRYPT_CHAR';
DEFAULT               : 'DEFAULT';
DEFAULT_ROLE          : 'DEFAULT_ROLE';
DEFERRABLE            : 'DEFERRABLE';
DEFERRED              : 'DEFERRED';
DEFERRED_PREPARE      : 'DEFERRED_PREPARE';
DEFINE                : 'DEFINE';
DEGREES               : 'DEGREES';
DELETE                : 'DELETE';
DELETING              : 'DELETING';
DELIMITER             : 'DELIMITER';
DELUXE                : 'DELUXE';
DENSE_RANK            : 'DENSE_RANK';
DENSERANK             : 'DENSERANK';
DESC                  : 'DESC';
DESCRIBE              : 'DESCRIBE';
DESCRIPTOR            : 'DESCRIPTOR';
DETACH                : 'DETACH';
DIAGNOSTICS           : 'DIAGNOSTICS';
DIRECTIVES            : 'DIRECTIVES';
DIRTY                 : 'DIRTY';
DISABLE               : 'DISABLE';
DISABLED              : 'DISABLED';
DISCARD               : 'DISCARD';
DISCONNECT            : 'DISCONNECT';
DISTINCT              : 'DISTINCT';
DISTRIBUTIONS         : 'DISTRIBUTIONS';
DOCUMENT              : 'DOCUMENT';
DORMANT               : 'DORMANT';
DOUBLE                : 'DOUBLE';
DROP                  : 'DROP';
EACH                  : 'EACH';
ELIF                  : 'ELIF';
ELSE                  : 'ELSE';
ENABLE                : 'ENABLE';
ENABLED               : 'ENABLED';
ENCRYPT_AES           : 'ENCRYPT_AES';
ENCRYPT_TDES          : 'ENCRYPT_TDES';
ENCRYPTION            : 'ENCRYPTION';
END                   : 'END';
ENVIRONMENT           : 'ENVIRONMENT';
ERKEY                 : 'ERKEY';
ERROR                 : 'ERROR';
ESCAPE                : 'ESCAPE';
EXCEPT                : 'EXCEPT';
EXCEPTION             : 'EXCEPTION';
EXCLUDE               : 'EXCLUDE';
EXCLUSIVE             : 'EXCLUSIVE';
EXECUTE               : 'EXECUTE';
EXEMPTION             : 'EXEMPTION';
EXISTS                : 'EXISTS';
EXIT                  : 'EXIT';
EXP                   : 'EXP';
EXPLAIN               : 'EXPLAIN';
EXPLICIT              : 'EXPLICIT';
EXPRESS               : 'EXPRESS';
EXPRESSION            : 'EXPRESSION';
EXTEND                : 'EXTEND';
EXTENT                : 'EXTENT';
EXTERNAL              : 'EXTERNAL';
EXTYPEID              : 'EXTYPEID';
EXTYPELENGTH          : 'EXTYPELENGTH';
EXTYPENAME            : 'EXTYPENAME';
EXTYPEOWNERLENGTH     : 'EXTYPEOWNERLENGTH';
EXTYPEOWNERNAME       : 'EXTYPEOWNERNAME';
FACT                  : 'FACT';
FAIL                  : 'FAIL';
FALSE                 : 'FALSE';
FETCH                 : 'FETCH';
FILE                  : 'FILE';
FILETOBLOB            : 'FILETOBLOB';
FILETOCLOB            : 'FILETOCLOB';
FILLFACTOR            : 'FILLFACTOR';
FILTER                : 'FILTER';
FILTERING             : 'FILTERING';
FINAL                 : 'FINAL';
FIRST                 : 'FIRST';
FIRST_ROWS            : 'FIRST_ROWS';
FIRST_VALUE           : 'FIRST_VALUE';
FLOAT                 : 'FLOAT';
FLOOR                 : 'FLOOR';
FLUSH                 : 'FLUSH';
FOLLOWING             : 'FOLLOWING';
FOR                   : 'FOR';
FORCE                 : 'FORCE';
FOREACH               : 'FOREACH';
FOREIGN               : 'FOREIGN';
FORMAT                : 'FORMAT';
FORMAT_UNITS          : 'FORMAT_UNITS';
FOUND                 : 'FOUND';
FRACTION              : 'FRACTION';
FRAGMENT              : 'FRAGMENT';
FRAGMENTS             : 'FRAGMENTS';
FREE                  : 'FREE';
FROM                  : 'FROM';
FULL                  : 'FULL';
FUNCTION              : 'FUNCTION';
G                     : 'G';
GB                    : 'GB';
GENBSON               : 'GENBSON';
GENERATED             : 'GENERATED';
GET                   : 'GET';
GETHINT               : 'GETHINT';
GiB                   : 'GiB';
GLOB                  : 'GLOB';
GLOBAL                : 'GLOBAL';
GO                    : 'GO';
GOTO                  : 'GOTO';
GRANT                 : 'GRANT';
GREATEST              : 'GREATEST';
GRID                  : 'GRID';
GROUP                 : 'GROUP';
GROUPS                : 'GROUPS';
HANDLESNULLS          : 'HANDLESNULLS';
HASH                  : 'HASH';
HAVING                : 'HAVING';
HEADINGS              : 'HEADINGS';
HEX                   : 'HEX';
HIGH                  : 'HIGH';
HINT                  : 'HINT';
HOLD                  : 'HOLD';
HOME                  : 'HOME';
HOUR                  : 'HOUR';
IDATA                 : 'IDATA';
IDSLBACREADARRAY      : 'IDSLBACREADARRAY';
IDSLBACREADSET        : 'IDSLBACREADSET';
IDSLBACREADTREE       : 'IDSLBACREADTREE';
IDSLBACRULES          : 'IDSLBACRULES';
IDSLBACWRITEARRAY     : 'IDSLBACWRITEARRAY';
IDSLBACWRITESET       : 'IDSLBACWRITESET';
IDSLBACWRITETREE      : 'IDSLBACWRITETREE';
IDSSECURITYLABEL      : 'IDSSECURITYLABEL';
IF                    : 'IF';
IFX_ALLOW_NEWLINE     : 'IFX_ALLOW_NEWLINE';
IFX_BIT_LEFTSHIFT     : 'IFX_BIT_LEFTSHIFT';
IFX_BIT_RIGHTSHIFT    : 'IFX_BIT_RIGHTSHIFT';
IFX_REPLACE_MODULE    : 'IFX_REPLACE_MODULE';
IFX_UNLOAD_MODULE     : 'IFX_UNLOAD_MODULE';
IGNORE                : 'IGNORE';
ILENGTH               : 'ILENGTH';
IMMEDIATE             : 'IMMEDIATE';
IMPLICIT              : 'IMPLICIT';
IN                    : 'IN';
INACTIVE              : 'INACTIVE';
INCREMENT             : 'INCREMENT';
INDEX                 : 'INDEX';
INDEXED               : 'INDEXED';
INDEXES               : 'INDEXES';
INDICATOR             : 'INDICATOR';
INFO                  : 'INFO';
INFORMIX              : 'INFORMIX';
INFORMIX_SQLCODE      : 'INFORMIX_SQLCODE';
INIT                  : 'INIT';
INITCAP               : 'INITCAP';
INITIALLY             : 'INITIALLY';
INNER                 : 'INNER';
INOUT                 : 'INOUT';
INPUT                 : 'INPUT';
INSENSITIVE           : 'INSENSITIVE';
INSERT                : 'INSERT';
INSERTING             : 'INSERTING';
INSTEAD               : 'INSTEAD';
INSTR                 : 'INSTR';
INT                   : 'INT';
INT8                  : 'INT8';
INTEG                 : 'INTEG';
INTEGER               : 'INTEGER';
INTERNAL              : 'INTERNAL';
INTERNALLENGTH        : 'INTERNALLENGTH';
INTERSECT             : 'INTERSECT';
INTERVAL              : 'INTERVAL';
INTO                  : 'INTO';
INVISIBLE             : 'INVISIBLE';
IS                    : 'IS';
ISNULL                : 'ISNULL';
ISOLATION             : 'ISOLATION';
ITER                  : 'ITER';
ITERATOR              : 'ITERATOR';
ITYPE                 : 'ITYPE';
JAVA                  : 'JAVA';
JOIN                  : 'JOIN';
JSON                  : 'JSON';
K                     : 'K';
KB                    : 'KB';
KEEP                  : 'KEEP';
KEY                   : 'KEY';
KiB                   : 'KiB';
LABEL                 : 'LABEL';
LAG                   : 'LAG';
LANGUAGE              : 'LANGUAGE';
LARGE                 : 'LARGE';
LAST                  : 'LAST';
LAST_DAY              : 'LAST_DAY';
LAST_VALUE            : 'LAST_VALUE';
LATERAL               : 'LATERAL';
LEAD                  : 'LEAD';
LEADING               : 'LEADING';
LEAST                 : 'LEAST';
LEFT                  : 'LEFT';
LEN                   : 'LEN';
LENGTH                : 'LENGTH';
LET                   : 'LET';
LEVEL                 : 'LEVEL';
LIKE                  : 'LIKE';
LIMIT                 : 'LIMIT';
LIST                  : 'LIST';
LISTING               : 'LISTING';
LN                    : 'LN';
LOAD                  : 'LOAD';
LOCK                  : 'LOCK';
LOCKS                 : 'LOCKS';
LOCOPY                : 'LOCOPY';
LOG                   : 'LOG';
LOG10                 : 'LOG10';
LOGN                  : 'LOGN';
LOOP                  : 'LOOP';
LOTOFILE              : 'LOTOFILE';
LOW                   : 'LOW';
LOWER                 : 'LOWER';
LPAD                  : 'LPAD';
LTRIM                 : 'LTRIM';
LVARCHAR              : 'LVARCHAR';
M                     : 'M';
MATCH                 : 'MATCH';
MATCHED               : 'MATCHED';
MATCHES               : 'MATCHES';
MAX                   : 'MAX';
MAXERRORS             : 'MAXERRORS';
MAXLEN                : 'MAXLEN';
MAXVALUE              : 'MAXVALUE';
MB                    : 'MB';
MDY                   : 'MDY';
MEDIUM                : 'MEDIUM';
MERGE                 : 'MERGE';
MESSAGE_LENGTH        : 'MESSAGE_LENGTH';
MESSAGE_TEXT          : 'MESSAGE_TEXT';
MiB                   : 'MiB';
MIN                   : 'MIN';
MINUS                 : 'MINUS';
MINUTE                : 'MINUTE';
MINVALUE              : 'MINVALUE';
MOD                   : 'MOD';
MODE                  : 'MODE';
MODERATE              : 'MODERATE';
MODIFY                : 'MODIFY';
MONEY                 : 'MONEY';
MONTH                 : 'MONTH';
MONTHS_BETWEEN        : 'MONTHS_BETWEEN';
MORE_                 : 'MORE';
MULTISET              : 'MULTISET';
NAME                  : 'NAME';
NATURAL               : 'NATURAL';
NCHAR                 : 'NCHAR';
NEGATOR               : 'NEGATOR';
NEW                   : 'NEW';
NEXT                  : 'NEXT';
NEXT_DAY              : 'NEXT_DAY';
NEXTVAL               : 'NEXTVAL';
NLSCASE               : 'NLSCASE';
NO                    : 'NO';
NOCACHE               : 'NOCACHE';
NOCYCLE               : 'NOCYCLE';
NOMAXVALUE            : 'NOMAXVALUE';
NOMINVALUE            : 'NOMINVALUE';
NON_DIM               : 'NON_DIM';
NONE                  : 'NONE';
NOORDER               : 'NOORDER';
NOT                   : 'NOT';
NOTNULL               : 'NOTNULL';
NOVALIDATE            : 'NOVALIDATE';
NTH_VALUE             : 'NTH_VALUE';
NTILE                 : 'NTILE';
NULL                  : 'NULL';
NULLABLE              : 'NULLABLE';
NULLIF                : 'NULLIF';
NULLS                 : 'NULLS';
NUMBER                : 'NUMBER';
NUMERIC               : 'NUMERIC';
NUMROWS               : 'NUMROWS';
NUMTODSINTERVAL       : 'NUMTODSINTERVAL';
NVARCHAR              : 'NVARCHAR';
NVL                   : 'NVL';
NVL2                  : 'NVL2';
OCTET_LENGTH          : 'OCTET_LENGTH';
OF                    : 'OF';
OFF                   : 'OFF';
OFFSET                : 'OFFSET';
OLD                   : 'OLD';
ON                    : 'ON';
ONLINE                : 'ONLINE';
ONLY                  : 'ONLY';
OPAQUE                : 'OPAQUE';
OPCLASS               : 'OPCLASS';
OPEN                  : 'OPEN';
OPTIMIZATION          : 'OPTIMIZATION';
OPTION                : 'OPTION';
OR                    : 'OR';
ORDER                 : 'ORDER';
OUT                   : 'OUT';
OUTER                 : 'OUTER';
OUTPUT                : 'OUTPUT';
OVER                  : 'OVER';
OVERRIDE              : 'OVERRIDE';
PAGE                  : 'PAGE';
PARALLELIZABLE        : 'PARALLELIZABLE';
PARAMETER             : 'PARAMETER';
PARTITION             : 'PARTITION';
PASSEDBYVALUE         : 'PASSEDBYVALUE';
PASSWORD              : 'PASSWORD';
PDQPRIORITY           : 'PDQPRIORITY';
PERCALL_COST          : 'PERCALL_COST';
PERCENT_RANK          : 'PERCENT_RANK';
PIPE                  : 'PIPE';
PLAN                  : 'PLAN';
POLICY                : 'POLICY';
POW                   : 'POW';
POWER                 : 'POWER';
PRAGMA                : 'PRAGMA';
PRECEDING             : 'PRECEDING';
PRECISION             : 'PRECISION';
PREPARE               : 'PREPARE';
PREVIOUS              : 'PREVIOUS';
PRIMARY               : 'PRIMARY';
PRIOR                 : 'PRIOR';
PRIVATE               : 'PRIVATE';
PRIVILEGES            : 'PRIVILEGES';
PROCEDURE             : 'PROCEDURE';
PROPERTIES            : 'PROPERTIES';
PUBLIC                : 'PUBLIC';
PUT                   : 'PUT';
QUARTER               : 'QUARTER';
QUERY                 : 'QUERY';
RADIANS               : 'RADIANS';
RAISE                 : 'RAISE';
RANGE                 : 'RANGE';
RANK                  : 'RANK';
RATIO_TO_REPORT       : 'RATIO_TO_REPORT';
RATIOTOREPORT         : 'RATIOTOREPORT';
RAW                   : 'RAW';
READ                  : 'READ';
REAL                  : 'REAL';
RECORDEND             : 'RECORDEND';
RECURSIVE             : 'RECURSIVE';
REFERENCES            : 'REFERENCES';
REFERENCING           : 'REFERENCING';
REGEXP                : 'REGEXP';
REINDEX               : 'REINDEX';
REJECTFILE            : 'REJECTFILE';
RELATIVE              : 'RELATIVE';
RELEASE               : 'RELEASE';
REMAINDER             : 'REMAINDER';
RENAME                : 'RENAME';
REOPTIMIZATION        : 'REOPTIMIZATION';
REPEATABLE            : 'REPEATABLE';
REPLACE               : 'REPLACE';
REPLCHECK             : 'REPLCHECK';
REPLICATION           : 'REPLICATION';
RESOLUTION            : 'RESOLUTION';
RESOURCE              : 'RESOURCE';
RESPECT               : 'RESPECT';
RESTART               : 'RESTART';
RESTRICT              : 'RESTRICT';
RESUME                : 'RESUME';
RETAIN                : 'RETAIN';
RETURN                : 'RETURN';
RETURNED_SQLSTATE     : 'RETURNED_SQLSTATE';
RETURNING             : 'RETURNING';
RETURNS               : 'RETURNS';
REUSE                 : 'REUSE';
REVERSE               : 'REVERSE';
REVOKE                : 'REVOKE';
RIGHT                 : 'RIGHT';
ROBIN                 : 'ROBIN';
ROLE                  : 'ROLE';
ROLLBACK              : 'ROLLBACK';
ROLLING               : 'ROLLING';
ROOT                  : 'ROOT';
ROUND                 : 'ROUND';
ROUTINE               : 'ROUTINE';
ROW                   : 'ROW';
ROW_COUNT             : 'ROW_COUNT';
ROW_NUMBER            : 'ROW_NUMBER';
ROWID                 : 'ROWID';
ROWIDS                : 'ROWIDS';
ROWNUMBER             : 'ROWNUMBER';
ROWS                  : 'ROWS';
RPAD                  : 'RPAD';
RTRIM                 : 'RTRIM';
RULE                  : 'RULE';
SAMEAS                : 'SAMEAS';
SAMPLING              : 'SAMPLING';
SAVE                  : 'SAVE';
SAVEPOINT             : 'SAVEPOINT';
SCALE                 : 'SCALE';
SCHEMA                : 'SCHEMA';
SCROLL                : 'SCROLL';
SECLABEL_BY_COMP      : 'SECLABEL_BY_COMP';
SECLABEL_BY_NAME      : 'SECLABEL_BY_NAME';
SECLABEL_TO_CHAR      : 'SECLABEL_TO_CHAR';
SECOND                : 'SECOND';
SECONDARY             : 'SECONDARY';
SECURED               : 'SECURED';
SECURITY              : 'SECURITY';
SELCONST              : 'SELCONST';
SELECT                : 'SELECT';
SELECTING             : 'SELECTING';
SELFUNC               : 'SELFUNC';
SENSITIVE             : 'SENSITIVE';
SEQUENCE              : 'SEQUENCE';
SERIAL                : 'SERIAL';
SERIAL8               : 'SERIAL8';
SERIALIZABLE          : 'SERIALIZABLE';
SERVER_NAME           : 'SERVER_NAME';
SESSION               : 'SESSION';
SET                   : 'SET';
SETSESSIONAUTH        : 'SETSESSIONAUTH';
SHARE                 : 'SHARE';
SIBLINGS              : 'SIBLINGS';
SIGN                  : 'SIGN';
SIN                   : 'SIN';
SINH                  : 'SINH';
SITENAME              : 'SITENAME';
SIZE                  : 'SIZE';
SKIP_                 : 'SKIP';
SMALL                 : 'SMALL';
SMALLFLOAT            : 'SMALLFLOAT';
SMALLINT              : 'SMALLINT';
SOME                  : 'SOME';
SOURCEID              : 'SOURCEID';
SOURCETYPE            : 'SOURCETYPE';
SPACE                 : 'SPACE';
SPECIFIC              : 'SPECIFIC';
SPL                   : 'SPL';
SQL                   : 'SQL';
SQLCODE               : 'SQLCODE';
SQLERROR              : 'SQLERROR';
SQLWARNING            : 'SQLWARNING';
SQRT                  : 'SQRT';
STABILITY             : 'STABILITY';
STACK                 : 'STACK';
STANDARD              : 'STANDARD';
STAR_JOIN             : 'STAR_JOIN';
START                 : 'START';
STATCHANGE            : 'STATCHANGE';
STATEMENT             : 'STATEMENT';
STATISTICS            : 'STATISTICS';
STATLEVEL             : 'STATLEVEL';
STATUS                : 'STATUS';
STDEV                 : 'STDEV';
STEP                  : 'STEP';
STOP                  : 'STOP';
STORAGE               : 'STORAGE';
STORE                 : 'STORE';
STORED                : 'STORED';
STRATEGIES            : 'STRATEGIES';
STYLE                 : 'STYLE';
SUBCLASS_ORIGIN       : 'SUBCLASS_ORIGIN';
SUBSTR                : 'SUBSTR';
SUBSTRB               : 'SUBSTRB';
SUBSTRING             : 'SUBSTRING';
SUBSTRING_INDEX       : 'SUBSTRING_INDEX';
SUM                   : 'SUM';
SUPPORT               : 'SUPPORT';
SYNONYM               : 'SYNONYM';
SYSDATE               : 'SYSDATE';
SYSTEM                : 'SYSTEM';
T                     : 'T';
TABLE                 : 'TABLE';
TABLES                : 'TABLES';
TAN                   : 'TAN';
TANH                  : 'TANH';
TB                    : 'TB';
TEMP                  : 'TEMP';
TEMPORARY             : 'TEMPORARY';
TEST                  : 'TEST';
TEXT                  : 'TEXT';
THEN                  : 'THEN';
TiB                   : 'TiB';
TIME                  : 'TIME';
TO                    : 'TO';
TO_CHAR               : 'TO_CHAR';
TO_DATE               : 'TO_DATE';
TO_DSINTERVAL         : 'TO_DSINTERVAL';
TO_NUMBER             : 'TO_NUMBER';
TODAY                 : 'TODAY';
TRACE                 : 'TRACE';
TRAILING              : 'TRAILING';
TRANSACTION           : 'TRANSACTION';
TRANSITION            : 'TRANSITION';
TREE                  : 'TREE';
TRIGGER               : 'TRIGGER';
TRIGGERS              : 'TRIGGERS';
TRIM                  : 'TRIM';
TRUE                  : 'TRUE';
TRUNC                 : 'TRUNC';
TRUNCATE              : 'TRUNCATE';
TRUSTED               : 'TRUSTED';
TYPE                  : 'TYPE';
UID                   : 'UID';
UNBOUNDED             : 'UNBOUNDED';
UNCOMMITTED           : 'UNCOMMITTED';
UNDER                 : 'UNDER';
UNION                 : 'UNION';
UNIQUE                : 'UNIQUE';
UNITS                 : 'UNITS';
UNLOAD                : 'UNLOAD';
UNLOCK                : 'UNLOCK';
UPDATE                : 'UPDATE';
UPDATING              : 'UPDATING';
UPON                  : 'UPON';
UPPER                 : 'UPPER';
USAGE                 : 'USAGE';
USE                   : 'USE';
USE_INVISIBLE_INDEXES : 'USE_INVISIBLE_INDEXES';
USER                  : 'USER';
USING                 : 'USING';
VACUUM                : 'VACUUM';
VALUE                 : 'VALUE';
VALUES                : 'VALUES';
VARCHAR               : 'VARCHAR';
VARIABLE              : 'VARIABLE';
VARIANCE              : 'VARIANCE';
VARIANT               : 'VARIANT';
VARYING               : 'VARYING';
VERCOLS               : 'VERCOLS';
VIEW                  : 'VIEW';
VIOLATIONS            : 'VIOLATIONS';
VIRTUAL               : 'VIRTUAL';
VISIBLE               : 'VISIBLE';
WAIT                  : 'WAIT';
WARNING               : 'WARNING';
WEEKDAY               : 'WEEKDAY';
WHEN                  : 'WHEN';
WHENEVER              : 'WHENEVER';
WHERE                 : 'WHERE';
WHILE                 : 'WHILE';
WINDOW                : 'WINDOW';
WITH                  : 'WITH';
WITHOUT               : 'WITHOUT';
WORK                  : 'WORK';
WRITE                 : 'WRITE';
WRITEDOWN             : 'WRITEDOWN';
WRITEUP               : 'WRITEUP';
XADATASOURCE          : 'XADATASOURCE';
YEAR                  : 'YEAR';




AND_                : '&&';
OR_                 : '||';
NOT_                : '!';
TILDE_              : '~';
VERTICAL_BAR_       : '|';
AMPERSAND_          : '&';
SIGNED_LEFT_SHIFT_  : '<<';
SIGNED_RIGHT_SHIFT_ : '>>';
CARET_              : '^';
MOD_                : '%';
COLON_              : ':';
DOUBLE_COLON_       : '::';
PLUS_               : '+';
MINUS_              : '-';
ASTERISK_           : '*';
SLASH_              : '/';
BACKSLASH_          : '\\';
DOT_                : '.';
DOUBLE_DOT_         : '..';
DOT_ASTERISK_       : '.*';
SAFE_EQ_            : '<=>';
DEQ_                : '==';
EQ_                 : '=';
NEQ_                : '<>' | '!=';
GT_                 : '>';
GTE_                : '>=';
LT_                 : '<';
LTE_                : '<=';
POUND_              : '#';
LP_                 : '(';
RP_                 : ')';
LBE_                : '{';
RBE_                : '}';
LBT_                : '[';
RBT_                : ']';
COMMA_              : ',';
DQ_                 : '"';
SQ_                 : '\'';
BQ_                 : '`';
QUESTION_           : '?';
AT_                 : '@';
SEMI_               : ';';
DOLLAR_             : '$';

LITERAL_COLLECTION: (SET | MULTISET | LIST) WS* '{' .*? '}';

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=segments-optimizer-directives
BLOCK_DIRECTIVES : '/*+' .*? '*/' | '{+' .*? '}';
INLINE_DIRECTIVES: '--+' ~[\r\n]* ('\r'? '\n' | EOF);


BLOCK_COMMENT  : '/*' .*? '*/'      -> channel(HIDDEN);
BLOCK_COMMENT2 : '{' .*? '}'      -> channel(HIDDEN);
INLINE_COMMENT : '--' ~[\r\n]* ('\r'? '\n' | EOF) -> channel(HIDDEN);

STRING_COLLECTION
    : SQ_ (SET | MULTISET | LIST) ('\'\'' | ~('\''))* SQ_
    | DQ_ (SET | MULTISET | LIST) ('""' | ~('"') )* DQ_
    ;

STRING_ROW
    : SQ_ ROW ('\'\'' | ~('\''))* SQ_
    | DQ_ ROW ('""' | ~('"') )* DQ_
    ;

STRING_: SINGLE_QUOTED_TEXT;
SINGLE_QUOTED_TEXT: SQ_ ('\'\'' | ~('\''))* SQ_;
DOUBLE_QUOTED_TEXT: DQ_ ('""' | ~('"') )* DQ_;


INT_: DIGIT+;
FOR_RANGE: INT_ DOUBLE_DOT_ INT_;
NUMBER_: (DIGIT+ ('.' DIGIT*)? | '.' DIGIT+) ('E' [-+]? DIGIT+)?;

HEX_DIGIT_: '0x' HEX_DIGIT+ | 'X' SQ_ HEX_DIGIT+ SQ_;

IDENTIFIER_: [a-z_][a-z0-9$_\u0080-\uFFFF]*;


fragment HEX_DIGIT   : [0-9A-F];
fragment DIGIT       : [0-9];
