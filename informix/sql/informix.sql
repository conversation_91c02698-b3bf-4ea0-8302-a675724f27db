allocate collection :a_typed_set;
allocate descriptor :descname with max :occ;
allocate descriptor 'desc1' with max 3;
allocate row :a_row;

ALTER ACCESS_METHOD remote
   ADD am_scancost = FS_scancost,
   ADD am_rowids,
   DROP am_getbyid,
   MODIFY am_costfactor = 0.9;

ALTER FRAGMENT ON TABLE cur_acct ATTACH old_acct;

CREATE TABLE employee
(emp_id INTEGER, name <PERSON><PERSON>(32),
 dept_id CHAR(2), mgr_id INTEGER, ssn CHAR(12))
    FRAGMENT BY RANGE (emp_id)
    INTERVAL (100) STORE IN (dbs1, dbs2, dbs3, dbs4)
      PARTITION p0 VALUES < 200 IN dbs1,
      PARTITION p1 VALUES < 400 IN dbs2;

CREATE UNIQUE INDEX employee_id_idx ON employee(emp_id);

CREATE INDEX employee_dept_idx ON employee(dept_id);

CREATE TABLE employee2
(emp_id INTEGER, name <PERSON><PERSON>(32),
 dept_id  CHAR(2), mgr_id INTEGER, ssn CHAR(12),
 CHECK (emp_id >=500 AND emp_id <600)) in dbs4;
CREATE UNIQUE INDEX employee2_id_idx ON employee2(emp_id) in dbs4;
CREATE INDEX employee2_dept_idx ON employee2(dept_id) in dbs4;
CREATE UNIQUE INDEX employee2_ssn_idx ON employee2(ssn) in dbs4;
CREATE INDEX employee2_name_idx ON employee2(name) in dbs4;

ALTER FRAGMENT ONLINE ON TABLE employee
   ATTACH employee2 AS PARTITION p3 VALUES < 300;

ALTER FRAGMENT ON TABLE pen_types ATTACH pen_types, pen_makers;

CREATE TABLE cur_acct (a int) FRAGMENT BY EXPRESSION
   a < 5 in dbsp1, a >= 5 and a < 10 in dbsp2;
CREATE TABLE new_acct (a int) IN dbsp3;
ALTER FRAGMENT ON TABLE cur_acct ATTACH new_acct AS a>=10;

ALTER FRAGMENT ON TABLE T1 DETACH PARTITION sys_pt1 detacht1;

ALTER FRAGMENT ON TABLE T2 DETACH PARTITION part2 detacht2;

CREATE TABLE employee (emp_id INTEGER, name CHAR(32), dept_id CHAR(2),
                       mgr_id INTEGER, ssn CHAR(12))
    FRAGMENT BY RANGE (emp_id)
    INTERVAL (100) STORE IN (dbs1, dbs2, dbs3, dbs4)
      PARTITION p0 VALUES < 200 IN dbs1,
      PARTITION p1 VALUES < 400 IN dbs2;
CREATE UNIQUE INDEX employee_id_idx ON employee(emp_id);
CREATE INDEX employee_dept_idx ON employee(dept_id);

INSERT INTO employee VALUES (401, "Susan", "DV", 101, "***********");
INSERT INTO employee VALUES (601, "David", "QA", 104, "***********");

ALTER FRAGMENT ONLINE ON TABLE employee
   DETACH PARTITION p0 employee3;
ALTER FRAGMENT ON TABLE cur_acct DETACH dbsp2 accounts;

CREATE TABLE checks (col1 INT, col2 INT)
    FRAGMENT BY ROUND ROBIN IN dbsp1, dbsp2, dbsp3;

ALTER FRAGMENT ON TABLE checks INIT IN dbsp1;

ALTER FRAGMENT ON TABLE T1 INIT
   FRAGMENT BY RANGE(c1)
   INTERVAL (100+100) STORE IN (dbs3, dbs4, dbs5, dbs6, dbs7, dbs8)
      PARTITION part0 VALUES < 0    IN dbs0,
      PARTITION part1 VALUES < 1000 IN dbs1,
      PARTITION part2 VALUES < 2000 IN dbs2;

ALTER FRAGMENT ON TABLE T1 INIT
   FRAGMENT BY RANGE(c2)
      INTERVAL (NUMTOYMINTERVAL(1,'MONTH'))
         PARTITION part0 VALUES <  DATE('01/01/2009') IN dbs0,
         PARTITION part1 VALUES <  DATE('07/01/2009') IN dbs1,
         PARTITION part2 VALUES <  DATE('01/01/2010') IN dbs2;

CREATE TABLE account (col1 INT, col2 INT)
    FRAGMENT BY ROUND ROBIN IN dbsp1, dbsp2;
ALTER FRAGMENT ON TABLE account
   INIT FRAGMENT BY EXPRESSION
   col1 < 0 IN dbsp1,
   col2 >= 0 IN dbsp2;

CREATE TABLE balances (col1 INT, col2 INT) IN dbsp1;
ALTER FRAGMENT ON TABLE balances INIT
   FRAGMENT BY EXPRESSION col1 <= 500 IN dbsp1,
      col1 > 500 AND col1 <=1000 IN dbsp2, REMAINDER IN dbsp3;

CREATE TABLE book (col1 INT, col2 INT)
    FRAGMENT BY ROUND ROBIN IN dbsp1, dbsp4;

ALTER FRAGMENT ON TABLE book ADD dbsp3;

CREATE TABLE book (col1 INT, col2 INT)
    FRAGMENT BY ROUND ROBIN IN dbsp1, dbsp4;

ALTER FRAGMENT ON TABLE book
   ADD PARTITION chapter3 IN dbsp1;

ALTER FRAGMENT ON TABLE news
   ADD PARTITION century3 (c1 >= 200 AND c1 < 300) IN dbsp2;

ALTER FRAGMENT ON INDEX cust_indx DROP dbsp2;

ALTER FRAGMENT ON TABLE customer DROP dbsp1;

ALTER FRAGMENT ON TABLE T2 DROP PARTITION part4;

ALTER FRAGMENT ON INDEX idx2 DROP PARTITION part4;

ALTER FRAGMENT ON TABLE T1 DROP INTERVAL STORE IN (dbs7, dbs8);

ALTER FRAGMENT ON INDEX idx1 DROP INTERVAL STORE IN (dbs7, dbs8);

ALTER FRAGMENT ON TABLE cust_acct
   MODIFY dbsp1 TO acct_num < 65 IN dbsp1;

ALTER FRAGMENT ON TABLE cust_acct
   MODIFY PARTITION part1 TO PARTITION part2 (acct_num < 35) IN dbsp2;

CREATE TABLE window_orders
(order_id INT, cust_id INT,
 order_date DATE, order_desc CHAR (1024))
    FRAGMENT BY RANGE (order_date)
   INTERVAL (NUMTOYMINTERVAL (1,'MONTH'))
      ROLLING (4 FRAGMENTS) DETACH
      STORE IN (dbs1, dbs2, dbs3)
      PARTITION p0 VALUES < DATE ('01/01/2015') IN dbs1,
      PARTITION p4 VALUES IS NULL in dbs3;

ALTER FRAGMENT ON TABLE window_orders MODIFY INTERVAL
   LIMIT TO 30 MiB DETACH INTERVAL ONLY;

ALTER FRAGMENT ON TABLE window_orders MODIFY INTERVAL
   ROLLING (4 FRAGMENTS) DISCARD;

ALTER FRAGMENT ON TABLE window_orders MODIFY INTERVAL
   ROLLING (6 FRAGMENTS);

ALTER FRAGMENT ON TABLE window_orders MODIFY INTERVAL
   ROLLING (4 FRAGMENTS) DETACH;

ALTER FRAGMENT ON TABLE window_orders MODIFY INTERVAL
   DROP ALL ROLLING;

CREATE TABLE window_orders
(order_id INT, cust_id INT,
 order_date DATE, order_desc CHAR (1024))
    FRAGMENT BY RANGE (order_date)
   INTERVAL (NUMTOYMINTERVAL (1,'MONTH'))
      STORE IN (dbs1, dbs2, dbs3)
      PARTITION p0 VALUES < DATE ('01/01/2015') IN dbs1,
      PARTITION p4 VALUES IS NULL in dbs3;

CREATE TABLE tabtrans (i INT, c CHAR(2))
    FRAGMENT BY RANGE (i)
   INTERVAL (100) STORE IN (dbs1, dbs2, dbs3)
      PARTITION p0 VALUES < 100 IN dbs0,
      PARTITION p1 VALUES < 200 IN dbs1,
      PARTITION p2 VALUES < 300 IN dbs0;  -- last range fragment (also

ALTER FRAGMENT ON TABLE tabtrans
   MODIFY INTERVAL TRANSITION TO 250;

ALTER FRAGMENT ON TABLE tabtrans
   MODIFY INTERVAL TRANSITION TO 500;

   ALTER FRAGMENT ON TABLE tab MODIFY INTERVAL TRANSITION TO 550;

CREATE TABLE tab (i INT, c CHAR(2))
    FRAGMENT BY RANGE (i)
      INTERVAL (100) STORE IN (dbs1, dbs2, dbs3)
         PARTITION p0 VALUES < 100 IN dbs0,
         PARTITION p1 VALUES < 200 IN dbs1,
         PARTITION p2 VALUES < 300 IN dbs0;

INSERT INTO tab
VALUES (301, "AA");

ALTER FRAGMENT ON TABLE tab MODIFY INTERVAL TRANSITION TO 500;

ALTER FRAGMENT ON TABLE tab
   MODIFY INTERVAL TRANSITION TO 550;

ALTER FRAGMENT ON TABLE tab MODIFY INTERVAL TRANSITION TO 750;

CREATE TABLE employee
     (emp_id INTEGER, name CHAR(32),
      dept_id CHAR(2), mgr_id INTEGER, ssn CHAR(12))
  FRAGMENT BY RANGE (emp_id)
    INTERVAL (100) STORE IN (dbs1, dbs2, dbs3, dbs4)
      PARTITION p0 VALUES < 200 IN dbs1,
      PARTITION p1 VALUES < 400 IN dbs2;
CREATE UNIQUE INDEX employee_id_idx ON employee(emp_id);
CREATE INDEX employee_dept_idx ON employee(dept_id);

INSERT INTO employee VALUES (401, "Susan", "DV", 101, "***********");
INSERT INTO employee VALUES (601, "David", "QA", 104, "***********");

ALTER FRAGMENT ONLINE ON TABLE employee
   MODIFY INTERVAL TRANSITION TO 300;

ALTER FRAGMENT ONLINE ON TABLE employee MODIFY INTERVAL TRANSITION TO 700;

ALTER FRAGMENT ON TABLE tab MODIFY INTERVAL DISABLED;

ALTER FRAGMENT ON TABLE tab MODIFY INTERVAL ENABLED;

ALTER FRAGMENT ON TABLE tab MODIFY INTERVAL DISABLED
   STORE IN (dbs4, dbs5);

ALTER FRAGMENT ON TABLE tab MODIFY
   PARTITION p1 TO PARTITION newp1,
   PARTITION sys_p6 TO PARTITION newsys_p6;

CREATE TABLE tab2 (i INT, c CHAR(2))
   FRAGMENT BY RANGE (i)
   INTERVAL (100) STORE IN (dbs1, dbs2, dbs3)
      PARTITION p0 VALUES < 100 IN dbs0,
      PARTITION p1 VALUES < 200 IN dbs1;

ALTER FRAGMENT ON TABLE tab2 MODIFY
   PARTITION p1 TO PARTITION p1 IN dbs2;

ALTER FRAGMENT ON TABLE tab2 MODIFY
   PARTITION p1 TO PARTITION p1 IN dbs2,
   PARTITION sys_p6 TO PARTITION sys_p6 IN dbs3;

CREATE TABLE tab (i INT, c CHAR(2))
   FRAGMENT BY RANGE (i)
   INTERVAL (100) STORE IN (dbs1, dbs2, dbs3)
      PARTITION p0 VALUES < 100 IN dbs0,
      PARTITION p1 VALUES < 200 IN dbs1;

ALTER FRAGMENT ON TABLE tab
   MODIFY INTERVAL STORE IN (dbs4, dbs5);

ALTER FRAGMENT ON TABLE mytab MODIFY INTERVAL
   STORE IN (dbs1, dbs6, dbs3, dbs4, dbs8);

CREATE TABLE mytab (col1 int)
   FRAGMENT BY RANGE (c1) INTERVAL (100)
   STORE IN (dbs1, dbs2, dbs3, dbs4, dbs5)
      PARTITION p1 VALUES < 300 in dbs0;

ALTER FRAGMENT ON TABLE tab MODIFY
   PARTITION p1 TO PARTITION p1 IN dbs2;

ALTER FRAGMENT ON TABLE tab MODIFY
   PARTITION p1 TO PARTITION p1 IN dbs2,
   PARTITION sys_p6 TO PARTITION sys_p6 IN dbs3;

CREATE TABLE tab (i INT, c CHAR(2))
   FRAGMENT BY RANGE (i)
   INTERVAL (100) STORE IN (dbs1, dbs2, dbs3)
      PARTITION p0 VALUES < 100 IN dbs0,
      PARTITION p1 VALUES < 200 IN dbs1;

ALTER FRAGMENT ON TABLE tab MODIFY
   PARTITION sys_p6 TO PARTITION sys_p6
      VALUES < 900 IN dbs2;

ALTER FRAGMENT ON TABLE tab MODIFY
   PARTITION p0 TO PARTITION p0
      VALUES < -50 IN dbs0;

ALTER FRAGMENT ON TABLE tab MODIFY
   PARTITION p0 TO PARTITION p0 VALUES < 150 IN dbs0;

ALTER FRAGMENT ON TABLE T2 MODIFY
   PARTITION part1 TO PARTITION part11
      VALUES ('CA', 'OR', 'TX') IN dbs1;

CREATE TABLE myTable (i int, c char(2))
    FRAGMENT BY LIST (c)
        PARTITION p1 VALUES ("AB", "CD") IN dbs1,
        PARTITION p2 VALUES ("PQ", "RS") IN dbs2,
        PARTITION p3 REMAINDER IN dbs3;

ALTER FRAGMENT ON TABLE myTable MODIFY
   PARTITION p2 TO PARTITION newp2
      VALUES (NULL) IN dbs5;

ALTER FRAGMENT ON TABLE tab MODIFY
   PARTITION p1 TO PARTITION p1
      VALUES ("AB", "CD", "EF") IN dbs1;

ALTER FRAGMENT ON TABLE tab MODIFY
   PARTITION p2 TO PARTITION p2 VALUES (NULL) IN dbs2;

CREATE INDEX item_idx ON items (stock_num) IN dbsp1;

ALTER FRAGMENT ON INDEX item_idx INIT
   FRAGMENT BY EXPRESSION
   stock_num <= 50 IN dbsp1,
   stock_num > 50 AND stock_num <= 80 IN dbsp2,
   REMAINDER IN dbsp3;

ALTER FRAGMENT ON INDEX item_idx
   ADD stock_num > 80 AND stock_num <= 120 IN dbsp4;

ALTER FRAGMENT ON INDEX item_idx
   MODIFY dbsp1 TO stock_num <= 40 IN dbsp1;

ALTER FRAGMENT ON INDEX item_idx
   DROP dbsp4;

ALTER FRAGMENT ON INDEX item_idx INIT
   PARTITION BY EXPRESSION
   PARTITION part1 stock_num <= 10 IN dbsp1,
   PARTITION part2 stock_num > 20 AND stock_num <= 30 IN dbsp1,
   PARTITION part3 REMAINDER IN dbsp2;

ALTER FRAGMENT ON INDEX item_idx ADD
   PARTITION part4 stock_num > 30 AND stock_num <= 40 IN dbsp2
   BEFORE part3;

ALTER FRAGMENT ON INDEX idx2 INIT
   FRAGMENT BY RANGE(c2)
      INTERVAL (NUMTOYMINTERVAL(1,'MONTH'))
         PARTITION part0 VALUES <  DATE('01/01/2007') IN dbs0,
         PARTITION part1 VALUES <  DATE('07/01/2007') IN dbs1,
         PARTITION part2 VALUES <  DATE('01/01/2008') IN dbs2;

ALTER FRAGMENT ON INDEX idx2 INIT
   FRAGMENT BY LIST(state)
      PARTITION part0 VALUES ('KS','IL') IN dbs0,
      PARTITION part1 VALUES ('CA','OR') IN dbs0,
      PARTITION part2 VALUES (NULL) IN dbs1,
      PARTITION part3 REMAINDER IN dbs2;

ALTER FUNCTION func1 WITH (MODIFY PARALLELIZABLE);
ALTER FUNCTION func1 WITH (ADD PARALLELIZABLE);

CREATE INDEX ix_cust ON orders (customer_num);
ALTER INDEX ix_cust TO CLUSTER;

CREATE UNIQUE INDEX ix_ord ON orders (order_num);

CREATE CLUSTER INDEX ix_cust ON orders (customer_num);
ALTER INDEX ix_cust TO NOT CLUSTER;

ALTER INDEX ix_ord TO CLUSTER;

ALTER PROCEDURE proc1 WITH (MODIFY PARALLELIZABLE);
ALTER PROCEDURE proc1 WITH (ADD PARALLELIZABLE);

ALTER ROUTINE func1 WITH (MODIFY PARALLELIZABLE);
ALTER ROUTINE func1 WITH (ADD PARALLELIZABLE);

ALTER ROUTINE func1(CHAR, INT, BOOLEAN)
   WITH (
      DROP HANDLESNULLS,
      MODIFY PERCALL_COST = 20,
      ADD PARALLELIZABLE
      );

ALTER SPECIFIC ROUTINE raise_sal;

CREATE SECURITY LABEL COMPONENT aquilae
   ARRAY [ "imperator", "tribunus", "centurio", "miles", "asinus" ];

ALTER SECURITY LABEL COMPONENT aquilae
   ADD ARRAY [ "legatus" BEFORE "tribunus","cunctator" AFTER "asinus" ];

CREATE SECURITY LABEL COMPONENT departments
   SET { 'Marketing', 'HR', 'Finance' };

ALTER SECURITY LABEL COMPONENT departments
   ADD SET { 'Training', 'QA', 'Security' };

CREATE SECURITY LABEL COMPONENT Oakland
TREE ( 'Port' ROOT,
       'Downtown' UNDER 'Port',
       'Airport' UNDER 'Port',
       'Estuary' UNDER 'Airport',
       'Avenues' UNDER 'Downtown',
       'Hills' UNDER 'Avenues');

ALTER SECURITY LABEL COMPONENT Oakland
   ADD TREE ( 'Uptown' UNDER 'Port',
              'Bay' UNDER 'Estuary');

CREATE SEQUENCE seq_2
   INCREMENT BY 1 START WITH 1
   MAXVALUE 30 MINVALUE 0
   NOCYCLE CACHE 10 ORDER;

CREATE TABLE tab1 (col1 int, col2 int);
INSERT INTO tab1 VALUES (0, 0);

ALTER SEQUENCE seq_2
   RESTART WITH 5
   INCREMENT by 2
   MAXVALUE 300;

INSERT INTO tab1 (col1, col2) VALUES (seq_2.NEXTVAL, seq_2.NEXTVAL)

ALTER TABLE customer ADD CRCOLS;
ALTER TABLE customer ADD ERKEY;
ALTER TABLE customer ADD REPLCHECK;
ALTER TABLE customer DROP REPLCHECK;
ALTER TABLE frag1 DROP RWIDS;
ALTER TABLE customer ADD VERCOLS;

ALTER TABLE tA
  ADD (Col_4 IDSSECURITYLABEL DEFAULT 'label4'), --to add this column
  ADD SECURITY POLICY Watchdog;

ALTER TABLE items
   ADD (item_weight DECIMAL(6,2) NOT NULL BEFORE total_price);
ALTER TABLE items
   ADD (item_velocity DECIMAL(6,3) BEFORE total_price);

ALTER TABLE items
   MODIFY (item_velocity DECIMAL(6,3) DEFAULT 299792.458);

ALTER TABLE items
   ADD (item_color CHAR(12) DEFAULT "translucent"
   BEFORE item_velocity);

CREATE TABLE tabB
  (
    id VARCHAR(128) NOT NULL,
    data DATE DEFAULT TODAY,
    modcount BIGINT,
    flags INTEGER DEFAULT 12,
  );

ALTER TABLE tabB
   MODIFY ( data "informix".BSON DEFAULT NULL);

ALTER TABLE T1
   ADD (D IDSSECURITYLABEL DEFAULT mylabel1)
   ADD SECURITY POLICY MegaCorp;

ALTER TABLE T1
   DROP SECURITY POLICY MegaCorp;
ALTER TABLE T1
   ADD (D IDSSECURITYLABEL DEFAULT myNewLabel1)
   ADD SECURITY POLICY Watchdog;

ALTER TABLE items
   ADD (item_weight DECIMAL(6,2)
   DEFAULT 2.0 NOT NULL
      BEFORE total_price);

ALTER TABLE detail ADD CONSTRAINT (FOREIGN KEY ( col_one )
   REFERENCES master( col_one ) CONSTRAINT detail_fork1 DISABLED);

ALTER TABLE cust_calls
   ADD ref_order INTEGER
   REFERENCES orders (order_num)
   BEFORE user_id;

ALTER TABLE catalog DROP CONSTRAINT aa;

ALTER TABLE catalog ADD CONSTRAINT
   (FOREIGN KEY (stock_num, manu_code) REFERENCES stock
   ON DELETE CASCADE CONSTRAINT ab);

ALTER TABLE items
   ADD (unit_price MONEY (6,2) CHECK (unit_price > 0));

ALTER TABLE items ADD CONSTRAINT CHECK (unit_price < total_price);

CREATE TABLE SecuriTab
   (Col1 CHAR (18),
   Col2 INT,
   Col4 IDSSECURITYLABEL DEFAULT LabelRW)
   SECURITY POLICY company;

ALTER TABLE SecuriTab
   ADD (Col3 CHAR (20) BEFORE Col4 COLUMN SECURED WITH Label23);

ALTER TABLE customer ADD CONSTRAINT UNIQUE (lname, fname);

ALTER TABLE customer
   ADD CONSTRAINT UNIQUE (lname, fname) CONSTRAINT u_cust;

ALTER TABLE customer
   ADD CONSTRAINT UNIQUE (lname, fname) CONSTRAINT u_cust DISABLED;

ALTER TABLE catalog DROP CONSTRAINT aa;

ALTER TABLE catalog ADD CONSTRAINT
   (FOREIGN KEY (stock_num, manu_code) REFERENCES stock
   CONSTRAINT ai DISABLED INDEX DISABLED);

ALTER TABLE catalog ADD CONSTRAINT
   FOREIGN KEY (stock_num, manu_code) REFERENCES stock
   CONSTRAINT ai DISABLED INDEX DISABLED;

 ALTER TABLE child ADD
   CONSTRAINT(FOREIGN KEY(x1) REFERENCES parent(c1)
      CONSTRAINT cons_child_x1 DISABLED INDEX DISABLED);

CREATE TABLE parent(c1 INT, c2 INT, c3 INT);
CREATE UNIQUE INDEX idx_parent_c1 ON parent(c1);
ALTER TABLE parent ADD
   CONSTRAINT PRIMARY KEY(c1)
      CONSTRAINT cons_parent_c1;
CREATE TABLE child(x1 INT, x2 INT, x3 VARCHAR(32));
CREATE INDEX idx_child_x1 ON child(x1);

ALTER TABLE child ADD
   CONSTRAINT(FOREIGN KEY(x1) REFERENCES parent(c1)
      CONSTRAINT cons_child_x1 INDEX DISABLED);

ALTER TABLE parent ADD CONSTRAINT
   PRIMARY KEY(c1) CONSTRAINT cons_parent_c1;

ALTER TABLE child
   ADD CONSTRAINT (FOREIGN KEY(x1)
       REFERENCES parent(c1) CONSTRAINT cons_child_x1);

ALTER TABLE child DROP CONSTRAINT cons_child_x1;

ALTER TABLE child
   ADD CONSTRAINT (FOREIGN KEY(x1)
      REFERENCES parent(c1)
         CONSTRAINT cons_child_x1 NOVALIDATE);

ALTER TABLE child
   ADD CONSTRAINT FOREIGN KEY(x1)
      REFERENCES parent(c1)
         CONSTRAINT cons_child_x1 NOVALIDATE;

CREATE TABLE postal(
   name     VARCHAR(30),
   address  VARCHAR(20),
   city     VARCHAR(20),
   state    CHAR(2),
   zip      INTEGER,
);

ALTER TABLE postal ADD TYPE postal_t;

CREATE TABLE postal OF TYPE postal_t;

ALTER TABLE manufact DROP CONSTRAINT con_name;

ALTER TABLE orders DROP CONSTRAINT (con_ref, con_check);
SELECT constrname FROM  sysconstraints
   WHERE tabid = (SELECT tabid FROM systables
      WHERE tabname = 'items');

CREATE TABLE IF NOT EXISTS MyPeople OF TYPE people_t;
ALTER TABLE MyPeople ADD CONSTRAINT UNIQUE (people_t.*)
   CONSTRAINT very_unique;
CREATE TABLE IF NOT EXISTS LittlePeople OF TYPE people_t
   UNDER MyPeople;
ALTER TABLE LittlePeople
   DROP CONSTRAINT very_unique; --cannot drop an inherited constraint

ALTER TABLE MyPeople DROP CONSTRAINT very_unique;

CREATE TABLE IF NOT EXISTS UnsTable (
   col1 CHAR(18),
   col2 INT NOT NULL,
   col3 CHAR(32) UNIQUE,
   col4 INT NOT NULL,
   col5 DATETIME YEAR TO MONTH);

ALTER TABLE UnsTable
   DROP (col2, col4)
   ADD (col2 INT NOT NULL BEFORE col3,
        col4 INT NOT NULL BEFORE col5);

ALTER TABLE UnsTable
   DROP (col2, col4);

ALTER TABLE UnsTable
   ADD (col2 INT NOT NULL BEFORE col3,
        col4 INT NOT NULL BEFORE col5);

ALTER TABLE tab2 DROP i4;

CREATE TRIGGER col1trig UPDATE OF i2 ON tab1
   BEFORE(INSERT INTO tab2 VALUES(1,1));

ALTER TABLE customer LOCK MODE(PAGE);

ALTER TABLE tabnolog TYPE (STANDARD);

ALTER TABLE items MODIFY (quantity INT DEFAULT 1 NOT NULL);

ALTER TABLE stock MODIFY (description LVARCHAR(3072));

ALTER TABLE tab1 MODIFY (c1 INT NOT NULL);

ALTER TABLE my_table MODIFY (serial_num SERIAL (1000));

ALTER TABLE child3 MODIFY (s serial(75), s8 serial8(75));

ALTER TABLE child3 MODIFY (s serial(75), s8 serial8(75));

ALTER TABLE items MODIFY (quantity CHAR(6));

ALTER TABLE Argoknot
   MODIFY (ColJ JSON SECURED WITH fleece);

CREATE TABLE Alphanumeric
   (ColB CHAR(18),
    Col2 INT,
    Col3 CHAR (20));

ALTER TABLE Alphanumeric
   MODIFY (Col1 CHAR(18) SECURED WITH Label23)
   ADD SECURITY POLICY watchdog;

ALTER TABLE Alphanumeric
    MODIFY (Col2 INT COLUMN SECURED WITH Label23);

ALTER TABLE Alphanumeric
    MODIFY (Col3 CHAR(20) COLUMN SECURED WITH Label23);

 GRANT SECURITY LABEL watchdog.Label23
   TO sam FOR READ ACCESS;

 GRANT SECURITY LABEL watchdog.Label23
   TO peter, elaine, olan FOR WRITE ACCESS;

 GRANT SECURITY LABEL watchdog.Label23
   TO lynette FOR ALL ACCESS;

CREATE TABLE Betanumeric
   (Col1 CHAR (18)SECURED WITH labelK9,
    Col2 INT,
    Col3 CHAR (20)SECURED WITH labelK9,
    Col4 IDSSECURITYLABEL DEFAULT 'labelK2')
   SECURITY POLICY watchdog;

ALTER TABLE Betanumeric
   MODIFY (Col1 CHAR (18) DROP COLUMN SECURITY);

ALTER TABLE Betanumeric DROP SECURITY POLICY;

ALTER TABLE customer MODIFY EXTENT SIZE 32;

ALTER TABLE customer MODIFY EXTENT SIZE 32 NEXT SIZE 32

ALTER TABLE customer MODIFY NEXT SIZE 32;

ALTER TABLE MyClobs ADD (c5 CLOB)
   PUT c5 IN (sbs3,sbs4) (EXTENT SIZE 64, HIGH INTEG);

ALTER TABLE sbtab ADD (c1 BLOB BEFORE c2)
   PUT c1 IN (sbs1) (EXTENT SIZE 32,
                     NO LOG,
                     MODERATE INTEG,
                     KEEP ACCESS TIME);

ALTER TABLE sbtab PUT c1 IN (sbs1) (EXTENT SIZE 64, NO LOG);

ALTER TABLE sbtab PUT c1 IN (sbs1) (EXTENT SIZE 64);

ALTER TABLE sbtab PUT c1 IN (sbs1, sbs2)
   (EXTENT SIZE 100, LOG, KEEP ACCESS TIME);

ALTER TABLE MyData
   ADD C4 IDSSECURITYLABEL,
   ADD SECURITY POLICY Watchdog;

ALTER TABLE MyData
   ADD C4 IDSSECURITYLABEL DEFAULT 'canine',
   ADD SECURITY POLICY Watchdog;

ALTER TABLE MyOtherData
   DROP SECURITY POLICY Watchdog;

ALTER TABLE MyOtherData
   ADD (C4 IDSSECURITYLABEL DEFAULT),
   MODIFY (C2 INT COLUMN SECURED WITH label7),
   ADD SECURITY POLICY Robodog;

ALTER TABLE MyOtherData
   ADD (C4 IDSSECURITYLABEL DEFAULT),
   ADD SECURITY POLICY Robodog;

ALTER TABLE MyOtherData
   MODIFY (C1 CHAR (8) COLUMN SECURED WITH label9),
   ADD SECURITY POLICY Robodog;

ALTER TABLE tabFrag STATLEVEL TABLE;

UPDATE STATISTICS LOW
   FOR TABLE tabFrag (smartblob) DROP DISTRIBUTIONS;

UPDATE STATISTICS HIGH
   FOR TABLE tabFrag (smartblob);

ALTER TABLE tabFrag STATCHANGE AUTO;

UPDATE STATISTICS LOW
   FOR TABLE tabFrag (smartblob) DROP DISTRIBUTIONS;

UPDATE STATISTICS MEDIUM
   FOR TABLE tabFrag (smartblob) AUTO;

   ALTER TRUSTED CONTEXT appserver
     DISABLE;

   ALTER TRUSTED CONTEXT securerole
     REPLACE USE FOR joe WITH AUTHENTICATION
     ADD USE FOR PUBLIC WITHOUT AUTHENTICATION;

   ALTER TRUSTED CONTEXT securerole
     ALTER ATTRIBUTES (ADDRESS '************');

ALTER USER bill DROP USER, ADD UID 1360;

ALTER USER bill MODIFY UID 1361, ADD GROUP (dbsa), ADD HOME "/u/user1";

ALTER USER bill ACCOUNT UNLOCK DROP AUTHORIZATION (dbsso);

ALTER USER bill DROP HOME;

BEGIN;
BEGIN WORK;
close democursor;
CLOSE DATABASE;
COMMIT;
COMMIT WORK;
connect to 'a@srv1' as 'A';
connect to 'b@srv2' as 'B' with concurrent transaction;

CREATE PRIMARY ACCESS_METHOD am_tabprops
(
am_open = FS_open,
am_close = FS_close,
am_beginscan = FS_beginScan,
am_create = FS_create,
am_scancost = FS_scanCost,
am_endscan = FS_endScan,
am_getnext = FS_getNext,
am_getbyid = FS_getById,
am_drop = FS_drop,
am_truncate = FS_truncate,
am_rowids,
am_sptype = 'x'
);

CREATE AGGREGATE average
   WITH (
      INIT = average_init,
      ITER = average_iter,
      COMBINE = average_combine,
      FINAL = average_final
      );

CREATE EXPLICIT CAST (rate_of_return AS percent
   WITH rate_to_prcnt);

SELECT bond_rate FROM bond
   WHERE bond_rate::percent > initial_APR;

CREATE IMPLICIT CAST (CHAR AS percent WITH char_to_prcnt);

CREATE DATABASE vehicles;

CREATE DATABASE vehicles IN research;
CREATE DATABASE bufDatabase WITH BUFFERED LOG;

CREATE DATABASE unbufDatabase WITH LOG;

CREATE DATABASE ansiDatabase WITH LOG MODE ANSI;

CREATE DATABASE employees IN dbspaceYee WITH BUFFERED LOG;
CREATE DATABASE stores IN dbsp1 WITH LOG NLSCASE SENSITIVE;
CREATE DATABASE stores IN dbsp1 WITH LOG;
CREATE DATABASE stores IN dbsp2 WITH BUFFERED LOG NLSCASE INSENSITIVE;
CREATE DATABASE casedb WITH LOG NLSCASE INSENSITIVE;
SELECT DISTINCT nc FROM foo;
CREATE DISTINCT TYPE birthday AS DATE;
CREATE DISTINCT TYPE dist_type AS NUMERIC;

CREATE EXTERNAL TABLE emp_ext
SAMEAS employee
USING (
   DATAFILES ("DISK:/work2/mydir/emp.dat"),
    REJECTFILE "/work2/mydir/emp.rej"
    );

CREATE EXTERNAL TABLE exttab (
       id    SERIAL,
       lobc  CLOB,
       lobb  BLOB)
USING (DATAFILES(
 "DISK:/work1/exttab1.dat;BLOBDIR:/work1/blobdir1;CLOBDIR:/work1/clobdir1",
 "DISK:/work1/exttab2.dat;CLOBDIR:/work1/clobdir2",
 "DISK:/work1/exttab3.dat"),
 DELIMITER '|');

CREATE EXTERNAL TABLE ext_date (dob date)
USING ( 	DATAFILES ("DISK:/tmp/datedisk"),
   		 	REJECTFILE "/tmp/datereject",
  		  	DBDATE "DMY2-",
  		  	FORMAT "delimited");

CREATE EXTERNAL TABLE empdata
(
   empname	char(40),
   empdoj	date
)
USING
(DATAFILES
    (
        "DISK:/work/empdata.unl"
    ),
    FORMAT "DELIMITED",
    REJECTFILE "/work/errlog/empdata.rej",
    MAXERRORS 100);


CREATE EXTERNAL TABLE emp_ext SAMEAS empdata
USING
(DATAFILES
    (
        "DISK:/work/empdata2.unl"
    ),
    REJECTFILE "/work/errlog/empdata2.rej",
    DELUXE
);

CREATE EXTERNAL TABLE ext1( col1 int )
 USING
 (DATAFILES
     (
        "DISK:/tmp/ext1.unl"
     )
);

CREATE TABLE base (col1 int);
INSERT INTO ext1 SELECT * FROM base;

CREATE EXTERNAL TABLE ext1( col1 int )
  USING
  (DATAFILES
      (
          "DISK:/tmp/ext1.unl"
      )
);

CREATE EXTERNAL TABLE emp_ext
 ( name CHAR(18) EXTERNAL CHAR(20),
   address VARCHAR(40) EXTERNAL CHAR(40),
   empno INTEGER EXTERNAL CHAR(6)
  )
USING (
  FORMAT 'FIXED',
  DATAFILES
     (
        "DISK:/work2/mydir/emp.fix"
     )
);

CREATE EXTERNAL TABLE emp_ext
SAMEAS employee
USING (
   FORMAT 'DELIMITED',
   DATAFILES
     ("DISK:/work2/mydir/emp.dat"),
   REJECTFILE "/work2/mydir/emp.rej",
   EXPRESS
   );

CREATE EXTERNAL TABLE emp_ext
SAMEAS employee
USING (
   DATAFILES ("DISK:/work2/mydir/emp.dat"),
   REJECTFILE "/work2/mydir/emp.rej",
   );

CREATE EXTERNAL TABLE emp_ext
SAMEAS employee
USING (
   DATAFILES ("DISK:/work2/mydir/emp.dat"),
    REJECTFILE "/work2/mydir/emp.rej"
    );

CREATE EXTERNAL TABLE emp_ext
   ( name CHAR(18) EXTERNAL CHAR(18),
     hiredate DATE EXTERNAL CHAR(10),
     address VARCHAR(40) EXTERNAL CHAR(40),
     empno INTEGER EXTERNAL CHAR(6) )
USING (
   FORMAT 'FIXED',
    DATAFILES ("DISK:/work2/mydir/emp.fix")
    );

MERGE INTO t1
     USING ext ON t1.c1 = ext.c1
    WHEN MATCHED THEN UPDATE
     SET t1.c2 = ext.c2
    WHEN NOT MATCHED THEN INSERT VALUES (99, '999');

create function testfunc() returning char(50);
    return "this is the original function";
end function;

CREATE FUNCTION getArea
  (i INT DEFAULT 0)
RETURNING INT SPECIFIC getSquareArea;
DEFINE j INT;
LET j = i * i;
RETURN j;
END FUNCTION;

CREATE FUNCTION getArea
  (i INT DEFAULT 0, j INT DEFAULT 0)
RETURNING INT SPECIFIC getRectangleArea;
	DEFINE ka INT;
	LET k = i * j;
	RETURN k;
END FUNCTION;

GRANT EXECUTE ON SPECIFIC FUNCTION getSquareArea TO informix;
GRANT EXECUTE ON SPECIFIC FUNCTION getRectangleArea TO informix;

GRANT  EXECUTE ON FUNCTION getArea (INTEGER) TO informix;
GRANT  EXECUTE ON FUNCTION getArea (INTEGER,INTEGER) TO informix;

CREATE FUNCTION update_by_pct ( pct INT, pid CHAR(10))
   RETURNING INT;
   UPDATE inventory SET price = price + price * (pct/100)
      WHERE part_id = pid;
   return (select price from inventory where part_id = pid);
END FUNCTION
   DOCUMENT "USAGE: Update a price by a percentage",
         "Enter an integer percentage from 1 - 100",
         "and a part id number"
   WITH LISTING IN '/tmp/warn_file';

CREATE FUNCTION equal ( arg1 basetype1, arg2 basetype1)
   RETURNING BOOLEAN;
   EXTERNAL NAME
      "/usr/lib/basetype1/lib/libbtype1.so(basetype1_equal)"
   LANGUAGE C
END FUNCTION;

CREATE DBA FUNCTION func2 () RETURNING INT;
   CREATE TABLE tab2 (coly INT);
   RETURN 1;
END FUNCTION;

CREATE FUNCTION delete_order( p_order_num INT) RETURNING INT, INT;
   DEFINE item_count INT;
   SELECT count(*) INTO item_count FROM items
      WHERE order_num = p_order_num;
   DELETE FROM orders WHERE order_num = p_order_num;
   RETURN p_order_num, item_count;
END FUNCTION;

create function from 'del_ord.sql';

CREATE UNIQUE INDEX c_num_ix ON customer (customer_num);

CREATE DISTINCT INDEX c_num_ix ON customer (customer_num);

CREATE UNIQUE INDEX c_num_desc_ix ON customer (customer_num DESC);

CREATE CLUSTER INDEX c_clust_ix ON customer (zipcode);

CREATE TABLE IF NOT EXISTS bson_table(bson_col BSON);

INSERT INTO bson_table VALUES(
    '{person:{givenname:"Jim",surname:"Flynn",age:29,cars:["dodge","olds"]}}'
    ::JSON::BSON);

CREATE INDEX idx2 ON bson_table(
   BSON_GET(bson_col, "person.surname") USING BSON );

CREATE INDEX zone_func_ind ON zones (Area(length,width));

CREATE UNIQUE INDEX st_man_ix ON stock (stock_num, manu_code);

CREATE TABLE customer (
   customer_num  SERIAL(101) UNIQUE,
   fname                CHAR(15),
   lname                CHAR(15),
   company              CHAR(20),
   address1             CHAR(20),
   address2             CHAR(20),
   city                 CHAR(15),
   state                CHAR(2),
   zipcode              CHAR(5),
   phone                CHAR(18)
   );

CREATE INDEX c_temp1 ON customer (customer_num DESC);
CREATE INDEX c_temp2 ON customer (customer_num, zipcode);

CREATE INDEX stock_idx1 ON stock
   (manu_code ASC, unit_price DESC);

CREATE INDEX c_num1_ix ON cust_tab (cust_num abs_btree_ops);

CREATE INDEX tx ON t(data)
   USING fulltext (WORD_SUPPORT='PATTERN',
   PHRASE_SUPPORT='MAXIMUM');

CREATE INDEX idx1 ON tab1(c1) HASH ON (c1) with 100 buckets;

CREATE INDEX idx2 on tab2(c1, c2, c3) HASH ON (c1, c2) with 10 buckets;

CREATE INDEX idx4 on tab4(c1, c2, c3);
CREATE INDEX idx3 on tab3(c1, c2) HASH ON (c1, c2) with 100 buckets;

CREATE INDEX cust3_ix ON customer (address) COMPRESSED
   EXTENT SIZE 32 NEXT SIZE 32;

CREATE UNIQUE INDEX cust3_ix ON customer (address) COMPRESSED ;

CREATE TABLE IF NOT EXISTS t (a INT, b INT);
CREATE INDEX  IF NOT EXISTS idx1 ON t(a) EXTENT SIZE 32 NEXT SIZE 32;
CREATE INDEX  IF NOT EXISTS idx2 ON t(b);

CREATE INDEX idx_cust ON customer (customer_num) IN custind;

CREATE INDEX ind_intab ON tab_autoloc (col1) IN TABLE;

CREATE UNIQUE INDEX IF NOT EXISTS idx_1 ON customer(lname) ONLINE;

CREATE INDEX idx3 on tab1(col3) INVISIBLE;
CREATE OPAQUE TYPE fixlen_typ(INTERNALLENGTH=8, CANNOTHASH);
CREATE OPAQUE TYPE varlen_typ
(INTERNALLENGTH=VARIABLE, MAXLEN=1024);

CREATE OPCLASS abs_btree_ops FOR btree
   STRATEGIES (abs_lt, abs_lte, abs_eq, abs_gte, abs_gt)
   SUPPORT (abs_cmp);
CREATE INDEX zip_ix ON customer(zipcode);

CREATE PROCEDURE raise_prices ( per_cent INT)
	UPDATE stock SET unit_price = unit_price + (unit_price * (per_cent/100) );
END PROCEDURE

CREATE PROCEDURE raise_prices ( per_cent INT, selected_unit CHAR )
	UPDATE stock SET unit_price = unit_price + (unit_price * (per_cent/100) )
	where unit=selected_unit;
END PROCEDURE

CREATE PROCEDURE raise_prices ( per_cent INT ) SPECIFIC
  raise_prices_all
	UPDATE stock SET unit_price = unit_price + (unit_price * (per_cent/100) );
END PROCEDURE

CREATE PROCEDURE raise_prices ( per_cent INT, selected_unit CHAR )
  SPECIFIC raise_prices_by_unit
	UPDATE stock SET unit_price = unit_price + (unit_price * (per_cent/100) )
	where unit=selected_unit;
END PROCEDURE

DROP SPECIFIC PROCEDURE raise_prices_by_unit;

DROP PROCEDURE raise_prices(INT, CHAR);

create procedure testproc() returning char(50);
    return "this is the original procedure";
end procedure;

CREATE PROCEDURE raise_prices ( per_cent INT )
   UPDATE stock SET unit_price =
      unit_price + (unit_price * (per_cent/100));
END PROCEDURE

CREATE PROCEDURE check_owner ( owner lvarchar )
   EXTERNAL NAME "/usr/lib/ext_lib/genlib.so(unix_owner)"
   LANGUAGE C
END PROCEDURE;
create procedure from 'raise_pr.sql';

CREATE ROLE engineer;
GRANT USAGE ON LANGUAGE SPL TO engineer;
GRANT engineer TO kaycee;
SET ROLE engineer;

CREATE ROUTINE FROM 'del_ord.sql';

CREATE ROW TYPE people_t
(
   name     VARCHAR(40) NOT NULL,
   address  VARCHAR(35),
   city     VARCHAR(25),
   bdate    DATE
);
CREATE TABLE birthdays OF TYPE people_t
   LOCK MODE ROW;

CREATE ROW TYPE employee_t (salary NUMERIC(10,2),
   bonus NUMERIC(10,2)) UNDER person_t;

CREATE ROW TYPE serialtype (s serial, s8 serial8);

CREATE ROW TYPE row1 (field1 byte IN blobspace1); --INVALID CODE

CREATE SCHEMA AUTHORIZATION sarah
   CREATE TABLE mytable (mytime DATE, mytext TEXT)
   GRANT SELECT, UPDATE, DELETE ON mytable TO rick
   CREATE VIEW myview AS
      SELECT * FROM mytable WHERE mytime > '12/31/2004'
   CREATE INDEX idxtime ON mytable (mytime);

CREATE SECURITY LABEL MegaCorp.label1
   COMPONENT levels 'VP',
   COMPONENT compartments 'Marketing';

CREATE SECURITY LABEL MegaCorp.label2
   COMPONENT level 'Director',
   COMPONENT compartments 'HR', 'Finance',
   COMPONENT groups 'EntireRegion';
CREATE SECURITY LABEL COMPONENT aquilae
   ARRAY [ "imperator", "tribunus", "centurio", "miles", "asinus" ];

CREATE SECURITY LABEL COMPONENT departments
   SET { 'Marketing', 'HR', 'Finance' };

CREATE SECURITY LABEL COMPONENT Oakland
TREE ( 'Port' ROOT,
          'Downtown' UNDER 'Port',
          'Airport' UNDER 'Port',
          'Estuary' UNDER 'Airport',
          'Avenues' UNDER 'Downtown',
          'Hills' UNDER 'Avenues');

CREATE SECURITY LABEL WatchDog.label9
   COMPONENT departments 'Sales','CanineResources';

CREATE SECURITY POLICY WatchDog
   COMPONENTS departments
      WITH IDSLBACRULES;

CREATE SECURITY POLICY MegaCorp
   COMPONENTS levels, compartments, groups
      WITH IDSLBACRULES;

CREATE SEQUENCE seq_2
   INCREMENT BY 1 START WITH 1
   MAXVALUE 30 MINVALUE 0
   NOCYCLE CACHE 10 ORDER;

CREATE SYNONYM mysum FOR payables:jean.summary;
CREATE SYNONYM mysum FOR payables@phoenix:jean.summary;

CREATE SEQUENCE IF NOT EXISTS MySequence
   INCREMENT BY 1 START WITH 1
   MAXVALUE 8000 MINVALUE 0
   NOCYCLE CACHE 20 ORDER;

CREATE PRIVATE SYNONYM IF NOT EXISTS anaphora FOR MySequence;
CREATE PRIVATE SYNONYM litotes FOR twoSmall;

INSERT INTO primus.litotes (col1, col2)
   VALUES (primus.anaphora.NEXTVAL, primus.anaphora.NEXTVAL);
CREATE SYNONYM IF NOT EXISTS synecdoche FOR MySequence;

CREATE PUBLIC SYNONYM IF NOT EXISTS zeugma FOR MySequence;
CREATE PRIVATE SYNONYM IF NOT EXISTS pleonasm FOR MySequence;

CREATE SYNONYM asyndeton FOR MySequence;
CREATE PRIVATE SYNONYM litotes FOR MySequence;
CREATE SYNONYM emp FOR accting.employee
CREATE SYNONYM our_custs FOR customer;
CREATE PRIVATE SYNONYM our_custs FOR cust_calls;-- ERROR!!!

CREATE SYNONYM cust FOR stores_demo@training:customer;

CREATE STANDARD TABLE IF NOT EXISTS myShadowy_tab(colA INT, colB CHAR)
   WITH ERKEY, WITH CRCOLS, WITH AUDIT LOCK MODE ROW;
CREATE TABLE shadow_columns (colA INT, colB CHAR)
   STATCHANGE 25 STATLEVEL TABLE LOCK MODE PAGE; --bad options order

CREATE RAW TABLE IF NOT EXISTS rtab1
   AS
   SELECT t1col1, t1col2, t2col1
      FROM tab1, tab2
      WHERE t1col1 < 100 and t2col1 > 5;

CREATE TABLE IF NOT EXISTS qtab1
   AS
   SELECT col1+5, col2
      FROM tab1;

CREATE TABLE IF NOT EXISTS qtab1 (qcol1, col2)
   AS
   SELECT col1+5, col2
      FROM tab1;

CREATE TABLE IF NOT EXISTS permtab (fcol1, col2)
   FRAGMENT BY EXPRESSION
      fcol1 < 300 IN dbs1,
      fcol1 >=300 IN dbs2
   LOCK MODE ROW
   AS SELECT col1::FLOAT, col2
         FROM tab1;


CREATE TABLE IF NOT EXISTS Vega
   (Col1 NCHAR(134)COLUMN SECURED WITH LabelRW,
   Col2 DATE,
   Col3 CHAR(20),
   Col4 IDSSECURITYLABEL DEFAULT LabelRW)
   SECURITY POLICY company;

CREATE TABLE tab1
  (
    id VARCHAR(128) NOT NULL,
    data "informix".BSON DEFAULT '"{id:1}"::JSON',
    modcount BIGINT,
    flags INTEGER DEFAULT 12,
    PRIMARY KEY (data)
  );

CREATE TABLE tab1
  (
    id VARCHAR(128) NOT NULL,
    date DATETIME YEAR TO FRACTION(3) DEFAULT DATETIME(1971-01-01 00:00:00.000)
         YEAR TO FRACTION(3),
    modcount BIGINT,
    flags INTEGER DEFAULT 12
  );

CREATE TABLE accounts (
   acc_num INTEGER DEFAULT 1,
   acc_type CHAR(1) DEFAULT 'A',
   acc_descr CHAR(20) DEFAULT 'New Account',
   acc_date DATETIME YEAR TO DAY DEFAULT SYSDATE YEAR TO DAY,
   acc_id CHAR(32) DEFAULT CURRENT_USER);

CREATE TABLE accounts (
   acc_num   INTEGER PRIMARY KEY CONSTRAINT num,
   acc_code  INTEGER UNIQUE CONSTRAINT code,
   acc_descr CHAR(30));

CREATE TABLE newitems (
   newitem_num INTEGER,
   manucode CHAR(3) NOT NULL,
   promotype INTEGER,
   descrip CHAR(20));

CREATE TABLE accounts
   (acc_name  CHAR(12),
    acc_num   SERIAL UNIQUE CONSTRAINT acc_num);

CREATE TABLE accounts
   (acc_name  CHAR(12),
    acc_num   SERIAL PRIMARY KEY CONSTRAINT acc_num);

CREATE TABLE employee
   (
   emp_num INTEGER PRIMARY KEY,
   mgr_num INTEGER REFERENCES employee (emp_num)
   );

CREATE TABLE sub_accounts (
   sub_acc INTEGER PRIMARY KEY,
   ref_num INTEGER REFERENCES accounts (acc_num),
   sub_descr CHAR(20));

CREATE TABLE xeno_counts (
   xeno_acc INTEGER PRIMARY KEY,
   xeno_num INTEGER REFERENCES accounts (acc_num)
      CONSTRAINT xeno_constr DISABLED,
   xeno_descr CHAR(20));

CREATE TABLE hard_candy
   (candy_num INT,
    candy_flavor CHAR(20),
    FOREIGN KEY (candy_num) REFERENCES all_candy
    ON DELETE CASCADE);

CREATE TABLE my_accounts (
   chk_id   SERIAL PRIMARY KEY,
   acct1    MONEY CHECK (acct1 BETWEEN 0 AND 99999),
   acct2    MONEY CHECK (acct2 BETWEEN 0 AND 99999));

CREATE TABLE order_items
   (
   order_id SERIAL,
   line_item_id INT not null,
   unit_price DECIMAL(6,2),
   quantity INT,
   UNIQUE (order_id,line_item_id) CONSTRAINT items_constr
   );

CREATE TABLE my_accounts
   (
   chk_id   SERIAL PRIMARY KEY,
   acct1    MONEY,
   acct2    MONEY,
   CHECK (0 < acct1 AND acct1 < 99999),
   CHECK (0 < acct2 AND acct2 < 99999),
   CHECK (acct1 > acct2)
   );

CREATE TABLE sub_accounts (
   sub_acc INTEGER PRIMARY KEY,
   ref_num INTEGER NOT NULL,
   ref_type INTEGER NOT NULL,
   sub_descr CHAR(20),
   FOREIGN KEY (ref_num, ref_type) REFERENCES accounts
      (acc_num, acc_type));

CREATE ROW TYPE student_t
   (name        VARCHAR(30),
    average     REAL,
    birthdate   DATETIME YEAR TO DAY);

CREATE TABLE students OF TYPE student_t;
CREATE TABLE grad_students OF TYPE grad_student_t  UNDER students;
CREATE TABLE customer (id INT) WITH ERKEY;
CREATE TABLE customer (id int) WITH REPLCHECK;

CREATE TABLE family
   (
   id_num      SERIAL(101) UNIQUE,
   name        CHAR(40),
   nickname    CHAR(20),
   mother      CHAR(40),
   father      CHAR(40)
   )
   IN famdata;

CREATE TABLE tabwblob
   (
    image01 BLOB
   ) PUT image01 IN (sbspace01);

CREATE TABLE tabw2sblobs
   (
    image04 BLOB,
    commentary05 CLOB
   ) PUT image04 IN (sbspace01,sbspace02),
       commentary05 IN (sbspace03);

CREATE TABLE tabw2sblobs
   (
    image04 BLOB,
    commentary05 CLOB
   ) PUT image04 IN (sbspace01,sbspace02) (KEEP ACCESS TIME, MODERATE INTEG),
       commentary05 IN (sbspace03) (EXTENT SIZE 30, LOG);

CREATE TABLE greek
(alpha INTEGER,
 beta  VARCHAR(150),
 gamma CLOB,
 delta BLOB,
 eps   TEXT IN blb1)
   FRAGMENT BY EXPRESSION
   alpha <= 5 IN dbs1, alpha > 5 IN dbs2
   PUT gamma IN (sb1), delta IN (sb2);

CREATE TABLE T1 (c1 INT) FRAGMENT BY EXPRESSION
   PARTITION PART_1 (c1 = 10) IN dbs1,
   PARTITION PART_2 (c1 = 20) IN dbs1,
   PARTITION PART_3 (c1 = 30) IN dbs1,
   PARTITION PART_4 (c1 = 40) IN dbs2,
   PARTITION PART_5 (c1 = 50) IN dbs2,
   PARTITION PART_6 REMAINDER IN dbs2;

create table tab1 (n int, n2 int, n4 int, cc char(16));

create index idx1 on tab1(n) fragment by expression
        (n2 is null) in dbs1 INDEX OFF,
	remainder in dbs2;

select count(*) from tab1 where n < 1000 and n2 is not null;

create index zip_ix on customer(zipcode)
    fragment by expression
        (state = 'CA') in dbs1,
        remainder in INDEX OFF;

with cte(n) as (
	select 1 as n
	union all
	select n+1 from cte where n < 10000
	)
insert into tab1
  select n,
  	case when mod(n,10) == 0 then n else null end,
  	mod(n,4),
  	n from cte;
CREATE TABLE employee (id INTEGER, name CHAR(32), basepay DECIMAL (10,2),
                       varpay DECIMAL (10,2), dept CHAR(2), hiredate DATE)
       FRAGMENT BY RANGE (id)
       INTERVAL (100) STORE IN (dbs1, dbs2, dbs3, dbs4)
             PARTITION p0 VALUES IS NULL IN dbs0,
             PARTITION p1 VALUES < 200 IN dbs1,
             PARTITION p2 VALUES < 400 IN dbs2;

CREATE TABLE customer (cust_id INT, name CHAR (128), street CHAR (1024),
   state CHAR (2), zipcode CHAR (5), phone CHAR (12))
   FRAGMENT BY RANGE (cust_id)
   INTERVAL (1000000) STORE IN (dbs2, dbs1)
      PARTITION p0 VALUES < 2000000 IN dbs1,
      PARTITION p1 VALUES < 4000000 IN dbs1,
      PARTITION p2 VALUES < 6000000 IN dbs2,
      PARTITION p3 VALUES < 8000000 IN dbs3;

CREATE TABLE t1 (c1 int, d1 date, dt1 DATETIME YEAR TO FRACTION)
   FRAGMENT BY RANGE (dt1) INTERVAL (INTERVAL(25) YEAR(2) TO YEAR)
      PARTITION p1 VALUES <
      DATETIME(2006-01-01 00:00:00.00000) YEAR TO FRACTION(5) IN dbs1;

CREATE TABLE orders (order_id INT, cust_id INT,
                     order_date DATE, order_desc CHAR (1024))
   FRAGMENT BY RANGE (order_date)
   INTERVAL (NUMTOYMINTERVAL (1,'MONTH')) STORE IN (dbs1, dbs2, dbs3)
      PARTITION p0 VALUES < DATE ('01/01/2005') IN dbs1,
      PARTITION p1 VALUES < DATE ('01/01/2006') IN dbs1,
      PARTITION p2 VALUES < DATE ('01/01/2007') IN dbs2,
      PARTITION p3 VALUES < DATE ('01/01/2008') IN dbs3,
      PARTITION p4 VALUES IS NULL in dbs3;

CREATE TABLE orders1 (order_id INT, cust_id INT, order_date DATE,
                       order_desc CHAR (1024))
  FRAGMENT BY RANGE (order_date)
  INTERVAL (NUMTOYMINTERVAL (1.5,'YEAR')) STORE IN (dbs1, dbs2, dbs3)
      PARTITION p0 VALUES < DATE ('01/01/2004') IN dbs1,
      PARTITION p1 VALUES < DATE ('01/01/2006') IN dbs1,
      PARTITION p2 VALUES < DATE ('01/01/2008') IN dbs2,
      PARTITION p3 VALUES < DATE ('01/01/2010') IN dbs3;

CREATE FUNCTION mydbname
   (
   owner CHAR(255),
   table CHAR(255),
   value DATE,		-- Data type must match or must be compatible
                -- with the data type of the fragment key
   retry INTEGER
   )
   RETURNING CHAR(255)
   IF (retry > 0)
   THEN
       RETURN NULL;  -- This UDF does not handle retries: if the first call
                     -- fails, an invalid dbspace is returned, and the DML
                     -- statement that requires a new fragment also fails.
   END IF;
   IF (MONTH(value) < 7)
   THEN
       RETURN "dbs1";
   ELSE
       RETURN "dbs2";
   END IF;
END FUNCTION;

CREATE TABLE orders
     (
     order_num               SERIAL(1001),
     order_date              DATE,
     customer_num            INTEGER NOT NULL,
     ship_instruct           CHAR(40),
     backlog                 CHAR(1),
     po_num                  CHAR(10),
     ship_date               DATE,
     ship_weight             DECIMAL(8,2),
     ship_charge             MONEY(6),
     paid_date               DATE
     )
PARTITION BY RANGE(order_date) INTERVAL(1 UNITS MONTH)
STORE IN (mydbname())
PARTITION prv_partition VALUES < DATE("01/01/2010") IN mydbs;

CREATE TABLE orders
       (order_id INT, cust_id INT,
        order_date DATE, order_desc CHAR (1024))
   FRAGMENT BY RANGE (order_date)
   INTERVAL (NUMTOYMINTERVAL (1,'MONTH'))
      ROLLING (3 FRAGMENTS) DETACH
      STORE IN (dbs1, dbs2, dbs3)
      PARTITION p0 VALUES < DATE ('01/01/2014') IN dbs1,
      PARTITION p4 VALUES IS NULL in dbs3;

CREATE TABLE employee
             (emp_id INTEGER, emp_name CHAR(64),
              ssn CHAR(12), basepay FLOAT, varpay FLOAT,
              dept_id SMALLINT, hire_date DATE)
    FRAGMENT BY RANGE(emp_id)
    INTERVAL(1000)
        ROLLING ( 10 FRAGMENTS )
        LIMIT TO 100000MiB DETACH ANY
        STORE IN (dbs1, dbs2, dbs3)
        PARTITION p1 VALUES < 5000 IN dbs0,
        PARTITION p2 VALUES < 10000 IN dbs0,
        PARTITION p3 VALUES < 20000 IN dbs4;

CREATE TABLE emp_info
   (
   f_name     CHAR(20),
   l_name     CHAR(20),
   position   CHAR(20),
   start_date DATETIME YEAR TO DAY,
   comments   VARCHAR(255)
   )
EXTENT SIZE 20;

CREATE TABLE t(c int, d int) EXTENT SIZE 32 NEXT SIZE 32 COMPRESSED;


CREATE TABLE Tab5 (C1 IDSSECURITYLABEL,
   C2 int,
   C3 char (10) COLUMN SECURED WITH label6)
   SECURITY POLICY company
   LOCK MODE PAGE;
ALTER TABLE Tab5 LOCK MODE(ROW);

CREATE TABLE mybook
   (C2 int)
   IN myextspace
   USING textfile (DELIMITER=':');

CREATE TEMP TABLE tab2 (fname CHAR(15), lname CHAR(15))
   WITH NO LOG;

SET TRIGGERS FOR subtable DISABLED

CREATE TRIGGER ins_tr INSERT ON newtab
   REFERENCING new AS post_ins
   FOR EACH ROW(EXECUTE PROCEDURE nt_pct (post_ins.mc));
INSERT INTO stores_demo@dbserver1:newtab
   SELECT item_num, order_num, quantity, stock_num, manu_code,
   total_price FROM items;

CREATE TRIGGER trig1 UPDATE OF item_num, stock_num ON items
   REFERENCING OLD AS pre NEW AS post
   FOR EACH ROW(EXECUTE PROCEDURE proc1());
CREATE TRIGGER trig2 UPDATE OF manu_code ON items
   BEFORE(EXECUTE PROCEDURE proc2());
CREATE TRIGGER trig3 UPDATE OF order_num, stock_num ON items
   BEFORE(EXECUTE PROCEDURE proc3());

CREATE TRIGGER trig1 UPDATE OF a, c ON taba
   AFTER (UPDATE tabb SET y = y + 1);

CREATE TRIGGER mytrig
   SELECT OF cola ON mytab REFERENCING OLD AS pre
   FOR EACH ROW (INSERT INTO newtab VALUES('for each action'));

SELECT vcol FROM (SELECT FIRST 5 col1 FROM tab1 ORDER BY col1 ) vtab(vcol);

DELETE tab1 WHERE EXISTS
   (SELECT col2 FROM tab1 WHERE col2 > 1024);

UPDATE tab1 SET col3 = col3 + 10
   WHERE col3 > ANY
      (SELECT col3 from tab1 WHERE col3 > 1);

SELECT vcol FROM (SELECT col2 FROM tab1 ORDER BY col1 ) vtab(vcol);

CREATE TRIGGER before_trig
   INSERT ON table1    REFERENCING NEW AS new
   FOR EACH ROW
   (
   INSERT INTO backup_table1 (col1, col2)
   VALUES (new.col1, new.col2)
   );

CREATE TRIGGER up_itemqty
   UPDATE OF quantity ON items
   BEFORE(EXECUTE PROCEDURE upd_items_p1());

CREATE TRIGGER up_price
   UPDATE OF unit_price ON stock
   REFERENCING OLD AS pre NEW AS post
   FOR EACH ROW WHEN(post.unit_price > pre.unit_price * 2)
   (INSERT INTO warn_tab VALUES(pre.stock_num, pre.order_num,
      pre.unit_price, post.unit_price, CURRENT));

CREATE TRIGGER t1 UPDATE OF b ON tab1
   FOR EACH ROW (EXECUTE PROCEDURE p2() INTO delete1, d);

CREATE TRIGGER t1 INSERT ON tab1
   BEFORE (INSERT INTO tab2 SELECT * FROM tab3, 'owner1'.update_);

CREATE TRIGGER t2 UPDATE OF a ON tab1
   BEFORE (UPDATE tab2 SET a = 10, tab2.insert_ = 5);

CREATE TRIGGER t1 UPDATE OF salary ON empsal
AFTER (INSERT INTO biggap SELECT * FROM empsal WHERE salary <
   (SELECT bonus FROM mgr WHERE eno = mgr));

CREATE TRIGGER upd_totpr UPDATE OF quantity ON items
   REFERENCING OLD AS pre_upd NEW AS post_upd
   FOR EACH ROW(EXECUTE PROCEDURE
      calc_totpr(pre_upd.quantity,post_upd.quantity,
      pre_upd.total_price) INTO total_price);

INSERT INTO tab1 (cola, colb) VALUES (1,10);

CREATE TRIGGER ins_totpr INSERT ON items
   REFERENCING NEW AS new_ins
   FOR EACH ROW (EXECUTE PROCEDURE calc_totpr
      (0, new_ins.quantity, 0) INTO total_price);

CREATE TRIGGER trig1 UPDATE OF a ON tab1-- Valid
   AFTER (UPDATE tab2 SET e = e + 1);

CREATE TRIGGER trig1 UPDATE OF a ON temp1
    FOR EACH ROW (EXECUTE PROCEDURE proc(50) INTO a, e);

CREATE PROCEDURE proc(val iINT) RETURNING INT,INT;
    RETURN val+10, val+20;
END PROCEDURE;


CREATE TRIGGER del_manu
   DELETE ON manufact REFERENCING OLD AS pre_del
   FOR EACH ROW(DELETE FROM stock WHERE manu_code = pre_del.manu_code);
CREATE TRIGGER del_stock
   DELETE ON stock REFERENCING OLD AS pre_del
   FOR EACH ROW(DELETE FROM items WHERE manu_code = pre_del.manu_code);
CREATE TRIGGER del_items
   DELETE ON items REFERENCING OLD AS pre_del
   FOR EACH ROW(EXECUTE PROCEDURE log_order(pre_del.order_num));

CREATE TRIGGER trig1 INSERT ON child
   REFERENCING NEW AS new
   FOR EACH ROW
   WHEN((SELECT COUNT (*) FROM parent
      WHERE cola = new.cola) = 0)
-- parent row does not exist
   (INSERT INTO parent VALUES (new.cola));

SELECT * from a
   WHERE func_2(x, out1 # INTEGER) < 100
   AND (out1 = 12 OR out1 = 13)
   AND func_3(a, out2 # FLOAT) = "SAN FRANCISCO"
   AND out2 = 3.1416;

CREATE TRIGGER upd_nt UPDATE ON newtab
   REFERENCING NEW AS post
   FOR EACH ROW(UPDATE stores_demo@dbserver2:items
      SET quantity = post.qty WHERE stock_num = post.stock
      AND manu_code = post.mc);

UPDATE stores_demo@dbserver1:newtab
   SET qty = qty * 2 WHERE s_num = 5
   AND mc = 'ANZ';

CREATE TABLE emp (
   empno INTEGER PRIMARY KEY,
   empname CHAR(20),
   deptno INTEGER REFERENCES dept(deptno),
   startdate DATE
);
ALTER TABLE dept ADD CONSTRAINT(FOREIGN KEY (manager_num)
      REFERENCES emp(empno));

CREATE VIEW manager_info AS
   SELECT d.deptno, d.deptname, e.empno, e.empname
      FROM emp e, dept d WHERE e.empno = d.manager_num;

CREATE TRIGGER manager_info_insert
   INSTEAD OF INSERT ON manager_info    --defines trigger event
      REFERENCING NEW AS n              --new manager data
   FOR EACH ROW                         --defines trigger action
      (EXECUTE PROCEDURE instab(n.deptno, n.empno));

CREATE PROCEDURE instab (dno INT, eno INT)
   INSERT INTO dept(deptno, manager_num) VALUES(dno, eno);
   INSERT INTO emp (empno, deptno) VALUES (eno, dno);
END PROCEDURE;

INSERT INTO manager_info(deptno, empno) VALUES (08, 4232);

CREATE TRUSTED CONTEXT appserver
 USER wrjaibi
 DEFAULT ROLE MANAGER
 ENABLE
 ATTRIBUTES (ADDRESS '************')
 WITH USE FOR joe WITHOUT AUTHENTICATION,
   bob WITH AUTHENTICATION;

CREATE TRUSTED CONTEXT securerole
 USER pbird
 ENABLE
 ATTRIBUTES (ADDRESS 'example.ibm.com')
 WITH USE FOR PUBLIC WITHOUT AUTHENTICATION;

CREATE USER joe;

CREATE USER joe WITH PASSWORD "joebar";

CREATE USER phil WITH PASSWORD "joebar" ACCOUNT LOCK;

CREATE USER mary WITH PASSWORD "joebar" PROPERTIES UID 44567
GROUP(1234) HOME "/home/<USER>/osuser";

CREATE USER bill WITH PROPERTIES user "foo_os";

CREATE DEFAULT USER WITH PROPERTIES USER "tmp";

CREATE VIEW v1 AS SELECT * FROM person;

CREATE VIEW v2 OF TYPE person_t AS SELECT * FROM person;

CREATE VIEW herostock AS
   SELECT stock_num, description, unit_price, unit, unit_descr
      FROM stock WHERE manu_code = 'HRO';

CREATE VIEW newview (firstcol, secondcol) AS
         SELECT sum(cola), colb FROM oldtab;

CREATE VIEW someorders (custnum,ocustnum,newprice) AS
         SELECT orders.order_num,items.order_num,
               items.total_price*1.5
            FROM orders, items
            WHERE orders.order_num = items.order_num
            AND items.total_price > 100.00;

CREATE VIEW myview (cola, colb) AS
         SELECT colx, coly from firsttab
         UNION
         SELECT colx, colz from secondtab;

CREATE VIEW palo_alto AS
   SELECT * FROM customer WHERE city = 'Palo Alto'
      WITH CHECK OPTION

CREATE XADATASOURCE informix.NewYork USING informix.MQSeries;

CREATE XADATASOURCE TYPE 'informix'.MQSeries(
                           xa_flags    = 1,
                           xa_version  = 0,
                           xa_open     = informix.mqseries_open,
                           xa_close    = informix.mqseries_close,
                           xa_start    = informix.mqseries_start,
                           xa_end      = informix.mqseries_end,
                           xa_rollback = informix.mqseries_rollback,
                           xa_prepare  = informix.mqseries_prepare,
                           xa_commit   = informix.mqseries_commit,
                           xa_recover  = informix.mqseries_recover,
                           xa_forget   = informix.mqseries_forget,
                          xa_complete = informix.mqseries_complete);

DATABASE stores_demo@db_titinius;

SELECT DBINFO('dbhostname')
   FROM systables
   WHERE tabid = 1;

DATABASE stores_demo@training EXCLUSIVE;

deallocate collection :a_set;
deallocate descriptor :descname;

deallocate descriptor 'desc1';
deallocate row :a_row;

declare x cursor for id1;
declare y scroll cursor for id1;
declare z cursor with hold for id1;
declare x cursor for select * from customer;
declare :s cursor for select * from customer;
declare z_curs cursor for
   select * from customer_ansi
   for read only;
declare new_curs cursor for
   select * from customer_notansi
   for update;
declare name_curs cursor for
   select * from customer_notansi
   for update of fname, lname;
declare q_curs cursor for
   select * from customer where lname matches :last_name for update;
declare ins_cur cursor for
   insert into stock values
   (:stock_no,:manu_code,:descr,:u_price,:unit,:u_desc);
declare s_cur cursor for
   select fname, lname into :st_fname, :st_lname
   from orders where customer_num = 114;

declare ins_cur cursor for
   insert into stock values
   (:stock_no,:manu_code,:descr,:u_price,:unit,:u_desc);

DECLARE sc_cur SCROLL CURSOR FOR SELECT * FROM orders;
DECLARE hld_cur CURSOR WITH HOLD FOR
   SELECT customer_num, lname, city FROM customer;

declare c_master cursor with hold for
   select customer_num from customer where city = 'Pittsburgh';

declare cust_curs cursor for
   select * from customer_notansi for read only;

declare new_curs cursor for
   select * from customer_notansi for update;

declare name_curs cursor for
   select * from customer_notansi for update of fname, lname;

declare set_curs cursor for select * from table(:a_set);

declare q_curs cursor for
   select customer_num, fname, lname from customer
   where lname matches :last_name for update;

DELETE "from";

DELETE zelaine.from1;

DELETE FROM tableZ;

DELETE FROM ONLY(super_tab)   -- scope excludes child tables
   WHERE name = "johnson";

DELETE FROM items WHERE order_num < 1034;

DELETE FROM orders WHERE paid_date IN
   (SELECT paid_date FROM orders WHERE paid_date < CURRENT );

DELETE FROM stock WHERE unit_price =
   (SELECT MAX(unit_price) FROM stock );

DELETE orders WHERE paid_date < CURRENT;

DELETE FROM stock WHERE unit_price =
   (SELECT MAX(unit_price) FROM stock );

DELETE stock AS where_
   WHERE manu_code =
      (SELECT manu_code FROM where_ WHERE manu_code MATCHES 'H*');

DELETE overstock@cleveland:stock AS ocs
   WHERE manu_code =
      (SELECT manu_code FROM overstock@cleveland:stock
       WHERE manu_code MATCHES 'H*');

DELETE overstock@cleveland:stock AS ocs
   WHERE manu_code =
      (SELECT manu_code FROM ocs WHERE manu_code MATCHES 'H*');

describe curs1 using sql descriptor 'desc1';
describe curs1 using sql descriptor :desc1var;
disconnect default;
DISCONNECT CURRENT;
DISCONNECT ALL;
DROP ACCESS_METHOD T_tree RESTRICT;
DROP CAST (decimal(5,5) AS percent);
DROP AGGREGATE my_avg;
DROP DATABASE IF EXISTS stores_demo;
DROP DATABASE stores_demo@gibson95;
DROP SPECIFIC FUNCTION compare_point;
DROP FUNCTION compare (int, int);
DROP INDEX stores_demo:joed.o_num_ix;
DROP INDEX stores_demo@prod:"informix".zip_ix ;
DROP INDEX IF EXISTS idx_01 ONLINE;
DROP OPCLASS abs_btree_ops RESTRICT
DROP SPECIFIC PROCEDURE compare_point;
DROP PROCEDURE compare(int, int);
DROP ROLE engineer;
DROP ROUTINE compare(INT, INT);
DROP SPECIFIC ROUTINE compare_point;
DROP ROW TYPE postal_t RESTRICT;
DROP SECURITY LABEL witty;
DROP SECURITY LABEL COMPONENT adhesive;
DROP SECURITY POLICY best CASCADE;
DROP SEQUENCE Invoice_Numbers;
CREATE SEQUENCE Invoice_Numbers
  START 10000 INCREMENT 1 NOCYCLE ;
DROP SYNONYM cathyg.nj_cust;
DROP TABLE customer;
DROP TABLE stores_demo@accntg:joed.state;
DROP TRIGGER items_pct;
DROP TRUSTED CONTEXT cntx1;
DROP TYPE new_type RESTRICT;
DROP USER bill;
DROP VIEW cust1
DROP XADATASOURCE informix.NewYork RESTRICT;
        DROP XADATASOURCE TYPE informix.MQSeries RESTRICT;
EXECUTE del_1;
execute sel1 into sql descriptor 'desc1';
execute sel1 into descriptor pointer2;
execute sel1 into :fname, :lname using :cust_num;
execute statement_1 using :order_date, :po_num;
execute prep_stmt using sql descriptor 'desc1';
execute prep_stmt using descriptor pointer2;
EXECUTE FUNCTION
   cust_num(fname, lname, company_name) INTO :c_num;
declare f_curs cursor for
   execute function get_orders(customer_num)
   into :ord_num, :ord_date;
declare f_curs cursor for
   execute function get_orders(customer_num);
execute immediate :cdb_text1;
   EXECUTE IMMEDIATE CRTOPER || TABNAME || COLS;
EXECUTE IMMEDIATE QRYSTR;
EXECUTE PROCEDURE ifx_unload_module
   ("C:\usr\apps\opaque_types\circle.dll");
CREATE TRIGGER ins_trig_tab1 INSERT ON tab1 REFERENCING NEW AS post
  FOR EACH ROW(EXECUTE PROCEDURE proc1() WITH TRIGGER REFERENCES);

FETCH seq_curs INTO :fname, :lname;
FETCH NEXT seq_curs INTO :fname, :lname;
fetch previous q_curs into :orders;
fetch relative -10 q_curs into :orders;
fetch absolute :row_num q_curs into :orders;
fetch next ord_date;
fetch selcurs using sql descriptor 'desc';
fetch selcurs2 using descriptor sqlda_ptr;
free colors_curs;
CREATE TABLE children
(
   age         SMALLINT,
   name         VARCHAR(30),
   fav_colors            SET(VARCHAR(20) NOT NULL),
);

FLUSH icurs;
free sel_stmt;

get descriptor 'desc1' :h_count = count;
get descriptor 'demodesc' value
	:index_ :type = TYPE,
	:len = LENGTH,
	:name = NAME;

get diagnostics  exception :i
            :sqlstate_code = RETURNED_SQLSTATE,
            :class_id = CLASS_ORIGIN, :subclass_id = SUBCLASS_ORIGIN,
            :message = MESSAGE_TEXT, :messlen = MESSAGE_LENGTH;

GRANT CONNECT TO PUBLIC;
GRANT DELETE, SELECT, UPDATE (customer_num, fname, lname)
   ON customer TO mary, john;
GRANT DELETE, SELECT, UPDATE (customer_num, fname, lname)
   ON customer TO PUBLIC;
GRANT UNDER ON tab1 TO john;
GRANT ALL ON customer TO tania;
REVOKE ALL ON customer FROM PUBLIC;
GRANT ALL ON customer TO john, mary;
GRANT SELECT (fname, lname, company, city) ON customer TO PUBLIC;
GRANT USAGE ON TYPE widget TO mark;
CREATE ROW TYPE rtype1 (cola INT, colb INT);

GRANT UNDER ON ROW TYPE rtype1 TO kathy;
CREATE ROW TYPE rtype2 (colc INT, cold INT) UNDER rtype1;
GRANT EXECUTE ON ROUTINE delete_order TO finn;
GRANT USAGE ON LANGUAGE C TO developers;
GRANT ALTER ON cust_seq TO mark;
GRANT SELECT ON cust_seq TO mark;
GRANT INSERT ON table1 TO mary;
GRANT payables TO maryf WITH GRANT OPTION;
GRANT payables TO charly, gene, marvin, raoul;
GRANT INSERT ON supplier TO payables;
GRANT ALTER, INSERT, SELECT ON stock TO accounting;
GRANT DEFAULT ROLE accounting TO mary, asok, vlad;
GRANT CONNECT TO PUBLIC;
GRANT SELECT ON emptab TO emprole;
GRANT emprole TO PUBLIC;
GRANT DEFAULT ROLE emprole TO PUBLIC;
GRANT EXTEND TO 'max';
GRANT ALL ON cust_seq TO mark WITH GRANT OPTION;
REVOKE ALL ON items FROM PUBLIC;
GRANT SELECT, UPDATE ON items TO mary WITH GRANT OPTION;
GRANT SELECT, UPDATE ON items TO cathy;
GRANT SELECT ON items TO paul;
REVOKE SELECT, UPDATE ON items FROM mary;
REVOKE ALL ON items FROM PUBLIC;
GRANT ALL ON items TO tom WITH GRANT OPTION;
GRANT SELECT, UPDATE ON items TO jim AS tom;
REVOKE ALL ON items FROM tom;
REVOKE SELECT, UPDATE ON items FROM jim;
GRANT DBSECADM TO niccolo;
GRANT EXEMPTION ON RULE ALL FOR MegaCorp TO manoj, sam;
GRANT EXEMPTION ON RULE IDSLBACREADARRAY FOR MegaCorp TO lynette;
CREATE SECURITY LABEL COMPONENT
   level ARRAY ['TS','S','C','U'];

CREATE SECURITY LABEL COMPONENT
   compartments SET {'A','B','C','D'};

CREATE SECURITY LABEL COMPONENT
   groups TREE ('G1' ROOT,
                'G2' UNDER ROOT,
                'G3' UNDER ROOT);

CREATE SECURITY POLICY secPolicy COMPONENTS
   level, compartments, groups;
CREATE SECURITY LABEL secPolicy.secLabel1
   COMPONENT level 'S',
   COMPONENT compartments 'A', 'B',
   COMPONENT groups 'G2';

 GRANT SECURITY LABEL secPolicy.secLabel1
   TO sam FOR READ ACCESS;
GRANT SECURITY LABEL secPolicy.secLabel1
   TO lynette FOR READ ACCESS;
GRANT SECURITY LABEL secPolicy.secLabel3
   TO sam FOR WRITE ACCESS;

GRANT SETSESSIONAUTH ON lynette, manoj TO sam;
GRANT SETSESSIONAUTH ON PUBLIC TO lynette;

GRANT ACCESS TO bob PROPERTIES USER fred;
GRANT ACCESS TO PUBLIC PROPERTIES USER dbuser;
GRANT ACCESS TO bob PROPERTIES USER dbuser HOME "/home/<USER>/bob";
GRANT ACCESS TO bob PROPERTIES UID 101, GROUP (10011);
GRANT ACCESS TO bob PROPERTIES UID 101, GROUP (ifx_user);
GRANT ACCESS TO bob PROPERTIES USER fred;
GRANT ACCESS TO bob PROPERTIES USER fred, GROUP (ifx_user), AUTHORIZATION (DBSA);
GRANT FRAGMENT ALL ON customer (part1) TO larry;
GRANT FRAGMENT ALL ON customer (part1, part2) TO millie;
GRANT FRAGMENT INSERT ON customer (part1, part2, part3) TO helen;

GRANT INSERT ON customer TO helen;
GRANT FRAGMENT ALL ON customer (part3) TO oswald;
GRANT FRAGMENT ALL ON customer (part3) TO jerome, hilda;
GRANT FRAGMENT UPDATE ON customer (part1) TO ed;
GRANT FRAGMENT UPDATE, INSERT ON customer (part1) TO susan;
GRANT FRAGMENT ALL ON customer (part1) TO harry;
GRANT FRAGMENT UPDATE ON customer (part3) TO george WITH GRANT OPTION;
GRANT FRAGMENT DELETE ON customer (part3) TO martha AS jack;
GRANT FRAGMENT UPDATE ON customer (part3) TO fred;
INFO TABLES;
INFO COLUMNS FOR customer;
CREATE PROCEDURE test3()
   DEFINE a_list LIST(SMALLINT NOT NULL);
   SELECT list_col INTO a_list FROM table1 WHERE id = 201;
   INSERT AT 3 INTO TABLE(a_list) VALUES( 9 );
   UPDATE table1 SET list_col = a_list WHERE id = 201;
END PROCEDURE;

INSERT INTO salary VALUES ('Smith', 'Pat', 75000, USER);

CREATE TABLE tab1
   (
   int1 INTEGER,
   list1 LIST(ROW(a INTEGER, b CHAR(5)) NOT NULL),
   dec1 DECIMAL(5,2)
   );

INSERT INTO tab1 VALUES
   (
   10,
   LIST{ROW(1,'abcde'),
      ROW(POW(3,3), '=27'),
      ROW(ROUND(ROOT(126)), '=11')},
   100
   );

CREATE ROW TYPE address_t
   (
   street CHAR(20),
   city CHAR(15),
   state CHAR(2),
   zipcode CHAR(9)
   );
CREATE TABLE employee
   (
   name ROW ( fname CHAR(20), lname CHAR(20)),
   address address_t
   );

INSERT INTO employee VALUES
   (
      ROW('John', 'Williams'),
      ROW('103 Baker St', 'Tracy','CA', 94060)::address_t
   );

INSERT INTO cust_calls (customer_num, call_dtime, user_id,
                 call_code, call_descr)
   VALUES (212, CURRENT, USER, 'L', '2 days');

INSERT INTO tab002
   VALUES (SECLABEL_BY_NAME('Megacorp', 'Decca'), 45, 'A.C.Debussy');

INSERT INTO orders (orders_num, order_date, customer_num) VALUES (0, NULL, 123);
INSERT INTO result_tmp EXECUTE FUNCTION f_one();
UNLOAD TO 'file' SELECT * FROM foo_tmp;
insert into rectangles values (12, :myrect);
LOAD FROM 'new_custs' INSERT INTO jason.customer;
LOAD FROM 'C:\data\loadfile' DELIMITER ';'
   INSERT INTO orders;
LOAD FROM 'C:\tmp\prices' DELIMITER ','
   INSERT INTO norman.worktab(price,discount)

 SET ISOLATION TO COMMITTED READ LAST COMMITTED;

LOCK TABLE orders IN EXCLUSIVE MODE;
LOCK TABLE orders IN SHARE MODE;

MERGE INTO t2 AS o  USING t1 AS n ON o.f1 = n.f1
   WHEN NOT MATCHED THEN INSERT ( o.f1,o.f2)
      VALUES ( n.f1,n.f2);

MERGE INTO sale USING new_sale AS n
   ON sale.cust_id = n.cust_id
   WHEN MATCHED THEN UPDATE
      SET sale.salecount = sale.salecount + n.salecount
   WHEN NOT MATCHED THEN INSERT (cust_id, salecount)
      VALUES (n.cust_id, n.salecount);

MERGE INTO customer c
   USING ext_customer e
   ON c.customer_num=e.customer_num
WHEN MATCHED THEN
   UPDATE SET c.fname = e.fname,
           c.lname = e.lname,
           c.company = e.company,
           c.address1 = e.address1,
           c.address2 = e.address2,
           c.city = e.city,
           c.state = e.state,
           c.zipcode = e.zipcode,
           c.phone = e.phone
WHEN NOT MATCHED THEN
   INSERT (c.fname, c.lname, c.company, c.address1, c.address2,
           c.city, c.state, c.zipcode, c.phone)
       VALUES
          (e.fname, e.lname, e.company, e.address1, e.address2,
           e.city, e.state, e.zipcode, e.phone);


MERGE INTO customer c
   USING ext_customer e
   ON c.customer_num=e.customer_num
      AND c.fname=e.fname AND c.lname=e.lname
WHEN MATCHED THEN
   UPDATE SET c.fname = e.fname,
           c.lname = e.lname,
           c.company = e.company,
           c.address1 = e.address1,
           c.address2 = e.address2,
           c.city = e.city,
           c.state = e.state,
           c.zipcode = e.zipcode,
           c.phone = e.phone
WHEN NOT MATCHED THEN
   INSERT
        (c.fname, c.lname, c.company, c.address1, c.address2,
         c.city, c.state, c.zipcode, c.phone)
        VALUES
        (e.fname, e.lname, e.company, e.address1, e.address2,
         e.city, e.state, e.zipcode, e.phone);

MERGE INTO customer c
USING ext_customer e
ON c.customer_num=e.customer_num
WHEN MATCHED THEN
UPDATE SET c.fname = e.fname,
           c.lname = e.lname,
           c.company = e.company,
           c.address1 = e.address1,
           c.address2 = e.address2,
           c.city = e.city,
           c.state = e.state,
           c.zipcode = e.zipcode,
           c.phone = e.phone ;

MERGE INTO customer c
   USING ext_customer e
      ON c.customer_num=e.customer_num
      WHEN MATCHED THEN
         DELETE ;

MERGE INTO customer c
USING ext_customer e
ON c.customer_num=e.customer_num AND c.fname=e.fname
   AND c.lname=e.lname
WHEN NOT MATCHED THEN
   INSERT
      (c.fname, c.lname, c.company, c.address1, c.address2,
       c.city, c.state, c.zipcode, c.phone)
       VALUES
        (e.fname, e.lname, e.company, e.address1, e.address2,
         e.city, e.state, e.zipcode, e.phone);

MERGE INTO customer c
   USING ext_customer e
   ON c.customer_num=e.customer_num AND c.fname=e.fname AND c.lname=e.lname
WHEN NOT MATCHED THEN
     INSERT
        (c.fname, c.lname, c.company, c.address1, c.address2,
         c.city, c.state, c.zipcode, c.phone)
     VALUES
        (e.fname, e.lname, e.company, e.address1, e.address2,
         e.city, e.state, e.zipcode, e.phone)
WHEN MATCHED THEN UPDATE
       SET c.fname = e.fname,
           c.lname = e.lname,
           c.company = e.company,
           c.address1 = e.address1,
           c.address2 = e.address2,
           c.city = e.city,
           c.state = e.state,
           c.zipcode = e.zipcode,
           c.phone = e.phone ;

MERGE INTO customer c
   USING (SELECT * from ext_customer e1, orders e2
          WHERE e1.customer_num=e2.customer_num ) e
   ON c.customer_num=e.customer_num AND c.fname=e.fname
      AND c.lname=e.lname
   WHEN NOT MATCHED THEN
      INSERT (c.fname, c.lname, c.company, c.address1, c.address2,
              c.city, c.state, c.zipcode, c.phone)
      VALUES (e.fname, e.lname, e.company, e.address1, e.address2,
              e.city, e.state, e.zipcode, e.phone)
   WHEN MATCHED THEN
      UPDATE SET c.fname = e.fname,
           c.lname = e.lname,
           c.company = e.company,
           c.address1 = e.address1,
           c.address2 = e.address2,
           c.city = e.city,
           c.state = e.state,
           c.zipcode = e.zipcode,
           c.phone = e.phone ;
open s_curs;
open func_curs using :arg1, :arg2;
open selcurs using sql descriptor 'desc1';
OUTPUT TO /usr/apri l/query1 WITHOUT HEADINGS
   SELECT * FROM cust_calls WHERE call_code = 'L';
OUTPUT TO PIPE more
   SELECT customer_num, call_dtime, call_code
      FROM cust_calls;

prepare :stmtid from 'select * from customer';
prepare new_cust from
       'insert into customer(fname,lname) values(?,?)';

CREATE PROCEDURE order_city() -- defines a UDR
RETURNING INT, CHAR(50);
DEFINE c_num INT;
DEFINE c_name CHAR(50);
DEFINE c_query VARCHAR(250);
LET c_query =
 "SELECT id, city_name FROM cities ORDER BY city_name;";
PREPARE c_stmt FROM c_query;
DECLARE c_cur CURSOR FOR c_stmt;
OPEN c_cur ;
while (1 = 1)
  FETCH c_cur INTO c_num, c_name;
  IF (SQLCODE != 100) THEN
    RETURN c_num, c_name WITH RESUME;
  ELSE
    EXIT;
  END IF
END WHILE
CLOSE c_cur;
FREE c_cur;
FREE c_stmt;
END PROCEDURE;

UPDATE STATISTICS HIGH;
prepare up_sel from :up_query;
put mcode from :the_code, :the_name;
 put selcurs using sql descriptor 'desc1';
put selcurs using descriptor pointer2;
RELEASE SAVEPOINT sp45;
RENAME COLUMN customer.customer_num TO c_num;
RENAME CONSTRAINT owner.old_constraint TO new_constraint
RENAME INDEX a100_1 TO idx1;
RENAME SECURITY LABEL honesty.opaque TO transparent;
RENAME SECURITY LABEL COMPONENT architect TO accountant;
RENAME TABLE new_table TO items;
RENAME SEQUENCE new_table TO items;
RENAME TRUSTED CONTEXT cntx1 TO cntx2;
RENAME USER bill TO bob;
REVOKE DBA FROM "sam";
REVOKE DBA FROM sam;
REVOKE ACCESS FROM bob;
REVOKE ALL ON customer FROM ted;
REVOKE INDEX, ALTER ON customer FROM PUBLIC;
GRANT INDEX, ALTER ON customer TO mary;
REVOKE SELECT ON customer FROM PUBLIC;
GRANT SELECT (fname, lname, company, city) ON customer TO PUBLIC;
GRANT REFERENCES (fname, lname, company, city) ON customer TO mary;
REVOKE REFERENCES ON customer FROM mary;
GRANT REFERENCES (company, city) ON customer TO mary;
REVOKE ALL ON customer FROM hal;
REVOKE USAGE ON TYPE widget FROM mark;
CREATE ROW TYPE rtype1 (cola INT, colb INT);
GRANT UNDER ON TYPE rtype1 TO kathy;
CREATE ROW TYPE rtype2 (colc INT, cold INT) UNDER rtype1;
REVOKE UNDER ON TYPE rtype1 FROM kathy;
REVOKE EXECUTE ON ROUTINE luke.delete_order FROM mark;
REVOKE USAGE ON LANGUAGE SPL FROM PUBLIC;
GRANT USAGE ON LANGUAGE SPL TO developers;
REVOKE ALTER ON cust_seq FROM mark;
REVOKE SELECT ON cust_seq FROM mark;
REVOKE ALL ON cust_seq FROM mark;
REVOKE accounting FROM mary;
REVOKE UPDATE ON employee FROM accounting;
GRANT USAGE ON LANGUAGE SPL TO accounting;
GRANT ALL PRIVILEGES ON receivables TO accounting;
GRANT DEFAULT ROLE accounting TO mary;
REVOKE DEFAULT ROLE FROM mary;
REVOKE EXTEND FROM 'max';
REVOKE ALL ON items FROM PUBLIC;
GRANT SELECT, UPDATE ON items TO mary WITH GRANT OPTION;
GRANT SELECT, UPDATE ON items TO cathy;
GRANT SELECT ON items TO paul;
REVOKE SELECT, UPDATE ON items FROM mary;
REVOKE SELECT ON customer FROM ted RESTRICT;
REVOKE SELECT ON customer FROM ted CASCADE;
REVOKE SELECT ON customer FROM ted;
REVOKE DBSECADM FROM niccolo;
REVOKE EXEMPTION ON RULE ALL FOR MegaCorp FROM manoj, sam;
REVOKE EXEMPTION ON RULE IDSLBACREADARRAY FOR MegaCorp FROM lynette;
 GRANT SECURITY LABEL secPolicy.secLabel1
   TO sam FOR READ ACCESS;
 REVOKE SECURITY LABEL secPolicy.secLabel1
   FROM sam FOR READ ACCESS;
GRANT SETSESSIONAUTH ON lynette, manoj TO sam;
GRANT SETSESSIONAUTH ON PUBLIC TO lynette;
SET SESSION AUTHORIZATION TO 'lynette';
REVOKE SETSESSIONAUTH ON lynette, manoj FROM sam;
REVOKE SETSESSIONAUTH ON PUBLIC FROM USER lynette;
REVOKE SETSESSIONAUTH ON PUBLIC FROM USER "lynette";
REVOKE FRAGMENT UPDATE, INSERT ON customer (part1) FROM susan;
REVOKE FRAGMENT UPDATE ON customer (part1) FROM ed;
REVOKE FRAGMENT ALL ON customer (part1) FROM harry;
REVOKE FRAGMENT ALL ON customer (part1, part2) FROM millie;
REVOKE FRAGMENT ALL ON customer (part3) FROM jerome, hilda;
REVOKE FRAGMENT ALL ON customer FROM mel;
ROLLBACK;
ROLLBACK WORK;
ROLLBACK TO SAVEPOINT pt109;
SAVE EXTERNAL DIRECTIVES /*+ USE_INDEX */ /*+ ORDERED */ ACTIVE FOR
     SELECT * FROM systables;
SAVE EXTERNAL DIRECTIVES /*+ AVOID_INDEX (table1 index1)*/ /*+ FULL(table1) */
       ACTIVE FOR
           SELECT /*+ INDEX( table1 index1 ) */  col1, col2
               FROM table1, table2 WHERE table1.col1 = table2.col1;
SAVEPOINT pt109;
SELECT SKIP 10 a, b FROM tab1;
SELECT FIRST 10 a, b FROM tab1;
SELECT * FROM (SELECT FIRST 8 col1
                  FROM tab1 WHERE col1 > 50 );
SELECT FIRST 10 a, b FROM tab1 UNION SELECT a, b FROM tab2;
SELECT SKIP 50 FIRST 10 a, b FROM tab1;
INSERT INTO tab2 SELECT SKIP 10 FIRST 5 * FROM tab1;
SELECT FIRST 10 name, salary FROM emp ORDER BY salary DESC;
SELECT *
   FROM TABLE(MULTISET(SELECT FIRST 10 * FROM employees
   ORDER BY employee_id)) vt(x,y), tab2
   WHERE tab2.id = vt.x;
SELECT SKIP 50 * FROM orders ORDER BY order_date;
SELECT * FROM TABLE (MULTISET (SELECT SKIP 10 FIRST 5 a FROM tab3
                     ORDER BY a)) INTO TEMP tableName;
INSERT INTO tab1 (a) SELECT *
   FROM TABLE (MULTISET (SELECT SKIP 10 FIRST 5 a
                            FROM tab3 ORDER BY a));
SELECT DISTINCT stock_num, manu_code FROM items;
SELECT DISTINCT stock_num, manu_code FROM items
   WHERE order_num = (SELECT DISTINCT order_num FROM orders
      WHERE customer_num = 120);
SELECT DISTINCT COUNT(DISTINCT ship_weight)
       FROM orders;
SELECT COUNT (DISTINCT customer_num),
       COUNT (UNIQUE order_num),
       AVG(DISTINCT ship_charge) FROM orders;
SELECT orders.order_num, items.price FROM orders, items;
SELECT customer.customer_num ccnum, company FROM customer;
SELECT catalog_num, stock_num, cat_advert [1,15] FROM catalog;
SELECT lead_time - 2 UNITS DAY FROM manufact;
SELECT 'The first name is', fname FROM customer;
SELECT TODAY FROM cust_calls;
SELECT SITENAME FROM systables WHERE tabid = 1;
SELECT lead_time - 2 UNITS DAY FROM manufact;
SELECT customer_num + LENGTH('string') from customer;
SELECT EXTEND(res_dtime, YEAR TO SECOND) FROM cust_calls;
SELECT LENGTH(fname) + LENGTH(lname) FROM customer;
SELECT HEX(order_num) FROM orders;
SELECT MONTH(order_date) FROM orders;
SELECT SUM(total_price) FROM items WHERE order_num = 1013;
SELECT COUNT(*) FROM orders WHERE order_num = 1001;
SELECT MAX(LENGTH(fname) + LENGTH(lname)) FROM customer;
SELECT SUM(DISTINCT total_price) FROM items WHERE order_num = 1013;
SELECT COUNT(DISTINCT *) FROM orders WHERE order_num = 1001;
SELECT MAX(LENGTH(fname) + LENGTH(UNIQUE lname)) FROM customer;
SELECT AVG(SUM(dollars)) OVER() FROM sales;
SELECT m.i,
       (SELECT COUNT(n.j)
           FROM tab2 WHERE j=15) AS o
    FROM tab m, tab2 n GROUP BY 1;
SELECT A.tabid,
       (SELECT SUM(B.collength * A.rowsize)
            FROM syscolumns B WHERE B.tabid = A.tabid)
    FROM systables A WHERE A.tabid = 1;
SELECT customer_num, lname, get_orders(customer_num) n_orders
      FROM customer;
SELECT stock_num, quantity*total_price FROM customer;
SELECT price*2 doubleprice FROM items;
SELECT count(*)+2 FROM customer;
SELECT count(*)+LENGTH('ab') FROM customer;
SELECT rowcol.* FROM my_tab;
SELECT call_dtime AS minute FROM cust_calls;
 SELECT pseudo_corinthian AS pcol FROM architecture GROUP BY pcol;
SELECT fname, lname, company
   INTO :p_fname, :p_lname, :p_coname
   FROM customer WHERE customer_num = 101;
SELECT ATANH(SQRT(POW(4,2) + POW(5,2))) FROM systables;
SELECT fname, lname, order_num FROM customer, orders
   WHERE customer.customer_num = orders.customer_num;
SELECT fname, lname, order_num FROM customer c, orders o
   WHERE c.customer_num = o.customer_num;
SELECT * FROM (SELECT * FROM t1
   WHERE a IN (SELECT b FROM t2 WHERE t1.a = t2.b));
SELECT * FROM t1 ,
            LATERAL (SELECT t2.a AS t2_a
                     FROM t2 WHERE t2.a = t1.a);
SELECT d.deptno, d.deptname,
  empinfo.avgsal, empinfo.empcount
     FROM department d,
        LATERAL (SELECT AVG(e.salary) AS avgsal,
                        COUNT(*) AS empcount
                 FROM employee e
                 WHERE e.workdept=d.deptno) AS empinfo;
SELECT * FROM (SELECT * FROM t);

SELECT * FROM (SELECT * FROM t) AS s;

SELECT * FROM (SELECT * FROM t) AS s WHERE t.a = s.b;

SELECT * FROM (SELECT * FROM t) AS s, (SELECT * FROM u) AS v WHERE s.a = v.b;

SELECT * FROM (SELECT SKIP 2 col1 FROM tab1 WHERE col1 > 50 ORDER BY col1  DESC);

SELECT * FROM (SELECT col1,col3 FROM tab1
   WHERE col1 < 50 GROUP BY col1,col3 ORDER BY col3 ) vtab(vcol0,vcol1);

SELECT * FROM (SELECT * FROM t WHERE t.a = 1) AS s,
OUTER
(SELECT * FROM u WHERE u.b = 2 GROUP BY 1) AS v WHERE s.a = v.b;

SELECT * FROM (SELECT a AS colA FROM t WHERE t.a = 1) AS s,
OUTER
(SELECT b AS colB FROM u WHERE u.b = 2 GROUP BY 1) AS v
   WHERE s.colA = v.colB;

CREATE VIEW vu AS SELECT * FROM (SELECT * FROM t);

SELECT * FROM ((SELECT * FROM t) AS r) AS s;
SELECT * FROM ONLY(super_tab);
SELECT width INTO :rect_width FROM table(:myrect);
SELECT * FROM TABLE ( fibGen(10));
SELECT * FROM (T1 LEFT JOIN T2) CROSS JOIN T3 ON (T1.c1 = T2.c5)
      WHERE (T1.c1 < 100);    -- Ambiguous order of operations;

SELECT * FROM (T1 LEFT JOIN T2 ON (T1.c1 = T2.c5)) CROSS JOIN T3
      WHERE (T1.c1 < 100);    -- Unambiguous order of operations;
SELECT * FROM
( (SELECT C1,C2 FROM T3) AS VT3(V31,V32)
 LEFT OUTER JOIN
        ( (SELECT C1,C2 FROM T1) AS VT1(VC1,VC2)
        LEFT OUTER JOIN
        (SELECT C1,C2 FROM T2) AS VT2(VC3,VC4)
        ON VT1.VC1 = VT2.VC3)
ON VT3.V31 = VT2.VC3);
SELECT c.customer_num, c.company, c.phone, o.order_date
   FROM customer c LEFT JOIN orders o
      ON c.customer_num = o.customer_num;
SELECT c.customer_num, c.company, c.phone, o.order_date
   FROM customer c LEFT JOIN orders o
      ON c.customer_num = o.customer_num
         AND c.company <> "All Sports Supplies";
SELECT c.catalog_num, c.stock_num, c.manu_code, i.quantity
   FROM catalog c LEFT JOIN items i
   ON c.stock_num = i.stock_num AND c.manu_code = i.manu_code
   WHERE i.quantity IS NULL AND c.manu_code = "HRO";

SELECT c.catalog_num, c.stock_num, s.description, s.unit_price,
   s.unit_descr, c.manu_code, i.quantity
FROM (catalog c INNER JOIN stock s
   ON c.stock_num = s.stock_num
      AND c.manu_code = s.manu_code)
    LEFT JOIN items i
      ON c.stock_num = i.stock_num
         AND c.manu_code = i.manu_code
   WHERE i.quantity IS NULL
      AND c.manu_code = "HRO";

SELECT c.customer_num, c.lname, o.order_num FROM customer c,
   OUTER orders o WHERE c.customer_num = o.customer_num;

SELECT c.company, o.order_date, i.total_price, m.manu_name
   FROM customer c,
      OUTER (orders o, OUTER (items i, OUTER manufact m))
   WHERE c.customer_num = o.customer_num
      AND o.order_num = i.order_num
      AND i.manu_code = m.manu_code;

SELECT c.company, o.order_date, c2.call_descr
   FROM customer c, OUTER orders o, OUTER cust_calls c2
   WHERE c.customer_num = o.customer_num
      AND c.customer_num = c2.customer_num;

SET ENVIRONMENT SELECT_GRID_ALL DEFAULT;
SET ENVIRONMENT SELECT_GRID 'region_03'
SELECT * FROM tab2 GRID 'region_03';
SELECT * FROM tab2 GRID ALL 'region_04';
SET ENVIRONMENT GRID_NODE_SKIP ON;

SELECT order_num FROM orders
   WHERE order_date > '6/04/08';
SELECT fname, lname, company
   FROM customer
   WHERE city[1,3] = 'San';
SELECT lname, fname, company FROM customer
   WHERE state IN ('CA','WA', 'NJ');
SELECT * FROM cust_calls
   WHERE user_id NOT IN (USER );

SELECT stock_num, manu_code FROM stock
   WHERE unit_price BETWEEN 125.00 AND 200.00;
SELECT DISTINCT customer_num, stock_num, manu_code
   FROM orders, items
   WHERE order_date BETWEEN '6/1/07' AND '9/1/07';
SELECT * FROM cust_calls WHERE call_dtime
   BETWEEN (CURRENT - INTERVAL(7) DAY TO DAY) AND CURRENT;

SELECT order_num, customer_num FROM orders
   WHERE paid_date IS NULL;

SELECT * FROM customer WHERE lname LIKE 'Baxter%' ;

SELECT * FROM customer WHERE lname LIKE fname;

SELECT stock_num, manu_code FROM stock
   WHERE description LIKE '%ball';
SELECT * FROM customer WHERE company LIKE '%\%%';
SELECT * FROM customer WHERE company LIKE '%z%%' ESCAPE 'z';
SELECT stock_num, manu_code FROM stock
   WHERE description MATCHES '*ball';

SELECT * FROM customer WHERE company MATCHES '*\**';

SELECT * FROM customer WHERE company MATCHES '*z**' ESCAPE 'z';

SELECT DISTINCT customer_num FROM orders
   WHERE order_num NOT IN
      (SELECT order_num FROM items
         WHERE stock_num = 1);

SELECT stock_num, manu_code FROM stock
   WHERE NOT EXISTS
      (SELECT stock_num, manu_code FROM items
         WHERE stock.stock_num = items.stock_num AND
            stock.manu_code = items.manu_code);

SELECT DISTINCT order_num FROM items
   WHERE total_price > ALL (SELECT total_price FROM items
        WHERE order_num = 1023);

SELECT DISTINCT order_num FROM items
   WHERE total_price > ANY (SELECT total_price FROM items
        WHERE order_num = 1023);

SELECT DISTINCT order_num FROM items
   WHERE total_price > (SELECT MIN(total_price) FROM items
      WHERE order_num = 1023);

SELECT order_num FROM items
   WHERE stock_num = 9 AND quantity =
      (SELECT MAX(quantity) FROM items WHERE stock_num = 9);

SELECT order_num, lname, fname FROM customer, orders
   WHERE customer.customer_num = orders.customer_num;

SELECT DISTINCT company, stock_num, manu_code
   FROM customer c, orders o, items i
   WHERE c.customer_num = o.customer_num
      AND o.order_num = i.order_num;

SELECT x.stock_num, x.manu_code, y.stock_num, y.manu_code
   FROM stock x, stock y WHERE x.unit_price > 2.5 * y.unit_price;

SELECT company, order_num FROM customer c, OUTER orders o
   WHERE c.customer_num = o.customer_num;

SELECT empid, name, mgrid , CONNECT_BY_ISLEAF leaf
FROM employee
START WITH name = 'Goyal'
CONNECT BY PRIOR empid = mgrid;

SELECT name, LEVEL FROM employee START WITH name = 'Goyal'
   CONNECT BY PRIOR empid = mgrid;

SELECT empid, name, mgrid,
   CONNECT_BY_ISLEAF leaf, CONNECT_BY_ISCYCLE cycle
FROM employee
   START WITH name = 'Goyal'
   CONNECT BY NOCYCLE PRIOR empid = mgrid;

SELECT empid, name, mgrid, SYS_CONNECT_BY_PATH( name,'/') as hierarchy
   FROM employee
   START WITH name = 'Henry'
   CONNECT BY PRIOR empid = mgrid;

SELECT order_date, COUNT(*), paid_date - order_date
   FROM orders GROUP BY 1, 3;
SELECT order_date, paid_date - order_date
   FROM orders GROUP BY order_date, 2;

SELECT order_num, COUNT(*), SUM(total_price)
   FROM items GROUP BY order_num;

SELECT order_num, AVG(total_price) FROM items
   GROUP BY order_num HAVING COUNT(*) > 2;
SELECT customer_num, EXTEND (call_dtime, MONTH TO MONTH)
   FROM cust_calls GROUP BY 1, 2 HAVING COUNT(*) > 1;
SELECT customer_num, EXTEND (call_dtime), call_code
   FROM cust_calls GROUP BY call_code, 2, 1
   HAVING customer_num < 120;

SELECT AVG(total_price) FROM items HAVING COUNT(*) > 10;

 SELECT order_num, COUNT(*) numbers, AVG (total_price) average
    FROM items
    GROUP BY order_num
       HAVING COUNT(DISTINCT *) > 2;

SELECT col1 FROM tab1 WHERE (col1 + 8) <= ALL
   (SELECT col2 FROM tab2 UNION SELECT col3 FROM tab3);

SELECT customer_num, call_code FROM cust_calls
   WHERE call_dtime BETWEEN
          DATETIME (2007-1-1) YEAR TO DAY
      AND DATETIME (2007-3-31) YEAR TO DAY
UNION ALL
SELECT customer_num, call_code FROM cust_calls
   WHERE call_dtime BETWEEN
          DATETIME (2008-1-1)YEAR TO DAY
      AND DATETIME (2008-3-31) YEAR TO DAY;

SELECT * FROM t1 WHERE EXISTS
   (SELECT a FROM t2
   UNION
   SELECT b FROM t3 WHERE t3.c IN
      (SELECT t4.x FROM t4 WHERE t4.a4 = t2.z));

SELECT col1 FROM t1 INTERSECT SELECT col1 FROM t2;

SELECT col1 FROM t1 MINUS SELECT col1 FROM t2;

SELECT vcol FROM
   (SELECT FIRST 5 col1 FROM tab1 ORDER BY col1) vtab(vcol);

SELECT paid_date - ship_date span, customer_num FROM orders
   ORDER BY span;

SELECT ship_date FROM orders ORDER BY order_date;

SELECT ship_charge, MAX(ship_weight) maxwgt
   FROM orders GROUP BY ship_charge ORDER BY maxwgt;

SELECT * from customer ORDER BY lname[6,9];

SELECT  a_col, SUM(a_col)
   FROM tab_case
   GROUP BY a_col
   ORDER BY CASE
               WHEN a_col IS NULL
                  THEN 1
               ELSE 0 END ASC,
            AVG(a_col);

SELECT  a_col, SUM(a_col)
   FROM tab_case GROUP BY a_col
   ORDER BY CASE
               WHEN a_col IS NULL
                  THEN 1
               ELSE 0 END ASC,
            AVG(a_col),
            CASE
               WHEN AVG(a_col) IS NULL
                  THEN 1
               ELSE 0 END;
SELECT order_num, customer_num, paid_date - order_date
   FROM orders
   ORDER BY 3, 2;

SELECT * FROM cust_calls ORDER BY call_code, call_dtime;

SELECT empid, name, mgrid, LEVEL
   FROM employee
      START WITH name = 'Goyal'
      CONNECT BY PRIOR empid = mgrid
   ORDER SIBLINGS BY name;

SELECT ( SELECT tab54.tab54_col7 tab56_col0
    FROM tab54
    WHERE (tab54.tab54_col7 = -1423023 )
    ) tab56_col0,
  "" tab56_col1
FROM tab57
WHERE tab57.tab57_col1 == -6296233
ORDER BY  (
    SELECT tab54.tab54_col7 tab56_col0
    FROM tab54
    WHERE (tab54.tab54_col7 = -1423023 )
    ) NULLS FIRST,2 NULLS FIRST
INTO tab56;

SELECT ( SELECT tab54.tab54_col7 tab56_col0
    FROM tab54
    WHERE (tab54.tab54_col7 = -1423023 )
    ) tab56_col0,
  "" tab56_col1
FROM tab57
WHERE tab57.tab57_col1 == -6296233
ORDER BY
    tab56_col0     -- Substituted alias for column expression in result table)
NULLS FIRST,2 NULLS FIRST
INTO tab56;

SELECT a, b FROM tab1 LIMIT 10;

SELECT name, salary FROM emp
   ORDER BY salary DESC LIMIT 10;

SELECT *
   FROM TABLE(MULTISET(SELECT * FROM employees
   ORDER BY employee_id LIMIT 10 )) vt(x,y), tab2
   WHERE tab2.id = vt.x;

-- SELECT a, b FROM tab1 LIMIT 10 UNION SELECT a, b FROM tab2;

SELECT FIRST 5 c1, c2 FROM tab ORDER BY c3;
SELECT c1, c2 FROM tab ORDER BY c3 LIMIT 5;

SELECT SKIP 20 c1, c2 FROM tab ORDER BY c3 LIMIT 10;

SELECT * FROM (SELECT SKIP 2 col1 FROM tab1
                  WHERE col1 > 50 LIMIT 8);

SELECT FIRST 20 c1, c2 FROM tab ORDER BY c3 LIMIT 10;
SELECT LIMIT 10 c1, c2 from tab ORDER BY c3 LIMIT 20;

SELECT LIMIT 20 c1, c2 FROM tab ORDER BY c4;
SELECT c1, c2 FROM tab ORDER BY c4 LIMIT 20;

select fname, lname from customer for update

select * from customer_ansi for read only;

SELECT customer_num, call_dtime + 5 UNITS DAY slowdate
   FROM cust_calls INTO TEMP pushdate;

SELECT col1::FLOAT fcol1, col2
   FROM tab1 INTO STANDARD stab1;

SELECT t1col1, t1col2, t2col1
  FROM tab1, tab2
  WHERE t1col1 < 100 and t2col1 > 5
    INTO RAW ptab1;

SELECT col1+5, col2
  FROM tab1
    INTO ptab1;

SELECT t1col1, t1col2, t2col1
  FROM tab1, tab2
  WHERE t1col1 < 100 and t2col1 > 5
    ORDER BY t2col2 DESC
       INTO RAW ptab2;
set autofree disabled;
set autofree disabled for x1;
 set collation "zh_cn.gb18030-2000";
set no collation;
SET CONNECTION 'con1';
SET CONNECTION 'con1' DORMANT;
SET CONNECTION CURRENT DORMANT;
SET INDEXES unq_ssn FILTERING;
SET TRIGGERS insert_trig, update_trig, delete_trig DISABLED;
SET TRIGGERS my_trig ENABLED;
SET CONSTRAINTS FOR cust_subset DISABLED;
SET CONSTRAINTS, INDEXES, TRIGGERS FOR cust_subset ENABLED;
SET TRIGGERS FOR subtable DISABLED;
SET CONSTRAINTS u100_1 DISABLED;
SET INDEXES u100_1 DISABLED;
SET CONSTRAINTS u100_2 ENABLED NOVALIDATE;
SET CONSTRAINTS r104_11 FILTERING WITHOUT ERROR;
SET CONSTRAINTS FOR orders DISABLED;
SET INDEXES FOR accounts DISABLED;
SET CONSTRAINTS refcon_1, refcon_2 FILTERING WITH ERROR;
SET CONSTRAINTS refcon_3, refcon_4 FILTERING WITHOUT ERROR;
SET DATASKIP ON dbsp1;
SET DATASKIP DEFAULT;
SET DATASKIP OFF;
SET DEBUG FILE TO 'debug' || '.out';
set deferred_prepare enabled;
 set descriptor 'desc_100' count = :count;
set descriptor 'desc1' value :itemno type = :type;
SET ENCRYPTION PASSWORD :password WITH HINT :myhint;
SET ENVIRONMENT EXTDIRECTIVES "1";
SET ENVIRONMENT AUTO_STAT_MODE OFF;
UPDATE STATISTICS HIGH FOR TABLE unseen AUTO;
SET ENVIRONMENT AUTOLOCATE "2";
SELECT sname FROM state WHERE LENGTH(sname) < 7
   INTO RAW reSulT IN dbsp07; --IN clause blocks AUTOLOCATE
SET ENVIRONMENT IFX_SESSION_LIMIT_LOCKS "1700";
SET EXPLAIN ON;
SET EXPLAIN ON AVOID_EXECUTE;
SET EXPLAIN FILE TO '/tmp/explain.out';
 SET INDEXES FOR cust_calls DISABLED;
 SET INDEXES FOR cust_calls ENABLED;
 SET INDEXES FOR cust_calls DISABLED;
 SET CONSTRAINTS FOR cust_calls DISABLED;
SET ISOLATION TO DIRTY READ WITH WARNING;
SET ISOLATION TO
   COMMITTED READ LAST COMMITTED RETAIN UPDATE LOCKS;
SET ENVIRONMENT RETAINUPDATELOCKS 'NONE';
SET ISOLATION TO COMMITTED READ LAST COMMITTED ;
SET LOCK MODE TO NOT WAIT;
SET LOCK MODE TO WAIT;
SET LOCK MODE TO WAIT 17;
SET BUFFERED LOG;
SET LOG;
SET OPTIMIZATION LOW;
SET OPTIMIZATION HIGH;
SET OPTIMIZATION FIRST_ROWS;
SET OPTIMIZATION ENVIRONMENT AVOID_FACT "";
SET OPTIMIZATION ENVIRONMENT NON_DIM '';
SET INDEXES idx1 INVISIBLE;
SET OPTIMIZATION ENVIRONMENT USE_INVISIBLE_INDEXES "TRUE";
SET OPTIMIZATION ENVIRONMENT STAR_JOIN 'FORCED';
SET OPTIMIZATION ENVIRONMENT FACT 'table1,table2, ... tableN';
SET ENVIRONMENT EXTDIRECTIVES ON;
SET ENVIRONMENT EXTDIRECTIVES '1';
SET ENVIRONMENT EXTDIRECTIVES OFF;
SET ENVIRONMENT EXTDIRECTIVES '0';
SET PDQPRIORITY 80;
SET PDQPRIORITY DEFAULT;
SET PDQPRIORITY LOW;
SET PDQPRIORITY -1;
set role NULL;
SET ROLE DEFAULT;
SET SESSION AUTHORIZATION TO 'cathl';
SET STATEMENT CACHE ON;
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
SET CONSTRAINTS ALL DEFERRED;
SET CONSTRAINTS update_const, insert_const DEFERRED;
SET USER PASSWORD OLD 'joebar' NEW 'joefoo';
START VIOLATIONS TABLE FOR orders MAX ROWS 50000;
START VIOLATIONS TABLE FOR cust_subset;
START VIOLATIONS TABLE FOR items USING exceptions, reasons;
START VIOLATIONS TABLE FOR cust_subset
   USING cust_subset_viols, cust_subset_diags;
STOP VIOLATIONS TABLE FOR cust_subset;
TRUNCATE TABLE customer;
TRUNCATE customer;
TRUNCATE TABLE state DROP STORAGE;
UNLOAD TO 'cust_file' DELIMITER '!'
   SELECT * FROM customer WHERE customer_num >= 138;
UNLOAD TO 'cust.out' DELIMITER ';'
   SELECT fname, lname, company, city FROM customer;
UNLOCK TABLE items;
UPDATE cust_view SET customer_num=10001 WHERE customer_num=101;
UPDATE ONLY(am_studies_super)
      SET advisor = "camarillo"
   WHERE advisor = "johnson";
UPDATE cust_view SET customer_num=10001 WHERE customer_cum=101;
UPDATE stock AS sets
   SET unit_price = unit_price * 0.94;

UPDATE nmosdb@wnmserver1:test
SET name=(SELECT name FROM test
   WHERE test.id = nmosdb@wnmserver1:test.id)
WHERE EXISTS(
SELECT 1 FROM test WHERE test.id = nmosdb@wnmserver1:test.id
);

UPDATE nmosdb@wnmserver1:test r_t
SET name=(SELECT name FROM test
   WHERE test.id = r_t.id)
WHERE EXISTS(
   SELECT 1 FROM test WHERE test.id = r_t.id
);

UPDATE customer
   SET city = 'Alviso'
      WHERE customer_num = 103;

UPDATE customer
   SET address1 = '1111 Alder Court', city = 'Palo Alto',
      zipcode = '94301' WHERE customer_num = 103;

UPDATE stock
   SET unit_price = unit_price * 1.07;

UPDATE orders
   SET ship_charge =
      (SELECT SUM(total_price) * .07 FROM items
         WHERE orders.order_num = items.order_num)
   WHERE orders.order_num = 1001;

UPDATE customer
   SET address1 = '123 New Street',
   address2 = null,
   city = 'Palo Alto',
   zipcode = '94303'
   WHERE customer_num = 134;

UPDATE customer
   SET fname = "gary", fname = "harry"
      WHERE customer_num = 101;

UPDATE customer
   SET (fname, lname) = ('John', 'Doe') WHERE customer_num = 101;

UPDATE manufact
   SET * = ('HNT', 'Hunter') WHERE manu_code = 'ANZ';

UPDATE items
   SET (stock_num, manu_code, quantity) =
      ( (SELECT stock_num, manu_code FROM stock
         WHERE description = 'baseball'), 2)
   WHERE item_num = 1 AND order_num = 1001;

UPDATE table1
   SET (col1, col2, col3) =
      ((SELECT MIN (ship_charge), MAX (ship_charge) FROM orders), '07/01/2007')
   WHERE col4 = 1001;

UPDATE empinfo SET name = ROW('John','Williams') WHERE emp_id =455;

UPDATE empinfo
   SET address = ROW('103 Baker St','Tracy','CA')::address_t
   WHERE emp_id = 3568;

UPDATE tab1
   SET list1 = LIST{ROW(2, 'zyxwv'),
      ROW(POW(2,6), ‘=64'),
      ROW(ROUND(ROOT(146)), ‘=12')}
   WHERE int1 = 10;

UPDATE stock SET unit_price = unit_price * 0.95
   WHERE unit_price IN
      (SELECT unit_price FROM stock WHERE unit_price > 50);

SELECT unit_price FROM stock WHERE unit_price > 50 INTO TEMP tmp1;

UPDATE stock SET unit_price = unit_price * 0.95
   WHERE unit_price IN ( SELECT * FROM tmp1 );

UPDATE t1 SET a = a + 10
   WHERE a > ALL (SELECT a FROM t1  WHERE a > 1) AND
         a > ANY (SELECT a FROM t1  WHERE a > 10) AND
          EXISTS (SELECT a FROM t1  WHERE a > 5);;

CREATE TRIGGER selt11 SELECT ON t1 BEFORE
  (UPDATE d1
     SET (c1, c2, c3, c4, c5) =
         (c1 + 1, c2 + 1, c3 + 1, c4 + 1, c5 + 1));

UPDATE t2 SET c1 = c1 +1
   WHERE c1 IN
      (SELECT t1.c1 from t1 WHERE t1.c1 > 10 );

UPDATE t1 SET a = a + 10 WHERE a in (SELECT a FROM t1 WHERE a > 1
   UNION SELECT a FROM t1, t2 WHERE a < b);

UPDATE t2 SET c1 = c1 + 1 WHERE c1 IN
   ( SELECT t1.c1 FROM t1 WHERE t1.c1 > 10);

UPDATE STATISTICS;
UPDATE STATISTICS LOW;
UPDATE STATISTICS MEDIUM FOR TABLE;
UPDATE STATISTICS FOR FUNCTION;
UPDATE STATISTICS FOR PROCEDURE;
UPDATE STATISTICS FOR ROUTINE;
UPDATE STATISTICS FOR FUNCTION someSPLroutine;
UPDATE STATISTICS FOR PROCEDURE someSPLroutine;
UPDATE STATISTICS FOR ROUTINE someSPLroutine;
UPDATE STATISTICS FOR FUNCTION someSPLroutine(INT, CHAR(140));
UPDATE STATISTICS FOR PROCEDURE someSPLroutine(INT, CHAR(140));
UPDATE STATISTICS FOR ROUTINE someSPLroutine(INT, CHAR(140));
UPDATE STATISTICS FOR SPECIFIC FUNCTION someSPLroutine;
UPDATE STATISTICS FOR SPECIFIC PROCEDURE someSPLroutine;
UPDATE STATISTICS FOR SPECIFIC ROUTINE someSPLroutine;
UPDATE STATISTICS FOR TABLE ONLY (sales_rep);
UPDATE STATISTICS FOR TABLE sales_rep;
UPDATE STATISTICS FOR TABLE orders (order_num, customer_num, ship_date);
UPDATE STATISTICS FORCE;
UPDATE STATISTICS MEDIUM FOR TABLE tableN FORCE;
UPDATE STATISTICS AUTO;
UPDATE STATISTICS MEDIUM FOR TABLE tableN AUTO;
UPDATE STATISTICS LOW FOR TABLE customer (customer_num);
SET ENVIRONMENT USTLOW_SAMPLE ON;
UPDATE STATISTICS LOW FOR TABLE items (quantity,total_price);
UPDATE STATISTICS LOW
   FOR TABLE customer (customer_num) DROP DISTRIBUTIONS;
UPDATE STATISTICS LOW
   FOR TABLE customer (customer_num) DROP DISTRIBUTIONS ONLY;
UPDATE STATISTICS DROP DISTRIBUTIONS ONLY;
UPDATE STATISTICS MEDIUM FOR TABLE orders
   RESOLUTION 4 0.90 DISTRIBUTIONS ONLY;
UPDATE STATISTICS MEDIUM FOR TABLE customer (city, state)
   SAMPLING SIZE 200 RESOLUTION 2 DISTRIBUTIONS ONLY;
UPDATE STATISTICS FOR SPECIFIC ROUTINE Perform_work;
UPDATE STATISTICS MEDIUM;
UPDATE STATISTICS MEDIUM RESOLUTION 10;
UPDATE STATISTICS MEDIUM RESOLUTION 10 .95;
UPDATE STATISTICS MEDIUM RESOLUTION 10 DISTRIBUTIONS ONLY;
UPDATE STATISTICS MEDIUM RESOLUTION 10 .95 DISTRIBUTIONS ONLY;
UPDATE STATISTICS HIGH;
UPDATE STATISTICS HIGH RESOLUTION 10;
UPDATE STATISTICS HIGH RESOLUTION 10 DISTRIBUTIONS ONLY;
UPDATE STATISTICS  FOR PROCEDURE;
UPDATE STATISTICS  FOR PROCEDURE company_proc1;
UPDATE STATISTICS  FOR PROCEDURE
   company_proc1(INT,SMALLINT,MONEY,VARCHAR(50), VARCHAR(30));
UPDATE STATISTICS FOR SPECIFIC PROCEDURE spec_cmpy;

UPDATE STATISTICS FOR FUNCTION;
UPDATE STATISTICS FOR FUNCTION square_w_default;
UPDATE STATISTICS FOR FUNCTION square_w_default(INT);
UPDATE STATISTICS FOR SPECIFIC FUNCTION spec_square;
WHENEVER SQLERROR STOP
WHENEVER SQLWARNING STOP
WHENEVER NOT FOUND CALL no_rows

WITH temp (n, fact) AS (
 SELECT 0, 1                                           -- Initial Subquery
  UNION ALL
 SELECT n+1, (n+1)*fact FROM temp       -- Recursive Subquery
        WHERE n < 9)
SELECT * FROM temp;

WITH fib(p, n) as (
    select 0, 1                              -- initial subquery
    UNION ALL                            -- ‘UNION ALL’
    select n, (p+n) from fib -- recursive subquery
             where n < 100                -- terminate condition
)
select p as fn from fib;

WITH cte AS (
    select id, pid from cycle where id = 1 UNION ALL
    select t.id, t.pid
               from cycle t, cte where t.pid = cte.id)
SELECT id, pid, iscycle from cte ;

with cte(n) as
(
    select 1
    UNION ALL
    select n+1 from cte
)
select first 2 n from cte;

CREATE PROCEDURE not_much()
   DEFINE i, j, k INT;
   CALL no_args (10,20);
   CALL yes_args (5) RETURNING i, j, k;
END PROCEDURE;

CREATE PROCEDURE not_much()
   DEFINE x INT;
LET x = 0;
BEGIN
        <<increment_x>>
        BEGIN
                LET x = x + 1;
        END;
        IF x < 10 THEN
                GOTO increment_x;
        END IF;
END;
END PROCEDURE;

CREATE PROCEDURE not_much()
   FOR isfdfds IN 1..5
   i = i +1 ;
END FOR lb_lklfor;
END PROCEDURE;

CREATE PROCEDURE not_much()
CASE i
   WHEN 1 THEN LET j = x;
   WHEN 2 THEN LET k = x;
   WHEN 3 THEN LET l = x;
   WHEN 4 THEN LET m = x;
   ELSE
      RAISE EXCEPTION 100; --invalid value
END CASE;
END PROCEDURE;

CREATE PROCEDURE case_proc( )
RETURNING CHAR(1);
DEFINE grade CHAR(1);
LET grade = 'D';
CASE grade
   WHEN 'A' THEN LET grade = 'a';
   WHEN 'B' THEN LET grade = 'b';
   WHEN 'C' THEN LET grade = 'c';
   WHEN NULL THEN LET grade = 'z';
   ELSE LET grade = 'd';
END CASE;
RETURN grade;
END PROCEDURE;

CREATE FUNCTION loop_skip()
   RETURNING INT;
   DEFINE i INT;
   DEFINE GLOBAL gl_out INT DEFAULT 23;
   DEFINE tmp INT;
   DEFINE GLOBAL d_var DATETIME YEAR TO MONTH
         DEFAULT CURRENT YEAR TO MONTH;
   DEFINE GLOBAL l_blob REFERENCES TEXT DEFAULT NULL;
  DEFINE GLOBAL gl_site CHAR(200) DEFAULT SITENAME;
  DEFINE GLOBAL gl_byte REFERENCES BYTE DEFAULT NULL;
   DEFINE word CHAR(15);
   DEFINE b_day DATE;
   DEFINE c_name LIKE customer.fname;
   DEFINE b_text REFERENCES TEXT;
   DEFINE a SET ( INT NOT NULL );

   DEFINE b MULTISET ( ROW ( b1 INT,
                      b2 CHAR(50)
                    ) NOT NULL );
   DEFINE d ROW;               -- generic ROW variable
   DEFINE area ROW ( x int, y char(10) );
   DEFINE rectv rectangle_t;   -- named ROW variable
   DEFINE c LIST( SET( INTEGER NOT NULL ) NOT NULL );
   DEFINE r rectangle_t;
   DEFINE local_var LIKE mytab.serialcol;
   DEFINE length PROCEDURE;
   DEFINE b point;
   LET r.length = 45.5;
   LET x = length (a,b,c);
   FOR i IN (3 TO 15 STEP 2)
      INSERT INTO testtable values(i, null, null);
      IF i = 11
        then CONTINUE FOR;
      END IF;
      RETURN i WITH RESUME;
   END FOR;
END FUNCTION;

CREATE FUNCTION loop_skip()
   RETURNING INT;
    FOREACH cursor1 FOR
      SELECT * INTO a FROM TABLE(b);
      IF a = 4 THEN
         DELETE FROM TABLE(b)
            WHERE CURRENT OF cursor1;
         EXIT FOREACH;
      END IF;
   END FOREACH;
END FUNCTION;

CREATE PROCEDURE ex_cont_ex()
   DEFINE i,s,j INT;
   FOR j = 1 TO 20
      IF j > 10 THEN
         CONTINUE FOR;
      END IF
      LET i,s = j,0;
      WHILE i > 0
         LET i = i -1;
         IF i = 5 THEN
            EXIT FOR;
         END IF
      END WHILE
   END FOR
END PROCEDURE;

CREATE PROCEDURE ex_cont_ex()
           <<outer>>
        LOOP
        LET x = x+1;
           <<inner>>
           WHILE ( i >10 ) LOOP
              LET x = x+1;
              EXIT inner WHEN x = 2;
              EXIT outer WHEN x > 3;
              END LOOP inner;
        LET x = x+1;
        END LOOP outer;
END PROCEDURE;

CREATE PROCEDURE ex_cont_ex()
FOR index_var IN (12 TO 21 STEP 2)
   -- statement block
END FOR;
FOR index_var = 12 TO 21 STEP 2
   -- statement block
END FOR;
END PROCEDURE;

CREATE PROCEDURE ex_cont_ex()
FOR i IN (12 TO 21 STEP 1)
   -- statement block
END FOR;
FOR i = 12 TO 21
   -- statement block
END FOR;
END PROCEDURE;

CREATE PROCEDURE ex_cont_ex()
FOR index_var IN (15 to 21 STEP 2, 21 to 15 STEP -3)
   -- statement body
END FOR;
END PROCEDURE;

CREATE PROCEDURE ex_cont_ex()
FOR c IN ('hello', (SELECT name FROM t), 'world', v1, v2)
   INSERT INTO t VALUES (c);
END FOR;
END PROCEDURE;

CREATE PROCEDURE ex_cont_ex()
FOR i IN (15,16,17,18,19,20,21)
   -- statement block
END FOR;
END PROCEDURE;

CREATE PROCEDURE for_ex ()
   DEFINE i, j INT;
   LET j = 10;
   FOR i IN (1 TO 20, (SELECT c1 FROM tab WHERE id = 1),
         j+20 to j-20, p_get_int(99),98,90 to 80 step -2)
      INSERT INTO tab VALUES (i);
   END FOR;
END PROCEDURE;

CREATE PROCEDURE ex_cont_ex()
   DEFINE i,s,j INT;
   <<for_lab>>
   FOR j = 1 TO 20
      IF j > 10 THEN
         CONTINUE FOR;
      END IF
      LET i,s = j,0;
      WHILE i > 0
         LET i = i -1;
         IF i = 5 THEN
            EXIT for_lab;
         END IF
      END WHILE
   END FOR for_lab
END PROCEDURE;

CREATE PROCEDURE foreach_ex()
   DEFINE i, j INT;
   FOREACH SELECT c1 INTO i FROM tab ORDER BY 1
      INSERT INTO tab2 VALUES (i);
   END FOREACH
   FOREACH cur1 FOR SELECT c2, c3 INTO i, j FROM tab
      IF j > 100 THEN
         DELETE FROM tab WHERE CURRENT OF cur1;
         CONTINUE FOREACH;
      END IF
      UPDATE tab SET c2 = c2 + 10 WHERE CURRENT OF cur1;
   END FOREACH
   FOREACH EXECUTE PROCEDURE bar(10,20) INTO i
      INSERT INTO tab2 VALUES (i);
   END FOREACH
END PROCEDURE; -- foreach_ex

CREATE DBA PROCEDURE IF NOT EXISTS shapes()
   DEFINE vertexes SET( point NOT NULL );
   DEFINE pnt point;
   SELECT definition INTO vertexes FROM polygons
      WHERE id = 207;
   FOREACH cursor1 FOR
      SELECT * INTO pnt FROM TABLE(vertexes); -- Semicolon not valid
      INSERT INTO tab2 VALUES (i);
   END FOREACH
END PROCEDURE;

CREATE DBA PROCEDURE IF NOT EXISTS shapes()
DEFINE employees employee_t;
DEFINE n VARCHAR(30);
DEFINE s INTEGER;
SELECT emp_list into employees FROM dept_table
   WHERE dept_no = 1057;
FOREACH cursor1 FOR
   SELECT name,salary
      INTO n,s FROM TABLE( employees ) AS e;
      INSERT INTO tab2 VALUES (i);
END FOREACH;
END PROCEDURE;

CREATE FUNCTION jump_back()
   RETURNING INT;
   DEFINE i,j INT;
   <<back>>
   LET j = j + i;
   FOR i IN (1 TO 52 STEP 5)
      IF i < 11 THEN
         LET j = j + 3;
         CONTINUE FOR;
      END IF;
      IF j > 100 THEN
         GOTO back;
      END IF;
      RETURN j WITH RESUME;
   END FOR;
END FUNCTION;

CREATE FUNCTION str_compare (str1 CHAR(20), str2 CHAR(20))
   RETURNING INT;
   DEFINE result INT;
      IF str1 > str2 THEN LET result =1;
         ELIF str2 > str1 THEN LET result = -1;
         ELSE LET result = 0;
      END IF
   RETURN result;
END FUNCTION -- str_compare

CREATE FUNCTION str_compare (str1 CHAR(20), str2 CHAR(20))
   RETURNING INT;
LET a   = c + d ;
LET a,b = c,d ;
LET expire_dt = end_dt + 7 UNITS DAY;
LET name = 'Brunhilda';
LET sname = DBSERVERNAME;
LET this_day = TODAY;
LET a,b = (SELECT c1,c2 FROM t WHERE id = 1);
LET a,b,c = (SELECT c1,c2 FROM t WHERE id = 1), 15;
LET a, b, c = func1(name = 'grok', age = 17);
LET a, b, c = 7, func2('orange', 'green');
LET a = (func1() + func2());
LET b = a;                   -- VALID CODE
LET d = function1(collection1);
LET a = function2(set1);
END FUNCTION -- str_compare

CREATE FUNCTION str_compare (str1 CHAR(20), str2 CHAR(20))
   RETURNING INT;
LOOP
LET i = i + 1;
    IF i = 5 THEN EXIT;
    ELSE
    CONTINUE;
    END IF
END LOOP;
LOOP
LET i = i + 1;
   EXIT WHEN i = 4;
END LOOP;
FOR i IN (1 TO 5) LOOP
   IF i = 5 THEN EXIT;
   ELSE
   CONTINUE;
   END IF;
END LOOP;
FOR i IN (1 TO 5) LOOP
   EXIT WHEN i = 5;
   END LOOP;
WHILE (i < 6) LOOP
   LET i = i + 1;
   IF i = 5 THEN EXIT;
   ELSE
   CONTINUE;
   END IF
END LOOP;
 <<voort>>
 LOOP
    LET x = x+1;
    <<endo>>
    WHILE ( i < 10 ) LOOP
       LET x = x+1;
          EXIT endo WHEN x = 7;
          EXIT voort WHEN x > 9;
          END LOOP endo;
       LET x = x+1;
    END LOOP voort;
END FUNCTION -- str_compare

CREATE PROCEDURE X()

    DEFINE v_cust_num CHAR(20);

    FOREACH cs_insert FOR SELECT cust_num INTO v_cust_num FROM A
        BEGIN
            ON EXCEPTION
            END EXCEPTION WITH RESUME;
            INSERT INTO B(cust_num) VALUES(v_cust_num);
        END;
    END FOREACH;

END PROCEDURE

CREATE FUNCTION add_salesperson(last CHAR(15), first CHAR(15))
   RETURNING INT;
   DEFINE x INT;
   ON EXCEPTION IN (-206) -- If no table was found, create one
      CREATE TABLE emp_list
          (lname CHAR(15),fname CHAR(15), tele CHAR(12));
      INSERT INTO emp_list VALUES -- and insert values
          (last, first, '************');
   END EXCEPTION WITH RESUME;
   INSERT INTO emp_list VALUES (last, first, '************');
   SELECT count(*) INTO x FROM emp_list;
   RETURN x;
END FUNCTION;

CREATE PROCEDURE delete_cust (cnum INT)
   ON EXCEPTION  IN (-691)    -- children exist
      BEGIN -- Begin-end so no other DELETEs get caught in here.
         ON EXCEPTION IN (-691)
            DELETE FROM another_child WHERE num = cnum;   { 1 }
            DELETE FROM orders WHERE customer_num = cnum; { 2 }
         END EXCEPTION -- for error -691
         DELETE FROM orders WHERE customer_num = cnum;    { 3 }
      END
      DELETE FROM cust_calls WHERE customer_num = cnum;   { 4 }
      DELETE FROM customer WHERE customer_num = cnum;     { 5 }
   END EXCEPTION
   DELETE FROM customer WHERE customer_num = cnum;        { 6 }
END PROCEDURE

CREATE PROCEDURE delete_cust (cnum INT)
RAISE EXCEPTION -208, 0;
RAISE EXCEPTION -746, 0, 'You broke the rules';
FOREACH SELECT c1 INTO alpha FROM sometable
IF alpha < 0 THEN
RAISE EXCEPTION -746, 0, 'a < 0 found'; -- emergency exit
END IF
END FOREACH
END PROCEDURE

CREATE FUNCTION two_returns (stockno INT)  RETURNING CHAR (15);
   DEFINE des CHAR(15);
   ON EXCEPTION (-272)     -- if user does not have select privilege
      RETURN;              -- return no values.
   END EXCEPTION;
   SELECT DISTINCT descript INTO des FROM stock
      WHERE stock_num = stockno;
   RETURN des;
END FUNCTION;

CREATE FUNCTION series (limit INT, backwards INT) RETURNING INT;
   DEFINE i INT;
   FOR i IN (1 TO limit)
      RETURN i WITH RESUME;
   END FOR;
   IF backwards = 0 THEN
      RETURN;
   END IF;
   FOR i IN (limit TO 1 STEP -1)
      RETURN i WITH RESUME;
   END FOR;
END FUNCTION; -- series

CREATE PROCEDURE sensitive_update()
   LET mailcall = 'mail headhoncho < alert';
   SYSTEM mailcall;
   SYSTEM 'mail -s violation' || user1 || ' ' || user2
                || '< violation_file';
    SYSTEM 'type errormess101 > %tmp%tmpfile.txt |
        sort >> %SystemRoot%systemlog.txt';
   SYSTEM 'del %tmp%tmpfile.txt';
END PROCEDURE; -- sensitive_update

CREATE PROCEDURE testproc()
   DEFINE i INT;
   SET DEBUG FILE TO 'C:\tmp\test.trace';
   TRACE OFF;
   TRACE 'Entering foo';
   TRACE PROCEDURE;
   LET i = test2();

   TRACE ON;
   LET i = i + 1;

   TRACE OFF;
   TRACE 'i+1 = ' || i+1;
   TRACE 'Exiting testproc';

   SET DEBUG FILE TO 'C:\tmp\test2.trace';

END PROCEDURE

CREATE PROCEDURE simp_while()
   DEFINE i INT;
   WHILE EXISTS (SELECT fname FROM customer
       WHERE customer_num > 400)
      DELETE FROM customer WHERE id_2 = 2;
   END WHILE;
   LET i = 1;
   WHILE i < 10
      INSERT INTO tab_2 VALUES (i);
      LET i = i + 1;
   END WHILE;
END PROCEDURE;

CREATE PROCEDURE ex_cont_ex()
   DEFINE i,s,j, INT;
   <<while_jlab>>
   WHILE j < 20
      IF j > 10 THEN
         CONTINUE WHILE;
      END IF
      LET i,s = j,0;
      <<while_slab>>
      WHILE i > 0
         LET i = i -1;
         IF i = 5 THEN
            EXIT while_jlab;
         END IF
      END WHILE while_slab
   END WHILE while_jlab
END PROCEDURE;

CREATE VIEW sales@boston:name_only AS
      SELECT customer_num, fname, lname FROM customer;

SELECT f(MULTISET(SELECT * FROM tab1 WHERE tab1.x = t.y))
   FROM t WHERE t.name = 'john doe';

SELECT f(MULTISET(SELECT id FROM tab1
UNION
SELECT id FROM tab2 WHERE tab2.id2 = tab3.id3)) FROM tab3;

SELECT * FROM TABLE(MULTISET(SELECT col1 FROM tab1 WHERE col1 = 100))
   AS vtab(c1),
   (SELECT col1 FROM tab1 WHERE col1 = 10) AS vtab1(vc1) ORDER BY c1;

SELECT * FROM (SELECT col1 FROM tab1 WHERE col1 = 100) AS vtab(c1),
   (SELECT col1 FROM tab1 WHERE col1 = 10) AS vtab1(vc1)
   ORDER BY c1;

SELECT * FROM (select col1 FROM tab1 WHERE col1 = 100) AS vtab(c1),
   TABLE(MULTISET(SELECT col1 FROM tab1 WHERE col1 = 10)) AS vtab1(vc1)
   ORDER BY c1;

SELECT * FROM orders
   WHERE ship_instruct = 'express'
   AND order_date > '05/01/98'
   AND ship_weight < 30;

SELECT customer_num, order_date FROM orders
   WHERE paid_date = '';

SELECT customer_num, order_date FROM orders
   WHERE NOT (paid_date !='');

SELECT customer_num, order_date FROM orders
   WHERE paid_date IS NULL;

CREATE TABLE tab_coll
(
set_num SET(INT NOT NULL),
list_name LIST(SET(CHAR(10) NOT NULL) NOT NULL)
);

SELECT customer_num, order_date FROM orders
   WHERE paid_date IS NULL
AND 5 IN set_num
AND 5.0::INT IN set_num
AND "5" NOT IN set_num
AND set_num IN ("SET{1,2,3}", "SET{7,8,9}")
AND "SET{'john', 'sally', 'bill'}" IN list_name
AND list_name IN ("LIST{""SET{'bill','usha'}"",
                  ""SET{'ann' 'moshi'}""}",
               "LIST{""SET{'bob','ramesh'}"",
                  ""SET{'bomani' 'ann'}""}")


SELECT emp_id, savings_in_401k AS employer_match FROM employee WHERE
    CASE WHEN(savings_in_401k IS NULL) THEN 0
         ELSE savings_in_401k END * 0.06 > 0;

SELECT emp_id, savings_in_401k AS employer_match FROM employee
         WHERE lname NOT LIKE 'Baxter%'
and lname NOT MATCHES 'Baxter*'
and description LIKE '%tennis%' ESCAPE '\'
and description LIKE '%\_%' ESCAPE '\'
and description MATCHES '*tennis*'

SELECT * FROM customer WHERE company LIKE '%z_%' ESCAPE 'z';

SELECT * FROM customer WHERE company MATCHES '*z?*' ESCAPE 'z';

UPDATE t1 SET a = a + 10 WHERE a IN
   (SELECT a FROM t1, t2  WHERE a > b
	   AND a IN
         (SELECT a FROM t1 WHERE a > 50 ) );
DELETE FROM t1 WHERE EXISTS
   (SELECT a FROM t1);

SELECT a FROM t1 WHERE order_num NOT IN
   (SELECT order_num FROM items WHERE stock_num = 1)

SELECT stock_num, manu_code FROM stock
   WHERE NOT EXISTS (SELECT stock_num, manu_code FROM items
      WHERE stock.stock_num = items.stock_num AND
      stock.manu_code = items.manu_code);

SELECT order_num FROM items
   WHERE stock_num = 9 AND quantity =
      (SELECT MAX(quantity) FROM items WHERE stock_num = 9);

SELECT customer_num, order_date FROM orders
   WHERE paid_date > '1/1/97' OR paid_date IS NULL;
SELECT order_num, total_price FROM items
   WHERE total_price > 200.00 AND manu_code LIKE 'H
SELECT lname, customer_num FROM customer
   WHERE zipcode BETWEEN '93500' AND '95700'
   OR state NOT IN ('CA', 'WA', 'OR');
CREATE TABLE IF NOT EXISTS bson_table(bson_col BSON);

INSERT INTO bson_table VALUES(
  '{person:{givenname:"Jim",surname:"Flynn",age:29,cars:["dodge","olds"]}}'::JSON);

SELECT bson_col::JSON FROM bson_table;

SELECT bson_col.person.surname::JSON FROM bson_table;

SELECT BSON_VALUE_LVARCHAR(bson_col, "person.surname") FROM bson_table;

CREATE INDEX idx2 ON bson_table(
   BSON_GET(bson_col, "person.surname")) USING BSON;

CREATE TABLE resume
   (
   fname         CHAR(15),
   lname         CHAR(15),
   phone         CHAR(18),
   recd_date     DATETIME YEAR TO HOUR,
   contact_date  DATETIME YEAR TO HOUR,
   comments      VARCHAR(250, 100),
   vita          TEXT IN TABLE,
   photo         BYTE IN photo_space
   )
   IN employ;

CREATE ROW TYPE row_t ( w INT, y INT);
CREATE ROW TYPE rowspace_t ( u INT, v row_t, z DATE);

SELECT (total_price * 2) FROM items
   WHERE order_num = 1001;
SELECT times(total_price, 2) FROM items
   WHERE order_num = 1001;

SELECT order_num, ship_charge/ship_weight FROM orders
   WHERE order_num = 1023;

SELECT BITOR(8, 20) AS bitor FROM systables WHERE tabid = 1;

SELECT BITNOT(-20) AS bitnot FROM systables WHERE tabid = 1;

SELECT LOTOFILE(mybytecol::blob, 'fname', 'client')
   FROM mytab
   WHERE pkey = 12345;

UPDATE newtab SET myclobcol = mytextcol::clob;

SELECT newtable.col0, null::int FROM newtable;

INSERT INTO bson_table VALUES(
   '{person:{givenname:"Jim",surname:"Flynn",age:29,cars:["dodge","olds"]}}'
   ::JSON::BSON);

SELECT rect.* FROM rectangles
   WHERE area = 64;

SELECT lname FROM customer WHERE phone[5,7] = '356';

SELECT *, ROWID FROM customer;

SELECT fname, ROWID FROM customer ORDER BY ROWID;

SELECT HEX(rowid) FROM customer WHERE customer_num = 106;

SELECT cust_name,
   CASE
   WHEN number_of_problems = 0
      THEN 100
   WHEN number_of_problems > 0 AND number_of_problems < 4
      THEN number_of_problems * 500
   WHEN number_of_problems >= 4 and number_of_problems <= 9
      THEN number_of_problems * 400
   ELSE
      (number_of_problems * 300) + 250
   END,
   cust_address
FROM custtab

SELECT title, CASE movie_type
      WHEN 1 THEN 'HORROR'
      WHEN 2 THEN 'COMEDY'
      WHEN 3 THEN 'ROMANCE'
      WHEN 4 THEN 'WESTERN'
      ELSE 'UNCLASSIFIED'
   END,
   our_cost FROM movie_titles;

SELECT fname, COALESCE (addr, 'Address unknown') AS address
   FROM employees;

SELECT fname, NVL (addr, 'Address unknown') AS address
   FROM employees;

SELECT name, answer, NULLIF(answer, 'f') FROM booktab;

SELECT firstname, DECODE(evaluation,
   'Poor', 0,
   'Fair', 25,
   'Good', 50,
   'Very Good', 75,
   'Great', 100,
   -1) as grade
FROM students;

SELECT 'The first name is ', fname FROM customer;

INSERT INTO manufact VALUES ('SPS', 'SuperSport');

UPDATE cust_calls SET res_dtime = '2007-1-1 10:45'
   WHERE customer_num = 120 AND call_code = 'B';

INSERT INTO items VALUES (4, 35, 52, 'HRO', 12, 4.00);

INSERT INTO acreage VALUES (4, 5.2e4);

SELECT unit_price + 5 FROM stock;

SELECT -1 * balance FROM accounts;

INSERT INTO cust_calls VALUES
   (221,CURRENT,USER,'B','Decimal point off', NULL, NULL);

SELECT * FROM cust_calls WHERE user_id = USER;

UPDATE cust_calls SET user_id = USER WHERE customer_num = 220;
select CURRENT_ROLE FROM systables WHERE tabid = 1;

SELECT DBSERVERNAME FROM customer;

INSERT INTO host_tab VALUES ('1', SITENAME);

SELECT * FROM host_tab WHERE site_col = DBSERVERNAME;

UPDATE customer SET company = SITENAME
    WHERE customer_num = 120;

UPDATE orders order_date SET order_date = TODAY
    WHERE order_num = 1005;

INSERT INTO orders VALUES
   (0, TODAY, 120, NULL, N, '1AUE217', NULL, NULL, NULL, NULL);

SELECT * FROM orders WHERE ship_date = TODAY;
CREATE TABLE new_acct (col1 INT, col2 DATETIME YEAR TO DAY
   DEFAULT CURRENT YEAR TO DAY);

CREATE TABLE tab1 (
id SERIAL,
value CHAR(20),
time1 DATETIME YEAR TO FRACTION(5) DEFAULT SYSDATE,
time2 DATETIME YEAR TO SECOND DEFAULT SYSDATE YEAR TO SECOND
);

SELECT *, DAY(time1) AS day FROM tab1
   WHERE DAY(time1) = DAY(SYSDATE);

SELECT DATETIME (2007-12-6) YEAR TO DAY FROM customer;

UPDATE cust_calls SET res_dtime = DATETIME (2008-07-07 10:40)
         YEAR TO MINUTE
   WHERE customer_num = 110
   AND call_dtime = DATETIME (2008-07-07 10:24) YEAR TO MINUTE;

SELECT * FROM cust_calls
   WHERE call_dtime
   = DATETIME (2008-12-25 00:00:00) YEAR TO SECOND;

INSERT INTO manufact VALUES ('CAT', 'Catwalk Sports',
   INTERVAL (16) DAY TO DAY);

SELECT lead_time + INTERVAL (5) DAY TO DAY FROM manufact;

SELECT lead_time + 5 UNITS DAY FROM manufact;

SELECT * FROM cust_calls WHERE (TODAY - call_dtime) > 30 UNITS DAY;

UPDATE manufact SET lead_time = 2 UNITS DAY + lead_time
   WHERE manu_code = 'ANZ';

CREATE SEQUENCE seq_2
   INCREMENT BY 1 START WITH 1
   MAXVALUE 30 MINVALUE 0
   NOCYCLE CACHE 10 ORDER;

INSERT INTO tab1 (col1, col2)
   VALUES (seq_2.NEXTVAL, seq_2.NEXTVAL);

SELECT seq_2.CURRVAL, seq_2.NEXTVAL FROM tab1;

INSERT INTO employee VALUES
   (ROW('103 Baker St', 'San Francisco',
      'CA', 94500));

UPDATE rectangles
   SET rect = ROW(8, 3, 7, 20)
   WHERE area = 140;

update table(:a_row)
   set x=0, y=0, length=10, width=20;

SELECT row_col FROM tab_b
   WHERE ROW(17, 'abc') IN (row_col);

INSERT INTO tab_a (set_col) VALUES ("SET{6, 9, 3, 12, 4}");

INSERT INTO TABLE(a_set) VALUES (9765);

UPDATE table1 SET set_col = "LIST{3}";

SELECT set_col FROM table1
   WHERE SET{17} IN (set_col);

CREATE TABLE new_tab
(
col1 row_t,
col2 ROW( a CHAR(2), b INT)
);

INSERT INTO new_tab
VALUES
(
ROW(32, 65)::row_t,
ROW('CA', 34)
);

SELECT * FROM person_tab
   WHERE col1 = ROW('charlie','hunter')::person_t;

CREATE FUNCTION f (a int) RETURNS int;
   RETURN a+1;
END FUNCTION;

CREATE TABLE tab1 (x SET(INT NOT NULL));

INSERT INTO tab1 VALUES
(
SET{10,
   1+2+3,
   f(10)-f(2),
   SQRT(100) +POW(2,3),
   (SELECT tabid FROM systables WHERE tabname = 'sysusers'),
   'T'::BOOLEAN::INT}
);

SELECT * FROM tab1 WHERE
   x=SET{10,
         1+2+3,
         f(10)-f(2),
         SQRT(100) +POW(2,3),
   (SELECT tabid FROM systables WHERE tabname = 'sysusers'),
   'T'::BOOLEAN::INT};

SELECT zip_code_t FROM address
   WHERE address.city = find_location(32.1, 35.7, rank # INT)
   AND rank < 101;

SELECT SUM(total_price) FROM items WHERE order_num = 1013;

SELECT COUNT(*) FROM orders WHERE order_num = 1001;

SELECT MAX(LENGTH(fname) + LENGTH(lname)) FROM customer;

SELECT COUNT(*) FROM stock WHERE manu_code = 'HRO';

SELECT c,d,
   SUM(d) OVER(
      PARTITION BY a,b
      ORDER BY c,d
      ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING)
   FROM table1;

SELECT ROW_NUMBER()
   OVER(PARTITION BY pkg_type ORDER BY prod_name)
   AS rownum, prod_name, pkg_type
FROM product;

SELECT name, salary,  LAG(salary)
                 OVER (PARTITION BY dept ORDER BY salary),
                      LEAD(salary, 1, 0)
                 OVER (PARTITION BY dept ORDER BY salary)
FROM employee;

SELECT emp_num, sales,
  DENSE_RANK() OVER (ORDER BY sales) AS dense_rank
  FROM sales;

SELECT name, salary,
   NTILE(5) OVER (PARTITION BY dept ORDER BY salary)
FROM employee;

SELECT price, price – FIRST_VALUE(price)
          OVER (PARTITION BY year ORDER BY tradingday)
          AS diff_price
FROM stock_price
WHERE tradingday between ‘2012-11-01’ and ‘2012’-11-07’;

SELECT city, SUM(dollars) AS SALES,
   RATIO_TO_REPORT(SUM(dollars)) OVER() *100 AS RATIO_DOLLARS
   FROM sales, store, period
   WHERE sales.store_id = store.store_id
      AND sales.period_id = period.period_id
   GROUP BY city
   ORDER BY sales DESC;

SELECT RANGE(age) OVER () FROM u_pop;

   SELECT symbol, tradingdate,
     AVG(closeprice) OVER (PARTITION BY symbol
       ORDER BY tradingdate
     ROWS BETWEEN 29 PRECEDING AND CURRENT ROW)
   FROM dailystockdata
   WHERE symbol IN ('ABC', 'XYZ')
     AND tradingdate BETWEEN '2012-01-01' AND '2012-12-31';

SELECT price,
       AVG(price) OVER (ORDER BY tradingday
                 ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING)
FROM stock_price
WHERE tradingday BETWEEN '2012-11-01' AND '2012-11-07';

SELECT customer_num, ship_date, ship_charge,
       COUNT(*) OVER (PARTITION BY customer_num)
FROM orders
WHERE customer_num <= 110;

  SELECT sales, SUM(sales) OVER (ORDER BY quarter)
     FROM sales WHERE year = 2013

SELECT team2, player2, points2,
   AVG(points1) OVER(PARTITION BY tea2m ORDER BY points2
      ROWS BETWEEN 1 PRECEDING AND CURRENT ROW) AS olap_avgs
FROM points2;

SELECT player, age, team, points,
   AVG(points) OVER(PARTITION BY team ORDER BY age
      RANGE BETWEEN CURRENT ROW AND 9 FOLLOWING) AS olap_avg
FROM points_age;

INSERT INTO table1 (set_col) VALUES (SET{6, 9, 9, 4});

CREATE TABLE tab5 (set_col SET(SET(INT NOT NULL) NOT NULL));

CREATE PROCEDURE not_much()
DEFINE my_date DATE;
DEFINE my_dt DATETIME YEAR TO SECOND;
LET my_date = CURRENT;
LET my_dt =
   ('2008-02-22 05:58:44.000')::DATETIME YEAR TO SECOND;
   LET my_date = ('2008-02-22 05:58:44.000');
LET my_date = ('2008-02-22 05:58:44.000')::DATE;
LET my_date =
   ('2008-02-22 05:58:44.000')::DATETIME YEAR TO SECOND::DATE;
   EXTEND (DATETIME (2007-8-1) YEAR TO DAY, YEAR TO MINUTE)
   - INTERVAL (720) MINUTE (3) TO MINUTE;
END PROCEDURE;

CREATE TABLE rectangles
(
   area FLOAT,
   rect ROW(x INTEGER, y INTEGER, length FLOAT, width FLOAT),
)

RENAME COLUMN 'Owner'.table2.collum3 TO column3;

EXECUTE PROCEDURE IFX_ALLOW_NEWLINE('T');

SELECT 'The quick brown fox
   jumped over the old gray fence'
   FROM customer
   WHERE customer_num = 101;

SELECT lname FROM customer WHERE phone [5,7] = '356';

EXECUTE PROCEDURE add_col (t ='customer', d ='integer',
   n ='newint');

EXECUTE PROCEDURE add_col ('customer','newint','integer') ;

SELECT * FROM TABLE(MULTISET(SELECT SKIP 50 FIRST 20 * FROM employees
   ORDER BY employee_id)) vt(x,y), tab2 WHERE tab2.id = vt.x;

SELECT emp_id, emp_name, emp_salary
   FROM  TABLE(MULTISET(SELECT SKIP 40 LIMIT 20 id, name, salary
                           FROM e1, e2
                           WHERE e1.id = e2.id ORDER BY salary ))
   AS etab(emp_id, emp_name, emp_salary);

SELECT * FROM TABLE(MULTISET(SELECT col1 FROM tab1 WHERE col1 = 100))
   AS vtab(c1),
   (SELECT col1 FROM tab1 WHERE col1 = 10) AS vtab1(vc1) ORDER BY c1;

SELECT * FROM (SELECT col1 FROM tab1 WHERE col1 = 100) AS vtab(c1),
   (SELECT col1 FROM tab1 WHERE col1 = 10) AS vtab1(vc1)
      ORDER BY c1;

SELECT COUNT(*)
   FROM parents, TABLE(parents.children) c_table
   WHERE parents.id = 1001;

SELECT (SELECT COUNT(*)
      FROM TABLE(parents.children) c_table)
   FROM parents WHERE parents.id = 1001;

CREATE ROW TYPE person (name CHAR(255), id INT);
CREATE TABLE parents
   (
   name CHAR(255),
   id INT,
   children LIST (person NOT NULL)
   );
CREATE TABLE parents2
   (
   name CHAR(255),
   id INT,
   children_ids LIST (INT NOT NULL)
   );

CREATE PROCEDURE test6()
   DEFINE a SMALLINT;
   DEFINE b SET(SMALLINT NOT NULL);
   SELECT set_col INTO b FROM table1
      WHERE id = 6;
      -- Select the set in one row from the table
      -- into a collection variable
   FOREACH cursor1 FOR
      SELECT * INTO a FROM TABLE(b);
         -- Select each element one at a time from
         -- the collection derived table b into a
      IF a = 4 THEN
         DELETE FROM TABLE(b)
            WHERE CURRENT OF cursor1;
            -- Delete the element if it has the value 4
         EXIT FOREACH;
      END IF;
   END FOREACH;
   UPDATE table1 SET set_col = b
      WHERE id = 6;
      -- Update the base table with the new collection
END PROCEDURE;

EXECUTE FUNCTION hr_db@remoteoffice:johan.suggestion_box(0);

CREATE FUNCTION delete_order(int) RETURNING int
   EXTERNAL NAME 'informix.demo_jar:delete_order.delete_order()'
   LANGUAGE JAVA;

CREATE FUNCTION equal( a point, b point ) RETURNING BOOLEAN;
   EXTERNAL NAME "/usr/lib/point/lib/libbtype1.so(point1_equal)"
   LANGUAGE C
END FUNCTION;













