parser grammar InformixParser;

options {
    tokenVocab = InformixLexer;
}

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=syntax-sql-statements
root
    : (
    allocateCollection
    | allocateDescriptor
    | allocateRow
    | alterAccessMethod
    | alterFragment
    | alterFunction
    | alterIndex
    | alterProcedure
    | alterRoutine
    | alterSecurityLabelComponent
    | alterSequence
    | alterTable
    | alterTrustedContext
    | alterUser
    | beginWork
    | closeDatabase
    | close
    | commitWork
    | connect
    | createAccessMethod
    | createAggregate
    | createCast
    | createDatabase
    | createDistinctType
    | createExternalTable
    | createFunction
    | createFunctionFrom
    | createIndex
    | createOpaqueType
    | createOpclass
    | createProcedure
    | createProcedureFrom
    | createRole
    | createRoutineFrom
    | createRowType
    | createSchema
    | createSecurityLabel
    | createSecurityLabelComponent
    | createSecurityPolicy
    | createSequence
    | createSynonym
    | createTable
    | createTempTable
    | createTrigger
    | createTrustedContext
    | createUser
    | createView
    | createXadatasource
    | createXadatasourceType
    | database
    | deallocate
    | declare
    | delete
    | describe
    | disconnect
    | dropAccessMethod
    | dropAggregate
    | dropCast
    | dropDatabase
    | dropFunction
    | dropIndex
    | dropOpclass
    | dropProcedure
    | dropRole
    | dropRoutine
    | dropRowType
    | dropSecurity
    | dropSequence
    | dropSynonym
    | dropTable
    | dropTrigger
    | dropTrustedContext
    | dropType
    | dropUser
    | dropView
    | dropXadatasource
    | dropXadataTypeSource
    | execute
    | fetch
    | flush
    | free
    | getDescriptor
    | getDiagnostics
    | grant
    | info
    | insert
    | load
    | lockTable
    | merge
    | open
    | output
    | prepare
    | put
    | releaseSavepoint
    | renameColumn
    | renameConstraint
    | renameDatabase
    | renameIndex
    | renameSecurity
    | renameSequence
    | renameTable
    | renameTrustedContext
    | renameUser
    | revoke
    | rollbackWork
    | saveExternalDirectives
    | savepoint
    | select
    | set
    | startViolationsTable
    | stopViolationsTable
    | truncateTable
    | unload
    | unlockTable
    | update
    | updateStatistics
    | whenever
    ) SEMI_? EOF
    ;

allocateCollection
    : ALLOCATE COLLECTION identifierVariable
    ;

allocateDescriptor
    : ALLOCATE DESCRIPTOR descriptor=stringVariable (WITH MAX items=numberVariable)?
    ;

allocateRow
    : ALLOCATE ROW identifierVariable
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-alter-access-method-statement
alterAccessMethod
    : ALTER ACCESS_METHOD accessMethodName  alterAccessMethodOption (COMMA_ alterAccessMethodOption)*
    ;

accessMethodName
    : objectName
    ;

alterAccessMethodOption
    : (MODIFY | ADD) purposeOption
    | DROP purpose_keyword=identifier
    ;

purposeOption
    : purposetask = identifier EQ_ objectName
    | purposevalue = identifier EQ_ (stringVariable | numberVariable)
    | purposeFlag = identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-alter-fragment-statement
alterFragment
    : ALTER FRAGMENT ONLINE? (alterFragmentOnTableClause | alterFragmentOnIndexClause)
    ;

alterFragmentOnTableClause
    : ON TABLE tableName (attachClause | detachClause | initClause | addClause | dropClause | modifyClause)
    ;

alterFragmentOnIndexClause
    : ON INDEX indexName (initClause | addClause | dropClause | modifyClause)
    ;

attachClause
    : ATTACH (tableName attachAsClause?) (COMMA_ tableName attachAsClause?)*
    ;

attachAsClause
    : AS (PARTITION new_frag=identifier)? (
    (expr | listExpressionClause) ((AFTER | BEFORE) old_frag=identifier)?
    | rangeIntervalExpression
    | REMAINDER
    )
    ;

rangeIntervalExpression
    : VALUES LT_ constantExpression | VALUES IS NULL
    ;

detachClause
    : DETACH PARTITION? fragmentName=identifier new_table=identifier
    ;

initClause
    : INIT (WITH ROWIDS)? (fragmentByClauseForTables | fragmentByClauseForIndexes | partition? inSpace)
    ;

partition
    : PARTITION part=identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=clause-fragment-by-tables#ids_sqs_0260
fragmentByClauseForTables
    : (FRAGMENT | PARTITION) BY (
    fragmentByRoundRobin
    | fragmentByExpressionList
    | fragmentByRange
    | fragmentByList
    )
    ;

fragmentByClauseForIndexes
    : (FRAGMENT | PARTITION) BY (
    fragmentByExpression
    | fragmentByRange
    | fragmentByList
    )
    ;

fragmentByExpressionList
    : EXPRESSION fragmentListClause
    ;
fragmentListClause
    : fragmentListItem (COMMA_ fragmentListItem)*
    ;

fragmentListItem
    : partition? ( LP_ expr RP_ | REMAINDER) inSpace
    ;

addClause
    : ADD (
    (partition? REMAINDER IN)? (partition IN)? space=identifier
    | partition? addExpression inSpace ((BEFORE | AFTER) fragmentName=identifier)?
    | INTERVAL STORE? IN LP_ identifier (COMMA_ identifier)* RP_
    )
    ;

addExpression
    : listExpressionClause
    | rangeIntervalExpression
    | expr
    ;

dropClause
    : DROP (
    PARTITION? fragmentName=identifier
    | INTERVAL STORE? IN LP_ identifier (COMMA_ identifier)* RP_
    )
    ;

modifyClause
    : MODIFY (modifyPartitionItem | modifyIntervalItem) (COMMA_ (modifyPartitionItem | modifyIntervalItem))*
    ;

modifyPartitionItem
    : PARTITION? old=identifier TO (PARTITION new=identifier)? fragmentExpression? inSpace?
    ;

modifyIntervalItem
    : INTERVAL(
    (ENABLED | DISABLED)? STORE? IN LP_ (identifier (COMMA_ identifier)* | userDefinedFunction) RP_
    | (ENABLED | DISABLED)
    | TRANSITION TO constantExpression
    | rollingWindowClause
    )
    ;

fragmentExpression
    : listExpressionClause
    | rangeIntervalExpression
    | expr
    | REMAINDER
    ;

alterFunction
    : ALTER (
    FUNCTION functionName (LP_ (dataType (COMMA_ dataType)*)?  RP_)?
    | SPECIFIC FUNCTION functionName
    ) alterWithClause
    ;

alterWithClause
    : WITH LP_ alterWithOption (COMMA_ alterWithOption)* RP_
    ;

alterWithOption
    : DROP dropRoutineModifier
    | (ADD | MODIFY) addOrModifyRoutineModifier
    | MODIFY EXTERNAL NAME EQ_ sharedObjectFile
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=segments-routine-modifier#ids_sqs_1719
routineModifier
    : dropRoutineModifier | addOrModifyRoutineModifier
    ;

dropRoutineModifier
    : NOT? VARIANT | NEGATOR | CLASS | ITERATOR | PARALLELIZABLE | HANDLESNULLS | INTERNAL | COSTFUNC
    | PERCALL_COST | SELFUNC | SELCONST | STACK
    ;

addOrModifyRoutineModifier
    : NOT? VARIANT
    | NEGATOR EQ_ identifier
    | CLASS EQ_ identifier
    | ITERATOR | PARALLELIZABLE | HANDLESNULLS | INTERNAL
    | PERCALL_COST EQ_ numberLiterals
    | COSTFUNC EQ_ identifier
    | SELFUNC EQ_ identifier
    | SELCONST EQ_ numberLiterals
    | STACK EQ_ numberLiterals
    ;

sharedObjectFile
    : stringVariable
    ;

alterIndex
    : ALTER INDEX indexName TO NOT? CLUSTER
    ;

alterProcedure
    : ALTER (
    PROCEDURE procedureName (LP_ (dataType (COMMA_ dataType)*)?  RP_)?
    | SPECIFIC PROCEDURE procedureName
    ) alterWithClause
    ;

alterRoutine
    : ALTER (
    ROUTINE  routineName (LP_ (dataType (COMMA_ dataType)*)?  RP_)?
    | SPECIFIC ROUTINE  routineName
    ) alterWithClause?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-alter-security-label-component-statement
alterSecurityLabelComponent
    : ALTER SECURITY LABEL COMPONENT componentName ADD (arrayElementClause | setElementClause | treeElementClause)
    ;

arrayElementClause
    : ARRAY '[' arrayElementItem (COMMA_ arrayElementItem)* ']'
    ;

arrayElementItem
    : element (COMMA_ element)* (BEFORE | AFTER) old_element=element
    | element
    ;

setElementClause
    : LITERAL_COLLECTION
    ;

treeElementClause
    : TREE LP_ treeElementItem (COMMA_ treeElementItem)* RP_
    ;

treeElementItem
    : element UNDER (ROOT | old_element=element)
    ;

element
    : stringLiterals
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-alter-sequence-statement
alterSequence
    : ALTER SEQUENCE sequenceName sequenceOption*
    ;

sequenceOption
    : CYCLE | NOCYCLE
    | CACHE numberLiterals
    | NOCACHE
    | ORDER | NOORDER
    | INCREMENT BY? numberLiterals
    | (RESTART | START) WITH? numberLiterals
    | NOMAXVALUE
    | MAXVALUE numberLiterals
    | NOMINVALUE
    | MINVALUE numberLiterals
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-alter-table-statement
alterTable
    : ALTER TABLE tableName (basicTableOptions | loggingTypeOption | addTypeClause | statisticsOption)
    ;

basicTableOptions
    : alterTableOption (COMMA_? alterTableOption)*
    ;

alterTableOption
    : addColumnClause
    | addAuditClause
    | addConstraintClause
    | addOrDropSpecializedColumns
    | dropAuditClause
    | dropConstraintClause
    | dropColumnClause
    | lockModeClause
    | alterTableModifyClause
    | modifyExtentSize
    | modifyNextSizeClause
    | alterTablePutClause
    | addOrDropSecurityPolicyClause
    ;

loggingTypeOption
    : TYPE LP_ (STANDARD | RAW) RP_
    ;

addTypeClause
    : ADD TYPE row_type=identifier
    ;

statisticsOption
    : STATCHANGE (AUTO | numberLiterals) (STATLEVEL (FRAGMENT | TABLE | AUTO))?
    | (STATCHANGE (AUTO | numberLiterals))? STATLEVEL (FRAGMENT | TABLE | AUTO)
    ;

addColumnClause
    : ADD alterColumnDefinition
    | ADD LP_ alterColumnDefinition (COMMA_ alterColumnDefinition)* RP_
    ;

alterColumnDefinition
    : columnName dataType (columnDefaultClause | singleColumnConstraintFormatOfAlter)* alterColumnBefore? addColumnSecuredWithLabelClause?
    ;

alterColumnBefore
    : BEFORE columnName
    ;

addColumnSecuredWithLabelClause
    : COLUMN? SECURED WITH label=identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=clause-default-alter-table#ids_sqs_0080
columnDefaultClause
    : DEFAULT constantExpression?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=clause-single-column-constraint-format#ids_sqs_0082
singleColumnConstraintFormatOfAlter
    : (NOT? NULL | UNIQUE | DISTINCT | PRIMARY KEY | referencesClause | checkClause) alterConstraintDefinition?
    ;

referencesClause
    : REFERENCES tableName columnNames? (ON DELETE CASCADE)?
    ;

checkClause
    : CHECK LP_ expr RP_
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=format-constraint-definition#ids_sqs_0300
alterConstraintDefinition
    : CONSTRAINT constraintName ((KEEP|DROP) ANY constraintType ((KEEP|DROP) ANY constraintType)? (OR constraintType)?)?
    (ON DELETE CASCADE)? constraintMode?
    ;

constraintType
    : REFERENCING FOREIGN KEY | CHECK CONSTRAINT
    ;

addAuditClause
    : ADD AUDIT
    ;

addConstraintClause
    : ADD CONSTRAINT (
    multipleColumnConstraintFormat (COMMA_ multipleColumnConstraintFormat)*
    | LP_ multipleColumnConstraintFormat (COMMA_ multipleColumnConstraintFormat)* RP_
    )
    ;

multipleColumnConstraintFormat
    : ( (NOT? NULL | UNIQUE | DISTINCT | PRIMARY KEY | referencesClause) columnNames
    | checkClause
    | foreignKeyDefinition
    ) alterConstraintDefinition?
    | foreignKeyDefinition alterConstraintDefinition INDEX DISABLED
    ;

foreignKeyDefinition
    : FOREIGN KEY columnNames referencesClause
    ;

addOrDropSpecializedColumns
    : (ADD | DROP) (CRCOLS | ERKEY | REPLCHECK | ROWIDS | VERCOLS)
    ;
dropAuditClause
    : DROP AUDIT
    ;
dropConstraintClause
    : DROP CONSTRAINT (constraintName (COMMA_ constraintName)* | LP_ constraintName (COMMA_ constraintName)* RP_)
    ;
dropColumnClause
    : DROP (columnNames | columnName)
    ;
lockModeClause
    : LOCK MODE LP_ (PAGE | ROW) RP_
    ;
// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=ats-modify-clause#ids_sqs_0078
alterTableModifyClause
    : MODIFY LP_ modifyColumnClause (COMMA_ modifyColumnClause)* RP_
    ;

modifyColumnClause
    : columnName dataType (columnDefaultClause | singleColumnConstraintFormatOfAlter)* modifyColumnSecurityClause?
    ;

modifyColumnSecurityClause
    : addColumnSecuredWithLabelClause
    | DROP COLUMN SECURITY
    ;

modifyExtentSize
    : MODIFY EXTENT SIZE expr (NEXT SIZE expr)?
    ;
modifyNextSizeClause
    : MODIFY NEXT SIZE expr
    ;
// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statement-put-clause
alterTablePutClause
    : PUT columnName IN LP_ sbspace=identifier (COMMA_ sbspace=identifier)* RP_ (LP_ (alterTablePutOption (COMMA_ alterTablePutOption)*)? RP_)?
    ;

alterTablePutOption
    : EXTENT SIZE numberLiterals
    | NO? LOG | HIGH INTEG | MODERATE INTEG | NO? KEEP ACCESS TIME
    ;

addOrDropSecurityPolicyClause
    : ADD SECURITY POLICY policy=identifier
    | DROP SECURITY POLICY (policy=identifier)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-alter-trusted-context
alterTrustedContext
    : ALTER TRUSTED CONTEXT context=identifier alterTrustedContextOption+
    ;

alterTrustedContextOption
    : ALTER SYSTEM AUTHID userName
    | (ALTER | ADD | DROP) ATTRIBUTES LP_ ADDRESS stringLiterals (COMMA_ ADDRESS stringLiterals)* RP_
    | ALTER NO DEFAULT ROLE
    | ALTER DEFAULT ROLE role=name
    | ALTER? (DISABLE | ENABLE)
    | (ADD | REPLACE) USE FOR authorizedUserClause (COMMA_ authorizedUserClause)*
    | DROP USE FOR (userName | PUBLIC) (COMMA_ (userName | PUBLIC))*
    ;

authorizedUserClause
    : (userName (ROLE roleName)?  | PUBLIC) ((WITHOUT | WITH) AUTHENTICATION)?
    ;

alterUser
    : ALTER (DEFAULT USER | USER userName (ACCOUNT (UNLOCK | LOCK))?) alterUserOption (COMMA_ alterUserOption)*
    ;

alterUserOption
    : (ADD | MODIFY) (
        PASSWORD stringLiterals
        | UID numberLiterals
        | USER surrog_user=userName
        | HOME stringLiterals
        )
    | (ADD | MODIFY | DROP) (surrogGroupClause| userAuthorizationOptions)
    | DROP (PASSWORD | UID | USER | HOME)
    ;

beginWork
    : BEGIN WORK? (WITHOUT REPLICATION)?
    ;

close
    : CLOSE cursorId
    ;

closeDatabase
    : CLOSE DATABASE
    ;

commitWork
    : COMMIT WORK?
    ;

connect
    : CONNECT TO (DEFAULT | databaseEnvironment (userAuthenticationClause TRUSTED?)?) (WITH CONCURRENT TRANSACTION)?
    ;

databaseEnvironment
    : dbName=stringVariable (AS connection=stringVariable)?
    ;

userAuthenticationClause
    : USER user_id=stringVariable USING validation_var=hostVariable
    ;

createAccessMethod
    : CREATE (SECONDARY | PRIMARY) ACCESS_METHOD ifNotExists? accessMethodName LP_ purposeOption (COMMA_ purposeOption)* RP_
    ;

ifNotExists
    : IF NOT EXISTS
    ;

createAggregate
    : CREATE AGGREGATE ifNotExists? aggregateName WITH LP_ modifier (COMMA_ modifier)* RP_
    ;

modifier
    : (INIT | ITER | COMBINE | FINAL) EQ_ identifier | HANDLESNULLS
    ;

aggregateName
    : (owner DOT_)? name
    ;

createCast
    : CREATE (EXPLICIT | IMPLICIT)? CAST ifNotExists? LP_ source_type=dataType AS target_type=dataType WITH functionName RP_
    ;

createDatabase
    : CREATE DATABASE ifNotExists? databaseName inSpace? (WITH ((BUFFERED? LOG) | LOG MODE ANSI))? (NLSCASE (SENSITIVE | INSENSITIVE))?
    ;

createDistinctType
    : CREATE DISTINCT TYPE ifNotExists? name AS dataType
    ;

createExternalTable
    : CREATE EXTERNAL TABLE ifNotExists? tableName (SAMEAS sameAsTable=tableName | LP_ externalTableColumnDefinition (COMMA_ externalTableColumnDefinition)* RP_)
    USING LP_ tableOption (COMMA_ tableOption)* COMMA_? RP_
    ;

externalTableColumnDefinition
    : name dataType otherOptionalClause?
    ;

otherOptionalClause
    : EXTERNAL CHAR LP_ size=numberLiterals RP_ (NOT NULL | NULL stringLiterals)?
    ;

tableOption
    : datafilesClause
    | FORMAT stringLiterals
    | DEFAULT | EXPRESS | DELUXE
    | DBDATE stringLiterals
    | DBMONEY stringLiterals
    | DELIMITER stringLiterals
    | RECORDEND stringLiterals
    | REJECTFILE stringLiterals
    | MAXERRORS numberLiterals
    | ESCAPE (ON | OFF)?
    | (NUMROWS | SIZE) numberLiterals
    ;

datafilesClause
    : DATAFILES LP_ stringLiterals  (COMMA_ stringLiterals)* RP_
    ;

createFunction
    : CREATE (OR REPLACE)? DBA? FUNCTION ifNotExists? functionName LP_ routineParameterList? RP_
    referencingAndForClauses? returnClause (SPECIFIC objectName)?
    withRoutineClause? ';'?
    (externalRoutineReference | statementBlock) END FUNCTION
    documentClause? withListingInClause?
    ;

withRoutineClause
    : WITH LP_ routineModifier (COMMA_ routineModifier)* RP_
    ;

documentClause
    : DOCUMENT stringLiterals (COMMA_ stringLiterals)*
    ;

withListingInClause
    : WITH LISTING IN pathname=stringLiterals
    ;

referencingAndForClauses
    : REFERENCING ((OLD | NEW) AS? alias)+ FOR tableName
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=segments-return-clause#ids_sqs_1712
returnClause
    : (RETURNING | RETURNS) returnDataType (COMMA_ returnDataType)*
    ;

returnDataType
    : (dataType | REFERENCES (BYTE | TEXT)) (AS identifier)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=segments-external-routine-reference#ids_sqs_1656
externalRoutineReference
    : EXTERNAL NAME sharedObjectFile LANGUAGE (C | JAVA) (PARAMETER STYLE INFORMIX?)? (NOT? VARIANT)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=segments-statement-block#ids_sqs_1750
statementBlock
    : defineStatement* onExceptionStatement*
    ((executeFunction | executeProcedure | splStatementsInBlock | sqlStatementsInBlock | beginEndStatement | expr) ';'?)*
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-define#ids_sqs_1306
defineStatement
    : DEFINE (
    GLOBAL spl_var=identifier (COMMA_ spl_var=identifier)* (
        dataType DEFAULT constantExpression
        | REFERENCES (BYTE | TEXT) DEFAULT NULL
        )
    | spl_var=identifier (COMMA_ spl_var=identifier)* COMMA_? (
        dataType
        | REFERENCES (BYTE | TEXT)
        | LIKE columnName
        | PROCEDURE
        )
    ) ';'
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-exception#ids_sqs_1351
onExceptionStatement
    : ON EXCEPTION (IN LP_ error_number=numberLiterals (COMMA_ error_number=numberLiterals)* RP_)?
    (SET sql_error_var=identifier (COMMA_ error_data_var=identifier)?)?
    statementBlock END EXCEPTION (WITH RESUME)? ';'?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=block-subset-spl-statements-valid-in-statement#ids_sqs_1751
splStatementsInBlock
    : labelStatement
    | callStatement
    | caseStatement
    | continueStatement
    | exitStatement
    | forStatement
    | foreachStatement
    | gotoStatement
    | ifStatement
    | letStatement
    | loopStatement
    | raiseExceptionStatement
    | returnStatement
    | systemStatement
    | traceStatement
    | whileStatement
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=block-sql-statements-valid-in-spl-statement-blocks#ids_sqs_1752
sqlStatementsInBlock
    : allocateCollection
    | allocateDescriptor
    | allocateRow
    | alterAccessMethod
    | alterFragment
    | alterFunction
    | alterIndex
    | alterProcedure
    | alterRoutine
    | alterSecurityLabelComponent
    | alterSequence
    | alterTable
    | alterTrustedContext
    | alterUser
    | beginWork
    | close
    | commitWork
    | createAccessMethod
    | createAggregate
    | createCast
    | createDistinctType
    | createExternalTable
    | createIndex
    | createOpaqueType
    | createOpclass
    | createRole
    | createRowType
    | createSchema
    | createSecurityLabel
    | createSecurityLabelComponent
    | createSecurityPolicy
    | createSequence
    | createSynonym
    | createTable
    | createTempTable
    | createTrigger
    | createTrustedContext
    | createUser
    | createView
    | createXadatasource
    | createXadatasourceType
    | deallocate
    | declare
    | delete
    | describe
    | dropAccessMethod
    | dropAggregate
    | dropCast
    | dropFunction
    | dropIndex
    | dropOpclass
    | dropProcedure
    | dropRole
    | dropRoutine
    | dropRowType
    | dropSecurity
    | dropSequence
    | dropSynonym
    | dropTable
    | dropTrigger
    | dropTrustedContext
    | dropType
    | dropUser
    | dropView
    | dropXadatasource
    | dropXadataTypeSource
    | fetch
    | free
    | getDescriptor
    | getDiagnostics
    | grant
    | insert
    | load
    | lockTable
    | merge
    | open
    | prepare
    | releaseSavepoint
    | renameColumn
    | renameConstraint
    | renameIndex
    | renameSecurity
    | renameSequence
    | renameTable
    | renameTrustedContext
    | renameUser
    | revoke
    | rollbackWork
    | saveExternalDirectives
    | savepoint
    | select
    | set
    | startViolationsTable
    | stopViolationsTable
    | truncateTable
    | unlockTable
    | update
    | whenever
    ;

labelStatement
    : SIGNED_LEFT_SHIFT_ label=identifier SIGNED_RIGHT_SHIFT_
    ;

callStatement
    : CALL (
    procedureName LP_ argumentList? RP_
    | functionName LP_ argumentList? RP_ RETURNING data_var=identifier (COMMA_ data_var=identifier)*
    | routine_var=identifier RETURNING data_var=identifier (COMMA_ data_var=identifier)*
    ) ';'
    ;
argumentList
    : argument (COMMA_ argument)*
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=segments-arguments#ids_sqs_1627
argument
    : (param=identifier EQ_)? expr
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-case
caseStatement
    : CASE value_expr=expr (elseClause | caseWhenClause+ elseClause?) END CASE ';'?
    ;

caseWhenClause
    : WHEN constantExpression THEN statementBlock
    ;

elseClause
    : ELSE statementBlock
    ;

continueStatement
    : CONTINUE (FOR | FOREACH | LOOP | WHILE)? ';'
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-exit
exitStatement
    : EXIT (FOREACH | (FOR | LOOP | WHILE)? (label=identifier)? (WHEN expr)?) ';'
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-
forStatement
    : labelStatement? FOR loop_var=identifier forRangeCluase
    ( statementBlock END FOR (lable=identifier)?
    | LOOP statementBlock END LOOP (lable=identifier)?
    ) ';'?
    ;

forRangeCluase
    : IN LP_ (rangeCluase | expr) (COMMA_ (rangeCluase | expr))* RP_
    | EQ_ rangeCluase
    | IN rangeCluase
    | IN FOR_RANGE
    ;

rangeCluase
    : left_expr=expr TO right_expr=expr (STEP increment_expr=expr)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-foreach
foreachStatement
    : FOREACH (
    (WITH HOLD | cursor=identifier (WITH HOLD)? FOR)? select ';'? statementBlock
    | (executeProcedure | executeFunction) statementBlock
    ) END FOREACH ';'?
    ;

gotoStatement
    : GOTO label=identifier ';'
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-if
ifStatement
    : IF expr THEN ifStatementItem* elifClause* (ELSE ifStatementItem+)? END IF ';'?
    ;

elifClause
    : ELIF expr THEN ifStatementItem*
    ;

ifStatementItem
    : beginEndStatement | splStatementsInBlock | sqlStatementInIf ';'
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=if-sql-statements-not-valid-in-statement#ids_sqs_1346
sqlStatementInIf
    : allocateCollection
    | allocateRow
    | alterAccessMethod
    | alterFragment
    | alterFunction
    | alterIndex
    | alterProcedure
    | alterRoutine
    | alterSecurityLabelComponent
    | alterSequence
    | alterTable
    | alterTrustedContext
    | alterUser
    | beginWork
    | close
    | commitWork
    | createAccessMethod
    | createAggregate
    | createCast
    | createDistinctType
    | createExternalTable
    | createFunction
    | createFunctionFrom
    | createIndex
    | createOpaqueType
    | createOpclass
    | createProcedureFrom
    | createRole
    | createRoutineFrom
    | createRowType
    | createSchema
    | createSecurityLabel
    | createSecurityLabelComponent
    | createSecurityPolicy
    | createSequence
    | createSynonym
    | createTable
    | createTempTable
    | createTrigger
    | createTrustedContext
    | createUser
    | createView
    | createXadatasource
    | createXadatasourceType
    | declare
    | delete
    | dropAccessMethod
    | dropAggregate
    | dropCast
    | dropFunction
    | dropIndex
    | dropOpclass
    | dropProcedure
    | dropRole
    | dropRoutine
    | dropRowType
    | dropSecurity
    | dropSequence
    | dropSynonym
    | dropTable
    | dropTrigger
    | dropTrustedContext
    | dropType
    | dropUser
    | dropView
    | dropXadatasource
    | dropXadataTypeSource
    | fetch
    | free
    | grant
    | insert
    | lockTable
    | merge
    | open
    | prepare
    | releaseSavepoint
    | renameColumn
    | renameConstraint
    | renameIndex
    | renameSecurity
    | renameSequence
    | renameTable
    | renameTrustedContext
    | renameUser
    | revoke
    | rollbackWork
    | saveExternalDirectives
    | savepoint
    | select
    | set
    | startViolationsTable
    | stopViolationsTable
    | truncateTable
    | unlockTable
    | update
    | updateStatistics
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-let
letStatement
    : LET spl_var=objectName (COMMA_ spl_var=objectName)* EQ_ letItemClause (COMMA_ letItemClause)* ';'
    ;

letItemClause
    : functionName  LP_ argumentList? RP_
    | expr | subquery
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-loop
loopStatement
    : labelStatement? ( WHILE expr | FOR loop_var=identifier forRangeCluase )?
    LOOP statementBlock END LOOP (lable=identifier)? ';'?
    ;

raiseExceptionStatement
    : RAISE EXCEPTION sql_error_var=expr (COMMA_ isam_error=expr (COMMA_ error_text=expr)?)? ';'
    ;

returnStatement
    : RETURN (expr (COMMA_ expr)* (WITH RESUME)?)? ';'
    ;

systemStatement
    : SYSTEM expr ';'
    ;

traceStatement
    : TRACE (ON | OFF | PROCEDURE | expr) ';'
    ;

whileStatement
    : labelStatement? WHILE expr (
    statementBlock END WHILE (lable=identifier)?
    | LOOP statementBlock END LOOP (lable=identifier)?
    ) ';'?
    ;

beginEndStatement
    : BEGIN statementBlock END ';'?
    ;

createFunctionFrom
    : CREATE FUNCTION FROM ifNotExists? stringVariable
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-index-statement
createIndex
    : CREATE indexTypeOption INDEX ifNotExists? indexName ON (tableName | synonymName) indexKeySpecification?
    indexOption*
    ;

indexTypeOption
    : (DISTINCT | UNIQUE)? CLUSTER?
    ;

indexKeySpecification
    : LP_ indexKey (COMMA_ indexKey)* RP_
    ;

indexKey
    : (columnName opclassName? | bsonProcessingFunction USING BSON | functionExpression) (ASC | DESC)?
    ;

indexOption
    : indexUsingAccessMethodClause
    | fillfactorOption
    | hashOnClause
    | storageOption
    | indexMode
    | ONLINE
    | COMPRESSED
    | indexExtentSizeOptions
    | indexVisibilityOptions
    ;
indexExtentSizeOptions
    : (EXTENT | NEXT) SIZE expr
    ;
indexUsingAccessMethodClause
    : USING accessMethodName LP_ argumentList? RP_
    ;

hashOnClause
    : HASH ON columnNames WITH numberLiterals BUCKETS
    ;

fillfactorOption
    : FILLFACTOR numberLiterals
    ;

storageOption
    : IN (TABLE | space=identifier)
    | fragmentByClauseForIndexes
    ;

indexMode
    : ENABLED | DISABLED | FILTERING (WITHOUT ERROR | WITH ERROR)?
    ;

indexVisibilityOptions
    : VISIBLE | INVISIBLE
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-opaque-type-statement
createOpaqueType
    : CREATE OPAQUE TYPE ifNotExists? typeName LP_ INTERNALLENGTH EQ_ (numberLiterals | VARIABLE) (COMMA_ opaqueTypeModifier)* RP_
    ;

opaqueTypeModifier
    : CANNOTHASH | PASSEDBYVALUE | MAXLEN EQ_ numberLiterals | ALIGNMENT EQ_ numberLiterals
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-opclass-statement
createOpclass
    : CREATE OPCLASS ifNotExists? opclassName FOR sec_acc_method=identifier
    STRATEGIES LP_ strategySpecification (COMMA_ strategySpecification)* RP_
    SUPPORT LP_ support_function=identifier (COMMA_ support_function=identifier)* RP_
    ;

strategySpecification
    : strategy_function=name (LP_ dataType (COMMA_ dataType)* RP_)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-procedure-statement
createProcedure
    : CREATE (OR REPLACE)? DBA? PROCEDURE ifNotExists? procedureName LP_ routineParameterList? RP_
    referencingAndForClauses? returnClause? (SPECIFIC objectName)?
    withRoutineClause? ';'?
        (externalRoutineReference | statementBlock) END PROCEDURE
        documentClause? withListingInClause?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-procedure-from-statement
createProcedureFrom
    : CREATE PROCEDURE FROM ifNotExists? stringVariable
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-role-statement
createRole
    : CREATE ROLE ifNotExists? roleName
    ;

roleName
    : identifier | stringLiterals
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-routine-from-statement
createRoutineFrom
    : CREATE ROUTINE FROM ifNotExists? stringVariable
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-row-type-statement
createRowType
    : CREATE ROW TYPE ifNotExists? rowTypeName (
    LP_ fieldDefinition (COMMA_ fieldDefinition)* COMMA_? RP_
    | (LP_ fieldDefinition (COMMA_ fieldDefinition)* COMMA_? RP_)? UNDER supertype=dataType
    )
    ;

rowTypeName
    : (owner DOT_)? name
    ;

fieldDefinition
    : field dataType (NOT NULL)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-schema-statement
createSchema
    : CREATE SCHEMA AUTHORIZATION userName schemaElement+ ';'?
    ;

schemaElement
    : createTable
    | createView
    | grant
    | createIndex
    | createSynonym
    | createTrigger
    | createSequence
    | createRowType
    | createOpaqueType
    | createDistinctType
    | createCast
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-security-label-statement
createSecurityLabel
    : CREATE SECURITY LABEL ifNotExists? policyName DOT_ label=identifier securityComponentClause (COMMA_ securityComponentClause)*
    ;

policyName
    : name
    ;

securityComponentClause
    : COMPONENT componentName element (COMMA_ element)*
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-security-label-component-statement
createSecurityLabelComponent
    : CREATE SECURITY LABEL COMPONENT ifNotExists? componentName (arrayElementClause | setElementClause | createTreeElementClause)
    ;

componentName
    : name
    ;

createTreeElementClause
    : TREE LP_ element ROOT COMMA_ treeElementItem (COMMA_ treeElementItem)* RP_
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-security-policy-statement
createSecurityPolicy
    : CREATE SECURITY POLICY ifNotExists? policyName COMPONENTS componentName (COMMA_ componentName)*
    (WITH IDSLBACRULES)? ((RESTRICT | OVERRIDE) NOT AUTHORIZED WRITE SECURITY LABEL)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-sequence-statement
createSequence
    : CREATE SEQUENCE ifNotExists? sequenceName sequenceOption*
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-synonym-statement
createSynonym
    : CREATE (PUBLIC | PRIVATE)? SYNONYM ifNotExists? synonymName FOR (tableName | viewName | sequenceName)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-table-statement
createTable
    : CREATE (LARGE | SMALL)? loggingOption? TABLE ifNotExists? tableName (
    (ofTypeClause | LP_ columnDefinition (COMMA_ (columnDefinition | multipleColumnConstraintFormatOfCreate))* COMMA_? RP_)
    (createTableWithOption (COMMA_ createTableWithOption)* )?
    securityPolicyClause? storageOptions lockMode? usingAccessMethodClause? statisticsOption?
    | createTableAsSelect
    )
    ;

createTableAsSelect
    : (LP_ columnAlias (COMMA_ columnAlias)* RP_)? storageOptions lockMode? AS select
    ;

columnAlias
    : name ('::' dataType)?
    ;

usingAccessMethodClause
    : USING accessMethodName (methodParams (COMMA_ methodParams)*)?
    ;

methodParams
    : LP_ config_keyword=identifier (COMMA_ config_keyword=identifier)* EQ_ config_value=stringLiterals RP_
    ;

createTableWithOption
    : WITH (AUDIT | CRCOLS | ERKEY | REPLCHECK | VERCOLS)
    ;
securityPolicyClause
    : SECURITY POLICY policy=identifier
    ;
loggingOption
    : STANDARD | RAW
    ;

ofTypeClause
    : OF TYPE row_type=identifier (LP_ multipleColumnConstraintFormatOfCreate (COMMA_ multipleColumnConstraintFormatOfCreate)* RP_)?
    (UNDER supertable=identifier)?
    ;

multipleColumnConstraintFormatOfCreate
    : ( (NOT? NULL)? (UNIQUE | DISTINCT | PRIMARY KEY | referencesClause) columnNames
    | checkClause
    | foreignKeyDefinition
    ) constraintDefinition
    ;

constraintDefinition
    : (CONSTRAINT constraintName)? constraintMode?
    ;

columnDefinition
    : name dataType (columnDefaultClause | singleColumnConstraintFormatOfCreate)* columnSecuredWithLabelClause?
    ;

singleColumnConstraintFormatOfCreate
    : (NOT? NULL | UNIQUE | DISTINCT | PRIMARY KEY | referencesClause | checkClause) constraintDefinition
    ;

columnSecuredWithLabelClause
    : COLUMN? SECURED WITH label=identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-temp-table-statement
createTempTable
    : CREATE (LARGE | SMALL)? TEMP TABLE ifNotExists? tableName
    LP_ tempColumnDefinition (COMMA_ (tempColumnDefinition | multipleColumnConstraintFormatOfTempTable))* RP_
    (WITH NO LOG)? storageOptions lockMode? usingAccessMethodClause?
    ;

multipleColumnConstraintFormatOfTempTable
    : NOT? NULL (UNIQUE | DISTINCT | PRIMARY KEY | referencesClause) columnNames
    | checkClause
    ;

tempColumnDefinition
    : name dataType (columnDefaultClause | singleColumnConstraintFormatOfTempTable)*
    ;

singleColumnConstraintFormatOfTempTable
    : NOT? NULL | UNIQUE | DISTINCT | PRIMARY KEY | checkClause
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-trigger-statement
createTrigger
    : CREATE TRIGGER ifNotExists? triggerName (triggerOnATable | INSTEAD OF triggerOnAView) triggerMode?
    ;

triggerMode
    : ENABLED | DISABLED
    ;

triggerName
    : objectName
    ;

triggerOnATable
    : (DELETE | SELECT (OF columnName (COMMA_ columnName)*)?) ON tableName deleteAndSelectTriggerClauses
    | UPDATE (OF columnName (COMMA_ columnName)*)? ON tableName updateTriggerClause
    | INSERT ON tableName insertTriggerClause
    ;

deleteAndSelectTriggerClauses
    : actionClause | declarationForUpdate correlatedTableAction
    ;

updateTriggerClause
    : actionClause | declarationForUpdate newDeclaration? correlatedTableAction
    ;

insertTriggerClause
    : actionClause | newDeclaration correlatedTableAction
    ;

actionClause
    : (BEFORE triggeredAction)? (FOR EACH ROW triggeredAction)? (AFTER triggeredAction)?
    ;

triggeredAction
    : (WHEN LP_ expr RP_)? LP_ triggeredActionStatement (COMMA_ triggeredActionStatement)* RP_
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statement-triggered-action-table#ids_sqs_0618
triggeredActionStatement
    : executeProcedure | executeFunction | insert | delete | update
    ;

correlatedTableAction
    : (BEFORE triggeredAction)? FOR EACH ROW triggeredAction (AFTER triggeredAction)?
    ;

triggerOnAView
    : (INSERT ON viewName newDeclaration?
    | DELETE ON viewName oldDeclaration?
    | UPDATE ON viewName declarationForUpdate?
    ) FOR EACH ROW LP_ triggeredActionStatement (COMMA_ triggeredActionStatement)* RP_
    ;

oldDeclaration
    : REFERENCING OLD AS? alias
    ;

newDeclaration
    : REFERENCING NEW AS? alias
    ;

declarationForUpdate
    : REFERENCING ((OLD | NEW) AS? alias)+
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-trusted-context
createTrustedContext
    : CREATE TRUSTED CONTEXT context=identifier (USER | BASED UPON CONNECTION USING SYSTEM AUTHID) userName trustedContextOption+
    ;

trustedContextOption
    : ATTRIBUTES LP_ ADDRESS stringLiterals (COMMA_ ADDRESS stringLiterals)* RP_
    | WITH USE FOR authorizedUserClause (COMMA_ authorizedUserClause)*
    | NO DEFAULT ROLE
    | DEFAULT ROLE roleName
    | DISABLE
    | ENABLE
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-user-statement-unix-linux
createUser
    : CREATE (
    DEFAULT USER WITH (ACCOUNT UNLOCK | ACCOUNT LOCK)? userProperties
    | USER userName (WITH (PASSWORD password=stringLiterals)? (ACCOUNT UNLOCK | ACCOUNT LOCK)? userProperties?)?
    )
    ;

userProperties
    : PROPERTIES (
    UID user_ID=numberLiterals surrogGroupClause
    | USER userName surrogGroupClause?
    ) (HOME directory=stringLiterals)? userAuthorizationOptions?
    ;

userAuthorizationOptions
    : AUTHORIZATION LP_ authorizationOption (COMMA_ authorizationOption)* RP_
    ;

authorizationOption
    : DBSA | DBSSO | AAO | BARGROUP
    ;

surrogGroupClause
    : GROUP LP_ (identifier | numberLiterals) (COMMA_ (identifier | numberLiterals))* RP_
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-view-statement
createView
    : CREATE VIEW ifNotExists? viewName (OF TYPE dataType | columnNames)?
    AS select (WITH CHECK OPTION)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-xadatasource-statement
createXadatasource
    : CREATE XADATASOURCE ifNotExists? xa_source=objectName USING xa_type=objectName
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-create-xadatasource-type-statement
createXadatasourceType
    : CREATE XADATASOURCE TYPE ifNotExists? xa_type=objectName LP_ purposeOption (COMMA_ purposeOption)* RP_
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-database-statement
database
    : DATABASE databaseName EXCLUSIVE?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-deallocate-collection-statement
deallocate
    : DEALLOCATE (COLLECTION | ROW | DESCRIPTOR) stringVariable
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-declare-statement
declare
    : DECLARE cursorId (
    CURSOR (WITH HOLD)? (FOR insert | select | otherSelectOrFunctionOptions)
    | SCROLL CURSOR (WITH HOLD)? otherSelectOrFunctionOptions
    | CURSOR FOR (select | insert)
    )
    ;

cursorId
    : identifierVariable
    ;

otherSelectOrFunctionOptions
    : FOR (select | identifierVariable | executeFunction | executeProcedure)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-delete-statement
delete
    : withClause? DELETE optimizerDirectives? FROM? (
    (tableName | viewName | synonymName) (AS? alias)?
    | ONLY LP_ (tableName | viewName | synonymName) RP_
    | collectionDerivedTable
    ) whereClause?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-describe-statement#ids_sqs_0688__scripted_160__title__1
describe
    : DESCRIBE (OUTPUT | INPUT)? statement_id=identifierVariable (
    USING SQL DESCRIPTOR descriptor_var=stringVariable
    | INTO SQL DESCRIPTOR descriptor_var=stringVariable
    | INTO sqlda_pointer=identifierVariable
    )
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-disconnect-statement
disconnect
    : DISCONNECT (CURRENT | ALL | DEFAULT | stringVariable)
    ;

ifExists
    : IF EXISTS
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-access-method-statement
dropAccessMethod
    : DROP ACCESS_METHOD ifExists? accessMethodName RESTRICT
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-aggregate-statement
dropAggregate
    : DROP AGGREGATE ifExists? aggregateName
    ;

//https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-cast-statement
dropCast
    : DROP CAST ifExists? LP_ sourceType=dataType AS targetType=dataType RP_
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-database-statement
dropDatabase
    : DROP DATABASE ifExists? databaseName
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-function-statement
dropFunction
    : DROP SPECIFIC? FUNCTION ifExists? functionName (LP_ dataType (COMMA_ dataType)* RP_)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-index-statement
dropIndex
    : DROP INDEX ifExists? indexName ONLINE?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-opclass-statement
dropOpclass
    : DROP OPCLASS ifExists? opclassName RESTRICT
    ;

opclassName
    : (owner DOT_)? name
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-procedure-statement
dropProcedure
    : DROP SPECIFIC? PROCEDURE ifExists? procedureName (LP_ dataType (COMMA_ dataType)* RP_)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-role-statement
dropRole
    : DROP ROLE ifExists? roleName
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-routine-statement
dropRoutine
    : DROP SPECIFIC? ROUTINE ifExists? routineName (LP_ dataType (COMMA_ dataType)* RP_)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-row-type-statement
dropRowType
    : DROP ROW TYPE ifExists? rowTypeName RESTRICT
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-security-statement
dropSecurity
    : DROP SECURITY (
    LABEL ifExists? (policyName DOT_)? label=identifier RESTRICT?
    | LABEL COMPONENT ifExists? componentName RESTRICT?
    | POLICY ifExists? policyName (RESTRICT | CASCADE)
    )
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-sequence-statement
dropSequence
    : DROP SEQUENCE ifExists? sequenceName
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-synonym-statement
dropSynonym
    : DROP SYNONYM ifExists? synonymName
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-table-statement
dropTable
    : DROP TABLE ifExists? tableName (CASCADE | RESTRICT)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-trigger-statement
dropTrigger
    : DROP TRIGGER ifExists? triggerName
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-trusted-context-statement
dropTrustedContext
    : DROP TRUSTED CONTEXT contextName = identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-type-statement
dropType
    : DROP TYPE ifExists? typeName RESTRICT
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-user-statement-unix-linux
dropUser
    : DROP USER userName
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-view-statement
dropView
    : DROP VIEW ifExists? viewName (CASCADE | RESTRICT)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-xadatasource-statement
dropXadatasource
    : DROP XADATASOURCE ifExists? xaSourceName = objectName RESTRICT
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-drop-xadatasource-type-statement
dropXadataTypeSource
    : DROP XADATASOURCE TYPE ifExists? xaSourceName = objectName RESTRICT
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-execute-statement
execute
    : EXECUTE stmt_id=identifierVariable executeIntoClause? executeUsingClause?
    | executeFunction
    | executeProcedure
    | executeImmediate
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-execute-function-statement
executeFunction
    : EXECUTE FUNCTION functionName LP_ argumentList? RP_ executeIntoClause? (WITH TRIGGER REFERENCES)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-execute-procedure-statement
executeProcedure
    : EXECUTE PROCEDURE procedureName LP_ argumentList? RP_ executeIntoClause? (WITH TRIGGER REFERENCES)?
    ;

executeIntoClause
    : INTO (
    (identifierVariable hostVariable?) (COMMA_ (identifierVariable hostVariable?))*
    | SQL DESCRIPTOR descriptor=stringVariable
    | DESCRIPTOR sqlda_pointer=identifierVariable
    )
    ;

executeUsingClause
    : USING (
    (identifierVariable hostVariable?) (COMMA_ (identifierVariable hostVariable?))*
    | SQL DESCRIPTOR descriptor=stringVariable
    | DESCRIPTOR sqlda_pointer=identifierVariable
    )
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-execute-immediate-statement
executeImmediate
    : EXECUTE IMMEDIATE (stringVariable | expr)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-fetch-statement
fetch
    : FETCH fetchOption? cursorId (executeIntoClause| executeUsingClause)?
    ;

fetchOption
    : NEXT
    | PRIOR
    | PREVIOUS
    | FIRST
    | LAST
    | CURRENT
    | RELATIVE numberVariable
    | ABSOLUTE numberVariable
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-flush-statement
flush
    : FLUSH cursorId
    ;

//https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-free-statement
free
    : FREE cursorId
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-get-descriptor-statement
getDescriptor
    : GET DESCRIPTOR descriptor=stringVariable (
    total_items_var=hostVariable EQ_ COUNT
    | VALUE item_num=numberVariable describedItemInformation (COMMA_ describedItemInformation)*
    )
    ;

describedItemInformation
    : field_var=hostVariable EQ_ ( DATA | IDATA | ILENGTH | INDICATOR | ITYPE | LENGTH | NAME | NULLABLE | PRECISION
    | SCALE | TYPE | EXTYPEID | EXTYPELENGTH | EXTYPENAME | EXTYPEOWNERLENGTH | EXTYPEOWNERNAME | SOURCEID | SOURCETYPE
    )
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-get-diagnostics-statement
getDiagnostics
    : GET DIAGNOSTICS (statementClause (COMMA_ statementClause)* | exceptionClause)
    ;

statementClause
    : status_var=hostVariable EQ_ (ROW_COUNT | NUMBER | MORE_)
    ;

exceptionClause
    : EXCEPTION exception_num=numberVariable informationClause (COMMA_ informationClause)*
    ;

informationClause
    : information=hostVariable EQ_ (CLASS_ORIGIN | CONNECTION_NAME | INFORMIX_SQLCODE | MESSAGE_LENGTH | MESSAGE_TEXT
    | RETURNED_SQLSTATE | SERVER_NAME | SUBCLASS_ORIGIN)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-grant-statement
grant
    : GRANT (
    ( databaseLevelPrivileges | tableLevelPrivileges | routineLevelPrivileges | languageLevelPrivileges | typeLevelPrivileges
        | sequenceLevelPrivileges | (DEFAULT ROLE)? roleName
        ) toOptions
    |  securityAdministrationOptions | surrogateUserProperties | grantFragment
    )
    ;

databaseLevelPrivileges
    : CONNECT | RESOURCE | DBA
    ;

tableLevelPrivileges
    : (ALL PRIVILEGES? | tablePrivilege (COMMA_ tablePrivilege)*) ON (tableName | viewName| synonymName)
    ;

tablePrivilege
    : INSERT | DELETE | ALTER | INDEX | UNDER
    | (UPDATE | SELECT | REFERENCES) columnNames?
    ;

routineLevelPrivileges
    : EXECUTE ON ( routineName
    | (PROCEDURE | FUNCTION | ROUTINE) routineName (LP_ routineParameterList? RP_)?
    | SPECIFIC (PROCEDURE | FUNCTION | ROUTINE) routineName
    )
    ;

languageLevelPrivileges
    : USAGE ON LANGUAGE (SPL | C | JAVA)
    ;

typeLevelPrivileges
    : USAGE ON TYPE typeName | UNDER ON ROW? TYPE rowTypeName
    ;

sequenceLevelPrivileges
    : (ALL | SELECT (COMMA_ ALTER)? | ALTER (COMMA_ SELECT)?) ON (sequenceName | synonymName)
    ;

securityAdministrationOptions
    : dbsecadmClause
    | exemptionClause
    | securityLabelClause
    | setsessionauthClause
    ;

dbsecadmClause
    : DBSECADM toUsers
    ;

exemptionClause
    : EXEMPTION ON RULE (
    | IDSLBACREADARRAY
    | IDSLBACREADTREE
    | IDSLBACREADSET
    | IDSLBACWRITEARRAY (WRITEDOWN | WRITEUP)?
    | IDSLBACWRITESET
    | IDSLBACWRITETREE
    | ALL
    ) FOR policyName toUsers
    ;

securityLabelClause
    : SECURITY LABEL policyName DOT_ label=identifier toUsers (FOR (ALL | READ | WRITE) ACCESS)?
    ;

setsessionauthClause
    : SETSESSIONAUTH ON (PUBLIC | (USER? userName) (COMMA_ (USER? userName))*) toUsersOrRoles
    ;

toUsersOrRoles
    : TO ((USER| ROLE)? (userName| roleName)) (COMMA_ ((USER | ROLE)? (userName| roleName)))*
    ;

surrogateUserProperties
    : ACCESS TO (PUBLIC | userName (COMMA_ userName)*) PROPERTIES
    (UID user_ID=numberLiterals COMMA_ surrogGroupClause | USER os_user_name=userName (COMMA_ surrogGroupClause)?)
    (HOME directory=stringLiterals)? (COMMA_ userAuthorizationOptions)?
    ;

toOptions
    : TO (
    (userName | roleName) (COMMA_ (userName | roleName))* (WITH GRANT OPTION)?
    | PUBLIC
    ) (AS grantor=name)?
    ;

toUsers
    : TO (USER? userName) (COMMA_ (USER? userName))*
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-grant-fragment-statement
grantFragment
    : FRAGMENT fragmentLevelPrivileges ON tableName LP_ frag=identifier (COMMA_ frag=identifier)* RP_ toOptions
    ;

fragmentLevelPrivileges
    : ALL | (INSERT | DELETE | UPDATE) (COMMA_ (INSERT | DELETE | UPDATE))*
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-info-statement
info
    : INFO (TABLES | (COLUMNS |INDEXES |STATUS |PRIVILEGES |ACCESS |FRAGMENTS |REFERENCES) FOR tableName)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-insert-statement
insert
    : withClause? INSERT (insertIntoTableClause | insertAtClause)
    ;

insertAtClause
    : (AT position=numberLiterals)? INTO collectionDerivedTable fieldOptions
    ;

fieldOptions
    : (field (COMMA_ field)*)? (valuesClause | select)
    ;

insertIntoTableClause
    : INTO (tableName | viewName | synonymName) columnNames? (valuesClause | executeRoutineClause | select)?
    ;

valuesClause
    : VALUES LP_ expr (COMMA_ expr)* RP_
    ;

executeRoutineClause
    : EXECUTE (PROCEDURE procedureName | FUNCTION functionName) LP_ argumentList? RP_
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-load-statement
load
    : LOAD FROM filename=stringLiterals (DELIMITER delimiter=stringLiterals)? INSERT INTO (tableName | viewName | synonymName) columnNames?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-lock-table-statement
lockTable
    : LOCK TABLE (tableName | synonymName) IN (SHARE | EXCLUSIVE) MODE
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-merge-statement
merge
    : MERGE optimizerDirectives? intoTargetClause usingClause onClause (updateClause | deleteClause | insertClause)+
    ;

intoTargetClause
    : INTO (tableName | viewName | synonymName) (AS? alias)?
    ;

usingClause
    : USING (tableName | viewName | subquery) (AS? alias)? columnNames?
    ;

onClause
    : ON expr
    ;

updateClause
    : WHEN MATCHED THEN UPDATE setClause
    ;
deleteClause
    : WHEN MATCHED THEN DELETE
    ;
insertClause
    : WHEN NOT MATCHED THEN INSERT columnNames? valuesClause
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-open-statement
open
    : OPEN cursorId openUsingClause? (WITH REOPTIMIZATION)?
    ;

openUsingClause
    : USING (
    hostVariable (COMMA_ hostVariable)*
    | SQL DESCRIPTOR descriptor=stringVariable
    | DESCRIPTOR sqlda_pointer=identifier
    )
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-output-statement
output
    : OUTPUT TO (PIPE program=identifier | fileName) (WITHOUT HEADINGS)? select
    ;

fileName
    : .*?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-prepare-statement
prepare
    : PREPARE statement_id=identifierVariable FROM (stringVariable | expr)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-put-statement
put
    : PUT cursorId (
    FROM outputItem (COMMA_ outputItem)*
    | USING SQL? DESCRIPTOR (stringVariable | identifierVariable)
    )
    ;

outputItem
    : output_var=identifierVariable hostVariable?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-release-savepoint-statement
releaseSavepoint
    : RELEASE SAVEPOINT savepointName=identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-rename-column-statement
renameColumn
    : RENAME COLUMN columnName TO newName=identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-rename-constraint-statement
renameConstraint
    : RENAME CONSTRAINT constraintName TO newName=identifier
    ;

//https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-rename-database-statement
renameDatabase
    : RENAME DATABASE databaseName TO newName=identifier
    ;

//https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-rename-index-statement
renameIndex
    : RENAME INDEX indexName TO newName=identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-rename-security-statement
renameSecurity
    : RENAME SECURITY (POLICY | LABEL policyName DOT_ | LABEL COMPONENT) name TO newName=identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-rename-sequence-statement
renameSequence
    : RENAME SEQUENCE sequenceName TO newName=identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-rename-table-statement
renameTable
    : RENAME TABLE tableName TO newName=identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-rename-trusted-context-statement
renameTrustedContext
    : RENAME TRUSTED CONTEXT name TO newName=identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-rename-user-statement-unix-linux
renameUser
    : RENAME USER userName TO newName=identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-revoke-statement
revoke
    : REVOKE (
    ( databaseLevelPrivileges | tableLevelPrivileges | routineLevelPrivileges | languageLevelPrivileges | typeLevelPrivileges
        | sequenceLevelPrivileges | DEFAULT ROLE | ACCESS | roleName
        ) fromOptions
    |  securityAdministrationOptionsRevoke | revokeFragment
    )
    ;
// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-revoke-fragment-statement
revokeFragment
    : FRAGMENT fragmentLevelPrivileges ON tableName (LP_ frag=identifier (COMMA_ frag=identifier)* RP_)? fromOptions
    ;
fromOptions
    : FROM  (
    (userName | roleName) (COMMA_ (userName | roleName))*
    | PUBLIC
    ) (CASCADE | RESTRICT)? (AS revoker=name)?
    ;

securityAdministrationOptionsRevoke
    : dbsecadmClauseRevoke
    | exemptionClauseRevoke
    | securityLabelClauseRevoke
    | setsessionauthClauseRevoke
    ;
fromUsers
    : FROM (USER? userName) (COMMA_ (USER? userName))*
    ;
dbsecadmClauseRevoke
    : DBSECADM fromUsers
    ;

exemptionClauseRevoke
    : EXEMPTION ON RULE (
    | IDSLBACREADARRAY
    | IDSLBACREADTREE
    | IDSLBACREADSET
    | IDSLBACWRITEARRAY (WRITEDOWN | WRITEUP)?
    | IDSLBACWRITESET
    | IDSLBACWRITETREE
    | ALL
    ) FOR policyName fromUsers
    ;

securityLabelClauseRevoke
    : SECURITY LABEL policyName (DOT_ label=identifier | DOT_ASTERISK_) fromUsers (FOR (ALL | READ | WRITE) ACCESS)?
    ;

setsessionauthClauseRevoke
    : SETSESSIONAUTH ON (PUBLIC | (USER? userName) (COMMA_ (USER? userName))*) fromUsersOrRoles
    ;

fromUsersOrRoles
    : FROM ((USER| ROLE)? (userName| roleName)) (COMMA_ ((USER | ROLE)? (userName| roleName)))*
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-rollback-work-statement
rollbackWork
    : ROLLBACK WORK? (TO SAVEPOINT savepointName = identifier?)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-save-external-directives-statement
saveExternalDirectives
    : SAVE EXTERNAL DIRECTIVES optimizerDirectives+ (ACTIVE | INACTIVE | TEST ONLY) FOR select
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-savepoint-statement
savepoint
    : SAVEPOINT savepointName = identifier UNIQUE?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-update-statement
update
    : withClause? UPDATE optimizerDirectives? updataTarget setClause whereClause?
    ;
updataTarget
    : (tableName | viewName | synonymName) (AS? alias)?
    | ONLY LP_ (tableName | synonymName) RP_
    | collectionDerivedTable
    ;

setClause
    : SET (singleColumnFormat (COMMA_ singleColumnFormat)* | multipleColumnFormat (COMMA_ multipleColumnFormat)*)
    ;

singleColumnFormat
    : columnName EQ_ setColumnValue
    ;

setColumnValue
    : expr
    ;

multipleColumnFormat
    : (columnNames | '*') EQ_ LP_ setColumnValue (COMMA_ setColumnValue)* RP_
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-select-statement
select
    : withClause? queryExpression
    ;
queryExpression
    : queryExpression combineType queryExpression
    | (querySpecification | parenthesisQueryExpression) orderByClause? limitClause? forUpdateClause? intoTableClause?
    ;
parenthesisQueryExpression
    : LP_ queryExpression RP_
    ;

combineType
    : UNION (ALL)? | EXCEPT | INTERSECT | MINUS
    ;

querySpecification
    : SELECT optimizerDirectives? projectionClause intoClause? fromClause? gridClause? whereClause? hierarchicalClause? groupByClause? havingClause?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statement-order-by-clause#ids_sqs_0157
orderByClause
    : ORDER SIBLINGS? BY orderByItem (COMMA_ orderByItem)*
    ;

orderByItem
    : (columnName | numberLiterals | expr) (ASC | DESC)? (NULLS (FIRST | LAST))?
    ;

limitClause
    : LIMIT INT_
    ;

forUpdateClause
    : FOR READ ONLY
    | FOR UPDATE (OF columnName (COMMA_ columnName)*)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statement-into-table-clauses#ids_sqs_0159
intoTableClause
    : intoTempClause | intoExternalClause | intoStandardOrRawClause
    ;

intoTempClause
    : INTO TEMP tableName (WITH NO LOG)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=clauses-into-external-clause#ids_sqs_2091
intoExternalClause
    : INTO EXTERNAL tableName USING LP_ (tableOptions COMMA_)? datafilesClause tableOptions? RP_
    ;


intoStandardOrRawClause
    : INTO (STANDARD | RAW)? tableName storageOptions lockMode?
    ;

tableOptions
    : tableOptionSpec (COMMA_ tableOptionSpec)*
    ;

tableOptionSpec
    : (FORMAT | DELIMITER | RECORDEND) STRING_
    | ESCAPE (ON| OFF)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=cts-storage-options#ids_sqs_0109
storageOptions
    : (inSpace | fragmentByClause)? putClause? extentSizeOptions compressedOption?
    ;

inSpace
    : IN space=identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=options-fragment-by-clause#ids_sqs_0106
fragmentByClause
    : (WITH ROWIDS)? (FRAGMENT | PARTITION) BY (
    fragmentByRoundRobin
    | fragmentByExpression
    | fragmentByRange
    | fragmentByList
    )
    ;

fragmentByRoundRobin
    : ROUND ROBIN ( IN space=identifier (COMMA_ space=identifier)* | roundRobinItem (COMMA_ roundRobinItem)*)
    ;

fragmentByExpression
    : EXPRESSION expressionFragmentClause
    ;

fragmentByRange
    : RANGE LP_ expr RP_ intervalFragmentClause
    ;

fragmentByList
    : LIST LP_ expr RP_ listFragmentClause
    ;

roundRobinItem
    : partition inSpace
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=clause-expression-fragment#ids_sqs_2097
expressionFragmentClause
    : fragmentSpec (COMMA_ fragmentSpec)* (INDEX OFF)? remainderClause?
    ;

fragmentSpec
    : partition? (expr | LP_ expr RP_) inSpace
    ;

remainderClause
    : COMMA_ partition? REMAINDER inSpace (INDEX OFF)?
    ;

intervalFragmentClause
    : INTERVAL LP_ expr RP_ rollingWindowClause? (STORE IN)?
    (LP_ (space=identifier (COMMA_ space=identifier)* | userDefinedFunction) RP_)?
    intervalValuesSpec (COMMA_ intervalValuesSpec)*
    ;

intervalValuesSpec
    : partition rangeIntervalExpression inSpace
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=clause-rolling-window-create-table#ids_sqs_2111
rollingWindowClause
    : rollingWindowWithSize | rollingWindowNoSize | DROP ALL ROLLING
    ;

rollingWindowWithSize
    : (ROLLING LP_ quantity=INT_ FRAGMENTS RP_)? LIMIT TO size=INT_ sizeUnits (DETACH | DISCARD) (INTERVAL FIRST | ANY | INTERVAL ONLY)?
    ;

rollingWindowNoSize
    :ROLLING LP_ quantity=INT_ FRAGMENTS RP_ (DETACH | DISCARD)?
    ;

sizeUnits
    : K | KB | KiB| M| MB| MiB| G| GB| GiB| T| TB| TiB
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=clause-list-fragment#ids_sqs_2096
listFragmentClause
    : listFragmentSpec (COMMA_ listFragmentSpec)* (COMMA_ partition REMAINDER inSpace)?
    ;

listFragmentSpec
    : partition listExpressionClause inSpace
    ;

listExpressionClause
    : VALUES LP_ constantExpression (COMMA_ constantExpression)* RP_
    | VALUES IS? NULL
    ;

// https://www.ibm.com/docs/zh/SSGU8G_15.1.0/com.ibm.sqls.doc/ids_sqs_0107.htm#ids_sqs_0107
putClause
    : PUT putItemSpec (COMMA_ putItemSpec)*
    ;

putColumnInSpec
    : columnName IN LP_ space=identifier (COMMA_ space=identifier)* RP_
    ;

putItemSpec
    : putColumnInSpec (LP_ (putItemOption (COMMA_ putItemOption)*)? RP_)?
    ;

putItemOption
    : EXTENT SIZE INT_ | NO? LOG | HIGH INTEG | MODERATE INTEG | NO? KEEP ACCESS TIME
    ;

extentSizeOptions
    : (EXTENT SIZE expr)? (NEXT SIZE expr)?
    ;

compressedOption
    : COMPRESSED
    ;

lockMode
    : LOCK MODE (PAGE | ROW)
    ;

// https://www.ibm.com/docs/zh/SSGU8G_15.1.0/com.ibm.sqls.doc/ids_sqs_0156.htm#ids_sqs_0156
projectionClause
    : (SKIP_ offset=INT_)? ((FIRST | LIMIT) max=INT_)? (ALL | DISTINCT | UNIQUE)? selectList (COMMA_ selectList)*
    ;

selectList
    : unqualifiedShorthand
    | qualifiedShorthand
    | columnName (AS? alias)?
    | expr (AS? alias)?
    | subquery
    | LP_ collectionSubquery RP_
    ;

unqualifiedShorthand
    : ASTERISK_
    ;

qualifiedShorthand
    : (tableName | viewName | synonymName | alias) DOT_ASTERISK_
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statement-into-clause#ids_sqs_0158
intoClause
    : INTO output_var=identifierVariable (COMMA_ output_var=identifierVariable)*
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=ss-from-clause#ids_sqs_0160
fromClause
    : FROM tableReferences
    ;

tableReferences
    : tableReference (COMMA_ tableReference)*
    ;

tableReference
    : tableSource (joinClause* | (COMMA_ outerJoinClause)*)
    | LP_ tableReference RP_ (joinClause* | (COMMA_ outerJoinClause)*)
    | lateralDerivedTable
    ;

tableSource
    : (tableName | synonymName | viewName | ONLY LP_ (tableName | synonymName) RP_) (AS? alias)? columnNames?
    | collectionDerivedTable
    | iteratorFunction
    | LP_ collectionSubquery RP_
    | subquery (AS? alias)? columnNames?
    | LP_ tableSource RP_ (AS? alias)? columnNames?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=segments-collection-derived-table#ids_sqs_1631
collectionDerivedTable
    : TABLE LP_ expr RP_ (AS? alias)? columnNames?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=clause-iterator-functions#ids_sqs_1021
iteratorFunction
    : TABLE LP_ (FUNCTION | PROCEDURE)? iterator=identifier LP_ routineParameterList? RP_ RP_ (AS? alias)? columnNames?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=segments-routine-parameter-list#ids_sqs_1737
routineParameterList
    : parameter (COMMA_ parameter)*
    ;

parameter
    : (IN | OUT | INOUT)? (parameterName=identifier)? (
    (dataType | LIKE columnName) (DEFAULT constantExpression)?
    | REFERENCES (BYTE | TEXT) (DEFAULT NULL)?
    )
    ;

joinClause
    : joinType tableReference joinOnCaluse?
    ;

joinType
    : (INNER | (LEFT | RIGHT | FULL) OUTER?)? JOIN
    | CROSS JOIN
    ;

joinOnCaluse
    : ON expr
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=clause-informix-extension-outer-joins#ids_sqs_1035
outerJoinClause
    : OUTER (
    tableReference
    | LP_ tableReference (COMMA_ tableReference)* COMMA_ outerJoinClause (COMMA_ outerJoinClause)*  RP_
    | LP_ outerJoinClause (COMMA_ outerJoinClause)* COMMA_ tableReference (COMMA_ tableReference)* RP_
    )
    ;

lateralDerivedTable
    : LATERAL subquery (AS? alias)? columnNames?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statement-grid-clause
gridClause
    : GRID ALL? STRING_
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statement-where-clause-select
whereClause
    : WHERE expr | WHERE CURRENT OF cursorId
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statement-hierarchical-clause
hierarchicalClause
    : startWithClause? connectByClause
    ;

startWithClause
    : START WITH expr
    ;

connectByClause
    : CONNECT BY NOCYCLE? PRIOR? expr
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statement-group-by-clause
groupByClause
    : GROUP BY groupByItem (COMMA_ groupByItem)*
    ;

groupByItem
    : INT_ | columnName
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statement-having-clause
havingClause
    : HAVING expr
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=segments-optimizer-directives#ids_sqs_1691
optimizerDirectives
    : BLOCK_DIRECTIVES | INLINE_DIRECTIVES
    ;

set
    : setAutofree
    | setCollation
    | setConnection
    | setConstraints
    | setIndexes
    | setTriggers
    | setDatabaseObjectMode
    | setDataskip
    | setDebugFile
    | setDeferredPrepare
    | setDescriptor
    | setEncryptionPassword
    | setEnvironment
    | setExplain
    | setIsolation
    | setLockMode
    | setLog
    | setOptimization
    | setPdqpriority
    | setRole
    | setSessionAuthorization
    | setStatementCache
    | setTransaction
    | setUserPassword
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-autofree-statement
setAutofree
    : SET AUTOFREE (ENABLED | DISABLED)? (FOR cursorId)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-collation-statement
setCollation
    : SET (COLLATION locale = stringLiterals | NO COLLATION)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-connection-statement
setConnection
    : SET CONNECTION (
    (DEFAULT | stringVariable | databaseEnvironment) DORMANT?
    | CURRENT DORMANT
    )
    ;

//https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-constraints-statement
setConstraints
    : SET CONSTRAINTS (
    (constraintName (COMMA_ constraintName)* | ALL) (IMMEDIATE | DEFERRED)
    | (constraintName (COMMA_ constraintName)* | FOR tableName) constraintMode
    )
    ;

constraintMode
    : DISABLED | ENABLED NOVALIDATE? | FILTERING (WITHOUT ERROR | WITH ERROR) NOVALIDATE? | NOVALIDATE
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-database-object-mode-statement
setDatabaseObjectMode
    : SET (CONSTRAINTS | INDEXES | TRIGGERS) (COMMA_ (CONSTRAINTS | INDEXES | TRIGGERS))* FOR tableName (constraintMode | triggerMode)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-dataskip-statement
setDataskip
    : SET DATASKIP (ON (identifier (COMMA_ identifier)*)? | OFF | DEFAULT)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-debug-file-statement
setDebugFile
    : SET DEBUG FILE TO (stringVariable | expr) (WITH APPEND)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-deferred-prepare-statement
setDeferredPrepare
    : SET DEFERRED_PREPARE (ENABLED | DISABLED)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-descriptor-statement
setDescriptor
    : SET DESCRIPTOR descriptor=stringVariable (
    COUNT EQ_ numberVariable
    | VALUE numberVariable itemDescriptor (COMMA_ itemDescriptor)*
    )
    ;

itemDescriptor
    : (TYPE | LENGTH | PRECISION | SCALE | NULLABLE | INDICATOR | ITYPE | ILENGTH) EQ_ numberVariable
    | (DATA | IDATA) EQ_ (literals | hostVariable)
    | (NAME | EXTYPENAME | EXTYPEOWNERNAME) EQ_ stringVariable
    | (SOURCEID | SOURCETYPE | EXTYPEID | EXTYPELENGTH | EXTYPEOWNERLENGTH) EQ_ numberVariable
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-encryption-password-statement
setEncryptionPassword
    : SET ENCRYPTION PASSWORD password=stringVariable (WITH HINT hint=stringVariable)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-environment-statement
setEnvironment
    : SET ENVIRONMENT name (ON | OFF | DEFAULT | stringLiterals | numberLiterals) (COMMA_ numberLiterals)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-explain-statement
setExplain
    : SET EXPLAIN (OFF | ON AVOID_EXECUTE? | FILE TO (stringVariable | expr))
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-indexes-statement
setIndexes
    : SET INDEXES (indexName (COMMA_ indexName)* | FOR tableName) (indexVisibilityOptions | indexMode)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-isolation-statement
setIsolation
    : SET ISOLATION TO? (
    REPEATABLE READ
    | (COMMITTED READ (LAST COMMITTED)? | CURSOR STABILITY | DIRTY READ (WITH WARNING)?) (RETAIN UPDATE LOCKS)?
    )
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-lock-mode-statement
setLockMode
    : SET LOCK MODE TO (NOT WAIT | WAIT numberLiterals?)
    ;

//https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-log-statement
setLog
    : SET BUFFERED? LOG
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-optimization-statement
setOptimization
    : SET OPTIMIZATION (HIGH | LOW | FIRST_ROWS | ALL_ROWS | environmentOptions)
    ;

environmentOptions
    : ENVIRONMENT (STAR_JOIN stringLiterals
    | USE_INVISIBLE_INDEXES stringLiterals
    | (FACT | AVOID_FACT | NON_DIM) (stringLiterals | DEFAULT)
    )
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-pdqpriority-statement
setPdqpriority
    : SET PDQPRIORITY (DEFAULT | LOW | OFF | HIGH | resources=numberLiterals)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-role-statement
setRole
    : SET ROLE (NULL | NONE | DEFAULT | roleName)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-session-authorization-statement
setSessionAuthorization
    : SET SESSION AUTHORIZATION TO stringVariable (USING password=stringVariable)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-statement-cache-statement
setStatementCache
    : SET STATEMENT CACHE (ON | OFF)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-transaction-statement
setTransaction
    : SET TRANSACTION setTransactionOption (COMMA_ setTransactionOption)*
    ;

setTransactionOption
    : READ WRITE | READ ONLY | ISOLATION LEVEL (READ COMMITTED | REPEATABLE READ | SERIALIZABLE | READ UNCOMMITTED)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-triggers-statement
setTriggers
    : SET TRIGGERS (triggerName (COMMA_ triggerName)* | FOR (tableName | viewName)) triggerMode?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-set-user-password-statement-unix-linux
setUserPassword
    : SET USER PASSWORD OLD old_password=stringLiterals NEW new_password=stringLiterals
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-start-violations-table-statement
startViolationsTable
    : START VIOLATIONS TABLE FOR tableName (USING violations=identifier COMMA_ diagnostics=identifier)? (MAX ROWS numberLiterals)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-stop-violations-table-statement
stopViolationsTable
    : STOP VIOLATIONS TABLE FOR tableName
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-truncate-statement
truncateTable
    : TRUNCATE TABLE? (tableName | synonymName) (DROP STORAGE |  REUSE STORAGE KEEP STATISTICS)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-unload-statement
unload
    : UNLOAD TO filename=stringLiterals (DELIMITER delimiter=stringLiterals)? (hostVariable | select)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-unlock-table-statement
unlockTable
    : UNLOCK TABLE (tableName | synonymName)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-update-statistics-statement
updateStatistics
    : UPDATE STATISTICS (
    LOW? tableAndColumnScope? (DROP DISTRIBUTIONS ONLY?)? (FORCE | AUTO)?
    | (MEDIUM | HIGH) tableAndColumnScope? resolutionClause (FORCE | AUTO)?
    | routineStatistics
    )
    ;

tableAndColumnScope
    : FOR TABLE (
    (tableName | synonymName) columnNames?
    | ONLY LP_ (tableName | synonymName) RP_ columnNames?
    )?
    ;

resolutionClause
    : resolutionForMediumMode | resolutionForHighMode
    ;

resolutionForMediumMode
    : (SAMPLING SIZE min=numberLiterals)? (RESOLUTION percent=numberLiterals (confidence=numberLiterals)?)? (DISTRIBUTIONS ONLY)?
    ;

resolutionForHighMode
    : (RESOLUTION percent=numberLiterals)? (DISTRIBUTIONS ONLY)?
    ;

routineStatistics
    : FOR (
    (PROCEDURE | FUNCTION | ROUTINE) (routineName (LP_ routineParameterList? RP_)?)?
    | SPECIFIC (PROCEDURE | FUNCTION | ROUTINE) objectName
    )
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-whenever-statement
whenever
    : WHENEVER (SQLERROR | NOT FOUND | SQLWARNING | ERROR) (CONTINUE | (GOTO | GO TO) label=identifierVariable | CALL routineName | STOP)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=statements-statement-common-table-expressions
withClause
    : WITH withTableClause (COMMA_ withTableClause)*
    ;

withTableClause
    : cte_name=identifier columnNames? AS subquery
    ;

// TODO ================================================================================

literals
    : stringLiterals
    | numberLiterals
    | dateTimeLiterals
    | hexadecimalLiterals
    | booleanLiterals
    | intervalLiterals
    | collectionLiterals
    | rowLiterals
    | nullValueLiterals
    ;

stringLiterals
    : STRING_ | DOUBLE_QUOTED_TEXT
    ;

numberLiterals
    : (PLUS_ | MINUS_)? (INT_ | NUMBER_)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=dte-literal-row#ids_sqs_1608
rowLiterals
    : ROW LP_ literals (COMMA_ literals)* RP_
    | STRING_ROW
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=dte-literal-collection
collectionLiterals
    : LITERAL_COLLECTION //(SET | MULTISET | LIST) LBE_ (literals (COMMA_ literals)*)? RBE_
    | collectionSubquery
    | STRING_COLLECTION
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expressions-collection-subquery
collectionSubquery
    : MULTISET subquery
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=dte-literal-datetime#ids_sqs_1600
dateTimeLiterals
    : DATETIME LP_ dateTimeValue RP_ dateTimeFieldQualifier
    | DATE LP_ stringLiterals? RP_
    ;

dateTimeValue
    : INT_ (
        '-' mo=INT_ ('-' dd=INT_ (hh=INT_ (':' mi=INT_ (':' ss=(INT_ | NUMBER_))?)?)?)?
        | '-' dd=INT_ (hh=INT_ (':' mi=INT_ (':' ss=(INT_ | NUMBER_))?)?)?
        | hh=INT_ (':' mi=INT_ (':' ss=(INT_ | NUMBER_))?)?
        | ':' mi=INT_ (':' ss=(INT_ | NUMBER_))?
        | ':' ss=(INT_ | NUMBER_))?
    | ss=(INT_ | NUMBER_)
    ;


// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expressions-datetime-field-qualifier#ids_sqs_1424
dateTimeFieldQualifier
    : YEAR (TO (YEAR | MONTH | DAY | HOUR | MINUTE | SECOND | FRACTION (LP_ scale RP_)?))
    | MONTH (TO (MONTH | DAY | HOUR | MINUTE | SECOND | FRACTION (LP_ scale RP_)?))
    | DAY (TO (DAY | HOUR | MINUTE | SECOND | FRACTION (LP_ scale RP_)?))
    | HOUR (TO (HOUR | MINUTE | SECOND | FRACTION (LP_ scale RP_)?))
    | MINUTE (TO (MINUTE | SECOND | FRACTION (LP_ scale RP_)?))
    | SECOND (TO (SECOND | FRACTION (LP_ scale RP_)?))
    | FRACTION TO FRACTION (LP_ scale RP_)?
    ;

hexadecimalLiterals
    : HEX_DIGIT_
    ;

booleanLiterals
    : TRUE | FALSE | BOOL_T | BOOL_F
    ;

nullValueLiterals
    : NULL
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=dte-literal-interval#ids_sqs_1602
intervalLiterals
    : INTERVAL LP_ (PLUS_ | MINUS_)? (INT_ (
    '-' mo=INT_
    | hh=INT_ (':' mi=INT_ (':' ss=NUMBER_)?)?
    | ':' mi=INT_ (':' ss=NUMBER_)?
    | ':' ss=NUMBER_
    )? | NUMBER_) RP_ intervalFieldQualifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expressions-interval-field-qualifier#ids_sqs_1596
intervalFieldQualifier
    : DAY (LP_ precision RP_)? (TO (DAY | HOUR | MINUTE | SECOND | FRACTION (LP_ scale RP_)?))?
    | HOUR (LP_ precision RP_)? (TO (HOUR | MINUTE | SECOND | FRACTION (LP_ scale RP_)?))?
    | MINUTE (LP_ precision RP_)? (TO (MINUTE | SECOND | FRACTION (LP_ scale RP_)?))?
    | SECOND (LP_ precision RP_)? (TO (SECOND | FRACTION))?
    | FRACTION TO FRACTION (LP_ scale RP_)?
    | YEAR (LP_ precision RP_)? (TO (YEAR | MONTH))?
    | MONTH (LP_ precision RP_)? TO MONTH
    ;

precision
    : INT_
    ;

scale
    : INT_
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expressions-condition
expr
    :  condition rightExpr*
    ;

rightExpr
    : logicalOperator condition
    ;

logicalOperator
    : AND | OR
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=condition-comparison-conditions-boolean-expressions
// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=condition-subquery#ids_sqs_0178
condition
    : NOT?
    ( expression relationalOperator (ALL | ANY | SOME)? subquery
    | expression relationalOperator expression
    | betweenCondition
    | inCondition
    | expression IS NOT? NULL
    | likeCondition
    | triggerTypeBooleanOperator
    | NOT? EXISTS subquery
    | functionExpression
    | LP_ collectionSubquery RP_
    | expression
    | LP_ expr RP_
    )
    ;

relationalOperator
    : GT_ | GTE_ | LT_ | LTE_ | EQ_ | DEQ_ | NEQ_
    ;

betweenCondition
    : expression NOT? BETWEEN expression AND expression
    ;

likeCondition
    : expression NOT? (LIKE| MATCHES) expression (ESCAPE expression)?
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=condition-trigger-type-boolean-operator#ids_sqs_1385
triggerTypeBooleanOperator
    : DELETING | INSERTING | SELECTING | UPDATING
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=condition-in#ids_sqs_1382
inCondition
    : expression NOT? IN (
        LP_ (expression (COMMA_ expression)*)? RP_
        | collectionLiterals
        | rowLiterals
        | identifier
        | subquery
    )
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expressions-expression
expression
    : expressionItem rightExpression*
    ;

rightExpression
    : binaryOperators expressionItem
    ;

binaryOperators
    : PLUS_ | MINUS_ | ASTERISK_ | SLASH_ | OR_
    ;

expressionItem
    : (PLUS_ | MINUS_)? (castExpression
    | conditionalExpression
    | constantExpression
    | functionExpression
    | olapWindowExpression
    | subquery
    | LP_ expression RP_
    | columnName
    | hostVariable
    ) (DOUBLE_COLON_ dataType)*
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expression-cast-expressions#ids_sqs_0191
castExpression
    : CAST LP_ expression (DOUBLE_COLON_ dataType)* AS dataType RP_
//    | expr (DOUBLE_COLON_ dataType)+
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expression-constant-expressions#ids_sqs_0184
constantExpression
    : literals
    | USER | CURRENT_USER | CURRENT_ROLE | DEFAULT_ROLE | SITENAME | DBSERVERNAME | TODAY
    | (CURRENT | SYSDATE) dateTimeFieldQualifier?
    | numberLiterals UNITS timeUnit
    | (sequenceName | synonymName) DOT_ (CURRVAL | NEXTVAL)
    | identifier
    ;
timeUnit
    : DAY  | HOUR  | MINUTE  | MONTH  | SECOND | YEAR | FRACTION
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expression-conditional-expressions#ids_sqs_0183
conditionalExpression
    : coalesceFunction
    | nvlFunction
    | nullifFunction
    | decodeFunction
    | caseExpression
    ;

coalesceFunction
    : COALESCE LP_ expression (COMMA_ expression)* RP_
    ;

nvlFunction
    : (NVL | NVL2) LP_ expression (COMMA_ expression)* RP_
    ;

nullifFunction
    : NULLIF LP_ expression (COMMA_ expression)* RP_
    ;

decodeFunction
    : DECODE LP_ expression (COMMA_ expression)* RP_
    ;

functionExpression
    : aggregateFunction | builtinFunction | bsonProcessingFunction | userDefinedFunction
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expression-function-expressions
builtinFunction
    : builtinFunctionName LP_ (expression (COMMA_ expression)*)? RP_
    | SQLCODE
    | EXTEND LP_ expression (COMMA_ dateTimeFieldQualifier)? RP_
    | TRIM LP_ ((BOTH | TRAILING | LEADING) expression FROM)? expression RP_
    | SUBSTRING LP_ expression FROM start_position=numberLiterals (FOR length=numberLiterals)? RP_
    ;

builtinFunctionName
    : ABS | CEIL | FLOOR | GREATEST | LEAST | MOD | POW | POWER | ROOT | ROUND | TRUNC | SQRT | IFX_BIT_LEFTSHIFT | IFX_BIT_RIGHTSHIFT
    | CARDINALITY | DBINFO | ENCRYPT_AES | ENCRYPT_TDES | DECRYPT_CHAR | DECRYPT_BINARY | GETHINT | DECRYPT_CHAR | DECRYPT_BINARY
    | ENCRYPT_AES | ENCRYPT_TDES | EXP | LN | LOGN | LOG10 | HEX | LENGTH | LEN | CHAR_LENGTH | CHARACTER_LENGTH
    | OCTET_LENGTH | SECLABEL_TO_CHAR | SECLABEL_BY_COMP | SECLABEL_BY_NAME | SIGN | FILETOBLOB | FILETOCLOB | LOTOFILE
    | LOCOPY | DATE | DAY | LAST_DAY | MONTH | QUARTER | WEEKDAY | YEAR | MDY | ADD_MONTHS | MONTHS_BETWEEN | NEXT_DAY
    | TO_CHAR | TO_DATE | TO_NUMBER | COS | COSH | SIN | SINH | TAN | TANH | DEGREES | ASIN | ASINH | ACOS | ACOSH
    | ATAN | ATANH | ATAN2 | RADIANS | CONCAT | ASCII | LTRIM | RTRIM | SPACE | REVERSE | REPLACE | LPAD | RPAD | CHR
    | UPPER | LOWER | INITCAP | CHARINDEX | INSTR | LEFT | RIGHT | SUBSTR | SUBSTRB | SUBSTRING_INDEX | FORMAT_UNITS
    | IFX_ALLOW_NEWLINE | TO_DSINTERVAL | NUMTODSINTERVAL | IFX_REPLACE_MODULE | IFX_UNLOAD_MODULE
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expression-aggregate-expressions#ids_sqs_0189
aggregateFunction
    : aggregateFunctionName LP_ (aggregateScopeQualifiers? (expression | ASTERISK_)) RP_
    | userDefinedAggregates
    ;
 // 用户自定义聚合函数，这里范围操作符必填，和 userDefinedFunction 区分开
userDefinedAggregates
    : aggregate=identifier LP_ (aggregateScopeQualifiers (expression | ASTERISK_) (COMMA_ expression)?) RP_
    ;

aggregateFunctionName
    : COUNT | AVG | MAX | MIN | SUM | RANGE | STDEV | VARIANCE
    ;

aggregateScopeQualifiers
    : (ALL | DISTINCT | UNIQUE)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expressions-user-defined-functions
userDefinedFunction
    : functionName LP_ (funcParameter (COMMA_ funcParameter)*)? (COMMA_ localVariableDeclaration)? RP_
    ;

funcParameter
    : (identifier EQ_)? expression
    ;

localVariableDeclaration
    : name '#' dataType
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=routines-bson-processing-functions
bsonProcessingFunction
    : bsonProcessingFunctionName LP_ (expression (COMMA_ expression)*)? RP_
    ;

bsonProcessingFunctionName
    : BSON_GET | BSON_SIZE | BSON_UPDATE | BSON_VALUE_BIGINT | BSON_VALUE_BOOLEAN | BSON_VALUE_DATE | BSON_VALUE_DOUBLE
    | BSON_VALUE_INT | BSON_VALUE_LVARCHAR | BSON_VALUE_OBJECTID | BSON_VALUE_TIMESTAMP | BSON_VALUE_VARCHAR | GENBSON
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expressions-olap-window
olapWindowExpression
    : olapNumberingFunctionExpression | olapRankingFunctionExpression | olapAggregatingFunctionExpression
    ;

olapNumberingFunctionExpression
    : olapNumberingFunction OVER LP_ windowPartitionClause? windowOrderClause? RP_
    ;

olapNumberingFunction
    : (ROW_NUMBER | ROWNUMBER) LP_ RP_
    ;

olapRankingFunctionExpression
    : olapRankingFunction OVER LP_ windowPartitionClause? windowOrderClause RP_
    ;

olapRankingFunction
    : lagAndLeadFunction
    | ntileFunction
    | (RANK | DENSE_RANK | DENSERANK | PERCENT_RANK | CUME_DIST) LP_ RP_
    ;

lagAndLeadFunction
    : (LAG | LEAD) LP_ expression respectOrIgnoreNulls? (COMMA_ expression (COMMA_ expression)?)? RP_
    | (LAG | LEAD) LP_ expression (COMMA_ expression (COMMA_ expression)?)? RP_ respectOrIgnoreNulls?
    ;

ntileFunction
    : NTILE LP_ expression RP_
    ;

respectOrIgnoreNulls
    : (RESPECT | IGNORE) NULLS
    ;

olapAggregatingFunctionExpression
    : (ratioToReportFunction | firstOrLastValueFunction | aggregateFunction) OVER LP_ windowPartitionClause? (windowOrderClause windowFrameClause?)? RP_
    ;

ratioToReportFunction
    : (RATIO_TO_REPORT | RATIOTOREPORT) LP_ expression RP_
    ;

firstOrLastValueFunction
    : (FIRST_VALUE | LAST_VALUE) LP_ expression respectOrIgnoreNulls? RP_
    | (FIRST_VALUE | LAST_VALUE) LP_ expression RP_ respectOrIgnoreNulls?
    ;

windowPartitionClause
    : PARTITION BY columnName (COMMA_ columnName)*
    ;

windowOrderClause
    : ORDER BY windowOrderItem (COMMA_ windowOrderItem)*
    ;

windowOrderItem
    : columnName (ASC | DESC)? (NULLS (FIRST | LAST)?)?
    ;

windowFrameClause
    : (RANGE | ROWS) (
    windowFramePosition
    | BETWEEN left=windowFramePosition AND right=windowFramePosition
    )
    ;

windowFramePosition
    : UNBOUNDED (PRECEDING | FOLLOWING)
    | CURRENT ROW
    | expression (PRECEDING | FOLLOWING)
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expressions-linear-case
caseExpression
    : CASE expression? caseWhen+ caseElse? END
    ;

caseWhen
    : WHEN expr THEN expression
    ;

caseElse
    : ELSE expression
    ;

subquery
    : LP_ select RP_
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expressions-data-type
dataType
    : builtInDataTypes | complexDataType | userDefinedDataType
    ;

builtInDataTypes
    : dataTypeName dataLength?
    ;

dataTypeName
    : BOOLEAN | JSON | BSON | IDSSECURITYLABEL | CHAR | CHARACTER | NCHAR | NVARCHAR | VARCHAR | CHARACTER VARYING
    | LVARCHAR | DECIMAL | DEC | NUMERIC | MONEY
    | BIGINT | INT | INTEGER | INT8 | SMALLINT | BIGSERIAL | SERIAL | SERIAL8 | FLOAT | DOUBLE PRECISION | SMALLFLOAT
    | REAL | BLOB | CLOB | DATE | INTERVAL intervalFieldQualifier | DATETIME dateTimeFieldQualifier
    | (TEXT | BYTE) (IN (TABLE | identifier))?
    ;

dataLength
    : LP_ (precision (COMMA_ scale)?)? RP_
    ;

userDefinedDataType
    : typeName | distinctType
    ;

distinctType
    : (DISTINCT OF dataType)+
    ;
// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=type-complex-data
complexDataType
    : rowDataType | collectionDataType
    ;
rowDataType
    : ROW (LP_ rowDataField (COMMA_ rowDataField)* RP_)?
    ;

rowDataField
    : field dataType
    ;

collectionDataType
    : COLLECTION
    | (SET | MULTISET | LIST) LP_ (dataType | LP_ dataType NOT NULL RP_) NOT NULL RP_
    ;

//jsonObject
//    : jsonDocument | jsonArray
//    ;
//
//jsonPair
//    : key=DOUBLE_QUOTED_TEXT ':' jsonValue
//    ;
//
//jsonValue
//    : DOUBLE_QUOTED_TEXT
//    | numberLiterals
//    | nullValueLiterals
//    | booleanLiterals
//    | jsonDocument
//    | jsonArray
//    ;
//
//jsonDocument
//    : '{' jsonPair (COMMA_ jsonPair)* '}'
//    ;
//
//jsonArray
//    : '[' (jsonValue (COMMA_ jsonValue)*)? ']'
//    ;

stringVariable
    : stringLiterals | hostVariable
    ;

numberVariable
    : numberLiterals | hostVariable
    ;

identifierVariable
    : identifier | hostVariable
    ;

hostVariable
    : (':' | '$' | INDICATOR) identifier
    ;
// informix 基本没有保留关键字，如果使用关键字做名称，可能引起编辑或运行错误，或语义错误
// 若要使用关键字，则需要使用双引号包裹
identifier
    : IDENTIFIER_ | unreservedKeyword | DOUBLE_QUOTED_TEXT
    ;

unreservedKeyword
    : ABORT | ACTION | ANALYZE | AUTOINCREMENT | COLLATE | CONFLICT | CURRENT_DATE | CURRENT_TIME | CURRENT_TIMESTAMP
    | DEFERRABLE | FAIL | GLOB | IGNORE | INDEXED | INITIALLY | ISNULL | MATCH | NATURAL | NOTNULL | OFFSET | PLAN
    | PRAGMA | QUERY | RECURSIVE | REGEXP | REINDEX | TEMPORARY | VACUUM | VIRTUAL | NTH_VALUE | GENERATED | ALWAYS
    | STORED | WINDOW | FILTER | GROUPS | EXCLUDE | SYSDATE | COALESCE | NVL2 | GREATEST | LEAST
    | IFX_BIT_LEFTSHIFT | IFX_BIT_RIGHTSHIFT | LN | SIGN | SINH | TANH | SUBSTRB | IFX_ALLOW_NEWLINE | BSON_GET
    | BSON_SIZE | BSON_UPDATE | BSON_VALUE_BIGINT | BSON_VALUE_BOOLEAN | BSON_VALUE_DATE | BSON_VALUE_DOUBLE
    | BSON_VALUE_INT | BSON_VALUE_LVARCHAR | BSON_VALUE_OBJECTID | BSON_VALUE_TIMESTAMP | BSON_VALUE_VARCHAR
    | RESPECT | IFX_REPLACE_MODULE | IFX_UNLOAD_MODULE | KiB | MiB | GiB | TiB | SKIP_ | REPLCHECK | ITER | COMBINE
    | DBMONEY | C | INVISIBLE | VISIBLE | LARGE | SMALL | INPUT | MORE_ | INFORMIX_SQLCODE | SPL | INFO | HEADINGS
    | USE_INVISIBLE_INDEXES | NAME | ACCOUNT | DATA | DBSA | T | ADDRESS | K | INFORMIX | INTEGER | CLASS | BSON
    | LENGTH | LEVEL | DATE | TABLE | VALUE | DELIMITER | NEW | LEN | TYPE | EXTEND | OPAQUE | M | MINUTE
    | STATUS | CYCLE | COUNT | PASSWORD | TEST | FACT | TEMP | OUTER | INNER | LAST | FIRST | LIMIT | RESUME
    | DAY | RANK | DENSE_RANK | STORE | YEAR | QUARTER
    ;

tableName
    : objectName
    ;

viewName
    : objectName
    ;

sequenceName
    : objectName
    ;

typeName
    : objectName
    ;

synonymName
    : objectName
    ;

functionName
    : objectName
    ;

indexName
    : objectName
    ;

procedureName
    : objectName
    ;

routineName
    : objectName
    ;

constraintName
    : objectName
    ;

userName
    : name
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=expression-column-expressions#ids_sqs_0195
columnName
    : (objectName DOT_)? (
    ROWID
    | name ('[' first=INT_ COMMA_ last=INT_ ']')?
    | name DOT_ASTERISK_
    | name (DOT_ field)+
    )
    ;

field
    : identifier
    ;

columnNames
    : LP_ columnName (COMMA_ columnName)* RP_
    ;

databaseName
    : name (AT_ dbservername)?
    | stringVariable
    ;
dbservername
    : identifier
    ;

// https://www.ibm.com/docs/zh/informix-servers/15.0.0?topic=segments-database-object-name#ids_sqs_1649
objectName
    : (databaseName COLON_)? (owner DOT_)? name
    ;

owner
    : identifier | stringLiterals
    ;

name
    : identifier
    ;

alias
    : identifier
    ;
