parser grammar HetuParser;

options
{
    tokenVocab = HetuLexer;
}

root
    : (
    createSchema
    | createVirtualSchema
    | dropVirtualSchema
    | createTable
    | createView
    | createFunction
    | createMaterializedView
    | alterMaterializedView
    | alterTable
    | alterView
    | alterSchema
    | dropSchema
    | dropTable
    | dropView
    | dropFunction
    | dropMaterializedView
    | truncateTable
    | comment
    | values
    | show
    | insert
    | delete
    | update
    | load
    | startTransaction
    | commit
    | rollback
    | select
    | use
    | setSession
    | resetSession
    | describe
    | explain
    | refresh
    | analyze
    | call
    | prepare
    | deallocate
    | execute
    | verify
    ) SEMI_? EOF
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300128.html
createSchema
    : CREATE (DATABASE | SCHEMA) ifNotExists? schemaName commentClause? locationClause? (WITH DBPROPERTIES? LP_ propertiesList RP_)?
    ;

locationClause
    : LOCATION stringLiterals
    ;
commentClause
    : COMMENT stringLiterals
    ;

withProperties
    : WITH LP_ propertiesList RP_
    ;
propertiesList
    : keyValueProperty (COMMA_ keyValueProperty)*
    ;
keyValueProperty
    : identifier EQ_ expr
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300129.html
createVirtualSchema
    : CREATE VIRTUAL SCHEMA ifNotExists? schemaName virtualSchemaWithClause
    ;

virtualSchemaWithClause
    : WITH LP_ (CATALOG EQ_ catalogName COMMA_)? SCHEMA EQ_ schemaName (COMMA_ keyValueProperty)* RP_
    ;

dropVirtualSchema
    : DROP VIRTUAL SCHEMA ifExists? schemaName
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300130.html
createTable
    : CREATE EXTERNAL? TABLE ifNotExists? tableName (createTableClause | createTableAsClause | createTableLikeClause)
    ;

createTableClause
    : LP_ columnDefinition (COMMA_ columnDefinition)* RP_ tableOption*
    ;

createTableAsClause
    : (LP_ name (COMMA_ name)* RP_)? tableOption* AS select (WITH NO? DATA)?
    ;

createTableLikeClause
    : tableLikeClause tableOption*
    ;

columnDefinition
    : tableLikeClause | name dataType (NOT NULL)? commentClause? withProperties?
    ;

tableLikeClause
    : LIKE tableName ((INCLUDING | EXCLUDING) PROPERTIES)?
    ;

tableOption
    : commentClause
    | withProperties
    | partitionedByClause
    | tableBuckets
    | tableRowFormat
    | locationClause
    | tblPropertiesClause
    | storedAsClause
    | sortByClause
    ;

partitionedByClause
    : PARTITIONED BY LP_ partitionedByItem (COMMA_ partitionedByItem)* RP_
    ;

partitionedByItem
    : columnName dataType?
    ;

clusteredByClause
    : CLUSTERED BY columnNames
    ;
sortedByClause
    : SORTED BY columnNames
    ;
tableBuckets
    : clusteredByClause sortedByClause? INTO NUMBER_ BUCKETS
    ;

storedAsClause
    : STORED AS identifier
    ;

tableRowFormat
    : rowFormatDelimited
    | rowFormatSerde
    ;
rowFormatSerde
    : ROW FORMAT SERDE? string_ (
        WITH SERDEPROPERTIES LP_ propertiesList RP_
    )?
    ;
rowFormatDelimited
    : ROW FORMAT DELIMITED tableRowFormatFieldIdentifier? tableRowFormatCollItemsIdentifier? tableRowFormatMapKeysIdentifier?
        tableRowFormatLinesIdentifier? tableRowNullFormat?
    ;
tableRowFormatFieldIdentifier
    : FIELDS TERMINATED BY string_ (ESCAPED BY string_)?
    ;
tableRowFormatCollItemsIdentifier
    : COLLECTION ITEMS TERMINATED BY string_
    ;
tableRowFormatMapKeysIdentifier
    : MAP KEYS TERMINATED BY string_
    ;
tableRowFormatLinesIdentifier
    : LINES TERMINATED BY string_
    ;
tableRowNullFormat
    : NULL DEFINED AS string_
    ;

tblPropertiesClause
    : TBLPROPERTIES LP_ propertiesList RP_
    ;

sortByClause
    : SORT BY columnNames
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300133.html
createView
    : CREATE orReplace? VIEW viewName (LP_ viewColumnDefinition (COMMA_ viewColumnDefinition)* RP_)?
    commentClause? tblPropertiesClause? AS select
    ;

orReplace
    : OR REPLACE
    ;

viewColumnDefinition
    : columnName commentClause?
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300134.html
createFunction
    : CREATE FUNCTION functionName parameterList RETURNS dataType
    commentClause? (LANGUAGE JAVA?)? (SPECIFIC specificName=identifier)? (NOT? DETERMINISTIC)?
    (RETURNS NULL ON NULL INPUT | CALLED ON NULL INPUT)? (SYMBOL class_name=string_)?
    (URI hdfs_path_to_jar=string_)?
    ;

parameterList
    : LP_ parameter (COMMA_ parameter)* RP_
    ;

parameter
    : name dataType
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300135.html
createMaterializedView
    : CREATE MATERIALIZED VIEW ifNotExists? viewName commentClause? withProperties? columnNames? AS select
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300136.html
alterMaterializedView
    : ALTER MATERIALIZED VIEW viewName (setMaterializedViewStatus | setMaterializedViewProperties)
    ;

setMaterializedViewStatus
    : SET STATUS (INIT | SUSPEND | ENABLE | REFRESHING | DISABLE)
    ;

setMaterializedViewProperties
    : SET PROPERTIES keyValueProperty
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300138.html
alterTable
    : ALTER TABLE tableName alterTableClause
    ;

alterTableClause
    : renameTable
    | addColumnDefinition
    | dropColumn
    | renameColumn
    | addPartition
    | dropPartition
    | renamePartition
    | exchangePartition
    | setTblProperties
    | modifyColumn
    | setLocation
    | setFileformat
    | tableBuckets
    ;

renameTable
    : RENAME TO name
    ;

addColumnDefinition
    : ADD COLUMN columnDefinition
    ;

dropColumn
    : DROP COLUMN columnName
    ;

renameColumn
    : RENAME COLUMN columnName TO name
    ;

addPartition
    : ADD ifNotExists? partitionSpec locationClause? (COMMA_? partitionSpec locationClause?)*
    ;

partitionSpec
    : PARTITION LP_ partitionValue (COMMA_ partitionValue)* RP_
    ;
partitionValue
    : identifier comparisonOperator expr
    ;
dropPartition
    : DROP ifExists? partitionSpec (COMMA_ partitionSpec)*
    ;

renamePartition
    : partitionSpec RENAME TO partitionSpec
    ;

exchangePartition
    : EXCHANGE partitionSpec WITH TABLE tableName
    ;

setTblProperties
    : SET TBLPROPERTIES LP_ propertiesList RP_
    ;

modifyColumn
    : partitionSpec? CHANGE COLUMN? oldColumn=columnName name dataType commentClause? (FIRST|AFTER columnPosition=columnName)? (CASCADE|RESTRICT)?
    ;

setLocation
    : partitionSpec? SET LOCATION string_
    ;

setFileformat
    : partitionSpec? SET FILEFORMAT identifier
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300139.html
alterView
    : ALTER VIEW viewName (AS select | setTblProperties)
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300140.html
alterSchema
    : ALTER (DATABASE|SCHEMA) schemaName SET (
    locationClause
    | OWNER USER username
    | DBPROPERTIES LP_ propertiesList RP_
    )
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300141.html
dropSchema
    : DROP (DATABASE|SCHEMA) ifExists? schemaName (RESTRICT|CASCADE)?
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300142.html
dropTable
    : DROP TABLE ifExists? tableName
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300143.html
dropView
    : DROP VIEW ifExists? viewName
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300144.html
dropFunction
    : DROP FUNCTION ifExists? functionName
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300145.html
dropMaterializedView
    : DROP MATERIALIZED VIEW ifExists? viewName
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300147.html
truncateTable
    : TRUNCATE TABLE? tableName partitionSpec?
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300148.html
comment
    : COMMENT ON TABLE tableName IS string_
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300149.html
values
    : VALUES (signleValueRow (COMMA_ signleValueRow)* | multiValueRow (COMMA_ multiValueRow)*)
    ;

signleValueRow
    : expr
    ;

multiValueRow
    : LP_ expr (COMMA_ expr)* RP_
    ;
// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300151.html
show
    : showCatalogs
    | showSchemas
    | showTables
    | showTableProperties
    | showTableExt
    | showTableStats
    | showFunctions
    | showExternalFunctions
    | showExternalFunction
    | showSession
    | showPartitions
    | showColumns
    | showCreateTable
    | showViews
    | showCreateView
    | showMaterializedViews
    | showCreateMaterializedView
    | showVirtualSchemas
    ;

showCatalogs
    : SHOW CATALOGS likePattern?
    ;

showSchemas
    : SHOW (SCHEMAS|DATABASES) ((FROM| IN) catalogName)? likePattern?
    ;
showTables
    : SHOW TABLES ((FROM| IN) schemaName)? likePattern?
    ;
showTableProperties
    : SHOW TBLPROPERTIES (tableName | viewName) (LP_ identifier RP_)?
    ;
showTableExt
    : SHOW TABLE EXTENDED ((FROM| IN) schemaName)? LIKE pattern=string_ partitionSpec?
    ;
showTableStats
    : SHOW STATS FOR (tableName | subquery)
    ;
showFunctions
    : SHOW FUNCTIONS  likePattern?
    ;
showExternalFunctions
    : SHOW EXTERNAL FUNCTIONS
    ;
showExternalFunction
    : SHOW EXTERNAL FUNCTION functionName
    ;
showSession
    : SHOW SESSION
    ;
showPartitions
    : SHOW PARTITIONS tableName partitionSpec?
    ;
showColumns
    : SHOW COLUMNS ((FROM| IN) tableName)?
    ;
showCreateTable
    : SHOW CREATE TABLE tableName
    ;
showViews
    : SHOW VIEWS ((FROM| IN) schemaName)? likePattern?
    ;
showCreateView
    : SHOW CREATE VIEW viewName
    ;
showMaterializedViews
    : SHOW MATERIALIZED VIEWS ((FROM| IN) schemaName | WITH? TABLES)? likePattern?
    ;
showCreateMaterializedView
    : SHOW CREATE MATERIALIZED VIEW viewName
    ;
showVirtualSchemas
    : SHOW VIRTUAL SCHEMAS (FROM catalogName)? likePattern?
    ;

likePattern
    : LIKE pattern=string_ (ESCAPE escapeChar=string_)?
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300168.html
insert
    : INSERT (INTO | OVERWRITE) TABLE? tableName columnNames? partitionSpec? (select | values)
    | fromClause INSERT (INTO | OVERWRITE) TABLE tableName partitionSpec? select
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300169.html
delete
    : DELETE FROM tableName whereClause?
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300170.html
update
    : UPDATE tableName setClause whereClause?
    ;

setClause
    : SET setClauseElement (COMMA_ setClauseElement)*
    ;

setClauseElement
    : columnName EQ_ expr
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300171.html
load
    : LOAD DATA INPATH string_ OVERWRITE? INTO TABLE tableName partitionSpec?
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300173.html
startTransaction
    : START TRANSACTION (transactionMode (COMMA_ transactionMode)*)?
    ;

transactionMode
    : ISOLATION LEVEL (READ UNCOMMITTED | READ COMMITTED | REPEATABLE READ | SERIALIZABLE)
    | READ (ONLY | WRITE)
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300174.html
commit
    : COMMIT WORK?
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300175.html
rollback
    : ROLLBACK WORK?
    ;

queryHint
    : BLOCK_HINT | INLINE_HINT
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300177.html
select
    : queryExpression
    ;

combineType
    : (UNION | INTERSECT | EXCEPT) (ALL | DISTINCT)?
    ;

queryExpression
    : queryExpression combineType queryExpression
    | querySpecification
    | LP_ queryExpression RP_ orderByClause? offsetClause? limitClause? fetchClause?
    ;

querySpecification
    : queryHint? withClause? SELECT (ALL | DISTINCT)? projections fromClause? whereClause? groupByClause? havingClause?
          orderByClause? offsetClause? limitClause? fetchClause?
    ;

withClause
    : WITH RECURSIVE? cteClause (COMMA_ cteClause)*
    ;

cteClause
    : identifier (LP_ columnNames RP_)? AS subquery
    ;

projections
    : unqualifiedShorthand | projection (COMMA_ projection)*
    ;

projection
    : expr (AS? alias)? | qualifiedShorthand
    ;

unqualifiedShorthand
    : ASTERISK_
    ;

qualifiedShorthand
    : tableName DOT_ASTERISK_
    ;
fromClause
    : FROM tableReferences (TABLESAMPLE (BERNOULLI | SYSTEM) LP_ NUMBER_ RP_)?
    ;

tableReferences
    : tableReference (COMMA_ tableReference)*
    ;

tableReference
    : tableSource joinClause*
    ;

tableSource
    : tableName (AS? alias)? columnNames?
    | subquery (AS? alias)? columnNames?
    | values (AS? alias)? columnNames?
    | functionCall (AS? alias)? columnNames?
    | LP_ tableReference RP_ (AS? alias)? columnNames?
    ;

joinClause
    : joinType LATERAL? tableReference joinSpecification?
    ;

joinType
    : (LEFT | RIGHT) (OUTER | SEMI | ANTI)? JOIN
    | FULL OUTER? JOIN
    | (INNER | CROSS)? JOIN
    ;

joinSpecification
    : ON expr | USING LP_ columnNames RP_
    ;

whereClause
    : WHERE expr
    ;

groupByClause
    : GROUP BY (ALL | DISTINCT)? groupByItem (COMMA_ groupByItem)*
    ;

groupByItem
    : LP_ RP_
    | GROUPING SETS LP_ (columnNames | LP_ RP_) (COMMA_ (columnNames | LP_ RP_))* RP_
    | (CUBE | ROLLUP) (columnNames | LP_ RP_)
    | NUMBER_ | expr
    ;

havingClause
    : HAVING expr
    ;

orderByClause
    : ORDER BY orderByItem (COMMA_ orderByItem)*
    ;

orderByItem
    : (numberLiterals | expr) direction?
    ;
direction
    : ASC | DESC
    ;

offsetClause
    : OFFSET numberLiterals (ROW | ROWS)?
    ;

limitClause
    : LIMIT (numberLiterals | ALL)
    ;

fetchClause
    : FETCH (FIRST | NEXT) numberLiterals? (ROW | ROWS) (ONLY | WITH TIES)
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300222.html
use
    : USE schemaName
    ;


// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300223.html
setSession
    : SET SESSION (catalogName DOT_)? name EQ_ expr
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300224.html
resetSession
    : RESET SESSION (catalogName DOT_)? name
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300225.html
describe
    : (DESCRIBE | DESC) (
    (EXTENDED| FORMATTED)? tableName partitionSpec? columnName?
    | (DATABASE|SCHEMA) EXTENDED? schemaName
    | INPUT name
    | OUTPUT name
    )
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300230.html
explain
    : EXPLAIN (LP_ explainOption (COMMA_ explainOption)* RP_ | ANALYZE VERBOSE?)? explainStatement
    ;

explainStatement
    : select|insert|update|delete
    ;

explainOption
    : FORMAT (TEXT | GRAPHVIZ | JSON)
    | TYPE (LOGICAL | DISTRIBUTED | VALIDATE | IO)
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300232.html
refresh
    : REFRESH (CATALOG catalogName | SCHEMA schemaName | TABLE tableName | MATERIALIZED VIEW viewName)
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300235.html
analyze
    : ANALYZE tableName withProperties?
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300236.html
call
    : CALL procedureName LP_ (name FAT_ARROW_)? expr (COMMA_ (name FAT_ARROW_)? expr)* RP_
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300237.html
prepare
    : PREPARE name FROM select
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300238.html
deallocate
    : DEALLOCATE PREPARE name
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300239.html
execute
    : EXECUTE name (USING expr (COMMA_ expr)*)?
    ;

// https://support.huaweicloud.com/cmpntguide-lts-mrs/mrs_01_300240.html
verify
    : VERIFY MATERIALIZED VIEW MVNAME LP_ viewName (COMMA_ viewName)* RP_ ORIGINALSQL select
    ;


//TODO ===============================================================

parameterMarker
    : QUESTION_
    ;

literals
    : stringLiterals
    | numberLiterals
    | temporalLiterals
    | hexadecimalLiterals
    | bitValueLiterals
    | booleanLiterals
    | nullValueLiterals
    ;

string_
    : DOUBLE_QUOTED_TEXT | SINGLE_QUOTED_TEXT
    ;

stringLiterals
    : UNDERSCORE_CHARSET? string_ | NCHAR_TEXT | UNICODE_STRING (UESCAPE SINGLE_QUOTED_TEXT)?
    ;

numberLiterals
    : (PLUS_ | MINUS_)? NUMBER_
    ;

temporalLiterals
    : (DATE | TIME | TIMESTAMP) SINGLE_QUOTED_TEXT
    ;

hexadecimalLiterals
    : UNDERSCORE_CHARSET? HEX_DIGIT_ collateClause?
    ;

bitValueLiterals
    : UNDERSCORE_CHARSET? BIT_NUM_ collateClause?
    ;

booleanLiterals
    : TRUE | FALSE
    ;

nullValueLiterals
    : NULL
    ;

collationName
    : identifier | BINARY
    ;

identifier
    : IDENTIFIER_
    | DOUBLE_QUOTED_TEXT
    | SINGLE_QUOTED_TEXT
    | BQUOTA_STRING
    | unreservedWord
    ;

unreservedWord
    : ADD | EXTERNAL | DEFAULT | YEAR | FILTER | SYSTEM | UUID
    | AFTER | AGAINST | ANALYZE | ANTI | ASC | ASCII | BIT | BOOL | BUCKETS | BYTE | CASCADE | CATALOG | CATALOGS
    | CHANGE | CHAR_VARYING | CHARACTER_VARYING | CHARSET | CLUSTERED | COLLECTION | COLUMNS | COMMENT | COMMITTED
    | DATA | DATABASE | DATABASES | DATETIME | DAY_HOUR | DAY_MICROSECOND | DAY_MINUTE | DAY_SECOND | DBPROPERTIES
    | DEFINED | DELIMITED | DESC | DISABLE | DISTRIBUTED | DIV | ENABLE | ENUM | ERROR | ESCAPED | EXCHANGE | EXCLUDING | EXPANSION | EXPLAIN
    | EXTENDED | FIELDS | FILEFORMAT | FIRST | FIXED | FOLLOWING | FORMAT | FORMATTED | FUNCTIONS | GEOMCOLLECTION | GEOMETRY
    | GEOMETRYCOLLECTION | GRAPHVIZ | GROUP_CONCAT | HOUR_MICROSECOND | HOUR_MINUTE | HOUR_SECOND | IGNORE | INCLUDING
    | INDEX | INIT | INPATH | INPUT | IO | ISOLATION | ITEMS | JAVA | KEYS | LAST | LAST_DAY | LEVEL | LIMIT | LINES
    | LINESTRING | LOAD | LOCATION | LOGICAL | LONG | LONG_CHAR_VARYING | LONG_VARCHAR | LONGBLOB | LONGTEXT | MAP
    | MATERIALIZED | MEDIUMBLOB | MEDIUMINT | MEDIUMTEXT | MICROSECOND | MIDDLEINT | MINUTE_MICROSECOND | MINUTE_SECOND | MODE
    | MULTILINESTRING | MULTIPOINT | MULTIPOLYGON | MVNAME | NATIONAL_CHAR | NATIONAL_CHAR_VARYING | NEXT | NULLS
    | NVARCHAR | ORIGINALSQL | OUTPUT | OVERWRITE | OWNER | PARTITIONED | PARTITIONS | POINT | POLYGON | PRECEDING
    | PROPERTIES | QUARTER | QUERY | READ | REFRESH | REFRESHING | REGEXP | RENAME | REPEATABLE | REPLACE | RESET
    | RESPECT | RESTRICT | RETURNING | REVERSE | RLIKE | SCHEMA | SCHEMAS | SECOND_MICROSECOND | SEMI | SEPARATOR
    | SERDE | SERDEPROPERTIES | SERIAL | SERIALIZABLE | SESSION | SETS | SIGNED | SIGNED_INT | SIGNED_INTEGER | SORT
    | SORTED | SOUNDS | STATS | STATUS | STORED | SUBSTR | SUSPEND | SYMBOL | TABLES | TBLPROPERTIES | TERMINATED
    | TEXT | TIES | TINYBLOB | TINYINT | TINYTEXT | TRANSACTION | TRY_CAST | TYPE | UNBOUNDED | UNCOMMITTED | UNICODE
    | UNSIGNED | UNSIGNED_INT | UNSIGNED_INTEGER | URI | VALIDATE | VERBOSE | VERIFY | VIEW | VIEWS | VIRTUAL
    | WEEK | WEIGHT_STRING | WORK | WRITE | XOR | YEAR_MONTH | ZEROFILL | ZONE
    ;

catalogName
    : name
    ;

schemaName
    : (owner DOT_)? name
    ;

charsetName
    : identifier | BINARY | DEFAULT
    ;


tableName
    : (owner DOT_)* name
    ;

columnName
    : (owner DOT_)* name
    ;

userIdentifierOrText
    : identifier (AT_ identifier)?
    ;

username
    : userIdentifierOrText | CURRENT_USER (LP_ RP_)?
    ;

functionName
    : (owner DOT_)* name
    ;

procedureName
    : (owner DOT_)* name
    ;

viewName
    : (owner DOT_)* name
    ;

owner
    : identifier
    ;

alias
    : identifier
    ;

name
    : identifier
    ;

columnNames
    : LP_ columnName (COMMA_ columnName)* RP_
    ;


expr
    : booleanPrimary
    | expr andOperator expr
    | expr orOperator expr
    | expr XOR expr
    | notOperator expr
    | LP_ expr RP_
    ;

andOperator
    : AND | AND_
    ;

orOperator
    : OR | OR_
    ;

notOperator
    : NOT | NOT_
    ;

booleanPrimary
    : booleanPrimary IS NOT? (TRUE | FALSE | UNKNOWN | NULL)
    | booleanPrimary SAFE_EQ_ predicate
    | booleanPrimary IS NOT? DISTINCT FROM predicate
    | booleanPrimary MEMBER OF LP_ (expr) RP_
    | booleanPrimary comparisonOperator predicate
    | booleanPrimary comparisonOperator (ALL | ANY | SOME) subquery
    | booleanPrimary assignmentOperator predicate
    | predicate
    ;

assignmentOperator
    : EQ_ | ASSIGNMENT_
    ;

comparisonOperator
    : EQ_ | GTE_ | GT_ | LTE_ | LT_ | NEQ_
    ;

predicate
    : bitExpr NOT? IN subquery
    | bitExpr NOT? IN LP_ expr (COMMA_ expr)* RP_
    | bitExpr NOT? BETWEEN bitExpr AND predicate
    | bitExpr SOUNDS LIKE bitExpr
    | bitExpr NOT? LIKE simpleExpr (ESCAPE simpleExpr)?
    | bitExpr NOT? (REGEXP | RLIKE) bitExpr
    | bitExpr
    ;

bitExpr
    : bitExpr VERTICAL_BAR_ bitExpr
    | bitExpr AMPERSAND_ bitExpr
    | bitExpr SIGNED_LEFT_SHIFT_ bitExpr
    | bitExpr SIGNED_RIGHT_SHIFT_ bitExpr
    | bitExpr PLUS_ bitExpr
    | bitExpr MINUS_ bitExpr
    | bitExpr ASTERISK_ bitExpr
    | bitExpr SLASH_ bitExpr
    | bitExpr DIV bitExpr
    | bitExpr MOD bitExpr
    | bitExpr MOD_ bitExpr
    | bitExpr CARET_ bitExpr
    | bitExpr PLUS_ intervalExpression
    | bitExpr MINUS_ intervalExpression
    | LP_ bitExpr RP_
    | simpleExpr
    ;

simpleExpr
    : functionCall
    | parameterMarker
    | dataType? literals
    | columnName
    | simpleExpr collateClause
    | simpleExpr OR_ simpleExpr
    | (PLUS_ | MINUS_ | TILDE_ | notOperator | BINARY) simpleExpr
    | EXISTS? subquery
    | LBE_ identifier expr RBE_
    | identifier LAMBDA_ expr
    | LP_ identifier (COMMA_ identifier)* RP_ LAMBDA_ expr
    | path (RETURNING dataType)? onEmptyError?
    | matchExpression
    | caseExpression
    | intervalExpression
    ;

path
    : string_
    ;

onEmptyError
    : (NULL | ERROR | DEFAULT literals) ON (EMPTY | ERROR)
    ;

columnRef
    : identifier (DOT_ identifier)? (DOT_ identifier)?
    ;

columnRefList
    : columnRef (COMMA_ columnRef)*
    ;

functionCall
    : specialFunction | regularFunction  | udfFunction | aggregationFunction
    ;

udfFunction
    : functionName LP_ (expr? | expr (COMMA_ expr)*) RP_
    ;

aggregationFunction
    : aggregationFunctionName LP_ DISTINCT? (expr (COMMA_ expr)* | ASTERISK_)? orderByClause? collateClause? RP_ overClause? (FILTER LP_ whereClause RP_)?
    | listaggFunction
    ;

listaggFunction
    : LISTAGG LP_ expr (COMMA_ string_)? (ON OVERFLOW (ERROR | TRUNCATE string_ WITH COUNT))? RP_ withinGroup?
    ;

withinGroup
    : WITHIN GROUP LP_ orderByClause RP_
    ;

aggregationFunctionName
    : MAX | MIN | SUM | COUNT | AVG | identifier
    ;

overClause
    : OVER (windowSpecification | identifier)
    ;

windowSpecification
    : LP_ identifier? (PARTITION BY expr (COMMA_ expr)*)? orderByClause? frameClause? RP_
    ;

frameClause
    : (ROWS | RANGE) (frameStart | frameBetween)
    ;

frameStart
    : CURRENT ROW | UNBOUNDED PRECEDING | UNBOUNDED FOLLOWING | expr PRECEDING | expr FOLLOWING
    ;

frameEnd
    : frameStart
    ;

frameBetween
    : BETWEEN frameStart AND frameEnd
    ;

specialFunction
    : groupConcatFunction | windowFunction | castFunction | convertFunction | positionFunction | substringFunction | extractFunction
    | charFunction | trimFunction | weightStringFunction | valuesFunction | currentUserFunction | indexFunction | timezoneFunction
    | mapFunction | rowFunciton | arrayFunction
    ;

rowFunciton
    : ROW LP_ expr (COMMA_ expr)* RP_
    | ROW LP_ fieldDefinition (COMMA_ fieldDefinition)* RP_
    ;

mapFunction
    : MAP LP_ arrayFunction COMMA_ arrayFunction RP_ (LBT_ literals RBT_)?
    ;

arrayFunction
    : ARRAY LBT_ (expr (COMMA_ expr)*)? RBT_
    ;

timezoneFunction
    : TIMEZONE_HOUR LP_ TIMESTAMP string_ AT TIME ZONE string_ RP_
    | TIMEZONE_MINUTE LP_ TIMESTAMP string_ AT TIME ZONE string_ RP_
    ;

indexFunction
    : INDEX LP_ string_ ( COMMA_ string_)* RP_
    ;

currentUserFunction
    : CURRENT_USER (LP_ RP_)?
    ;

groupConcatFunction
    : GROUP_CONCAT LP_ DISTINCT? (expr (COMMA_ expr)* | ASTERISK_)? (orderByClause)? (SEPARATOR expr)? RP_
    ;

windowFunction
    : funcName = (ROW_NUMBER | RANK | DENSE_RANK | CUME_DIST | PERCENT_RANK) LP_ RP_ windowingClause
    | funcName = NTILE LP_ NUMBER_ RP_ windowingClause
    | funcName = (LEAD | LAG) LP_ expr leadLagInfo? RP_ nullTreatment? windowingClause
    | funcName = (FIRST_VALUE | LAST_VALUE) LP_ expr RP_ nullTreatment? windowingClause
    | funcName = NTH_VALUE LP_ expr COMMA_ simpleExpr RP_ (FROM (FIRST | LAST))? nullTreatment? windowingClause
    ;

windowingClause
    : OVER (windowName=identifier | windowSpecification)
    ;

leadLagInfo
    : COMMA_ (NUMBER_ | QUESTION_) (COMMA_ expr)?
    ;

nullTreatment
    : (RESPECT | IGNORE) NULLS
    ;

castFunction
    : (CAST | TRY_CAST) LP_ expr AS castType ARRAY? RP_
    | (CAST | TRY_CAST) LP_ expr AS expr ARRAY? RP_
    | (CAST | TRY_CAST) LP_ expr AT TIME ZONE expr AS DATETIME typeDatetimePrecision? RP_
    ;

convertFunction
    : CONVERT LP_ expr COMMA_ castType RP_
    | CONVERT LP_ expr USING charsetName RP_
    ;

castType
    : castTypeName = BINARY fieldLength?
    | castTypeName = CHAR fieldLength? charsetWithOptBinary?
    | castTypeName = VARCHAR
    | (castTypeName = NCHAR | castTypeName = NATIONAL_CHAR) fieldLength?
    | castTypeName = (SIGNED | SIGNED_INT | SIGNED_INTEGER)
    | castTypeName = (UNSIGNED | UNSIGNED_INT | UNSIGNED_INTEGER)
    | castTypeName = DATE
    | castTypeName = TIME typeDatetimePrecision? (WITH TIME ZONE)?
    | castTypeName = TIMESTAMP (WITH TIME ZONE)?
    | castTypeName = DATETIME typeDatetimePrecision?
    | castTypeName = DECIMAL (fieldLength | precision)?
    | castTypeName = JSON
    | castTypeName = REAL
    | castTypeName = DOUBLE PRECISION
    | castTypeName = FLOAT precision?
    | castTypeName = BOOLEAN
    | castTypeName = DOUBLE
    | castTypeName = BIGINT
    | castTypeName = INTEGER
    | castTypeName = INT
    ;

positionFunction
    : POSITION LP_ expr IN expr RP_
    ;

substringFunction
    : (SUBSTRING | SUBSTR) LP_ expr FROM NUMBER_ (FOR NUMBER_)? RP_
    | (SUBSTRING | SUBSTR) LP_ expr COMMA_ NUMBER_ (COMMA_ NUMBER_)? RP_
    ;

extractFunction
    : EXTRACT LP_ identifier FROM expr RP_
    ;

charFunction
    : CHAR LP_ expr (COMMA_ expr)* (USING charsetName)? RP_
    ;

trimFunction
    : TRIM LP_ ((LEADING | BOTH | TRAILING) expr? FROM)? expr RP_
    | TRIM LP_ (expr FROM)? expr RP_
    ;

valuesFunction
    : VALUES LP_ expr (COMMA_ expr)* RP_
    ;

weightStringFunction
    : WEIGHT_STRING LP_ expr (AS dataType)? levelClause? RP_
    ;

levelClause
    : LEVEL (levelInWeightListElement (COMMA_ levelInWeightListElement)* | NUMBER_ MINUS_ NUMBER_)
    ;

levelInWeightListElement
    : NUMBER_ direction? REVERSE?
    ;

regularFunction
    : completeRegularFunction
    | shorthandRegularFunction
    ;

shorthandRegularFunction
    : CURRENT_DATE | CURRENT_TIME (LP_ NUMBER_? RP_)? | CURRENT_TIMESTAMP | LAST_DAY | LOCALTIME | LOCALTIMESTAMP
    ;

completeRegularFunction
    : regularFunctionName (LP_ DISTINCT? (expr (COMMA_ expr)* | ASTERISK_)? RP_)
    ;

regularFunctionName
    : IF
    ;

matchExpression
    : MATCH (columnRefList | LP_ columnRefList RP_ ) AGAINST LP_ expr matchSearchModifier? RP_
    ;

matchSearchModifier
    : IN NATURAL LANGUAGE MODE | IN NATURAL LANGUAGE MODE WITH QUERY EXPANSION | IN BOOLEAN MODE | WITH QUERY EXPANSION
    ;

caseExpression
    : CASE simpleExpr? caseWhen+ caseElse? END
    ;

caseWhen
    : WHEN expr THEN expr
    ;

caseElse
    : ELSE expr
    ;

intervalExpression
    : INTERVAL intervalValue
    ;

intervalValue
    : expr intervalUnit
    ;

intervalUnit
    : MICROSECOND | SECOND | MINUTE | HOUR | DAY | WEEK | MONTH | DAY TO SECOND | YEAR TO MONTH
    | QUARTER | YEAR | SECOND_MICROSECOND | MINUTE_MICROSECOND | MINUTE_SECOND | HOUR_MICROSECOND | HOUR_SECOND
    | HOUR_MINUTE | DAY_MICROSECOND | DAY_SECOND | DAY_MINUTE | DAY_HOUR | YEAR_MONTH
    ;

subquery
    : LP_ (select | values) RP_
    ;

fieldDefinition
    : identifier dataType?
    ;

dataType
    : dataTypeName = (INTEGER | INT | TINYINT | SMALLINT | MIDDLEINT | MEDIUMINT | BIGINT) fieldLength? fieldOptions?
    | (dataTypeName = REAL | dataTypeName = DOUBLE PRECISION?) precision? fieldOptions?
    | dataTypeName = (FLOAT | DECIMAL | NUMERIC | FIXED) (fieldLength | precision)? fieldOptions?
    | dataTypeName = BIT fieldLength?
    | dataTypeName = (BOOL | BOOLEAN)
    | dataTypeName = CHAR fieldLength? charsetWithOptBinary?
    | (dataTypeName = NCHAR | dataTypeName = NATIONAL_CHAR) fieldLength? BINARY?
    | dataTypeName = (SIGNED | SIGNED_INT | SIGNED_INTEGER)
    | dataTypeName = BINARY fieldLength?
    | dataTypeName = STRING
    | (dataTypeName = CHAR_VARYING | dataTypeName = CHARACTER_VARYING | dataTypeName = VARCHAR) fieldLength? charsetWithOptBinary?
    | (dataTypeName = NATIONAL VARCHAR | dataTypeName = NVARCHAR | dataTypeName = NCHAR VARCHAR | dataTypeName = NATIONAL_CHAR_VARYING | dataTypeName = NCHAR VARYING) fieldLength BINARY?
    | dataTypeName = VARBINARY fieldLength?
    | dataTypeName = YEAR fieldLength? fieldOptions?
    | dataTypeName = DATE
    | dataTypeName = TIME typeDatetimePrecision?
    | dataTypeName = (UNSIGNED | UNSIGNED_INT | UNSIGNED_INTEGER)
    | dataTypeName = TIMESTAMP typeDatetimePrecision?
    | dataTypeName = DATETIME typeDatetimePrecision?
    | dataTypeName = TINYBLOB
    | dataTypeName = BLOB fieldLength?
    | dataTypeName = (MEDIUMBLOB | LONGBLOB)
    | dataTypeName = LONG VARBINARY
    | dataTypeName = (LONG_CHAR_VARYING | LONG_VARCHAR) charsetWithOptBinary?
    | dataTypeName = TINYTEXT charsetWithOptBinary?
    | dataTypeName = TEXT fieldLength? charsetWithOptBinary?
    | dataTypeName = MEDIUMTEXT charsetWithOptBinary?
    | dataTypeName = LONGTEXT charsetWithOptBinary?
    | dataTypeName = ENUM stringList charsetWithOptBinary?
    | dataTypeName = SET stringList charsetWithOptBinary?
    | dataTypeName = ARRAY '<' dataType '>'
    | dataTypeName = MAP '<' dataType COMMA_ dataType '>'
    | dataTypeName = STRUCT '<' identifier ':' dataType COMMA_ identifier ':' dataType '>'
    | dataTypeName = ROW LP_ fieldDefinition (COMMA_ fieldDefinition)* RP_
    | dataTypeName = (IPADDRESS | QDIGEST | UUID | SERIAL | JSON | GEOMETRY | GEOMCOLLECTION | GEOMETRYCOLLECTION
        | POINT | MULTIPOINT | LINESTRING | MULTILINESTRING | POLYGON | MULTIPOLYGON)
    ;

stringList
    : LP_ textString (COMMA_ textString)* RP_
    ;

textString
    : string_
    | HEX_DIGIT_
    | BIT_NUM_
    ;


fieldOptions
    : (UNSIGNED | SIGNED | ZEROFILL)+
    ;

precision
    : LP_ NUMBER_ COMMA_ NUMBER_ RP_
    ;

typeDatetimePrecision
    : LP_ NUMBER_ RP_
    ;

charsetWithOptBinary
    : ascii
    | unicode
    | BYTE
    | charset charsetName BINARY?
    | BINARY (charset charsetName)?
    ;

ascii
    : ASCII BINARY?
    | BINARY ASCII
    ;

unicode
    : UNICODE BINARY?
    | BINARY UNICODE
    ;

charset
    : (CHAR | CHARACTER) SET
    | CHARSET
    ;


fieldLength
    : LP_ length=NUMBER_ RP_
    ;


collateClause
    : COLLATE (collationName | parameterMarker)
    ;


ifNotExists
    : IF NOT EXISTS
    ;

ifExists
    : IF EXISTS
    ;

