lexer grammar HetuLexer;

options {
    caseInsensitive = true;
}
WS: [ \t\r\n]+ -> skip;

ADD                   : 'ADD';
AFTER                 : 'AFTER';
AGAINST               : 'AGAINST';
ALL                   : 'ALL';
ALTER                 : 'ALTER';
ANALYZE               : 'ANALYZE';
AND                   : 'AND';
ANTI                  : 'ANTI';
ANY                   : 'ANY';
ARRAY                 : 'ARRAY';
AS                    : 'AS';
ASC                   : 'ASC';
ASCII                 : 'ASCII';
AT                    : 'AT';
AVG                   : 'AVG';
BETWEEN               : 'BETWEEN';
BIGINT                : 'BIGINT';
BINARY                : 'BINARY';
BIT                   : 'BIT';
BLOB                  : 'BLOB';
BOOL                  : 'BOOL';
BOOLEAN               : 'BOOLEAN';
BOTH                  : 'BOTH';
BUCKETS               : 'BUCKETS';
BY                    : 'BY';
BYTE                  : 'BYTE';
CALL                  : 'CALL';
CALLED                : 'CALLED';
CASCADE               : 'CASCADE';
CASE                  : 'CASE';
CAST                  : 'CAST';
CATALOG               : 'CATALOG';
CATALOGS              : 'CATALOGS';
CHANGE                : 'CHANGE';
CHAR                  : 'CHAR';
CHAR_VARYING          : CHAR WS+ VARYING;
CHARACTER             : 'CHARACTER';
CHARACTER_VARYING     : CHARACTER WS+ VARYING;
CHARSET               : 'CHARSET';
CLUSTERED             : 'CLUSTERED';
COLLATE               : 'COLLATE';
COLLECTION            : 'COLLECTION';
COLUMN                : 'COLUMN';
COLUMNS               : 'COLUMNS';
COMMENT               : 'COMMENT';
COMMIT                : 'COMMIT';
COMMITTED             : 'COMMITTED';
CONVERT               : 'CONVERT';
COUNT                 : 'COUNT';
CREATE                : 'CREATE';
CROSS                 : 'CROSS';
CUBE                  : 'CUBE';
CUME_DIST             : 'CUME_DIST';
CURRENT               : 'CURRENT';
CURRENT_DATE          : 'CURRENT_DATE';
CURRENT_TIME          : 'CURRENT_TIME';
CURRENT_TIMESTAMP     : 'CURRENT_TIMESTAMP';
CURRENT_USER          : 'CURRENT_USER';
DATA                  : 'DATA';
DATABASE              : 'DATABASE';
DATABASES             : 'DATABASES';
DATE                  : 'DATE';
DATETIME              : 'DATETIME';
DAY                   : 'DAY';
DAY_HOUR              : 'DAY_HOUR';
DAY_MICROSECOND       : 'DAY_MICROSECOND';
DAY_MINUTE            : 'DAY_MINUTE';
DAY_SECOND            : 'DAY_SECOND';
DBPROPERTIES          : 'DBPROPERTIES';
DEALLOCATE            : 'DEALLOCATE';
DECIMAL               : 'DECIMAL';
DEFAULT               : 'DEFAULT';
DEFINED               : 'DEFINED';
DELETE                : 'DELETE';
DELIMITED             : 'DELIMITED';
DENSE_RANK            : 'DENSE_RANK';
DESC                  : 'DESC';
DESCRIBE              : 'DESCRIBE';
DETERMINISTIC         : 'DETERMINISTIC';
DISABLE               : 'DISABLE';
DISTINCT              : 'DISTINCT';
DISTRIBUTED           : 'DISTRIBUTED';
DIV                   : 'DIV';
DOUBLE                : 'DOUBLE';
DROP                  : 'DROP';
ELSE                  : 'ELSE';
EMPTY                 : 'EMPTY';
ENABLE                : 'ENABLE';
END                   : 'END';
ENUM                  : 'ENUM';
ERROR                 : 'ERROR';
ESCAPE                : 'ESCAPE';
ESCAPED               : 'ESCAPED';
EXCEPT                : 'EXCEPT';
EXCHANGE              : 'EXCHANGE';
EXCLUDING             : 'EXCLUDING';
EXECUTE               : 'EXECUTE';
EXISTS                : 'EXISTS';
EXPANSION             : 'EXPANSION';
EXPLAIN               : 'EXPLAIN';
EXTENDED              : 'EXTENDED';
EXTERNAL              : 'EXTERNAL';
EXTRACT               : 'EXTRACT';
FALSE                 : 'FALSE';
FETCH                 : 'FETCH';
FIELDS                : 'FIELDS';
FILEFORMAT            : 'FILEFORMAT';
FIRST                 : 'FIRST';
FIRST_VALUE           : 'FIRST_VALUE';
FIXED                 : 'FIXED';
FLOAT                 : 'FLOAT';
FOLLOWING             : 'FOLLOWING';
FOR                   : 'FOR';
FORMAT                : 'FORMAT';
FORMATTED             : 'FORMATTED';
FROM                  : 'FROM';
FULL                  : 'FULL';
FUNCTION              : 'FUNCTION';
FUNCTIONS             : 'FUNCTIONS';
GEOMCOLLECTION        : 'GEOMCOLLECTION';
GEOMETRY              : 'GEOMETRY';
GEOMETRYCOLLECTION    : 'GEOMETRYCOLLECTION';
GRAPHVIZ              : 'GRAPHVIZ';
GROUP                 : 'GROUP';
GROUP_CONCAT          : 'GROUP_CONCAT';
GROUPING              : 'GROUPING';
HAVING                : 'HAVING';
HOUR                  : 'HOUR';
HOUR_MICROSECOND      : 'HOUR_MICROSECOND';
HOUR_MINUTE           : 'HOUR_MINUTE';
HOUR_SECOND           : 'HOUR_SECOND';
IF                    : 'IF';
IGNORE                : 'IGNORE';
IN                    : 'IN';
INCLUDING             : 'INCLUDING';
INDEX                 : 'INDEX';
INIT                  : 'INIT';
INNER                 : 'INNER';
INPATH                : 'INPATH';
INPUT                 : 'INPUT';
INSERT                : 'INSERT';
INT                   : 'INT';
INTEGER               : 'INTEGER';
INTERSECT             : 'INTERSECT';
INTERVAL              : 'INTERVAL';
INTO                  : 'INTO';
IO                    : 'IO';
IS                    : 'IS';
ISOLATION             : 'ISOLATION';
ITEMS                 : 'ITEMS';
JAVA                  : 'JAVA';
JOIN                  : 'JOIN';
JSON                  : 'JSON';
KEYS                  : 'KEYS';
LAG                   : 'LAG';
LANGUAGE              : 'LANGUAGE';
LAST                  : 'LAST';
LAST_DAY              : 'LAST_DAY';
LAST_VALUE            : 'LAST_VALUE';
LEAD                  : 'LEAD';
LEADING               : 'LEADING';
LEFT                  : 'LEFT';
LEVEL                 : 'LEVEL';
LIKE                  : 'LIKE';
LIMIT                 : 'LIMIT';
LINES                 : 'LINES';
LINESTRING            : 'LINESTRING';
LOAD                  : 'LOAD';
LOCALTIME             : 'LOCALTIME';
LOCALTIMESTAMP        : 'LOCALTIMESTAMP';
LOCATION              : 'LOCATION';
LOGICAL               : 'LOGICAL';
LONG                  : 'LONG';
LONG_CHAR_VARYING     : LONG CHAR VARYING;
LONG_VARCHAR          : LONG  VARCHAR;
LONGBLOB              : 'LONGBLOB';
LONGTEXT              : 'LONGTEXT';
MAP                   : 'MAP';
MATCH                 : 'MATCH';
MATERIALIZED          : 'MATERIALIZED';
MAX                   : 'MAX';
MEDIUMBLOB            : 'MEDIUMBLOB';
MEDIUMINT             : 'MEDIUMINT';
MEDIUMTEXT            : 'MEDIUMTEXT';
MEMBER                : 'MEMBER';
MICROSECOND           : 'MICROSECOND';
MIDDLEINT             : 'MIDDLEINT';
MIN                   : 'MIN';
MINUTE                : 'MINUTE';
MINUTE_MICROSECOND    : 'MINUTE_MICROSECOND';
MINUTE_SECOND         : 'MINUTE_SECOND';
MOD                   : 'MOD';
MODE                  : 'MODE';
MONTH                 : 'MONTH';
MULTILINESTRING       : 'MULTILINESTRING';
MULTIPOINT            : 'MULTIPOINT';
MULTIPOLYGON          : 'MULTIPOLYGON';
MVNAME                : 'MVNAME';
NATIONAL              : 'NATIONAL';
NATIONAL_CHAR         : NATIONAL WS+ CHAR;
NATIONAL_CHAR_VARYING : NATIONAL WS+ CHAR_VARYING;
NATURAL               : 'NATURAL';
NCHAR                 : 'NCHAR';
NEXT                  : 'NEXT';
NO                    : 'NO';
NOT                   : 'NOT';
NTH_VALUE             : 'NTH_VALUE';
NTILE                 : 'NTILE';
NULL                  : 'NULL';
NULLS                 : 'NULLS';
NUMERIC               : 'NUMERIC';
NVARCHAR              : 'NVARCHAR';
OF                    : 'OF';
OFFSET                : 'OFFSET';
ON                    : 'ON';
ONLY                  : 'ONLY';
OR                    : 'OR';
ORDER                 : 'ORDER';
ORIGINALSQL           : 'ORIGINALSQL';
OUTER                 : 'OUTER';
OUTPUT                : 'OUTPUT';
OVER                  : 'OVER';
OVERWRITE             : 'OVERWRITE';
OWNER                 : 'OWNER';
PARTITION             : 'PARTITION';
PARTITIONED           : 'PARTITIONED';
PARTITIONS            : 'PARTITIONS';
PERCENT_RANK          : 'PERCENT_RANK';
POINT                 : 'POINT';
POLYGON               : 'POLYGON';
POSITION              : 'POSITION';
PRECEDING             : 'PRECEDING';
PRECISION             : 'PRECISION';
PREPARE               : 'PREPARE';
PROPERTIES            : 'PROPERTIES';
QUARTER               : 'QUARTER';
QUERY                 : 'QUERY';
RANGE                 : 'RANGE';
RANK                  : 'RANK';
READ                  : 'READ';
REAL                  : 'REAL';
RECURSIVE             : 'RECURSIVE';
REFRESH               : 'REFRESH';
REFRESHING            : 'REFRESHING';
REGEXP                : 'REGEXP';
RENAME                : 'RENAME';
REPEATABLE            : 'REPEATABLE';
REPLACE               : 'REPLACE';
RESET                 : 'RESET';
RESPECT               : 'RESPECT';
RESTRICT              : 'RESTRICT';
RETURNING             : 'RETURNING';
RETURNS               : 'RETURNS';
REVERSE               : 'REVERSE';
RIGHT                 : 'RIGHT';
RLIKE                 : 'RLIKE';
ROLLBACK              : 'ROLLBACK';
ROLLUP                : 'ROLLUP';
ROW                   : 'ROW';
ROW_NUMBER            : 'ROW_NUMBER';
ROWS                  : 'ROWS';
SCHEMA                : 'SCHEMA';
SCHEMAS               : 'SCHEMAS';
SECOND                : 'SECOND';
SECOND_MICROSECOND    : 'SECOND_MICROSECOND';
SELECT                : 'SELECT';
SEMI                  : 'SEMI';
SEPARATOR             : 'SEPARATOR';
SERDE                 : 'SERDE';
SERDEPROPERTIES       : 'SERDEPROPERTIES';
SERIAL                : 'SERIAL';
SERIALIZABLE          : 'SERIALIZABLE';
SESSION               : 'SESSION';
SET                   : 'SET';
SETS                  : 'SETS';
SHOW                  : 'SHOW';
SIGNED                : 'SIGNED';
SIGNED_INT            : SIGNED WS+ INT;
SIGNED_INTEGER        : SIGNED WS+ INTEGER;
SMALLINT              : 'SMALLINT';
SORT                  : 'SORT';
SORTED                : 'SORTED';
SOUNDS                : 'SOUNDS';
SPECIFIC              : 'SPECIFIC';
START                 : 'START';
STATS                 : 'STATS';
STATUS                : 'STATUS';
STORED                : 'STORED';
SUBSTR                : 'SUBSTR';
SUBSTRING             : 'SUBSTRING';
SUM                   : 'SUM';
SUSPEND               : 'SUSPEND';
SYMBOL                : 'SYMBOL';
TABLE                 : 'TABLE';
TABLES                : 'TABLES';
TBLPROPERTIES         : 'TBLPROPERTIES';
TERMINATED            : 'TERMINATED';
TEXT                  : 'TEXT';
THEN                  : 'THEN';
TIES                  : 'TIES';
TIME                  : 'TIME';
TIMESTAMP             : 'TIMESTAMP';
TIMEZONE_HOUR         : 'TIMEZONE_HOUR';
TIMEZONE_MINUTE       : 'TIMEZONE_MINUTE';
TINYBLOB              : 'TINYBLOB';
TINYINT               : 'TINYINT';
TINYTEXT              : 'TINYTEXT';
TO                    : 'TO';
TRAILING              : 'TRAILING';
TRANSACTION           : 'TRANSACTION';
TRIM                  : 'TRIM';
TRUE                  : 'TRUE';
TRUNCATE              : 'TRUNCATE';
TRY_CAST              : 'TRY_CAST';
TYPE                  : 'TYPE';
UNBOUNDED             : 'UNBOUNDED';
UNCOMMITTED           : 'UNCOMMITTED';
UNICODE               : 'UNICODE';
UNION                 : 'UNION';
UNKNOWN               : 'UNKNOWN';
UNSIGNED              : 'UNSIGNED';
UNSIGNED_INT          : UNSIGNED WS+ INT;
UNSIGNED_INTEGER      : UNSIGNED WS+ INTEGER;
UPDATE                : 'UPDATE';
URI                   : 'URI';
USE                   : 'USE';
USER                  : 'USER';
USING                 : 'USING';
VALIDATE              : 'VALIDATE';
VALUES                : 'VALUES';
VARBINARY             : 'VARBINARY';
VARCHAR               : 'VARCHAR';
VARYING               : 'VARYING';
VERBOSE               : 'VERBOSE';
VERIFY                : 'VERIFY';
VIEW                  : 'VIEW';
VIEWS                 : 'VIEWS';
VIRTUAL               : 'VIRTUAL';
WEEK                  : 'WEEK';
WEIGHT_STRING         : 'WEIGHT_STRING';
WHEN                  : 'WHEN';
WHERE                 : 'WHERE';
WITH                  : 'WITH';
WORK                  : 'WORK';
WRITE                 : 'WRITE';
XOR                   : 'XOR';
YEAR                  : 'YEAR';
YEAR_MONTH            : 'YEAR_MONTH';
ZEROFILL              : 'ZEROFILL';
ZONE                  : 'ZONE';
STRING: 'STRING';
TABLESAMPLE:'TABLESAMPLE';
BERNOULLI:'BERNOULLI';
SYSTEM:'SYSTEM';
LATERAL:'LATERAL';
SOME:'SOME';
FILTER:'FILTER';
WITHIN:'WITHIN';
LISTAGG:'LISTAGG';
OVERFLOW:'OVERFLOW';
IPADDRESS:'IPADDRESS';
UESCAPE:'UESCAPE';
UUID:'UUID';
QDIGEST:'QDIGEST';
STRUCT:'STRUCT';



AND_:                '&&';
OR_:                 '||';
NOT_:                '!';
TILDE_:              '~';
VERTICAL_BAR_:       '|';
AMPERSAND_:          '&';
SIGNED_LEFT_SHIFT_:  '<<';
SIGNED_RIGHT_SHIFT_: '>>';
CARET_:              '^';
MOD_:                '%';
COLON_:              ':';
PLUS_:               '+';
MINUS_:              '-';
ASTERISK_:           '*';
SLASH_:              '/';
BACKSLASH_:          '\\';
DOT_:                '.';
DOT_ASTERISK_:       '.*';
SAFE_EQ_:            '<=>';
DEQ_:                '==';
EQ_:                 '=';
NEQ_:                '<>' | '!=';
GT_:                 '>';
GTE_:                '>=';
LT_:                 '<';
LTE_:                '<=';
POUND_:              '#';
LP_:                 '(';
RP_:                 ')';
LBE_:                '{';
RBE_:                '}';
LBT_:                '[';
RBT_:                ']';
COMMA_:              ',';
DQ_:                 '"';
SQ_ :                '\'';
BQ_:                 '`';
QUESTION_:           '?';
AT_:                 '@';
SEMI_:               ';';
ASSIGNMENT_:         ':=';
LAMBDA_:      '->';
FAT_ARROW_:      '=>';



BLOCK_HINT : '/*+' .*? '*/';
INLINE_HINT: '--+' ~[\r\n]* ('\r'? '\n' | EOF);

BLOCK_COMMENT:  '/*' .*? '*/' -> channel(HIDDEN);
INLINE_COMMENT: (('-- ' | '#') ~[\r\n]* ('\r'? '\n' | EOF) | '--' ('\r'? '\n' | EOF)) -> channel(HIDDEN);


FILESIZE_LITERAL
    : INT_NUM_ ('K'|'M'|'G'|'T')
    ;

SINGLE_QUOTED_TEXT
    : SQ_ ('\\'. | '\'\'' | ~('\'' | '\\'))* SQ_
    ;

DOUBLE_QUOTED_TEXT
    : DQ_ ( '\\'. | '""' | ~('"'| '\\') )* DQ_
    ;

BQUOTA_STRING
    : BQ_ ( '\\'. | '``' | ~('`'|'\\'))* BQ_
    ;

NCHAR_TEXT
    : 'N' SINGLE_QUOTED_TEXT
    ;

UNICODE_STRING
    : 'U&' SINGLE_QUOTED_TEXT
    ;

UNDERSCORE_CHARSET
    : '_' [a-z0-9]+
    ;

NUMBER_
    : INT_NUM_
    | FLOAT_NUM_
    | DECIMAL_NUM_
    ;

INT_NUM_
    : DIGIT+
    ;

FLOAT_NUM_
    : INT_NUM_? DOT_? INT_NUM_ 'E' (PLUS_ | MINUS_)? INT_NUM_
    ;

DECIMAL_NUM_
    : INT_NUM_? DOT_ INT_NUM_
    ;

HEX_DIGIT_
    : '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_ | 'X' SQ_ + SQ_
    ;

BIT_NUM_
    : '0b' ('0' | '1')+ | 'B' SQ_ ('0' | '1')+ SQ_
    ;

IDENTIFIER_
    : [a-z_$0-9\u0080-\uFFFF]*?[a-z_$\u0080-\uFFFF]+?[a-z_$0-9\u0080-\uFFFF]*
    ;


fragment DIGIT
    : [0-9]
    ;

fragment HEX_
    : [0-9a-f]
    ;
