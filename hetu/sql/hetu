CREATE SCHEMA web;
CREATE SCHEMA test_schema_5 LOCATION '/user/hive';
CREATE SCHEMA hive.sales;
CREATE SCHEMA IF NOT EXISTS traffic;
CREATE DATABASE createtestwithlocation COMMENT 'Holds all values' LOCATION '/user/hive/warehouse/create_new' WITH dbproperties('name'='akku', 'id' ='9');

--通过describe schema|database 语句来查看刚创建的schema
describe schema createtestwithlocation;
CREATE VIRTUAL SCHEMA hive_default WITH (catalog = 'hive', schema = 'default');
DROP VIRTUAL SCHEMA hive_default;
SHOW VIRTUAL SCHEMAS;

CREATE TABLE orders (
orderkey bigint,
orderstatus varchar,
totalprice double,
orderdate date
)
WITH (format = 'ORC', location='/user',orc_compress='ZLIB',external=true, "auto.purge"=false);

DESC formatted  orders ;

CREATE TABLE student(
id string,birthday string,
grade int,
memo string)
ROW FORMAT DELIMITED FIELDS TERMINATED BY ',';

CREATE TABLE test(
id int,
name string ,
tel string)
ROW FORMAT DELIMITED FIELDS TERMINATED BY '\t'
LINES TERMINATED BY '\n'
STORED AS TEXTFILE;

CREATE TABLE IF NOT EXISTS orders (
orderkey bigint,
orderstatus varchar,
totalprice double COMMENT 'Price in cents.',
orderdate date
)
COMMENT 'A table to keep track of orders.';

insert into orders values
(202011181113,'online',9527,date '2020-11-11'),
(202011181114,'online',666,date '2020-11-11'),
(202011181115,'online',443,date '2020-11-11'),
(202011181115,'offline',2896,date '2020-11-11');

CREATE TABLE bigger_orders (
another_orderkey bigint,
LIKE orders,
another_orderdate date
);

SHOW CREATE TABLE bigger_orders ;

 CREATE TABLE hive.default.bigger_orders (
    another_orderkey bigint,
    orderkey bigint,
    orderstatus varchar,
    totalprice double,
    ordersdate date,
    another_orderdate date
 )
 WITH (
    external = false,
    format = 'ORC',
    location = 'hdfs://hacluster/user/hive/warehouse/bigger_orders',
    orc_compress = 'GZIP',
    orc_compress_size = 262144,
    orc_row_index_stride = 10000,
    orc_stripe_size = 67108864
 )

CREATE EXTERNAL TABLE hetu_test (orderkey bigint, orderstatus varchar, totalprice double, orderdate date) PARTITIONED BY(ds int) SORT BY (orderkey, orderstatus) COMMENT 'test' STORED AS ORC LOCATION '/user' TBLPROPERTIES (orc_compress = 'SNAPPY', orc_compress_size = 6710422, orc_bloom_filter_columns = 'orderstatus,totalprice');

CREATE EXTERNAL TABLE hetu_test1 (orderkey bigint, orderstatus varchar, totalprice double, orderdate date) COMMENT 'test' PARTITIONED BY(ds int) CLUSTERED BY (orderkey, orderstatus) SORTED BY (orderkey, orderstatus) INTO 16 BUCKETS STORED AS ORC LOCATION '/user'  TBLPROPERTIES (orc_compress = 'SNAPPY', orc_compress_size = 6710422, orc_bloom_filter_columns = 'orderstatus,totalprice');

CREATE TABLE hetu_test2 (orderkey bigint, orderstatus varchar, totalprice double, orderdate date, ds int) COMMENT 'This table is in Hetu syntax' WITH (partitioned_by = ARRAY['ds'], bucketed_by = ARRAY['orderkey', 'orderstatus'], sorted_by = ARRAY['orderkey', 'orderstatus'], bucket_count = 16, orc_compress = 'SNAPPY', orc_compress_size = 6710422, orc_bloom_filter_columns = ARRAY['orderstatus', 'totalprice'], external = true, format = 'orc', location = '/user');

CREATE TABLE hive.default.hetu_test1 (
    orderkey bigint,
    orderstatus varchar,
    totalprice double,
    orderdate date,
    ds integer
 )
 COMMENT 'test'
 WITH (
    bucket_count = 16,
    bucketed_by = ARRAY['orderkey','orderstatus'],
    bucketing_version = 1,
    external_location = 'hdfs://hacluster/user',
    format = 'ORC',
    orc_bloom_filter_columns = ARRAY['orderstatus','totalprice'],
    orc_bloom_filter_fpp = 5E-2,
    orc_compress = 'SNAPPY',
    orc_compress_size = 6710422,
    orc_row_index_stride = 10000,
    orc_stripe_size = 67108864,
    partitioned_by = ARRAY['ds'],
    sorted_by = ARRAY['orderkey','orderstatus']
 )

CREATE SCHEMA hive.web WITH (location = 'hdfs://hacluster/user');

CREATE TABLE hive.web.page_views (
  view_time timestamp,
  user_id bigint,
  page_url varchar,
  ds date,
  country varchar
)
WITH (
  format = 'ORC',
  partitioned_by = ARRAY['ds', 'country'],
  bucketed_by = ARRAY['user_id'],
  bucket_count = 50
);

CALL system.create_empty_partition(
    schema_name => 'web',
    table_name => 'page_views',
    partition_columns => ARRAY['ds', 'country'],
    partition_values => ARRAY['2020-07-17', 'US']);

SELECT * FROM hive.web."page_views$partitions";

insert into hive.web.page_views values(timestamp '2020-07-17 23:00:15',bigint '15141','www.local.com',date '2020-07-17','US' );


 select * from hive.web.page_views;

CREATE TABLE orders_column_aliased (order_date, total_price)
AS
SELECT orderdate, totalprice FROM orders;

CREATE TABLE orders_by_date
COMMENT 'Summary of orders by date'
WITH (format = 'ORC')
AS
SELECT orderdate, sum(totalprice) AS price
FROM orders
GROUP BY orderdate;

CREATE TABLE IF NOT EXISTS orders_by_date AS
SELECT orderdate, sum(totalprice) AS price
FROM orders
GROUP BY orderdate;

CREATE TABLE empty_orders AS
SELECT *
FROM orders
WITH NO DATA;

CREATE EXTERNAL TABLE hetu_copy(corderkey, corderstatus, ctotalprice, corderdate, cds)
 PARTITIONED BY(cds)
 SORT BY (corderkey, corderstatus)
 COMMENT 'test'
 STORED AS orc
 LOCATION '/user/hetuserver/tmp'
 TBLPROPERTIES (orc_bloom_filter_fpp = 0.3, orc_compress = 'SNAPPY', orc_compress_size = 6710422, orc_bloom_filter_columns = 'corderstatus,ctotalprice')
 as select * from hetu_test;

CREATE TABLE hetu_copy1(corderkey, corderstatus, ctotalprice, corderdate, cds)
 WITH (partitioned_by = ARRAY['cds'], bucketed_by = ARRAY['corderkey', 'corderstatus'],
 sorted_by = ARRAY['corderkey', 'corderstatus'],
 bucket_count = 16,
 orc_compress = 'SNAPPY',
 orc_compress_size = 6710422,
 orc_bloom_filter_columns = ARRAY['corderstatus', 'ctotalprice'],
 external = true,
 format = 'orc',
 location = '/user/hetuserver/tmp ')
  as select * from hetu_test;

CREATE TABLE order01(id int,name string,tel string) ROW FORMAT DELIMITED FIELDS TERMINATED BY '\t' LINES TERMINATED BY '\n'STORED AS TEXTFILE;

CREATE TABLE order02(sku int, sku_name string, sku_describe string);

CREATE TABLE orders_like01 like order01 INCLUDING PROPERTIES;

CREATE TABLE orders_like02 like order02 STORED AS TEXTFILE;

CREATE TABLE orders_like03 (c1 int,c2 float,LIKE order01 INCLUDING PROPERTIES,LIKE order02);

CREATE TABLE order_partition(id int,name string,tel string) PARTITIONED BY (sku int);

CREATE TABLE orders_like04 (like order_partition);

CREATE TABLE orders_like05 like order_partition;

DESC orders_like04;

CREATE VIEW test (oderkey comment 'orderId',orderstatus comment 'status',half comment 'half') AS
SELECT orderkey, orderstatus, totalprice / 2 AS half FROM orders;

CREATE VIEW orders_by_date AS
SELECT orderdate, sum(totalprice) AS price
FROM orders
GROUP BY orderdate;

CREATE OR REPLACE VIEW test AS
SELECT orderkey, orderstatus, totalprice / 4 AS quarter
FROM orders

create or replace view  view1 comment 'the first view' TBLPROPERTIES('format'='orc') as select * from fruit;

CREATE FUNCTION example.default.add_two (
 num integer
)
RETURNS integer
LANGUAGE JAVA
DETERMINISTIC
SYMBOL "com.example.functions.AddTwo"
URI "hdfs://hacluster/udfs/function-1.0.jar";

select hetu.default.add_two(2);

set session materialized_view_rewrite_enabled=true;

create schema mv.tpcds;

create table t1 (id int, c1 varchar);

Insert into t1 values (1,'abc'), (2,'abc2'), (3,'abc3'), (4,'abc4'), (5,'abc5'),(6, 'abc6');

create materialized view mv.tpcds.test as select c1 from t1 where id <7;

create materialized view mv.tpcds.test (a ,b) as select c1, id from t1 where id<7;

create materialized view if not exists mv.tpcds.test as select c1, id from t1 where id<7;

create materialized view mv.tpcds.test with (storage_table='mppdb.tpcds.test2',need_auto_refresh = true, mv_validity = '10m', start_refresh_ahead_of_expiry = 0.2, refresh_priority = 1, refresh_duration = '5m') as select c1, id from t1 where id<7;

create materialized view mv.tpcds.test comment 'test_comment' as select c1, id from t1 where id<7;

create materialized view mv.tpcds.test1 as select t1.a, b, d from ((select a, b, c from tb_a) as t1 join (select a, d, e from tb_b) as t2 on t1.a=t2.a);

create materialized view mv.tpcds.test1 as
with t1 as (select a, b, c from tb_a),
t2 as (select a, d, e from tb_b)select t1.a, b, d from t1 join t2 on t1.a = t2.a;

create materialized view mv.tpcds.test as select c1 from t1 where id <7;

select id,name,age from hivetb1;

alter materialized view mv.default.mv1 set status SUSPEND;

Alter materialized view mv.mvtestprop.pepa_ss set PROPERTIES refresh_priority = 2;


CREATE TABLE IF NOT EXISTS hetu_int_table5 (eid int, name String, salary String, destination String, dept String, yoj int) COMMENT 'Employee Names' partitioned by (dt timestamp,country String, year int, bonus decimal(10,3)) STORED AS TEXTFILE;

ALTER TABLE hetu_int_table5 ADD IF NOT EXISTS PARTITION (dt='2008-08-08 10:20:30.0', country='IN', year=2001, bonus=500.23) PARTITION (dt='2008-08-09 10:20:30.0', country='IN', year=2001, bonus=100.50) ;

show partitions hetu_int_table5;

ALTER TABLE hetu_int_table5 DROP IF EXISTS PARTITION (dt=timestamp '2008-08-08 10:20:30.0', country='IN', year=2001, bonus=500.23);

CREATE TABLE hetu_exchange_partition1 (a string, b string) PARTITIONED BY (ds string);

ALTER TABLE hetu_exchange_partition1 ADD PARTITION (ds='1');

ALTER TABLE part_test.hetu_exchange_partition2 EXCHANGE PARTITION (ds='1') WITH TABLE hetu_exchange_partition1;

CREATE TABLE IF NOT EXISTS hetu_rename_table ( eid int, name String, salary String, destination String, dept String, yoj int)
COMMENT 'Employee details'
partitioned by (year int)
STORED AS TEXTFILE;

ALTER TABLE hetu_rename_table ADD IF NOT EXISTS PARTITION (year=2001);

ALTER TABLE hetu_rename_table PARTITION (year=2001) rename to partition (year=2020);

create table altercolumn4(a integer, b string) partitioned by (c integer);

alter table altercolumn4 SET FILEFORMAT textfile;

insert into altercolumn4 values (100, 'Daya', 500);

alter table altercolumn4 partition (c=500) change column b empname string comment 'changed column name to empname' first;

alter table altercolumn4 partition (c=500) set Location '/user/hive/warehouse/c500';

create table altercolumn1(a integer, b integer) stored as textfile;

alter table altercolumn1 change column b name string;

ALTER TABLE altercolumn1 CLUSTERED BY(a, name) SORTED BY(name) INTO 25 BUCKETS;

describe formatted altercolumn1;

CREATE OR REPLACE VIEW tv_view as SELECT id,name from (values (1, 'HetuEngine')) as x(id,name);

ALTER VIEW tv_view as SELECT id, brand FROM (VALUES (1, 'brand_1', 100), (2, 'brand_2', 300) ) AS x (id, brand, price);

ALTER VIEW tv_view SET TBLPROPERTIES ('comment' = 'This is a new comment');

show tblproperties tv_view;

ALTER SCHEMA foo SET LOCATION 'hdfs://hacluster/newlocation';

ALTER SCHEMA foo SET OWNER user admin;

DROP SCHEMA IF EXISTS sales;

USE test_drop;

CREATE TABLE tb_web(col1 int);

DROP DATABASE test_drop CASCADE;

drop table if exists testfordrop;

DROP VIEW IF EXISTS orders_by_date;

DROP FUNCTION example.namespace01.date_diff

drop materialized view mv.tpcds.t1;

refresh materialized view mv.tpcds.test;

Truncate table simple;

Truncate table tb_truncate_part partition (state = 'ap', age = 10);

COMMENT ON TABLE users IS 'master table';

VALUES 1, 2, 3

VALUES
(1, 'a'),
(2, 'b'),
(3, 'c')

SELECT * FROM (values (1, 'a'), (2, 'b'),(3, 'c')) AS t (id, name);

CREATE TABLE example AS
 SELECT * FROM (VALUES (1, 'a'), (2, 'b'), (3, 'c')) AS t (id, name);

SHOW CATALOGS LIKE 'sys%';

SHOW SCHEMAS;

SHOW SCHEMAS FROM hive LIKE 't%';

SHOW SCHEMAS IN hive LIKE 'pm/_%' ESCAPE '/';

show tables in  default like 'show_table_';

show tables in default like 'show$_%' ESCAPE '$';

SHOW TBLPROPERTIES show_table1('orc.compression.codec');

show table extended  like 'show*';

 show table extended  like 'from*|show*';
SHOW STATS FOR orders;
SHOW STATS FOR (SELECT * FROM orders);
SHOW functions;
show functions like 'boo_%';
SHOW external function example.namespace02.repeat;
SHOW external functions;
show session;
SHOW PARTITIONS test PARTITION(hr = '12', ds = 12);
SHOW PARTITIONS test PARTITION(ds > 12);
SHOW COLUMNS FROM fruit;
SHOW COLUMNS IN fruit;

CREATE TABLE orders (
 orderkey bigint,
 orderstatus varchar,
 totalprice double,
 orderdate date
 )
 WITH (format = 'ORC', location='/user',orc_compress='ZLIB',external=true, "auto.purge"=false);

show create table orders;
Show views;
show views like 't*';
SHOW CREATE VIEW test_view;
SHOW MATERIALIZED VIEWS;
SHOW MATERIALIZED VIEWS FROM auto_created_mv;
SHOW MATERIALIZED VIEWS WITH TABLES LIKE '_ive.tpcds_bin_partitioned_orc_2.call_center';
SHOW MATERIALIZED VIEWS TABLES LIKE '*.call_center';
SHOW MATERIALIZED VIEWS WITH TABLES LIKE '*.call_center|*.date_dim';
show create materialized view mv.tpcds.test;
INSERT INTO test_row values row(row(1, 'test'));
CREATE TABLE test_multy_value(id int, col row(c1 int, c2 string));
INSERT INTO test_multy_value values (1,row(1,'test'));
insert into fruit values('LIchee',32);
insert into table fruit values('Cherry',88);
insert into fruit values('banana',10),('peach',6),('lemon',12),('apple',7);
insert into fruit_copy select * from fruit;
insert overwrite fruit_copy select *  from fruit limit 2;
insert into varchar50 values('hetuEngine');
insert into varchar100 select * from varchar50;
create table test_part (id int, alias varchar) partitioned by (dept_id int, status varchar);
insert into test_part  partition(dept_id=10, status='good') values (1, 'xyz'), (2, 'abc');
select * from test_part order by id;
insert overwrite test_part (id, alias, dept_id, status) values (3, 'uvw', 25, 'overwrite');
insert overwrite test_part (id, alias, dept_id, status) values (4, 'new', 10, 'good');
insert into test_p_1 partition (provice = 'hebei', city= 'baoding') values ('xiaobei',15),( 'xiaoming',22);
from test_p_1 insert into table test_p_2 partition (provice = 'hebei', city= 'baoding') select name,age;
insert into test_p_2 partition(provice = 'hebei', city= 'baoding') select name,age from test_p_1;
delete from tb_del where id =1;
delete from hive.web.page_views where ds=date '2020-07-17' and country='US';
delete from tb_trans where a=1;
update upd_tb set col1=5 where col1=4;
LOAD DATA INPATH '/opt/load_test/f1.txt' into table tb_load_f1;
LOAD DATA INPATH '/opt/load_test/' into table tb_load_f2;
CREATE TABLE tb_load_f2(id int) with (format='TEXTFILE');
CREATE TABLE tb_load_f3(id int,name varchar) with(format='TEXTFILE',textfile_field_separator='-');
Load data inpath '/opt/load_test/f3.txt' into table tb_load_f3;
START TRANSACTION;
START TRANSACTION ISOLATION LEVEL REPEATABLE READ;
START TRANSACTION READ WRITE;
START TRANSACTION ISOLATION LEVEL READ COMMITTED, READ ONLY;
START TRANSACTION READ WRITE, ISOLATION LEVEL SERIALIZABLE;
COMMIT;
COMMIT WORK;
ROLLBACK;
ROLLBACK WORK;

SELECT name, maxprice FROM (SELECT name, MAX(price) AS maxprice FROM fruit GROUP BY name) AS x;

WITH x AS (SELECT name, MAX(price) AS maxprice FROM fruit GROUP BY name) SELECT name, maxprice FROM x;

with
t1 as(select name,max(price) as maxprice from fruit group by name),
t2 as(select name,avg(price) as avgprice from fruit group by name)
select t1.*,t2.* from t1 join t2 on t1.name = t2.name;

WITH
x AS (SELECT a FROM t),
y AS (SELECT a AS b FROM x),
z AS (SELECT b AS c FROM y)
SELECT c FROM z;

SELECT count(*), nationkey FROM customer GROUP BY 2;
SELECT count(*), nationkey FROM customer GROUP BY nationkey;

SELECT count(*) FROM customer GROUP BY mktsegment;

create table shipping(origin_state varchar(25),origin_zip integer,destination_state varchar(25) ,destination_zip integer,package_weight integer);

insert into shipping values ('California',94131,'New Jersey',8648,13),
('California',94131,'New Jersey',8540,42),
('California',90210,'Connecticut',6927,1337),
('California',94131,'Colorado',80302,5),
('New York',10002,'New Jersey',8540,3),
('New Jersey',7081,'Connecticut',6708,225);

SELECT
	origin_state,
	origin_zip,
	destination_state,
	sum( package_weight )
FROM shipping
GROUP BY GROUPING SETS (
		( origin_state ),
	( origin_state, origin_zip ),
	( destination_state ));

SELECT origin_state, NULL,NULL,sum( package_weight ) FROM shipping GROUP BY origin_state UNION ALL  SELECT origin_state,origin_zip,NULL,sum( package_weight ) FROM shipping GROUP BY origin_state,origin_zip UNION ALL  SELECT NULL,NULL,destination_state,sum( package_weight ) FROM  shipping GROUP BY  destination_state;

SELECT
	origin_state,
	destination_state,
	sum( package_weight )
FROM
	shipping
GROUP BY
	CUBE ( origin_state, destination_state );

SELECT
origin_state,
destination_state,
sum( package_weight )
FROM
	shipping
GROUP BY
	GROUPING SETS (
		( origin_state, destination_state ),
		( origin_state ),
	( destination_state ),
	());

SELECT
	origin_state,
	origin_zip,
	sum( package_weight )
FROM
	shipping
GROUP BY
	ROLLUP ( origin_state, origin_zip );

SELECT
origin_state,
origin_zip,
sum( package_weight )
FROM
	shipping
GROUP BY
	GROUPING SETS ((origin_state,origin_zip ),( origin_state ),());

SELECT count(*), mktsegment, nationkey,
CAST(sum(acctbal) AS bigint) AS totalbal
FROM customer
GROUP BY mktsegment, nationkey
HAVING sum(acctbal) > 5700000
ORDER BY totalbal DESC;

SELECT * FROM (VALUES 13, 42) EXCEPT SELECT 13;

SELECT * FROM (VALUES 13,42) INTERSECT SELECT 13;

INSERT INTO some_table
SELECT * FROM another_table
ORDER BY field;

SELECT *
FROM some_table
JOIN (SELECT * FROM another_table ORDER BY field) u
ON some_table.key = u.key;

SELECT name FROM fruit ORDER BY name OFFSET 3;

SELECT * FROM fruit LIMIT 5;

SELECT orderdate FROM orders FETCH FIRST ROW ONLY;
SELECT * FROM (VALUES 5, 2, 4, 1, 3) t(x) ORDER BY x OFFSET 2  FETCH FIRST ROW ONLY;
insert into nation values ('ETHIOPIA',0),('MOROCCO',0),('ETHIOPIA',2),('KENYA',2),('ALGERIA',0),('MOZAMBIQUE',0);
SELECT name, regionkey FROM nation ORDER BY regionkey FETCH FIRST ROW WITH TIES;
SELECT * FROM users TABLESAMPLE BERNOULLI (50);
SELECT * FROM users TABLESAMPLE SYSTEM (75);
SELECT student, score FROM tests CROSS JOIN UNNEST(scores) AS t (score);

SELECT numbers, animals, n, a
FROM (
VALUES
(ARRAY[2, 5], ARRAY['chicken', 'cat', 'bird']),
(ARRAY[7, 8, 9], ARRAY['cow', 'fish'])
) AS x (numbers, animals)
CROSS JOIN UNNEST(numbers, animals) AS t (n, a);

SELECT * FROM nation CROSS JOIN region;
SELECT * FROM nation, region;

SELECT * FROM nation INNER JOIN region ON nation.name=region.name;
SELECT * FROM nation ,region WHERE nation.name=region.name;
SELECT * FROM nation LEFT OUTER JOIN region ON nation.name=region.name;
SELECT * FROM nation RIGHT OUTER JOIN region ON nation.name=region.name;
SELECT * FROM nation FULL OUTER JOIN region ON nation.name=region.name;
SELECT name, x, y FROM nation CROSS JOIN LATERAL (SELECT name || ' :-' AS x) CROSS JOIN LATERAL (SELECT x || ')' AS y);
select * from table1 left semi join table2 on table1.name=table2.name where table1.name='rohit' and table2.serial=3;
SELECT * FROM table1 t1 LEFT SEMI JOIN table2 t2 on t1.name=t2.name left semi join table3 t3 on t1.name = t3.name left semi join table4 t4 on t1.name=t4.name;
SELECT nation.name, region.name FROM nation CROSS JOIN region;
SELECT n.name, r.name FROM nation AS n CROSS JOIN region AS r;
SELECT n.name, r.name FROM nation n CROSS JOIN region r;
SELECT name FROM nation WHERE EXISTS (SELECT * FROM region WHERE region.regionkey = nation.regionkey)
SELECT name FROM nation WHERE regionkey IN (SELECT regionkey FROM region)
/*+ NOREWRITE */ SELECT c1,c2 FROM table1;
SET SESSION materialized_view_rewrite_enabled=true;
CREATE MATERIALIZED VIEW mv.tpcds.test7 AS SELECT a.id,b.c1 FROM (SELECT id FROM t1 WHERE id>5) as a,(SELECT id1,c1 FROM t2 WHERE id1>4) AS b WHERE a.id = b.id1;
EXPLAIN /*+ REWRITE(mv.tpcds.test6a mv.tpcds.test6b) */ SELECT a.id,b.c1 FROM (SELECT id FROM t1 WHERE id>5) AS a,(SELECT id1,c1 FROM t2 WHERE id1>4) AS b WHERE a.id = b.id1;
EXPLAIN SELECT a.id,b.c1 FROM (SELECT id FROM t1 WHERE id>5) AS a,(SELECT id1,c1 FROM t2 WHERE id1>4) AS b WHERE a.id = b.id1;
SELECT NULL BETWEEN 2 AND 4; -- null
SELECT 2 BETWEEN NULL AND 6; -- null
SELECT 3 BETWEEN 2 AND 6; -- true
SELECT 3 >= 2 AND 3 <= 6; -- true
SELECT 3 NOT BETWEEN 2 AND 6; -- false
SELECT 3 < 2 OR 3 > 6; -- false
SELECT 3.0 IS NULL; -- false
select col from dis_tab where col is distinct from null;
insert into dis_tab values (2),(3),(5),(null);
SELECT 'hello' = ANY (VALUES 'hello', 'world'); -- true
SELECT 21 < ALL (VALUES 19, 20, 21); -- false
SELECT 42 >= SOME (SELECT 41 UNION ALL SELECT 42 UNION ALL SELECT 43);-- true

select a,
case a
 when 1 then 'one'
 when 2 then 'two'
 else 'many' end from
 (values (1),(2),(3),(4)) as t(a);


select a,b,
case
when a=1 then 'one'
when b=2 then 'tow'
else 'many' end from (values (1,2),(3,4),(1,3),(4,2)) as t(a,b);

select if(a=1,8) from (values (1),(1),(2)) as t(a); -- 8 8 NULL
select if(a=1,'value') from (values (1),(1),(2)) as t(a); -- value value NULL
select if(a=1,'on','off') from (values (1),(1),(2)) as t(a);
 select coalesce(a,0) from (values (2),(3),(null)) as t(a); -- 2 3 0
select nullif(a,b) from (values (1,1),(1,2)) as t(a,b);
select zeroifnull(a),zeroifnull(b),zeroifnull(c) from (values (null,13.11,bigint '157'),(88,null,bigint '188'),(55,14.11,null)) as t(a,b,c);
SELECT CAST(origin_zip AS BIGINT) FROM shipping;
SELECT TRY(CAST(origin_zip AS BIGINT)) FROM shipping;
SELECT total_cost/packages AS per_package FROM shipping;
SELECT COALESCE(TRY(total_cost/packages),0) AS per_package FROM shipping;
SELECT numbers, transform(numbers, n -> n * n) as squared_numbers FROM (VALUES (ARRAY[1, 2]),(ARRAY[3, 4]),(ARRAY[5, 6, 7])) AS t(numbers);
SELECT transform(prices, n -> TRY_CAST(n AS VARCHAR) || '$') as price_tags FROM (VALUES (ARRAY[100, 200]),(ARRAY[30, 4])) AS t(prices);
SELECT xvalues, a, b, transform(xvalues, x -> a * x + b) as linear_function_values FROM (VALUES (ARRAY[1, 2], 10, 5), (ARRAY[3, 4], 4, 2)) AS t(xvalues, a, b);
SELECT numbers FROM (VALUES (ARRAY[1,NULL,3]), (ARRAY[10,200,30]), (ARRAY[100,20,300])) AS t(numbers) WHERE any_match(numbers, n ->  COALESCE(n, 0) > 100);
SELECT reduce_agg(value, 0, (a, b) -> a + b, (a, b) -> a + b) sum_values FROM (VALUES (1), (2), (3), (4), (5)) AS t(value);
select cast('186' as int );
select cast(186 as varchar);
SELECT typeof(cos(2)+1.5);-- double
select width_bucket(x,array [1.00,2.89,3.33,4.56,5.87,15.44,20.78,30.77]) from (values (3),(4)) as t(x);
SELECT cosine_similarity (MAP(ARRAY['a'],ARRAY[1.0]),MAP(ARRAY['a'],ARRAY[2.0]));-- 1.0
SELECT 'he'||'llo';
SELECT regexp_count('1a 2b 14m', '\s*[a-z]+\s*'); -- 3
SELECT regexp_replace('1a 2b 14m','(\d+)([ab]) ','3c$2 ');-- '3ca 3cb 14m'
select to_base64(CAST('hello world' as binary)); -- aGVsbG8gd29ybGQ=
SELECT rpad(x'15245F', 11,x'15487F'); -- 15 24 5f 15 48 7f 15 48 7f 15 48
SELECT CAST(JSON '[123, "abc",true]' AS ROW(v1 BIGINT, v2 VARCHAR, v3 BOOLEAN));-- {value1=123, value2=abc, value3=true}
select json_array_contains(json '[1,23,44]',23); -- true
select to_milliseconds(interval '8' day to second);-- 691200000
select to_iso8601(timestamp '2020-07-25 15:22:15.214');
select array_agg (name order by price) from fruit;-- [apple, peach]
select array_agg(name) filter (where price<10) from fruit;
SELECT LISTAGG(value, ',' ON OVERFLOW TRUNCATE '.....' WITH COUNT) WITHIN GROUP (ORDER BY value)FROM (VALUES 'a', 'b', 'c') t(value);
SELECT listagg(value, ',' ON OVERFLOW ERROR) WITHIN GROUP (ORDER BY value) csv_value FROM (VALUES 'a', 'b', 'c') t(value);
SELECT listagg(value, ',') WITHIN GROUP (ORDER BY value) csv_value FROM (VALUES 'a', 'c', 'b') t(value);
select dept,userid,sal,rank() over (partition by dept order by sal desc) as rnk from salary order by dept,rnk;
SELECT dept, userid, sal, CUME_DIST() OVER(ORDER BY sal) AS rn1, CUME_DIST() OVER(PARTITION BY dept ORDER BY sal) AS rn2 FROM salary;

SELECT cookieid,createtime,pv,
NTILE(2) OVER(PARTITION BY cookieid ORDER BY createtime) AS rn1,
NTILE(3) OVER(PARTITION BY cookieid ORDER BY createtime) AS rn2,
NTILE(4) OVER(ORDER BY createtime) AS rn3
FROM cookies_log
ORDER BY cookieid,createtime;

select all_match(a, x-> true) from (values array[]) t(a);
select all_match(a, x-> x>1) from (values array[NULL, NULL ,NULL]) t(a);

SELECT array_sort(ARRAY [3, 2, null, 5, null, 1, 2],
                 (x, y) -> CASE WHEN x IS NULL THEN 1
                                 WHEN y IS NULL THEN -1
                                 WHEN x < y THEN 1
                                 WHEN x = y THEN 0
                                 ELSE -1 END);

SELECT filter(ARRAY [], x -> true);
select size(map(array['num1','num2'],array[11,12]));
select url_extract_host('http://www.example.com:80/stu/index.html?name=xxx&age=25#teacher');
SELECT contains('10.0.0.0/8', IPADDRESS '**************');
USE hive.test
SET SESSION optimize_hash_generation = true;
RESET SESSION hive.optimized_reader_enabled;
DESCRIBE fruit;
DESCRIBE FORMATTED fruit;
describe formatted show_table1 a;
DESCRIBE SCHEMA web;
DESCRIBE INPUT my_select1;
DESCRIBE OUTPUT my_select1;
EXPLAIN SELECT regionkey, count(*) FROM testTable GROUP BY 1;
EXPLAIN (type DISTRIBUTED) SELECT regionkey, count(*) FROM testTable GROUP BY 1;
EXPLAIN (TYPE IO, FORMAT JSON) SELECT regionkey , count(*) FROM testTable GROUP BY 1;
EXPLAIN  ANALYZE  SELECT count(*),sum(totalprice) FROM new_orders GROUP BY orderstatus;
refresh catalog hive;
refresh schema default;
refresh table fruit;
ANALYZE hive.default.orders;
ANALYZE hive.web.page_views WITH (partitions = ARRAY[ARRAY['2020-07-17','US'], ARRAY['2020-07-18','US']]);

CALL system.create_empty_partition(
    schema_name => 'web',
    table_name => 'page_views',
    partition_columns => ARRAY['ds', 'country'],
    partition_values => ARRAY['2020-07-19', 'UK']);

PREPARE my_select1 FROM SELECT * FROM fruit;
PREPARE my_select2 FROM  SELECT name FROM fruit WHERE name= ? AND  price< ?;
DEALLOCATE PREPARE my_select1;
EXECUTE my_select1;
EXECUTE my_select2 USING  'peach',10;
verify materialized view mvname(mv.tpcds.test,mv.tpcds.t1) originalsql select c1 from t1 where id < 7;
select BOOLEAN 'TRUE';
select json '{"name": "aa", "gender": "man"}';
insert into int_type_t1  values (TINYINT'10');
insert into decimal_t1 values (DECIMAL '5.325');
CREATE TABLE decimal_t1 (dec_col1 DECIMAL(10,3)) ;
CREATE TABLE float_t1 (float_col1 FLOAT) ;
select U&'Hello winter \2603 !';
select U&'Hello winter #2603 !' UESCAPE '#';
SELECT TIMESTAMP '2015-10-18 23:00:15' + INTERVAL '3 12:15:4.111' DAY TO SECOND;
select INTERVAL '1' DAY+INTERVAL '2' HOUR +INTERVAL '3' MINUTE +INTERVAL '4' SECOND ;
SELECT TIMESTAMP '2015-10-18 23:00:15' + INTERVAL '3-1' YEAR TO MONTH;
create table array_tb(col1 ARRAY<STRING>);
create table map_tb(col1 MAP<STRING,INT>);
create table row_tb (id int,col1 row(a int,b varchar));
select  row(1,2e0),CAST(ROW(1, 2e0) AS ROW(x BIGINT, y DOUBLE));
select IPADDRESS '********', IPADDRESS '2001:db8::1';
