CREATE DATABASE IF NOT EXISTS my_database LOCATION '/user/hive/warehouse/my_database.db';
CREATE DATABASE IF NOT EXISTS test1 COMMENT 'This is a sample database' LOCATION '/user/hive/warehouse/my_database.db' MANAGEDLOCATION '/user/hive/warehouse/my_database.db/managed' WITH DBPROPERTIES ('creator'='admin', 'created_at'='2023-10-01');
drop database test1;
drop database if exists test1;
ALTER DATABASE test1 SET DBPROPERTIES ('creator'='admin', 'created_at'='2023-10-01');
ALTER DATABASE test1 SET OWNER USER admin;
ALTER DATABASE test1 SET OWNER ROLE role1;
ALTER DATABASE test1 SET LOCATION '/user/hive/warehouse/my_database.db';
ALTER DATABASE test1 SET MANAGEDLOCATION '/user/hive/warehouse/my_database.db';
use test1;

create table ttt (
    id  int,
    dtDontQuery       string,
    name              string
)
partitioned by (date string);

CREATE TABLE page_view(
viewTime INT,
userid BIGINT,
page_url STRING,
referrer_url STRING,
ip STRING COMMENT 'IP Address of the User'
)
COMMENT 'This is the page view table'
PARTITIONED BY(dt STRING, country STRING)
STORED AS SEQUENCEFILE;
CREATE TABLE page_view1(
    viewTime INT,
    userid BIGINT,
    page_url STRING,
    referrer_url STRING,
    ip STRING COMMENT 'IP Address of the User'
)
    COMMENT 'This is the page view table'
    PARTITIONED BY(dt STRING, country STRING)
    ROW FORMAT DELIMITED
        FIELDS TERMINATED BY '001'
    STORED AS SEQUENCEFILE;

CREATE EXTERNAL TABLE page_view2(
    viewTime INT, userid BIGINT,
    page_url STRING, referrer_url STRING,
    ip STRING COMMENT 'IP Address of the User',
    country STRING COMMENT 'country of origination'
)
    COMMENT 'This is the staging page view table'
    ROW FORMAT DELIMITED FIELDS TERMINATED BY '054'
    STORED AS TEXTFILE
    LOCATION '<hdfs_location>';

// todo select 未添加
CREATE TABLE new_key_value_store
    ROW FORMAT SERDE "org.apache.hadoop.hive.serde2.columnar.ColumnarSerDe"
    STORED AS RCFile
AS
SELECT (key % 1024) new_key, concat(key, value) key_value_pair
FROM key_value_store
    SORT BY new_key, key_value_pair;

CREATE TABLE empty_key_value_store1
    LIKE key_value_store TBLPROPERTIES ("property_name"="property_value");
// 分桶排序表
CREATE TABLE page_view(viewTime INT, userid BIGINT,
                       page_url STRING, referrer_url STRING,
                       ip STRING COMMENT 'IP Address of the User')
    COMMENT 'This is the page view table'
    PARTITIONED BY(dt STRING, country STRING)
    CLUSTERED BY(userid) SORTED BY(viewTime) INTO 32 BUCKETS
    ROW FORMAT DELIMITED
        FIELDS TERMINATED BY '001'
        COLLECTION ITEMS TERMINATED BY '002'
        MAP KEYS TERMINATED BY '003'
    STORED AS SEQUENCEFILE;

CREATE TABLE list_bucket_single (key STRING, value STRING)
    SKEWED BY (key) ON (1,5,6) STORED AS DIRECTORIES;

CREATE TABLE list_bucket_multiple (col1 STRING, col2 int, col3 STRING)
    SKEWED BY (col1, col2) ON (('s1',1), ('s3',3), ('s13',13), ('s78',78)) STORED AS DIRECTORIES;

CREATE TEMPORARY TABLE list_bucket_multiple (col1 STRING, col2 int, col3 STRING);

CREATE TRANSACTIONAL TABLE transactional_table_test(key string, value string) PARTITIONED BY(ds string) STORED AS ORC;
//约束
create table pk(id1 integer, id2 integer,
                primary key(id1, id2) disable novalidate);

create table constraints1(id1 integer UNIQUE disable novalidate, id2 integer NOT NULL,
                          usr string DEFAULT NULL, price double CHECK (price > 8 AND price <= 1000));

create table constraints2(id1 integer, id2 integer,
                          constraint c1_unique UNIQUE(id1) disable novalidate);
create table constraints3(id1 integer, id2 integer,
                          constraint c1_check CHECK(id1 + id2 > 0));
drop table if exists ttt;
TRUNCATE TABLE table_name PARTITION (ads="2341");
ALTER TABLE table_name SET SERDEPROPERTIES ('field.delim' = ',');

ALTER TABLE table_name NOT SKEWED;
ALTER TABLE table_name NOT STORED AS DIRECTORIES;

CREATE EXTERNAL TABLE IF NOT EXISTS default.sales_data (
    id INT COMMENT 'Unique ID for each record',
    product_name STRING COMMENT 'Name of the product',
    sales_amount DOUBLE COMMENT 'Total sales amount',
    sale_date DATE COMMENT 'Date of sale'
)
COMMENT 'Sales data table for analysis'
PARTITIONED BY (region STRING COMMENT 'Geographical region of the sale')
CLUSTERED BY (id) SORTED BY (sale_date DESC) INTO 10 BUCKETS
STORED AS PARQUET
LOCATION '/user/hive/warehouse/sales_data'
TBLPROPERTIES (
    'creator'='data_team',
    'created_date'='2025-01-13'
);
ALTER TABLE page_view ADD PARTITION (dt='2008-08-08', country='us') location '/path/to/us/part080808'
                          PARTITION (dt='2008-08-09', country='us') location '/path/to/us/part080809';

CREATE TABLE test_change (a int, b int, c int);

ALTER TABLE test_change CHANGE a a1 int;
ALTER TABLE test_change CHANGE a1 a2 STRING AFTER b;

ALTER TABLE test_change CHANGE c c1 INT FIRST ;
ALTER TABLE test_change CHANGE a1 a1 INT COMMENT 'this is column a1';

CREATE VIEW onion_referrers(url COMMENT 'URL of Referring page')
        COMMENT 'Referrers to The Onion website'
AS
SELECT DISTINCT referrer_url
FROM page_view
WHERE page_url='http://www.theonion.com';

DROP VIEW ttt.onion_referrers;

CREATE TEMPORARY MACRO fixed_number() 42;

CREATE TEMPORARY MACRO string_len_plus_two(x string) length(x) + 2;

CREATE TEMPORARY MACRO simple_add (x int, y int) x + y;

grant select on table secured_table to role my_role;

revoke update, select on table secured_table from role my_role;

show grant user ashutosh on all;

show grant user ashutosh on table hivejiratable;

show grant on table hivejiratable;

SHOW COMPACTIONS db1.tbl0 PARTITION (p=101,day='Monday') POOL 'pool0' TYPE 'minor' STATUS 'ready for clean' ORDER BY cq_table DESC, cq_state LIMIT 42;

SHOW COMPACTIONS SCHEMA db1;

SHOW COMPACTIONS DATABASE db1;

SHOW COMPACTIONS tbl0;

SHOW COMPACTIONS compactionid =1;

DESCRIBE FORMATTED default.src_table PARTITION (part_col = 100) columnA;

DESCRIBE default.src_thrift lintString.$elem$.myint;

--Create two tables, partitioned by ds
CREATE TABLE T1(a string, b string) PARTITIONED BY (ds string);
CREATE TABLE T2(a string, b string) PARTITIONED BY (ds string);
ALTER TABLE T1 ADD PARTITION (ds='1');

--Move partition from T1 to T2
ALTER TABLE T2 EXCHANGE PARTITION (ds='1') WITH TABLE T1;

--Create two tables with multiple partition columns.
CREATE TABLE T1 (a string, b string) PARTITIONED BY (ds string, hr string);
CREATE TABLE T2 (a string, b string) PARTITIONED BY (ds string, hr string);
ALTER TABLE T1 ADD PARTITION (ds = '1', hr = '00');
ALTER TABLE T1 ADD PARTITION (ds = '1', hr = '01');
ALTER TABLE T1 ADD PARTITION (ds = '1', hr = '03');

--Alter the table, moving all the three partitions data where ds='1' from table T1 to table T2 (ds=1)
ALTER TABLE T2 EXCHANGE PARTITION (ds='1') WITH TABLE T1;

-- Create two tables with multiple partition columns.
CREATE TABLE T1 (a int) PARTITIONED BY (d1 int, d2 int);
CREATE TABLE T2 (a int) PARTITIONED BY (d1 int, d2 int);
ALTER TABLE T1 ADD PARTITION (d1=1, d2=2);

-- Alter the table, moving partition data d1=1, d2=2 from table T1 to table T2
ALTER TABLE T2 EXCHANGE PARTITION (d1 = 1, d2 = 2) WITH TABLE T1;

create scheduled query t_analyze cron '0 */1 * * * ? *' as analyze table t compute statistics for columns;
       
--- Example 1 – basic example of using schedules
create table t (a integer);

-- create a scheduled query; every 10 minute insert a new row
create scheduled query sc1 cron '0 */10 * * * ? *' as insert into t values (1);
-- depending on hive.scheduled.queries.create.as.enabled the query might get create in disabled mode
-- it can be enabled using:
alter scheduled query sc1 enabled;

-- inspect scheduled queries using the information_schema
select * from information_schema.scheduled_queries s where schedule_name='sc1';
    +-----------------------+------------------+------------+----------------------+-------------------+---------+-----------+----------------------+
    | s.scheduled_query_id  | s.schedule_name  | s.enabled  | s.cluster_namespace  |    s.schedule     | s.user  |  s.query  |   s.next_execution   |
    +-----------------------+------------------+------------+----------------------+-------------------+---------+-----------+----------------------+
    | 1                     | sc1              | true       | hive                 | 0 */10 * * * ? *  | dev     | select 1  | 2020-02-03 15:10:00  |
    +-----------------------+------------------+------------+----------------------+-------------------+---------+-----------+----------------------+

-- wait 10 minutes or execute by issuing:
alter scheduled query sc1 execute;

select * from information_schema.scheduled_executions s where schedule_name='sc1' order by scheduled_execution_id desc limit 1;
+---------------------------+------------------+----------------------------------------------------+-----------+----------------------+----------------------+------------+------------------+---------------------+
| s.scheduled_execution_id  | s.schedule_name  |                s.executor_query_id                 |  s.state  |     s.start_time     |      s.end_time      | s.elapsed  | s.error_message  | s.last_update_time  |
+---------------------------+------------------+----------------------------------------------------+-----------+----------------------+----------------------+------------+------------------+---------------------+
| 496                       | sc1              | dev_20200203152025_bdf3deac-0ca6-407f-b122-c637e50f99c8 | FINISHED  | 2020-02-03 15:20:23  | 2020-02-03 15:20:31  | 8          | NULL             | NULL                |
+---------------------------+------------------+----------------------------------------------------+-----------+----------------------+----------------------+------------+------------------+---------------------+

-- Example 2 – analyze external table periodically
-- Suppose you have an external table - the contents of it is slowly changing…which will eventually lead that Hive will utilize outdated statistics during planning time

-- create external table
create external table t (a integer);

-- see where the table lives:
desc formatted t;
[...]
| Location:                     | file:/data/hive/warehouse/t                       | NULL                                               |
[...]

-- in a terminal; load some data into the table directory:
seq 1 10 > /data/hive/warehouse/t/f1

-- back in hive you will see that 
select count(1) from t;
10
-- meanwhile basic stats show that the table has "0" rows 
desc formatted t;
[...]
|                               | numRows                                            | 0                                                  |
[...]

create scheduled query t_analyze cron '0 */1 * * * ? *' as analyze table t compute statistics for columns;

-- wait some time or execute by issuing:
alter scheduled query t_analyze execute;

select * from information_schema.scheduled_executions s where schedule_name='ex_analyze' order by scheduled_execution_id desc limit 3;
+---------------------------+------------------+----------------------------------------------------+------------+----------------------+----------------------+------------+------------------+----------------------+
| s.scheduled_execution_id  | s.schedule_name  |                s.executor_query_id                 |  s.state   |     s.start_time     |      s.end_time      | s.elapsed  | s.error_message  |  s.last_update_time  |
+---------------------------+------------------+----------------------------------------------------+------------+----------------------+----------------------+------------+------------------+----------------------+
| 498                       | t_analyze       | dev_20200203152640_a59bc198-3ed3-4ef2-8f63-573607c9914e | FINISHED   | 2020-02-03 15:26:38  | 2020-02-03 15:28:01  | 83         | NULL             | NULL                 |
+---------------------------+------------------+----------------------------------------------------+------------+----------------------+----------------------+------------+------------------+----------------------+

-- and the numrows have been updated
desc formatted t;
[...]
|                               | numRows                                            | 10                                                 |
[...]

-- we don't want this running every minute anymore...
alter scheduled query t_analyze disable;      
--- Example 3 – materialized view rebuild
-- some settings...they might be there already
set hive.support.concurrency=true;
set hive.txn.manager=org.apache.hadoop.hive.ql.lockmgr.DbTxnManager;
set hive.strict.checks.cartesian.product=false;
set hive.stats.fetch.column.stats=true;
set hive.materializedview.rewriting=true;

-- create some tables
CREATE TABLE emps (
                      empid INT,
                      deptno INT,
                      name VARCHAR(256),
                      salary FLOAT,
                      hire_date TIMESTAMP)
    STORED AS ORC
TBLPROPERTIES ('transactional'='true');

CREATE TABLE depts (
                       deptno INT,
                       deptname VARCHAR(256),
                       locationid INT)
    STORED AS ORC
TBLPROPERTIES ('transactional'='true');

-- load data
insert into emps values (100, 10, 'Bill', 10000, 1000), (200, 20, 'Eric', 8000, 500),
                        (150, 10, 'Sebastian', 7000, null), (110, 10, 'Theodore', 10000, 250), (120, 10, 'Bill', 10000, 250),
                        (1330, 10, 'Bill', 10000, '2020-01-02');
insert into depts values (10, 'Sales', 10), (30, 'Marketing', null), (20, 'HR', 20);

insert into emps values (1330, 10, 'Bill', 10000, '2020-01-02');

-- create mv
CREATE MATERIALIZED VIEW mv1 AS
SELECT empid, deptname, hire_date FROM emps
                                            JOIN depts ON (emps.deptno = depts.deptno)
WHERE hire_date >= '2016-01-01 00:00:00';

EXPLAIN
SELECT empid, deptname FROM emps
                                JOIN depts ON (emps.deptno = depts.deptno)
WHERE hire_date >= '2018-01-01';

-- create a schedule to rebuild mv
create scheduled query mv_rebuild cron '0 */10 * * * ? *' defined as 
  alter materialized view mv1 rebuild;

-- from this expalin it will be seen that the mv1 is being used
EXPLAIN
SELECT empid, deptname FROM emps
                                JOIN depts ON (emps.deptno = depts.deptno)
WHERE hire_date >= '2018-01-01';

-- insert a new record
insert into emps values (1330, 10, 'Bill', 10000, '2020-01-02');

-- the source tables are scanned
EXPLAIN
SELECT empid, deptname FROM emps
                                JOIN depts ON (emps.deptno = depts.deptno)
WHERE hire_date >= '2018-01-01';

-- wait 10 minutes or execute
alter scheduled query mv_rebuild execute;

-- run it again...the view should be rebuilt
EXPLAIN
SELECT empid, deptname FROM emps
                                JOIN depts ON (emps.deptno = depts.deptno)
WHERE hire_date >= '2018-01-01';

--- Example 4 – Ingestion
drop table if exists t;
drop table if exists s;

-- suppose that this table is an external table or something
-- which supports the pushdown of filter condition on the id column
create table s(id integer, cnt integer);

-- create an internal table and an offset table
create table t(id integer, cnt integer);
create table t_offset(offset integer);
insert into t_offset values(0);

-- pretend that data is added to s
insert into s values(1,1);

-- run an ingestion...
from (select id==offset as first,* from s
join t_offset on id>=offset) s1
insert into t select id,cnt where first = false
              insert overwrite table t_offset select max(s1.id);

-- configure to run ingestion every 10 minutes
create scheduled query ingest every 10 minutes defined as
from (select id==offset as first,* from s
join t_offset on id>=offset) s1
insert into t select id,cnt where first = false
              insert overwrite table t_offset select max(s1.id);

-- add some new values
insert into s values(2,2),(3,3);

-- pretend that a timeout have happened
alter scheduled query ingest execute;

CREATE CONNECTOR pg_local TYPE 'postgres' URL 'jdbc:<postgresql://localhost:5432>' WITH DCPROPERTIES ("hive.sql.dbcp.username"="postgres", "hive.sql.dbcp.password"="postgres");
 CREATE REMOTE DATABASE pg_hive_testing USING pg_local WITH DBPROPERTIES ("connector.remoteDbName"="hive_hms_testing");

-- USING keystore instead of cleartext passwords in DCPROPERTIES
CREATE CONNECTOR pg_local_ks TYPE 'postgres' URL 'jdbc:<postgresql://localhost:5432/hive_hms_testing>' WITH DCPROPERTIES("hive.sql.dbcp.username"="postgres","hive.sql.dbcp.password.keystore"="jceks://app/local/hive/secrets.jceks","hive.sql.dbcp.password.key"="postgres.credential");
CREATE REMOTE DATABASE pg_ks_local USING pg_local_ks("connector.remoteDbName"="hive_hms_testing");


