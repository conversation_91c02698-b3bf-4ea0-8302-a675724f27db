lexer grammar HiveLexer;

options {
    caseInsensitive = true;
}


ABORT                                  : 'ABORT';
ACCOUNT                                : 'ACCOUNT';
ACTION                                 : 'ACTION';
ACTIVATE                               : 'ACTIVATE';
ACTIVE                                 : 'ACTIVE';
ADD                                    : 'ADD';
ADMIN                                  : 'ADMIN';
AFTER                                  : 'AFTER';
AGAINST                                : 'AGAINST';
AGGREGATE                              : 'AGGREGATE';
ALGORITHM                              : 'ALGORITHM';
ALL                                    : 'ALL';

ALTER                                  : 'ALTER';
ALWAYS                                 : 'ALWAYS';
ANALYZE                                : 'ANALYZE';
AND                                    : 'AND';
ANTI                                   : 'ANTI';
ANY                                    : 'ANY';
APPLICATION                            : 'APPLICATION';
ARCHIVE                                : 'ARCHIVE';
ARRAY                                  : 'ARRAY';
AS                                     : 'AS';
ASC                                    : 'ASC';
ASCII                                  : 'ASCII';
ASENSITIVE                             : 'ASENSITIVE';
AST                                    : 'AST';
AT                                     : 'AT';
ATTRIBUTE                              : 'ATTRIBUTE';
AUTHORIZATION                          : 'AUTHORIZATION';
AUTO                                   : 'AUTO';
INCREMENT                              : 'INCREMENT';
AUTOCOMMIT                             : 'AUTOCOMMIT';

AVG                                    : 'AVG';
BACKUP                                 : 'BACKUP';
BATCH                                  : 'BATCH';
BEFORE                                 : 'BEFORE';
BEGIN                                  : 'BEGIN';
BETWEEN                                : 'BETWEEN';
BIGINT                                 : 'BIGINT';
BINARY                                 : 'BINARY';
BINLOG                                 : 'BINLOG';
BIT                                    : 'BIT';
BITMAP                                 : 'BITMAP';
BLOB                                   : 'BLOB';
BLOCK                                  : 'BLOCK';
BOOL                                   : 'BOOL';
BOOLEAN                                : 'BOOLEAN';
BOTH                                   : 'BOTH';
BTREE                                  : 'BTREE';
BUCKET                                 : 'BUCKET';
BUCKETS                                : 'BUCKETS';
BY                                     : 'BY';
BYTE                                   : 'BYTE';
CACHE                                  : 'CACHE';
CALL                                   : 'CALL';
CASCADE                                : 'CASCADE';
CASCADED                               : 'CASCADED';
CASE                                   : 'CASE';
CAST                                   : 'CAST';
CATALOG                                : 'CATALOG';
CBO                                    : 'CBO';
CHAIN                                  : 'CHAIN';
CHANGE                                 : 'CHANGE';
CHANGED                                : 'CHANGED';
CHANNEL                                : 'CHANNEL';
CHAR                                   : 'CHAR';
CHARACTER                              : 'CHARACTER';

CHARSET                                : 'CHARSET';
CHECK                                  : 'CHECK';
CHECKSUM                               : 'CHECKSUM';
CIPHER                                 : 'CIPHER';
CLASS                                  : 'CLASS';
ORIGIN                                 : 'ORIGIN';
CLIENT                                 : 'CLIENT';
CLONE                                  : 'CLONE';
CLOSE                                  : 'CLOSE';
CLUSTER                                : 'CLUSTER';
CLUSTERED                              : 'CLUSTERED';
CLUSTERSTATUS                          : 'CLUSTERSTATUS';
COALESCE                               : 'COALESCE';
CODE                                   : 'CODE';
COLLATE                                : 'COLLATE';
COLLATION                              : 'COLLATION';
COLLECTION                             : 'COLLECTION';
COLUMN                                 : 'COLUMN';
COLUMNS                                : 'COLUMNS';
COMMENT                                : 'COMMENT';
COMMIT                                 : 'COMMIT';
COMMITTED                              : 'COMMITTED';
COMPACT                                : 'COMPACT';
COMPACTIONID                           : 'COMPACTIONID';
COMPACTIONS                            : 'COMPACTIONS';
COMPLETION                             : 'COMPLETION';
COMPONENT                              : 'COMPONENT';
COMPRESSED                             : 'COMPRESSED';
COMPRESSION                            : 'COMPRESSION';
COMPUTE                                : 'COMPUTE';
CONCATENATE                            : 'CONCATENATE';
CONCURRENT                             : 'CONCURRENT';
CONDITION                              : 'CONDITION';
CONF                                   : 'CONF';
CONNECTION                             : 'CONNECTION';
CONNECTOR                              : 'CONNECTOR';
CONNECTORS                             : 'CONNECTORS';
CONSISTENT                             : 'CONSISTENT';
CONSTRAINT                             : 'CONSTRAINT';
CONTAINS                               : 'CONTAINS';
CONTEXT                                : 'CONTEXT';
CONTINUE                               : 'CONTINUE';
CONVERT                                : 'CONVERT';
COPY                                   : 'COPY';
COST                                   : 'COST';
COUNT                                  : 'COUNT';
CPU                                    : 'CPU';
CREATE                                 : 'CREATE';
CRON                                   : 'CRON';
CROSS                                  : 'CROSS';
CUBE                                   : 'CUBE';
CURRENT                                : 'CURRENT';
CURSOR                                 : 'CURSOR';
DATA                                   : 'DATA';
DATABASE                               : 'DATABASE';
DATABASES                              : 'DATABASES';
DATAFILE                               : 'DATAFILE';
DATE                                   : 'DATE';
DATETIME                               : 'DATETIME';
DAY                                    : 'DAY' 'S'?;
DAYOFWEEK                              : 'DAYOFWEEK';
DBPROPERTIES                           : 'DBPROPERTIES';
DCPROPERTIES                           : 'DCPROPERTIES';
DDL                                    : 'DDL';
DEALLOCATE                             : 'DEALLOCATE';
DEBUG                                  : 'DEBUG';
DEC                                    : 'DEC';
DECIMAL                                : 'DEC' 'IMAL'?;
DECLARE                                : 'DECLARE';
DEFAULT                                : 'DEFAULT';

DEFERRED                               : 'DEFERRED';
DEFINED                                : 'DEFINED';
DEFINER                                : 'DEFINER';
DEFINITION                             : 'DEFINITION';
DELAYED                                : 'DELAYED';
DELETE                                 : 'DELETE';
DELIMITED                              : 'DELIMITED';
DELIMITER                              : 'DELIMITER';
DEPENDENCY                             : 'DEPENDENCY';
DESC                                   : 'DESC';
DESCRIBE                               : 'DESCRIBE';
DESCRIPTION                            : 'DESCRIPTION';
DETAIL                                 : 'DETAIL';
DETERMINISTIC                          : 'DETERMINISTIC';
DIAGNOSTICS                            : 'DIAGNOSTICS';
DIRECTORIES                            : 'DIRECTORIES';
DIRECTORY                              : 'DIRECTORY';
DISABLE                                : 'DISABLE' 'D'?;
DISCARD                                : 'DISCARD';
DISK                                   : 'DISK';
DISTINCT                               : 'DISTINCT';
DISTINCTROW                            : 'DISTINCTROW';
DISTRIBUTE                             : 'DISTRIBUTE';
DISTRIBUTED                            : 'DISTRIBUTED';
DIV                                    : 'DIV';
DO                                     : 'DO';
DOUBLE                                 : 'DOUBLE';
DROP                                   : 'DROP';
DUAL                                   : 'DUAL';
DUMP                                   : 'DUMP';
DUMPFILE                               : 'DUMPFILE';
DUPLICATE                              : 'DUPLICATE';
DYNAMIC                                : 'DYNAMIC';
EACH                                   : 'EACH';
ELEM_TYPE                              : '$ELEM$';
ELSE                                   : 'ELSE';
ELSEIF                                 : 'ELSEIF';
EMPTY                                  : 'EMPTY';
ENABLE                                 : 'ENABLE' 'D'?;
ENCLOSED                               : 'ENCLOSED';
ENCRYPTION                             : 'ENCRYPTION';
END                                    : 'END';
ENDS                                   : 'ENDS';
ENFORCED                               : 'ENFORCED';
ENGINE                                 : 'ENGINE';
ENGINES                                : 'ENGINES';
ENUM                                   : 'ENUM';
ERROR                                  : 'ERROR';
ERRORS                                 : 'ERRORS';
ESCAPE                                 : 'ESCAPE';
ESCAPED                                : 'ESCAPED';
EVENT                                  : 'EVENT';
EVENTS                                 : 'EVENTS';
EVERY                                  : 'EVERY';
EXCEPT                                 : 'EXCEPT';
EXCHANGE                               : 'EXCHANGE';
EXCLUDE                                : 'EXCLUDE';
EXCLUSIVE                              : 'EXCLUSIVE';
EXECUTE                                : 'EXECUTE';
EXECUTED                               : 'EXECUTED';
EXISTS                                 : 'EXISTS';
EXIT                                   : 'EXIT';
EXPANSION                              : 'EXPANSION';
EXPIRE                                 : 'EXPIRE';
EXPLAIN                                : 'EXPLAIN';
EXPORT                                 : 'EXPORT';
EXPRESSION                             : 'EXPRESSION';
EXTENDED                               : 'EXTENDED';
EXTERNAL                               : 'EXTERNAL';
EXTRACT                                : 'EXTRACT';
FALSE                                  : 'FALSE';
FAST                                   : 'FAST';
FAULTS                                 : 'FAULTS';
FETCH                                  : 'FETCH';
FIELDS                                 : 'FIELDS';
FILE                                   : 'FILE';
FILEFORMAT                             : 'FILEFORMAT';
FILTER                                 : 'FILTER';
FIRST                                  : 'FIRST';
FIXED                                  : 'FIXED';
FLOAT                                  : 'FLOAT';
FLOAT4                                 : 'FLOAT4';
FLOAT8                                 : 'FLOAT8';
FLOOR                                  : 'FLOOR';
FLUSH                                  : 'FLUSH';
FOLLOWING                              : 'FOLLOWING';
FOLLOWS                                : 'FOLLOWS';
FOR                                    : 'FOR';
FORCE                                  : 'FORCE';
FOREIGN                                : 'FOREIGN';
FORMAT                                 : 'FORMAT';
FORMATTED                              : 'FORMATTED';
FOUND                                  : 'FOUND';
FROM                                   : 'FROM';
FULL                                   : 'FULL';
FULLTEXT                               : 'FULLTEXT';
FUNCTION                               : 'FUNCTION';
FUNCTIONS                              : 'FUNCTIONS';
GENERAL                                : 'GENERAL';
GENERATE                               : 'GENERATE';
GENERATED                              : 'GENERATED';
GEOMCOLLECTION                         : 'GEOMCOLLECTION';
GEOMETRY                               : 'GEOMETRY';
GEOMETRYCOLLECTION                     : 'GEOMETRYCOLLECTION';
GET                                    : 'GET';


GLOBAL                                 : 'GLOBAL';
GRANT                                  : 'GRANT';
GRANTS                                 : 'GRANTS';
GROUP                                  : 'GROUP';
CONCAT                                  : 'CONCAT';
REPLICATION                             : 'REPLICATION';
GROUPING                               : 'GROUPING';
GROUPS                                 : 'GROUPS';
HANDLER                                : 'HANDLER';
HASH                                   : 'HASH';
HAVING                                 : 'HAVING';
HELP                                   : 'HELP';
HISTOGRAM                              : 'HISTOGRAM';
HISTORY                                : 'HISTORY';
HOST                                   : 'HOST';
HOSTS                                  : 'HOSTS';
HOUR                                   : 'HOUR' 'S'?;

IDENTIFIED                             : 'IDENTIFIED';
IDXPROPERTIES                          : 'IDXPROPERTIES';
IF                                     : 'IF';
IGNORE                                 : 'IGNORE';
IMPORT                                 : 'IMPORT';
IN                                     : 'IN';
INACTIVE                               : 'INACTIVE';
INDEX                                  : 'INDEX';
INDEXES                                : 'INDEXES';
INFILE                                 : 'INFILE';
INNER                                  : 'INNER';
INNODB                                 : 'INNODB';
INOUT                                  : 'INOUT';
INPATH                                 : 'INPATH';
INPLACE                                : 'INPLACE';
INPUTDRIVER                            : 'INPUTDRIVER';
INPUTFORMAT                            : 'INPUTFORMAT';
INSENSITIVE                            : 'INSENSITIVE';
INSERT                                 : 'INSERT';
INSTALL                                : 'INSTALL';
INSTANCE                               : 'INSTANCE';
INSTANT                                : 'INSTANT';
INT                                    : 'INT';
INTEGER                                : 'INTEGER';
INT1                                   : 'INT1';
INT2                                   : 'INT2';
INT3                                   : 'INT3';
INT4                                   : 'INT4';
INT8                                   : 'INT8';
INTERSECT                              : 'INTERSECT';
INTERVAL                               : 'INTERVAL';
INTO                                   : 'INTO';
INVISIBLE                              : 'INVISIBLE';
INVOKER                                : 'INVOKER';
IO                                     : 'IO';
IPC                                    : 'IPC';
IS                                     : 'IS';
ISOLATION                              : 'ISOLATION';
ISSUER                                 : 'ISSUER';
ITEMS                                  : 'ITEMS';
ITERATE                                : 'ITERATE';
JAR                                    : 'JAR';
JOIN                                   : 'JOIN';
JOINCOST                               : 'JOINCOST';
JSON                                   : 'JSON';
KEY                                    : 'KEY';
KEYS                                   : 'KEYS';
KILL                                   : 'KILL';
LAG                                    : 'LAG';
LANGUAGE                               : 'LANGUAGE';
LAST                                   : 'LAST';
LATERAL                                : 'LATERAL';
LEAD                                   : 'LEAD';
LEADING                                : 'LEADING';
LEAVE                                  : 'LEAVE';
LEAVES                                 : 'LEAVES';
LEFT                                   : 'LEFT';
LESS                                   : 'LESS';
LEVEL                                  : 'LEVEL';
LIKE                                   : 'LIKE';
LIMIT                                  : 'LIMIT';
LINEAR                                 : 'LINEAR';
LINES                                  : 'LINES';
LINESTRING                             : 'LINESTRING';
LIST                                   : 'LIST';
LITERAL                                : 'LITERAL';
LOAD                                   : 'LOAD';
LOCAL                                  : 'LOCAL';
LOCALTIME                              : 'LOCALTIME';
LOCALTIMESTAMP                         : 'LOCALTIMESTAMP';
LOCATION                               : 'LOCATION';
LOCK                                   : 'LOCK';
LOCKED                                 : 'LOCKED';
LOCKS                                  : 'LOCKS';
LOGFILE                                : 'LOGFILE';
LOGICAL                                : 'LOGICAL';
LOGS                                   : 'LOGS';
LONG                                   : 'LONG';
LONGBLOB                               : 'LONGBLOB';
LONGTEXT                               : 'LONGTEXT';
LOOP                                   : 'LOOP';
MACRO                                  : 'MACRO';
MAJOR                                  : 'MAJOR';
MANAGED                                : 'MANAGED';
MANAGEDLOCATION                        : 'MANAGEDLOCATION';
MANAGEMENT                             : 'MANAGEMENT';
MAP                                    : 'MAP';
MAPJOIN                                : 'MAPJOIN';
MAPPING                                : 'MAPPING';
MASTER                                 : 'MASTER';
MATCH                                  : 'MATCH';
MATCHED                                : 'MATCHED';
MATERIALIZED                           : 'MATERIALIZED';
MAX                                    : 'MAX';
MAXVALUE                               : 'MAXVALUE';
MEDIUM                                 : 'MEDIUM';
MEDIUMBLOB                             : 'MEDIUMBLOB';
MEDIUMINT                              : 'MEDIUMINT';
MEDIUMTEXT                             : 'MEDIUMTEXT';
MEMBER                                 : 'MEMBER';
MEMORY                                 : 'MEMORY';
MERGE                                  : 'MERGE';
METADATA                               : 'METADATA';
MICROSECOND                            : 'MICROSECOND';
MIDDLEINT                              : 'MIDDLEINT';
MIGRATE                                : 'MIGRATE';
MIN                                    : 'MIN';
MIN_ROWS                               : 'MIN_ROWS';
MINOR                                  : 'MINOR';
MINUS                                  : 'MINUS';
MINUTE                                 : 'MINUTE' 'S'?;
MOD                                    : 'MOD';
MODE                                   : 'MODE';
MODIFIES                               : 'MODIFIES';
MODIFY                                 : 'MODIFY';
MONTH                                  : 'MONTH' 'S'?;
MOVE                                   : 'MOVE';
MSCK                                   : 'MSCK';
MULTILINESTRING                        : 'MULTILINESTRING';
MULTIPOINT                             : 'MULTIPOINT';
MULTIPOLYGON                           : 'MULTIPOLYGON';
MULTISET                               : 'MULTISET';
MUTEX                                  : 'MUTEX';
NAME                                   : 'NAME';
NAMES                                  : 'NAMES';
NATIONAL                               : 'NATIONAL';


NATURAL                                : 'NATURAL';
NCHAR                                  : 'NCHAR';
NDB                                    : 'NDB'-> type(NDBCLUSTER)    ;
NDBCLUSTER                             : 'NDBCLUSTER';
NESTED                                 : 'NESTED';
NETWORK_NAMESPACE                      : 'NETWORK_NAMESPACE';
NEVER                                  : 'NEVER';
NEW                                    : 'NEW';
NEXT                                   : 'NEXT';
NO                                     : 'NO';
NODEGROUP                              : 'NODEGROUP';
NONE                                   : 'NONE';
NORELY                                 : 'NORELY';
NOSCAN                                 : 'NOSCAN';
NOT                                    : 'NOT';
NOVALIDATE                             : 'NOVALIDATE';
NOWAIT                                 : 'NOWAIT';
NTH_VALUE                              : 'NTH_VALUE';
NTILE                                  : 'NTILE';
NULL                                   : 'NULL';
NULLS                                  : 'NULLS';
NUMBER                                 : 'NUMBER';
NUMERIC                                : 'NUMERIC';
NVARCHAR                               : 'NVARCHAR';
OF                                     : 'OF';
OFF                                    : 'OFF';
OFFLINE                                : 'OFFLINE';
OFFSET                                 : 'OFFSET';
OJ                                     : 'OJ';
OLD                                    : 'OLD';
ON                                     : 'ON';
ONE                                    : 'ONE';
ONLY                                   : 'ONLY';
OPEN                                   : 'OPEN';
OPERATOR                               : 'OPERATOR';
OPTIMIZE                               : 'OPTIMIZE';
OPTIMIZER_COSTS                        : 'OPTIMIZER_COSTS';
OPTION                                 : 'OPTION';
OPTIONAL                               : 'OPTIONAL';
OPTIONALLY                             : 'OPTIONALLY';
OPTIONS                                : 'OPTIONS';
OR                                     : 'OR';
ORDER                                  : 'ORDER';
ORDINALITY                             : 'ORDINALITY';
ORGANIZATION                           : 'ORGANIZATION';
OTHERS                                 : 'OTHERS';
OUT                                    : 'OUT';
OUTER                                  : 'OUTER';
OUTFILE                                : 'OUTFILE';
OUTPUTDRIVER                           : 'OUTPUTDRIVER';
OUTPUTFORMAT                           : 'OUTPUTFORMAT';
OVER                                   : 'OVER';
OVERWRITE                              : 'OVERWRITE';
OWNER                                  : 'OWNER';
PACK_KEYS                              : 'PACK_KEYS';
PAGE                                   : 'PAGE';
PARSER                                 : 'PARSER';
PARTIAL                                : 'PARTIAL';
PARTITION                              : 'PARTITION';
PARTITIONED                            : 'PARTITIONED';
PARTITIONING                           : 'PARTITIONING';
PARTITIONS                             : 'PARTITIONS';
PASSWORD                               : 'PASSWORD';
PATH                                   : 'PATH';
PERCENT                                : 'PERCENT';
PERSIST                                : 'PERSIST';
PHASE                                  : 'PHASE';
PLAN                                   : 'PLAN';
PLANS                                  : 'PLANS';
PLUGIN                                 : 'PLUGIN';
PLUGINS                                : 'PLUGINS';
PLUS                                   : 'PLUS';
POINT                                  : 'POINT';
POLYGON                                : 'POLYGON';
POOL                                   : 'POOL';
PORT                                   : 'PORT';
POSITION                               : 'POSITION';
PRECEDES                               : 'PRECEDES';
PRECEDING                              : 'PRECEDING';
PRECISION                              : 'PRECISION';
PREPARE                                : 'PREPARE';
PRESERVE                               : 'PRESERVE';
PREV                                   : 'PREV';
PRIMARY                                : 'PRIMARY';
PRINCIPALS                             : 'PRINCIPALS';
PRIOR                                  : 'PRIOR';
PRIVILEGES                             : 'PRIVILEGES';
PROCEDURE                              : 'PROCEDURE';
PROCESS                                : 'PROCESS';
PROCESSLIST                            : 'PROCESSLIST';
PROFILE                                : 'PROFILE';
PROFILES                               : 'PROFILES';
PROTECTION                             : 'PROTECTION';
PROXY                                  : 'PROXY';
PURGE                                  : 'PURGE';
QUALIFY                                : 'QUALIFY';
QUARTER                                : 'QUARTER';
QUERY                                  : 'QUERY';
QUICK                                  : 'QUICK';
RANDOM                                 : 'RANDOM';
RANGE                                  : 'RANGE';
RANK                                   : 'RANK';
READ                                   : 'READ';
READONLY                               : 'READONLY';
READS                                  : 'READS';
REAL                                   : 'REAL';
REBALANCE                              : 'REBALANCE';
REBUILD                                : 'REBUILD';
RECORDREADER                           : 'RECORDREADER';
RECORDWRITER                           : 'RECORDWRITER';
RECOVER                                : 'RECOVER';
RECURSIVE                              : 'RECURSIVE';
REDO                                   : 'REDO';
LOG                                    : 'LOG';
REDUCE                                 : 'REDUCE';
REDUNDANT                              : 'REDUNDANT';
REF                                    : 'REF';
REFERENCE                              : 'REFERENCE';
REFERENCES                             : 'REFERENCES';
REGEXP                                 : 'REGEXP';
RELAY                                  : 'RELAY';
RELAYLOG                               : 'RELAYLOG';
RELEASE                                : 'RELEASE';
RELOAD                                 : 'RELOAD';
RELY                                   : 'RELY';
REMOTE                                 : 'REMOTE';
REMOVE                                 : 'REMOVE';
RENAME                                 : 'RENAME';
REOPTIMIZATION                         : 'REOPTIMIZATION';
REORGANIZE                             : 'REORGANIZE';
REPAIR                                 : 'REPAIR';
REPEAT                                 : 'REPEAT';
REPEATABLE                             : 'REPEATABLE';
REPL                                   : 'REPL';
REPLACE                                : 'REPLACE';
REPLICA                                : 'REPLICA';
REPLICAS                               : 'REPLICAS';
REQUIRE                                : 'REQUIRE';
RESET                                  : 'RESET';
RESIGNAL                               : 'RESIGNAL';
RESOURCE                               : 'RESOURCE';
RESPECT                                : 'RESPECT';
RESTART                                : 'RESTART';
RESTORE                                : 'RESTORE';
RESTRICT                               : 'RESTRICT';
RESUME                                 : 'RESUME';
RETAIN                                 : 'RETAIN';
RETURN                                 : 'RETURN';
RETURNING                              : 'RETURNING';
RETURNS                                : 'RETURNS';
REUSE                                  : 'REUSE';
REVERSE                                : 'REVERSE';
REVOKE                                 : 'REVOKE';
REWRITE                                : 'REWRITE';
RIGHT                                  : 'RIGHT';
RLIKE                                  : 'RLIKE';
ROLE                                   : 'ROLE';
ROLES                                  : 'ROLES';
ROLLBACK                               : 'ROLLBACK';
ROLLUP                                 : 'ROLLUP';
ROTATE                                 : 'ROTATE';
ROUTINE                                : 'ROUTINE';
ROW                                    : 'ROW';
ROWS                                   : 'ROWS';
RTREE                                  : 'RTREE';
SAVEPOINT                              : 'SAVEPOINT';
SCHEDULE                               : 'SCHEDULE';
SCHEDULED                              : 'SCHEDULED';
SCHEMA                                 : 'SCHEMA';
SCHEMAS                                : 'SCHEMAS';
SECOND                                 : 'SECOND' 'S'?;
SECONDARY                              : 'SECONDARY';
SECURITY                               : 'SECURITY';
SELECT                                 : 'SELECT';
SEMI                                   : 'SEMI';
SENSITIVE                              : 'SENSITIVE';
SEPARATOR                              : 'SEPARATOR';
SEQUENCEFILE                           : 'SEQUENCEFILE';
SERDE                                  : 'SERDE';
SERDEPROPERTIES                        : 'SERDEPROPERTIES';
SERIAL                                 : 'SERIAL';
SERIALIZABLE                           : 'SERIALIZABLE';
SERVER                                 : 'SERVER';
SESSION                                : 'SESSION';
SET                                    : 'SET';
SETS                                   : 'SETS';
SHARE                                  : 'SHARE';
SHARED                                 : 'SHARED';
SHOW                                   : 'SHOW';
SHUTDOWN                               : 'SHUTDOWN';
SIGNAL                                 : 'SIGNAL';
SIGNED                                 : 'SIGNED';
SIMPLE                                 : 'SIMPLE';
SKEWED                                 : 'SKEWED';
SKIP_SYMBOL                            : 'SKIP';
SLAVE                                  : 'SLAVE';
SLOW                                   : 'SLOW';
SMALLINT                               : 'SMALLINT';
SNAPSHOT                               : 'SNAPSHOT';
SOCKET                                 : 'SOCKET';
SOME                                   : 'SOME';
SONAME                                 : 'SONAME';
SORT                                   : 'SORT';
SORTED                                 : 'SORTED';
SOUNDS                                 : 'SOUNDS';
SOURCE                                 : 'SOURCE';
SPATIAL                                : 'SPATIAL';
SPEC                                   : 'SPEC';
SPECIFIC                               : 'SPECIFIC';
SQL                                    : 'SQL';
SQLEXCEPTION                           : 'SQLEXCEPTION';
SQLSTATE                               : 'SQLSTATE';
SQLWARNING                             : 'SQLWARNING';
SRID                                   : 'SRID';
SSL                                    : 'SSL';
STACKED                                : 'STACKED';
START                                  : 'START';
STARTING                               : 'STARTING';
STARTS                                 : 'STARTS';
STATISTICS                             : 'STATISTICS';
STATUS                                 : 'STATUS';
STOP                                   : 'STOP';
STORAGE                                : 'STORAGE';
STORED                                 : 'STORED';
STREAM                                 : 'STREAM';
STREAMTABLE                            : 'STREAMTABLE';
STRING                                 : 'STRING';
STRUCT                                 : 'STRUCT';
SUBJECT                                : 'SUBJECT';
SUBPARTITION                           : 'SUBPARTITION';
SUBPARTITIONS                          : 'SUBPARTITIONS';
SUBSTR                                 : 'SUBSTR';
SUBSTRING                              : 'SUBSTRING';
SUM                                    : 'SUM';
SUMMARY                                : 'SUMMARY';
SUPER                                  : 'SUPER';
SUSPEND                                : 'SUSPEND';
SWAPS                                  : 'SWAPS';
SWITCHES                               : 'SWITCHES';
SYNC                                   : 'SYNC';
SYSTEM                                 : 'SYSTEM';
TABLE                                  : 'TABLE';
TABLES                                 : 'TABLES';
TABLESAMPLE                            : 'TABLESAMPLE';
TABLESPACE                             : 'TABLESPACE';
TBLPROPERTIES                          : 'TBLPROPERTIES';
TEMPORARY                              : 'TEMPORARY';
TEMPTABLE                              : 'TEMPTABLE';
TERMINATED                             : 'TERMINATED';
TEXT                                   : 'TEXT';
THAN                                   : 'THAN';
THEN                                   : 'THEN';
TIES                                   : 'TIES';
TIME                                   : 'TIME';
TIMESTAMP                              : 'TIMESTAMP';
TIMESTAMPLOCALTZ                       : 'TIMESTAMPLOCALTZ';
TIMESTAMPTZ                            : 'TIMESTAMPTZ';
TINYBLOB                               : 'TINYBLOB';
TINYINT                                : 'TINYINT';
TINYTEXT                               : 'TINYTEXT';
TLS                                    : 'TLS';
TO                                     : 'TO';
TOUCH                                  : 'TOUCH';
TRADITIONAL                            : 'TRADITIONAL';
TRAILING                               : 'TRAILING';
TRANSACTION                            : 'TRANSACTION';
TRANSACTIONAL                          : 'TRANSACTIONAL';
TRANSACTIONS                           : 'TRANSACTIONS';
TRANSFORM                              : 'TRANSFORM';
TREAT                                  : 'TREAT';
TREE                                   : 'TREE';
TRIGGER                                : 'TRIGGER';
TRIGGERS                               : 'TRIGGERS';
TRIM                                   : 'TRIM';
TRUE                                   : 'TRUE';
TRUNCATE                               : 'TRUNCATE';
TYPE                                   : 'TYPE';
TYPES                                  : 'TYPES';
UL_BINARY                              : '_BINARY';
UNARCHIVE                              : 'UNARCHIVE';
UNBOUNDED                              : 'UNBOUNDED';
UNCOMMITTED                            : 'UNCOMMITTED';
UNDEFINED                              : 'UNDEFINED';
UNDO                                   : 'UNDO';
UNDOFILE                               : 'UNDOFILE';
UNICODE                                : 'UNICODE';
UNINSTALL                              : 'UNINSTALL';
UNION                                  : 'UNION';
UNIONTYPE                              : 'UNIONTYPE';
UNIQUE                                 : 'UNIQUE';
UNIQUEJOIN                             : 'UNIQUEJOIN';
UNKNOWN                                : 'UNKNOWN';
UNLOCK                                 : 'UNLOCK';
UNMANAGED                              : 'UNMANAGED';
UNSET                                  : 'UNSET';
UNSIGNED                               : 'UNSIGNED';
UNTIL                                  : 'UNTIL';
UPDATE                                 : 'UPDATE';
UPGRADE                                : 'UPGRADE';
URI                                    : 'URI';
URL                                    : 'URL';
USAGE                                  : 'USAGE';
USE                                    : 'USE';
USER                                   : 'USER';
USING                                  : 'USING';
UTC                                    : 'UTC';
VALIDATE                               : 'VALIDATE';
VALIDATION                             : 'VALIDATION';
VALUE                                  : 'VALUE';
VALUE_TYPE                             : '$VALUE$';
VALUES                                 : 'VALUES';
VARBINARY                              : 'VARBINARY';
VARCHAR                                : 'VARCHAR';
VARCHARACTER                           : 'VARCHARACTER';
VARIABLES                              : 'VARIABLES';
VARYING                                : 'VARYING';
VCPU                                   : 'VCPU';
VECTORIZATION                          : 'VECTORIZATION';
VIEW                                   : 'VIEW';
VIEWS                                  : 'VIEWS';
VIRTUAL                                : 'VIRTUAL';
VISIBLE                                : 'VISIBLE';
WAIT                                   : 'WAIT';
WARNINGS                               : 'WARNINGS';
WEEK                                   : 'WEEK' 'S'?;
WHEN                                   : 'WHEN';
WHERE                                  : 'WHERE';
WHILE                                  : 'WHILE';
WINDOW                                 : 'WINDOW';
WITH                                   : 'WITH';
WITHIN                                 : 'WITHIN';
WITHOUT                                : 'WITHOUT';
WORK                                   : 'WORK';
WORKLOAD                               : 'WORKLOAD';
WRAPPER                                : 'WRAPPER';
WRITE                                  : 'WRITE';
X509                                   : 'X509';
XA                                     : 'XA';
XID                                    : 'XID';
XML                                    : 'XML';
XOR                                    : 'XOR';
YEAR                                   : 'YEAR' 'S'?;
ZEROFILL                               : 'ZEROFILL';
ZONE                                   : 'ZONE';
MYSQL                                  : 'MYSQL';
MAIN                                   : 'MAIN';
VERSION                                : 'VERSION';
PRIORITY    : 'PRIORITY';
LOW     : 'LOW';
HIGH    : 'HIGH';
STRAIGHT    : 'STRAIGHT';
SMALL       : 'SMALL';
RESULT      : 'RESULT';
BIG         : 'BIG';
BUFFER      : 'BUFFER';
CALC        : 'CALC';
APPEND      : 'APPEND';
DEPTH       : 'DEPTH';
LENGTH      : 'LENGTH';
PATCH       : 'PATCH';
OBJECT      : 'OBJECT';
OVERLAPS    : 'OVERLAPS';
PRETTY      : 'PRETTY';
QUOTE       : 'QUOTE';
VALID       : 'VALID';
REPORT      : 'REPORT';
SEARCH      : 'SEARCH';
FREE        : 'FREE';
SIZE        : 'SIZE';
UNQUOTE     : 'UNQUOTE';
DENSE       : 'DENSE';
CUME        : 'CUME';
DIST        : 'DIST';
NTH         : 'NTH';
WEIGHT      : 'WEIGHT';
DIFF        : 'DIFF';
AUTH        : 'AUTH';
DELAY       : 'DELAY';
EXTENT      : 'EXTENT';
FAILED      : 'FAILED';
LOGIN       : 'LOGIN';
ATTEMPTS    : 'ATTEMPTS';
PUBLIC      : 'PUBLIC';
IDS         : 'IDS';
INITIAL     : 'INITIAL';
METHOD      : 'METHOD';
CONNECT     : 'CONNECT';
RETRY       : 'RETRY';
HEARTBEAT   : 'HEARTBEAT';
PERIOD      : 'PERIOD';
NETWORK     : 'NETWORK';
NAMESPACE   : 'NAMESPACE';
POS         : 'POS';
ID          : 'ID';
CAPATH      : 'CAPATH';
CA          : 'CA';
CERT        : 'CERT';
CRLPATH     : 'CRLPATH';
CRL         : 'CRL';
CIPHERSUITES    : 'CIPHERSUITES';
ZSTD        : 'ZSTD';
CONNECTIONS : 'CONNECTIONS';
PER         : 'PER';
QUERIES     : 'QUERIES';
UPDATES     : 'UPDATES';
MESSAGE     : 'MESSAGE';
ERRNO       : 'ERRNO';
PACK        : 'PACK';
DIR         : 'DIR';
PRIVILEGE   : 'PRIVILEGE';
CHECKS      : 'CHECKS';
THREAD      : 'THREAD';
REPLICATE   : 'REPLICATE';
DB          : 'DB';
WILD        : 'WILD';
RESOURCES   : 'RESOURCES';
RETURNED    : 'RETURNED';
UNLOAD      : 'UNLOAD';
GTIDS       : 'GTIDS';
MTS       : 'MTS';
GAPS       : 'GAPS';
STATS       : 'STATS';
RECALC       : 'RECALC';
PERSISTENT       : 'PERSISTENT';
SAMPLE       : 'SAMPLE';
PAGES       : 'PAGES';
SUBCLASS       : 'SUBCLASS';
FRM       : 'FRM';




AMPERSAND_           : '&';
AND_                 : '&&';
ARROW_               : '=>';
ASSIGNMENT_OPERATOR_ : ':=';
ASTERISK_            : '*';
AT_                  : '@';
BACKSLASH_           : '\\';
BQ_                  : '`';
CARET_               : '^';
COLON_               : ':';
COMMA_               : ',';
//DEQ_                 : '==';
DOLLAR_              : '$';
DOT_                 : '.';
DOT_ASTERISK_        : '.*';
DQ_                  : '"';
EQ_                  : '=' | '==';
EXPONENT_            : '**';
GT_                  : '>';
GTE_                 : '>=';
LBE_                 : '{';
LBT_                 : '[';
LP_                  : '(';
LT_                  : '<';
LTE_                 : '<=';
MINUS_               : '-';
MOD_                 : '%';
NEQ_                 : '<>' | '!=' | '^=';
NOT_                 : '!';
OR_                  : '||';
PLUS_                : '+';
POUND_               : '#';
QUESTION_            : '?';
RANGE_OPERATOR_      : '..';
RBE_                 : '}';
RBT_                 : ']';
RP_                  : ')';
SAFE_EQ_             : '<=>';
SEMI_                : ';';
SIGNED_LEFT_SHIFT_   : '<<';
SIGNED_RIGHT_SHIFT_  : '>>';
SLASH_               : '/';
SQ_                  : '\'';
TILDE_               : '~';
VERTICAL_BAR_        : '|';
UL_                  : '_';
JSON_SEPARATOR:      '->';
JSON_UNQUOTED_SEPARATOR:      '->>';

WS : [ \t\r\n\u3000] + ->skip;

BLOCK_HINT : '/*+' .*? '*/';
INLINE_HINT: '--+' ~[\r\n]* ('\r'? '\n' | EOF);

BLOCK_COMMENT:  '/*' .*? '*/'                           -> channel(HIDDEN);
INLINE_COMMENT: '--' ~[\r\n]* ('\r'? '\n' | EOF)        -> channel(HIDDEN);

ERROR_END_BLOCK  : '$error' .*? '$end'                  -> channel(HIDDEN);
IF_END_BLOCK  : '$if' (ERROR_END_BLOCK | .)*? '$end'    -> channel(HIDDEN);
STRING_: SINGLE_QUOTED_TEXT;
SINGLE_QUOTED_TEXT: SQ_ (~('\'' | '\r' | '\n') | '\'' '\'' | '\r'? '\n')* SQ_;


DOUBLE_QUOTED_TEXT: (DQ_ ( '\\'. | '""' | ~('"'| '\\') )* DQ_);
BQ_QUOTED_TEXT: BQ_ ( '\\'. | '``' | ~('`'|'\\'))* BQ_;
NCHAR_TEXT: 'N' STRING_;
UCHAR_TEXT: 'U' STRING_;


NUMBER_: INTEGER_? DOT_? INTEGER_ ('E' (PLUS_ | MINUS_)? INTEGER_)?;
INTEGER_: INT_+;
HEX_DIGIT_: '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_;
BIT_NUM_: '0b' ('0' | '1')+ | 'B' SQ_ ('0' | '1')+ SQ_;

A: 'A';
K: 'K';
M: 'M';
G: 'G';
T: 'T';
P: 'P';
E: 'E';
H: 'H';

I_CURSOR : '[CURSOR]' ;
UNDERSCORE_CHARSET: '_' [0-9A-Z]+;
IP_ADDRESS : INTEGER_ DOT_ INTEGER_ DOT_ INTEGER_ DOT_ INTEGER_;
ByteLengthLiteral:  NO_ZERO_INT_ (INTEGER_)* BYTE_UNIT;

IDENTIFIER_: [A-Z\u0080-\u2FFF\u3001-\uFF0B\uFF0D-\uFFFF]+[A-Z_$#0-9\u0080-\u2FFF\u3001-\uFF0B\uFF0D-\uFFFF]*;


fragment INT_: [0-9];
fragment NO_ZERO_INT_: [1-9];
fragment HEX_: [0-9A-F];

fragment BYTE_UNIT : 'B' | 'KB' | 'MB' | 'GB' | 'TB' | 'M' | 'G' | 'T';
