parser grammar HiveParser;

options
{
    tokenVocab = HiveLexer;
}
root
    :( select
    | insert
    | update
    | delete
    | multiInsert
    | setCommands
    | createDatabase
    | dropDatabase
    | alterDatabase
    | useDatabase
    | createDataConnector
    | dropDataConnector
    | alterDataConnector
    | createTable
    | dropTable
    | truncateTable
    | alterTable
    | createView
    | dropView
    | alterView
    | createMaterializedView
    | alterMaterializedView
    | dropMaterializedView
    | createIndex
    | dropIndex
    | alterIndex
    | createMacro
    | dropMacro
    | createFunction
    | dropFunction
    | reloadFunction
    | createRole
    | dropRole
    | showRoles
    | setRole
    | grantRole
    | revokeRole
    | showRoleGrant
    | showRolePrincipals
    | grantPrivileges
    | revokePrivileges
    | showGrant
    | showClause
    | describe
    | abortTransaction
    | createScheduledQuery
    | exportOrImport
    | load
    | merge
    | explain
    ) SEMI_? SLASH_? EOF;
explain
    : EXPLAIN explainOption* (select|insert|update|delete|multiInsert)
    ;
explainOption
    : EXTENDED
    | FORMATTED
    | DEPENDENCY
    | CBO (COST | JOINCOST)?
    | LOGICAL
    | AUTHORIZATION
    | ANALYZE
    | REOPTIMIZATION
    | LOCKS
    | AST
    | VECTORIZATION ONLY? (SUMMARY| OPERATOR | EXPRESSION | DETAIL)?
    | DEBUG
    | DDL
    ;
load
    : LOAD DATA LOCAL? INPATH stringLiterals OVERWRITE? INTO TABLE tableName partitionSpec? inputFileFormat?
    ;
inputFileFormat
    : INPUTFORMAT stringLiterals SERDE  stringLiterals
    ;
virtualTableSource
    : TABLE LP_ valuesClause RP_ AS? identifier (LP_ identifier (COMMA_ identifier)*)? RP_
    ;
valuesClause
    : VALUES LP_ columnRefList RP_ (COMMA_ LP_ columnRefList RP_)*
    ;
lateralView
    : LATERAL VIEW OUTER functionCall identifier (AS identifier (COMMA_ identifier)*)?
    | COMMA_? LATERAL (
        VIEW functionCall identifier (AS identifier (COMMA_ identifier)*)?
        | TABLE LP_ valuesFunction RP_ AS? identifier (LP_ identifier (COMMA_ identifier)* RP_)?
    )
    ;
splitSample
    : TABLESAMPLE LP_ (numberLiterals  (PERCENT | ROWS) | ByteLengthLiteral) RP_
    ;
tableBucketSample
    : TABLESAMPLE LP_ BUCKET NUMBER_ OUT OF NUMBER_ (
        ON  expr (COMMA_ expr)*
    )? RP_
    ;
tableSample
    : tableBucketSample
    | splitSample
    ;
tableSource
    : tableName properties? tableSample? asOfClause? (
        AS? identifier
    )?
    ;
asOfClause
    : FOR (
        SYSTEM TIME AS OF expr
        | FOR SYSTEM VERSION AS OF NUMBER_
    )
    ;
whenClauses
    : (whenMatchedThenClause)* whenNotMatchedClause?
    ;
whenNotMatchedClause
    : WHEN NOT MATCHED ( AND expr)? THEN INSERT (LP_ columnNameList RP_)? insertValuesClause
    ;
whenMatchedThenClause
    : WHEN MATCHED ( AND expr)? THEN updateOrDelete
    ;
updateOrDelete
    : UPDATE setAssignmentsClause
    | DELETE
    ;
partitionTableFunctionSource
    : subquery
    | tableSource
    | partitionedTableFunction
    ;
partitionedTableFunction
    : identifier LP_ ON  partitionTableFunctionSource  partitionSpec? (
        identifier LP_ expr RP_ (COMMA_ identifier LP_ expr RP_)*
    )? RP_ identifier?
    ;
joinSourcePart
    : (tableSource | virtualTableSource | subQuerySource | partitionedTableFunction) lateralView*
    ;
subQuerySource
    : subquery AS? identifier
    ;

merge
    : MERGE INTO tableName ( AS identifier)? USING joinSourcePart ON simpleExpr whenClauses
    ;
multiInsert
    : fromClause insert+
    ;
definedAsSpec
    : DEFINED? AS (select| insert| update | delete)
    ;
intervalQualifiers
    : YEAR TO MONTH
    | DAY TO SECOND
    | YEAR
    | MONTH
    | DAY
    | HOUR
    | MINUTE
    | SECOND
    ;
executedAsSpec
    : EXECUTED AS stringLiterals
    ;
scheduleSpec
    : CRON stringLiterals
    | EVERY NUMBER_? qualifier = intervalQualifiers (
        (AT | OFFSET BY) stringLiterals
    )?
    ;
exportOrImport
    : EXPORT TABLE tableName partitionSpec? TO stringLiterals (FOR REPLICATION LP_ stringLiterals RP_)?
    | IMPORT (EXTERNAL? TABLE tableName partitionSpec?)? FROM stringLiterals dbLocation?
    ;
createScheduledQuery
    : CREATE SCHEDULED QUERY identifier  scheduleSpec executedAsSpec? enableValidateSpecification? definedAsSpec
    ;
abortTransaction
    : ABORT TRANSACTIONS NUMBER_+
    ;
describe
      : (DESCRIBE | DESC) (
      db_schema EXTENDED? dataConnectorName
      | CONNECTOR EXTENDED? tableName
      | FUNCTION EXTENDED? descFuncNames
      | (FORMATTED | EXTENDED) tabPartColTypeExpr
      | tabPartColTypeExpr
    )
    ;
sysFuncNames
    : AND
    | OR
    | NOT
    | LIKE
    | IF
    | CASE
    | WHEN
    | FLOOR
    | TINYINT
    | SMALLINT
    | INT
    | INTEGER
    | BIGINT
    | FLOAT
    | REAL
    | DOUBLE
    | BOOLEAN
    | STRING
    | BINARY
    | ARRAY
    | MAP
    | STRUCT
    | UNIONTYPE
    | EQ_
    | SAFE_EQ_
    | NEQ_
    | LTE_
    | LT_
    | GTE_
    | GT_
    | SLASH_
    | PLUS
    | MINUS
    | ASTERISK_
    | MOD
    | DIV
    | AMPERSAND_
    | TILDE_
    | VERTICAL_BAR_
    | CARET_
    | RLIKE
    | REGEXP
    | IN
    | BETWEEN
    ;
descFuncNames
    : sysFuncNames
    | stringLiterals
    | functionName
    ;
tabPartColTypeExpr
    : tableName partitionSpec? (identifier (DOT_ (ELEM_TYPE | KEY TYPE | VALUE TYPE | identifier))*)?
    ;
showClause
    : SHOW (db_schema (LIKE identifier)?
    | CONNECTORS
    | TABLES (IN identifier)? identifier?
    | VIEWS ((IN|FROM) identifier)? LIKE? identifier?
    | PARTITIONS tableName partitionSpec? whereClause? orderByClause? limitClause?
    | TABLE EXTENDED ((IN|FROM) identifier)? LIKE identifier partitionSpec?
    | TBLPROPERTIES tableName (LP_ columnNameList RP_)?
    | CREATE TABLE tableName|viewName
    | FORMATTED? (INDEX|INDEXES) ON tableName ((IN|FROM) identifier)?
    | COLUMNS (IN|FROM) identifier ((IN|FROM) identifier)* (LIKE identifier)?
    | FUNCTIONS (LIKE identifier)?
    | LOCKS db_schema? tableName partitionSpec? EXTENDED?
    | CONF identifier
    | TRANSACTIONS
    | COMPACTIONS ((COMPACTIONID EQ_ NUMBER_)?
            | tableName? partitionSpec? compactionPool? compactionType? compactionStatus? orderByClause? limitClause?
            | db_schema? tableName? compactionPool? compactionType? compactionStatus? orderByClause? limitClause?)
    )
    ;

compactionStatus
    : STATUS identifier
    ;
compactionType
    : TYPE identifier
    ;

showGrant
    : SHOW GRANT principalSpec? (ON ALL| privilegeObject)?
    ;

revokePrivileges
    : REVOKE (GRANT OPTION FOR)? privilegeList privilegeObject? FROM principalSpec
    ;
grantPrivileges
    : GRANT  privilegeList privilegeObject? TO principalSpec (WITH GRANT OPTION)?
    ;
privilegeObject
    : ON (db_schema identifier
         | TABLE? tableName  (LP_ columnNameList RP_)? partitionSpec?
         | URI stringLiterals
         | SERVER identifier
    )
    ;
privilegeList
    : privilegeTypeSpec*
    ;
privilegeTypeSpec
    : privilegeType (LP_ columnNameList RP_)?
    ;
privilegeType
    : ALL
    | ALTER
    | UPDATE
    | CREATE
    | DROP
    | LOCK
    | SELECT
    | SHOW DATABASE
    | INSERT
    | DELETE
    | INDEX
    ;
showRolePrincipals
    : SHOW PRINCIPALS roleName
    ;
showRoleGrant
    : showGrant
    | SHOW ROLE GRANT principalSpec
    ;
revokeRole
    : REVOKE (ADMIN OPTION FOR)? roleName (COMMA_ roleName)* FROM principalSpec
    ;
grantRole
    : GRANT ROLE? roleName (COMMA_ roleName)* TO principalSpec (WITH GRANT OPTION)?
    ;
principalSpec
    : (USER username| ROLE roleName | GROUP groupName) (COMMA_ (USER username| ROLE roleName| GROUP groupName))*
    |
    ;
setRole
    : SET ROLE roleName|ALL|NONE
    ;
showRoles
    : SHOW CURRENT? ROLES
    ;
dropRole
    : DROP ROLE roleName
    ;
createRole
    : CREATE ROLE roleName
    ;
reloadFunction
    : RELOAD (FUNCTIONS|FUNCTION)
    ;
dropFunction
    : DROP TEMPORARY? FUNCTION ifExists functionName
    ;
createFunction
    : CREATE TEMPORARY? FUNCTION functionName AS className (USING resourceList)?
    ;
resourceList
    : resource (COMMA_ resource)*
    ;

resource
    : (JAR|FILE|ARCHIVE) stringLiterals
    ;
macroName
    : (owner DOT_)? name
    ;
className
    : (owner DOT_)? name
    ;
dropMacro
    : DROP TEMPORARY MACRO ifExists macroName
    ;
createMacro
    : CREATE TEMPORARY MACRO macroName LP_ columnDefinition? RP_ expr
    ;
alterIndex
    : ALTER INDEX indexName ON tableName partitionSpec? REBUILD
    ;
dropIndex
    : DROP INDEX ifExists indexName ON tableName
    ;
createIndex
    : CREATE INDEX indexName ON TABLE baseTable = tableName LP_ columnNameList RP_ AS createIndexSpecification (WITH DEFERRED REBUILD)?
     (IDXPROPERTIES properties)? (IN TABLE indexTable = tableName)? tableRowFormat? tableFileFormat? dbLocation? tablePropertiesPrefixed? commentClause?
    ;
createIndexSpecification
    : COMPACT | BITMAP
    ;
dropMaterializedView
    : DROP MATERIALIZED VIEW viewName
    ;
alterMaterializedView
    : ALTER MATERIALIZED VIEW viewName ((ENABLE|DISABLE) REWRITE| REBUILD)
    ;
createMaterializedView
    : CREATE MATERIALIZED VIEW ifNotExists? viewName (DISABLE REWRITE)? commentClause? partitionSpec? viewBuckets? tableRowFormat? tableFileFormat?
    dbLocation? tablePropertiesPrefixed? AS select
    ;
viewBuckets
    : ( CLUSTERED ON LP_ columnNameList RP_
    | DISTRIBUTED ON LP_ columnNameList RP_ SORTED ON LP_ columnNameList RP_
    )
    ;
alterView
    : ALTER VIEW viewName ( AS select
    | SET tablePropertiesPrefixed
    )
    ;
dropView
    : DROP VIEW ifExists? viewName
    ;
createView
    : CREATE VIEW viewName  (LP_ viewColumnList RP_)? commentClause? tablePropertiesPrefixed?  AS select
    ;
viewColumnList
    : columnName commentClause? (COMMA_ columnName commentClause?)*
    ;
alterTable
    : ALTER TABLE tableName alterDefinitionClause
    | MSCK REPAIR? tableName repairTablePartitions
    | ALTER TABLE COMPACT tableName compactionPool
    ;
compactionPool
    : POOL identifier
    ;
repairTablePartitions
    : (ADD|DROP|SYNC) PARTITIONS
    ;
alterDefinitionClause
    : alterTableProperties
    | alterTablePartition
    | alterTableEitherOrPartition
    | partitionSpec? alterTableColumns
    ;
alterTableColumns
    : ( alterRuleForColumnNamesClause
    | alterAddOrReplaceColumnClasuse
    )
    ;

alterAddOrReplaceColumnClasuse
    : (ADD|REPLACE) COLUMNS LP_ columnDefinition RP_ restrictOrCascade?
    ;
alterRuleForColumnNamesClause
    : CHANGE COLUMN? columnName columnClause alterChangeColPosition? restrictOrCascade?
    ;
alterChangeColPosition
    : FIRST | AFTER columnName
    ;
alterTablePartitionColumnsClause
    : partitionSpec UPDATE COLUMNS
    ;
alterTableEitherOrPartition
    : ( alterPatitionFileFormatClause
    | alterPartitionLocationClase
    | alterPartitionTouchClause
    | alterPartitionProtectionsClause
    | alterPatitionCompactClause
    | alterPartitionConcatenateClause
    | alterTablePartitionColumnsClause
    )
    ;
alterPartitionConcatenateClause
    : partitionSpec CONCATENATE
    ;
alterPatitionCompactClause
    : partitionSpec? COMPACT partitionCompactionType (AND WAIT)? clusteredBucketsOption? orderByClause? (POOL stringLiterals)? overwriteTblPropertiesClause?
    ;
overwriteTblPropertiesClause
    : WITH OVERWRITE TBLPROPERTIES properties
    ;
clusteredBucketsOption
    : CLUSTERED INTO (INT|identifier) BUCKETS
    ;
partitionCompactionType
    : (MAJOR | MINOR |REBALANCE)
    | stringLiterals
    ;
alterPartitionProtectionsClause
    : partitionSpec? enableValidateSpecification (NO DROP|OFFLINE) CASCADE?
    ;
alterPartitionTouchClause
    : TOUCH partitionSpec
    ;
alterPartitionLocationClase
    : partitionSpec? SET LOCATION stringLiterals
    ;
alterPatitionFileFormatClause
    : partitionSpec? SET FILEFORMAT tableFileFormat
    ;
alterTablePartition
    : ( addPartitionClause
    | renamePartitionClause
    | alterPartitionClause
    | recoverPartitionClause
    | dropPartitionClause
    | archivePartitionClause
    )
    ;

archivePartitionClause
    : (ARCHIVE|UNARCHIVE) partitionSpec
    ;
dropPartitionClause
    : DROP ifExists? partitionSpec (COMMA_ partitionSpec)* (IGNORE PROTECTION)? PURGE?
    ;
recoverPartitionClause
    : RECOVER PARTITIONS
    ;
alterPartitionClause
    : EXCHANGE partitionSpec WITH TABLE tableName
    ;
renamePartitionClause
    : partitionSpec RENAME TO partitionSpec
    ;
addPartitionClause
    : ADD partitionSpec dbLocation? (partitionSpec dbLocation?)*
    ;
alterTableProperties
    :(renameTableName
    | setPropertiesClause
    | alterSerDePropertiesClause
    | removeSerDePropertiesClause
    | alterStoragePropertiesClause
    | alterSkewedClause
    | alterNotSkewedClause
    | alterNotStoredAsDirectoriesClause
    | alterConstraintsClause
    )
    ;

alterConstraintsClause
    : (ADD | CHANGE) tableConstraint
    | DROP CONSTRAINT stringLiterals
    ;
alterNotStoredAsDirectoriesClause
    : NOT STORED AS DIRECTORIES
    ;
alterNotSkewedClause
    : NOT SKEWED
    ;
alterSkewedClause
    : tableSkewed
    ;
tableSkewed
    : SKEWED BY LP_ columnNameList RP_ ON LP_  skewedValueElement RP_ storedAsDirs?
    | SET SKEWED LOCATION properties
    ;
storedAsDirs
    : STORED AS DIRECTORIES
    ;
skewedValueElement
    : skewedColumnValues
    | skewedColumnValuePairList
    ;
skewedColumnValuePairList
    : skewedColumnValuePair (COMMA_ skewedColumnValuePair)*
    ;

skewedColumnValuePair
    : LP_ skewedColumnValues RP_
    ;

skewedColumnValues
    : skewedColumnValue (COMMA_ skewedColumnValue)*
    ;
skewedColumnValue
    : identifier
    ;
alterStoragePropertiesClause
    : tableBuckets
    ;
tableBuckets
    : CLUSTERED BY LP_  columnNameList RP_ (
        SORTED BY LP_  columnNameOrderList RP_
    )? INTO NUMBER_ BUCKETS
    ;
columnNameOrderList
    : columnNameOrder (COMMA_ columnNameOrder)*
    ;
orderSpecification
    : ASC
    | DESC
    ;
nullOrdering
    : NULLS (FIRST | LAST)
    ;
columnNameOrder
    : columnName  orderSpecification?  nullOrdering?
    ;
removeSerDePropertiesClause
    : partitionSpec? UNSET SERDEPROPERTIES propertieNames
    ;
propertieNames
    : LP_ stringLiterals (stringLiterals*) RP_
    ;
alterSerDePropertiesClause
    : partitionSpec? SET ((SERDE stringLiterals) | SERDEPROPERTIES ) stringLiterals (WITH SERDEPROPERTIES properties)?
    ;
setPropertiesClause
    : SET tablePropertiesPrefixed
    ;
renameTableName
    : RENAME TO tableName
    ;
truncateTable
    : TRUNCATE TABLE? tableName partitionSpec (COLUMNS LP_ columnNameList RP_)? force?
    ;
force
    : FORCE
    ;
partitionSpec
    : PARTITION LP_ partitionVal (COMMA_ partitionVal)* RP_
    ;

partitionVal
    : identifier (EQ_ assignmentValue)?
    ;
replicationClause
    : FOR METADATA? REPLICATION LP_ stringLiterals RP_
    ;
dropTable
    : DROP TABLE ifExists? tableName PURGE? replicationClause?
    ;
createTable
    : CREATE TEMPORARY? TRANSACTIONAL? EXTERNAL? TABLE ifNotExists? tableName (
            likeTableOrFile createTablePartitionSpec? tableRowFormat? tableFileFormat? dbLocation? tablePropertiesPrefixed?
            | (LP_ columnNameTypeOrConstraintList RP_)? commentClause? createTablePartitionSpec? tableBuckets? tableSkewed? tableRowFormat?
                tableFileFormat? dbLocation? tablePropertiesPrefixed? (AS select)?
        )
    | CREATE MANAGED TABLE ifNotExists? tableName (
            likeTableOrFile tableRowFormat? tableFileFormat? dbLocation? tablePropertiesPrefixed?
            | (LP_ columnNameTypeOrConstraintList RP_)? commentClause? createTablePartitionSpec? tableBuckets? tableSkewed? tableRowFormat?
                tableFileFormat? dbLocation? tablePropertiesPrefixed? (AS select)?
    )
    ;
columnNameTypeOrConstraintList
    : columnNameTypeOrConstraint (COMMA_ columnNameTypeOrConstraint)*
    ;
tableFileFormat
    : STORED AS INPUTFORMAT stringLiterals OUTPUTFORMAT stringLiterals (
        INPUTDRIVER stringLiterals OUTPUTDRIVER stringLiterals
    )?
    | STORED BY stringLiterals (
        WITH SERDEPROPERTIES stringLiterals
    )? (STORED AS identifier)?
    | STORED BY identifier (WITH SERDEPROPERTIES properties)? (
        STORED AS identifier
    )?
    | STORED AS identifier
    ;
tableRowFormat
    : rowFormatDelimited
    | rowFormatSerde
    ;
rowFormatSerde
    : ROW FORMAT SERDE? string_ (
        WITH SERDEPROPERTIES properties
    )?
    ;
rowFormatDelimited
    : ROW FORMAT DELIMITED tableRowFormatFieldIdentifier? tableRowFormatCollItemsIdentifier? tableRowFormatMapKeysIdentifier?
        tableRowFormatLinesIdentifier? tableRowNullFormat?
    ;
tableRowFormatFieldIdentifier
    : FIELDS TERMINATED BY string_ (ESCAPED BY string_)?
    ;
tableRowFormatCollItemsIdentifier
    : COLLECTION ITEMS TERMINATED BY string_
    ;
tableRowFormatMapKeysIdentifier
    : MAP KEYS TERMINATED BY string_
    ;
tableRowFormatLinesIdentifier
    : LINES TERMINATED BY string_
    ;
tableRowNullFormat
    : NULL DEFINED AS string_
    ;

partitionTransformSpec
    : partitionTransformType (COMMA_ partitionTransformType)*
    ;
partitionTransformType
    : columnName
    | (YEAR | MONTH | DAY | HOUR) LP_ columnName RP_
    | (TRUNCATE | BUCKET) LP_  NUMBER_ COMMA_ columnName RP_
    ;
columnNameList
    : columnName (COMMA_ columnName)*
    ;
colConstraint
    : (CONSTRAINT identifier)? columnConstraintType constraintOptsCreate?
    ;
columnConstraint
    : foreignKeyConstraint
    | colConstraint
    ;
columnConstraintType
    : NOT NULL
    | DEFAULT defaultVal
    | checkConstraint
    | tableConstraintType
    ;
columnNameTypeOrConstraint
    : tableConstraint
    | columnClause
    ;
constraintOptsCreate
    : enableValidateSpecification (RELY | NORELY)?
    ;
createConstraint
    : (CONSTRAINT identifier)? (UNIQUE| PRIMARY KEY) LP_ columnNameList RP_ constraintOptsCreate?
    ;
createForeignKey
    : (CONSTRAINT identifier)? FOREIGN KEY  LP_ foreignkey_col = columnNameList RP_ REFERENCES tableName LP_ references_col = columnNameList RP_ constraintOptsCreate?
    ;
createCheckConstraint
    : (CONSTRAINT identifier) checkConstraint
    ;
tableConstraint
    : createForeignKey
    | createConstraint
    | createCheckConstraint
    ;


enableValidateSpecification
    : (ENABLE | DISABLE) (VALIDATE| NOVALIDATE)?
    | (ENFORCED | NOT ENFORCED)
    ;
foreignKeyConstraint
    : (CONSTRAINT identifier)? REFERENCES tableName LP_ columnName RP_ constraintOptsCreate?
    ;
checkConstraint
    : CHECK LP_ expr RP_
    ;
tableConstraintType
    : PRIMARY KEY
    | UNIQUE
    ;

defaultVal
    : LITERAL
    | CURRENT TIMESTAMP LP_ RP_
    | CURRENT DATE LP_ RP_
    | CURRENT USER LP_ RP_
    | NULL
    ;
columnClause
    : columnName dataType columnConstraint? commentClause?
    ;
columnDefinition
    : columnClause (COMMA_ columnClause)*
    ;
createTablePartitionSpec
    : PARTITIONED BY (
        LP_ (columnDefinition | columnNameList)
        | SPEC LP_  partitionTransformSpec
    ) RP_
    ;

likeTableOrFile
    : LIKE FILE
    | LIKE FILE identifier stringLiterals
    | LIKE tableName
    ;
tablePropertiesPrefixed
    : TBLPROPERTIES properties
    ;
alterSuffixSetUrl
    : SET URL identifier
    ;
alterDataConnector
    : ALTER CONNECTOR dataConnectorName ( alterSuffixProperties
                                          | alterSuffixSetOwner
                                          | alterSuffixSetUrl)
    ;
dropDataConnector
    : DROP CONNECTOR ifExists? dataConnectorName
    ;
createDataConnector
    : CREATE CONNECTOR ifNotExists? dataConnectorName dataConnectorType? dataConnectorUrl? commentClause? (
             WITH DCPROPERTIES properties
         )?
    ;
dataConnectorUrl
    : URL identifier
    ;
dataConnectorType
    : TYPE identifier
    ;
dataConnectorName
    : identifier
    ;
useDatabase
    : USE databaseName
    ;
alterDatabase
    : ALTER db_schema databaseName (alterSuffixProperties
                                    | alterSuffixSetOwner
                                    | alterSuffixSetLocation
                                    )
    ;
alterSuffixSetLocation
    : SET (dbLocation
    | dbManagedLocation)
    ;
alterSuffixSetOwner
    : SET OWNER (USER
    | GROUP
    | ROLE
    ) identifier
    ;
alterSuffixProperties
    : SET DBPROPERTIES properties
    ;
dropDatabase
    : DROP db_schema ifExists? databaseName restrictOrCascade?
    ;
restrictOrCascade
    : RESTRICT
    | CASCADE
    ;
createDatabase
    : CREATE REMOTE? db_schema ifNotExists? databaseName commentClause? dbLocation? dbManagedLocation? (USING simpleExpr)? (
        WITH DBPROPERTIES properties
        )?
    ;
properties
    : LP_ propertiesList RP_
    ;
propertiesList
    : keyValueProperty (COMMA_ keyValueProperty)*
    ;
keyValueProperty
    : stringLiterals EQ_ stringLiterals
    ;
dbManagedLocation
    : MANAGEDLOCATION stringLiterals
    ;
dbLocation
    : LOCATION stringLiterals
    ;
commentClause
    : COMMENT stringLiterals
    ;
db_schema
    : DATABASE
    | SCHEMA
    ;
databaseName
    : identifier
    ;
setCommands
    : SET commandsCli?
    ;
commandsCli
    : identifier (DOT_ identifier)* EQ_ (identifier| literals|numberLiterals)
    ;
insert
    : INSERT insertSpecification?
    ( (INTO? TABLE? tableName partitionSpec?)
    | (LOCAL? DIRECTORY identifier
        tableRowFormat?  tableFileFormat?)
    )
    (insertValuesClause | setAssignmentsClause | insertSelectClause) onDuplicateKeyClause?
    ;

insertSpecification
    : (LOW PRIORITY | DELAYED | HIGH PRIORITY | OVERWRITE)? IGNORE?
    ;

insertValuesClause
    : (LP_ fields? RP_ )? (VALUES | VALUE) (assignmentValues (COMMA_ assignmentValues)* | rowConstructorList) valueReference?
    ;

fields
    : insertIdentifier (COMMA_ insertIdentifier)*
    ;

insertIdentifier
    : columnRef | tableWild
    ;

tableWild
    : identifier DOT_ (identifier DOT_)? ASTERISK_
    ;

insertSelectClause
    : valueReference? (LP_ fields? RP_)? select
    ;

onDuplicateKeyClause
    : (AS identifier)? ON DUPLICATE KEY UPDATE assignment (COMMA_ assignment)*
    ;

valueReference
    : AS alias derivedColumns?
    ;

derivedColumns
    : LP_ alias (COMMA_ alias)* RP_
    ;

update
    : withClause? UPDATE updateSpecification_ tableReferences setAssignmentsClause whereClause? orderByClause? limitClause?
    ;

updateSpecification_
    : (LOW PRIORITY)? IGNORE?
    ;

assignment
    : columnRef EQ_ assignmentValue
    ;

setAssignmentsClause
    : valueReference? SET assignment (COMMA_ assignment)*
    ;

assignmentValues
    : LP_ assignmentValue (COMMA_ assignmentValue)* RP_
    | LP_ RP_
    ;

assignmentValue
    : blobValue | expr | DEFAULT | stringLiterals
    ;

blobValue
    : UL_BINARY string_
    ;

delete
    : DELETE deleteSpecification (singleTableClause | multipleTablesClause) whereClause? orderByClause? limitClause?
    ;

deleteSpecification
    : (LOW PRIORITY)? QUICK? IGNORE?
    ;

singleTableClause
    : FROM tableName (AS? alias)? partitionNames?
    ;

multipleTablesClause
    : tableAliasRefList FROM tableReferences | FROM tableAliasRefList USING tableReferences
    ;

select
    : selectSubquery
    ;
selectSubquery
    : selectSubquery combineClause selectSubquery | (queryExpression lockClauseList? | queryExpressionParens | selectWithInto) orderByClause? limitClause?
    ;

selectWithInto
    : LP_ selectWithInto RP_
    | queryExpression selectIntoExpression lockClauseList?
    | queryExpression lockClauseList selectIntoExpression
    ;

queryExpression
    : withClause? (queryExpressionBody | queryExpressionParens)
    ;

queryExpressionBody
    : queryPrimary
    | queryExpressionParens
    ;

combineClause
    : UNION combineOption?
    | EXCEPT combineOption?
    ;

queryExpressionParens
    : LP_ selectSubquery RP_
    ;

queryPrimary
    : querySpecification
    | tableValueConstructor
    | tableStatement
    ;

querySpecification
    : SELECT selectSpecification* projections selectIntoExpression? fromClause? whereClause? groupByClause? havingClause? windowClause?
    ;

tableStatement
    : TABLE tableName
    ;

tableValueConstructor
    : VALUES rowConstructorList
    ;

rowConstructorList
    : ROW assignmentValues (COMMA_ ROW assignmentValues)*
    ;

withClause
    : WITH RECURSIVE? cteClause (COMMA_ cteClause)*
    ;

cteClause
    : identifier (LP_ columnNames RP_)? AS subquery
    ;

selectSpecification
    : duplicateSpecification
    | HIGH PRIORITY
    | STRAIGHT JOIN
    | SQL SMALL RESULT
    | SQL BIG RESULT
    | SQL BUFFER RESULT
    | SQL NO CACHE
    | SQL CALC FOUND ROWS
    ;

duplicateSpecification
    : ALL | DISTINCT | DISTINCTROW
    ;

projections
    : (unqualifiedShorthand | projection) (COMMA_ projection)*
    ;

projection
    : expr (AS? alias)? | qualifiedShorthand
    ;

unqualifiedShorthand
    : ASTERISK_
    ;

qualifiedShorthand
    : (identifier DOT_)? identifier DOT_ASTERISK_
    ;

fromClause
    : FROM (DUAL | tableReferences)
    ;

tableReferences
    : tableReference (COMMA_ tableReference)*
    ;

escapedTableReference
    : tableFactor joinedTable*
    ;

tableReference
    : (tableFactor | LBE_ OJ escapedTableReference RBE_) joinedTable*
    ;

tableFactor
    : tableName partitionNames? (AS? alias)? indexHintList? | subquery AS? alias (LP_ columnNames RP_)? | LP_ tableReferences RP_
    ;

partitionNames
    : PARTITION LP_ identifier (COMMA_ identifier)* RP_
    ;

indexHintList
    : indexHint (COMMA_ indexHint)*
    ;

indexHint
    : (USE | IGNORE | FORCE) (INDEX | KEY) (FOR (JOIN | ORDER BY | GROUP BY))? LP_ indexName (COMMA_ indexName)* RP_
    ;

joinedTable
    : innerJoinType tableReference joinSpecification?
    | outerJoinType tableReference joinSpecification
    | naturalJoinType tableFactor
    ;

innerJoinType
    : (INNER | CROSS)? JOIN
    | STRAIGHT JOIN
    ;

outerJoinType
    : (LEFT | RIGHT) OUTER? JOIN
    ;

naturalJoinType
    : NATURAL INNER? JOIN
    | NATURAL (LEFT | RIGHT) OUTER? JOIN
    ;

joinSpecification
    : ON expr | USING LP_ columnNames RP_
    ;

whereClause
    : WHERE expr
    ;

groupByClause
    : GROUP BY orderByItem (COMMA_ orderByItem)* (WITH ROLLUP)?
    ;

havingClause
    : HAVING expr
    ;

limitClause
    : LIMIT ((limitOffset COMMA_)? limitRowCount | limitRowCount OFFSET limitOffset)
    ;

limitRowCount
    : numberLiterals | parameterMarker
    ;

limitOffset
    : numberLiterals | parameterMarker
    ;

windowClause
    : WINDOW windowItem (COMMA_ windowItem)*
    ;

windowItem
    : identifier AS windowSpecification
    ;

subquery
    : queryExpressionParens
    ;

selectLinesInto
    : STARTING BY string_ | TERMINATED BY string_
    ;

selectFieldsInto
    : TERMINATED BY string_ | OPTIONALLY? ENCLOSED BY string_ | ESCAPED BY string_
    ;

selectIntoExpression
    : INTO variable (COMMA_ variable )* | INTO DUMPFILE string_
    | (INTO OUTFILE string_ (CHARACTER SET charsetName)?(COLUMNS selectFieldsInto+)? (LINES selectLinesInto+)?)
    ;

lockClause
    : FOR lockStrength tableLockingList? lockedRowAction?
    | LOCK IN SHARE MODE
    ;

lockClauseList
    : lockClause+
    ;

lockStrength
    : UPDATE | SHARE
    ;

lockedRowAction
    : SKIP_SYMBOL LOCKED | NOWAIT
    ;

tableLockingList
    : OF tableAliasRefList
    ;

tableIdentOptWild
    : tableName DOT_ASTERISK_?
    ;

tableAliasRefList
    : tableIdentOptWild (COMMA_ tableIdentOptWild)*
    ;


customKeyword
    : MAX
    | MIN
    | SUM
    | COUNT
    | GROUP CONCAT
    | CAST
    | POSITION
    | SUBSTRING
    | SUBSTR
    | EXTRACT
    | TRIM
    | LAST DAY
    | TRADITIONAL
    | TREE
    | MYSQL ADMIN
    | INSTANT
    | INPLACE
    | COPY
    | UL_BINARY
    | AUTOCOMMIT
    | INNODB
    | REDO LOG
    | LAST VALUE
    | PRIMARY
    | MAXVALUE
    | BIT XOR
    | MYSQL MAIN
    | UTC DATE
    | UTC TIME
    | UTC TIMESTAMP
    ;

literals
    : stringLiterals
    | numberLiterals
    | temporalLiterals
    | hexadecimalLiterals
    | bitValueLiterals
    | booleanLiterals
    | nullValueLiterals
    ;

string_
    : DOUBLE_QUOTED_TEXT | SINGLE_QUOTED_TEXT | STRING_
    ;

stringLiterals
    : (UNDERSCORE_CHARSET | UL_BINARY )? string_ | NCHAR_TEXT
    ;

numberLiterals
    : (PLUS_ | MINUS_)? NUMBER_
    ;

temporalLiterals
    : (DATE | TIME | TIMESTAMP) SINGLE_QUOTED_TEXT
    ;

hexadecimalLiterals
    : UNDERSCORE_CHARSET? HEX_DIGIT_ collateClause?
    ;

bitValueLiterals
    : UNDERSCORE_CHARSET? BIT_NUM_ collateClause?
    ;

booleanLiterals
    : TRUE | FALSE
    ;

nullValueLiterals
    : NULL
    ;

collationName
    : textOrIdentifier | BINARY
    ;

identifier
    : IDENTIFIER_
    | unreservedWord
    | identifierKeywordsAmbiguous1RolesAndLabels
    | identifierKeywordsAmbiguous2Labels
    | identifierKeywordsAmbiguous3Roles
    | identifierKeywordsAmbiguous4SystemVariables
    | customKeyword
    | DOUBLE_QUOTED_TEXT
    | UNDERSCORE_CHARSET
    | BQ_QUOTED_TEXT
    | literals
    ;


textOrIdentifier
    : identifier | string_ | ipAddress
    ;

ipAddress
    : IP_ADDRESS
    ;

variable
    : userVariable | systemVariable
    ;

userVariable
    : AT_ textOrIdentifier
    | textOrIdentifier
    ;

systemVariable
    : AT_ AT_ (systemVariableScope=(GLOBAL | SESSION | LOCAL) DOT_)? rvalueSystemVariable
    ;

rvalueSystemVariable
    : textOrIdentifier
    | textOrIdentifier DOT_ identifier
    ;





charsetName
    : textOrIdentifier | BINARY | DEFAULT
    ;
tableName
    : (owner DOT_)? name
    ;

columnName
    : identifier
    ;

indexName
    : identifier
    ;




userIdentifierOrText
    : textOrIdentifier (AT_ textOrIdentifier)?
    ;

username
    : userIdentifierOrText | CURRENT USER (LP_ RP_)?
    ;




functionName
    : (owner DOT_)? identifier
    ;



viewName
    : (owner DOT_)? identifier
    ;
owner
    : identifier
    ;

alias
    : textOrIdentifier
    ;

name
    : identifier
    ;



columnNames
    : columnName (COMMA_ columnName)*
    ;

groupName
    : identifier
    ;




roleName
    : roleIdentifierOrText (AT_ textOrIdentifier)?
    ;

roleIdentifierOrText
    : identifier | string_
    ;



expr
    : booleanPrimary
    | expr andOperator expr
    | expr orOperator expr
    | expr XOR expr
    | notOperator expr
    ;

andOperator
    : AND | AND_
    ;

orOperator
    : OR | OR_
    ;

notOperator
    : NOT | NOT_
    ;

booleanPrimary
    : booleanPrimary IS NOT? (TRUE | FALSE | UNKNOWN | NULL)
    | booleanPrimary SAFE_EQ_ predicate
    | booleanPrimary MEMBER OF LP_ (expr) RP_
    | booleanPrimary comparisonOperator predicate
    | booleanPrimary comparisonOperator (ALL | ANY) subquery
    | booleanPrimary assignmentOperator predicate
    | predicate
    ;

assignmentOperator
    : EQ_ | ASSIGNMENT_OPERATOR_
    ;

comparisonOperator
    : EQ_ | GTE_ | GT_ | LTE_ | LT_ | NEQ_
    ;

predicate
    : bitExpr NOT? IN subquery
    | bitExpr NOT? IN LP_ expr (COMMA_ expr)* RP_
    | bitExpr NOT? BETWEEN bitExpr AND predicate
    | bitExpr SOUNDS LIKE bitExpr
    | bitExpr NOT? LIKE simpleExpr (ESCAPE simpleExpr)?
    | bitExpr NOT? (REGEXP | RLIKE) bitExpr
    | bitExpr
    ;

bitExpr
    : bitExpr VERTICAL_BAR_ bitExpr
    | bitExpr AMPERSAND_ bitExpr
    | bitExpr SIGNED_LEFT_SHIFT_ bitExpr
    | bitExpr SIGNED_RIGHT_SHIFT_ bitExpr
    | bitExpr PLUS_ bitExpr
    | bitExpr MINUS_ bitExpr
    | bitExpr ASTERISK_ bitExpr
    | bitExpr SLASH_ bitExpr
    | bitExpr DIV bitExpr
    | bitExpr MOD bitExpr
    | bitExpr MOD_ bitExpr
    | bitExpr CARET_ bitExpr
    | bitExpr PLUS_ intervalExpression
    | bitExpr MINUS_ intervalExpression
    | simpleExpr
    ;

simpleExpr
    : functionCall
    | parameterMarker
    | literals
    | columnRef
    | simpleExpr collateClause
    | variable
    | simpleExpr OR_ simpleExpr
    | (PLUS_ | MINUS_ | TILDE_ | notOperator | orOperator | andOperator | BINARY | CARET_ | VERTICAL_BAR_  ) simpleExpr
    | ROW? LP_ expr (COMMA_ expr)* RP_
    | EXISTS? subquery
    | LBE_ identifier expr RBE_
    | identifier (JSON_SEPARATOR | JSON_UNQUOTED_SEPARATOR) string_
    | path (RETURNING dataType)? onEmptyError?
    | matchExpression
    | caseExpression
    | intervalExpression
    ;

path
    : string_
    ;

onEmptyError
    : (NULL | ERROR | DEFAULT literals) ON (EMPTY | ERROR)
    ;

columnRef
    : identifier (DOT_ identifier)? (DOT_ identifier)?
    ;

columnRefList
    : columnRef (COMMA_ columnRef)*
    ;

functionCall
    : aggregationFunction | specialFunction | regularFunction | jsonFunction | udfFunction
    ;

udfFunction
    : functionName LP_ (expr? | expr (COMMA_ expr)*) RP_
    ;

aggregationFunction
    : aggregationFunctionName LP_ distinct? (expr (COMMA_ expr)* | ASTERISK_)? collateClause? RP_ overClause?
    ;

jsonFunction
    : columnRef (JSON_SEPARATOR | JSON_UNQUOTED_SEPARATOR) path
    | jsonFunctionName LP_ (expr? | expr (COMMA_ expr)*) RP_
    ;

jsonFunctionName
    : JSON ARRAY
    | JSON ARRAY APPEND
    | JSON ARRAY INSERT
    | JSON CONTAINS
    | JSON CONTAINS PATH
    | JSON DEPTH
    | JSON EXTRACT
    | JSON INSERT
    | JSON KEYS
    | JSON LENGTH
    | JSON MERGE
    | JSON MERGE PATCH
    | JSON MERGE PRESERVE
    | JSON OBJECT
    | JSON OVERLAPS
    | JSON PRETTY
    | JSON QUOTE
    | JSON REMOVE
    | JSON REPLACE
    | JSON SCHEMA VALID
    | JSON SCHEMA VALIDATION REPORT
    | JSON SEARCH
    | JSON SET
    | JSON STORAGE FREE
    | JSON STORAGE SIZE
    | JSON TABLE
    | JSON TYPE
    | JSON UNQUOTE
    | JSON VALID
    | JSON VALUE
    | MEMBER OF
    ;

aggregationFunctionName
    : MAX | MIN | SUM | COUNT | AVG | (BIT XOR)
    ;

distinct
    : DISTINCT
    ;

overClause
    : OVER (windowSpecification | identifier)
    ;

windowSpecification
    : LP_ identifier? (PARTITION BY expr (COMMA_ expr)*)? orderByClause? frameClause? RP_
    ;

frameClause
    : (ROWS | RANGE) (frameStart | frameBetween)
    ;

frameStart
    : CURRENT ROW | UNBOUNDED PRECEDING | UNBOUNDED FOLLOWING | expr PRECEDING | expr FOLLOWING
    ;

frameEnd
    : frameStart
    ;

frameBetween
    : BETWEEN frameStart AND frameEnd
    ;

specialFunction
    : groupConcatFunction | windowFunction | castFunction | convertFunction | positionFunction | substringFunction | extractFunction
    | charFunction | trimFunction | weightStringFunction | valuesFunction | currentUserFunction
    ;

currentUserFunction
    : CURRENT USER (LP_ RP_)?
    ;

groupConcatFunction
    : GROUP CONCAT LP_ distinct? (expr (COMMA_ expr)* | ASTERISK_)? (orderByClause)? (SEPARATOR expr)? RP_
    ;

windowFunction
    : funcName simpleExpr? LP_ expr? (COMMA_ simpleExpr)? leadLagInfo? RP_  (FROM (FIRST | LAST))? nullTreatment? windowingClause?
    ;
funcName
    : ROW NUMBER
    | RANK
    | DENSE RANK
    | CUME DIST
    | PERCENT RANK
    | NTILE
    | LEAD
    | LAG
    | FIRST VALUE
    | LAST VALUE
    | NTH VALUE
    ;
windowingClause
    : OVER (windowName=identifier | windowSpecification)
    ;

leadLagInfo
    : COMMA_ (NUMBER_ | QUESTION_) (COMMA_ expr)?
    ;

nullTreatment
    : (RESPECT | IGNORE) NULLS
    ;



castFunction
    : CAST LP_ expr AS castType ARRAY? RP_
    | CAST LP_ expr AT TIME ZONE expr AS DATETIME typeDatetimePrecision? RP_
    ;

convertFunction
    : CONVERT LP_ expr COMMA_ castType RP_
    | CONVERT LP_ expr USING charsetName RP_
    ;

castType
    :  castTypeName fieldLength? charsetWithOptBinary? typeDatetimePrecision? precision?
    ;
castTypeName
    : BINARY
    | CHAR
    | NCHAR
    | NATIONAL CHAR
    | SIGNED
    | SIGNED INT
    | SIGNED INTEGER
    | UNSIGNED
    | UNSIGNED INT
    | UNSIGNED INTEGER
    | DATE
    | TIME
    | DATETIME
    | DECIMAL
    | JSON
    | REAL
    | DOUBLE PRECISION
    | FLOAT
    ;

positionFunction
    : POSITION LP_ expr IN expr RP_
    ;

substringFunction
    : (SUBSTRING | SUBSTR) LP_ expr FROM NUMBER_ (FOR NUMBER_)? RP_
    | (SUBSTRING | SUBSTR) LP_ expr COMMA_ NUMBER_ (COMMA_ NUMBER_)? RP_
    ;

extractFunction
    : EXTRACT LP_ identifier FROM expr RP_
    ;

charFunction
    : CHAR LP_ expr (COMMA_ expr)* (USING charsetName)? RP_
    ;

trimFunction
    : TRIM LP_ ((LEADING | BOTH | TRAILING) expr? FROM)? expr RP_
    | TRIM LP_ (expr FROM)? expr RP_
    ;

valuesFunction
    : VALUES LP_ columnRefList RP_
    ;

weightStringFunction
    : WEIGHT STRING LP_ expr (AS dataType)? levelClause? RP_
    ;

levelClause
    : LEVEL (levelInWeightListElement (COMMA_ levelInWeightListElement)* | NUMBER_ MINUS_ NUMBER_)
    ;

levelInWeightListElement
    : NUMBER_ direction? REVERSE?
    ;

regularFunction
    : completeRegularFunction
    | shorthandRegularFunction
    ;

shorthandRegularFunction
    : CURRENT DATE
    | CURRENT TIME (LP_ NUMBER_? RP_)?
    | CURRENT TIMESTAMP
    | LAST DAY
    | LOCALTIME
    | LOCALTIMESTAMP
    ;

completeRegularFunction
    : regularFunctionName (LP_ (expr (COMMA_ expr)* | ASTERISK_)? RP_)
    ;

regularFunctionName
    : IF | LOCALTIME | LOCALTIMESTAMP | REPLACE | INSERT | INTERVAL | MOD
    | DATABASE | SCHEMA | LEFT | RIGHT | DATE | DAY | GEOMETRYCOLLECTION | REPEAT
    | LINESTRING | MULTILINESTRING | MULTIPOINT | MULTIPOLYGON | POINT | POLYGON
    | TIME | TIMESTAMP | (TIMESTAMP ADD) | (TIMESTAMP DIFF) | DATE | (CURRENT TIMESTAMP)
    | (CURRENT DATE) | (CURRENT TIME) | (UTC TIMESTAMP) | identifier
    ;

matchExpression
    : MATCH (columnRefList | LP_ columnRefList RP_ ) AGAINST LP_ expr matchSearchModifier? RP_
    ;

matchSearchModifier
    : IN NATURAL LANGUAGE MODE | IN NATURAL LANGUAGE MODE WITH QUERY EXPANSION | IN BOOLEAN MODE | WITH QUERY EXPANSION
    ;

caseExpression
    : CASE simpleExpr? caseWhen+ caseElse? END
    ;


caseWhen
    : WHEN expr THEN expr
    ;

caseElse
    : ELSE expr
    ;

intervalExpression
    : INTERVAL intervalValue
    ;

intervalValue
    : expr intervalUnit
    ;

intervalUnit
    : MICROSECOND | SECOND | MINUTE | HOUR | DAY | WEEK | MONTH
    | QUARTER | YEAR | (SECOND MICROSECOND) | (MINUTE MICROSECOND) | (MINUTE SECOND) | (HOUR MICROSECOND) | (HOUR SECOND)
    | (HOUR MINUTE) | (DAY MICROSECOND) | (DAY SECOND) | (DAY MINUTE) | (DAY HOUR) | (YEAR MONTH)
    ;


orderByClause
    : ORDER BY orderByItem (COMMA_ orderByItem)*
    ;

orderByItem
    : (numberLiterals | expr) direction?
    ;

dataType
    : dataTypeName fieldLength? charsetWithOptBinary? precision? fieldOptions? typeDatetimePrecision?
    | (ENUM | SET) stringList charsetWithOptBinary?
    ;
dataTypeName
    : INTEGER | INT | TINYINT | SMALLINT | MIDDLEINT | MEDIUMINT | BIGINT
    | REAL
    | DOUBLE PRECISION?
    | FLOAT
    | DECIMAL
    | NUMERIC
    | FIXED
    | BIT
    | BOOL
    | BOOLEAN
    | CHAR
    | NCHAR
    | NATIONAL CHAR
    | SIGNED
    | SIGNED INT
    | SIGNED INTEGER
    | BINARY
    | CHAR VARYING
    | CHARACTER VARYING
    | VARCHAR
    | NATIONAL VARCHAR
    | NVARCHAR
    | NCHAR VARCHAR
    | NATIONAL CHAR VARYING
    | NCHAR VARYING
    | VARBINARY
    | YEAR
    | DATE
    | TIME
    | UNSIGNED
    | UNSIGNED INT
    | UNSIGNED INTEGER
    | TIMESTAMP
    | DATETIME
    | TINYBLOB
    | BLOB
    | MEDIUMBLOB
    | LONGBLOB
    | LONG VARBINARY
    | LONG CHAR VARYING
    | LONG VARCHAR
    | TINYTEXT
    | TEXT
    | MEDIUMTEXT
    | LONGTEXT
    | ENUM
    | SET
    | SERIAL
    | JSON
    | GEOMETRY
    | GEOMCOLLECTION
    | GEOMETRYCOLLECTION
    | POINT
    | MULTIPOINT
    | LINESTRING
    | MULTILINESTRING
    | POLYGON
    | MULTIPOLYGON
    | STRING
    ;
stringList
    : LP_ textString (COMMA_ textString)* RP_
    ;

textString
    : string_
    | HEX_DIGIT_
    | BIT_NUM_
    ;

fieldOptions
    : (UNSIGNED | SIGNED | ZEROFILL)+
    ;

precision
    : LP_ NUMBER_ COMMA_ NUMBER_ RP_
    ;

typeDatetimePrecision
    : LP_ NUMBER_ RP_
    ;

charsetWithOptBinary
    : ascii
    | unicode
    | BYTE
    | charset charsetName BINARY?
    | BINARY (charset charsetName)?
    ;

ascii
    : ASCII BINARY?
    | BINARY ASCII
    ;

unicode
    : UNICODE BINARY?
    | BINARY UNICODE
    ;

charset
    : (CHAR | CHARACTER) SET
    | CHARSET
    ;


direction
    : ASC | DESC
    ;



fieldLength
    : LP_ length=NUMBER_ RP_
    ;


collateClause
    : COLLATE (collationName | parameterMarker)
    ;


ifNotExists
    : IF NOT EXISTS
    ;

ifExists
    : IF EXISTS
    ;




combineOption
    : ALL | DISTINCT
    ;


parameterMarker
    : QUESTION_
    ;
unreservedWord
    : ACTION
    | ACCOUNT
    | ACTIVE
//    | ADDDATE
    | ADMIN
    | AFTER
    | AGAINST
    | AGGREGATE
    | ALGORITHM
    | ALWAYS
    | ANY
    | ARRAY
    | AT
    | ATTRIBUTE
    | AVG
    | BACKUP
    | BINLOG
    | BIT
    | BLOCK
    | BOOLEAN
    | BOOL
    | BTREE
    | BUCKETS
    | CASCADED
    | CATALOG
    | CHAIN
    | CHANGED
    | CHANNEL
    | CIPHER
    | CLIENT
    | CLOSE
    | COALESCE
    | CODE
    | COLLATION
    | COLUMNS
    | COLUMN FORMAT
    | COLUMN NAME
    | COMMITTED
    | COMPACT
    | COMPLETION
    | COMPONENT
    | COMPRESSED
    | COMPRESSION
    | CONCURRENT
    | CONNECTION
    | CONSISTENT
    | CONSTRAINT CATALOG
    | CONSTRAINT NAME
    | CONSTRAINT SCHEMA
    | CONTEXT
    | CPU
    | CREATE
    | CURRENT
    | CURSOR NAME
    | DATAFILE
    | DATA
    | DATETIME
    | DATE
    | DAY
    | DAY MINUTE
    | DEFAULT AUTH
    | DEFINER
    | DEFINITION
    | DELAY KEY WRITE
    | DESCRIPTION
    | DIAGNOSTICS
    | DIRECTORY
    | DISABLE
    | DISCARD
    | DISK
    | DUMPFILE
    | DUPLICATE
    | DYNAMIC
    | ENABLE
    | ENCRYPTION
    | ENDS
    | ENFORCED
    | ENGINES
    | ENGINE
    | ENGINE ATTRIBUTE
    | ENUM
    | ERRORS
    | ERROR
    | ESCAPE
    | EVENTS
    | EVERY
    | EXCHANGE
    | EXCLUDE
    | EXPANSION
    | EXPIRE
    | EXPORT
    | EXTENDED
    | EXTENT SIZE
    | FAILED LOGIN ATTEMPTS
    | FAST
    | FAULTS
    | FILE BLOCK SIZE
    | FILTER
    | FIRST
    | FIXED
    | FOLLOWING
    | FORMAT
    | FOUND
    | FULL
    | GENERAL
    | GEOMETRYCOLLECTION
    | GEOMETRY
    | GET FORMAT
    | GET MASTER PUBLIC KEY
    | GRANTS
    | GROUP REPLICATION
    | HASH
    | HISTOGRAM
    | HISTORY
    | HOSTS
    | HOST
    | HOUR
    | IDENTIFIED
    | IGNORE SERVER IDS
    | INACTIVE
    | INDEXES
    | INITIAL SIZE
    | INSERT METHOD
    | INSTANCE
    | INVISIBLE
    | INVOKER
    | IO
    | IPC
    | ISOLATION
    | ISSUER
    | JSON
    | JSON VALUE
    | KEY
    | KEY BLOCK SIZE
    | LAST
    | LEAVES
    | LESS
    | LEVEL
    | LINESTRING
    | LIST
    | LOCKED
    | LOCKS
    | LOGFILE
    | LOGS
    | MASTER AUTO POSITION
    | MASTER COMPRESSION ALGORITHM
    | MASTER CONNECT RETRY
    | MASTER DELAY
    | MASTER HEARTBEAT PERIOD
    | MASTER HOST
    | NETWORK NAMESPACE
    | MASTER LOG FILE
    | MASTER LOG POS
    | MASTER PASSWORD
    | MASTER PORT
    | MASTER PUBLIC KEY PATH
    | MASTER RETRY COUNT
    | MASTER SERVER ID
    | MASTER SSL CAPATH
    | MASTER SSL CA
    | MASTER SSL CERT
    | MASTER SSL CIPHER
    | MASTER SSL CRLPATH
    | MASTER SSL CRL
    | MASTER SSL KEY
    | MASTER SSL
    | MASTER
    | MASTER TLS CIPHERSUITES
    | MASTER TLS VERSION
    | MASTER USER
    | MASTER ZSTD COMPRESSION LEVEL
    | MAX CONNECTIONS PER HOUR
    | MAX QUERIES PER HOUR
    | MAX ROWS
    | MAX SIZE
    | MAX UPDATES PER HOUR
    | MAX USER CONNECTIONS
    | MEDIUM
    | MEMBER
    | MEMORY
    | MERGE
    | MESSAGE TEXT
    | MICROSECOND
    | MIGRATE
    | MINUTE
    | MIN ROWS
    | MODE
    | MODIFY
    | MONTH
    | MULTILINESTRING
    | MULTIPOINT
    | MULTIPOLYGON
    | MUTEX
    | MYSQL ERRNO
    | NAMES
    | NAME
    | NATIONAL
    | NCHAR
    | NDBCLUSTER
    | NESTED
    | NEVER
    | NEW
    | NEXT
    | NODEGROUP
    | NOWAIT
    | NO WAIT
    | NULLS
    | NUMBER
    | NVARCHAR
    | OFF
    | OFFSET
    | OJ
    | OLD
    | ONE
    | ONLY
    | OPEN
    | OPTIONAL
    | OPTIONS
    | ORDINALITY
    | ORGANIZATION
    | OTHERS
    | OWNER
    | PACK KEYS
    | PAGE
    | PARSER
    | PARTIAL
    | PARTITIONING
    | PARTITIONS
    | PASSWORD
    | PASSWORD LOCK TIME
    | PATH
    | PHASE
    | PLUGINS
    | PLUGIN DIR
    | PLUGIN
    | POINT
    | POLYGON
    | PORT
    | PRECEDING
    | PRESERVE
    | PREV
    | PRIVILEGES
    | PRIVILEGE CHECKS USER
    | PROCESSLIST
    | PROFILES
    | PROFILE
    | QUARTER
    | QUERY
    | QUICK
    | RANDOM
    | READ ONLY
    | REBUILD
    | RECOVER
    | REDO BUFFER SIZE
    | REDUNDANT
    | REFERENCE
    | RELAY
    | RELAYLOG
    | RELAY LOG FILE
    | RELAY LOG POS
    | RELAY THREAD
    | REMOVE
    | REORGANIZE
    | REPEATABLE
    | REPLICATE DO DB
    | REPLICATE DO TABLE
    | REPLICATE IGNORE DB
    | REPLICATE IGNORE TABLE
    | REPLICATE REWRITE DB
    | REPLICATE WILD DO TABLE
    | REPLICATE WILD IGNORE TABLE
    | REQUIRE ROW FORMAT
//    | REQUIRE TABLE PRIMARY KEY CHECK
    | USER RESOURCES
    | RESPECT
    | RESTORE
    | RESUME
    | RETAIN
    | RETURNED SQLSTATE
    | RETURNING
    | RETURNS
    | REUSE
    | REVERSE
    | ROLE
    | ROLLUP
    | ROTATE
    | ROUTINE
    | ROW COUNT
    | ROW FORMAT
    | RTREE
    | SCHEDULE
    | SCHEMA NAME
    | SECONDARY ENGINE
    | SECONDARY ENGINE ATTRIBUTE
    | SECONDARY LOAD
    | SECONDARY
    | SECONDARY UNLOAD
    | SECOND
    | SECURITY
    | SERIALIZABLE
    | SERIAL
    | SERVER
    | SHARE
    | SIMPLE
//    | SKIP
    | SLOW
    | SNAPSHOT
    | SOCKET
    | SONAME
    | SOUNDS
    | SOURCE
    | SQL AFTER GTIDS
    | SQL AFTER MTS GAPS
    | SQL BEFORE GTIDS
    | SQL BUFFER RESULT
    | SQL NO CACHE
    | SQL THREAD
    | SRID
    | STACKED
    | STARTS
    | STATS AUTO RECALC
    | STATS PERSISTENT
    | STATS SAMPLE PAGES
    | STATUS
    | STORAGE
    | STREAM
    | STRING
    | SUBCLASS ORIGIN
//    | SUBDATE
    | SUBJECT
    | SUBPARTITIONS
    | SUBPARTITION
    | SUSPEND
    | SWAPS
    | SWITCHES
    | SYSTEM
    | TABLE
    | TABLES
    | TABLESPACE
    | TABLE CHECKSUM
    | TABLE NAME
    | TEMPORARY
    | TEMPTABLE
    | TEXT
    | THAN
    | THREAD PRIORITY
    | TIES
    | TIMESTAMP ADD
    | TIMESTAMP DIFF
    | TIMESTAMP
    | TIME
    | TLS
    | TRANSACTION
    | TRIGGERS
    | TYPES
    | TYPE
    | UNBOUNDED
    | UNCOMMITTED
    | UNDEFINED
    | UNDOFILE
    | UNDO BUFFER SIZE
    | UNKNOWN
    | UNTIL
    | UPGRADE
    | USER
    | USE FRM
    | VALIDATION
    | VALUE
    | VARIABLES
    | VCPU
    | VIEW
    | VISIBLE
    | WAIT
    | WARNINGS
    | WEEK
    | WEIGHT STRING
    | WITHOUT
    | WORK
    | WRAPPER
    | X509
    | XID
    | XML
    | YEAR
    | YEAR MONTH
    | REDUCE
    | DEFAULT
    | SEQUENCEFILE
    | OPTIMIZE
    | DB
    ;

identifierKeywordsAmbiguous1RolesAndLabels
    : EXECUTE
    | RESTART
    | SHUTDOWN
    ;

identifierKeywordsAmbiguous2Labels
    : ASCII
    | BEGIN
    | BYTE
    | CACHE
    | CHARSET
    | CHECKSUM
    | CLONE
    | COMMENT
    | COMMIT
    | CONTAINS
    | DEALLOCATE
    | DO
    | END
    | FLUSH
    | FOLLOWS
    | HANDLER
    | HELP
    | IMPORT
    | INSTALL
    | LANGUAGE
    | NO
    | PRECEDES
    | PREPARE
    | REPAIR
    | RESET
    | ROLLBACK
    | SAVEPOINT
    | SIGNED
    | SLAVE
    | START
    | STOP
    | TRUNCATE
    | UNICODE
    | UNINSTALL
    | XA
    | A
    | ID
    | E
    | G
    | T
    | P
    | K
    | H
    | M
    ;

identifierKeywordsAmbiguous3Roles
    : EVENT
    | FILE
    | NONE
    | PROCESS
    | PROXY
    | RELOAD
    | REPLICATION
    | RESOURCE
    | SUPER
    ;

identifierKeywordsAmbiguous4SystemVariables
    : GLOBAL
    | LOCAL
    | PERSIST
    | PERSIST ONLY
    | SESSION
    ;
