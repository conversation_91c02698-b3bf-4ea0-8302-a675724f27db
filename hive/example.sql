-- Simple distinct counting examples using HLL
create table sketch_input (id int, category char(1))
    STORED AS ORC
    TBLPROPERTIES ('transactional'='true');

insert into table sketch_input values
    (1,'a'),(1, 'a'), (2, 'a'), (3, 'a'), (4, 'a'), (5, 'a'), (6, 'a'), (7, 'a'), (8, 'a'), (9, 'a'), (10, 'a'),
    (6,'b'),(6, 'b'), (7, 'b'), (8, 'b'), (9, 'b'), (10, 'b'), (11, 'b'), (12, 'b'), (13, 'b'), (14, 'b'), (15, 'b')
;
-- Use HLL to compute distinct values using an intermediate table
-- build sketches per category
create temporary table sketch_intermediate (category char(1), sketch binary);
insert into sketch_intermediate select category, ds_hll_sketch(id) from sketch_input group by category;

-- get unique count estimates per category
select category, ds_hll_estimate(sketch) from sketch_intermediate;

-- union sketches across categories and get overall unique count estimate
select ds_hll_estimate(ds_hll_union(sketch)) from sketch_intermediate;
-- Use HLL to compute distinct values without intermediate table
select category, ds_hll_estimate(ds_hll_sketch(id)) from sketch_input group by category;
select ds_hll_estimate(ds_hll_sketch(id)) from sketch_input;
-- Use HLL to compute distinct values transparently thru BI mode
set hive.optimize.bi.enabled=true;
select category,count(distinct id) from sketch_input group by category;
select count(distinct id) from sketch_input;
-- Use HLL to compute distinct values transparently thru BI mode - while utilizing a Materialized View to store the intermediate sketches.
-- create an MV to store precomputed HLL values
create  materialized view mv_1 as
select category, ds_hll_sketch(id) from sketch_input group by category;

set hive.optimize.bi.enabled=true;
select category,count(distinct id) from sketch_input group by category;
select count(distinct id) from sketch_input;

---- Data Connector for Hive and Hive-like engines
--1. Create a connector first.
CREATE CONNECTOR hiveserver_connector TYPE 'hivejdbc' URL 'jdbc:hive2://<maskedhost>:10000'   
     WITH DCPROPERTIES ("hive.sql.dbcp.username"="hive", "hive.sql.dbcp.password"="hive");  
  

--2. Create a database of type REMOTE in hive using the connector from Step 1. This maps a remote database named “*default*” to a hive database named “hiveserver_remote” in hive.
         CREATE REMOTE DATABASE hiveserver_remote USING hiveserver_connector   
         WITH DBPROPERTIES ("connector.remoteDbName"="default");

-- 3. Use the tables in REMOTE database much like the JDBC-storagehandler based tables in hive. One big difference
--      is that the metadata for these tables are never persisted in hive. Currently, create/alter/drop table DDLs
--      are not supported in REMOTE databases.
USE hiveserver_remote;
describe formatted test_emr_tbl;
-- 4. Offload the remote table to local cluster, run CTAS (example below pulls in all the data into the local table,
--       but you can pull in select columns and rows by applying predicates)
create table default.emr_clone as select * from test_emr_tbl;
select count(*) from default.emr_clone;


-- 5. To fetch data from the remote tables, run SELECT queries using column spec and predicates as you would
--       normally with any SQL tables.
select * from test_emr_tbl where tblkey > 1;

-- 6. Join with local hive tables, run SELECT queries joining multiple tables (local or remote) as you would
--   normally with any SQL tables.

--Number of implicit buckets
-- It is possible to set the desired number of implicit buckects during a rebalance compaction.

-- SQL example:
ALTER TABLE table_name COMPACT 'REBALANCE' CLUSTERED INTO n BUCKETS;

ALTER TABLE table_name COMPACT 'REBALANCE' ORDER BY column_name DESC;

-- Exchange Partition
--Create two tables, partitioned by ds
CREATE TABLE T1(a string, b string) PARTITIONED BY (ds string);
CREATE TABLE T2(a string, b string) PARTITIONED BY (ds string);
ALTER TABLE T1 ADD PARTITION (ds='1');

--Move partition from T1 to T2
ALTER TABLE T2 EXCHANGE PARTITION (ds='1') WITH TABLE T1;

--Create two tables with multiple partition columns.
CREATE TABLE T1 (a string, b string) PARTITIONED BY (ds string, hr string);
CREATE TABLE T2 (a string, b string) PARTITIONED BY (ds string, hr string);
ALTER TABLE T1 ADD PARTITION (ds = '1', hr = '00');
ALTER TABLE T1 ADD PARTITION (ds = '1', hr = '01');
ALTER TABLE T1 ADD PARTITION (ds = '1', hr = '03');

--Alter the table, moving all the three partitions data where ds='1' from table T1 to table T2 (ds=1)
ALTER TABLE T2 EXCHANGE PARTITION (ds='1') WITH TABLE T1;


-- Create two tables with multiple partition columns.
CREATE TABLE T1 (a int) PARTITIONED BY (d1 int, d2 int);
CREATE TABLE T2 (a int) PARTITIONED BY (d1 int, d2 int);
ALTER TABLE T1 ADD PARTITION (d1=1, d2=2);

-- Alter the table, moving partition data d1=1, d2=2 from table T1 to table T2
ALTER TABLE T2 EXCHANGE PARTITION (d1 = 1, d2 = 2) WITH TABLE T1;

CREATE TABLE tab1 (col1 int, col2 int) PARTITIONED BY (col3 int) STORED AS ORC;
LOAD DATA LOCAL INPATH 'filepath' INTO TABLE tab1;


FROM page_view_stg pvs
INSERT OVERWRITE TABLE page_view PARTITION(dt='2008-06-08', country)
SELECT pvs.viewTime, pvs.userid, pvs.page_url, pvs.referrer_url, null, null, pvs.ip, pvs.cnt;



CREATE TABLE students (name VARCHAR(64), age INT, gpa DECIMAL(3, 2))
    CLUSTERED BY (age) INTO 2 BUCKETS STORED AS ORC;

INSERT INTO TABLE students
    VALUES ('fred flintstone', 35, 1.28), ('barney rubble', 32, 2.32);

CREATE TABLE pageviews (userid VARCHAR(64), link STRING, came_from STRING)
    PARTITIONED BY (datestamp STRING) CLUSTERED BY (userid) INTO 256 BUCKETS STORED AS ORC;

INSERT INTO TABLE pageviews PARTITION (datestamp = '2014-09-23')
VALUES ('jsmith', 'mail.com', 'sports.com'), ('jdoe', 'mail.com', null);

INSERT INTO TABLE pageviews PARTITION (datestamp)
VALUES ('tjohnson', 'sports.com', 'finance.com', '2014-09-23'), ('tlee', 'finance.com', null, '2014-09-21');

INSERT INTO TABLE pageviews
    VALUES ('tjohnson', 'sports.com', 'finance.com', '2014-09-23'), ('tlee', 'finance.com', null, '2014-09-21');