LOAD DATA LOCAL INPATH "filepath" INTO TABLE tab1;
INSERT OVERWRITE TABLE page_view PARTITION(dt='2008-06-08', country) (a,b,c)
SELECT pvs.viewTime, pvs.userid, pvs.page_url, pvs.referrer_url, null, null, pvs.ip, pvs.cnt FROM page_view_stg pvs;

INSERT OVERWRITE LOCAL DIRECTORY 'adsf'
  ROW FORMAT DELIMITED STORED AS file_format
SELECT * from ttt;

FROM page_view_stg pvs
INSERT OVERWRITE TABLE page_view PARTITION(dt='2008-06-08', country)
SELECT pvs.viewTime, pvs.userid, pvs.page_url, pvs.referrer_url, null, null, pvs.ip, pvs.cnt;

CREATE TABLE students (name <PERSON><PERSON><PERSON><PERSON>(64), age INT, gpa DECIMAL(3, 2))
    CLUSTERED BY (age) INTO 2 BUCKETS STORED AS ORC;

INSERT INTO TABLE students
    VALUES ('fred flintstone', 35, 1.28), ('barney rubble', 32, 2.32);

CREATE TABLE pageviews (userid VARCHAR(64), link STRING, came_from STRING)
    PARTITIONED BY (datestamp STRING) CLUSTERED BY (userid) INTO 256 BUCKETS STORED AS ORC;

INSERT INTO TABLE pageviews PARTITION (datestamp = '2014-09-23')
VALUES ('jsmith', 'mail.com', 'sports.com'), ('jdoe', 'mail.com', null);

INSERT INTO TABLE pageviews PARTITION (datestamp)
VALUES ('tjohnson', 'sports.com', 'finance.com', '2014-09-23'), ('tlee', 'finance.com', null, '2014-09-21');

INSERT INTO TABLE pageviews
    VALUES ('tjohnson', 'sports.com', 'finance.com', '2014-09-23'), ('tlee', 'finance.com', null, '2014-09-21');

UPDATE tablename SET sdwe = "qewrwq";
SELECT A + - B * C ^ D + E | F OR G AND H || I - (SELECT COUNT(*) FROM DB.TB) OR J BETWEEN K AND L OR M IS TRUE FROM DB.TB1;

MERGE INTO target_table AS T
    USING TABLE (VALUES
    (1, 'Alice', 'active'),
            (2, 'Bob', 'inactive')
          ) AS S (id, name, status)
    ON t.id = s.id
    WHEN MATCHED AND S.status = 'active' THEN
        UPDATE SET T.name = S.name, T.updated_at = CURRENT_TIMESTAMP
    WHEN MATCHED AND S.status = 'inactive' THEN
        DELETE
    WHEN NOT MATCHED AND S.status = 'new' THEN
        INSERT VALUES (S.id, S.name, CURRENT_TIMESTAMP);

SELECT id, CAST(DS_KLL_RANK(t2.sketch, idVal) AS DOUBLE)
FROM (SELECT id, CAST(COALESCE(CAST(id AS FLOAT), 340282346638528860000000000000000000000) AS FLOAT) AS idVal FROM sketch_input) AS t,
     (SELECT DS_KLL_SKETCH(CAST(`id` AS FLOAT)) AS `sketch` FROM sketch_input) AS t2;

select id,
       case when ceil(ds_kll_cdf(ds, CAST(id AS FLOAT) )[0]*4) < 1 then 1 else ceil(ds_kll_cdf(ds, CAST(id AS FLOAT) )[0]*4) end
from sketch_input
         join ( select ds_kll_sketch(cast(id as float)) as ds from sketch_input ) q
order by id;

select id,
       rank() over (order by id),
        case when ds_kll_n(ds) < (ceil(ds_kll_rank(ds, CAST(id AS FLOAT) )*ds_kll_n(ds))+1) then ds_kll_n(ds) else (ceil(ds_kll_rank(ds, CAST(id AS FLOAT) )*ds_kll_n(ds))+1) end;

FROM pv_users 
  INSERT OVERWRITE TABLE pv_gender_sum
SELECT pv_users.gender, count(DISTINCT pv_users.userid)
    GROUP BY pv_users.gender
INSERT OVERWRITE DIRECTORY '/user/facebook/tmp/pv_age_sum'
SELECT pv_users.age, count(DISTINCT pv_users.userid)
    GROUP BY pv_users.age;



EXPLAIN CBO
WITH customer_total_return AS
(SELECT sr_customer_sk AS ctr_customer_sk,
  sr_store_sk AS ctr_store_sk,
  SUM(SR_FEE) AS ctr_total_return
  FROM store_returns, date_dim
  WHERE sr_returned_date_sk = d_date_sk
    AND d_year =2000
  GROUP BY sr_customer_sk, sr_store_sk)
SELECT c_customer_id
FROM customer_total_return ctr1, store, customer
WHERE ctr1.ctr_total_return > (SELECT AVG(ctr_total_return)*1.2
FROM customer_total_return ctr2
WHERE ctr1.ctr_store_sk = ctr2.ctr_store_sk)
  AND s_store_sk = ctr1.ctr_store_sk
  AND s_state = 'NM'
  AND ctr1.ctr_customer_sk = c_customer_sk
ORDER BY c_customer_id
LIMIT 100;


export table employee partition (emp_country="in", emp_state="ka") to 'hdfs_exports_location/employee';
import from 'hdfs_exports_locationemployee';


select cast(dt as string format 'DD-MM-YYYY');
select cast('01-05-2017' as date format 'DD-MM-YYYY');

Select count(*) cnt
From store_sales ss
         join household_demographics hd on (ss.ss_hdemo_sk = hd.hd_demo_sk)
         join time_dim t on (ss.ss_sold_time_sk = t.t_time_sk)
         join store s on (s.s_store_sk = ss.ss_store_sk)
Where
    t.t_hour = 8
    t.t_minute >= 30
     hd.hd_dep_count = 2
order by cnt;