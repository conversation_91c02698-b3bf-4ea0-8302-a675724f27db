parser grammar KingbaseParser;

options {
    tokenVocab = KingbaseLexer;
}

root
    : (select

    ) SEMI_? SLASH_? EOF
    ;

select
    : selectSubquery forUpdateClause?
    ;
selectSubquery
    : selectSubquery combineType selectSubquery | ((queryBlock | parenthesisSelectSubquery) pivotClause? orderByClause? rowLimitingClause)
    ;
parenthesisSelectSubquery
    : LP_ selectSubquery RP_
    ;
orderByClause
    : ORDER SIBLINGS? BY orderByItem (COMMA_ orderByItem)*
    ;
orderByItem
    : (columnName | numberLiterals | expr) (ASC | DESC)? (NULLS FIRST | NULLS LAST)?
    ;
rowLimitingClause
    : (OFFSET offset (ROW | ROWS))? (FETCH (FIRST | NEXT) (rowcount | percent PERCENT)? (ROW | ROWS) (ONLY | WITH TIES))?
    ;
pivotClause
    : PIVOT XML?
    LP_ aggregationFunction (AS? alias)? (COMMA_ aggregationFunction (AS? alias)?)* pivotForClause pivotInClause RP_
    ;
aggregationFunction
    : aggregationFunctionName LP_ (((DISTINCT | ALL)? expr (COMMA_ expr)*) | ASTERISK_) (COMMA_ stringLiterals)?
     listaggOverflowClause? orderByClause? RP_ (WITHIN GROUP LP_ orderByClause RP_)? keepClause? overClause? overClause?
    ;
overClause
    : OVER LP_ analyticClause RP_
    ;
analyticClause
    : queryPartitionClause? (orderByClause windowingClause?)?
    ;
windowingClause
    : (ROWS | RANGE) ((BETWEEN (UNBOUNDED PRECEDING | CURRENT ROW | expr (PRECEDING | FOLLOWING)) AND (UNBOUNDED FOLLOWING | CURRENT ROW | expr (PRECEDING | FOLLOWING)))
    | (UNBOUNDED PRECEDING | CURRENT ROW | expr PRECEDING))
    ;
queryPartitionClause
    : PARTITION BY (exprs | exprList)
    ;
listaggOverflowClause
    : ON OVERFLOW (ERROR | (TRUNCATE stringLiterals? ((WITH | WITHOUT) COUNT)?))
    ;
keepClause
    : KEEP LP_ DENSE_RANK (FIRST | LAST) orderByClause RP_ overClause?
    ;


pivotForClause
    : FOR (columnName | columnNames)
    ;

pivotInClause
    : IN LP_ (pivotInClauseExpr (COMMA_ pivotInClauseExpr)*
    | selectSubquery
    | ANY (COMMA_ ANY)*) RP_
    ;

pivotInClauseExpr
    : (expr | exprList) (AS? alias)?
    ;

combineType
    : UNION ALL? | INTERSECT | MINUS
    ;
queryBlock
    : withClause? SELECT hint? duplicateSpecification? selectList (intoClause | bulkCollectIntoClause)? selectFromClause whereClause? hierarchicalQueryClause? groupByClause? modelClause?
    ;
modelClause
    : MODEL cellReferenceOptions? returnRowsClause? referenceModel* mainModel
    ;
cellReferenceOptions
    : (IGNORE | KEEP) NAV | ((IGNORE | KEEP) NAV)? UNIQUE (DIMENSION | SINGLE REFERENCE)
    ;

returnRowsClause
    : RETURN (UPDATED | ALL) ROWS
    ;

referenceModel
    : REFERENCE referenceModelName ON LP_ selectSubquery RP_ modelColumnClauses cellReferenceOptions?
    ;

mainModel
    : (MAIN mainModelName)? modelColumnClauses cellReferenceOptions? modelRulesClause
    ;

modelColumnClauses
    : (PARTITION BY LP_ expr alias? (COMMA_ expr alias?)* RP_)?
    DIMENSION BY LP_ expr alias? (COMMA_ expr alias?)* RP_ MEASURES LP_ expr alias? (COMMA_ expr alias?)* RP_
    ;

modelRulesClause
    : (RULES (UPDATE | UPSERT ALL?)? ((AUTOMATIC | SEQUENTIAL) ORDER)? modelIterateClause?)?
    LP_ (UPDATE | UPSERT ALL?)? cellAssignment orderByClause? EQ_ modelExpr (COMMA_ (UPDATE | UPSERT ALL?)? cellAssignment orderByClause? EQ_ modelExpr)* RP_
    ;
cellAssignment
    : measureColumn LBT_ (((condition | expr | singleColumnForLoop) (COMMA_ (condition | expr | singleColumnForLoop))*) | multiColumnForLoop) RBT_
    ;
modelExpr
    : (numberLiterals ASTERISK_)? ((measureColumn LBT_ (condition | expr) (COMMA_ (condition | expr))* RBT_)
    | (aggregationFunction LBT_ (((condition | expr) (COMMA_ (condition | expr))*) | (singleColumnForLoop (COMMA_ singleColumnForLoop)*) | multiColumnForLoop) RBT_)
    | analyticFunction) ((PLUS_ | SLASH_) LP_? modelExpr* RP_? | ASTERISK_ numberLiterals (ASTERISK_ modelExpr)?)?
    | expr
    ;
analyticFunction
    : specifiedAnalyticFunctionName = (LEAD | LAG) ((LP_ expr leadLagInfo? RP_ respectOrIgnoreNulls?) | (LP_ expr respectOrIgnoreNulls? leadLagInfo? RP_)) overClause
    | specifiedAnalyticFunctionName = (NTILE | MEDIAN | RATIO_TO_REPORT) LP_ expr RP_ overClause?
    | specifiedAnalyticFunctionName = NTH_VALUE LP_ expr COMMA_ expr RP_ fromFirstOrLast? respectOrIgnoreNulls? overClause
    | specifiedAnalyticFunctionName = (PERCENTILE_CONT | PERCENTILE_DISC | LISTAGG) LP_ expr (COMMA_ expr)* RP_ WITHIN GROUP LP_ orderByClause RP_ overClause?
    | specifiedAnalyticFunctionName = (CORR | COVAR_POP | COVAR_SAMP) LP_ expr COMMA_ expr RP_ overClause?
    | specifiedAnalyticFunctionName = (PERCENT_RANK | RANK | ROW_NUMBER) LP_ RP_ overClause
    | analyticFunctionName LP_ dataType* RP_ overClause
    ;
fromFirstOrLast
    : FROM FIRST | FROM LAST
    ;
leadLagInfo
    : COMMA_ expr (COMMA_ expr)?
    ;
singleColumnForLoop
    : FOR dimensionColumn ((IN LP_ ((literals (COMMA_ literals)*) | selectSubquery) RP_)
    | ((LIKE pattern)? FROM literals TO literals (INCREMENT | DECREMENT) literals))
    ;

multiColumnForLoop
    : FOR LP_ dimensionColumn (COMMA_ dimensionColumn)* RP_ IN LP_ (selectSubquery
    | LP_ literals (COMMA_ literals)* RP_ (COMMA_ LP_ literals (COMMA_ literals)* RP_)*) RP_
    ;

modelIterateClause
    : ITERATE LP_ numberLiterals RP_ (UNTIL LP_ condition RP_)?
    ;
groupByClause
    : GROUP BY groupByItem (COMMA_ groupByItem)* havingClause?
    ;

groupByItem
    : rollupCubeClause | groupingSetsClause | expr
    ;

rollupCubeClause
    : (ROLLUP | CUBE) LP_ groupingExprList RP_
    ;

groupingSetsClause
    : GROUPING SETS LP_ (rollupCubeClause | groupingExprList) (COMMA_ (rollupCubeClause | groupingExprList))* RP_
    ;

groupingExprList
    : expressionList (COMMA_ expressionList)*
    ;
expressionList
    : exprs | LP_ expr? (COMMA_ expr?)* RP_
    ;
havingClause
    : HAVING expr
    ;
hierarchicalQueryClause
    : CONNECT BY NOCYCLE? expr (START WITH expr)?
    | START WITH expr CONNECT BY NOCYCLE? expr
    ;
whereClause
    : WHERE expr
    ;
selectFromClause
    : FROM fromClauseList
    ;
fromClauseList
    : fromClauseOption (COMMA_ fromClauseOption)*
    ;

fromClauseOption
    : joinClause
    | LP_ joinClause RP_
    | selectTableReference
    | inlineAnalyticView
    | (regularFunction | xmlTableFunction) alias?
    ;
xmlTableFunction
    : XMLTABLE LP_ (xmlNamespacesClause COMMA_)? STRING_ xmlTableOptions RP_
    ;
xmlTableOptions
    : xmlPassingClause? (RETURNING SEQUENCE BY REF)? (COLUMNS xmlTableColumn (COMMA_ xmlTableColumn)*)?
    ;
xmlPassingClause
    : PASSING (BY VALUE)? expr (AS alias)? (COMMA_ expr (AS alias)?)*
    ;
xmlTableColumn
    : columnName (FOR ORDINALITY | (dataType | XMLTYPE (LP_ SEQUENCE RP_ BY REF)?) (PATH STRING_)? (DEFAULT expr)?)
    ;
xmlNamespacesClause
    : XMLNAMESPACES LP_ (defaultString COMMA_)? (xmlNamespaceStringAsIdentifier | defaultString) (COMMA_ (xmlNamespaceStringAsIdentifier | defaultString))* RP_
    ;

xmlNamespaceStringAsIdentifier
    : STRING_ AS identifier
    ;

defaultString
    : DEFAULT STRING_
    ;
regularFunction
    : (owner DOT_)? regularFunctionName LP_ (expr (COMMA_ expr)* | ASTERISK_)? RP_
    ;


inlineAnalyticView
    : ANALYTIC VIEW LP_ subavClause RP_ (AS? alias)?
    ;
subavClause
    : USING baseAvName hierarchiesClause? filterClauses? addCalcsClause?
    ;
addCalcsClause
    : ADD MEASURES LP_ calcMeasClause (COMMA_ calcMeasClause)* RP_
    ;

calcMeasClause
    : measName AS LP_ calcMeasExpression RP_
    ;

calcMeasExpression
    : avExpression | expr
    ;

avExpression
    : avMeasExpression | avHierExpression
    ;
avMeasExpression
    : leadLagExpression
    | windowExpression
    | rankExpression
    | shareOfExpression
    | qdrExpression
    ;
qdrExpression
    : QUALIFY LP_ calcMeasExpression COMMA_ qualifier RP_
    ;

qualifier
    : hierarchyRef EQ_ memberExpression
    ;
memberExpression
    : levelMemberLiteral
    | hierNavigationExpression
    | CURRENT MEMBER
    | NULL
    | ALL
    ;
levelMemberLiteral
    : levelRef (posMemberKeys | namedMemberKeys)
    ;

posMemberKeys
    : SQ_ LBT_ SQ_ memberKeyExpr (COMMA_ memberKeyExpr)* SQ_ RBT_ SQ_
    ;

namedMemberKeys
    : SQ_ LBT_ SQ_ (attributeName EQ_ memberKeyExpr) (COMMA_ (attributeName EQ_ memberKeyExpr))* SQ_ RBT_ SQ_
    ;
hierNavigationExpression
    : hierAncestorExpression | hierParentExpression | hierLeadLagExpression
    ;

hierAncestorExpression
    : HIER_ANCESTOR LP_ memberExpression AT (LEVEL levelRef | DEPTH depthExpression) RP_
    ;

hierParentExpression
    : HIER_PARENT LP_ memberExpression RP_
    ;

hierLeadLagExpression
    : (HIER_LEAD | HIER_LAG) LP_ hierLeadLagClause RP_
    ;

hierLeadLagClause
    : memberExpression OFFSET offsetExpr
    (WITHIN ((LEVEL | PARENT) | (ACROSS ANCESTOR AT LEVEL levelRef (POSITION FROM (BEGINNING | END))?)))?
    ;
offsetExpr
    : expr | numberLiterals
    ;
avHierExpression
    : hierFunctionName LP_ memberExpression WITHIN HIERARCHY hierarchyRef RP_
    ;
rankExpression
    : rankFunctionName LP_ RP_ OVER LP_ rankClause RP_
    ;
rankClause
    : HIERARCHY hierarchyRef ORDER BY calcMeasOrderByClause (COMMA_ calcMeasOrderByClause)*
    (WITHIN (LEVEL | PARENT | ANCESTOR AT LEVEL levelRef))?
    ;

calcMeasOrderByClause
    : calcMeasExpression (ASC | DESC)? (NULLS (FIRST | LAST))?
    ;

shareOfExpression
    : SHARE_OF LP_ calcMeasExpression shareClause RP_
    ;

shareClause
    : HIERARCHY hierarchyRef (PARENT | LEVEL levelRef | MEMBER memberExpression)
    ;

leadLagExpression
    : leadLagFunctionName LP_ calcMeasExpression RP_ OVER LP_ leadLagClause RP_
    ;



leadLagClause
    : HIERARCHY hierarchyRef OFFSET offsetExpr
    ((WITHIN (LEVEL | PARENT)) | (ACROSS ANCESTOR AT LEVEL levelRef (POSITION FROM (BEGINNING | END))?))?
    ;

hierarchyRef
    : (alias DOT_)? alias
    ;

windowExpression
    : aggregationFunction OVER LP_ windowClause RP_
    ;

windowClause
    : HIERARCHY hierarchyRef BETWEEN (precedingBoundary | followingBoundary)
    (WITHIN (LEVEL | PARENT | ANCESTOR AT LEVEL levelRef))?
    ;
precedingBoundary
    : (UNBOUNDED PRECEDING | offsetExpr PRECEDING) AND (CURRENT MEMBER | offsetExpr (PRECEDING | FOLLOWING) | UNBOUNDED FOLLOWING)
    ;

followingBoundary
    : (CURRENT MEMBER | offsetExpr FOLLOWING) AND (offsetExpr FOLLOWING | UNBOUNDED FOLLOWING)
    ;
filterClauses
    : FILTER FACT LP_ filterClause (COMMA_ filterClause)* RP_
    ;

filterClause
    : (MEASURES | (alias DOT_)? alias) TO predicate
    ;
hierarchiesClause
    : HIERARCHIES LP_ ((alias DOT_)? alias (COMMA_ (alias DOT_)? alias)*)? RP_
    ;
selectTableReference
    : (queryTableExprClause | containersClause | shardsClause) alias?
    ;
shardsClause
    : SHARDS LP_ (tableName | viewName) RP_
    ;
containersClause
    : CONTAINERS LP_ (tableName | viewName) RP_
    ;
queryTableExprClause
    : (ONLY LP_ queryTableExpr RP_ | queryTableExpr) flashbackQueryClause? (pivotClause | unpivotClause | rowPatternClause)?
    ;
rowPatternClause
    : MATCH_RECOGNIZE LP_ rowPatternPartitionBy? rowPatternOrderBy? rowPatternMeasures?
    rowPatternRowsPerMatch? rowPatternSkipTo? PATTERN LP_ rowPattern RP_
    rowPatternSubsetClause? DEFINE rowPatternDefinitionList RP_
    ;

rowPatternPartitionBy
    : PARTITION BY columnNames
    ;

rowPatternOrderBy
    : ORDER BY columnNames
    ;

rowPatternMeasures
    : MEASURES rowPatternMeasureColumn (COMMA_ rowPatternMeasureColumn)*
    ;

rowPatternMeasureColumn
    : patternMeasExpression AS alias
    ;
patternMeasExpression
    : stringLiterals
    | numberLiterals
    | columnName
    | rowPatternRecFunc
    ;
rowPatternRowsPerMatch
    : (ONE ROW | ALL ROWS) PER MATCH
    ;

rowPatternSkipTo
    : AFTER MATCH SKIP_SYMBOL ((TO NEXT | PAST LAST) ROW
    | TO (FIRST | LAST)? variableName)
    ;

rowPattern
    : rowPatternTerm
    ;

rowPatternTerm
    : rowPatternFactor
    ;

rowPatternFactor
    : rowPatternPrimary rowPatternQuantifier?
    ;

rowPatternPrimary
    : variableName
    | DOLLAR_
    | CARET_
    | LP_ rowPattern? RP_
    | LBE_ MINUS_ rowPattern MINUS_ RBE_
    | rowPatternPermute
    ;

rowPatternPermute
    : PERMUTE LP_ rowPattern (COMMA_ rowPattern)* RP_
    ;

rowPatternQuantifier
    : ASTERISK_ QUESTION_?
    | PLUS_ QUESTION_?
    | QUESTION_ QUESTION_?
    | (LBE_ INTEGER_? COMMA_ INTEGER_? RBE_ QUESTION_?
    | LBE_ INTEGER_ RBE_)
    ;

rowPatternSubsetClause
    : SUBSET rowPatternSubsetItem (COMMA_ rowPatternSubsetItem)*
    ;

rowPatternSubsetItem
    : variableName EQ_ LP_ variableName (COMMA_ variableName)* RP_
    ;

rowPatternDefinitionList
    : rowPatternDefinition (COMMA_ rowPatternDefinition)*
    ;

rowPatternDefinition
    : variableName AS expr
    ;

rowPatternRecFunc
    : rowPatternClassifierFunc
    | rowPatternMatchNumFunc
    | rowPatternNavigationFunc
    | rowPatternAggregateFunc
    ;
rowPatternClassifierFunc
    : CLASSIFIER LP_ RP_
    ;

rowPatternMatchNumFunc
    : MATCH_NUMBER LP_ RP_
    ;

rowPatternNavigationFunc
    : rowPatternNavLogical
    | rowPatternNavPhysical
    | rowPatternNavCompound
    ;

rowPatternNavLogical
    : (RUNNING | FINAL)? (FIRST | LAST) LP_ expr (COMMA_ offset)? RP_
    ;

rowPatternNavPhysical
    : (PREV | NEXT) LP_ expr (COMMA_ offset)? RP_
    ;

rowPatternNavCompound
    : (PREV | NEXT) LP_ (RUNNING | FINAL)? (FIRST | LAST) LP_ expr (COMMA_ offset)? RP_ (COMMA_ offset)? RP_
    ;

rowPatternAggregateFunc
    : (RUNNING | FINAL)? aggregationFunction
    ;
unpivotClause
    : UNPIVOT ((INCLUDE | EXCLUDE) NULLS)? LP_ (columnName | columnNames) pivotForClause unpivotInClause RP_
    ;

unpivotInClause
    : IN LP_ unpivotInClauseExpr (COMMA_ unpivotInClauseExpr)* RP_
    ;
unpivotInClauseExpr
    : (columnName | columnNames) (AS (literals | LP_ literals (COMMA_ literals)* RP_))?
    ;
flashbackQueryClause
    : VERSIONS (BETWEEN (SCN | TIMESTAMP) | PERIOD FOR validTimeColumn BETWEEN) (expr | MINVALUE) AND (expr | MAXVALUE)
    | AS OF ((SCN | TIMESTAMP) (expr | intervalExprClause) | PERIOD FOR validTimeColumn expr)
    ;

intervalExprClause
    : LP_ SYSTIMESTAMP  (PLUS_ | MINUS_)  INTERVAL (INTEGER_ | STRING_) (HOUR | MINUTE | SECOND) RP_
    ;
queryTableExpr
    : queryTableExprSampleClause
    | queryName
    | lateralClause
    | tableCollectionExpr
    ;
tableCollectionExpr
    : TABLE LP_ collectionExpr RP_ (LP_ PLUS_ RP_)?
    ;

collectionExpr
    : selectSubquery | columnName | functionCall | expr
    ;
lateralClause
    : LATERAL? LP_ selectSubquery subqueryRestrictionClause? RP_
    ;
subqueryRestrictionClause
    : WITH (READ ONLY | CHECK OPTION) (CONSTRAINT constraintName)?
    ;
queryTableExprSampleClause
    : (queryTableExprTableClause
    | queryTableExprViewClause
    | hierarchyName
    | queryTableExprAnalyticClause) sampleClause?
    ;
sampleClause
    : SAMPLE BLOCK? LP_ samplePercent RP_ (SEED LP_ seedValue RP_)?
    ;
queryTableExprTableClause
    : tableName (mofifiedExternalTable | partitionExtClause | AT_ dbLink)?
    ;
partitionExtClause
    : PARTITION (LP_ partitionName RP_ | FOR LP_ partitionKeyValue (COMMA_ partitionKeyValue) RP_)
    | SUBPARTITION (LP_ subpartitionName RP_ | FOR LP_ subpartitionKeyValue (COMMA_ subpartitionKeyValue) RP_)
    ;
mofifiedExternalTable
    : EXTERNAL MODIFY modifyExternalTableProperties
    ;
modifyExternalTableProperties
    : (DEFAULT DIRECTORY directoryName)? (LOCATION LP_ (directoryName COLON_)? SQ_ locationSpecifier SQ_ (COMMA_ (directoryName COLON_)? SQ_ locationSpecifier SQ_)* RP_)?
    (ACCESS PARAMETERS (BADFILE fileName | LOGFILE fileName | DISCARDFILE fileName))? (REJECT LIMIT (INTEGER_ | UNLIMITED))?
    ;
queryTableExprViewClause
    : (viewName | materializedViewName) (AT_ dbLink)?
    ;

queryTableExprAnalyticClause
    : analyticViewName (HIERARCHIES LP_ ((attrDim DOT_)? hierarchyName (COMMA_ (attrDim DOT_)? hierarchyName)*)? RP_)?
    ;
joinClause
    : selectTableReference selectJoinOption+
    ;
selectJoinOption
    : innerCrossJoinClause
    | outerJoinClause
    | crossOuterApplyClause
    ;
crossOuterApplyClause
    : (CROSS | OUTER) APPLY (selectTableReference | collectionExpr)
    ;
innerCrossJoinClause
    : INNER? JOIN selectTableReference selectJoinSpecification
    | (CROSS | NATURAL INNER?) JOIN selectTableReference
    ;

selectJoinSpecification
    : ON expr | USING columnNames
    ;

outerJoinClause
    : queryPartitionClause? NATURAL? outerJoinType JOIN
    selectTableReference queryPartitionClause? selectJoinSpecification?
    ;
outerJoinType
    : (FULL | LEFT | RIGHT) OUTER?
    ;
bulkCollectIntoClause
    : BULK COLLECT INTO (collection=name | hostArray)
    ;
hostArray
    : COLON_ variableName
    ;
intoClause
    : INTO (variableName (COMMA_ variableName)* | record)
    ;


selectList
    : (unqualifiedShorthand | selectProjection) (COMMA_ selectProjection)*
    ;
selectProjection
    : (queryName | (tableName | viewName | materializedViewName) | alias) DOT_ASTERISK_
    | selectProjectionExprClause
    ;
selectProjectionExprClause
    : (columnName | expr) (AS? alias)?
    ;

unqualifiedShorthand
    : ASTERISK_
    ;
duplicateSpecification
    : (DISTINCT | UNIQUE) | ALL
    ;
withClause
    : WITH plsqlDeclarations? ((subqueryFactoringClause | subavFactoringClause) (COMMA_ (subqueryFactoringClause | subavFactoringClause))*)?
    ;

subavFactoringClause
    : subavName ANALYTIC VIEW AS LP_ subavClause RP_
    ;
subqueryFactoringClause
    : queryName (LP_ alias (COMMA_ alias)* RP_)? AS subquery searchClause? cycleClause?
    ;

searchClause
    : SEARCH (DEPTH | BREADTH) FIRST BY (alias (ASC | DESC)? (NULLS FIRST | NULLS LAST)?) (COMMA_ (alias (ASC | DESC)? (NULLS FIRST | NULLS LAST)?))*
    SET orderingColumn
    ;

cycleClause
    : CYCLE alias (COMMA_ alias)* SET alias TO cycleValue DEFAULT noCycleValue
    ;
plsqlDeclarations
    : (functionDeclaration | procedureDeclaration)+
    ;

functionDeclaration
    : functionHeading ((DETERMINISTIC | PIPELINED | PARALLEL_ENABLE | RESULT_CACHE)+)?
    ;

functionHeading
    : FUNCTION functionName parameterDeclarationList? RETURN dataType
    ;
parameterDeclarationList
    : LP_ (parameterDeclaration (COMMA_ parameterDeclaration)*)? RP_
    ;
parameterDeclaration
    : parameterName (IN? dataType ((ASSIGNMENT_OPERATOR_ | DEFAULT) expr)? | IN? OUT NOCOPY? dataType)?
    ;

procedureDeclaration
    : procedureHeading procedureProperties* ';'
    ;

procedureHeading
    : PROCEDURE procedureName parameterDeclarationList?
    ;

procedureProperties
    : accessibleByClause | defaultCollationClause | invokerRightsClause
    ;
defaultCollationClause
    : DEFAULT COLLATION USING_NLS_COMP
    ;
invokerRightsClause
    : AUTHID (CURRENT_USER | DEFINER)
    ;
accessibleByClause
    : ACCESSIBLE BY LP_ accessor (COMMA_ accessor)* RP_
    ;

accessor
    : unitKind? unitName
    ;
condition
    : comparisonCondition
    | floatingPointCondition
    | condition (AND | OR) condition | NOT condition
    | modelCondition
    | multisetCondition
    | patternMatchingCondition
    | rangeCondition
    | nullCondition
    | xmlCondition
    | jsonCondition
    | LP_ condition RP_ | NOT condition | condition (AND | OR) condition
    | existsCondition
    | inCondition
    | isOfTypeCondition
    ;
floatingPointCondition
    : expr IS NOT? (NAN | INFINITE)
    ;

modelCondition
    : isAnyCondition | isPresentCondition
    ;

isAnyCondition
    : (dimensionColumn IS)? ANY
    ;

isPresentCondition
    : cellReference IS PRESENT
    ;


multisetCondition
    : isASetCondition
    | isEmptyCondition
    | memberCondition
    | submultisetCondition
    ;

isASetCondition
    : tableName IS NOT? A SET
    ;

isEmptyCondition
    : tableName IS NOT? EMPTY
    ;

memberCondition
    : expr NOT? MEMBER OF? tableName
    ;

submultisetCondition
    : tableName NOT? SUBMULTISET OF? tableName
    ;

patternMatchingCondition
    : likeCondition | regexpLikeCondition
    ;

likeCondition
    : searchValue NOT? (LIKE | LIKEC | LIKE2 | LIKE4) pattern (ESCAPE escapeChar)?
    ;

escapeChar
    : stringLiterals
    ;

regexpLikeCondition
    : REGEXP_LIKE LP_ searchValue COMMA_ pattern (COMMA_ matchParam)? RP_
    ;

matchParam
    : stringLiterals
    ;

rangeCondition
    : expr NOT? BETWEEN expr AND expr
    ;

nullCondition
    : expr IS NOT? NULL
    ;

xmlCondition
    : equalsPathCondition | underPathCondition
    ;

equalsPathCondition
    : EQUALS_PATH LP_ columnName COMMA_ pathString (COMMA_ correlationInteger)? RP_
    ;

pathString
    : stringLiterals
    ;

correlationInteger
    : INTEGER_
    ;

underPathCondition
    : UNDER_PATH LP_ columnName (COMMA_ levels)? COMMA_ pathString (COMMA_ correlationInteger)? RP_
    ;



jsonCondition
    : isJsonCondition | jsonExistsCondition | jsonTextcontainsCondition
    ;

isJsonCondition
    : expr IS NOT? JSON (FORMAT JSON)? (STRICT | LAX)? ((WITH | WITHOUT) UNIQUE KEYS)?
    ;

jsonExistsCondition
    : JSON_EXISTS LP_ expr (FORMAT JSON)? COMMA_ jsonBasicPathExpr
    jsonPassingClause? jsonExistsOnErrorClause? jsonExistsOnEmptyClause? RP_
    ;

jsonPassingClause
    : PASSING expr AS identifier (COMMA_ expr AS identifier)*
    ;

jsonExistsOnErrorClause
    : (ERROR | TRUE | FALSE) ON ERROR
    ;

jsonExistsOnEmptyClause
    : (ERROR | TRUE | FALSE) ON EMPTY
    ;

jsonTextcontainsCondition
    : JSON_TEXTCONTAINS LP_ columnName COMMA_ jsonBasicPathExpr COMMA_ stringLiterals RP_
    ;

jsonBasicPathExpr
    : jsonAbsolutePathExpr | jsonRelativePathExpr
    ;

jsonAbsolutePathExpr
    : DOLLAR_ jsonNonfunctionSteps? jsonFunctionStep?
    ;

jsonNonfunctionSteps
    : ((jsonObjectStep | jsonArrayStep | jsonDescendentStep) jsonFilterExpr?)+
    ;

jsonObjectStep
    : DOT_ASTERISK_ | DOT_ jsonFieldName
    ;

digit
    : numberLiterals
    ;

jsonArrayStep
    : LBT_ (ASTERISK_ | INTEGER_ (TO INTEGER_)? (COMMA_ INTEGER_ (TO INTEGER_)?)*) RBT_
    ;

jsonDescendentStep
    : DOT_ DOT_ jsonFieldName
    ;

jsonFunctionStep
    : DOT_ jsonItemMethod LP_ RP_
    ;



jsonFilterExpr
    : QUESTION_ LP_ jsonCond RP_
    ;

jsonCond
    : jsonCond OR_ jsonCond | jsonCond AND_ jsonCond | jsonNegation
    | LP_ jsonCond RP_ | jsonComparison | jsonExistsCond
    | jsonInCond | jsonLikeCond | jsonLikeRegexCond
    | jsonEqRegexCond | jsonHasSubstringCond | jsonStartsWithCond
    ;

jsonNegation
    : NOT_ LP_ jsonCond RP_
    ;

jsonExistsCond
    : EXISTS LP_ jsonRelativePathExpr RP_
    ;

jsonHasSubstringCond
    : jsonRelativePathExpr HAS SUBSTRING (jsonString | jsonVar)
    ;

jsonStartsWithCond
    : jsonRelativePathExpr STARTS WITH (jsonString | jsonVar)
    ;

jsonLikeCond
    : jsonRelativePathExpr LIKE (jsonString | jsonVar)
    ;

jsonLikeRegexCond
    : jsonRelativePathExpr LIKE_REGEX (jsonString | jsonVar)
    ;

jsonEqRegexCond
    : jsonRelativePathExpr EQ_REGEX (jsonString | jsonVar)
    ;

jsonInCond
    : jsonRelativePathExpr IN valueList
    ;

valueList
    : LP_ (jsonScalar | jsonVar) (COMMA_ (jsonScalar | jsonVar))* RP_
    ;

jsonComparison
    : (jsonRelativePathExpr jsonComparePred (jsonVar | jsonScalar))
    | ((jsonVar | jsonScalar) jsonComparePred jsonRelativePathExpr)
    | (jsonScalar jsonComparePred jsonScalar)
    ;

jsonRelativePathExpr
    : AT_ jsonNonfunctionSteps? jsonFunctionStep?
    ;

jsonComparePred
    : DEQ_ | NEQ_ | LT_ | LTE_ | GTE_ | GT_
    ;

jsonVar
    : DOLLAR_ identifier
    ;

jsonScalar
    : jsonNumber | TRUE | FALSE | NULL | jsonString
    ;

jsonNumber
    : numberLiterals
    ;

jsonString
    : stringLiterals | identifier
    ;

existsCondition
    : EXISTS LP_ subquery RP_
    ;

inCondition
    : (expr NOT? IN LP_ (expressionList | subquery) RP_)
    | (exprList NOT? IN LP_ ((expressionList (COMMA_ expressionList)*) | subquery) RP_)
    ;

isOfTypeCondition
    : expr IS NOT? OF TYPE? LP_ ONLY? typeName (COMMA_ ONLY? typeName)* RP_
    ;


comparisonCondition
    : simpleComparisonCondition | groupComparisonCondition
    ;
groupComparisonCondition
    : (expr (EQ_ | NEQ_ | GT_ | LT_ | GTE_ | LTE_) (ANY | SOME | ALL) LP_ (expressionList | subquery) RP_)
    | (exprList (EQ_ | NEQ_) (ANY | SOME | ALL) LP_ ((expressionList (SQ_ expressionList)*) | subquery) RP_)
    ;
simpleComparisonCondition
    : (expr (EQ_ | NEQ_ | GT_ | LT_ | GTE_ | LTE_) expr)
    | (exprList (EQ_ | NEQ_) LP_ (expressionList | subquery) RP_)
    ;

unitKind
    : FUNCTION
    | PROCEDURE
    | PACKAGE
    | TRIGGER
    | TYPE
    ;

hint
    : INLINE_HINT | BLOCK_HINT
    ;
forUpdateClause
    : FOR UPDATE (OF forUpdateClauseList)? ((NOWAIT | WAIT INTEGER_) | SKIP_SYMBOL LOCKED)?
    ;

forUpdateClauseList
    : forUpdateClauseOption (COMMA_ forUpdateClauseOption)*
    ;

forUpdateClauseOption
    : ((tableName | viewName) DOT_)? columnName
    ;
offset
    : numberLiterals | expr | nullValueLiterals
    ;

rowcount
    : numberLiterals | expr | nullValueLiterals
    ;

percent
    : numberLiterals | expr | nullValueLiterals
    ;
respectOrIgnoreNulls
    : (RESPECT | IGNORE) NULLS
    ;
exprs
    : expr (COMMA_ expr)*
    ;

exprList
    : LP_ exprs RP_
    ;


joinOperator
    : LP_ PLUS_ RP_
    ;
attribute
    : (owner DOT_)? identifier
    ;


lobItem
    : attributeName | columnName
    ;

lobItems
    : lobItem (COMMA_ lobItem)*
    ;

lobItemList
    : LP_ lobItems RP_
    ;

dataType
    : characterSetDataType
    | numberDataType
    | intervalDatatype
    | dataTypeName dataTypeLength? datetimeTypeSuffix?
//    | specialDatatype
//    | customDataType
//    | typeAttribute
//    | *******. 位串类型 KingbaseES支持SELECT b’’ 和 SELECT x’’语法，兼容MySQL二进制语法。
    ;
intervalDatatype
    : INTERVAL DAY (LP_ numberValue RP_)? TO SECOND (LP_ numberValue RP_)?
    | INTERVAL YEAR (LP_ numberValue RP_)? TO MONTH
    | INTERVAL MONTH
    | INTERVAL DAY TO (HOUR | MINUTE | (SECOND LP_ numberValue RP_))
    | INTERVAL HOUR TO (MINUTE | (SECOND LP_ numberValue RP_))
    | INTERVAL MINUTE TO SECOND (LP_ numberValue RP_)?
    | INTERVAL SECOND (LP_ numberValue RP_)?
    | INTERVAL (LP_ numberValue RP_)? stringLiterals?
    ;
datetimeTypeSuffix
    : ((WITH | WITHOUT) LOCAL? TIME ZONE) | TO MONTH | TO SECOND (LP_ numberValue RP_)?
    ;
dataTypeLength
    : LP_ (length=INTEGER_ (COMMA_ (MINUS_)? INTEGER_)? type=(CHAR | BYTE)?)? RP_
    ;
numberDataType
    :
    ;
characterSetDataType
    : dataTypeName CHARACTER SET characterSetName ('%' CHARSET)?
    ;
dataTypeName
    : CHARACTER | CHAR | NCHAR | RAW | VARCHAR | VARCHAR2 | NVARCHAR | NVARCHAR2 | LONG | LONG RAW | BLOB | CLOB | NCLOB | BINARY_FLOAT | BINARY_DOUBLE
    | BOOLEAN | PLS_INTEGER | BINARY_INTEGER | INTEGER | NUMBER | NATURAL | NATURALN | POSITIVE | POSITIVEN | SIGNTYPE
    | SIMPLE_INTEGER | BFILE | MLSLABEL | UROWID | DATE | TIMESTAMP | TIMESTAMP WITH TIME ZONE | TIMESTAMP WITH LOCAL TIME ZONE
    | INTERVAL DAY TO SECOND | INTERVAL YEAR TO MONTH | JSON | FLOAT | REAL | DOUBLE PRECISION | INT | SMALLINT
    | DECIMAL | NUMERIC | DEC | IDENTIFIER_ | XMLTYPE | ROWID | ANYDATA | ANYTYPE | ANYDATASET | UB2 | SB4 | TIME
    | TEXT | LONGTEXT | MEDIUMTEXT | TINYTEXT
    | TINYINT | MIDDLEINT | MEDIUMINT | INT3 | BIGINT | FIXED | SMALLSERIAL | SERIAL | BIGSERIAL
    | MONEY
    | TIMESTAMP WITHOUT TIME ZONE | YEAR | TIME WITHOUT TIME ZONE | TIME WITH TIME ZONE
    | BYTEA | POINT | LINE | LSEG | BOX | PATH | POLYGON | CIRCLE
    | INET | CIDR | MACADDR | MACADDR8 | TSVECTOR | TSQUERY | UUID | JSON | INT4RANGE | INT8RANGE
    | NUMRANGE | TSRANGE | TSTZRANGE | DATERANGE
    | SYS_LSN | SET | ASSOCIATIVEARRAY | NESTED TABLE | VARRAY
    | ROWID | UROWID
    ;
toDateFunction
    : TO_DATE LP_ char=STRING_ (DEFAULT returnValue=STRING_ ON CONVERSION ERROR)? (COMMA_ fmt=STRING_ (COMMA_ STRING_)?)? RP_
    ;
subpartitionKeyValue
    : INTEGER_ | dateTimeLiterals
    ;

searchValue
    : identifier | stringLiterals
    ;
numberValue
    : INTEGER_ | NUMBER_
    ;
level
    : identifier
    ;

levels
    : INTEGER_
    ;
letter
    : identifier
    ;
dbLink
    : identifier (DOT_ identifier)*
    ;
pattern
    : stringLiterals
    ;
samplePercent
    : numberLiterals
    ;
seedValue
    : numberLiterals
    ;
partitionKeyValue
    : INTEGER_ | dateTimeLiterals | toDateFunction
    ;
orderingColumn
    : columnName
    ;
cycleValue
    : STRING_
    ;
noCycleValue
    : STRING_
    ;
measureColumn
    : columnName
    ;
dimensionColumn
    : columnName
    ;
analyticFunctionName
    : identifier
    ;
jsonItemMethod
    : identifier
    ;
cellReference
    : identifier
    ;
depthExpression
    : identifier
    ;
levelRef
    : identifier
    ;
attrDim
    : identifier
    ;
memberKeyExpr
    : identifier
    ;
locationSpecifier
    : identifier
    ;
// --name
attributeName
    : oracleId
    ;
oracleId
    : ((STRING_ | identifier) DOT_)* (STRING_ | identifier)
    ;
aggregationFunctionName
    : MAX | MIN | SUM | COUNT | AVG | GROUPING | LISTAGG | PERCENT_RANK | PERCENTILE_CONT | PERCENTILE_DISC | CUME_DIST | RANK
    | REGR_SLOPE | REGR_INTERCEPT | REGR_COUNT | REGR_R2 | REGR_AVGX | REGR_AVGY | REGR_SXX | REGR_SYY | REGR_SXY
    | COLLECT | CORR | CORR_S | CORR_K | COVAR_POP | COVAR_SAMP | DENSE_RANK | FIRST
    | GROUP_ID | GROUPING_ID | LAST | MEDIAN | STATS_BINOMIAL_TEST | STATS_CROSSTAB | STATS_F_TEST | STATS_KS_TEST
    | STATS_MODE | STATS_MW_TEST | STATS_ONE_WAY_ANOVA | STATS_T_TEST_ONE | STATS_T_TEST_PAIRED | STATS_T_TEST_INDEP
    | STATS_T_TEST_INDEPU | STATS_WSR_TEST | STDDEV | STDDEV_POP | STDDEV_SAMP | VAR_POP | VAR_SAMP | VARIANCE
    ;
rankFunctionName
    : RANK
    | DENSE_RANK
    | AVERAGE_RANK
    | ROW_NUMBER
    ;
leadLagFunctionName
    : LAG
    | LAG_DIFF
    | LAG_DIF_PERCENT
    | LEAD
    | LEAD_DIFF
    | LEAD_DIFF_PERCENT
    ;
hierFunctionName
    : HIER_CAPTION
    | HIER_DEPTH
    | HIER_DESCRIPTION
    | HIER_LEVEL
    | HIER_MEMBER_NAME
    | HIER_MEMBER_UNIQUE_NAME
    ;
columnNames
    : LP_? columnName (COMMA_ columnName)* RP_?
    ;
queryName
    : identifier | STRING_
    ;
validTimeColumn
    : columnName
    ;
materializedViewName
    : (owner DOT_)? name
    ;
measName
    : identifier
    ;
baseAvName
    : (owner DOT_)? name
    ;
regularFunctionName
    : identifier | IF | LOCALTIME | LOCALTIMESTAMP | INTERVAL | DECODE
    ;
jsonFieldName
    : jsonString | (letter (letter | digit)*)
    ;
typeName
    : (owner DOT_)? name
    ;
mainModelName
    : identifier
    ;
unitName
    : (owner DOT_)? name
    ;
procedureName
    : (owner DOT_)? name
    ;
parameterName
    : identifier
    ;
constraintName
    : identifier
    ;
hierarchyName
    : (owner DOT_)? name
    ;
partitionName
    : identifier
    ;
subpartitionName
    : identifier
    ;
directoryName
    : (owner DOT_)? name
    ;
fileName
    : identifier | STRING_
    ;
analyticViewName
    : (owner DOT_)? name
    ;
subavName
    : (owner DOT_)? name
    ;
functionName
    : (owner DOT_)? name | STRING_
    ;
variableName
    : (owner DOT_)? name | stringLiterals
    ;
record
    : name
    ;
referenceModelName
    : identifier
    ;
characterSetName
    : identifier
    ;
//-----------------------------
literals
    : stringLiterals
    | numberLiterals
    | dateTimeLiterals
    | hexadecimalLiterals
    | bitValueLiterals
    | booleanLiterals
    | nullValueLiterals
    ;


stringLiterals
    : STRING_
    | NCHAR_TEXT
    | UCHAR_TEXT
    ;

numberLiterals
   : (PLUS_ | MINUS_)? (INTEGER_ | NUMBER_)
   ;

dateTimeLiterals
    : (DATE | TIME | TIMESTAMP) stringLiterals
    | LBE_ identifier stringLiterals RBE_
    ;

hexadecimalLiterals
    : HEX_DIGIT_
    ;

bitValueLiterals
    : BIT_NUM_
    ;

booleanLiterals
    : TRUE | FALSE
    ;

nullValueLiterals
    : NULL
    ;

identifier
    : IDENTIFIER_ | unreservedWord | QUESTION_
    ;

// 非保留关键字
unreservedWord
    :
    ;

schemaName
    : identifier
    ;

tableName
    : (owner DOT_)? name
    ;

viewName
    : (owner DOT_)? name
    ;

columnName
    : (owner DOT_)* name
    ;

objectName
    : (owner DOT_)? name
    ;

owner
    : identifier
    ;

name
    : identifier
    ;

alias
    : identifier | STRING_
    ;

expr
    : expr andOperator expr
    | expr orOperator expr
    | notOperator expr
    | LP_ expr RP_
    | booleanPrimary
    ;

andOperator
    : AND | AND_
    ;

orOperator
    : OR | OR_
    ;

notOperator
    : NOT | NOT_
    ;

booleanPrimary
    : booleanPrimary IS NOT? (TRUE | FALSE | UNKNOWN | NULL)
    | (PRIOR | DISTINCT) predicate
    | CONNECT_BY_ROOT predicate
    | booleanPrimary SAFE_EQ_ predicate
    | booleanPrimary comparisonOperator (ALL | ANY) subquery
    | booleanPrimary comparisonOperator predicate
    | predicate
    ;

comparisonOperator
    : EQ_ | GTE_ | GT_ | LTE_ | LT_ | NEQ_
    ;

predicate
    : bitExpr NOT? IN subquery
    | PRIOR predicate
    | bitExpr NOT? IN LP_ expr (COMMA_ expr)* RP_
    | bitExpr NOT? IN LP_ expr (COMMA_ expr)* RP_ AND predicate
    | bitExpr NOT? BETWEEN bitExpr AND predicate
    | bitExpr NOT? LIKE simpleExpr (ESCAPE simpleExpr)?
    | bitExpr
    ;

bitExpr
    : bitExpr VERTICAL_BAR_ bitExpr
    | bitExpr AMPERSAND_ bitExpr
    | bitExpr SIGNED_LEFT_SHIFT_ bitExpr
    | bitExpr SIGNED_RIGHT_SHIFT_ bitExpr
    | bitExpr PLUS_ bitExpr
    | simpleExpr
    | bitExpr MINUS_ bitExpr
    | bitExpr ASTERISK_ bitExpr
    | bitExpr SLASH_ bitExpr
    | bitExpr MOD_ bitExpr
    | bitExpr MOD bitExpr
    | bitExpr CARET_ bitExpr
    | bitExpr DOT_ bitExpr
    | bitExpr ARROW_ bitExpr
    ;

simpleExpr
    : functionCall #functionExpr
    | literals #literalsExpr
    | simpleExpr OR_ simpleExpr #orExpr
    | (PLUS_ | MINUS_ | TILDE_ | NOT_ | BINARY) simpleExpr #unaryExpr
    | ROW? LP_ expr (COMMA_ expr)* RP_ #rowExpr
    | EXISTS? subquery #subqueryExpr
    | LBE_ identifier expr RBE_ #objectAccessExpr
    | caseExpression #caseExpr
    | columnName #columnExpr
    // | ...
    ;

functionCall
    : // aggregationFunction | analyticFunction | specialFunction | regularFunction | xmlFunction
    ;
caseExpression
    : CASE simpleExpr? caseWhen+ caseElse? END
    ;

caseWhen
    : WHEN expr THEN expr
    ;

caseElse
    : ELSE expr
    ;

subquery
    : LP_ select RP_
    ;