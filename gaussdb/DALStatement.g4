
grammar DALStatement;

import DDLStatement;

show
    : SHOW (varName | TIME ZONE | TRANSACTION ISOLATION LEVEL | SESSION AUTHORIZATION | ALL)
    ;

set
    : SET runtimeScope?
    (timeZoneClause
    | configurationParameterClause
    | varName FROM CURRENT
    | TIME ZONE zoneValue
    | CATALOG STRING_
    | SCHEMA STRING_
    | NAMES encoding?
    | ROLE nonReservedWordOrSconst
    | SESSION AUTHORIZATION IDENTIFIER_ PASSWORD STRING_
    | SESSION AUTHORIZATION DEFAULT
    | XML OPTION documentOrContent)
    ;

runtimeScope
    : SESSION | LOCAL
    ;

timeZoneClause
    : TIME ZONE (numberLiterals | LOCAL | DEFAULT)
    ;

configurationParameterClause
    : varName (TO | EQ_) (varList | DEFAULT)
    ;

resetParameter
    : RESET (ALL | identifier)
    ;

explain
    : EXPLAIN
    (analyzeKeyword VERBOSE?
    | VERBOSE
    | PERFORMANCE
    | LP_ explainOptionList RP_)?
    explainableStmt
    ;

explainableStmt
    : select | insert | update | delete | declare | executeStmt | createMaterializedView | refreshMatViewStmt
    ;

explainOptionList
    : explainOptionElem (COMMA_ explainOptionElem)*
    ;

explainOptionElem
    : explainOptionName explainOptionArg?
    ;

explainOptionArg
    : booleanOrString | numericOnly
    ;

explainOptionName
    : nonReservedWord | analyzeKeyword
    ;

analyzeKeyword
    : ANALYZE | ANALYSE
    ;

analyzeTable
    : analyzeKeyword (VERBOSE? | LP_ vacAnalyzeOptionList RP_) vacuumRelationList?
    ;

vacuumRelationList
    : vacuumRelation (COMMA_ vacuumRelation)*
    ;

vacuumRelation
    : qualifiedName optNameList
    ;

vacAnalyzeOptionList
    : vacAnalyzeOptionElem (COMMA_ vacAnalyzeOptionElem)*
    ;

vacAnalyzeOptionElem
    : vacAnalyzeOptionName vacAnalyzeOptionArg?
    ;

vacAnalyzeOptionArg
    : booleanOrString | numericOnly
    ;

vacAnalyzeOptionName
    : nonReservedWord | analyzeKeyword
    ;

load
    : LOAD fileName
    ;

valuesClause
    : VALUES LP_ exprList RP_
    | valuesClause COMMA_ LP_ exprList RP_
    ;

vacuum
    : VACUUM ((FULL? FREEZE? VERBOSE? ANALYZE?) | (LP_ vacAnalyzeOptionList RP_)) vacuumRelationList?
    ;
    
emptyStatement
    :
    ;
