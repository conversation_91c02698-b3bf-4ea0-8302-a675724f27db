
grammar TCLStatement;

import DMLStatement;

setTransaction
    : SET (SESSION CHARACTERISTICS AS)? TRANSACTION transactionModeList
    | SET TRANSACTION SNAPSHOT STRING_
    ;

beginTransaction
    : BEGIN (WORK | TRANSACTION)? transactionModeList?
    ;

commit
    : COMMIT (WORK | TRANSACTION)? (AND (NO)? CHAIN)?
    ;

savepoint
    : SAVEPOINT colId
    ;

abort
    : ABORT (WORK | TRANSACTION)? (AND (NO)? CHAIN)?
    ;

startTransaction
    : START TRANSACTION transactionModeList?
    ;

end
    : END (WORK | TRANSACTION)? (AND (NO)? CHAIN)?
    ;

rollback
    : ROLLBACK (WORK | TRANSACTION)? (AND (NO)? CHAIN)?
    ;

releaseSavepoint
    : RELEASE SAVEPOINT? colId
    ;

rollbackToSavepoint
    : ROLLBACK (WORK | TRANSACTION)? TO SAVEPOINT? colId
    ;

prepareTransaction
    : PREPARE TRANSACTION STRING_
    ;

commitPrepared
    : COMMIT PREPARED STRING_
    ;

rollbackPrepared
    : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> PREPARED STRING_
    ;

setConstraints
    : SET CONSTRAINTS constraintsSetList constraintsSetMode
    ;

constraintsSetMode
    : DEFERRED | IMMEDIATE
    ;

constraintsSetList
    : ALL | qualifiedNameList
    ;

