
lexer grammar Literals;

import Alphabet, Symbol;

IDENTIFIER_
    : IdentifierStartChar IdentifierChar*
    | DQ_ ~'"'+ DQ_
    ;

STRING_
    : SQ_ ('\\'. | '\'\'' | ~('\'' | '\\'))* SQ_
    ;

NUMBER_
    : INT_? DOT_? INT_ (E (PLUS_ | MINUS_)? INT_)?
    ;

HEX_DIGIT_
    : '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_
    ;

BIT_NUM_
    : '0b' ('0' | '1')+ | B SQ_ ('0' | '1')+ SQ_
    ;

FILESIZE_LITERAL
    : INT_ ('K'|'M'|'G'|'T')
    ;

fragment INT_
    : [0-9]+
    ;

fragment HEX_
    : [0-9a-fA-F]
    ;

fragment IdentifierStartChar
   : [a-zA-Z_]
   | [\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u00FF]
   | [\u0100-\uD7FF\uE000-\uFFFF]
   | [\uD800-\uDBFF] [\uDC00-\uDFFF]
   ;

fragment IdentifierChar
   : StrictIdentifierChar
   | DOLLAR_
   ;
   
fragment StrictIdentifierChar
   : IdentifierStartChar
   | [0-9]
   ;
