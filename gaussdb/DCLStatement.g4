
grammar DCLStatement;

import DDLStatement;

grant
    : GRANT (privilegeClause | roleClause)
    ;

revoke
    : REVOKE optionForClause? (privilegeClause | roleClause) (CASCADE | RESTRICT)?
    ;

optionForClause
    : (GRANT | ADMIN) OPTION FOR
    ;

createUser
    : CREATE USER roleSpec WITH? createOptRoleElem*
    ;

createOptRoleElem
    : alterOptRoleElem
    | SYSID NUMBER_
    | ADMIN roleList
    | ROLE roleList
    | IN ROLE roleList
    | IN GROUP roleList
    ;

alterOptRoleElem
    : PASSWORD STRING_
    | PASSWORD NULL
    | ENCRYPTED PASSWORD STRING_
    | UNENCRYPTED PASSWORD STRING_
    | INHERIT
    | CONNECTION LIMIT signedIconst
    | VALID UNTIL STRING_
    | USER roleList
    | identifier
    ;

dropBehavior
    : CASCADE | RESTRICT
    ;

dropUser
    : DROP USER ifExists? nameList dropBehavior?
    ;

alterUser
    : ALTER USER alterUserClauses
    ;

alterUserClauses
    : roleSpec WITH? alterOptRoleList
    | roleSpec (IN DATABASE name)? setResetClause
    | ALL (IN DATABASE name)? setResetClause
    | roleSpec RENAME TO roleSpec
    ;

alterOptRoleList
    : alterOptRoleElem*
    ;

createRole
    : CREATE ROLE roleSpec WITH? createOptRoleElem*
    ;

dropRole
    : DROP ROLE ifExists? roleList
    ;

alterRole
    : ALTER ROLE alterUserClauses
    ;

createGroup
    : CREATE GROUP roleSpec WITH? createOptRoleElem*
    ;

dropDroup
    : DROP GROUP ifExists? roleList
    ;

reassignOwned
    : REASSIGN OWNED BY roleList TO roleSpec
    ;
