
lexer grammar Keyword;

import Alphabet;

WS
    : [ \t\r\n] + ->skip
    ;

SELECT
    : S E L E C T
    ;

INSERT
    : I N S E R T
    ;

UPDATE
    : U P D A T E
    ;

DELETE
    : D E L E T E
    ;

CREATE
    : C R E A T E
    ;

ALTER
    : A L T E R
    ;

DROP
    : D R O P
    ;

TRUNCATE
    : T R U N C A T E
    ;

SCHEMA
    : S C H E M A
    ;

GRANT
    : G R A N T
    ;

REVOKE
    : R E V O K E
    ;

ADD
    : A D D
    ;

SET
    : S E T
    ;

TABLE
    : T A B L E
    ;

COLUMN
    : C O L U M N
    ;

INDEX
    : I N D E X
    ;

CONSTRAINT
    : C O N S T R A I N T
    ;

PRIMARY
    : P R I M A R Y
    ;

UNIQUE
    : U N I Q U E
    ;

FOREIGN
    : F O R E I G N
    ;

KEY
    : K E Y
    ;

POSITION
    : P O S I T I O N
    ;

PRECISION
    : P R E C I S I O N
    ;

FUNCTION
    : F U N C T I O N
    ;

TRIGGER
    : T R I G G E R
    ;

PROCEDURE
    : P R O C E D U R E
    ;

VIEW
    : V I E W
    ;

INTO
    : I N T O
    ;

VALUES
    : V A L U E S
    ;

WITH
    : W I T H
    ;

UNION
    : U N I O N
    ;

DISTINCT
    : D I S T I N C T
    ;

MAXSIZE
    : M A X S I Z E
    ;

CASE
    : C A S E
    ;

WHEN
    : W H E N
    ;

CAST
    : C A S T
    ;

TRIM
    : T R I M
    ;

SUBSTRING
    : S U B S T R I N G
    ;

FROM
    : F R O M
    ;

NATURAL
    : N A T U R A L
    ;

JOIN
    : J O I N
    ;

FULL
    : F U L L
    ;

INNER
    : I N N E R
    ;

OUTER
    : O U T E R
    ;

LEFT
    : L E F T
    ;

RIGHT
    : R I G H T
    ;

CROSS
    : C R O S S
    ;

USING
    : U S I N G
    ;

WHERE
    : W H E R E
    ;

AS
    : A S
    ;

ON
    : O N
    ;

IF
    : I F
    ;

ELSE
    : E L S E
    ;

THEN
    : T H E N
    ;

FOR
    : F O R
    ;

TO
    : T O
    ;

AND
    : A N D
    ;

OR
    : O R
    ;

IS
    : I S
    ;

NOT
    : N O T
    ;

NULL
    : N U L L
    ;

TRUE
    : T R U E
    ;

FALSE
    : F A L S E
    ;

EXISTS
    : E X I S T S
    ;

BETWEEN
    : B E T W E E N
    ;

IN
    : I N
    ;

ALL
    : A L L
    ;

ANY
    : A N Y
    ;

LIKE
    : L I K E
    ;

ORDER
    : O R D E R
    ;

GROUP
    : G R O U P
    ;

BY
    : B Y
    ;

ASC
    : A S C
    ;

DESC
    : D E S C
    ;

HAVING
    : H A V I N G
    ;

LIMIT
    : L I M I T
    ;

OFFSET
    : O F F S E T
    ;

BEGIN
    : B E G I N
    ;

COMMIT
    : C O M M I T
    ;

ROLLBACK
    : R O L L B A C K
    ;

SAVEPOINT
    : S A V E P O I N T
    ;

BOOLEAN
    : B O O L E A N
    ;

DOUBLE
    : D O U B L E
    ;

CHAR
    : C H A R
    ;

CHARACTER
    : C H A R A C T E R
    ;

ARRAY
    : A R R A Y
    ;

INTERVAL
    : I N T E R V A L
    ;

DATE
    : D A T E
    ;

TIME
    : T I M E
    ;

TIMESTAMP
    : T I M E S T A M P
    ;

LOCALTIME
    : L O C A L T I M E
    ;

LOCALTIMESTAMP
    : L O C A L T I M E S T A M P
    ;

YEAR
    : Y E A R
    ;

QUARTER
    : Q U A R T E R
    ;

MONTH
    : M O N T H
    ;

WEEK
    : W E E K
    ;

DAY
    : D A Y
    ;

HOUR
    : H O U R
    ;

MINUTE
    : M I N U T E
    ;

SECOND
    : S E C O N D
    ;

MICROSECOND
    : M I C R O S E C O N D
    ;

DEFAULT
    : D E F A U L T
    ;

CURRENT
    : C U R R E N T
    ;

ENABLE
    : E N A B L E
    ;

DISABLE
    : D I S A B L E
    ;

CALL
    : C A L L
    ;

INSTANCE
    : I N S T A N C E
    ;

PRESERVE
    : P R E S E R V E
    ;

DO
    : D O
    ;

DEFINER
    : D E F I N E R
    ;

CURRENT_USER
    : C U R R E N T UL_ U S E R
    ;

SQL
    : S Q L
    ;


CASCADED
    : C A S C A D E D
    ;

LOCAL
    : L O C A L
    ;

CLOSE
    : C L O S E
    ;

OPEN
    : O P E N
    ;

NEXT
    : N E X T
    ;

NAME
    : N A M E
    ;

COLLATION
    : C O L L A T I O N
    ;

NAMES
    : N A M E S
    ;

INTEGER
    : I N T E G E R
    ;

REAL
    : R E A L
    ;

DECIMAL
    : D E C I M A L
    ;

TYPE
    : T Y P E
    ;

SMALLINT
    : S M A L L I N T
    ;

BIGINT
    : B I G I N T
    ;

NUMERIC
    : N U M E R I C
    ;

TEXT
    : T E X T
    ;

REPEATABLE
    : R E P E A T A B L E
    ;

CURRENT_DATE
    : C U R R E N T UL_ D A T E
    ;

CURRENT_TIME
    : C U R R E N T UL_ T I M E
    ;

CURRENT_TIMESTAMP
    : C U R R E N T UL_ T I M E S T A M P
    ;

NULLIF
    : N U L L I F
    ;

VARYING
    : V A R Y I N G
    ;

NATIONAL
    : N A T I O N A L
    ;

NCHAR
    : N C H A R
    ;

VALUE
    : V A L U E
    ;

BOTH
    : B O T H
    ;

LEADING
    : L E A D I N G
    ;

TRAILING
    : T R A I L I N G
    ;

COALESCE
    : C O A L E S C E
    ;

INTERSECT
    : I N T E R S E C T
    ;

EXCEPT
    : E X C E P T
    ;

PACKAGE
    : P A C K A G E
    ;
    
MINUS
    : M I N U S
    ;

TIES
    : T I E S
    ;

FETCH
    : F E T C H
    ;

CUBE
    : C U B E
    ;

GROUPING
    : G R O U P I N G
    ;

SETS
    : S E T S
    ;

WINDOW
    : W I N D O W
    ;

OTHERS
    : O T H E R S
    ;

OVERLAPS
    : O V E R L A P S
    ;

SOME
    : S O M E
    ;

AT
    : A T
    ;

DEC
    : D E C
    ;

END
    : E N D
    ;

LESS
    : L E S S
    ;

THAN
    : T H A N
    ;

MAXVALUE
    : M A X V A L U E
    ;

