-- 测试Oracle ALTER TABLE ADD CONSTRAINT语句解析
ALTER TABLE t_order_item ADD CONSTRAINT fk_order_id FOREIGN KEY (order_id) REFERENCES t_order (order_id) ON DELETE CASCADE;

-- 其他相关的测试语句
ALTER TABLE t_order_item ADD CONSTRAINT pk_id PRIMARY KEY (id);
ALTER TABLE t_order_item ADD CONSTRAINT chk_status CHECK (status IN ('ACTIVE', 'INACTIVE'));
ALTER TABLE t_order_item ADD CONSTRAINT uq_order_item UNIQUE (order_id, item_id);

-- 测试不同的引用完整性操作
ALTER TABLE child_table ADD CONSTRAINT fk_parent FOREIGN KEY (parent_id) REFERENCES parent_table (id) ON DELETE SET NULL;
ALTER TABLE child_table ADD CONSTRAINT fk_parent2 FOREIGN KEY (parent_id) REFERENCES parent_table (id) ON DELETE RESTRICT;
ALTER TABLE child_table ADD CONSTRAINT fk_parent3 FOREIGN KEY (parent_id) REFERENCES parent_table (id) ON UPDATE CASCADE;

-- 原有测试语句
ALTER TABLE employees ADD (
    CONSTRAINT emp_emp_id_pk PRIMARY KEY (employee_id),
    CONSTRAINT emp_dept_fk FOREIGN KEY (department_id) REFERENCES departments,
    CONSTRAINT emp_job_fk FOREIGN KEY (job_id) REFERENCES jobs (job_id),
    CONSTRAINT emp_manager_fk FOREIGN KEY (manager_id) REFERENCES employees
);