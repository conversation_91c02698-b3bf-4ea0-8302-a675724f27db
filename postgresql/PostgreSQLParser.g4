parser grammar PostgreSQLParser;

options {
    tokenVocab = PostgreSQLLexer;
}

root
    : (select
    | insert
    | update
    | delete
    | abort
    | alterAggregate
    | alterCollation
    | alterConversion
    | alterDatabase
    | alterDefaultPrivileges
    | alterDomain
    | alterEventTrigger
    | alterExtension
    | alterForeignDataWrapper
    | alterForeignTable
    | alterFunction
    | alterGroup
    | alterIndex
    | alterLanguage
    | alterLargeObject
    | alterMaterializedView
    | alterOperator
    | alterOperatorClass
    | alterOperatorFamily
    | alterPolicy
    | alterProcedure
    | alterPublication
    | alterRole
    | alterRoutine
    | alterRule
    | alterSchema
    | alterSequence
    | alterServer
    | alterStatistics
    | alterSubscription
    | alterSystem
    | alterTable
    | alterTablespace
    | alterTextSearchConfiguration
    | alterTextSearchDictionary
    | alterTextSearchParser
    | alterTextSearchTemplate
    | alterTrigger
    | alterType
    | alterUser
    | alterUserMapping
    | alterView
    | analyzeTable
    | beginTransaction
    | call
    | checkpoint
    | close
    | cluster
    | comment
    | commit
    | commitPrepared
    | copy
    | createAccessMethod
    | createAggregate
    | createCast
    | createCollation
    | createConversion
    | createDatabase
    | createDomain
    | createEventTrigger
    | createExtension
    | createForeignDataWrapper
    | createForeignTable
    | createFunction
    | createGroup
    | createIndex
    | createLanguage
    | createMaterializedView
    | createOperator
    | createOperatorClass
    | createOperatorFamily
    | createPolicy
    | createProcedure
    | createPublication
    | createRole
    | createRule
    | createSchema
    | createSequence
    | createServer
    | createStatistics
    | createSubscription
    | createTable
    | createTablespace
    | createTextSearch
    | createTransform
    | createTrigger
    | createType
    | createUser
    | createUserMapping
    | createView
    | deallocate
    | declare
    | discard
    | do
    | dropAccessMethod
    | dropAggregate
    | dropCast
    | dropCollation
    | dropConversion
    | dropDatabase
    | dropDomain
    | dropEventTrigger
    | dropExtension
    | dropForeignDataWrapper
    | dropForeignTable
    | dropFunction
    | dropGroup
    | dropIndex
    | dropLanguage
    | dropMaterializedView
    | dropOperator
    | dropOperatorClass
    | dropOperatorFamily
    | dropOwned
    | dropPolicy
    | dropProcedure
    | dropPublication
    | dropRole
    | dropRoutine
    | dropRule
    | dropSchema
    | dropSequence
    | dropServer
    | dropServer
    | dropStatistics
    | dropSubscription
    | dropTable
    | dropTablespace
    | dropTextSearch
    | dropTransform
    | dropTrigger
    | dropType
    | dropUser
    | dropUserMapping
    | dropView
    | emptyStatement
    | end
    | executeStmt
    | explain
    | fetch
    | grant
    | importForeignSchema
    | listen
    | load
    | lock
    | move
    | notifyStmt
    | prepare
    | prepareTransaction
    | reassignOwned
    | refreshMaterializedView
    | reindex
    | releaseSavepoint
    | resetParameter
    | revoke
    | rollback
    | rollbackPrepared
    | rollbackToSavepoint
    | savepoint
    | securityLabelStmt
    | set
    | setConstraints
    | setTransaction
    | show
    | startTransaction
    | truncateTable
    | unlisten
    | vacuum
    | open
    | merge
    ) SEMI_? EOF
    ;
//https://www.postgresql.org/docs/current/sql-merge.html
merge
    : withClause? MERGE INTO ONLY? tableName ASTERISK_? AS? alias? USING dataSource ON joinCondition whenClause+ returningClause?
    ;
joinCondition
    : aExpr
    ;
dataSource
    :  (ONLY? tableName ASTERISK_?  | select) AS? alias?
    ;
//https://www.postgresql.org/docs/current/sql-createtable.html
//https://www.postgresql.org/docs/current/sql-createtableas.html
createTable
    : CREATE createTableSpecification TABLE ifNotExists? tableName
      ( createDefinitionClause
        | (OF typeName (LP_ typedTableElementList RP_)?)
        | (PARTITION OF parentTable=tableName (LP_ typedTableElementList RP_)? partitionBoundSpec)
      )?
      inheritClause? partitionSpec? tableAccessMethodClause? withOption? onCommitOption? tableSpace?
      (AS select withData?)?
      (EXECUTE name executeParamClause withData?)?
    ;

executeParamClause
    : LP_ exprList RP_
    ;

partitionBoundSpec
    : FOR VALUES ( IN LP_ exprList RP_
        | FROM LP_ exprList RP_ TO LP_ exprList RP_
        | WITH LP_ hashPartbound RP_
    )
    | DEFAULT
    ;

hashPartbound
    : hashPartboundElem (COMMA_ hashPartboundElem)*
    ;

hashPartboundElem
    : (MODULUS | REMAINDER) NUMBER_
    ;

typedTableElementList
    : typedTableElement (COMMA_ typedTableElement)*
    ;

typedTableElement
    : columnOptions
    | tableConstraint
    ;

columnOptions
    : columnName (WITH OPTIONS)? colQualList
    ;

colQualList
    : columnConstraint*
    ;

withData
    : WITH NO? DATA
    ;

tableSpace
    : TABLESPACE tablespaceName
    ;

onCommitOption
    : ON COMMIT (DROP | DELETE ROWS | PRESERVE ROWS)
    ;

withOption
    : WITH reloptions | WITHOUT OIDS
    ;

tableAccessMethodClause
    : USING accessMethod
    ;

accessMethod
    : identifier | unreservedWord | colNameKeyword
    ;
//https://www.postgresql.org/docs/current/sql-createindex.html
createIndex
    : CREATE UNIQUE? INDEX CONCURRENTLY? (ifNotExists? indexName)? ON onlyClause? tableName
      accessMethodClause? LP_ indexParams RP_ include? (WITH reloptions)? tableSpace? whereClause?
    ;

include
    : INCLUDE LP_ indexIncludingParams RP_
    ;

indexIncludingParams
    : indexElem (COMMA_ indexElem)*
    ;

accessMethodClause
    : USING accessMethod
    ;
//https://www.postgresql.org/docs/current/sql-createdatabase.html
createDatabase
    : CREATE DATABASE databaseName WITH? createDatabaseSpecification*
    ;
//https://www.postgresql.org/docs/current/sql-createview.html
createView
    : CREATE (OR REPLACE)? (TEMP | TEMPORARY)? RECURSIVE? VIEW viewName
      columnNames?
      (WITH reloptions)?
      AS select
      (WITH (CASCADE | CASCADED | LOCAL)? CHECK OPTION)?
    ;

columnList
    : columnName (COMMA_ columnName)*
    ;

//https://www.postgresql.org/docs/current/sql-dropdatabase.html
dropDatabase
    : DROP DATABASE ifExists? databaseName withOption?
    ;

createDatabaseSpecification
    :  createdbOptName EQ_? (signedIconst | booleanOrString | DEFAULT)
    ;

createdbOptName
    : identifier
    | CONNECTION LIMIT
    | ENCODING
    | LOCATION
    | OWNER
    | TABLESPACE
    | TEMPLATE
    | ALLOW_CONNECTIONS
    | IS_TEMPLATE
    | STRATEGY
    | LOCALE
    | LC_COLLATE
    | LC_CTYPE
    | BUILTIN_LOCALE
    | ICU_LOCALE
    | ICU_RULES
    | LOCALE_PROVIDER
    | COLLATION_VERSION
    | OID
    ;
//https://www.postgresql.org/docs/current/sql-altertable.html
alterTable
    : ALTER TABLE
    ( ifExists? onlyClause?  tableName ASTERISK_? alterDefinitionClause
    | ALL IN TABLESPACE tableName ASTERISK_? (OWNED BY roleList)? SET TABLESPACE tablespaceName NOWAIT?)
    ;
//https://www.postgresql.org/docs/current/sql-alterindex.html
alterIndex
    : ALTER INDEX (ifExists? | ALL IN TABLESPACE) indexName alterIndexDefinitionClause
    ;
//https://www.postgresql.org/docs/current/sql-droptable.html
dropTable
    : DROP TABLE ifExists? tableName (COMMA_ tableName)* dropTableOpt?
    ;

dropTableOpt
    : CASCADE | RESTRICT
    ;
//https://www.postgresql.org/docs/current/sql-dropindex.html
dropIndex
    : DROP INDEX CONCURRENTLY? ifExists? indexName (COMMA_ indexName)* dropIndexOpt?
    ;

dropIndexOpt
    : CASCADE | RESTRICT
    ;
//https://www.postgresql.org/docs/current/sql-truncate.html
truncateTable
    : TRUNCATE TABLE? onlyClause? tableNamesClause restartSeqs? dropTableOpt?
    ;

restartSeqs
    : CONTINUE IDENTITY
    | RESTART IDENTITY
    ;

createTableSpecification
    : ((GLOBAL | LOCAL)? (TEMPORARY | TEMP) | UNLOGGED)?
    ;

createDefinitionClause
    : LP_ (createDefinition (COMMA_ createDefinition)*)? RP_
    ;

createDefinition
    : columnDefinition | tableConstraint | LIKE tableName likeOption*
    ;

columnDefinition
    : columnName dataType storageOption? compressionOption? collateClause? columnConstraint*
    ;
compressionOption
    : COMPRESSION (DEFAULT | identifier)
    ;
storageOption
    : STORAGE (PLAIN | EXTERNAL | EXTENDED | MAIN | DEFAULT)
    ;
columnConstraint
    : constraintClause? columnConstraintOption constraintOptionalParam
    ;

constraintClause
    : CONSTRAINT constraintName
    ;

columnConstraintOption
    : NOT? NULL
    | checkOption
    | DEFAULT defaultExpr
    | GENERATED ALWAYS AS LP_ aExpr RP_ STORED
    | GENERATED (ALWAYS | BY DEFAULT) AS IDENTITY (LP_ sequenceOptions RP_)?
    | UNIQUE indexParameters
    | primaryKey indexParameters
    | REFERENCES tableName columnNames? (MATCH FULL | MATCH PARTIAL | MATCH SIMPLE)? (ON (DELETE | UPDATE) action)*
    ;

checkOption
    : CHECK aExpr (NO INHERIT)?
    ;

defaultExpr
    : CURRENT_TIMESTAMP | aExpr
    ;

sequenceOptions
    : sequenceOption+
    ;

sequenceOption
    : START WITH? NUMBER_
    | INCREMENT BY? NUMBER_
    | MAXVALUE NUMBER_
    | NO MAXVALUE
    | MINVALUE NUMBER_
    | NO MINVALUE
    | CYCLE
    | NO CYCLE
    | CACHE NUMBER_
    | OWNED BY
    ;

indexParameters
    : (USING INDEX TABLESPACE ignoredIdentifier)?
    | INCLUDE columnNames
    | WITH definition
    ;

action
    : NO ACTION | RESTRICT | CASCADE | SET (NULL | DEFAULT) columnNames?
    ;

constraintOptionalParam
    : (NOT? DEFERRABLE)? (INITIALLY (DEFERRED | IMMEDIATE))?
    ;

likeOption
    : (INCLUDING | EXCLUDING) (COMMENTS | CONSTRAINTS | DEFAULTS | IDENTITY | INDEXES | STATISTICS | STORAGE | ALL)
    ;

tableConstraint
    : constraintClause? tableConstraintOption constraintOptionalParam
    ;

tableConstraintOption
    : checkOption
    | UNIQUE (NULLS (NOT)? DISTINCT)? columnNames indexParameters
    | primaryKey columnNames indexParameters
    | EXCLUDE (USING ignoredIdentifier)? LP_ exclusionConstraintList RP_ indexParameters exclusionWhereClause?
    | FOREIGN KEY columnNames REFERENCES tableName columnNames? (MATCH FULL | MATCH PARTIAL | MATCH SIMPLE)? (ON (DELETE | UPDATE) action)*
    ;

exclusionWhereClause
    : WHERE LP_ aExpr RP_
    ;

exclusionConstraintList
    : exclusionConstraintElem (COMMA_ exclusionConstraintElem)*
    ;

exclusionConstraintElem
    : indexElem WITH anyOperator
    | indexElem WITH OPERATOR LP_ anyOperator RP_
    ;

inheritClause
    : INHERITS tableNames
    ;

partitionSpec
    : PARTITION BY partStrategy LP_ partParams RP_
    ;

partParams
    : partElem (COMMA_ partElem)*
    ;

partElem
    : (columnName  | LP_ aExpr RP_ | funcExprWindowless)  (COLLATE collation)? opclass?
    ;

funcExprWindowless
    : funcApplication | functionExprCommonSubexpr
    ;

partStrategy
    : RANGE | LIST | HASH
    | identifier
    | unreservedWord
    ;


onlyClause
    : ONLY
    ;

alterDefinitionClause
    : alterTableActions
    | renameColumnSpecification
    | renameConstraint
    | renameTableSpecification
    | SET SCHEMA schemaName
    | partitionCmd
    ;
partitionCmd
    : ATTACH PARTITION anyName partitionBoundSpec
    | DETACH PARTITION anyName (CONCURRENTLY | FINALIZE)?
    ;
renameColumnSpecification
    : RENAME COLUMN? columnName TO newName=columnName
    ;
renameConstraint
    : RENAME CONSTRAINT constraintName TO newConstraintName=constraintName
    ;
renameTableSpecification
    : RENAME TO tableName
    ;

alterIndexDefinitionClause
    : RENAME TO indexName
    | (OWNED BY ignoredIdentifiers)? SET TABLESPACE tablespaceName (NOWAIT)?
    | NO? DEPENDS ON EXTENSION extensionName
    | alterTableCmds
    | indexPartitionCmd     //最新版本不存在
    ;

indexPartitionCmd
    : ATTACH PARTITION qualifiedName
    ;







tableNamesClause
    : tableNameClause (COMMA_ tableNameClause)*
    ;

tableNameClause
    : tableName ASTERISK_?
    ;

alterTableActions
    : alterTableAction (COMMA_ alterTableAction)*
    ;

alterTableAction
    : addColumnSpecification
    | dropColumnSpecification
    | modifyColumnSpecification
    | addConstraintSpecification
    | modifyConstraintSpecification
    | validateConstraintSpecification
    | dropConstraintSpecification
    | triggerSpecification
    | triggerExtSpecification
    | ruleSpecification
    | rowLevelSecuritySpec
    | clusterSpec
    | accessMethodSpec
    | setWithoutClusterSpec
    | setOidsSpec
    | setStitisticsSpec
    | setTableSpaceSpec
    | setLoggedSpec
    | setStorageSpec
    | setStorageParameterSpec
    | resetStorageSpec
    | inheritSpec
    | ofSpec
    | notOfSpec
    | ownerToSpec
    | replicaIdentitySpec
    ;
replicaIdentitySpec
    : REPLICA IDENTITY (DEFAULT | (USING INDEX indexName) | FULL | NOTHING)
    ;
ownerToSpec
    : OWNER TO roleSpec
    ;
notOfSpec
    : NOT OF
    ;
ofSpec
    : OF dataTypeName
    ;
inheritSpec
    : NO? INHERIT tableName
    ;
resetStorageSpec
    : RESET LP_ storageParameter (COMMA_ storageParameter)* RP_
    ;
setStorageParameterSpec
    : SET LP_ (storageParameter) (COMMA_ storageParameter)* RP_
    ;
setStorageSpec
    : SET LP_ (storageParameterWithValue) (COMMA_ storageParameterWithValue)* RP_
    ;
setLoggedSpec
    : SET (LOGGED | UNLOGGED)
    ;
setTableSpaceSpec
    : SET TABLESPACE tablespaceName
    ;
setStitisticsSpec
    : SET STATISTICS signedIconst
    ;
setOidsSpec
    : SET (WITH | WITHOUT) OIDS
    ;
setWithoutClusterSpec
    : SET WITHOUT CLUSTER
    ;
accessMethodSpec
    : SET (ACCESS METHOD accessMethod) (COMMA_ ACCESS METHOD accessMethod)*
    ;
clusterSpec
    : CLUSTER ON indexName
    ;
rowLevelSecuritySpec
    : (DISABLE | ENABLE | (NO? FORCE)) ROW LEVEL SECURITY
    ;
ruleSpecification
    : (DISABLE | ENABLE)  (REPLICA | ALWAYS)? RULE ruleName
    ;
triggerExtSpecification
    : ENABLE (REPLICA | ALWAYS) TRIGGER triggerName
    ;
triggerSpecification
    : (DISABLE | ENABLE) TRIGGER (triggerName | ALL | USER)?
    ;
dropConstraintSpecification
    : DROP CONSTRAINT ifExists? constraintName (RESTRICT | CASCADE)?
    ;
validateConstraintSpecification
    : VALIDATE CONSTRAINT constraintName
    ;
modifyConstraintSpecification
    : ALTER CONSTRAINT constraintName constraintOptionalParam
    ;
addConstraintSpecification
    : ADD (tableConstraint (NOT VALID)? | tableConstraintUsingIndex)
    ;
modifyColumnSpecification
    : ALTER COLUMN? columnName alterColumnOption
    ;
dropColumnSpecification
    : DROP COLUMN? ifExists? columnName (RESTRICT | CASCADE)?
    ;
addColumnSpecification
    : ADD COLUMN? ifNotExists? columnDefinition
    ;
ruleName
    : (owner DOT_)* name
    ;


attributeOptions
    : attributeOption (COMMA_ attributeOption)*
    ;

attributeOption
    : IDENTIFIER_ (EQ_ aExpr)?
    ;


tableConstraintUsingIndex
    : (CONSTRAINT constraintName)? (UNIQUE | primaryKey) USING INDEX indexName constraintOptionalParam
    ;

storageParameterWithValue
    : storageParameter EQ_ aExpr
    ;

storageParameter
    : IDENTIFIER_
    ;

//https://www.postgresql.org/docs/current/sql-alterdatabase.html
alterDatabase
    : ALTER DATABASE databaseName alterDatabaseClause
    ;

alterDatabaseClause
    : WITH? createdbOptItems
    | RENAME TO databaseName
    | OWNER TO roleSpec
    | SET TABLESPACE name
    | setResetClause
    ;

createdbOptItems
    : createdbOptItem+
    ;

createdbOptItem
    : createdbOptName EQ_? (signedIconst | booleanOrString | DEFAULT)
    ;

alterTableCmds
    : alterTableCmd (COMMA_ alterTableCmd)*
    ;
alterColumnOption
    : (SET DATA)? TYPE dataType collateClause? (USING aExpr)?
    | SET DEFAULT aExpr
    | DROP DEFAULT
    | (SET | DROP) NOT NULL
    | SET STATISTICS (signedIconst | DEFAULT)
    | (SET | RESET) LP_ attributeOptions RP_
    | SET STORAGE (PLAIN | EXTERNAL | EXTENDED | MAIN | DEFAULT)
    | alterGenericOptions
    | SET COMPRESSION (DEFAULT | identifier) //ALTER MATERIALIZED VIEW    //todo 以下未在 ALTER FOREIGN TABLE中
    | ADD GENERATED (ALWAYS | BY DEFAULT) AS IDENTITY parenthesizedSeqOptList?
    | alterIdentityColumnOptionList
    | DROP IDENTITY ifExists?
    | setData? TYPE typeName collateClause? alterUsing?
    | SET EXPRESSION AS LP_ aExpr RP_ //alter table
    | DROP EXPRESSION

//    | alterColumnSetOption alterColumnSetOption*
//    | DROP IDENTITY ifExists?
    ;
alterTableCmd
    : ADD COLUMN? ifNotExists? columnDefinition
    | DROP COLUMN? ifExists? columnName dropBehavior?
    | ALTER COLUMN? columnName alterColumnOption
    | ADD tableConstraint (NOT VALID)?
    | VALIDATE CONSTRAINT constraintName
    | DROP CONSTRAINT ifExists? constraintName dropBehavior?
    | (DISABLE | ENABLE) (ALWAYS|REPLICA)? TRIGGER (triggerName | ALL | USER)
    | SET WITHOUT OIDS
    | NO? INHERIT tableName
    | OWNER TO roleSpec
    | alterGenericOptions    //todo 以下未在 ALTER FOREIGN TABLE中
    | (SET | RESET) reloptions
    | CLUSTER ON indexName  //MATERIALIZED VIEW
    | SET WITHOUT CLUSTER
    | SET ACCESS METHOD accessMethod //--

    | ALTER COLUMN? NUMBER_ SET STATISTICS signedIconst
    | DROP COLUMN? ifExists colId dropBehavior?
    | DROP COLUMN? columnName dropBehavior?
    | ALTER CONSTRAINT name constraintAttributeSpec
    | SET WITHOUT CLUSTER
    | SET LOGGED
    | SET UNLOGGED
    | ENABLE RULE name
    | ENABLE ALWAYS RULE name
    | ENABLE REPLICA RULE name
    | DISABLE RULE name
    | NO INHERIT qualifiedName
    | OF anyName
    | NOT OF
    | SET TABLESPACE name
    | REPLICA IDENTITY replicaIdentity
    | ENABLE ROW LEVEL SECURITY
    | DISABLE ROW LEVEL SECURITY
    | FORCE ROW LEVEL SECURITY
    | NO FORCE ROW LEVEL SECURITY
    ;
triggerName
    : (owner DOT_)* name
    ;


constraintAttributeSpec
    : constraintAttributeElem+
    ;

constraintAttributeElem
    : NOT DEFERRABLE
    | DEFERRABLE
    | INITIALLY IMMEDIATE
    | INITIALLY DEFERRED
    | NOT VALID
    | NO INHERIT
    ;

alterGenericOptions
    : OPTIONS LP_ alterGenericOptionList RP_
    ;

alterGenericOptionList
    : alterGenericOptionElem (COMMA_ alterGenericOptionElem)*
    ;

alterGenericOptionElem
    : genericOptionElem
    | SET genericOptionElem
    | ADD genericOptionElem
    | DROP genericOptionName
    ;

genericOptionName
    : colLable
    ;

dropBehavior
    : CASCADE | RESTRICT
    ;

alterUsing
    : USING aExpr
    ;

setData
    : SET DATA
    ;

alterIdentityColumnOptionList
    : alterIdentityColumnOption+
    ;

alterIdentityColumnOption
    : RESTART
    | RESTART WITH? numericOnly
    | SET seqOptElem
    | SET GENERATED (ALWAYS | BY DEFAULT)
    ;

alterColumnDefault
    : SET DEFAULT aExpr
    | DROP DEFAULT
    ;
//https://www.postgresql.org/docs/current/sql-alteroperator.html
alterOperator
    : ALTER OPERATOR alterOperatorClauses
    ;
//https://www.postgresql.org/docs/current/sql-alteropclass.html
alterOperatorClass
    : ALTER OPERATOR CLASS anyName USING indexName alterOperatorClassClauses
    ;

alterOperatorClassClauses
    : RENAME TO indexName | SET SCHEMA schemaName | OWNER TO roleSpec
    ;
//https://www.postgresql.org/docs/current/sql-alteropfamily.html
alterOperatorFamily
    : ALTER OPERATOR FAMILY anyName USING indexName alterOperatorFamilyClauses
    ;

alterOperatorFamilyClauses
    : (ADD | DROP) opclassItemList
    | alterOperatorClassClauses
    ;

opclassItemList
    : opclassItem (COMMA_ opclassItem)*
    ;

opclassItem
    : OPERATOR NUMBER_ anyOperator operArgtypes? opclassPurpose? RECHECK?
    | FUNCTION NUMBER_ (LP_ typeList RP_)? functionWithArgtypes?
    | STORAGE typeName
    ;

opclassPurpose
    : FOR SEARCH | FOR ORDER BY anyName
    ;

alterOperatorClauses
    : operatorWithArgtypes SET SCHEMA schemaName
    | operatorWithArgtypes SET LP_ operatorDefList RP_
    | operatorWithArgtypes OWNER TO roleSpec
    ;

operatorDefList
    : operatorDefElem (COMMA_ operatorDefElem)*
    ;

operatorDefElem
    : (RESTRICT | JOIN | COMMUTATOR | NEGATOR) EQ_ (NONE | operatorDefArg)
    | HASHES
    | MERGES
    ;

operatorDefArg
    : funcType
    | reservedKeyword
    | qualAllOp
    | numericOnly
    | STRING_
    ;

operatorWithArgtypes
    : anyOperator? operArgtypes
    ;
//https://www.postgresql.org/docs/current/sql-alteraggregate.html
alterAggregate
    : ALTER AGGREGATE aggregateSignature alterAggregateDefinitionClause
    ;

aggregateSignature
    : aggrName aggrArgs
    ;

aggrArgs
    : LP_ ASTERISK_ RP_
    | LP_ aggrArgsList RP_
    | LP_ aggrArgsList? ORDER BY aggrArgsList RP_
    ;

aggrArgsList
    : aggrArg (COMMA_ aggrArg)*
    ;

aggrArg
    : funcArg
    ;

alterAggregateDefinitionClause
    : RENAME TO name
    | OWNER TO  roleSpec
    | SET SCHEMA schemaName
    ;
//https://www.postgresql.org/docs/current/sql-altercollation.html
alterCollation
    : ALTER COLLATION collationName alterCollationClause
    ;

alterCollationClause
    : REFRESH VERSION
    | RENAME TO collationName
    | OWNER TO roleSpec
    | SET SCHEMA schemaName
    ;
//https://www.postgresql.org/docs/current/sql-alterconversion.html
alterConversion
    : ALTER CONVERSION conversionName alterConversionClause
    ;
conversionName
    : (owner DOT_)* name
    ;
alterConversionClause
    : RENAME TO conversionName
    | OWNER TO roleSpec
    | SET SCHEMA schemaName
    ;
//https://www.postgresql.org/docs/current/sql-alterdefaultprivileges.html
alterDefaultPrivileges
    : ALTER DEFAULT PRIVILEGES (defACLOption defACLOption?)?  abbreviatedGrantOrRevoke
    ;

abbreviatedGrantOrRevoke
    : GRANT privilegeTypes ON defaclPrivilegeTarget TO granteeList grantGrantOption?
    | REVOKE privilegeTypes ON defaclPrivilegeTarget FROM granteeList dropBehavior?
    | REVOKE GRANT OPTION FOR privilegeTypes ON defaclPrivilegeTarget FROM granteeList dropBehavior?
    ;

grantGrantOption
    : WITH GRANT OPTION
    ;

granteeList
    : roleSpec (COMMA_ roleSpec)*
    ;


defaclPrivilegeTarget
    : TABLES
    | SEQUENCES
    | FUNCTIONS
    | ROUTINES
    | TYPES
    | SCHEMAS
    ;

defACLOption
    : IN SCHEMA schemaNameList
    | FOR (ROLE | USER) roleList
    ;

schemaNameList
    : schemaName (COMMA_ schemaName)*
    ;
//https://www.postgresql.org/docs/current/sql-alterdomain.html
alterDomain
    : ALTER DOMAIN domainName alterDomainClause
    ;

alterDomainClause
    : alterColumnDefault
    | (SET | DROP) NOT NULL
    | ADD domainConstraint (NOT VALID)?
    | DROP CONSTRAINT ifExists? constraintName dropBehavior?
    | VALIDATE CONSTRAINT constraintName
    | RENAME CONSTRAINT oldConstraintName = constraintName TO newConstraintName = constraintName
    | OWNER TO roleSpec
    | RENAME TO domainName
    | SET SCHEMA schemaName
    ;
domainConstraint
    : constraintClause (NOT NULL | checkOption)
    ;
//https://www.postgresql.org/docs/current/sql-altereventtrigger.html
alterEventTrigger
    : ALTER EVENT TRIGGER tiggerName alterEventTriggerClause
    ;

alterEventTriggerClause
    : DISABLE
    | ENABLE (REPLICA | ALWAYS)
    | OWNER TO roleSpec
    | RENAME TO tiggerName
    ;

tiggerName
    : colId
    ;
//https://www.postgresql.org/docs/current/sql-alterextension.html
alterExtension
    : ALTER EXTENSION extensionName alterExtensionClauses
    ;
extensionName
    : (owner DOT_)* name
    ;
alterExtensionClauses
    : UPDATE (TO stringLiterals)?
    | (ADD | DROP) memberObject
    ;
memberObject
    : ACCESS METHOD objectName
    | AGGREGATE aggregateWithArgtypes
    | CAST LP_ sourceType = typeName AS targetType = typeName RP_
    | COLLATION objectName
    | CONVERSION objectName
    | DOMAIN objectName
    | EVENT TRIGGER objectName
    | FOREIGN DATA WRAPPER objectName
    | FOREIGN TABLE objectName
    | MATERIALIZED VIEW objectName
    | FUNCTION functionWithArgtypes
    | OPERATOR operatorWithArgtypes

    | (ADD | DROP) ACCESS METHOD anyName
    | (ADD | DROP) AGGREGATE aggregateWithArgtypes
    | (ADD | DROP) CAST LP_ typeName AS typeName RP_
    | (ADD | DROP) COLLATION anyName
    | (ADD | DROP)
    | (ADD | DROP)
    | (ADD | DROP)
    | (ADD | DROP) PROCEDURAL? LANGUAGE anyName
    | (ADD | DROP) OPERATOR operatorWithArgtypes
    | (ADD | DROP) OPERATOR (CLASS | FAMILY) anyName USING accessMethod
    | (ADD | DROP) PROCEDURE functionWithArgtypes
    | (ADD | DROP) ROUTINE functionWithArgtypes
    | (ADD | DROP) SCHEMA anyName
    | (ADD | DROP)
    | (ADD | DROP) TABLE anyName
    | (ADD | DROP) TEXT SEARCH PARSER anyName
    | (ADD | DROP) TEXT SEARCH DICTIONARY anyName
    | (ADD | DROP) TEXT SEARCH TEMPLATE anyName
    | (ADD | DROP) TEXT SEARCH CONFIGURATION anyName
    | (ADD | DROP) SEQUENCE anyName
    | (ADD | DROP) VIEW anyName
    | (ADD | DROP)
    | (ADD | DROP)
    | (ADD | DROP)
    | (ADD | DROP) SERVER anyName
    | (ADD | DROP) TRANSFORM FOR typeName LANGUAGE name
    | (ADD | DROP) TYPE typeName
    | SET SCHEMA schemaName
    ;

functionWithArgtypes
    : funcName funcArgs?
    | funcName indirection
    ;

funcArgs
    : LP_ funcArgsList RP_
    | LP_ RP_
    ;

aggregateWithArgtypes
    : funcName aggrArgs
    ;




//https://www.postgresql.org/docs/current/sql-alterforeigndatawrapper.html
alterForeignDataWrapper
    : ALTER FOREIGN DATA WRAPPER name alterForeignDataWrapperClauses
    ;

alterForeignDataWrapperClauses
    : fdwOptions? alterGenericOptions
    | fdwOptions
    | RENAME TO name
    | OWNER TO roleSpec
    ;

genericOptionElem
    : genericOptionName genericOptionArg
    ;

genericOptionArg
    : aexprConst
    ;

fdwOptions
    : fdwOption+
    ;

fdwOption
    : HANDLER handlerName
    | NO HANDLER
    | VALIDATOR handlerName
    | NO VALIDATOR
    ;

handlerName
    : (owner DOT_)* name
    ;
//https://www.postgresql.org/docs/current/sql-altergroup.html
alterGroup
    : ALTER GROUP alterGroupClauses
    ;

alterGroupClauses
    : roleSpec (ADD|DROP) USER roleList
    | roleSpec RENAME TO roleName
    ;
//https://www.postgresql.org/docs/current/sql-alterlanguage.html
alterLanguage
    : ALTER PROCEDURAL? LANGUAGE langName (
        RENAME TO newName=langName
        | OWNER TO roleSpec)
    ;
//https://www.postgresql.org/docs/current/sql-alterlargeobject.html
alterLargeObject
    : ALTER LARGE OBJECT numericOnly OWNER TO roleSpec
    ;
//https://www.postgresql.org/docs/current/sql-altermaterializedview.html
alterMaterializedView
    : ALTER MATERIALIZED VIEW ifExists? (ALL IN TABLESPACE)? viewName alterMaterializedViewClauses
    ;

alterMaterializedViewClauses
    : alterTableCmds
    | NO? DEPENDS ON EXTENSION name
    | RENAME COLUMN? columnName TO columnName
    | RENAME TO qualifiedName
    | SET SCHEMA schemaName
    | (OWNED BY roleList)? SET TABLESPACE name NOWAIT?
    ;
//https://www.postgresql.org/docs/current/sql-execute.html
executeStmt
    : EXECUTE name executeParamClause?
    ;
//https://www.postgresql.org/docs/current/sql-creatematerializedview.html
createMaterializedView
    : CREATE UNLOGGED? MATERIALIZED VIEW ifNotExists? tableName createMvTarget AS select (WITH DATA | WITH NO DATA)?
    ;

createMvTarget
    : columnNames? tableAccessMethodClause? (WITH reloptions)? (TABLESPACE tablespaceName)?
    ;
//https://www.postgresql.org/docs/current/sql-alterpolicy.html
alterPolicy
    : ALTER POLICY name ON tableName alterPolicyClauses
    ;

alterPolicyClauses
    : (TO roleList)? (USING LP_ aExpr RP_)? (WITH CHECK LP_ aExpr RP_)?
    | RENAME TO name
    ;


//https://www.postgresql.org/docs/current/sql-alterprocedure.html
alterProcedure
    : ALTER PROCEDURE functionWithArgtypes alterProcedureClauses
    ;

alterProcedureClauses
    : alterfuncOptList RESTRICT?
    | RENAME TO name
    | NO? DEPENDS ON EXTENSION name
    | SET SCHEMA name
    | OWNER TO roleSpec
    ;

alterfuncOptList
    : commonFuncOptItem+
    ;
//https://www.postgresql.org/docs/current/sql-alterfunction.html
alterFunction
    : ALTER FUNCTION functionWithArgtypes alterFunctionClauses
    ;

alterFunctionClauses
    : alterfuncOptList RESTRICT?
    | RENAME TO funcName
    | OWNER TO roleSpec
    | SET SCHEMA name
    | NO? DEPENDS ON EXTENSION extensionName
    ;
//https://www.postgresql.org/docs/current/sql-alterpublication.html
alterPublication
    : ALTER PUBLICATION publicationNameItem
    ( RENAME TO name
    | OWNER TO roleSpec
    | SET definition
    | (ADD | SET | DROP) publicationObject (COMMA_ publicationObject)*
    )
    ;
publicationObject
    : TABLE  ONLY? tableName ASTERISK_?  columnNames? whereClause? (COMMA_ tableName ASTERISK_?  columnNames? whereClause?)*
    | TABLES IN SCHEMA (schemaName | CURRENT_SCHEMA) (COMMA_ (schemaName | CURRENT_SCHEMA))*
    ;
//    https://www.postgresql.org/docs/current/sql-alterroutine.html
alterRoutine
    : ALTER ROUTINE functionWithArgtypes alterProcedureClauses
    ;
//https://www.postgresql.org/docs/current/sql-alterrule.html
alterRule
    : ALTER RULE roleName ON tableName RENAME TO newName=roleName
    ;
//https://www.postgresql.org/docs/current/sql-altersequence.html
alterSequence
    : ALTER SEQUENCE ifExists? sequenceName alterSequenceClauses
    ;

alterSequenceClauses
    : OWNER TO roleSpec
    | seqOptList
    | RENAME TO sequenceName
    | SET SCHEMA schemaName
    ;
//https://www.postgresql.org/docs/current/sql-alterserver.html
alterServer
    : ALTER SERVER serverName
    ( foreignServerVersion? alterGenericOptions
    | foreignServerVersion alterGenericOptions?
    | RENAME TO newName=serverName
    | OWNER TO roleSpec)
    ;

foreignServerVersion
    : VERSION (STRING_ | NULL)
    ;
//https://www.postgresql.org/docs/current/sql-alterstatistics.html
alterStatistics
    : ALTER STATISTICS
    ( ifExists? anyName SET STATISTICS (signedIconst|DEFAULT)
    | anyName RENAME TO newName=anyName
    | anyName SET SCHEMA schemaName
    | anyName OWNER TO roleSpec )
    ;
//https://www.postgresql.org/docs/current/sql-altersubscription.html
alterSubscription
    : ALTER SUBSCRIPTION anyName
    ( RENAME TO newName=anyName
    | OWNER TO roleSpec
    | SET definition
    | CONNECTION STRING_
    | REFRESH PUBLICATION (WITH definition)?
    | (SET | ADD | DROP) PUBLICATION publicationNameList (WITH definition)?
    | W_SKIP LP_ attributeOption RP_
    | (ENABLE | DISABLE))
    ;

publicationNameList
    : publicationNameItem (COMMA_ publicationNameItem)*
    ;

publicationNameItem
    : identifier
    | unreservedWord
    | colNameKeyword
    | typeFuncNameKeyword
    | reservedKeyword
    ;
//https://www.postgresql.org/docs/current/sql-altersystem.html
alterSystem
    : ALTER SYSTEM (SET genericSet | RESET genericReset)
    ;
//https://www.postgresql.org/docs/current/sql-altertablespace.html
alterTablespace
    : ALTER TABLESPACE tablespaceName
    ( (SET|RESET) reloptions
    | RENAME TO newName=tablespaceName
    | OWNER TO roleSpec)
    ;
//https://www.postgresql.org/docs/current/sql-altertsconfig.html
alterTextSearchConfiguration
    : ALTER TEXT SEARCH CONFIGURATION anyName alterTextSearchConfigurationClauses
    ;

alterTextSearchConfigurationClauses
    : RENAME TO newName=anyName
    | SET SCHEMA schemaName
    | OWNER TO roleSpec
    | (ADD | ALTER) MAPPING FOR nameList WITH? anyNameList
    | ALTER MAPPING (FOR typeList)? REPLACE anyName WITH newName=anyName
    | DROP MAPPING ifExists? FOR typeList
    ;

anyNameList
    : anyName (COMMA_ anyName)*
    ;
//https://www.postgresql.org/docs/current/sql-altertsdictionary.html
alterTextSearchDictionary
    : ALTER TEXT SEARCH DICTIONARY anyName
    ( RENAME TO newName=anyName
    | SET SCHEMA schemaName
    | OWNER TO roleSpec
    | definition)
    ;
//https://www.postgresql.org/docs/current/sql-altertsparser.html
alterTextSearchParser
    : ALTER TEXT SEARCH PARSER anyName (RENAME TO newName=anyName | SET SCHEMA schemaName)
    ;
//https://www.postgresql.org/docs/current/sql-altertstemplate.html
alterTextSearchTemplate
    : ALTER TEXT SEARCH TEMPLATE anyName (RENAME TO newName=anyName | SET SCHEMA schemaName)
    ;
//https://www.postgresql.org/docs/current/sql-altertrigger.html
alterTrigger
    : ALTER TRIGGER triggerName ON tableName (RENAME TO newName=triggerName | NO? DEPENDS ON EXTENSION extensionName)
    ;
//https://www.postgresql.org/docs/current/sql-altertype.html
alterType
    : ALTER TYPE anyName alterTypeClauses
    ;

alterTypeClauses
    : alterTypeCmds
    | ADD VALUE ifNotExists? newEnumValue=STRING_ ((BEFORE | AFTER) neighborEnumValue=STRING_)?
    | RENAME VALUE STRING_ TO STRING_
    | RENAME TO anyName
    | RENAME ATTRIBUTE attrName TO newName=attrName dropBehavior?
    | SET SCHEMA schemaName
    | SET LP_ typeDefList RP_
    | OWNER TO roleSpec
    ;

alterTypeCmds
    : alterTypeCmd (COMMA_ alterTypeCmd)?
    ;

alterTypeCmd
    : ADD ATTRIBUTE tableFuncElement dropBehavior?
    | DROP ATTRIBUTE ifExists? attrName dropBehavior?
    | ALTER ATTRIBUTE attrName setData? TYPE dataType collateClause? dropBehavior?
    ;

typeDefList
    : typeDefElem (COMMA_ typeDefElem)*
    ;

typeDefElem
    : (RECEIVE | SEND | TYPMOD_IN | TYPMOD_OUT | ANALYZE | SUBSCRIPT | STORAGE) EQ_ (NONE | typeDefArg)
    ;

typeDefArg
    : funcType
    | reservedKeyword
    | qualAllOp
    | numericOnly
    | STRING_
    ;
//https://www.postgresql.org/docs/current/sql-alterusermapping.html
alterUserMapping
    : ALTER USER MAPPING FOR authIdent SERVER serverName alterGenericOptions
    ;

authIdent
    : roleSpec | USER | userName
    ;
//https://www.postgresql.org/docs/current/sql-alterview.html
alterView
    : ALTER VIEW ifExists? viewName alterViewClauses
    ;

alterViewClauses
    : alterTableCmds                                    #alterViewCmds
    | RENAME TO viewName                                #alterRenameView
    | RENAME COLUMN? columnName TO newName=columnName   #alterRenameColumn
    | SET SCHEMA schemaName                                   #alterSetSchema
    ;
//https://www.postgresql.org/docs/current/sql-close.html
close
    : CLOSE (cursorName | ALL)
    ;
//https://www.postgresql.org/docs/current/sql-cluster.html
cluster
    : CLUSTER clusterVerboseSpecification? tableName? clusterIndexSpecification?
    | CLUSTER indexName ON tableName
    ;

clusterVerboseSpecification
    : VERBOSE | clusterVerboseOptionList
    ;

clusterIndexSpecification
    : USING indexName
    ;

clusterVerboseOptionList
    : LP_ clusterVerboseOption (COMMA_ clusterVerboseOption)* RP_
    ;

clusterVerboseOption
    : VERBOSE booleanValue?
    ;
//https://www.postgresql.org/docs/current/sql-comment.html
comment
    : COMMENT ON commentClauses
    ;

commentClauses
    : objectTypeAnyName objectName IS commentText
    | COLUMN columnName IS commentText
    | objectTypeName objectName IS commentText
    | TYPE objectName IS commentText
    | DOMAIN objectName IS commentText
    | AGGREGATE aggregateWithArgtypes IS commentText
    | FUNCTION functionWithArgtypes IS commentText
    | OPERATOR operatorWithArgtypes IS commentText
    | CONSTRAINT constraintName ON DOMAIN domainName IS commentText
    | objectTypeNameOnAnyName anyName ON tableName IS commentText
    | PROCEDURE functionWithArgtypes IS commentText
    | ROUTINE functionWithArgtypes IS commentText
    | TRANSFORM FOR typeName LANGUAGE name IS commentText
    | OPERATOR CLASS anyName USING name IS commentText
    | OPERATOR FAMILY anyName USING name IS commentText
    | LARGE OBJECT numericOnly IS commentText
    | CAST LP_ typeName AS targetType=typeName RP_ IS commentText
    ;

objectTypeNameOnAnyName
    : POLICY | RULE	| TRIGGER | CONSTRAINT
    ;

objectTypeName
    : dropTypeName
    | DATABASE
    | ROLE
    | SUBSCRIPTION
    | TABLESPACE
    ;

dropTypeName
    : ACCESS METHOD
    | EVENT TRIGGER
    | EXTENSION
    | FOREIGN DATA WRAPPER
    | PROCEDURAL? LANGUAGE
    | PUBLICATION
    | SCHEMA
    | SERVER
    ;

objectTypeAnyName
    : TABLE
    | DATABASE
    | DOMAIN
    | EVENT TRIGGER
    | FOREIGN TABLE
    | MATERIALIZED VIEW
    | PROCEDURAL? LANGUAGE
    | PUBLICATION
    | ROLE
    | ROUTINE
    | SCHEMA
    | SEQUENCE
    | SUBSCRIPTION
    | TABLESPACE
    | TYPE
    | VIEW
    | INDEX
    | FOREIGN TABLE
    | COLLATION
    | CONVERSION
    | STATISTICS
    | TEXT SEARCH PARSER
    | TEXT SEARCH DICTIONARY
    | TEXT SEARCH TEMPLATE
    | TEXT SEARCH CONFIGURATION
    ;

commentText
    : STRING_ | NULL
    ;
//https://www.postgresql.org/docs/current/sql-create-access-method.html
createAccessMethod
    : CREATE ACCESS METHOD accessMethod TYPE (INDEX|TABLE) HANDLER handlerName
    ;
//https://www.postgresql.org/docs/current/sql-createaggregate.html
createAggregate
    : CREATE (OR REPLACE)? AGGREGATE aggrName (aggrArgs definition | oldAggrDefinition)
    ;

oldAggrDefinition
    : LP_ oldAggrList RP_
    ;

oldAggrList
    : oldAggrElem (COMMA_ oldAggrElem)*
    ;

oldAggrElem
    : identifier EQ_ defArg
    ;
//https://www.postgresql.org/docs/current/sql-createcast.html
createCast
    : CREATE CAST LP_ typeName AS targetType=typeName RP_
    ( WITH FUNCTION functionWithArgtypes castContext?
    | WITHOUT FUNCTION castContext?
    | WITH INOUT castContext?)
    ;

castContext
    : AS IMPLICIT | AS ASSIGNMENT
    ;
//https://www.postgresql.org/docs/current/sql-createcollation.html
createCollation
    : CREATE COLLATION ifNotExists? collationName (definition | FROM anyName)
    ;
//https://www.postgresql.org/docs/current/sql-createconversion.html
createConversion
    : CREATE DEFAULT? CONVERSION conversionName FOR STRING_ TO destEncoding=STRING_ FROM funcName
    ;
//https://www.postgresql.org/docs/current/sql-createdomain.html
createDomain
    : CREATE DOMAIN domainName AS? dataType colQualList
    ;
//https://www.postgresql.org/docs/current/sql-createeventtrigger.html
createEventTrigger
    : CREATE EVENT TRIGGER triggerName ON colLabel (WHEN eventTriggerWhenList)? EXECUTE (FUNCTION | PROCEDURE) funcName LP_ RP_
    ;

eventTriggerWhenList
    : eventTriggerWhenItem (AND eventTriggerWhenItem)*
    ;

eventTriggerWhenItem
    : colId IN LP_ eventTriggerValueList RP_
    ;

eventTriggerValueList
    : STRING_ (COMMA_ STRING_)*
    ;
//https://www.postgresql.org/docs/current/sql-createextension.html
createExtension
    : CREATE EXTENSION ifNotExists? extensionName WITH? createExtensionOptList
    ;

createExtensionOptList
    : createExtensionOptItem*
    ;

createExtensionOptItem
    : SCHEMA schemaName
    | VERSION nonReservedWordOrSconst
    | FROM nonReservedWordOrSconst
    | CASCADE
    ;
//https://www.postgresql.org/docs/current/sql-createforeigndatawrapper.html
createForeignDataWrapper
    : CREATE FOREIGN DATA WRAPPER anyName fdwOptions? createGenericOptions?
    ;
//https://www.postgresql.org/docs/current/sql-createforeigntable.html
createForeignTable
    : CREATE FOREIGN TABLE ifNotExists? tableName createForeignTableClauses
    ;

createForeignTableClauses
    : LP_ tableElementList? RP_
      (INHERITS LP_ qualifiedNameList RP_)? SERVER name createGenericOptions?
    | PARTITION OF qualifiedName (LP_ typedTableElementList RP_)? partitionBoundSpec
      SERVER name createGenericOptions?
    ;

tableElementList
    : tableElement (COMMA_ tableElement)*
    ;

tableElement
    : columnDef	| tableLikeClause | tableConstraint
    ;

tableLikeClause
    : LIKE qualifiedName tableLikeOptionList
    ;

tableLikeOptionList
    : tableLikeOptionList (INCLUDING | EXCLUDING) tableLikeOption |
    ;

tableLikeOption
    : COMMENTS
    | CONSTRAINTS
    | DEFAULTS
    | IDENTITY
    | GENERATED
    | INDEXES
    | STATISTICS
    | STORAGE
    | ALL
    ;
//https://www.postgresql.org/docs/current/sql-createfunction.html
createFunction
    : CREATE (OR REPLACE)? FUNCTION funcName funcArgsWithDefaults
    ( RETURNS funcType
    | RETURNS TABLE LP_ tableFuncColumnList RP_)? createfuncOptList (RETURN aExpr)?
    ;

tableFuncColumnList
    : tableFuncColumn (COMMA_ tableFuncColumn)*
    ;

tableFuncColumn
    : paramName funcType
    ;

createfuncOptList
    : createfuncOptItem+
    ;

createfuncOptItem
    : AS funcAs
    | LANGUAGE langName
    | TRANSFORM transformTypeList
    | WINDOW
    | commonFuncOptItem
    | stmt
    ;

transformTypeList
    : FOR TYPE typeName (COMMA_ FOR TYPE typeName)
    ;

funcAs
//    : identifier | STRING_ (COMMA_ identifier|STRING_)?
//    : sconst    /*| AS 'obj_file', 'link_symbol'*/
    : sconst (COMMA_ sconst)*
    ;
sconst
    : anysconst opt_uescape
    ;

anysconst
    : BEGIN_DOLLAR_STRING_CONSTANT DOLLAR_TEXT* END_DOLLAR_STRING_CONSTANT
    | STRING_
    ;

opt_uescape
    : UESCAPE anysconst
    |
    ;


funcArgsWithDefaults
    : LP_ funcArgsWithDefaultsList? RP_
    ;

funcArgsWithDefaultsList
    : funcArgWithDefault (COMMA_ funcArgWithDefault)*
    ;

funcArgWithDefault
    : funcArg
    | funcArg DEFAULT aExpr
    | funcArg EQ_ aExpr
    ;
//https://www.postgresql.org/docs/current/sql-createlanguage.html
createLanguage
    : CREATE (OR REPLACE)? TRUSTED? PROCEDURAL? LANGUAGE langName
    ( HANDLER handlerName (INLINE inlineHandler=handlerName)? validatorClause?
    | LP_ transformElementList RP_)?    //原G4， 最新文档不存在
    ;

transformElementList
    : FROM SQL WITH FUNCTION functionWithArgtypes COMMA_ (TO | FROM) SQL WITH FUNCTION functionWithArgtypes
    | (TO | FROM) SQL WITH FUNCTION functionWithArgtypes
    ;

validatorClause
    : VALIDATOR handlerName	| NO VALIDATOR
    ;
//https://www.postgresql.org/docs/current/sql-createpolicy.html
createPolicy
    : CREATE POLICY anyName ON tableName (AS (PERMISSIVE | RESTRICTIVE))?
      (FOR rowSecurityCmd)? (TO roleList)?
      (USING LP_ aExpr RP_)? (WITH CHECK LP_ aExpr RP_)?
    ;
//https://www.postgresql.org/docs/current/sql-createprocedure.html
createProcedure
    : CREATE (OR REPLACE)? PROCEDURE funcName funcArgsWithDefaults createfuncOptList
    ;
//https://www.postgresql.org/docs/current/sql-createpublication.html
createPublication
    : CREATE PUBLICATION publicationNameItem publicationForTables?	(WITH definition)?
    ;

publicationForTables
    : FOR ALL TABLES
    | FOR publicationObject (COMMA_ publicationObject)*
    ;
//https://www.postgresql.org/docs/current/sql-createrule.html
createRule
    : CREATE (OR REPLACE)? RULE ruleName AS ON event TO tableName (WHERE aExpr)?
      DO (INSTEAD | ALSO)? ruleActionList
    ;

ruleActionList
    : NOTHING
    | ruleActionStmt
    | LP_ ruleActionMulti RP_
    ;

ruleActionStmt
    : select
    | insert
    | update
    | delete
    | notifyStmt
    ;

ruleActionMulti
    : ruleActionStmt? (SEMI_ ruleActionStmt?)*
    ;
//https://www.postgresql.org/docs/current/sql-createtrigger.html
createTrigger
    : CREATE (OR REPLACE)? CONSTRAINT? TRIGGER triggerName triggerActionTime triggerEvents ON tableName (FROM referencedTableName = tableName)? constraintAttributeSpec? triggerReferencing? triggerForSpec? triggerWhen? EXECUTE (FUNCTION | PROCEDURE) funcName LP_ triggerFuncArgs? RP_
    | CREATE (OR REPLACE)? CONSTRAINT TRIGGER (FROM qualifiedName)? constraintAttributeSpec FOR EACH ROW triggerWhen EXECUTE (FUNCTION | PROCEDURE) funcName LP_ triggerFuncArgs RP_
    ;

triggerEvents
    : triggerOneEvent (OR triggerOneEvent)*
    ;

triggerOneEvent
    : INSERT
    | DELETE
    | UPDATE
    | UPDATE OF columnList
    | TRUNCATE
    ;

triggerActionTime
    : BEFORE | AFTER | INSTEAD OF
    ;

triggerFuncArgs
    : triggerFuncArg (COMMA_ triggerFuncArg)*
    ;

triggerFuncArg
    : NUMBER_ | STRING_ | colLabel
    ;

triggerWhen
    : WHEN LP_ aExpr RP_
    ;

triggerForSpec
    : FOR EACH? (ROW | STATEMENT)
    ;

triggerReferencing
    : REFERENCING triggerTransitions
    ;

triggerTransitions
    : triggerTransition+
    ;

triggerTransition
    : transitionOldOrNew transitionRowOrTable AS? transitionRelName
    ;

transitionRelName
    : identifier
    ;

transitionRowOrTable
    : TABLE | ROW
    ;

transitionOldOrNew
    : OLD | NEW
    ;
//https://www.postgresql.org/docs/current/sql-createsequence.html
createSequence
    : CREATE tempOption? SEQUENCE ifNotExists? sequenceName seqOptList?
    ;

tempOption
    : ((LOCAL | GLOBAL)? (TEMPORARY | TEMP)) | UNLOGGED
    ;
//https://www.postgresql.org/docs/current/sql-createserver.html
createServer
    : CREATE SERVER ifNotExists? serverName (TYPE STRING_)? foreignServerVersion? FOREIGN DATA WRAPPER fdwName createGenericOptions
    ;
//https://www.postgresql.org/docs/current/sql-createstatistics.html
createStatistics
    : CREATE STATISTICS ifNotExists? anyName optNameList ON exprList FROM tableName
    ;
//https://www.postgresql.org/docs/current/sql-createsubscription.html
createSubscription
    : CREATE SUBSCRIPTION anyName CONNECTION STRING_ PUBLICATION publicationNameList (WITH definition)?
    ;
//https://www.postgresql.org/docs/current/sql-createtablespace.html
createTablespace
    : CREATE TABLESPACE tablespaceName (OWNER roleSpec)? LOCATION STRING_ (WITH reloptions)?
    ;
//https://www.postgresql.org/docs/current/sql-createtsconfig.html
//https://www.postgresql.org/docs/current/sql-createtsdictionary.html
//https://www.postgresql.org/docs/current/sql-createtsparser.html
createTextSearch
    : CREATE TEXT SEARCH (CONFIGURATION | DICTIONARY | PARSER | TEMPLATE) anyName definition
    ;
//https://www.postgresql.org/docs/current/sql-createtransform.html
createTransform
    : CREATE (OR REPLACE)? TRANSFORM FOR typeName LANGUAGE langName LP_ transformElementList RP_
    ;
//https://www.postgresql.org/docs/current/sql-createtype.html
createType
    : CREATE TYPE typeName createTypeClauses?
    ;

createTypeClauses
    : definition
    | AS LP_ tableFuncElementList? RP_
    | AS ENUM LP_ enumValList? RP_
    | AS RANGE definition
    ;

enumValList
    : STRING_ (COMMA_ STRING_)*
    ;
//https://www.postgresql.org/docs/current/sql-createusermapping.html
createUserMapping
    : CREATE USER MAPPING ifNotExists? FOR authIdent SERVER serverName createGenericOptions
    ;
//https://www.postgresql.org/docs/current/sql-discard.html
discard
    : DISCARD (ALL | PLANS | SEQUENCES | TEMPORARY | TEMP)
    ;
//https://www.postgresql.org/docs/current/sql-drop-access-method.html
dropAccessMethod
    : DROP ACCESS METHOD ifExists? anyName dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropaggregate.html
dropAggregate
    : DROP AGGREGATE ifExists? aggregateWithArgtypesList dropBehavior?
    ;

aggregateWithArgtypesList
    : aggregateWithArgtypes (COMMA_ aggregateWithArgtypes)*
    ;
//https://www.postgresql.org/docs/current/sql-dropcast.html
dropCast
    : DROP CAST ifExists? LP_ typeName AS targetType=typeName RP_ dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropcollation.html
dropCollation
    : DROP COLLATION ifExists? collationName dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropconversion.html
dropConversion
    : DROP CONVERSION ifExists? conversionName dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropdomain.html
dropDomain
    : DROP DOMAIN ifExists? nameList dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropeventtrigger.html
dropEventTrigger
    : DROP EVENT TRIGGER ifExists? triggerName dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropextension.html
dropExtension
    : DROP EXTENSION ifExists? nameList dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropforeigndatawrapper.html
dropForeignDataWrapper
    : DROP FOREIGN DATA WRAPPER ifExists? nameList dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropforeigntable.html
dropForeignTable
    : DROP FOREIGN TABLE ifExists? tableName (COMMA_ tableName)* dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropforeigntable.html
dropFunction
    : DROP FUNCTION ifExists? functionWithArgtypesList dropBehavior?
    ;

functionWithArgtypesList
    : functionWithArgtypes (COMMA_ functionWithArgtypes)*
    ;
//https://www.postgresql.org/docs/current/sql-droplanguage.html
dropLanguage
    : DROP PROCEDURAL? LANGUAGE ifExists? langName dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropmaterializedview.html
dropMaterializedView
    : DROP MATERIALIZED VIEW ifExists? viewName (COMMA_ viewName)* dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropoperator.html
dropOperator
    : DROP OPERATOR ifExists? operatorWithArgtypesList dropBehavior?
    ;

operatorWithArgtypesList
    : operatorWithArgtypes (COMMA_ operatorWithArgtypes)*
    ;
//https://www.postgresql.org/docs/current/sql-dropopclass.html
dropOperatorClass
    : DROP OPERATOR CLASS ifExists? anyName USING indexName dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropopfamily.html
dropOperatorFamily
    : DROP OPERATOR FAMILY ifExists? anyName USING indexName dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-drop-owned.html
dropOwned
    : DROP OWNED BY roleList dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-droppolicy.html
dropPolicy
    : DROP POLICY ifExists? name ON tableName dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropprocedure.html
dropProcedure
    : DROP PROCEDURE ifExists? functionWithArgtypesList dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-droppublication.html
dropPublication
    : DROP PUBLICATION ifExists? anyNameList dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-droproutine.html
dropRoutine
    : DROP ROUTINE ifExists? functionWithArgtypesList dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-droprule.html
dropRule
    : DROP RULE ifExists? ruleName ON tableName dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropsequence.html
dropSequence
    : DROP SEQUENCE ifExists? sequenceName (COMMA_ sequenceName)* dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropserver.html
dropServer
    : DROP SERVER ifExists? serverName (COMMA_ serverName) dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropstatistics.html
dropStatistics
    : DROP STATISTICS ifExists? anyNameList dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropsubscription.html
dropSubscription
    : DROP SUBSCRIPTION ifExists? anyNameList dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-droptablespace.html
dropTablespace
    : DROP TABLESPACE ifExists? tablespaceName
    ;
//https://www.postgresql.org/docs/current/sql-droptsconfig.html
//https://www.postgresql.org/docs/current/sql-droptsdictionary.html
//https://www.postgresql.org/docs/current/sql-droptsparser.html
//https://www.postgresql.org/docs/current/sql-droptstemplate.html
dropTextSearch
    : DROP TEXT SEARCH (CONFIGURATION | DICTIONARY | PARSER | TEMPLATE) ifExists? anyName dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-droptransform.html
dropTransform
    : DROP TRANSFORM ifExists? FOR typeName LANGUAGE langName dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-droptrigger.html
dropTrigger
    : DROP TRIGGER ifExists? tiggerName ON tableName dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-droptype.html
dropType
    : DROP TYPE ifExists? typeName (COMMA_ typeName)* dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-dropusermapping.html
dropUserMapping
    : DROP USER MAPPING ifExists? FOR authIdent SERVER serverName
    ;
//https://www.postgresql.org/docs/current/sql-dropview.html
dropView
    : DROP VIEW ifExists? viewName (COMMA_ viewName)* dropBehavior?
    ;
//https://www.postgresql.org/docs/current/sql-importforeignschema.html
importForeignSchema
    : IMPORT FOREIGN SCHEMA schemaName importQualification? FROM SERVER serverName INTO localSchema=schemaName createGenericOptions?
    ;

importQualification
    : importQualificationType LP_ tableName (COMMA_ tableName)* RP_
    ;

importQualificationType
    : LIMIT TO | EXCEPT
    ;

//https://www.postgresql.org/docs/current/sql-declare.html
declare
    : DECLARE cursorName cursorOption CURSOR ((WITH | WITHOUT) HOLD)? FOR select
    ;

cursorOption
    : BINARY? (ASENSITIVE | INSENSITIVE)? (NO? SCROLL)?
    ;

open
    : OPEN cursorName (usingValueClause | usingSqlDescriptorClause)?
    ;

usingValueClause
    : USING value (COMMA_ value)*
    ;

value
    : aexprConst | hostVariable
    ;

usingSqlDescriptorClause
    : USING SQL DESCRIPTOR descriptorName
    ;

descriptorName
    : identifier | hostVariable
    ;
//https://www.postgresql.org/docs/current/sql-move.html
move
    : MOVE direction? (FROM | IN)? cursorName
    ;
//https://www.postgresql.org/docs/current/sql-fetch.html
fetch
    : FETCH direction? (FROM | IN)? cursorName
    ;
//https://www.postgresql.org/docs/current/sql-listen.html
listen
    : LISTEN channelName
    ;
//https://www.postgresql.org/docs/current/sql-unlisten.html
unlisten
    : UNLISTEN (channelName | ASTERISK_)
    ;
//https://www.postgresql.org/docs/current/sql-notify.html
notifyStmt
    : NOTIFY channelName (COMMA_ STRING_)?
    ;

direction
    : NEXT #next
    | PRIOR #prior
    | FIRST #first
    | LAST #last
    | ABSOLUTE signedIconst #absoluteCount
    | RELATIVE signedIconst #relativeCount
    | signedIconst #count
    | ALL #all
    | FORWARD #forward
    | FORWARD signedIconst #forwardCount
    | FORWARD ALL #forwardAll
    | BACKWARD #backward
    | BACKWARD signedIconst #backwardCount
    | BACKWARD ALL #backwardAll
    ;
//https://www.postgresql.org/docs/current/sql-prepare.html
prepare
    : PREPARE name prepTypeClause? AS preparableStmt
    ;
//https://www.postgresql.org/docs/current/sql-reindex.html
reindex
    : REINDEX reIndexClauses
    ;

reIndexClauses
    : (LP_ reindexOptionList RP_)? (reindexTargetType | reindexTargetMultitable)?  CONCURRENTLY? indexName?
    ;

reindexOptionList
    : reindexOptionElem (COMMA_ reindexOptionElem)*
    ;

reindexOptionElem
    : VERBOSE booleanValue?
     | CONCURRENTLY booleanValue?
     | TABLESPACE tablebaseName
    ;

reindexTargetMultitable
    : SCHEMA | SYSTEM | DATABASE
    ;

reindexTargetType
    : INDEX | TABLE
    ;
//https://www.postgresql.org/docs/current/sql-deallocate.html
deallocate
    : DEALLOCATE PREPARE? (anyName | ALL)
    ;

prepTypeClause
    : LP_ typeList RP_
    ;
//https://www.postgresql.org/docs/current/sql-refreshmaterializedview.html
refreshMaterializedView
    : REFRESH MATERIALIZED VIEW CONCURRENTLY? viewName withData?
    ;
//https://www.postgresql.org/docs/current/sql-alterforeigntable.html
alterForeignTable
    : ALTER FOREIGN TABLE ifExists? tableName ASTERISK_? alterForeignTableClauses
    ;

alterForeignTableClauses
    : RENAME TO tableName
    | RENAME COLUMN? oldColumnName = columnName TO newColumnName = columnName
    | alterTableCmds
    | SET SCHEMA schemaName
    ;
//https://www.postgresql.org/docs/current/sql-createoperator.html
createOperator
    : CREATE OPERATOR anyOperator definition
    ;
//https://www.postgresql.org/docs/current/sql-createopclass.html
createOperatorClass
    : CREATE OPERATOR CLASS anyName DEFAULT? FOR TYPE dataType USING indexName (FAMILY familyName=anyName)? AS opclassItemList
    ;
//https://www.postgresql.org/docs/current/sql-createopfamily.html
createOperatorFamily
    : CREATE OPERATOR FAMILY anyName USING indexName
    ;
//https://www.postgresql.org/docs/current/sql-createschema.html
createSchema
    : CREATE SCHEMA ifNotExists? schemaName? (AUTHORIZATION roleSpec)? schemaEltList?
    ;

schemaEltList
    : schemaStmt+
    ;

schemaStmt
    : createTable | createView | createIndex | createSequence | createTrigger | grant
    ;
//https://www.postgresql.org/docs/current/sql-security-label.html
securityLabelStmt
    : SECURITY LABEL (FOR provider)? ON securityLabelClausces IS securityLabel
    ;
provider
    : nonReservedWordOrSconst
    ;
securityLabel
    : STRING_ | NULL
    ;

securityLabelClausces
    : objectTypeAnyName objectName
    | COLUMN columnName
    | AGGREGATE aggregateSignature
    | FUNCTION functionWithArgtypes
    | LARGE OBJECT largeObjectOid
    | (PROCEDURE | ROUTINE) functionWithArgtypes
    ;
largeObjectOid
    : numericOnly
    ;
//    https://www.postgresql.org/docs/current/sql-grant.html
grant
    : GRANT (privilegeClause | roleClause)
    ;

privilegeClause
    : privilegeTypes ON onObjectClause (FROM | TO) granteeList withGrantOption? grantedByCllause?
    ;
withGrantOption
    : WITH GRANT OPTION
    ;
//roleClause
//    : privilegeList (FROM | TO) roleList (WITH ADMIN OPTION)? (GRANTED BY grantee)?
//    ;
roleClause
    : roleName (COMMA_ roleName)* (FROM | TO) roleList roleWithOption? grantedByCllause?
    ;
roleWithOption
    : WITH (ADMIN | INHERIT | SET) (OPTION | TRUE | FALSE)
    ;
grantedByCllause
    : GRANTED BY roleSpec
    ;
privilegeTypes
    : privilegeType columnNames? (COMMA_ privilegeType columnNames?)*
    ;

onObjectClause
    : TABLE? tableName (COMMA_ tableName)*
    | ALL (TABLES | SEQUENCES | FUNCTIONS | PROCEDURES | ROUTINES) IN SCHEMA schemaNameList
    | SEQUENCE sequenceName (COMMA_ sequenceName)*
    | DATABASE databaseName (COMMA_ databaseName)*
    | DOMAIN domainName (COMMA_ domainName)*
    | FOREIGN DATA WRAPPER fdwName (COMMA_ fdwName)*
    | FOREIGN SERVER serverName (COMMA_ serverName)*
    | (FUNCTION | PROCEDURE | ROUTINE) functionWithArgtypesList
    | LANGUAGE langName (COMMA_ langName)*
    | LARGE OBJECT loidList
    | PARAMETER configurationParameterClause (COMMA_ configurationParameterClause)*
    | SCHEMA schemaNameList
    | TABLESPACE tablespaceName (COMMA_ tablespaceName)*
    | TYPE typeName (COMMA_ typeName)*
    | TABLE? privilegeLevel   //todo grant未匹配到此条规则
    ;

privilegeLevel
    : ASTERISK_ | ASTERISK_ DOT_ASTERISK_ | identifier DOT_ASTERISK_ | tableNames | schemaName DOT_ routineName
    ;

routineName
    : identifier
    ;

privilegeType
    : SELECT
    | INSERT
    | UPDATE
    | DELETE
    | TRUNCATE
    | REFERENCES
    | TRIGGER
    | CREATE
    | CONNECT
    | TEMPORARY
    | TEMP
    | EXECUTE
    | USAGE
    | ALL PRIVILEGES?
    | MAINTAIN
    | SET
    | ALTER SYSTEM
    |
    ;
//https://www.postgresql.org/docs/current/sql-alterschema.html
alterSchema
    : ALTER SCHEMA schemaName (RENAME TO newName=schemaName | OWNER TO roleSpec)
    ;
//https://www.postgresql.org/docs/current/sql-dropschema.html
dropSchema
    : DROP SCHEMA ifExists? schemaNameList dropBehavior?
    ;

//https://www.postgresql.org/docs/current/sql-show.html
show
    : SHOW (varName | TIME ZONE | TRANSACTION ISOLATION LEVEL | SESSION AUTHORIZATION | ALL)
    ;
//https://www.postgresql.org/docs/current/sql-set.html
//https://www.postgresql.org/docs/current/sql-set-role.html
//https://www.postgresql.org/docs/current/sql-set-session-authorization.html
//https://www.postgresql.org/docs/current/sql-set-transaction.html
set
    : SET runtimeScope?
    (timeZoneClause
    | configurationParameterClause
    | varName FROM CURRENT
    | CATALOG STRING_
    | SCHEMA STRING_
    | NAMES encoding?
    | ROLE (roleName | NONE)
    | SESSION AUTHORIZATION (userName | DEFAULT)
    | XML OPTION documentOrContent)
    ;

runtimeScope
    : SESSION | LOCAL
    ;

timeZoneClause
    : TIME ZONE (numberLiterals | zoneValue | LOCAL | DEFAULT)
    ;

configurationParameterClause
    : varName (TO | EQ_) (varList | DEFAULT)
    ;
//https://www.postgresql.org/docs/current/sql-reset.html
resetParameter
    : RESET (ALL | identifier)
    ;
//https://www.postgresql.org/docs/current/sql-explain.html
explain
    : EXPLAIN
    (analyzeKeyword VERBOSE?
    | VERBOSE
    | LP_ explainOptionList RP_)?
    explainableStmt
    ;

explainableStmt
    : select | insert | update | delete | declare | merge | createTable | executeStmt | createMaterializedView | refreshMaterializedView
    ;

explainOptionList
    : explainOptionElem (COMMA_ explainOptionElem)*
    ;

explainOptionElem
    : explainOptionName explainOptionArg?
    ;

explainOptionArg
    : booleanOrString | numericOnly
    ;

explainOptionName
    : nonReservedWord | analyzeKeyword
    ;

analyzeKeyword
    : ANALYZE | ANALYSE | FORMAT | MEMORY | SUMMARY | TIMING | WAL | SERIALIZE | BUFFERS | GENERIC_PLAN | SETTINGS | COSTS | VERBOSE | SKIP_LOCKED | BUFFER_USAGE_LIMIT
    ;
//https://www.postgresql.org/docs/current/sql-analyze.html
analyzeTable    // analyzeKeyword为原g4,现有文档只有 ANALYZE
    : analyzeKeyword (VERBOSE? | LP_ vacAnalyzeOptionList RP_) vacuumRelationList?
    ;

vacuumRelationList
    : vacuumRelation (COMMA_ vacuumRelation)*
    ;

vacuumRelation
    : tableName columnNames?
    ;

vacAnalyzeOptionList
    : vacAnalyzeOptionElem (COMMA_ vacAnalyzeOptionElem)*
    ;

vacAnalyzeOptionElem
    : vacAnalyzeOptionName vacAnalyzeOptionArg?
    ;

vacAnalyzeOptionArg
    : booleanOrString | numericOnly
    ;

vacAnalyzeOptionName
    : nonReservedWord | analyzeKeyword
    ;
//https://www.postgresql.org/docs/current/sql-load.html
load
    : LOAD fileName
    ;
//https://www.postgresql.org/docs/current/sql-vacuum.html
vacuum
    : VACUUM ((FULL? FREEZE? VERBOSE? ANALYZE?) | (LP_ vacuumOptionList RP_)) vacuumRelationList?
    ;

vacuumOptionList
    : (vacuumOptionName vacAnalyzeOptionArg?) (COMMA_ (vacuumOptionName vacAnalyzeOptionArg?))*
    ;
vacuumOptionName
    : FULL
    | FREEZE
    | VERBOSE
    | ANALYZE
    | DISABLE_PAGE_SKIPPING
    | SKIP_LOCKED
    | INDEX_CLEANUP
    | PROCESS_MAIN
    | PROCESS_TOAST
    | TRUNCATE
    | PARALLEL
    | SKIP_DATABASE_STATS
    | ONLY_DATABASE_STATS
    | BUFFER_USAGE_LIMIT
    ;
emptyStatement
    :
    ;
//https://www.postgresql.org/docs/current/sql-revoke.html
revoke
    : REVOKE optionForClause? (privilegeClause | roleClause) (CASCADE | RESTRICT)?
    ;

optionForClause
    : (GRANT | ADMIN | INHERIT | SET) OPTION FOR
    ;
//https://www.postgresql.org/docs/current/sql-createuser.html
createUser
    : CREATE USER userName WITH? createOptRoleElem*
    ;

createOptRoleElem
    : alterOptRoleElem
    | SYSID NUMBER_
    | ADMIN roleList
    | ROLE roleList
    | IN ROLE roleList
    | IN GROUP roleList
    ;

alterOptRoleElem
    : (ENCRYPTED |UNENCRYPTED)? PASSWORD (STRING_ | NULL)
    | INHERIT
    | CONNECTION LIMIT signedIconst
    | VALID UNTIL STRING_
    | USER roleList
    | identifier
    | SUPERUSER | NOSUPERUSER | CREATEDB | NOCREATEDB
    | CREATEROLE | NOCREATEROLE | INHERIT | NOINHERIT
    | LOGIN | NOLOGIN | REPLICATION | NOREPLICATION
    | BYPASSRLS | NOBYPASSRLS
    ;
//https://www.postgresql.org/docs/current/sql-dropuser.html
dropUser
    : DROP USER ifExists? userName (COMMA_ userName)*
    ;
//https://www.postgresql.org/docs/current/sql-alteruser.html
alterUser
    : ALTER USER alterUserClauses
    ;

alterUserClauses
    : roleSpec WITH? alterOptRoleList
    | (roleSpec | ALL) (IN DATABASE databaseName)? setResetClause
    | roleSpec RENAME TO newName=roleSpec
    ;

alterOptRoleList
    : alterOptRoleElem*
    ;
//https://www.postgresql.org/docs/current/sql-createrole.html
createRole
    : CREATE ROLE roleName WITH? createOptRoleElem*
    ;
//https://www.postgresql.org/docs/current/sql-droprole.html
dropRole
    : DROP ROLE ifExists? roleName (COMMA_ roleName)*
    ;
//https://www.postgresql.org/docs/current/sql-alterrole.html
alterRole
    : ALTER ROLE alterUserClauses
    ;
//https://www.postgresql.org/docs/current/sql-creategroup.html
createGroup
    : CREATE GROUP roleName WITH? createOptRoleElem*
    ;
//https://www.postgresql.org/docs/current/sql-reassign-owned.html
reassignOwned
    : REASSIGN OWNED BY roleList TO roleSpec
    ;
//https://www.postgresql.org/docs/current/sql-dropgroup.html
dropGroup
    : DROP GROUP ifExists? roleList
    ;
//https://www.postgresql.org/docs/current/sql-insert.html
insert
    : withClause? INSERT INTO tableName ( AS alias)? columnNames? owerrideValue? (insertDefaultValue | select) optOnConflict? returningClause?
    ;
owerrideValue
    : OVERRIDING (USER | SYSTEM) VALUE
    ;
insertDefaultValue
    : DEFAULT VALUES
    ;
optOnConflict
    : ON CONFLICT conflictTarget? conflictAction
    ;
conflictAction
    : DO UPDATE SET setClauseList whereClause?
    | DO NOTHING
    ;
conflictTarget
    : LP_ columnName (COLLATE collation)? opclass? (COMMA_ columnName (COLLATE collation)? opclass?)* RP_ whereClause?
    | ON CONSTRAINT constraintName
    ;
opclass
    :  (owner DOT_)* name
    ;
collation
    : STRING_ | nonReservedWord
    ;

//https://www.postgresql.org/docs/current/sql-update.html
update
    : withClause? UPDATE ONLY? tableName ASTERISK_? (AS? alias)? SET setClauseList selectFromClause? whereOrCurrentClause? returningClause?
    ;

setClauseList
    : setClause (COMMA_ setClause)*
    ;

setClause
    : setTarget EQ_ aExpr
    | LP_ setTargetList RP_ EQ_ aExpr
    ;

setTarget
    : colId
    ;

setTargetList
    : setTarget (COMMA_ setTarget)*
    ;

returningClause
    : RETURNING targetList
    ;
//https://www.postgresql.org/docs/current/sql-delete.html
delete
    : withClause? DELETE FROM ONLY? tableName ASTERISK_? (AS? alias)? usingClause? whereOrCurrentClause? returningClause?
    ;


usingClause
    : USING fromClauseList
    ;
// https://www.postgresql.org/docs/current/sql-select.html
select
    : selectSubquery
    ;
selectSubquery
    : selectSubquery combineType selectSubquery
    | (queryBlock | parenthesisSelectSubquery) (selectExtraClause selectExtraClause? selectExtraClause?)?
    ;
selectExtraClause
    : sortClause
    | selectLimit
    | forUpdateClause
    ;
queryBlock
    : withClause? SELECT duplicateSpecification? selectList? intoClause? selectFromClause? whereClause? groupClause? havingClause? windowClause?
    | valuesClause
    ;
selectList
    :  (unqualifiedShorthand | selectProjection) (COMMA_ selectProjection)*
    ;
unqualifiedShorthand
    : ASTERISK_
    ;
selectFromClause
    : FROM fromClauseList
    ;
fromClauseList
    : fromClauseOption (COMMA_ fromClauseOption)*
    ;
fromClauseOption
    : fromClauseOption joinedTable+
    | selectTableReference
    | LATERAL? LP_ selectSubquery RP_ (AS? alias columnNames?)?
    | queryName (AS? alias columnNames?)?
    | LATERAL? funcApplication withOrdAs?
    | LATERAL? funcApplication (AS| AS? alias) (LP_ columnDefinition (COMMA_ columnDefinition)* RP_)
    | LATERAL?  ROWS FROM LP_ funcApplication RP_ (AS LP_ columnDefinition (COMMA_ columnDefinition)* RP_)? withOrdAs?
    ;
withOrdAs
    : WITH ORDINALITY (AS? alias columnNames)?
    | (WITH ORDINALITY)? AS? alias columnNames
    ;
joinedTable
    : NATURAL? joinType fromClauseOption (ON joinCondition | USING columnNames (AS? alias))?
    ;
selectTableReference
    : ONLY? tableName ASTERISK_? (AS? alias)? columnNames? (TABLESAMPLE funcApplication (REPEATABLE LP_ seed RP_) )?
    ;
seed
    : literals
    ;

joinType
    : innerJoinType
    | outerJoinType
    | crossJoinType
    ;
selectProjection
    : (tableName | viewName | sequenceName | alias ) DOT_ASTERISK_
    | selectProjectionExprClause
    | ASTERISK_
    ;
selectProjectionExprClause
    : (columnName | aExpr) (AS? alias)?
    ;
duplicateSpecification
    : ALL | DISTINCT  (ON LP_ exprList RP_)?
    ;

withClause
    : WITH RECURSIVE? withQueryList
    ;
withQueryList
    : withQuery (COMMA_ withQuery)*
    ;
withQuery
    : queryName columnNames? AS (NOT? MATERIALIZED)?  LP_ preparableStmt RP_ searchSetClause? cycleSetUsingClause?
    ;
cycleSetUsingClause
    : CYCLE columnName (COMMA_ columnName)* SET cycleMarkColName (TO cycleMarkValue DEFAULT cycleMarkDefault)? USING cyclePathColName
    ;
searchSetClause
    : SEARCH ( BREADTH | DEPTH ) FIRST BY columnName ( COMMA_ columnName)* SET searchEeqColName
    ;
parenthesisSelectSubquery
    : LP_ selectSubquery RP_ (AS? alias)?
    ;
combineType
    : (UNION | INTERSECT | EXCEPT) (ALL | DISTINCT)?
    ;
forUpdateClause
    : FOR (UPDATE | NO KEY UPDATE | SHARE | KEY SHARE)  (OF fromReferenceList)? (NOWAIT | W_SKIP LOCKED)*
    ;
fromReferenceList
    : (tableName | alias) (COMMA_ (tableName | alias))*
    ;
intoClause
    : INTO optTempTableName
    ;

optTempTableName
    : TEMPORARY TABLE? qualifiedName
    | TEMP TABLE? qualifiedName
    | LOCAL TEMPORARY TABLE? qualifiedName
    | LOCAL TEMP TABLE? qualifiedName
    | GLOBAL TEMPORARY TABLE? qualifiedName
    | GLOBAL TEMP TABLE? qualifiedName
    | UNLOGGED TABLE? qualifiedName
    | TABLE? qualifiedName
    | qualifiedName
    ;

optNameList
    :LP_ nameList RP_ |
    ;

preparableStmt
    : select
    | insert
    | update
    | delete
    | merge
    ;
qualifiedNameList
    : qualifiedName (COMMA_ qualifiedName)*
    ;

selectLimit
    : limitClause offsetClause?
    | offsetClause limitClause?
    ;
//https://www.postgresql.org/docs/current/sql-values.html
valuesClause
    : VALUES LP_ exprList RP_ (COMMA_ LP_ exprList RP_)*
    ;

limitClause
    : LIMIT selectLimitValue
    | FETCH firstOrNext selectFetchValue? rowOrRows onlyOrWithTies
    ;

offsetClause
    : OFFSET selectOffsetValue rowOrRows?
    ;

selectLimitValue
    : cExpr
    | ALL
    ;

selectOffsetValue
    : cExpr
    ;

selectFetchValue
    : cExpr
    ;

rowOrRows
    : ROW | ROWS
    ;

firstOrNext
    : FIRST | NEXT
    ;

onlyOrWithTies
    : ONLY | WITH TIES
    ;

targetList
    : targetEl (COMMA_ targetEl)*
    ;

targetEl
    : ASTERISK_
    | tableName DOT_ASTERISK_
    | aExpr (AS? alias)?
    ;

groupClause
    : GROUP BY (ALL | DISTINCT)? groupByList
    ;

groupByList
    : groupByItem (COMMA_ groupByItem)*
    ;

groupByItem
    : aExpr
    | emptyGroupingSet
    | cubeClause
    | rollupClause
    | groupingSetsClause
    ;

emptyGroupingSet
    : LP_ RP_
    ;

rollupClause
    : ROLLUP LP_ exprList RP_
    ;

cubeClause
    : CUBE LP_ exprList RP_
    ;

groupingSetsClause
    : GROUPING SETS LP_ groupByList RP_
    ;

windowClause
    : WINDOW windowDefinitionList
    ;

windowDefinitionList
    : windowDefinition (COMMA_ windowDefinition)*
    ;

windowDefinition
    : windowName AS windowSpecification
    ;

alias
    : identifier | STRING_
    ;



crossJoinType
    : CROSS JOIN
    ;

innerJoinType
    : INNER? JOIN
    ;

outerJoinType
    : (FULL | LEFT | RIGHT) OUTER? JOIN
    ;
whereClause
    : WHERE aExpr
    ;

whereOrCurrentClause
    : whereClause
    | WHERE CURRENT OF cursorName
    ;

havingClause
    : HAVING aExpr
    ;

do
    : DO dostmtOptList
    ;

dostmtOptList
    : dostmtOptItem+
    ;

dostmtOptItem
    : STRING_ | LANGUAGE nonReservedWordOrSconst | aexprConst
    ;
//https://www.postgresql.org/docs/current/sql-checkpoint.html
checkpoint
    : CHECKPOINT
    ;
//https://www.postgresql.org/docs/current/sql-copy.html
copy
    : copyWithTableOrQuery | copyWithTableOrQueryBinaryCsv | copyWithTableBinary
    |
    ;

copyWithTableOrQuery
    : COPY (tableName columnNames? | LP_ preparableStmt RP_) (FROM | TO) (fileName | PROGRAM STRING_ | STDIN | STDOUT)
     (WITH? LP_ copyOptionList RP_)? whereClause?
    ;

copyOptionList
    : copyOption (COMMA_ copyOption)*
    ;

copyOption
    : FORMAT identifier
    | FREEZE booleanValue?
    | DELIMITER STRING_
    | NULL STRING_
    | HEADER (booleanValue | MATCH)?
    | QUOTE STRING_
    | ESCAPE STRING_
    | (FORCE_QUOTE | FORCE_NOT_NULL | FORCE_NULL) (columnNames | ASTERISK_)
    | ENCODING STRING_
    | ON_ERROR identifier
    | LOG_VERBOSITY identifier
    ;

// pg9.0之前版本适用
copyWithTableOrQueryBinaryCsv
    : COPY (tableName columnNames? | LP_ preparableStmt RP_) (FROM | TO) (fileName | STDIN | STDOUT)
     WITH? BINARY? (DELIMITER AS? STRING_)? (NULL AS? STRING_)?
     (CSV HEADER?
        (QUOTE AS? STRING_)?
        (ESCAPE AS? STRING_)?
        (FORCE NOT NULL columnName (COMMA_ columnName)*)?
        (FORCE QUOTE (columnName (COMMA_ columnName)* | ASTERISK_))?
     )?
    ;

// pg9.0之前版本适用
copyWithTableBinary
    : COPY BINARY? tableName (FROM | TO) (fileName | STDIN | STDOUT) (USING? DELIMITERS STRING_)? (WITH NULL AS STRING_)?
    ;
//https://www.postgresql.org/docs/current/sql-call.html
call
    : CALL funcName LP_ callArguments? RP_
    ;

callArguments
    : callArgument (COMMA_ callArgument)*
    ;

callArgument
    : positionalNotation | namedNotation
    ;

positionalNotation
    : aExpr
    ;

namedNotation
    : identifier EQ_ GT_ aExpr
    ;
//https://www.postgresql.org/docs/current/sql-set-transaction.html
setTransaction
    : SET (SESSION CHARACTERISTICS AS)? TRANSACTION transactionModeList
    | SET TRANSACTION SNAPSHOT snapshotId=STRING_
    ;
//https://www.postgresql.org/docs/current/sql-begin.html
beginTransaction
    : BEGIN (WORK | TRANSACTION)? transactionModeList?
    ;
//https://www.postgresql.org/docs/current/sql-commit.html
commit
    : COMMIT (WORK | TRANSACTION)? (AND (NO)? CHAIN)?
    ;
//https://www.postgresql.org/docs/current/sql-savepoint.html
savepoint
    : SAVEPOINT sevepointName
    ;
//https://www.postgresql.org/docs/current/sql-abort.html
abort
    : ABORT (WORK | TRANSACTION)? (AND (NO)? CHAIN)?
    ;
//https://www.postgresql.org/docs/current/sql-start-transaction.html
startTransaction
    : START TRANSACTION transactionModeList?
    ;
//https://www.postgresql.org/docs/current/sql-end.html
end
    : END (WORK | TRANSACTION)? (AND (NO)? CHAIN)?
    ;
//https://www.postgresql.org/docs/current/sql-rollback.html
rollback
    : ROLLBACK (WORK | TRANSACTION)? (AND (NO)? CHAIN)?
    ;
//https://www.postgresql.org/docs/current/sql-release-savepoint.html
releaseSavepoint
    : RELEASE SAVEPOINT? savepointName
    ;
//https://www.postgresql.org/docs/current/sql-rollback-to.html
rollbackToSavepoint
    : ROLLBACK (WORK | TRANSACTION)? TO SAVEPOINT? savepointName
    ;
//https://www.postgresql.org/docs/current/sql-commit-prepared.html
commitPrepared
    : COMMIT PREPARED STRING_
    ;
//https://www.postgresql.org/docs/current/sql-rollback-prepared.html
rollbackPrepared
    : ROLLBACK PREPARED STRING_
    ;
//https://www.postgresql.org/docs/current/sql-set-constraints.html
setConstraints
    : SET CONSTRAINTS constraintsSetList constraintsSetMode
    ;

constraintsSetMode
    : DEFERRED | IMMEDIATE
    ;

constraintsSetList
    : ALL | nameList
    ;
//https://www.postgresql.org/docs/current/sql-lock.html
lock
    : LOCK TABLE? ONLY? relationExprList (IN lockType MODE)? NOWAIT?
    ;

lockType
    : ACCESS SHARE
    | ROW SHARE
    | ROW EXCLUSIVE
    | SHARE UPDATE EXCLUSIVE
    | SHARE
    | SHARE ROW EXCLUSIVE
    | EXCLUSIVE
    | ACCESS EXCLUSIVE
    ;
//https://www.postgresql.org/docs/current/sql-prepare-transaction.html
prepareTransaction
    : PREPARE TRANSACTION STRING_
    ;




parameterMarker
    : QUESTION_ literalsType?
    | DOLLAR_ NUMBER_
    ;

reservedKeyword
    : ALL
    | ANALYSE
    | ANALYZE
    | AND
    | ANY
    | ARRAY
    | AS
    | ASC
    | ASYMMETRIC
    | BOTH
    | CASE
    | CAST
    | CHECK
    | COLLATE
    | COLUMN
    | CONSTRAINT
    | CREATE
    | CURRENT_CATALOG
    | CURRENT_DATE
    | CURRENT_ROLE
    | CURRENT_TIME
    | CURRENT_TIMESTAMP
    | CURRENT_USER
    | DEFAULT
    | DEFERRABLE
    | DESC
    | DISTINCT
    | DO
    | ELSE
    | END
    | EXCEPT
    | FALSE
    | FETCH
    | FOR
    | FOREIGN
    | FROM
    | GRANT
    | GROUP
    | HAVING
    | IN
    | INITIALLY
    | INTERSECT
    | INTO
    | LATERAL
    | LEADING
    | LIMIT
    | LOCALTIME
    | LOCALTIMESTAMP
    | NOT
    | NULL
    | OFFSET
    | ON
    | ONLY
    | OR
    | ORDER
    | PLACING
    | PRIMARY
    | REFERENCES
    | RETURNING
    | SELECT
    | SESSION_USER
    | SOME
    | SYMMETRIC
    | TABLE
    | THEN
    | TO
    | TRAILING
    | TRUE
    | UNION
    | UNIQUE
    | USER
    | USING
    | VARIADIC
    | WHEN
    | WHERE
    | WINDOW
    | WITH
    ;

numberLiterals
   : MINUS_? NUMBER_ literalsType?
   ;

literalsType
    : TYPE_CAST_ IDENTIFIER_
    ;

identifier
    : UNICODE_ESCAPE? IDENTIFIER_ uescape? |  unreservedWord | typeFuncNameKeyword | STRING_
    ;

uescape
    : UESCAPE STRING_
    ;

unreservedWord
    : ABORT
    | ABSOLUTE
    | ACCESS
    | ACTION
    | ADD
    | ADMIN
    | AFTER
    | AGGREGATE
    | ALSO
    | ALTER
    | ALWAYS
    | ASSERTION
    | ASSIGNMENT
    | AT
    | ATTACH
    | ATTRIBUTE
    | BACKWARD
    | BEFORE
    | BEGIN
    | BY
    | BYTEA
    | BOX
    | CACHE
    | CALL
    | CALLED
    | CASCADE
    | CASCADED
    | CATALOG
    | CHAIN
    | CHARACTERISTICS
    | CHECKPOINT
    | CLASS
    | CLOSE
    | CLUSTER
    | COLUMNS
    | COMMENT
    | COMMENTS
    | COMMIT
    | COMMITTED
    | CONFIGURATION
    | CONFLICT
    | CONNECTION
    | CONSTRAINTS
    | CONTENT
    | CONTINUE
    | CONVERSION
    | COPY
    | COST
    | CSV
    | CUBE
    | CURRENT
    | CURSOR
    | CYCLE
    | CIRCLE
    | DATA
    | DATABASE
    | DAY
    | DEALLOCATE
    | DECLARE
    | DEFAULTS
    | DEFERRED
    | DEFINER
    | DELETE
    | DELIMITER
    | DELIMITERS
    | DEPENDS
    | DETACH
    | DICTIONARY
    | DISABLE
    | DISCARD
    | DOCUMENT
    | DOMAIN
    | DOUBLE
    | DROP
    | EACH
    | ENABLE
    | ENCODING
    | ENCRYPTED
    | ENUM
    | ESCAPE
    | EVENT
    | EXCLUDE
    | EXCLUDING
    | EXCLUSIVE
    | EXECUTE
    | EXPLAIN
    | EXPRESSION
    | EXTENDED
    | EXTENSION
    | EXTERNAL
    | FAMILY
    | FILTER
    | FIRST
    | FOLLOWING
    | FORCE
    | FORWARD
    | FUNCTION
    | FUNCTIONS
    | GENERATED
    | GLOBAL
    | GRANTED
    | GROUPS
    | HANDLER
    | HEADER
    | HOLD
    | HOUR
    | IDENTITY
    | IF
    | IMMEDIATE
    | IMMUTABLE
    | IMPLICIT
    | IMPORT
    | INCLUDE
    | INCLUDING
    | INCREMENT
    | INDEX
    | INDEXES
    | INHERIT
    | INHERITS
    | INLINE
    | INPUT
    | INSENSITIVE
    | INSERT
    | INSTEAD
    | INVOKER
    | INTERVAL
    | ISOLATION
    | KEY
    | LABEL
    | LANGUAGE
    | LARGE
    | LAST
    | LEAKPROOF
    | LEVEL
    | LISTEN
    | LOAD
    | LOCAL
    | LOCATION
    | LOCK
    | LOCKED
    | LOGGED
    | LSEG
    | MAIN
    | MAPPING
    | MATCH
    | MATERIALIZED
    | MAXVALUE
    | METHOD
    | MINUTE
    | MINVALUE
    | MODE
    | MONTH
    | MOVE
    | MOD
    | NAME
    | NAMES
    | NATIONAL
    | NEW
    | NEXT
    | NFC
    | NFD
    | NFKC
    | NFKD
    | NO
    | NORMALIZED
    | NOTHING
    | NOTIFY
    | NOWAIT
    | NULLS
    | OBJECT
    | OF
    | OFF
    | OIDS
    | OLD
    | OPERATOR
    | OPTION
    | OPTIONS
    | ORDINALITY
    | OTHERS
    | OVER
    | OVERRIDING
    | OWNED
    | OWNER
    | PARALLEL
    | PARSER
    | PARTIAL
    | PARTITION
    | PASSING
    | PASSWORD
    | PATH
    | PLAIN
    | PLANS
    | POLICY
    | POINT
    | POLYGON
    | PRECEDING
    | PREPARE
    | PREPARED
    | PRESERVE
    | PRIOR
    | PRIVILEGES
    | PROCEDURAL
    | PROCEDURE
    | PROCEDURES
    | PROGRAM
    | PUBLICATION
    | QUOTE
    | RANGE
    | READ
    | REASSIGN
    | RECHECK
    | RECURSIVE
    | REF
    | REFERENCING
    | REFRESH
    | REINDEX
    | RELATIVE
    | RELEASE
    | RENAME
    | REPEATABLE
    | REPLACE
    | REPLICA
    | RESET
    | RESTART
    | RESTRICT
    | RETURNS
    | REVOKE
    | ROLE
    | ROLLBACK
    | ROLLUP
    | ROUTINE
    | ROUTINES
    | ROWS
    | RULE
    | SAVEPOINT
    | SCHEMA
    | SCHEMAS
    | SCROLL
    | SEARCH
    | SECOND
    | SECURITY
    | SEQUENCE
    | SEQUENCES
    | SERIALIZABLE
    | SERVER
    | SESSION
    | SET
    | SETS
    | SHARE
    | SHOW
    | SIMPLE
//    | SKIP
    | SNAPSHOT
    | SQL
    | STABLE
    | STANDALONE
    | START
    | STATEMENT
    | STATISTICS
    | STDIN
    | STDOUT
    | STORAGE
    | STORED
    | STRICT
    | STRIP
    | SUBSCRIPTION
    | SUPPORT
    | SYSID
    | SYSTEM
    | TABLES
    | TABLESPACE
    | TEMP
    | TEMPLATE
    | TEMPORARY
    | TEXT
    | TIES
    | TRANSACTION
    | TRANSFORM
    | TRIGGER
    | TRUNCATE
    | TRUSTED
    | TYPE
    | TYPES
    | TIME
    | TIMESTAMP
    | UESCAPE
    | UNBOUNDED
    | UNCOMMITTED
    | UNENCRYPTED
    | UNKNOWN
    | UNLISTEN
    | UNLOGGED
    | UNTIL
    | UPDATE
    | VACUUM
    | VALID
    | VALIDATE
    | VALIDATOR
    | VALUE
    | VARYING
    | VERSION
    | VIEW
    | VIEWS
    | VOLATILE
    | WHITESPACE
    | WITHIN
    | WITHOUT
    | WORK
    | WRAPPER
    | WRITE
    | XML
    | YEAR
    | YES
    | ZONE
    | JSON
    | PARAM
    | TABLE
    | PUBLIC
    | SERIAL
    | COMMUTATOR
    | NEGATOR
    | HASHES
    | MERGES
    | DATE
    | DEPTH
    | SUMMARY
    | TARGET
    ;

typeFuncNameKeyword
    : AUTHORIZATION
    | BINARY
    | COLLATION
    | CONCURRENTLY
    | CROSS
    | CURRENT_SCHEMA
    | FREEZE
    | FULL
    | ILIKE
    | INNER
    | IS
    | ISNULL
    | JOIN
    | LEFT
    | LIKE
    | NATURAL
    | NOTNULL
    | OUTER
    | OVERLAPS
    | RIGHT
    | SIMILAR
    | TABLESAMPLE
    | VERBOSE
    | INT4
    | TEXT
    | XML
    | JSON
    | YAML
    | BINARY
    ;

schemaName
    : (owner DOT_)? identifier
    ;

tableName
    : (owner DOT_)* name
    ;

columnName
    : (owner DOT_)* name
    ;

owner
    : identifier
    ;

name
    : identifier
    ;

tableNames
    : LP_? tableName (COMMA_ tableName)* RP_?
    ;

columnNames
    : LP_ columnName (COMMA_ columnName)* RP_
    ;

indexName
    : identifier
    ;

constraintName
    : identifier
    ;

primaryKey
    : PRIMARY? KEY
    ;

andOperator
    : AND | AND_
    ;

orOperator
    : OR | OR_
    ;

comparisonOperator
    : EQ_ | GTE_ | GT_ | LTE_ | LT_ | NEQ_
    ;

patternMatchingOperator
    : LIKE
    | TILDE_TILDE_
    | NOT LIKE
    | NOT_TILDE_TILDE_
    | ILIKE
    | ILIKE_
    | NOT ILIKE
    | NOT_ILIKE_
    | SIMILAR TO
    | NOT SIMILAR TO
    | TILDE_
    | NOT_ TILDE_
    | TILDE_ ASTERISK_
    | NOT_ TILDE_ ASTERISK_
    ;

cursorName
    : name | hostVariable
    ;

aExpr
    : cExpr
    | aExpr TYPE_CAST_ typeName
    | aExpr COLLATE anyName
    | aExpr AT TIME ZONE aExpr
    | PLUS_ aExpr
    | MINUS_ aExpr
    | aExpr PLUS_ aExpr
    | aExpr MINUS_ aExpr
    | aExpr ASTERISK_ aExpr
    | aExpr SLASH_ aExpr
    | aExpr MOD_ aExpr
    | aExpr CARET_ aExpr
    | aExpr AMPERSAND_ aExpr
    | aExpr VERTICAL_BAR_ aExpr
    | aExpr qualOp aExpr
    | qualOp aExpr
    | aExpr qualOp
    | aExpr comparisonOperator aExpr
    | NOT aExpr
    | aExpr patternMatchingOperator aExpr ESCAPE aExpr
    | aExpr patternMatchingOperator aExpr
    | aExpr IS NULL
    | aExpr ISNULL
    | aExpr IS NOT NULL
    | aExpr NOTNULL
    | row OVERLAPS row
    | aExpr IS TRUE
    | aExpr IS NOT TRUE
    | aExpr IS FALSE
    | aExpr IS NOT FALSE
    | aExpr IS UNKNOWN
    | aExpr IS NOT UNKNOWN
    | aExpr IS DISTINCT FROM aExpr
    | aExpr IS NOT DISTINCT FROM aExpr
    | aExpr IS OF LP_ typeList RP_
    | aExpr IS NOT OF LP_ typeList RP_
    | aExpr BETWEEN ASYMMETRIC? bExpr AND aExpr
    | aExpr NOT BETWEEN ASYMMETRIC? bExpr AND aExpr
    | aExpr BETWEEN SYMMETRIC bExpr AND aExpr
    | aExpr NOT BETWEEN SYMMETRIC bExpr AND aExpr
    | aExpr IN inExpr
    | aExpr NOT IN inExpr
    | aExpr subqueryOp subType selectSubquery
    | aExpr subqueryOp subType LP_ aExpr RP_
    | UNIQUE selectSubquery
    | aExpr IS DOCUMENT
    | aExpr IS NOT DOCUMENT
    | aExpr IS NORMALIZED
    | aExpr IS unicodeNormalForm NORMALIZED
    | aExpr IS NOT NORMALIZED
    | aExpr IS NOT unicodeNormalForm NORMALIZED
    | aExpr andOperator aExpr
    | aExpr orOperator aExpr
    | DEFAULT
    ;

bExpr
    : cExpr
    | bExpr TYPE_CAST_ typeName
    | PLUS_ bExpr
    | MINUS_ bExpr
    | bExpr qualOp bExpr
    | qualOp bExpr
    | bExpr qualOp
    | bExpr IS DISTINCT FROM bExpr
    | bExpr IS NOT DISTINCT FROM bExpr
    | bExpr IS OF LP_ typeList RP_
    | bExpr IS NOT OF LP_ typeList RP_
    | bExpr IS DOCUMENT
    | bExpr IS NOT DOCUMENT
    ;

cExpr
    : parameterMarker
    | columnName
    | identifier DOT_ASTERISK_
    | aexprConst
    | PARAM indirectionEl?
    | LP_ aExpr RP_ optIndirection
    | caseExpr
    | funcExpr
    | selectSubquery
    | selectSubquery indirection
    | EXISTS selectSubquery
    | ARRAY selectSubquery
    | ARRAY arrayExpr
    | explicitRow
    | implicitRow
    | GROUPING LP_ exprList RP_
    ;

indirection
    : indirectionEl
    | indirection indirectionEl
    ;

optIndirection
    : optIndirection indirectionEl |
    ;

indirectionEl
    : DOT_ attrName
    | DOT_ASTERISK_
    | LBT_ aExpr RBT_
    | LBT_ sliceBound? COLON_ sliceBound? RBT_
    ;

sliceBound
    : aExpr
    ;

inExpr
    : selectSubquery | LP_ exprList RP_
    ;

caseExpr
    : CASE caseArg? whenClauseList caseDefault? END
    ;

whenClauseList
    : whenClause+
    ;

whenClause
    : WHEN ( MATCHED (AND aExpr)?
     | NOT MATCHED BY SOURCE (AND aExpr)?
     | NOT MATCHED (BY TARGET)? (AND aExpr)?
     | aExpr
     ) THEN ( mergeInsert
     | mergeUpdate
     | delete
     | aExpr
     )
    ;
mergeUpdate
    : UPDATE SET setClauseList
    ;
mergeInsert
    : INSERT columnNames? (OVERRIDING (SYSTEM | USER ) VALUE)?
       (VALUES LP_ (aExpr | DEFAULT) (COMMA_ (aExpr | DEFAULT))* RP_) | (DEFAULT VALUES)
    ;
caseDefault
    : ELSE aExpr
    ;

caseArg
    : aExpr
    ;



qualOp
    : jsonOperator
    | geometricOperator
    | OPERATOR LP_ anyOperator RP_
    ;

subqueryOp
    : allOp
    | OPERATOR LP_ anyOperator RP_
    | LIKE
    | NOT LIKE
    | TILDE_
    | NOT_ TILDE_
    ;

allOp
    : op | mathOperator
    ;

op
    : (AND_
    | OR_
    | NOT_
    | TILDE_
    | VERTICAL_BAR_
    | AMPERSAND_
    | SIGNED_LEFT_SHIFT_
    | SIGNED_RIGHT_SHIFT_
    | CARET_
    | MOD_
    | COLON_
    | PLUS_
    | MINUS_
    | ASTERISK_
    | SLASH_
    | BACKSLASH_
    | DOT_
    | DOT_ASTERISK_
    | SAFE_EQ_
    | DEQ_
    | EQ_
    | CQ_
    | NEQ_
    | GT_
    | GTE_
    | LT_
    | LTE_
    | POUND_
    | LP_
    | RP_
    | LBE_
    | RBE_
    | LBT_
    | RBT_
    | COMMA_
    | DQ_
    | SQ_
    | BQ_
    | QUESTION_
    | DOLLAR_
    | AT_
    | SEMI_
    | TILDE_TILDE_
    | NOT_TILDE_TILDE_
    | TYPE_CAST_
    | ILIKE_
    | NOT_ILIKE_
    | UNICODE_ESCAPE
    | JSON_EXTRACT_
    | JSON_EXTRACT_TEXT_
    | JSON_PATH_EXTRACT_
    | JSON_PATH_EXTRACT_TEXT_
    | JSONB_CONTAIN_RIGHT_
    | JSONB_CONTAIN_LEFT_
    | JSONB_CONTAIN_ALL_TOP_KEY_
    | JSONB_PATH_DELETE_
    | JSONB_PATH_CONTAIN_ANY_VALUE_
    | JSONB_PATH_PREDICATE_CHECK_
    | GEOMETRIC_LENGTH_
    | GEOMETRIC_DISTANCE_
    | GEOMETRIC_EXTEND_RIGHT_
    | GEOMETRIC_EXTEND_LEFT_
    | GEOMETRIC_STRICT_BELOW_
    | GEOMETRIC_STRICT_ABOVE_
    | GEOMETRIC_EXTEND_ABOVE_
    | GEOMETRIC_EXTEND_BELOW_
    | GEOMETRIC_BELOW_
    | GEOMETRIC_ABOVE_
    | GEOMETRIC_INTERSECT_
    | GEOMETRIC_PERPENDICULAR_
    | GEOMETRIC_SAME_AS_ )+
    ;

mathOperator
    : PLUS_
    | MINUS_
    | ASTERISK_
    | SLASH_
    | MOD_
    | CARET_
    | LT_
    | GT_
    | EQ_
    | LTE_
    | GTE_
    | NEQ_
    ;

jsonOperator
    : JSON_EXTRACT_ # jsonExtract
    | JSON_EXTRACT_TEXT_ # jsonExtractText
    | JSON_PATH_EXTRACT_ # jsonPathExtract
    | JSON_PATH_EXTRACT_TEXT_ # jsonPathExtractText
    | JSONB_CONTAIN_RIGHT_ # jsonbContainRight
    | JSONB_CONTAIN_LEFT_ # jsonbContainLeft
    | QUESTION_ # jsonbContainTopKey
    | QUESTION_ VERTICAL_BAR_ # jsonbContainAnyTopKey
    | JSONB_CONTAIN_ALL_TOP_KEY_ # jsonbContainAllTopKey
    | OR_ # jsonbConcat
    | MINUS_ # jsonbDelete
    | JSONB_PATH_DELETE_ # jsonbPathDelete
    | JSONB_PATH_CONTAIN_ANY_VALUE_ # jsonbPathContainAnyValue
    | JSONB_PATH_PREDICATE_CHECK_ # jsonbPathPredicateCheck
    ;

geometricOperator
    : GEOMETRIC_LENGTH_
    | GEOMETRIC_DISTANCE_
    | GEOMETRIC_EXTEND_RIGHT_
    | GEOMETRIC_EXTEND_LEFT_
    | GEOMETRIC_STRICT_BELOW_
    | GEOMETRIC_STRICT_ABOVE_
    | GEOMETRIC_EXTEND_ABOVE_
    | GEOMETRIC_EXTEND_BELOW_
    | GEOMETRIC_BELOW_
    | GEOMETRIC_ABOVE_
    | GEOMETRIC_INTERSECT_
    | GEOMETRIC_PERPENDICULAR_
    | GEOMETRIC_SAME_AS_
    | QUESTION_ MINUS_
    | QUESTION_ OR_
    | POUND_
    | SIGNED_LEFT_SHIFT_
    | SIGNED_RIGHT_SHIFT_
    ;

qualAllOp
    : allOp
    | OPERATOR LP_ anyOperator RP_
    ;

ascDesc
    : ASC | DESC
    ;

anyOperator
    : ((owner DOT_)* name DOT_)? allOp
    ;

frameClause
    : (RANGE|ROWS|GROUPS) frameExtent windowExclusionClause?
    ;

frameExtent
    : frameBound
    | BETWEEN frameBound AND frameBound frameExclusion?
    ;
frameExclusion
    : EXCLUDE CURRENT ROW
    | EXCLUDE GROUP
    | EXCLUDE TIES
    | EXCLUDE NO OTHERS
    ;
frameBound
    : UNBOUNDED PRECEDING
    | UNBOUNDED FOLLOWING
    | CURRENT ROW
    | aExpr PRECEDING
    | aExpr FOLLOWING
    ;


windowExclusionClause
    : EXCLUDE CURRENT ROW
    | EXCLUDE GROUP
    | EXCLUDE TIES
    | EXCLUDE NO OTHERS
    ;

row
    : ROW LP_ exprList RP_
    | ROW LP_ RP_
    | LP_ exprList COMMA_ aExpr RP_
    ;

explicitRow
    : ROW LP_ exprList RP_
    | ROW LP_ RP_
    ;

implicitRow
    : LP_ exprList COMMA_ aExpr RP_
    ;

subType
    : ANY | SOME | ALL
    ;

arrayExpr
    : LBT_ exprList RBT_
    | LBT_ arrayExprList RBT_
    | LBT_ RBT_
    ;

arrayExprList
    : arrayExpr (COMMA_ arrayExpr)*
    ;

funcArgList
    : funcArgExpr (COMMA_ funcArgExpr)*
    ;

paramName
    : typeFunctionName
    ;

funcArgExpr
    : aExpr
    | paramName CQ_ aExpr
    | paramName GTE_ aExpr
    ;

typeList
    : typeName (COMMA_ typeName)*
    ;

funcApplication
    : funcName LP_ RP_
    | funcName LP_ funcArgList sortClause? RP_
    | funcName LP_ VARIADIC funcArgExpr sortClause? RP_
    | funcName LP_ funcArgList COMMA_ VARIADIC funcArgExpr sortClause? RP_
    | funcName LP_ ALL funcArgList sortClause? RP_
    | funcName LP_ DISTINCT funcArgList sortClause? RP_
    | funcName LP_ ASTERISK_ RP_
    ;


funcName
    : (owner DOT_)* name
    ;
aexprConst
    : numberConst
    | STRING_
    | BEGIN_DOLLAR_STRING_CONSTANT DOLLAR_TEXT* END_DOLLAR_STRING_CONSTANT
    | funcName STRING_
    | funcName LP_ funcArgList sortClause? RP_ STRING_
    | constTypeName STRING_
    | TRUE
    | FALSE
    | NULL
    ;

numberConst
    : (PLUS_ | MINUS_)? NUMBER_
    ;

qualifiedName
    : colId indirection?
    ;

colId
    : identifier
    ;

channelName
    : identifier
    ;

typeFunctionName
    : identifier | unreservedWord | typeFuncNameKeyword
    ;
funcExpr
    : funcApplication withinGroupClause? filterClause? overClause?
    | functionExprCommonSubexpr
    ;

withinGroupClause
    : WITHIN GROUP LP_ sortClause RP_
    ;

filterClause
    : FILTER LP_ WHERE aExpr RP_
    ;

functionExprWindowless
    : funcApplication | functionExprCommonSubexpr
    ;



functionExprCommonSubexpr
    : COLLATION FOR LP_ aExpr RP_
    | CURRENT_DATE
    | CURRENT_TIME
    | CURRENT_TIME LP_ NUMBER_ RP_
    | CURRENT_TIMESTAMP
    | CURRENT_TIMESTAMP LP_ NUMBER_ RP_
    | LOCALTIME
    | LOCALTIME LP_ NUMBER_ RP_
    | LOCALTIMESTAMP
    | LOCALTIMESTAMP LP_ NUMBER_ RP_
    | CURRENT_ROLE
    | CURRENT_USER
    | SESSION_USER
    | USER
    | CURRENT_CATALOG
    | CURRENT_SCHEMA
    | CAST LP_ aExpr AS typeName RP_
    | EXTRACT LP_ extractList? RP_
    | NORMALIZE LP_ aExpr RP_
    | NORMALIZE LP_ aExpr COMMA_ unicodeNormalForm RP_
    | OVERLAY LP_ overlayList RP_
    | POSITION LP_ positionList RP_
    | SUBSTRING LP_ substrList RP_
    | TREAT LP_ aExpr AS typeName RP_
    | TRIM LP_ BOTH trimList RP_
    | TRIM LP_ LEADING trimList RP_
    | TRIM LP_ TRAILING trimList RP_
    | TRIM LP_ trimList RP_
    | NULLIF LP_ aExpr COMMA_ aExpr RP_
    | COALESCE LP_ exprList RP_
    | GREATEST LP_ exprList RP_
    | LEAST LP_ exprList RP_
    | XMLCONCAT LP_ exprList RP_
    | XMLELEMENT LP_ NAME identifier RP_
    | XMLELEMENT LP_ NAME identifier COMMA_ xmlAttributes RP_
    | XMLELEMENT LP_ NAME identifier COMMA_ exprList RP_
    | XMLELEMENT LP_ NAME identifier COMMA_ xmlAttributes COMMA_ exprList RP_
    | XMLEXISTS LP_ cExpr xmlExistsArgument RP_
    | XMLFOREST LP_ xmlAttributeList RP_
    | XMLPARSE LP_ documentOrContent aExpr xmlWhitespaceOption RP_
    | XMLPI LP_ NAME identifier RP_
    | XMLPI LP_ NAME identifier COMMA_ aExpr RP_
    | XMLROOT LP_ aExpr COMMA_ xmlRootVersion xmlRootStandalone? RP_
    | XMLSERIALIZE LP_ documentOrContent aExpr AS simpleTypeName RP_
    ;

typeName
    : simpleTypeName optArrayBounds
    | SETOF simpleTypeName optArrayBounds
    | simpleTypeName ARRAY LBT_ NUMBER_ RBT_
    | SETOF simpleTypeName ARRAY LBT_ NUMBER_ RBT_
    | simpleTypeName ARRAY
    | SETOF simpleTypeName ARRAY
    ;

simpleTypeName
    : genericType
    | numeric
    | bit
    | character
    | constDatetime
    | constInterval optInterval
    | constInterval LP_ NUMBER_ RP_
    ;

constTypeName
    : numeric
    | bit
    | character
    | constDatetime
    ;

exprList
    : aExpr (COMMA_ aExpr)*
    ;

extractList
    : extractArg FROM aExpr
    ;

extractArg
    : YEAR
    | MONTH
    | DAY
    | HOUR
    | MINUTE
    | SECOND
    | identifier
    ;

genericType
    : typeFunctionName typeModifiers? | typeFunctionName attrs typeModifiers?
    ;

typeModifiers
    : LP_ exprList RP_
    ;

numeric
    : INT | INTEGER | SMALLINT | BIGINT| REAL | FLOAT optFloat | DOUBLE PRECISION | DECIMAL typeModifiers? | DEC typeModifiers? | NUMERIC typeModifiers? | BOOLEAN | FLOAT8 | FLOAT4 | INT2 | INT4 | INT8
    ;

constDatetime
    : TIMESTAMP LP_ NUMBER_ RP_ timezone?
    | TIMESTAMP timezone?
    | TIME LP_ NUMBER_ RP_ timezone?
    | TIME timezone?
    | DATE
    ;

timezone
    : WITH TIME ZONE
    | WITHOUT TIME ZONE
    ;

character
    : characterWithLength | characterWithoutLength
    ;

characterWithLength
    : characterClause LP_ NUMBER_ RP_
    ;

characterWithoutLength
    : characterClause
    ;

characterClause
    : CHARACTER VARYING?
    | CHAR VARYING?
    | VARCHAR
    | NATIONAL CHARACTER VARYING?
    | NATIONAL CHAR VARYING?
    | NCHAR VARYING?
    ;

optFloat
    : LP_ NUMBER_ RP_ |
    ;

attrs
    : DOT_ attrName | attrs DOT_ attrName
    ;

attrName
    : identifier
    | colNameKeyword
    | typeFuncNameKeyword
    | reservedKeyword
    ;

colLable
    : identifier
    | colNameKeyword
    | typeFuncNameKeyword
    | reservedKeyword
    ;

bit
    : bitWithLength | bitWithoutLength
    ;

bitWithLength
    : BIT VARYING? LP_ exprList RP_
    ;

bitWithoutLength
    : BIT VARYING?
    ;

constInterval
    : INTERVAL
    ;

optInterval
    : YEAR
    | MONTH
    | DAY
    | HOUR
    | MINUTE
    | intervalSecond
    | YEAR TO MONTH
    | DAY TO HOUR
    | DAY TO MINUTE
    | DAY TO intervalSecond
    | HOUR TO MINUTE
    | HOUR TO intervalSecond
    | MINUTE TO intervalSecond
    |
    ;

optArrayBounds
    : optArrayBounds LBT_ RBT_
    | optArrayBounds LBT_ NUMBER_ RBT_
    |
    ;

intervalSecond
    : SECOND
    | SECOND LP_ NUMBER_ RP_
    ;

unicodeNormalForm
    : NFC | NFD | NFKC | NFKD
    ;

trimList
    : aExpr FROM exprList
    | FROM exprList
    | exprList
    ;

overlayList
    : aExpr overlayPlacing substrFrom substrFor
    | aExpr overlayPlacing substrFrom
    ;

overlayPlacing
    : PLACING aExpr
    ;

substrFrom
    : FROM aExpr
    ;

substrFor
    : FOR aExpr
    ;

positionList
    : bExpr IN bExpr |
    ;

substrList
    : aExpr substrFrom substrFor
    | aExpr substrFor substrFrom
    | aExpr substrFrom
    | aExpr substrFor
    | exprList
    |
    ;

xmlAttributes
    : XMLATTRIBUTES LP_ xmlAttributeList RP_
    ;

xmlAttributeList
    : xmlAttributeEl (COMMA_ xmlAttributeEl)*
    ;

xmlAttributeEl
    : aExpr AS identifier | aExpr
    ;

xmlExistsArgument
    : PASSING cExpr
    | PASSING cExpr xmlPassingMech
    | PASSING xmlPassingMech cExpr
    | PASSING xmlPassingMech cExpr xmlPassingMech
    ;

xmlPassingMech
    : BY REF | BY VALUE
    ;

documentOrContent
    : DOCUMENT | CONTENT
    ;

xmlWhitespaceOption
    : PRESERVE WHITESPACE | STRIP WHITESPACE |
    ;

xmlRootVersion
    : VERSION aExpr
    | VERSION NO VALUE
    ;

xmlRootStandalone
    : COMMA_ STANDALONE YES
    | COMMA_ STANDALONE NO
    | COMMA_ STANDALONE NO VALUE
    ;





tableFuncElementList
    : tableFuncElement (COMMA_ tableFuncElement)*
    ;

tableFuncElement
    : attrName dataType collateClause?
    ;

collateClause
    : COLLATE EQ_? anyName
    ;

anyName
    : (owner DOT_)* name
    ;



nameList
    : name (COMMA_ name)*
    ;

sortClause
    : ORDER BY sortbyList
    ;

sortbyList
    : sortby (COMMA_ sortby)*
    ;

sortby
    : aExpr USING qualAllOp nullsOrder?
    | aExpr ascDesc? nullsOrder?
    ;

nullsOrder
    : NULLS FIRST
    | NULLS LAST
    ;




overClause
    : OVER windowSpecification
    | OVER colId
    ;

windowSpecification
    : LP_ windowName? partitionClause? sortClause? frameClause? RP_
    ;

windowName
    : identifier
    ;

partitionClause
    : PARTITION BY exprList
    ;

indexParams
    : indexElem (COMMA_ indexElem)*
    ;

indexElemOptions
    : collate? optClass ascDesc? nullsOrder?
    | collate? anyName reloptions ascDesc? nullsOrder?
    ;

indexElem
    : columnName indexElemOptions
    | LP_ aExpr RP_ indexElemOptions
    | functionExprWindowless indexElemOptions
    ;

collate
    : COLLATE anyName
    ;

optClass
    : anyName |
    ;

reloptions
    : LP_ reloptionList RP_
    ;

reloptionList
    : reloptionElem (COMMA_ reloptionElem)*
    ;

reloptionElem
    : alias EQ_ defArg
    | alias
    | alias DOT_ alias EQ_ defArg
    | alias DOT_ alias
    ;

defArg
    : funcType
    | reservedKeyword
    | qualAllOp
    | NUMBER_
    | STRING_
    | NONE
    | funcName (LP_ funcArgsList RP_ | LP_ RP_)
    ;

funcType
    : typeName
    | typeFunctionName attrs MOD_ TYPE
    | SETOF typeFunctionName attrs MOD_ TYPE
    ;

dataType
    : dataTypeName dataTypeLength? characterSet? collateClause? | dataTypeName LP_ STRING_ (COMMA_ STRING_)* RP_ characterSet? collateClause?
    ;

dataTypeName
    : INT | INT2 | INT4 | INT8 | SMALLINT | INTEGER | BIGINT | DECIMAL | NUMERIC | REAL | FLOAT | FLOAT4 | FLOAT8 | DOUBLE PRECISION | SMALLSERIAL | SERIAL | BIGSERIAL
    | VARCHAR | CHARACTER | CHAR | TEXT | NAME | BYTEA | TIMESTAMP | DATE | TIME | INTERVAL | BOOLEAN | ENUM | POINT
    | LINE | LSEG | BOX | PATH | POLYGON | CIRCLE | CIDR | INET | MACADDR | MACADDR8 | BIT | VARBIT | TSVECTOR | TSQUERY | XML
    | JSON | INT4RANGE | INT8RANGE | NUMRANGE | TSRANGE | TSTZRANGE | DATERANGE | ARRAY | identifier | constDatetime | typeName
    ;

dataTypeLength
    : LP_ NUMBER_ (COMMA_ NUMBER_)? RP_
    ;

characterSet
    : (CHARACTER | CHAR) SET EQ_? ignoredIdentifier
    ;

ignoredIdentifier
    : identifier (DOT_ identifier)?
    ;

ignoredIdentifiers
    : ignoredIdentifier (COMMA_ ignoredIdentifier)*
    ;

signedIconst
    : NUMBER_
    | PLUS_ NUMBER_
    | MINUS_ NUMBER_
    ;

booleanOrString
    : TRUE
    | FALSE
    | ON
    | nonReservedWord
    | STRING_
    | OFF
    | AUTO
    ;

nonReservedWord
    : identifier
    | unreservedWord
    | colNameKeyword
    | typeFuncNameKeyword
    ;

colNameKeyword
    : BETWEEN
    | BIGINT
    | BIT
    | BOOLEAN
    | CHAR
    | CHARACTER
    | COALESCE
    | DEC
    | DECIMAL
    | EXISTS
    | EXTRACT
    | FLOAT
    | GREATEST
    | GROUPING
    | INOUT
    | INT
    | INTEGER
    | INTERVAL
    | LEAST
    | NATIONAL
    | NCHAR
    | NONE
    | NULLIF
    | NUMERIC
    | OUT
    | OVERLAY
    | POSITION
    | PRECISION
    | REAL
    | ROW
    | SETOF
    | SMALLINT
    | SUBSTRING
    | TIME
    | TIMESTAMP
    | TREAT
    | TRIM
    | VALUES
    | VARCHAR
    | XMLATTRIBUTES
    | XMLCONCAT
    | XMLELEMENT
    | XMLEXISTS
    | XMLFOREST
    | XMLNAMESPACES
    | XMLPARSE
    | XMLPI
    | XMLROOT
    | XMLSERIALIZE
    | XMLTABLE
    ;

databaseName
    : colId
    ;

roleSpec
    : GROUP? (roleName | nonReservedWord)
    | CURRENT_USER
    | SESSION_USER
    | CURRENT_ROLE
    | PUBLIC
    ;

varName
    : (owner DOT_)* name
    ;

varList
    : varValue (COMMA_ varValue)*
    ;

varValue
    : booleanOrString | numericOnly
    ;

zoneValue
    : STRING_
    | identifier
    | INTERVAL STRING_ optInterval
    | INTERVAL LP_ NUMBER_ RP_ STRING_
    | numericOnly
    | DEFAULT
    | LOCAL
    ;

numericOnly
    : NUMBER_
    | PLUS_ NUMBER_
    | MINUS_ NUMBER_
    ;

isoLevel
    : READ UNCOMMITTED
    | READ COMMITTED
    | REPEATABLE READ
    | SERIALIZABLE
    ;

columnDef
    : columnName dataType createGenericOptions? colQualList
    ;

parenthesizedSeqOptList
    : LP_ seqOptList RP_
    ;

seqOptList
    : seqOptElem+
    ;

seqOptElem
    : AS dataType
    | CACHE numericOnly
    | NO? CYCLE
    | INCREMENT BY? numericOnly
    | (MAXVALUE | MINVALUE) numericOnly
    | NO (MAXVALUE | MINVALUE)
    | OWNED BY (columnName | NONE)
    | SEQUENCE NAME anyName
    | (RESTART | START) (WITH? numericOnly)?
    ;

definition
    : LP_ defList RP_
    ;

defList
    : defElem (COMMA_ defElem)*
    ;

defElem
    : colLabel (EQ_ defArg)?
    ;

colLabel
    : identifier
    | unreservedWord
    | colNameKeyword
    | typeFuncNameKeyword
    | reservedKeyword
    ;


createGenericOptions
    : OPTIONS LP_ genericOptionList RP_
    ;

genericOptionList
    : genericOptionElem (COMMA_ genericOptionElem)*
    ;

replicaIdentity
    : NOTHING
    | FULL
    | DEFAULT
    | USING INDEX name
    ;

operArgtypes
    : LP_ (typeName | NONE) COMMA_ rightType = typeName RP_
    ;

funcArg
    : argmode? argName? argType
    | argClass paramName funcType
    | paramName argClass funcType
    | paramName funcType
    | argClass funcType
    | funcType
    ;
argType
    : dataType
    ;
argmode
    : IN
    | OUT
    | INOUT
    | IN OUT
    | VARIADIC
    ;
argClass
    : IN
    | OUT
    | INOUT
    | IN OUT
    | VARIADIC
    ;

funcArgsList
    : funcArg (COMMA_ funcArg)*
    ;

nonReservedWordOrSconst
    : nonReservedWord
    | STRING_
    ;

fileName
    : STRING_
    ;

roleList
    : roleSpec (COMMA_ roleSpec)*
    ;

setResetClause
    : SET setRest
    | RESET resetRest
    ;

setRest
    : TRANSACTION transactionModeList
    | SESSION CHARACTERISTICS AS TRANSACTION transactionModeList
    | setRestMore
    ;

transactionModeList
    : transactionModeItem (COMMA_? transactionModeItem)*
    ;

transactionModeItem
    : ISOLATION LEVEL isoLevel
    | READ ONLY
    | READ WRITE
    | NOT? DEFERRABLE
    ;

setRestMore
    : genericSet
    | varName FROM CURRENT
    | TIME ZONE zoneValue
    | CATALOG STRING_
    | SCHEMA STRING_
    | NAMES encoding?
    | ROLE nonReservedWord | STRING_
    | SESSION AUTHORIZATION nonReservedWord | STRING_
    | SESSION AUTHORIZATION DEFAULT
    | XML OPTION documentOrContent
    | TRANSACTION SNAPSHOT STRING_
    ;

encoding
    : STRING_
    | DEFAULT
    ;

genericSet
    : varName (EQ_|TO) (varList | DEFAULT)
    ;



resetRest
    : TIME ZONE
    | TRANSACTION ISOLATION LEVEL
    | SESSION AUTHORIZATION
    | viewName
    | ALL
    ;

genericReset
    : (owner DOT_)* name
    | ALL
    ;

relationExprList
    : relationExpr (COMMA_ relationExpr)*
    ;

relationExpr
    : tableName (ASTERISK_)?
    | LP_? tableName RP_?
    ;

commonFuncOptItem
    : CALLED ON NULL INPUT
    | RETURNS NULL ON NULL INPUT
    | STRICT
    | IMMUTABLE
    | STABLE
    | VOLATILE
    | EXTERNAL? SECURITY (INVOKER | DEFINER)
    | NOT? LEAKPROOF
    | COST numericOnly
    | ROWS numericOnly
    | SUPPORT funcName
    | PARALLEL (UNSAFE | RESTRICTED | SAFE)
    | functionSetResetClause
    ;

functionSetResetClause
    : SET setRestMore
    | RESET resetRest
    ;

rowSecurityCmd
    : ALL | SELECT | INSERT | UPDATE | DELETE
    ;

event
    : SELECT | UPDATE | DELETE | INSERT
    ;

ifNotExists
    : IF NOT EXISTS
    ;

ifExists
    : IF EXISTS
    ;

booleanValue
    : TRUE | ON | FALSE | OFF | NUMBER_
    ;

hostVariable
    : (COLON_)? identifier
    ;
stmt
    : (beginBlock
    | insert
    | update
    | delete
    | select
    ) SEMI_
    ;
beginBlock
    : beginTransaction identifier? stmt* end
    ;

tablebaseName
    : (owner DOT_?)* name
    ;
savepointName
    : name
    ;
sequenceName
    : (owner DOT_?)* name
    ;
domainName
    : (owner DOT_?)* name
    ;
argName
    : name
    ;
langName
    : name
    ;
loidList
    : numericOnly (COMMA_ numericOnly)*
    ;
tablespaceName
    : name
    ;
fdwName
    : name
    ;
serverName
    : name
    ;
sevepointName
    : name
    ;
objectName
    : (owner DOT_?)* name
    ;

searchEeqColName
    : columnName
    ;
cycleMarkColName
    : columnName
    ;
cycleMarkValue
    : value
    ;
cycleMarkDefault
    : value
    ;
cyclePathColName
    : identifier
    ;
queryName
    : name
    ;
viewName
    : (owner DOT_)* name
    ;

roleName
    : name
    ;
userName
    : name
    ;

literals
    : stringLiterals
    | numberLiterals
    | dateTimeLiterals
    | hexadecimalLiterals
    | bitValueLiterals
    | booleanLiterals
    | nullValueLiterals
    ;


stringLiterals
    : STRING_
    | NCHAR_TEXT
    | UCHAR_TEXT
    ;

dateTimeLiterals
    : (DATE | TIME | TIMESTAMP) stringLiterals
    | LBE_ identifier stringLiterals RBE_
    ;

hexadecimalLiterals
    : HEX_DIGIT_
    ;

bitValueLiterals
    : BIT_NUM_
    ;

booleanLiterals
    : TRUE | FALSE
    ;

nullValueLiterals
    : NULL
    ;
aggrName
    : (owner DOT_)* name
    ;
collationName
    : (owner DOT_)* name
    ;

