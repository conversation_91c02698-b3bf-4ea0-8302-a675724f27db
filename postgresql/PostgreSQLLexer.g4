
lexer grammar PostgreSQLLexer;

options {
    superClass = PostgreSQLLexerBase;
    caseInsensitive = true;
}

@header {
    import com.dc.parser.ext.postgresql.parser.PostgreSQLLexerBase;
}


ABORT             : 'ABORT';
ABSOLUTE          : 'ABSOLUTE';
ACCESS            : 'ACCESS';
ACTION            : 'ACTION';
ADD               : 'ADD';
ADMIN             : 'ADMIN';
AFTER             : 'AFTER';
AGGREGATE         : 'AGGREGATE';
ALL               : 'ALL';
ALSO              : 'ALSO';
ALTER             : 'ALTER';
ALWAYS            : 'ALWAYS';
ANALYSE           : 'ANALYSE';
ANALYZE           : 'ANALYZE';
AND               : 'AND';
ANY               : 'ANY';
ARRAY             : 'ARRAY';
AS                : 'AS';
ASC               : 'ASC';
ASENSITIVE        : 'ASENSITIVE';
ASSERTION         : 'ASSERTION';
DESCRIPTOR        : 'DESCRIPTOR';
ASSIGNMENT        : 'ASSIGNMENT';
ASYMMETRIC        : 'ASYMMETRIC';
AT                : 'AT';
ATTACH            : 'ATTACH';
ATTRIBUTE         : 'ATTRIBUTE';
AUTHORIZATION     : 'AUTHORIZATION';
BACKWARD          : 'BACKWARD';
BEFORE            : 'BEFORE';
BEGIN             : 'BEGIN';
BETWEEN           : 'BETWEEN';
BIGINT            : 'BIGINT';
BIGSERIAL         : 'BIGSERIAL';
BINARY            : 'BINARY';
BIT               : 'BIT';
BOOLEAN           : 'BOOLEAN';
BOTH              : 'BOTH';
BOX               : 'BOX';
BY                : 'BY';
BYPASSRLS         : 'BYPASSRLS';
BYTEA             : 'BYTEA';
CACHE             : 'CACHE';
CALL              : 'CALL';
CALLED            : 'CALLED';
CASCADE           : 'CASCADE';
CASCADED          : 'CASCADED';
CASE              : 'CASE';
CAST              : 'CAST';
CATALOG           : 'CATALOG';
CHAIN             : 'CHAIN';
CHAR              : 'CHAR';
CHARACTER         : 'CHARACTER';
CHARACTERISTICS   : 'CHARACTERISTICS';
CHECK             : 'CHECK';
CHECKPOINT        : 'CHECKPOINT';
CIDR              : 'CIDR';
CIRCLE            : 'CIRCLE';
CLASS             : 'CLASS';
CLOSE             : 'CLOSE';
CLUSTER           : 'CLUSTER';
COALESCE          : 'COALESCE';
COLLATE           : 'COLLATE';
COLLATION         : 'COLLATION';
COLUMN            : 'COLUMN';
COLUMNS           : 'COLUMNS';
COMMENT           : 'COMMENT';
COMMENTS          : 'COMMENTS';
COMMIT            : 'COMMIT';
COMMITTED         : 'COMMITTED';
COMPRESSION       : 'COMPRESSION';
CONCURRENTLY      : 'CONCURRENTLY';
CONFIGURATION     : 'CONFIGURATION';
CONFLICT          : 'CONFLICT';
CONNECT           : 'CONNECT';
CONNECTION        : 'CONNECTION';
CONSTRAINT        : 'CONSTRAINT';
CONSTRAINTS       : 'CONSTRAINTS';
CONTENT           : 'CONTENT';
CONTINUE          : 'CONTINUE';
CONVERSION        : 'CONVERSION';
COPY              : 'COPY';
COST              : 'COST';
CREATE            : 'CREATE';
CREATEDB          : 'CREATEDB';
CREATEROLE        : 'CREATEROLE';
CROSS             : 'CROSS';
CSV               : 'CSV';
CUBE              : 'CUBE';
CURRENT           : 'CURRENT';
CURRENT_CATALOG   : 'CURRENT_CATALOG';
CURRENT_DATE      : 'CURRENT_DATE';
CURRENT_ROLE      : 'CURRENT_ROLE';
CURRENT_SCHEMA    : 'CURRENT_SCHEMA';
CURRENT_TIME      : 'CURRENT_TIME';
CURRENT_TIMESTAMP : 'CURRENT_TIMESTAMP';
CURRENT_USER      : 'CURRENT_USER';
CURSOR            : 'CURSOR';
CYCLE             : 'CYCLE';
DATA              : 'DATA';
DATABASE          : 'DATABASE';
DATE              : 'DATE';
DATERANGE         : 'DATERANGE';
DAY               : 'DAY';
DEALLOCATE        : 'DEALLOCATE';
DEC               : 'DEC';
DECIMAL           : 'DECIMAL';
DECLARE           : 'DECLARE';
DEFAULT           : 'DEFAULT';
DEFAULTS          : 'DEFAULTS';
DEFERRABLE        : 'DEFERRABLE';
DEFERRED          : 'DEFERRED';
DEFINER           : 'DEFINER';
DELETE            : 'DELETE';
DELIMITER         : 'DELIMITER';
DELIMITERS        : 'DELIMITERS';
DEPENDS           : 'DEPENDS';
DESC              : 'DESC';
DETACH            : 'DETACH';
DICTIONARY        : 'DICTIONARY';
DISABLE           : 'DISABLE';
DISCARD           : 'DISCARD';
DISTINCT          : 'DISTINCT';
DO                : 'DO';
DOCUMENT          : 'DOCUMENT';
DOMAIN            : 'DOMAIN';
DOUBLE            : 'DOUBLE';
DROP              : 'DROP';
EACH              : 'EACH';
ELSE              : 'ELSE';
ENABLE            : 'ENABLE';
ENCODING          : 'ENCODING';
ENCRYPTED         : 'ENCRYPTED';
END               : 'END';
ENUM              : 'ENUM';
ESCAPE            : 'ESCAPE';
EVENT             : 'EVENT';
EXCEPT            : 'EXCEPT';
EXCLUDE           : 'EXCLUDE';
EXCLUDING         : 'EXCLUDING';
EXCLUSIVE         : 'EXCLUSIVE';
EXECUTE           : 'EXECUTE';
EXISTS            : 'EXISTS';
EXPLAIN           : 'EXPLAIN';
EXPRESSION        : 'EXPRESSION';
EXTENDED          : 'EXTENDED';
EXTENSION         : 'EXTENSION';
EXTERNAL          : 'EXTERNAL';
EXTRACT           : 'EXTRACT';
FALSE             : 'FALSE';
FAMILY            : 'FAMILY';
FETCH             : 'FETCH';
FILTER            : 'FILTER';
FINALIZE          : 'FINALIZE';
FIRST             : 'FIRST';
FLOAT             : 'FLOAT';
FLOAT4            : 'FLOAT4';
FLOAT8            : 'FLOAT8';
FOLLOWING         : 'FOLLOWING';
FOR               : 'FOR';
FORCE             : 'FORCE';
FORCE_NOT_NULL    : 'FORCE_NOT_NULL';
FORCE_NULL        : 'FORCE_NULL';
FORCE_QUOTE       : 'FORCE_QUOTE';
FOREIGN           : 'FOREIGN';
FORMAT            : 'FORMAT';
FORWARD           : 'FORWARD';
FREEZE            : 'FREEZE';
FROM              : 'FROM';
FULL              : 'FULL';
FUNCTION          : 'FUNCTION';
FUNCTIONS         : 'FUNCTIONS';
GENERATED         : 'GENERATED';
GLOBAL            : 'GLOBAL';
GRANT             : 'GRANT';
GRANTED           : 'GRANTED';
GREATEST          : 'GREATEST';
GROUP             : 'GROUP';
GROUPING          : 'GROUPING';
GROUPS            : 'GROUPS';
HANDLER           : 'HANDLER';
HAVING            : 'HAVING';
HEADER            : 'HEADER';
HOLD              : 'HOLD';
HOUR              : 'HOUR';
IDENTITY          : 'IDENTITY';
IF                : 'IF';
ILIKE             : 'ILIKE';
IMMEDIATE         : 'IMMEDIATE';
IMMUTABLE         : 'IMMUTABLE';
IMPLICIT          : 'IMPLICIT';
IMPORT            : 'IMPORT';
IN                : 'IN';
INCLUDE           : 'INCLUDE';
INCLUDING         : 'INCLUDING';
INCREMENT         : 'INCREMENT';
INDEX             : 'INDEX';
INDEXES           : 'INDEXES';
INET              : 'INET';
INHERIT           : 'INHERIT';
INHERITS          : 'INHERITS';
INITIALLY         : 'INITIALLY';
INLINE            : 'INLINE';
INNER             : 'INNER';
INOUT             : 'INOUT';
INPUT             : 'INPUT';
INSENSITIVE       : 'INSENSITIVE';
INSERT            : 'INSERT';
INSTANCE          : 'INSTANCE';
INSTEAD           : 'INSTEAD';
INT               : 'INT';
INT2              : 'INT2';
INT4              : 'INT4';
INT4RANGE         : 'INT4RANGE';
INT8              : 'INT8';
INT8RANGE         : 'INT8RANGE';
INTEGER           : 'INTEGER';
INTERSECT         : 'INTERSECT';
INTERVAL          : 'INTERVAL';
INTO              : 'INTO';
INVOKER           : 'INVOKER';
IS                : 'IS';
ISNULL            : 'ISNULL';
ISOLATION         : 'ISOLATION';
JOIN              : 'JOIN';
JSON              : 'JSON';
KEY               : 'KEY';
LABEL             : 'LABEL';
LANGUAGE          : 'LANGUAGE';
LARGE             : 'LARGE';
LAST              : 'LAST';
LATERAL           : 'LATERAL';
LEADING           : 'LEADING';
LEAKPROOF         : 'LEAKPROOF';
LEAST             : 'LEAST';
LEFT              : 'LEFT';
LEVEL             : 'LEVEL';
LIKE              : 'LIKE';
LIMIT             : 'LIMIT';
LINE              : 'LINE';
LISTEN            : 'LISTEN';
LOAD              : 'LOAD';
LOCAL             : 'LOCAL';
LOCALTIME         : 'LOCALTIME';
LOCALTIMESTAMP    : 'LOCALTIMESTAMP';
LOCATION          : 'LOCATION';
LOCK              : 'LOCK';
LOCKED            : 'LOCKED';
LOGGED            : 'LOGGED';
LOGIN             : 'LOGIN';
LSEG              : 'LSEG';
MACADDR           : 'MACADDR';
MACADDR8          : 'MACADDR8';
MAIN              : 'MAIN';
MAPPING           : 'MAPPING';
MATCH             : 'MATCH';
MATERIALIZED      : 'MATERIALIZED';
MAXVALUE          : 'MAXVALUE';
METHOD            : 'METHOD';
MICROSECOND       : 'MICROSECOND';
MINUTE            : 'MINUTE';
MINVALUE          : 'MINVALUE';
MOD               : 'MOD';
MODE              : 'MODE';
MONTH             : 'MONTH';
MOVE              : 'MOVE';
NAME              : 'NAME';
NAMES             : 'NAMES';
NATIONAL          : 'NATIONAL';
NATURAL           : 'NATURAL';
NCHAR             : 'NCHAR';
NEW               : 'NEW';
NEXT              : 'NEXT';
NFC               : 'NFC';
NFD               : 'NFD';
NFKC              : 'NFKC';
NFKD              : 'NFKD';
NO                : 'NO';
NOBYPASSRLS       : 'NOBYPASSRLS';
NOCREATEDB        : 'NOCREATEDB';
NOCREATEROLE      : 'NOCREATEROLE';
NOINHERIT         : 'NOINHERIT';
NOLOGIN           : 'NOLOGIN';
NONE              : 'NONE';
NOREPLICATION     : 'NOREPLICATION';
NORMALIZE         : 'NORMALIZE';
NORMALIZED        : 'NORMALIZED';
NOSUPERUSER       : 'NOSUPERUSER';
NOT               : 'NOT';
NOTHING           : 'NOTHING';
NOTIFY            : 'NOTIFY';
NOTNULL           : 'NOTNULL';
NOWAIT            : 'NOWAIT';
NULL              : 'NULL';
NULLIF            : 'NULLIF';
NULLS             : 'NULLS';
NUMERIC           : 'NUMERIC';
NUMRANGE          : 'NUMRANGE';
OBJECT            : 'OBJECT';
OF                : 'OF';
OFF               : 'OFF';
OFFSET            : 'OFFSET';
OIDS              : 'OIDS';
OLD               : 'OLD';
ON                : 'ON';
ONLY              : 'ONLY';
OPEN              : 'OPEN';
OPERATOR          : 'OPERATOR';
OPTION            : 'OPTION';
OPTIONS           : 'OPTIONS';
OR                : 'OR';
ORDER             : 'ORDER';
ORDINALITY        : 'ORDINALITY';
OTHERS            : 'OTHERS';
OUT               : 'OUT';
OUTER             : 'OUTER';
OVER              : 'OVER';
OVERLAPS          : 'OVERLAPS';
OVERLAY           : 'OVERLAY';
OVERRIDING        : 'OVERRIDING';
OWNED             : 'OWNED';
OWNER             : 'OWNER';
PARALLEL          : 'PARALLEL';
PARAM             : 'PARAM';
PARSER            : 'PARSER';
PARTIAL           : 'PARTIAL';
PARTITION         : 'PARTITION';
PASSING           : 'PASSING';
PASSWORD          : 'PASSWORD';
PATH              : 'PATH';
PLACING           : 'PLACING';
PLAIN             : 'PLAIN';
PLANS             : 'PLANS';
POINT             : 'POINT';
POLICY            : 'POLICY';
POLYGON           : 'POLYGON';
POSITION          : 'POSITION';
PRECEDING         : 'PRECEDING';
PRECISION         : 'PRECISION';
PREPARE           : 'PREPARE';
PREPARED          : 'PREPARED';
PRESERVE          : 'PRESERVE';
PRIMARY           : 'PRIMARY';
PRIOR             : 'PRIOR';
PRIVILEGES        : 'PRIVILEGES';
PROCEDURAL        : 'PROCEDURAL';
PROCEDURE         : 'PROCEDURE';
PROCEDURES        : 'PROCEDURES';
PROGRAM           : 'PROGRAM';
PUBLICATION       : 'PUBLICATION';
QUARTER           : 'QUARTER';
QUOTE             : 'QUOTE';
RANGE             : 'RANGE';
READ              : 'READ';
REAL              : 'REAL';
REASSIGN          : 'REASSIGN';
RECHECK           : 'RECHECK';
RECURSIVE         : 'RECURSIVE';
REF               : 'REF';
REFERENCES        : 'REFERENCES';
REFERENCING       : 'REFERENCING';
REFRESH           : 'REFRESH';
REINDEX           : 'REINDEX';
RELATIVE          : 'RELATIVE';
RELEASE           : 'RELEASE';
RENAME            : 'RENAME';
REPEATABLE        : 'REPEATABLE';
REPLACE           : 'REPLACE';
REPLICA           : 'REPLICA';
REPLICATION       : 'REPLICATION';
RESET             : 'RESET';
RESTART           : 'RESTART';
RESTRICT          : 'RESTRICT';
RETURNING         : 'RETURNING';
RETURNS           : 'RETURNS';
REVOKE            : 'REVOKE';
RIGHT             : 'RIGHT';
ROLE              : 'ROLE';
ROLLBACK          : 'ROLLBACK';
ROLLUP            : 'ROLLUP';
ROUTINE           : 'ROUTINE';
ROUTINES          : 'ROUTINES';
ROW               : 'ROW';
ROWS              : 'ROWS';
RULE              : 'RULE';
SAVEPOINT         : 'SAVEPOINT';
SCHEMA            : 'SCHEMA';
SCHEMAS           : 'SCHEMAS';
SCROLL            : 'SCROLL';
SEARCH            : 'SEARCH';
SECOND            : 'SECOND';
SECURITY          : 'SECURITY';
SELECT            : 'SELECT';
SEQUENCE          : 'SEQUENCE';
SEQUENCES         : 'SEQUENCES';
SERIAL            : 'SERIAL';
SERIALIZABLE      : 'SERIALIZABLE';
SERVER            : 'SERVER';
SESSION           : 'SESSION';
SESSION_USER      : 'SESSION_USER';
SET               : 'SET';
SETOF             : 'SETOF';
SETS              : 'SETS';
SHARE             : 'SHARE';
SHOW              : 'SHOW';
SIMILAR           : 'SIMILAR';
SIMPLE            : 'SIMPLE';
SMALLINT          : 'SMALLINT';
SMALLSERIAL       : 'SMALLSERIAL';
SNAPSHOT          : 'SNAPSHOT';
SOME              : 'SOME';
SQL               : 'SQL';
STABLE            : 'STABLE';
STANDALONE        : 'STANDALONE';
START             : 'START';
STATEMENT         : 'STATEMENT';
STATISTICS        : 'STATISTICS';
STDIN             : 'STDIN';
STDOUT            : 'STDOUT';
STORAGE           : 'STORAGE';
STORED            : 'STORED';
STRICT            : 'STRICT';
STRIP             : 'STRIP';
SUBSCRIPTION      : 'SUBSCRIPTION';
SUBSTRING         : 'SUBSTRING';
SUPERUSER         : 'SUPERUSER';
SUPPORT           : 'SUPPORT';
SYMMETRIC         : 'SYMMETRIC';
SYSID             : 'SYSID';
SYSTEM            : 'SYSTEM';
TABLE             : 'TABLE';
TABLES            : 'TABLES';
TABLESAMPLE       : 'TABLESAMPLE';
TABLESPACE        : 'TABLESPACE';
TEMP              : 'TEMP';
TEMPLATE          : 'TEMPLATE';
TEMPORARY         : 'TEMPORARY';
TEXT              : 'TEXT';
THEN              : 'THEN';
TIES              : 'TIES';
TIME              : 'TIME';
TIMESTAMP         : 'TIMESTAMP';
TO                : 'TO';
TRAILING          : 'TRAILING';
TRANSACTION       : 'TRANSACTION';
TRANSFORM         : 'TRANSFORM';
TREAT             : 'TREAT';
TRIGGER           : 'TRIGGER';
TRIM              : 'TRIM';
TRUE              : 'TRUE';
TRUNCATE          : 'TRUNCATE';
TRUSTED           : 'TRUSTED';
TSQUERY           : 'TSQUERY';
TSRANGE           : 'TSRANGE';
TSTZRANGE         : 'TSTZRANGE';
TSVECTOR          : 'TSVECTOR';
TYPE              : 'TYPE';
TYPES             : 'TYPES';
UESCAPE           : 'UESCAPE';
UNBOUNDED         : 'UNBOUNDED';
UNCOMMITTED       : 'UNCOMMITTED';
UNENCRYPTED       : 'UNENCRYPTED';
UNION             : 'UNION';
UNIQUE            : 'UNIQUE';
UNKNOWN           : 'UNKNOWN';
UNLISTEN          : 'UNLISTEN';
UNLOGGED          : 'UNLOGGED';
UNTIL             : 'UNTIL';
UPDATE            : 'UPDATE';
USAGE             : 'USAGE';
USER              : 'USER';
USING             : 'USING';
VACUUM            : 'VACUUM';
VALID             : 'VALID';
VALIDATE          : 'VALIDATE';
VALIDATOR         : 'VALIDATOR';
VALUE             : 'VALUE';
VALUES            : 'VALUES';
VARBIT            : 'VARBIT';
VARCHAR           : 'VARCHAR';
VARIADIC          : 'VARIADIC';
VARYING           : 'VARYING';
VERBOSE           : 'VERBOSE';
VERSION           : 'VERSION';
VIEW              : 'VIEW';
VIEWS             : 'VIEWS';
VOLATILE          : 'VOLATILE';
WEEK              : 'WEEK';
WHEN              : 'WHEN';
WHERE             : 'WHERE';
WHITESPACE        : 'WHITESPACE';
WINDOW            : 'WINDOW';
WITH              : 'WITH';
WITHIN            : 'WITHIN';
WITHOUT           : 'WITHOUT';
WORK              : 'WORK';
WRAPPER           : 'WRAPPER';
WRITE             : 'WRITE';
XML               : 'XML';
XMLATTRIBUTES     : 'XMLATTRIBUTES';
XMLCONCAT         : 'XMLCONCAT';
XMLELEMENT        : 'XMLELEMENT';
XMLEXISTS         : 'XMLEXISTS';
XMLFOREST         : 'XMLFOREST';
XMLNAMESPACES     : 'XMLNAMESPACES';
XMLPARSE          : 'XMLPARSE';
XMLPI             : 'XMLPI';
XMLROOT           : 'XMLROOT';
XMLSERIALIZE      : 'XMLSERIALIZE';
XMLTABLE          : 'XMLTABLE';
YEAR              : 'YEAR';
YES               : 'YES';
ZONE              : 'ZONE';
RECEIVE           : 'RECEIVE';
SEND              : 'SEND';
TYPMOD_IN         : 'TYPMOD_IN';
TYPMOD_OUT        : 'TYPMOD_OUT';
SUBSCRIPT         : 'SUBSCRIPT';
PUBLIC            : 'PUBLIC';
ALLOW_CONNECTIONS : 'ALLOW_CONNECTIONS';
IS_TEMPLATE       : 'IS_TEMPLATE';
HASHES            : 'HASHES';
MERGES            : 'MERGES';
COMMUTATOR        : 'COMMUTATOR';
NEGATOR           : 'NEGATOR';
W_SKIP              : 'SKIP';
RETURN              : 'RETURN';
YAML                : 'YAML';
MEMORY               : 'MEMORY';
SUMMARY              : 'SUMMARY';
TIMING               : 'TIMING';
WAL                  : 'WAL';
SERIALIZE            : 'SERIALIZE';
BUFFERS              : 'BUFFERS';
GENERIC_PLAN         : 'GENERIC_PLAN';
SETTINGS             : 'SETTINGS';
COSTS                : 'COSTS';
MERGE                : 'MERGE';
MATCHED              : 'MATCHED';
SOURCE              : 'SOURCE';
TARGET              : 'TARGET';
MAINTAIN            : 'MAINTAIN';
BREADTH             : 'BREADTH';
DEPTH               : 'DEPTH';
DISABLE_PAGE_SKIPPING : 'DISABLE_PAGE_SKIPPING';
SKIP_LOCKED         : 'SKIP_LOCKED';
INDEX_CLEANUP       : 'INDEX_CLEANUP';
PROCESS_MAIN        : 'PROCESS_MAIN';
PROCESS_TOAST       : 'PROCESS_TOAST';
SKIP_DATABASE_STATS : 'SKIP_DATABASE_STATS';
ONLY_DATABASE_STATS : 'ONLY_DATABASE_STATS';
BUFFER_USAGE_LIMIT  : 'BUFFER_USAGE_LIMIT';
AUTO                : 'AUTO';
PARAMETER           : 'PARAMETER';
UNSAFE          : 'UNSAFE';
RESTRICTED      : 'RESTRICTED';
SAFE            : 'SAFE';
ON_ERROR        : 'ON_ERROR';
LOG_VERBOSITY   : 'LOG_VERBOSITY';
STRATEGY        : 'STRATEGY';
LOCALE          : 'LOCALE';
LC_COLLATE      : 'LC_COLLATE';
LC_CTYPE        : 'LC_CTYPE';
BUILTIN_LOCALE  : 'BUILTIN_LOCALE';
ICU_LOCALE      : 'ICU_LOCALE';
ICU_RULES       : 'ICU_RULES';
LOCALE_PROVIDER : 'LOCALE_PROVIDER';
COLLATION_VERSION : 'COLLATION_VERSION';
OID              : 'OID';
PERMISSIVE       : 'PERMISSIVE';
RESTRICTIVE      : 'RESTRICTIVE';
MODULUS          : 'MODULUS';
REMAINDER       : 'REMAINDER';
LIST            : 'LIST';
HASH            : 'HASH';


AND_:                '&&';
OR_:                 '||';
NOT_:                '!';
TILDE_:              '~';
VERTICAL_BAR_:       '|';
AMPERSAND_:          '&';
SIGNED_LEFT_SHIFT_:  '<<';
SIGNED_RIGHT_SHIFT_: '>>';
CARET_:              '^';
MOD_:                '%';
COLON_:              ':';
PLUS_:               '+';
MINUS_:              '-';
ASTERISK_:           '*';
SLASH_:              '/';
BACKSLASH_:          '\\';
DOT_:                '.';
DOT_ASTERISK_:       '.*';
SAFE_EQ_:            '<=>';
DEQ_:                '==';
EQ_:                 '=';
CQ_:                 ':=';
NEQ_:                '<>' | '!=';
GT_:                 '>';
GTE_:                '>=';
LT_:                 '<';
LTE_:                '<=';
POUND_:              '#';
LP_:                 '(';
RP_:                 ')';
LBE_:                '{';
RBE_:                '}';
LBT_:                '[';
RBT_:                ']';
COMMA_:              ',';
DQ_:                 '"';
SQ_ :                '\'';
BQ_:                 '`';
QUESTION_:           '?';
DOLLAR_:             '$';
AT_:                 '@';
SEMI_:               ';';
TILDE_TILDE_:        '~~';
NOT_TILDE_TILDE_:    '!~~';
TYPE_CAST_:          '::';
ILIKE_:              '~~*';
NOT_ILIKE_:          '!~~*';
UNICODE_ESCAPE:      'U&' | 'u&';
JSON_EXTRACT_:       '->';
JSON_EXTRACT_TEXT_:  '->>';
JSON_PATH_EXTRACT_:  '#>';
JSON_PATH_EXTRACT_TEXT_:        '#>>';
JSONB_CONTAIN_RIGHT_:           '@>';
JSONB_CONTAIN_LEFT_:            '<@';
JSONB_CONTAIN_ALL_TOP_KEY_:     '?&';
JSONB_PATH_DELETE_:             '#-';
JSONB_PATH_CONTAIN_ANY_VALUE_:  '@?';
JSONB_PATH_PREDICATE_CHECK_:    '@@';
GEOMETRIC_LENGTH_:              '@-@';
GEOMETRIC_DISTANCE_:            '<->';
GEOMETRIC_EXTEND_RIGHT_:        '&<';
GEOMETRIC_EXTEND_LEFT_:         '&>';
GEOMETRIC_STRICT_BELOW_:        '<<|';
GEOMETRIC_STRICT_ABOVE_:        '|>>';
GEOMETRIC_EXTEND_ABOVE_:        '&<|';
GEOMETRIC_EXTEND_BELOW_:        '|&>';
GEOMETRIC_BELOW_:               '<^';
GEOMETRIC_ABOVE_:               '>^';
GEOMETRIC_INTERSECT_:           '?#';
GEOMETRIC_PERPENDICULAR_:       '?-|';
GEOMETRIC_SAME_AS_:             '~=';

WS : [ \t\r\n\u3000] + ->skip;

BLOCK_COMMENT:  '/*' .*? '*/' -> channel(HIDDEN);
INLINE_COMMENT: '--' ~[\r\n]* ('\r'? '\n' | EOF) -> channel(HIDDEN);


IDENTIFIER_
    : IDENTIFIER_START_CHAR IDENTIFIER_CHAR*
    | DQ_ ~'"'+ DQ_
    ;

STRING_
    : 'E'? SQ_ ('\\'. | '\'\'' | ~('\'' | '\\'))* SQ_
    ;

NUMBER_
    : INT_? DOT_? INT_ ('E' (PLUS_ | MINUS_)? INT_)?
    ;

HEX_DIGIT_
    : '0x' HEX_+ | 'X' SQ_ HEX_+ SQ_
    ;

BIT_NUM_
    : '0b' ('0' | '1')+ | 'B' SQ_ ('0' | '1')+ SQ_
    ;

fragment INT_
    : [0-9]+
    ;

fragment HEX_
    : [0-9a-f]
    ;

//fragment IDENTIFIER_START_CHAR
//   : [a-z_]
//   | [\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u00FF]
//   | [\u0100-\uD7FF\uE000-\uFFFF]
//   | [\uD800-\uDBFF] [\uDC00-\uDFFF]
//   ;
fragment IDENTIFIER_START_CHAR options {
    caseInsensitive = false;
}: // these are the valid identifier start characters below 0x7F
    [a-zA-Z_]
    | // these are the valid characters from 0x80 to 0xFF
    [\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u00FF]
    |                               // these are the letters above 0xFF which only need a single UTF-16 code unit
    [\u0100-\uD7FF\uE000-\uFFFF]
    |                               // letters which require multiple UTF-16 code units
    [\uD800-\uDBFF] [\uDC00-\uDFFF]
;
fragment IDENTIFIER_CHAR
   : STRICT_IDENTIFIER_CHAR
   | '$'
   ;

fragment STRICT_IDENTIFIER_CHAR
   : IDENTIFIER_START_CHAR
   | [0-9]
   ;

BEGIN_DOLLAR_STRING_CONSTANT
   : '$' TAG? '$'
   {pushTag();} -> pushMode (DOLLAR_QUOTED_STRING_MODE)
   ;

mode DOLLAR_QUOTED_STRING_MODE;
//DOLLAR_TEXT
//   : ~ '$'+
//   | '$' ~ '$'*
//   ;
DOLLAR_TEXT
   : (~'$' | '\n')+  // 允许换行符
   ;
fragment TAG
   : IDENTIFIER_START_CHAR STRICT_IDENTIFIER_CHAR*
   ;
END_DOLLAR_STRING_CONSTANT
   : ('$' TAG? '$')
   {isTag()}?
   {popTag();} -> popMode
   ;
NCHAR_TEXT: 'N' STRING_;
UCHAR_TEXT: 'U' STRING_;