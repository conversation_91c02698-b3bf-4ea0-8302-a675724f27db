ALTER CONVERSION iso_8859_1_to_utf8 RENAME TO latin1_to_unicode;
ALTER CONVERSION iso_8859_1_to_utf8 OWNER TO joe;

ALTER DEFAULT PRIVILEGES IN SCHEMA myschema GRANT SELECT ON TABLES TO PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA myschema GRANT INSERT ON TABLES TO webuser;
ALTER DEFAULT PRIVILEGES IN SCHEMA myschema REVOKE SELECT ON TABLES FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA myschema REVOKE INSERT ON TABLES FROM webuser;
ALTER DEFAULT PRIVILEGES FOR ROLE admin REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;
ALTER DEFAULT PRIVILEGES IN SCHEMA public REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;

ALTER DOMAIN zipcode SET NOT NULL;
ALTER DOMAIN zipcode DROP NOT NULL;
ALTER DOMAIN zipcode ADD CONSTRAINT zipchk CHECK (char_length(VALUE) = 5);
ALTER DOMAIN zipcode DROP CONSTRAINT zipchk;
ALTER DOMAIN zipcode RENAME CONSTRAINT zipchk TO zip_check;
ALTER DOMAIN zipcode SET SCHEMA customers;

ALTER EXTENSION hstore UPDATE TO '2.0';
ALTER EXTENSION hstore SET SCHEMA utils;
ALTER EXTENSION hstore ADD FUNCTION populate_record(anyelement, hstore);

ALTER FOREIGN DATA WRAPPER dbi OPTIONS (ADD foo '1', DROP bar);
ALTER FOREIGN DATA WRAPPER dbi VALIDATOR bob.myvalidator;
ALTER FOREIGN TABLE distributors ALTER COLUMN street SET NOT NULL;
ALTER FOREIGN TABLE myschema.distributors OPTIONS (ADD opt1 'value', SET opt2 'value2', DROP opt3);

ALTER FUNCTION sqrt(integer) RENAME TO square_root;
ALTER FUNCTION sqrt(integer) OWNER TO joe;
ALTER FUNCTION sqrt(integer) SET SCHEMA maths;
ALTER FUNCTION sqrt(integer) DEPENDS ON EXTENSION mathlib;
ALTER FUNCTION check_password(text) SET search_path = admin, pg_temp;
ALTER FUNCTION check_password(text) RESET search_path;

ALTER GROUP staff ADD USER karl, john;
ALTER GROUP workers DROP USER beth;

ALTER INDEX distributors RENAME TO suppliers;
ALTER INDEX distributors SET TABLESPACE fasttablespace;
ALTER INDEX distributors SET (fillfactor = 75);
REINDEX INDEX distributors;
CREATE INDEX coord_idx ON measured (x, y, (z + t));
ALTER INDEX coord_idx ALTER COLUMN 3 SET STATISTICS 1000;

ALTER MATERIALIZED VIEW foo RENAME TO bar;
ALTER OPERATOR @@ (text, text) OWNER TO joe;
ALTER OPERATOR && (int[], int[]) SET (RESTRICT = _int_contsel, JOIN = _int_contjoinsel);

ALTER OPERATOR FAMILY integer_ops USING btree ADD

  -- int4 vs int2
  OPERATOR 1 < (int4, int2) ,
  OPERATOR 2 <= (int4, int2) ,
  OPERATOR 3 = (int4, int2) ,
  OPERATOR 4 >= (int4, int2) ,
  OPERATOR 5 > (int4, int2) ,
  FUNCTION 1 btint42cmp(int4, int2) ,

  -- int2 vs int4
  OPERATOR 1 < (int2, int4) ,
  OPERATOR 2 <= (int2, int4) ,
  OPERATOR 3 = (int2, int4) ,
  OPERATOR 4 >= (int2, int4) ,
  OPERATOR 5 > (int2, int4) ,
  FUNCTION 1 btint24cmp(int2, int4) ;

ALTER OPERATOR FAMILY integer_ops USING btree DROP

  -- int4 vs int2
OPERATOR 1 (int4, int2) ,
  OPERATOR 2 (int4, int2) ,
  OPERATOR 3 (int4, int2) ,
  OPERATOR 4 (int4, int2) ,
  OPERATOR 5 (int4, int2) ,
  FUNCTION 1 (int4, int2) ,

  -- int2 vs int4
  OPERATOR 1 (int2, int4) ,
  OPERATOR 2 (int2, int4) ,
  OPERATOR 3 (int2, int4) ,
  OPERATOR 4 (int2, int4) ,
  OPERATOR 5 (int2, int4) ,
  FUNCTION 1 (int2, int4) ;

ALTER PROCEDURE insert_data(integer, integer) RENAME TO insert_record;
ALTER PROCEDURE insert_data(integer, integer) OWNER TO joe;
ALTER PROCEDURE insert_data(integer, integer) SET SCHEMA accounting;
ALTER PROCEDURE insert_data(integer, integer) DEPENDS ON EXTENSION myext;
ALTER PROCEDURE check_password(text) SET search_path = admin, pg_temp;
ALTER PROCEDURE check_password(text) RESET search_path;

ALTER PUBLICATION noinsert SET (publish = 'update, delete');
ALTER PUBLICATION mypublication ADD TABLE users (user_id, firstname), departments;
ALTER PUBLICATION mypublication SET TABLE users (user_id, firstname, lastname), TABLE departments;
ALTER PUBLICATION sales_publication ADD TABLES IN SCHEMA marketing, CURRENT_SCHEMA;
ALTER PUBLICATION production_publication ADD TABLE users, departments, TABLES IN SCHEMA production;

ALTER ROLE davide WITH PASSWORD 'hu8jmn3';
ALTER ROLE davide WITH PASSWORD NULL;
ALTER ROLE chris VALID UNTIL 'May 4 12:00:00 2015 +1';
ALTER ROLE fred VALID UNTIL 'infinity';
ALTER ROLE miriam CREATEROLE CREATEDB;
ALTER ROLE worker_bee SET maintenance_work_mem = 100000;
ALTER ROLE fred IN DATABASE devel SET client_min_messages = DEBUG;

ALTER ROUTINE foo(integer) RENAME TO foobar;
ALTER RULE notify_all ON emp RENAME TO notify_me;

ALTER SERVER foo OPTIONS (host 'foo', dbname 'foodb');
ALTER SERVER foo VERSION '8.4' OPTIONS (SET host 'baz');

ALTER SUBSCRIPTION mysub SET PUBLICATION insert_only;
ALTER SYSTEM SET wal_level = replica;
ALTER SYSTEM RESET wal_level;

ALTER TABLE distributors ADD COLUMN address varchar(30);
ALTER TABLE measurements
    ADD COLUMN mtime timestamp with time zone DEFAULT now();
ALTER TABLE transactions
    ADD COLUMN status varchar(30) DEFAULT 'old',
ALTER COLUMN status SET default 'current';

ALTER TABLE distributors DROP COLUMN address RESTRICT;
ALTER TABLE distributors
ALTER COLUMN address TYPE varchar(80),
    ALTER COLUMN name TYPE varchar(100);
ALTER TABLE foo
    ALTER COLUMN foo_timestamp SET DATA TYPE timestamp with time zone
    USING
        timestamp with time zone 'epoch' + foo_timestamp * interval '1 second';
ALTER TABLE foo
    ALTER COLUMN foo_timestamp DROP DEFAULT,
ALTER COLUMN foo_timestamp TYPE timestamp with time zone
    USING
        timestamp with time zone 'epoch' + foo_timestamp * interval '1 second',
    ALTER COLUMN foo_timestamp SET DEFAULT now();

ALTER TABLE distributors RENAME COLUMN address TO city;
ALTER TABLE distributors RENAME TO suppliers;
ALTER TABLE distributors RENAME CONSTRAINT zipchk TO zip_check;
ALTER TABLE distributors ALTER COLUMN street SET NOT NULL;
ALTER TABLE distributors ALTER COLUMN street DROP NOT NULL;
ALTER TABLE distributors ADD CONSTRAINT zipchk CHECK (char_length(zipcode) = 5);
ALTER TABLE distributors ADD CONSTRAINT zipchk CHECK (char_length(zipcode) = 5) NO INHERIT;
ALTER TABLE distributors DROP CONSTRAINT zipchk;
ALTER TABLE ONLY distributors DROP CONSTRAINT zipchk;
ALTER TABLE distributors ADD CONSTRAINT distfk FOREIGN KEY (address) REFERENCES addresses (address);
ALTER TABLE distributors ADD CONSTRAINT distfk FOREIGN KEY (address) REFERENCES addresses (address) NOT VALID;
ALTER TABLE distributors VALIDATE CONSTRAINT distfk;
ALTER TABLE distributors ADD CONSTRAINT dist_id_zipcode_key UNIQUE (dist_id, zipcode);
ALTER TABLE distributors ADD PRIMARY KEY (dist_id);
ALTER TABLE distributors SET TABLESPACE fasttablespace;
ALTER TABLE myschema.distributors SET SCHEMA yourschema;
CREATE UNIQUE INDEX CONCURRENTLY dist_id_temp_idx ON distributors (dist_id);
ALTER TABLE distributors DROP CONSTRAINT distributors_pkey,
    ADD CONSTRAINT distributors_pkey PRIMARY KEY USING INDEX dist_id_temp_idx;
ALTER TABLE measurement
    ATTACH PARTITION measurement_y2016m07 FOR VALUES FROM ('2016-07-01') TO ('2016-08-01');
ALTER TABLE cities
    ATTACH PARTITION cities_ab FOR VALUES IN ('a', 'b');
ALTER TABLE orders
    ATTACH PARTITION orders_p4 FOR VALUES WITH (MODULUS 4, REMAINDER 3);
ALTER TABLE cities
    ATTACH PARTITION cities_partdef DEFAULT;
ALTER TABLE measurement
    DETACH PARTITION measurement_y2015m12;

ALTER TEXT SEARCH CONFIGURATION my_config
  ALTER MAPPING REPLACE english WITH swedish;
ALTER TRIGGER emp_stamp ON emp RENAME TO emp_track_chgs;
ALTER TRIGGER emp_stamp ON emp DEPENDS ON EXTENSION emplib;
ALTER TYPE electronic_mail RENAME TO email;
ALTER TYPE email OWNER TO joe;
ALTER TYPE email SET SCHEMA customers;
ALTER TYPE compfoo ADD ATTRIBUTE f3 int;
ALTER TYPE colors ADD VALUE 'orange' AFTER 'red';
ALTER TYPE colors RENAME VALUE 'purple' TO 'mauve';
ALTER TYPE mytype SET (
    SEND = mytypesend,
    RECEIVE = mytyperecv
    );
ALTER USER MAPPING FOR bob SERVER foo OPTIONS (SET password 'public');
ALTER VIEW foo RENAME TO bar;
ALTER VIEW a_view ALTER COLUMN ts SET DEFAULT now();
CREATE TABLE base_table (id int, ts timestamptz);
CREATE VIEW a_view AS SELECT * FROM base_table;
ALTER VIEW a_view ALTER COLUMN ts SET DEFAULT now();
INSERT INTO base_table(id) VALUES(1);  -- ts will receive a NULL
INSERT INTO a_view(id) VALUES(2);  -- ts will receive the current time

CALL do_db_maintenance();
CLOSE liahona;
CLUSTER employees USING employees_ind;
CLUSTER employees;
CLUSTER;
COMMENT ON TABLE mytable IS 'This is my table.';
COMMENT ON TABLE mytable IS NULL;
COMMENT ON ACCESS METHOD gin IS 'GIN index access method';
COMMENT ON AGGREGATE my_aggregate (double precision) IS 'Computes sample variance';
COMMENT ON CAST (text AS int4) IS 'Allow casts from text to int4';
COMMENT ON COLLATION "fr_CA" IS 'Canadian French';
COMMENT ON COLUMN my_table.my_column IS 'Employee ID number';
COMMENT ON CONVERSION my_conv IS 'Conversion to UTF8';
COMMENT ON CONSTRAINT bar_col_cons ON bar IS 'Constrains column col';
COMMENT ON CONSTRAINT dom_col_constr ON DOMAIN dom IS 'Constrains col of domain';
COMMENT ON DATABASE my_database IS 'Development Database';
COMMENT ON DOMAIN my_domain IS 'Email Address Domain';
COMMENT ON EVENT TRIGGER abort_ddl IS 'Aborts all DDL commands';
COMMENT ON EXTENSION hstore IS 'implements the hstore data type';
COMMENT ON FOREIGN DATA WRAPPER mywrapper IS 'my foreign data wrapper';
COMMENT ON FOREIGN TABLE my_foreign_table IS 'Employee Information in other database';
COMMENT ON FUNCTION my_function (timestamp) IS 'Returns Roman Numeral';
COMMENT ON INDEX my_index IS 'Enforces uniqueness on employee ID';
COMMENT ON LANGUAGE plpython IS 'Python support for stored procedures';
COMMENT ON LARGE OBJECT 346344 IS 'Planning document';
COMMENT ON MATERIALIZED VIEW my_matview IS 'Summary of order history';
COMMENT ON OPERATOR ^ (text, text) IS 'Performs intersection of two texts';
COMMENT ON OPERATOR - (NONE, integer) IS 'Unary minus';
COMMENT ON OPERATOR CLASS int4ops USING btree IS '4 byte integer operators for btrees';
COMMENT ON OPERATOR FAMILY integer_ops USING btree IS 'all integer operators for btrees';
COMMENT ON POLICY my_policy ON mytable IS 'Filter rows by users';
COMMENT ON PROCEDURE my_proc (integer, integer) IS 'Runs a report';
COMMENT ON PUBLICATION alltables IS 'Publishes all operations on all tables';
COMMENT ON ROLE my_role IS 'Administration group for finance tables';
COMMENT ON ROUTINE my_routine (integer, integer) IS 'Runs a routine (which is a function or procedure)';
COMMENT ON RULE my_rule ON my_table IS 'Logs updates of employee records';
COMMENT ON SCHEMA my_schema IS 'Departmental data';
COMMENT ON SEQUENCE my_sequence IS 'Used to generate primary keys';
COMMENT ON SERVER myserver IS 'my foreign server';
COMMENT ON STATISTICS my_statistics IS 'Improves planner row estimations';
COMMENT ON SUBSCRIPTION alltables IS 'Subscription for all operations on all tables';
COMMENT ON TABLE my_schema.my_table IS 'Employee Information';
COMMENT ON TABLESPACE my_tablespace IS 'Tablespace for indexes';
COMMENT ON TEXT SEARCH CONFIGURATION my_config IS 'Special word filtering';
COMMENT ON TEXT SEARCH DICTIONARY swedish IS 'Snowball stemmer for Swedish language';
COMMENT ON TEXT SEARCH PARSER my_parser IS 'Splits text into words';
COMMENT ON TEXT SEARCH TEMPLATE snowball IS 'Snowball stemmer';
COMMENT ON TRANSFORM FOR hstore LANGUAGE plpython3u IS 'Transform between hstore and Python dict';
COMMENT ON TRIGGER my_trigger ON my_table IS 'Used for RI';
COMMENT ON TYPE complex IS 'Complex number data type';
COMMENT ON VIEW my_view IS 'View of departmental costs';

COMMIT PREPARED 'foobar';
COPY country TO STDOUT (DELIMITER '|');
COPY country FROM '/usr1/proj/bray/sql/country_data';
COPY (SELECT * FROM country WHERE country_name LIKE 'A%') TO '/usr1/proj/bray/sql/a_list_countries.copy';
COPY country TO PROGRAM 'gzip > /usr1/proj/bray/sql/country_data.gz';

CREATE ACCESS METHOD heptree TYPE INDEX HANDLER heptree_handler;
CREATE AGGREGATE avg (float8)
(
    sfunc = float8_accum,
    stype = float8[],
    finalfunc = float8_avg,
    initcond = '{0,0,0}'
);
CREATE AGGREGATE sum (complex)
(
    sfunc = complex_add,
    stype = complex,
    initcond = '(0,0)'
);
CREATE AGGREGATE sum (complex)
(
    sfunc = complex_add,
    stype = complex,
    initcond = '(0,0)',
    msfunc = complex_add,
    minvfunc = complex_sub,
    mstype = complex,
    minitcond = '(0,0)'
);
CREATE AGGREGATE unsafe_sum (float8)
(
    stype = float8,
    sfunc = float8pl,
    mstype = float8,
    msfunc = float8pl,
    minvfunc = float8mi
);
CREATE AGGREGATE array_accum (anycompatible)
(
    sfunc = array_append,
    stype = anycompatiblearray,
    initcond = '{}'
);
CREATE AGGREGATE array_agg (anynonarray)
(
    sfunc = array_agg_transfn,
    stype = internal,
    finalfunc = array_agg_finalfn,
    finalfunc_extra
);
CREATE AGGREGATE percentile_disc (float8 ORDER BY anyelement)
(
    sfunc = ordered_set_transition,
    stype = internal,
    finalfunc = percentile_disc_final,
    finalfunc_extra
);
CREATE CAST (bigint AS int4) WITH FUNCTION int4(bigint) AS ASSIGNMENT;
CREATE COLLATION french (locale = 'fr_FR.utf8');
CREATE COLLATION german_phonebook (provider = icu, locale = 'de-u-co-phonebk');
CREATE COLLATION custom (provider = icu, locale = 'und', rules = '&V << w <<< W');
CREATE COLLATION german FROM "de_DE";
CREATE CONVERSION myconv FOR 'UTF8' TO 'LATIN1' FROM myfunc;
CREATE DATABASE lusiadas;
CREATE DATABASE music2
    LOCALE 'sv_SE.iso885915'
    ENCODING LATIN9
    TEMPLATE template0;
CREATE DATABASE music
    LOCALE 'sv_SE.utf8'
    TEMPLATE template0;
CREATE DATABASE sales OWNER salesapp TABLESPACE salesspace;
CREATE DOMAIN us_postal_code AS TEXT
    CHECK(
        VALUE ~ '^\d{5}$'
    OR VALUE ~ '^\d{5}-\d{4}$'
    );
CREATE TABLE us_snail_addy (
                               address_id SERIAL PRIMARY KEY,
                               street1 TEXT NOT NULL,
                               street2 TEXT,
                               street3 TEXT,
                               city TEXT NOT NULL,
                               postal us_postal_code NOT NULL
);
CREATE EVENT TRIGGER abort_ddl ON ddl_command_start
   EXECUTE FUNCTION abort_any_command();
CREATE EXTENSION hstore SCHEMA addons;
CREATE FOREIGN DATA WRAPPER dummy;
CREATE FOREIGN DATA WRAPPER file HANDLER file_fdw_handler;
CREATE FOREIGN DATA WRAPPER mywrapper
    OPTIONS (debug 'true');
CREATE FOREIGN TABLE films (
    code        char(5) NOT NULL,
    title       varchar(40) NOT NULL,
    did         integer NOT NULL,
    date_prod   date,
    kind        varchar(10),
    len         interval hour to minute
)
SERVER film_server;
CREATE FUNCTION add(integer, integer) RETURNS integer
AS 'select $1 + $2;'
    LANGUAGE SQL
    IMMUTABLE
    RETURNS NULL ON NULL INPUT;
CREATE FUNCTION add(a integer, b integer) RETURNS integer
    LANGUAGE SQL
    IMMUTABLE
    RETURNS NULL ON NULL INPUT
    RETURN a + b;
CREATE OR REPLACE FUNCTION increment(i integer) RETURNS integer AS $$
BEGIN
RETURN i + 1;
END;
$$ LANGUAGE plpgsql;
CREATE FUNCTION dup(in int, out f1 int, out f2 text)
    AS $$ SELECT $1, CAST($1 AS text) || ' is text' $$
              LANGUAGE SQL;
CREATE TYPE dup_result AS (f1 int, f2 text);

CREATE FUNCTION dup(int) RETURNS dup_result
AS $$ SELECT $1, CAST($1 AS text) || ' is text' $$
          LANGUAGE SQL;

SELECT * FROM dup(42);
CREATE FUNCTION dup(int) RETURNS TABLE(f1 int, f2 text)
    AS $$ SELECT $1, CAST($1 AS text) || ' is text' $$
              LANGUAGE SQL;
CREATE FUNCTION check_password(uname TEXT, pass TEXT)
    RETURNS BOOLEAN AS $$
DECLARE passed BOOLEAN;
BEGIN
SELECT  (pwd = $2) INTO passed
FROM    pwds
WHERE   username = $1;

RETURN passed;
END;
$$  LANGUAGE plpgsql
    SECURITY DEFINER
    -- Set a secure search_path: trusted schema(s), then 'pg_temp'.
    SET search_path = admin, pg_temp;
CREATE UNIQUE INDEX title_idx ON films (title);
CREATE UNIQUE INDEX title_idx ON films (title) INCLUDE (director, rating);
CREATE INDEX title_idx ON films (title) WITH (deduplicate_items = off);
CREATE INDEX ON films ((lower(title)));
CREATE INDEX title_idx_german ON films (title COLLATE "de_DE");
CREATE INDEX title_idx_nulls_low ON films (title NULLS FIRST);
CREATE UNIQUE INDEX title_idx ON films (title) WITH (fillfactor = 70);
CREATE INDEX gin_idx ON documents_table USING GIN (locations) WITH (fastupdate = off);
CREATE INDEX code_idx ON films (code) TABLESPACE indexspace;
CREATE INDEX pointloc
    ON points USING gist (box(location,location));
SELECT * FROM points
WHERE box(location,location) && '(0,0),(1,1)'::box;
CREATE INDEX CONCURRENTLY sales_quantity_index ON sales_table (quantity);
CREATE FUNCTION plsample_call_handler() RETURNS language_handler
AS '$libdir/plsample'
    LANGUAGE C;
CREATE LANGUAGE plsample
    HANDLER plsample_call_handler;
CREATE EXTENSION plsample;
CREATE OPERATOR === (
    LEFTARG = box,
    RIGHTARG = box,
    FUNCTION = area_equal_function,
    COMMUTATOR = ===,
    NEGATOR = !==,
    RESTRICT = area_restriction_function,
    JOIN = area_join_function,
    HASHES, MERGES
);
CREATE POLICY user_policy ON users
    USING (user_name = current_user);
CREATE TABLE accounts (manager text, company text, contact_email text);

ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;

CREATE POLICY account_managers ON accounts TO managers
    USING (manager = current_user);
CREATE POLICY user_sel_policy ON users
    FOR SELECT
                          USING (true);
CREATE POLICY user_mod_policy ON users
    USING (user_name = current_user);
-- Simple passwd-file based example
CREATE TABLE passwd (
                        user_name             text UNIQUE NOT NULL,
                        pwhash                text,
                        uid                   int  PRIMARY KEY,
                        gid                   int  NOT NULL,
                        real_name             text NOT NULL,
                        home_phone            text,
                        extra_info            text,
                        home_dir              text NOT NULL,
                        shell                 text NOT NULL
);

CREATE ROLE admin;  -- Administrator
CREATE ROLE bob;    -- Normal user
CREATE ROLE alice;  -- Normal user

-- Populate the table
INSERT INTO passwd VALUES
    ('admin','xxx',0,0,'Admin','************',null,'/root','/bin/dash');
INSERT INTO passwd VALUES
    ('bob','xxx',1,1,'Bob','************',null,'/home/<USER>','/bin/zsh');
INSERT INTO passwd VALUES
    ('alice','xxx',2,1,'Alice','************',null,'/home/<USER>','/bin/zsh');

-- Be sure to enable row-level security on the table
ALTER TABLE passwd ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Administrator can see all rows and add any rows
CREATE POLICY admin_all ON passwd TO admin USING (true) WITH CHECK (true);
-- Normal users can view all rows
CREATE POLICY all_view ON passwd FOR SELECT USING (true);
-- Normal users can update their own records, but
-- limit which shells a normal user is allowed to set
CREATE POLICY user_mod ON passwd FOR UPDATE
                                         USING (current_user = user_name)
                                     WITH CHECK (
                                         current_user = user_name AND
                                         shell IN ('/bin/bash','/bin/sh','/bin/dash','/bin/zsh','/bin/tcsh')
                                         );

-- Allow admin all normal rights
GRANT SELECT, INSERT, UPDATE, DELETE ON passwd TO admin;
-- Users only get select access on public columns
GRANT SELECT
    (user_name, uid, gid, real_name, home_phone, extra_info, home_dir, shell)
    ON passwd TO public;
-- Allow users to update certain columns
GRANT UPDATE
    (pwhash, real_name, home_phone, extra_info, shell)
    ON passwd TO public;
CREATE PROCEDURE insert_data(a integer, b integer)
    LANGUAGE SQL
    AS $$
INSERT INTO tbl VALUES (a);
INSERT INTO tbl VALUES (b);
$$;
CREATE PUBLICATION mypublication FOR TABLE users, departments;
CREATE PUBLICATION active_departments FOR TABLE departments WHERE (active IS TRUE);
CREATE PUBLICATION alltables FOR ALL TABLES;
CREATE PUBLICATION insert_only FOR TABLE mydata
    WITH (publish = 'insert');
CREATE PUBLICATION production_publication FOR TABLE users, departments, TABLES IN SCHEMA production;
CREATE PUBLICATION sales_publication FOR TABLES IN SCHEMA marketing, sales;
CREATE PUBLICATION users_filtered FOR TABLE users (user_id, firstname);
CREATE ROLE jonathan LOGIN;
CREATE USER davide WITH PASSWORD 'jw8s0F4';
CREATE ROLE admin WITH CREATEDB CREATEROLE;
CREATE ROLE miriam WITH LOGIN PASSWORD 'jw8s0F4' VALID UNTIL '2005-01-01';

CREATE RULE "_RETURN" AS
    ON SELECT TO t1
                  DO INSTEAD
SELECT * FROM t2;
CREATE RULE notify_me AS ON UPDATE TO mytable DO ALSO NOTIFY mytable;

UPDATE mytable SET name = 'foo' WHERE id = 42;
CREATE SCHEMA myschema;
CREATE SCHEMA IF NOT EXISTS test AUTHORIZATION joe;
CREATE SCHEMA hollywood
    CREATE TABLE films (title text, release date, awards text[])
    CREATE VIEW winners AS
    SELECT title, release FROM films WHERE awards IS NOT NULL;
CREATE SEQUENCE serial START 101;
CREATE SERVER myserver FOREIGN DATA WRAPPER postgres_fdw OPTIONS (host 'foo', dbname 'foodb', port '5432');

CREATE TABLE t1 (
                    a   int,
                    b   int
);

INSERT INTO t1 SELECT i/100, i/500
FROM generate_series(1,1000000) s(i);

ANALYZE t1;

-- the number of matching rows will be drastically underestimated:
EXPLAIN ANALYZE SELECT * FROM t1 WHERE (a = 1) AND (b = 0);

CREATE STATISTICS s1 (dependencies) ON a, b FROM t1;

ANALYZE t1;

-- now the row count estimate is more accurate:
EXPLAIN ANALYZE SELECT * FROM t1 WHERE (a = 1) AND (b = 0);

CREATE TABLE t2 (
                    a   int,
                    b   int
);

INSERT INTO t2 SELECT mod(i,100), mod(i,100) FROM generate_series(1,1000000) s(i);

CREATE STATISTICS s2 (mcv) ON a, b FROM t2;

ANALYZE t2;

-- valid combination (found in MCV)
EXPLAIN ANALYZE SELECT * FROM t2 WHERE (a = 1) AND (b = 1);

-- invalid combination (not found in MCV)
EXPLAIN ANALYZE SELECT * FROM t2 WHERE (a = 1) AND (b = 2);


CREATE TABLE t3 (
                    a   timestamp
);

INSERT INTO t3 SELECT i FROM generate_series('2020-01-01'::timestamp,'2020-12-31'::timestamp, '1 minute'::interval) s(i);

ANALYZE t3;

-- the number of matching rows will be drastically underestimated:
EXPLAIN ANALYZE SELECT * FROM t3
                WHERE date_trunc('month', a) = '2020-01-01'::timestamp;

EXPLAIN ANALYZE SELECT * FROM t3
                WHERE date_trunc('day', a) BETWEEN '2020-01-01'::timestamp
                  AND '2020-06-30'::timestamp;

EXPLAIN ANALYZE SELECT date_trunc('month', a), date_trunc('day', a)
                FROM t3 GROUP BY 1, 2;

-- build ndistinct statistics on the pair of expressions (per-expression
-- statistics are built automatically)
CREATE STATISTICS s3 (ndistinct) ON date_trunc('month', a), date_trunc('day', a) FROM t3;

ANALYZE t3;

-- now the row count estimates are more accurate:
EXPLAIN ANALYZE SELECT * FROM t3
                WHERE date_trunc('month', a) = '2020-01-01'::timestamp;

EXPLAIN ANALYZE SELECT * FROM t3
                WHERE date_trunc('day', a) BETWEEN '2020-01-01'::timestamp
                  AND '2020-06-30'::timestamp;

EXPLAIN ANALYZE SELECT date_trunc('month', a), date_trunc('day', a)
                FROM t3 GROUP BY 1, 2;

CREATE SUBSCRIPTION mysub
         CONNECTION 'host=************ port=5432 user=foo dbname=foodb'
        PUBLICATION mypublication, insert_only;

CREATE SUBSCRIPTION mysub
         CONNECTION 'host=************ port=5432 user=foo dbname=foodb'
        PUBLICATION insert_only
               WITH (enabled = false);
CREATE TABLE array_int (
                           vector  int[][]
);
CREATE TABLE films (
                       code        char(5),
                       title       varchar(40),
                       did         integer,
                       date_prod   date,
                       kind        varchar(10),
                       len         interval hour to minute,
                       CONSTRAINT production UNIQUE(date_prod)
);
CREATE TABLE distributors (
                              did     integer CHECK (did > 100),
                              name    varchar(40)
);
CREATE TABLE distributors (
                              did     integer,
                              name    varchar(40),
                              CONSTRAINT con1 CHECK (did > 100 AND name <> '')
);
CREATE TABLE films (
                       code        char(5),
                       title       varchar(40),
                       did         integer,
                       date_prod   date,
                       kind        varchar(10),
                       len         interval hour to minute,
                       CONSTRAINT code_title PRIMARY KEY(code,title)
);
CREATE TABLE distributors (
                              did     integer,
                              name    varchar(40),
                              PRIMARY KEY(did)
);
CREATE TABLE distributors (
                              did     integer PRIMARY KEY,
                              name    varchar(40)
);
CREATE TABLE distributors (
                              name      varchar(40) DEFAULT 'Luso Films',
                              did       integer DEFAULT nextval('distributors_serial'),
                              modtime   timestamp DEFAULT current_timestamp
);
CREATE TABLE distributors (
                              did     integer CONSTRAINT no_null NOT NULL,
                              name    varchar(40) NOT NULL
);
CREATE TABLE distributors (
                              did     integer,
                              name    varchar(40) UNIQUE
);
CREATE TABLE distributors (
                              did     integer,
                              name    varchar(40),
                              UNIQUE(name)
);

CREATE TABLE distributors (
                              did     integer,
                              name    varchar(40),
                              UNIQUE(name) WITH (fillfactor=70)
)
    WITH (fillfactor=70);
CREATE TABLE circles (
                         c circle,
                         EXCLUDE USING gist (c WITH &&)
);
CREATE TABLE cinemas (
                         id serial,
                         name text,
                         location text
) TABLESPACE diskvol1;
CREATE TYPE employee_type AS (name text, salary numeric);

CREATE TABLE employees OF employee_type (
                                            PRIMARY KEY (name),
                                            salary WITH OPTIONS DEFAULT 1000
);
CREATE TABLE measurement (
                             logdate         date not null,
                             peaktemp        int,
                             unitsales       int
) PARTITION BY RANGE (logdate);
CREATE TABLE measurement_year_month (
                                        logdate         date not null,
                                        peaktemp        int,
                                        unitsales       int
) PARTITION BY RANGE (EXTRACT(YEAR FROM logdate), EXTRACT(MONTH FROM logdate));
CREATE TABLE cities (
                        city_id      bigserial not null,
                        name         text not null,
                        population   bigint
) PARTITION BY LIST (left(lower(name), 1));

CREATE TABLE orders (
                        order_id     bigint not null,
                        cust_id      bigint not null,
                        status       text
) PARTITION BY HASH (order_id);
CREATE TABLE measurement_y2016m07
    PARTITION OF measurement (
                                 unitsales DEFAULT 0
) FOR VALUES FROM ('2016-07-01') TO ('2016-08-01');

CREATE TABLE measurement_ym_older
    PARTITION OF measurement_year_month
    FOR VALUES FROM (MINVALUE, MINVALUE) TO (2016, 11);

CREATE TABLE measurement_ym_y2016m11
    PARTITION OF measurement_year_month
    FOR VALUES FROM (2016, 11) TO (2016, 12);

CREATE TABLE measurement_ym_y2016m12
    PARTITION OF measurement_year_month
    FOR VALUES FROM (2016, 12) TO (2017, 01);

CREATE TABLE measurement_ym_y2017m01
    PARTITION OF measurement_year_month
    FOR VALUES FROM (2017, 01) TO (2017, 02);
CREATE TABLE cities_ab
    PARTITION OF cities (
                            CONSTRAINT city_id_nonzero CHECK (city_id != 0)
    ) FOR VALUES IN ('a', 'b');
CREATE TABLE cities_ab
    PARTITION OF cities (
                            CONSTRAINT city_id_nonzero CHECK (city_id != 0)
    ) FOR VALUES IN ('a', 'b') PARTITION BY RANGE (population);

CREATE TABLE cities_ab_10000_to_100000
    PARTITION OF cities_ab FOR VALUES FROM (10000) TO (100000);
CREATE TABLE orders_p1 PARTITION OF orders
    FOR VALUES WITH (MODULUS 4, REMAINDER 0);
CREATE TABLE orders_p2 PARTITION OF orders
    FOR VALUES WITH (MODULUS 4, REMAINDER 1);
CREATE TABLE orders_p3 PARTITION OF orders
    FOR VALUES WITH (MODULUS 4, REMAINDER 2);
CREATE TABLE orders_p4 PARTITION OF orders
    FOR VALUES WITH (MODULUS 4, REMAINDER 3);
CREATE TABLE cities_partdef
    PARTITION OF cities DEFAULT;

CREATE TABLE films_recent AS
SELECT * FROM films WHERE date_prod >= '2002-01-01';
CREATE TABLE films2 AS
    TABLE films;
PREPARE recentfilms(date) AS
SELECT * FROM films WHERE date_prod > $1;
CREATE TABLESPACE dbspace LOCATION '/data/dbs';
CREATE TABLESPACE indexspace OWNER genevieve LOCATION '/data/indexes';
CREATE TEXT SEARCH DICTIONARY my_russian (
    template = snowball,
    language = russian,
    stopwords = myrussian
);
CREATE EXTENSION plpython3u;
CREATE TRANSFORM FOR hstore LANGUAGE plpython3u (
    FROM SQL WITH FUNCTION hstore_to_plpython(internal),
    TO SQL WITH FUNCTION plpython_to_hstore(internal)
);
CREATE TRIGGER check_update
    BEFORE UPDATE ON accounts
    FOR EACH ROW
    EXECUTE FUNCTION check_account_update();
CREATE TRIGGER check_update
    BEFORE UPDATE ON accounts
    FOR EACH ROW
    WHEN (OLD.balance IS DISTINCT FROM NEW.balance)
    EXECUTE FUNCTION check_account_update();
CREATE TRIGGER log_update
    AFTER UPDATE ON accounts
    FOR EACH ROW
    WHEN (OLD.* IS DISTINCT FROM NEW.*)
    EXECUTE FUNCTION log_account_update();
CREATE TRIGGER view_insert
    INSTEAD OF INSERT ON my_view
    FOR EACH ROW
    EXECUTE FUNCTION view_insert_row();
CREATE TRIGGER transfer_insert
    AFTER INSERT ON transfer
    REFERENCING NEW TABLE AS inserted
    FOR EACH STATEMENT
    EXECUTE FUNCTION check_transfer_balances_to_zero();
CREATE TRIGGER paired_items_update
    AFTER UPDATE ON paired_items
    REFERENCING NEW TABLE AS newtab OLD TABLE AS oldtab
    FOR EACH ROW
    EXECUTE FUNCTION check_matching_pairs();
CREATE TYPE compfoo AS (f1 int, f2 text);

CREATE FUNCTION getfoo() RETURNS SETOF compfoo AS $$
SELECT fooid, fooname FROM foo
                               $$ LANGUAGE SQL;
CREATE TYPE bug_status AS ENUM ('new', 'open', 'closed');

CREATE TABLE bug (
                     id serial,
                     description text,
                     status bug_status
);
CREATE TYPE float8_range AS RANGE (subtype = float8, subtype_diff = float8mi);

CREATE TYPE box;

CREATE FUNCTION my_box_in_function(cstring) RETURNS box AS ... ;
CREATE FUNCTION my_box_out_function(box) RETURNS cstring AS ... ;

CREATE TYPE box (
    INTERNALLENGTH = 16,
    INPUT = my_box_in_function,
    OUTPUT = my_box_out_function
);

CREATE TABLE myboxes (
                         id integer,
                         description box
);
CREATE TYPE box (
    INTERNALLENGTH = 16,
    INPUT = my_box_in_function,
    OUTPUT = my_box_out_function,
    ELEMENT = float4
);
CREATE TYPE bigobj (
    INPUT = lo_filein, OUTPUT = lo_fileout,
    INTERNALLENGTH = VARIABLE
);
CREATE TABLE big_objs (
                          id integer,
                          obj bigobj
);
CREATE USER MAPPING FOR bob SERVER foo OPTIONS (user 'bob', password 'secret');
CREATE VIEW comedies AS
SELECT *
FROM films
WHERE kind = 'Comedy';
CREATE VIEW universal_comedies AS
SELECT *
FROM comedies
WHERE classification = 'U'
        WITH LOCAL CHECK OPTION;
CREATE VIEW comedies AS
SELECT f.*,
       country_code_to_name(f.country_code) AS country,
       (SELECT avg(r.rating)
        FROM user_ratings r
        WHERE r.film_id = f.id) AS avg_rating
FROM films f
WHERE f.kind = 'Comedy';

CREATE RECURSIVE VIEW public.nums_1_100 (n) AS
VALUES (1)
UNION ALL
SELECT n+1 FROM nums_1_100 WHERE n < 100;
DELETE FROM films WHERE kind <> 'Musical';
DELETE FROM tasks WHERE status = 'DONE' RETURNING *;
DELETE FROM tasks WHERE CURRENT OF c_tasks;
WITH delete_batch AS (
    SELECT l.ctid FROM user_logs AS l
    WHERE l.status = 'archived'
    ORDER BY l.creation_date
    FOR UPDATE
    LIMIT 10000
)
DELETE FROM user_logs AS dl
    USING delete_batch AS del
WHERE dl.ctid = del.ctid;

DROP ACCESS METHOD heptree;
DROP AGGREGATE myrank(VARIADIC "any" ORDER BY VARIADIC "any");
DROP AGGREGATE myavg(integer);
DROP CAST (text AS int);
DROP COLLATION german;
DROP CONVERSION myname;

DROP DOMAIN box;
DROP EVENT TRIGGER snitch;
DROP EXTENSION hstore;
DROP FOREIGN DATA WRAPPER dbi;
DROP FOREIGN TABLE films, distributors;
DROP FUNCTION update_employee_salaries();
DROP FUNCTION update_employee_salaries;
DROP FUNCTION sqrt(integer), sqrt(bigint);
DROP FUNCTION sqrt(integer);
DROP INDEX title_idx;
DROP LANGUAGE plsample;
DROP MATERIALIZED VIEW order_summary;
DROP OPERATOR ~ (none, bit), ^ (integer, integer);
DROP OPERATOR ~ (none, bit);
DROP OPERATOR ^ (integer, integer);
DROP OPERATOR CLASS widget_ops USING btree;
DROP OPERATOR FAMILY float_ops USING btree;
REASSIGN OWNED BY doomed_role TO successor_role;
DROP OWNED BY doomed_role;
-- repeat the above commands in each database of the cluster
DROP ROLE doomed_role;
DROP POLICY p1 ON my_table;
DROP PROCEDURE do_db_maintenance;
DROP PROCEDURE do_db_maintenance(IN target_schema text, OUT results text);
DROP PROCEDURE do_db_maintenance(IN text, OUT text);
DROP PROCEDURE do_db_maintenance(IN text);
DROP PROCEDURE do_db_maintenance(text);
DROP PROCEDURE do_db_maintenance(text, text);  -- potentially ambiguous
DROP PUBLICATION mypublication;
DROP ROLE jonathan;
DROP ROUTINE foo(integer);
DROP RULE newrule ON mytable;
DROP SCHEMA mystuff CASCADE;
DROP SEQUENCE serial;
DROP SERVER IF EXISTS foo;
DROP STATISTICS IF EXISTS
    accounting.users_uid_creation,
    public.grants_user_role;
DROP SUBSCRIPTION mysub;
DROP TABLE films, distributors;
DROP TEXT SEARCH CONFIGURATION my_english;
DROP TEXT SEARCH DICTIONARY english;
DROP TEXT SEARCH PARSER my_parser;
DROP TEXT SEARCH TEMPLATE thesaurus;
DROP TRANSFORM FOR hstore LANGUAGE plpython3u;
DROP TRIGGER if_dist_exists ON films;
DROP TYPE box;
DROP USER MAPPING IF EXISTS FOR bob SERVER foo;
DROP VIEW kinds;
PREPARE fooplan (int, text, bool, numeric) AS
    INSERT INTO foo VALUES($1, $2, $3, $4);
EXECUTE fooplan(1, 'Hunter Valley', 't', 200.00);
PREPARE usrrptplan (int) AS
SELECT * FROM users u, logs l WHERE u.usrid=$1 AND u.usrid=l.usrid
                                AND l.date = $2;
EXECUTE usrrptplan(1, current_date);
EXPLAIN SELECT * FROM foo;
EXPLAIN (FORMAT YAML) SELECT * FROM foo WHERE i='4';
EXPLAIN (FORMAT JSON) SELECT * FROM foo;
EXPLAIN (COSTS FALSE) SELECT * FROM foo WHERE i = 4;
PREPARE query(int, int) AS SELECT sum(bar) FROM test
    WHERE id > $1 AND id < $2
    GROUP BY foo;
EXPLAIN SELECT sum(i) FROM foo WHERE i < 10;
EXPLAIN (GENERIC_PLAN)
SELECT sum(bar) FROM test
WHERE id > $1 AND id < $2
GROUP BY foo;
EXPLAIN (GENERIC_PLAN)
SELECT sum(bar) FROM test
WHERE id > $1::integer AND id < $2::integer
GROUP BY foo;

BEGIN WORK;

-- Set up a cursor:
DECLARE liahona SCROLL CURSOR FOR SELECT * FROM films;

-- Fetch the first 5 rows in the cursor liahona:
FETCH FORWARD 5 FROM liahona;

code  |          title          | did | date_prod  |   kind   |  len
-------+-------------------------+-----+------------+----------+-------
 BL101 | The Third Man           | 101 | 1949-12-23 | Drama    | 01:44
 BL102 | The African Queen       | 101 | 1951-08-11 | Romantic | 01:43
 JL201 | Une Femme est une Femme | 102 | 1961-03-12 | Romantic | 01:25
 P_301 | Vertigo                 | 103 | 1958-11-14 | Action   | 02:08
 P_302 | Becket                  | 103 | 1964-02-03 | Drama    | 02:28

-- Fetch the previous row:
FETCH PRIOR FROM liahona;

 code  |  title  | did | date_prod  |  kind  |  len
-------+---------+-----+------------+--------+-------
 P_301 | Vertigo | 103 | 1958-11-14 | Action | 02:08

-- Close the cursor and end the transaction:
CLOSE liahona;
COMMIT WORK;

GRANT ALL PRIVILEGES ON kinds TO manuel;
GRANT admins TO joe;

GRANT INSERT ON films TO PUBLIC;

IMPORT FOREIGN SCHEMA foreign_films
    FROM SERVER film_server INTO films;
IMPORT FOREIGN SCHEMA foreign_films LIMIT TO (actors, directors)
    FROM SERVER film_server INTO films;

INSERT INTO films VALUES
    ('UA502', 'Bananas', 105, '1971-07-13', 'Comedy', '82 minutes');
INSERT INTO films (code, title, did, date_prod, kind)
VALUES ('T_601', 'Yojimbo', 106, '1961-06-16', 'Drama');
INSERT INTO films VALUES
    ('UA502', 'Bananas', 105, DEFAULT, 'Comedy', '82 minutes');
INSERT INTO films (code, title, did, date_prod, kind)
VALUES ('T_601', 'Yojimbo', 106, DEFAULT, 'Drama');
INSERT INTO films DEFAULT VALUES;
INSERT INTO films (code, title, did, date_prod, kind) VALUES
                                                          ('B6717', 'Tampopo', 110, '1985-02-10', 'Comedy'),
                                                          ('HG120', 'The Dinner Game', 140, DEFAULT, 'Comedy');
INSERT INTO films SELECT * FROM tmp_films WHERE date_prod < '2004-05-07';

INSERT INTO tictactoe (game, board[1:3][1:3])
VALUES (1, '{{" "," "," "},{" "," "," "},{" "," "," "}}');
INSERT INTO tictactoe (game, board)
VALUES (2, '{{X," "," "},{" ",O," "},{" ",X," "}}');
INSERT INTO distributors (did, dname) VALUES (DEFAULT, 'XYZ Widgets')
    RETURNING did;
WITH upd AS (
UPDATE employees SET sales_count = sales_count + 1 WHERE id =
                                                         (SELECT sales_person FROM accounts WHERE name = 'Acme Corporation')
    RETURNING *
)
INSERT INTO employees_log SELECT *, current_timestamp FROM upd;
INSERT INTO distributors (did, dname)
VALUES (5, 'Gizmo Transglobal'), (6, 'Associated Computing, Inc')
    ON CONFLICT (did) DO UPDATE SET dname = EXCLUDED.dname;
INSERT INTO distributors (did, dname) VALUES (7, 'Redline GmbH')
    ON CONFLICT (did) DO NOTHING;
-- Don't update existing distributors based in a certain ZIP code
INSERT INTO distributors AS d (did, dname) VALUES (8, 'Anvil Distribution')
ON CONFLICT (did) DO UPDATE
                         SET dname = EXCLUDED.dname || ' (formerly ' || d.dname || ')'
                     WHERE d.zipcode <> '21201';

-- Name a constraint directly in the statement (uses associated
-- index to arbitrate taking the DO NOTHING action)
INSERT INTO distributors (did, dname) VALUES (9, 'Antwerp Design')
    ON CONFLICT ON CONSTRAINT distributors_pkey DO NOTHING;
-- This statement could infer a partial unique index on "did"
-- with a predicate of "WHERE is_active", but it could also
-- just use a regular unique constraint on "did"
INSERT INTO distributors (did, dname) VALUES (10, 'Conrad International')
    ON CONFLICT (did) WHERE is_active DO NOTHING;
LISTEN virtual;
NOTIFY virtual;

BEGIN WORK;
LOCK TABLE films IN SHARE MODE;
SELECT id FROM films
WHERE name = 'Star Wars: Episode I - The Phantom Menace';
-- Do ROLLBACK if record was not returned
INSERT INTO films_user_comments VALUES
    (_id_, 'GREAT! I was waiting for it for so long!');
COMMIT WORK;

MERGE INTO customer_account ca
    USING recent_transactions t
    ON t.customer_id = ca.customer_id
    WHEN MATCHED THEN
        UPDATE SET balance = balance + transaction_value
    WHEN NOT MATCHED THEN
        INSERT (customer_id, balance)
            VALUES (t.customer_id, t.transaction_value);
MERGE INTO customer_account ca
    USING (SELECT customer_id, transaction_value FROM recent_transactions) AS t
    ON t.customer_id = ca.customer_id
    WHEN MATCHED THEN
        UPDATE SET balance = balance + transaction_value
    WHEN NOT MATCHED THEN
        INSERT (customer_id, balance)
            VALUES (t.customer_id, t.transaction_value);

MERGE INTO wines w
    USING wine_stock_changes s
    ON s.winename = w.winename
    WHEN NOT MATCHED AND s.stock_delta > 0 THEN
        INSERT VALUES(s.winename, s.stock_delta)
    WHEN MATCHED AND w.stock + s.stock_delta > 0 THEN
        UPDATE SET stock = w.stock + s.stock_delta
    WHEN MATCHED THEN
        DELETE
    RETURNING merge_action(), w.*;
MERGE INTO wines w
    USING new_wine_list s
    ON s.winename = w.winename
    WHEN NOT MATCHED BY TARGET THEN
    INSERT VALUES(s.winename, s.stock)
    WHEN MATCHED AND w.stock != s.stock THEN
UPDATE SET stock = s.stock
    WHEN NOT MATCHED BY SOURCE THEN
DELETE;


BEGIN WORK;
DECLARE liahona CURSOR FOR SELECT * FROM films;

-- Skip the first 5 rows:
MOVE FORWARD 5 IN liahona;
MOVE 5

-- Fetch the 6th row from the cursor liahona:
FETCH 1 FROM liahona;
 code  | title  | did | date_prod  |  kind  |  len
-------+--------+-----+------------+--------+-------
 P_303 | 48 Hrs | 103 | 1982-10-22 | Action | 01:37
(1 row)

-- Close the cursor liahona and end the transaction:
CLOSE liahona;
COMMIT WORK;

PREPARE fooplan (int, text, bool, numeric) AS
    INSERT INTO foo VALUES($1, $2, $3, $4);
EXECUTE fooplan(1, 'Hunter Valley', 't', 200.00);

REFRESH MATERIALIZED VIEW order_summary;
REFRESH MATERIALIZED VIEW annual_statistics_basis WITH NO DATA;
REINDEX TABLE CONCURRENTLY my_broken_table;

ROLLBACK;
ROLLBACK TO SAVEPOINT sp1;

RESET timezone;

REVOKE INSERT ON films FROM PUBLIC;
REVOKE ALL PRIVILEGES ON kinds FROM manuel;
REVOKE admins FROM joe;

ROLLBACK PREPARED 'foobar';

BEGIN;
INSERT INTO table1 VALUES (3);
SAVEPOINT my_savepoint;
INSERT INTO table1 VALUES (4);
RELEASE SAVEPOINT my_savepoint;
COMMIT;

BEGIN;
INSERT INTO table1 VALUES (1);
SAVEPOINT my_savepoint;
INSERT INTO table1 VALUES (2);
SAVEPOINT my_savepoint;
INSERT INTO table1 VALUES (3);

-- rollback to the second savepoint
ROLLBACK TO SAVEPOINT my_savepoint;
SELECT * FROM table1;               -- shows rows 1 and 2

-- release the second savepoint
RELEASE SAVEPOINT my_savepoint;

-- rollback to the first savepoint
ROLLBACK TO SAVEPOINT my_savepoint;
SELECT * FROM table1;               -- shows only row 1
COMMIT;

SECURITY LABEL FOR selinux ON TABLE mytable IS 'system_u:object_r:sepgsql_table_t:s0';
SECURITY LABEL FOR selinux ON TABLE mytable IS NULL;

TRUNCATE bigtable, fattable;
TRUNCATE bigtable, fattable RESTART IDENTITY;
TRUNCATE othertable CASCADE;

UPDATE weather SET temp_lo = temp_lo+1, temp_hi = temp_lo+15, prcp = DEFAULT
WHERE city = 'San Francisco' AND date = '2003-07-03';
UPDATE weather SET temp_lo = temp_lo+1, temp_hi = temp_lo+15, prcp = DEFAULT
WHERE city = 'San Francisco' AND date = '2003-07-03'
    RETURNING temp_lo, temp_hi, prcp;
UPDATE weather SET (temp_lo, temp_hi, prcp) = (temp_lo+1, temp_lo+15, DEFAULT)
WHERE city = 'San Francisco' AND date = '2003-07-03';
UPDATE employees SET sales_count = sales_count + 1 FROM accounts
WHERE accounts.name = 'Acme Corporation'
  AND employees.id = accounts.sales_person;
UPDATE employees SET sales_count = sales_count + 1 WHERE id =
                                                         (SELECT sales_person FROM accounts WHERE name = 'Acme Corporation');
UPDATE accounts SET (contact_first_name, contact_last_name) =
                        (SELECT first_name, last_name FROM employees
                         WHERE employees.id = accounts.sales_person);
UPDATE accounts SET contact_first_name = first_name,
                    contact_last_name = last_name
    FROM employees WHERE employees.id = accounts.sales_person;
WITH exceeded_max_retries AS (
    SELECT w.ctid FROM work_item AS w
    WHERE w.status = 'active' AND w.num_retries > 10
    ORDER BY w.retry_timestamp
    FOR UPDATE
    LIMIT 5000
)
UPDATE work_item SET status = 'failed'
    FROM exceeded_max_retries AS emr
WHERE work_item.ctid = emr.ctid;

VACUUM (VERBOSE, ANALYZE) onek;

ALTER AGGREGATE mypercentile(float8, integer) SET SCHEMA myschema;
ALTER AGGREGATE mypercentile(float8 ORDER BY integer) SET SCHEMA myschema;
ALTER AGGREGATE myavg(integer) OWNER TO joe;
ALTER AGGREGATE myavg(integer) RENAME TO my_average;