parser grammar HBaseParser;

options
{
    tokenVocab = HBaseLexer;
}

// starting rule
statement
    : methodStatement
    ;

methodStatement
    : simpleStatement
    | listNamespaceTablesStatement
    | listStatement
    | isEnabledStatement
    | existsStatement
    | disableStatement
    | enableStatement
    | dropStatement
    | truncateStatement
    | truncatePreserveStatement
    | createStatement
    | describeStatement
    | alterStatement
    | snapshotStatement
    | cloneSnapshotStatement
    | deleteSnapshotStatement
    | putStatement
    | deleteAllStatement
    | scanStatement
    ;

simpleStatement
    : VERSION | LIS_NAMESPACE | LIST
    ;
listNamespaceTablesStatement
    : LIST_NAMESPACE_TABLES fullTableName
    ;
listStatement
    : LIST fullTableName
    ;
isEnabledStatement
    : IS_ENABLED fullTableName
    ;
existsStatement
    : EXISTS fullTableName
    ;
disableStatement
    : DISABLE fullTableName
    ;
enableStatement
    : ENABLE fullTableName
    ;
dropStatement
    : DROP fullTableName
    ;
truncateStatement
    : TRUNCATE fullTableName
    ;
truncatePreserveStatement
    : TRUNCATE_PRESERVE fullTableName
    ;
createStatement
    : CREATE fullTableName COMMA hJsonObject (COMMA hJsonObject)*
    ;
describeStatement
    : DESCRIBE fullTableName
    ;
alterStatement
    : ALTER fullTableName COMMA (hJsonObject (COMMA hJsonObject)* | deleteColumnFamily)
    ;
deleteColumnFamily
    : DELETE EQ_ARROW hJsonValue
    ;
snapshotStatement
    : SNAPSHOT fullTableName COMMA snapshotName
    ;
snapshotName
    : fullTableName
    ;
cloneSnapshotStatement
    : CLONE_SNAPSHOT snapshotName COMMA fullTableName
    ;
deleteSnapshotStatement
    : DELETE_SNAPSHOT snapshotName
    ;
putStatement
    : PUT fullTableName COMMA rowKey COMMA familyColumnName COMMA columnValue (COMMA timestamp)?
    ;
rowKey
    : STRING_LITERAL
    ;
familyColumnName
    : STRING_LITERAL
    ;
columnValue
    : STRING_LITERAL
    ;
timestamp
    : regularId
    ;
deleteAllStatement
    : DELETEALL fullTableName COMMA rowKey (COMMA familyColumnName)? (COMMA timestamp)?
    ;
scanStatement
    : SCAN fullTableName (COMMA '{' filter '}')?
    ;
filter
    : FILTER EQ_ARROW DQUOTA_STRING
    ;


fullTableName
//    : QUOTE_SINGLE (namespace COLON)? tableName QUOTE_SINGLE
    : STRING_LITERAL
    ;
namespace
    : regularId
    ;
tableName
    : regularId
    ;
regularId
    : REGULAR_ID | DECIMAL | INTEGER
    | VERSION
    | LIS_NAMESPACE
    | LIST
    | EXISTS
    | DISABLE
    | ENABLE
    | DROP
    | TRUNCATE
    | TRUNCATE_PRESERVE
    | CREATE
    | DESCRIBE
    | ALTER
    | SNAPSHOT
    | CLONE_SNAPSHOT
    | DELETE_SNAPSHOT
    | PUT
    | DELETEALL
    | SCAN
    | NAME_
    | VERSIONS_
    | EVICT_BLOCKS_ON_CLOSE
    | NEW_VERSION_BEHAVIOR
    | KEEP_DELETED_CELLS
    | CACHE_DATA_ON_WRITE
    | DATA_BLOCK_ENCODING
    | TTL
    | MIN_VERSIONS
    | REPLICATION_SCOPE
    | BLOOMFILTER
    | CACHE_INDEX_ON_WRITE
    | IN_MEMORY
    | CACHE_BLOOMS_ON_WRITE
    | PREFETCH_BLOCKS_ON_OPEN
    | COMPRESSION
    | BLOCKCACHE
    | BLOCKSIZE
    | FILTER
    ;

hJsonObject
    : '{' hJsonPair (COMMA hJsonPair)* '}'
    ;
hJsonPair
    : hJsonKey EQ_ARROW hJsonValue
    ;
hJsonKey
    : NAME_
    | VERSIONS_
    | EVICT_BLOCKS_ON_CLOSE
    | NEW_VERSION_BEHAVIOR
    | KEEP_DELETED_CELLS
    | CACHE_DATA_ON_WRITE
    | DATA_BLOCK_ENCODING
    | TTL
    | MIN_VERSIONS
    | REPLICATION_SCOPE
    | BLOOMFILTER
    | CACHE_INDEX_ON_WRITE
    | IN_MEMORY
    | CACHE_BLOOMS_ON_WRITE
    | PREFETCH_BLOCKS_ON_OPEN
    | COMPRESSION
    | BLOCKCACHE
    | BLOCKSIZE
    ;
hJsonValue
    : STRING_LITERAL
    ;
