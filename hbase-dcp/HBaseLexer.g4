lexer grammar HBaseLexer;

options {
    caseInsensitive = true;
}

VERSION                         : 'version';
LIS_NAMESPACE                   : 'lis_namespace';
LIST_NAMESPACE_TABLES           : 'list_namespace_tables';
LIST                            : 'list';
IS_ENABLED                      : 'is_enabled';
EXISTS                          : 'exists';
DISABLE                         : 'disable';
ENABLE                          : 'enable';
DROP                            : 'drop';
TRUNCATE                        : 'truncate';
TRUNCATE_PRESERVE               : 'truncate_preserve';
CREATE                          : 'create';
DESCRIBE                        : 'describe';
ALTER                           : 'alter';
SNAPSHOT                        : 'snapshot';
CLONE_SNAPSHOT                  : 'clone_snapshot';
DELETE_SNAPSHOT                 : 'delete_snapshot';
PUT                             : 'put';
DELETEALL                       : 'deleteall';
SCAN                            : 'scan';

NAME_                           : 'name';
VERSIONS_                       : 'versions';
EVICT_BLOCKS_ON_CLOSE           : 'evict_blocks_on_close';
NEW_VERSION_BEHAVIOR            : 'new_version_behavior';
KEEP_DELETED_CELLS              : 'keep_deleted_cells';
CACHE_DATA_ON_WRITE             : 'cache_data_on_write';
DATA_BLOCK_ENCODING             : 'data_block_encoding';
TTL                             : 'ttl';
MIN_VERSIONS                    : 'min_versions';
REPLICATION_SCOPE               : 'replication_scope';
BLOOMFILTER                     : 'bloomfilter';
CACHE_INDEX_ON_WRITE            : 'cache_index_on_write';
IN_MEMORY                       : 'in_memory';
CACHE_BLOOMS_ON_WRITE           : 'cache_blooms_on_write';
PREFETCH_BLOCKS_ON_OPEN         : 'prefetch_blocks_on_open';
COMPRESSION                     : 'compression';
BLOCKCACHE                      : 'blockcache';
BLOCKSIZE                       : 'blocksize';
FILTER                          : 'filter';
DELETE                          : '\'delete\'';


AMPERSAND       : '&';
ASTERISK        : '*';
BAR             : '|';
COLON           : ':';
COMMA           : ',';
DOT             : '.';
EQUALS          : '=';
GREATER_THAN_OP : '>';
LEFT_BRACE      : '{';
LEFT_BRACKET    : '[';
LEFT_PAREN      : '(';
LESS_THAN_OP    : '<';
MINUS_SIGN      : '-';
PLUS_SIGN       : '+';
QUESTION        : '?';
RIGHT_BRACE     : '}';
RIGHT_BRACKET   : ']';
RIGHT_PAREN     : ')';
SEMICOLON       : ';';
SLASH           : '/';
QUOTE_SINGLE    : '\'';
EQ_ARROW        : '=>';


STRING_LITERAL                : '\'' (~'\'' | '\'\'' | '\\\'')* '\'';
DQUOTA_STRING                 : '"' ~'"'* '"';
INTEGER                       : Digit+;
DECIMAL                       : INTEGER '.' Digit+;
REGULAR_ID                    : ~([\\/?"<>{}|`',= \n\t\r]) ~([\\/?"<>{}|`',= \n\t\r])*;
SPACES                        : [ \t\r\n]+                          -> channel(HIDDEN);


fragment Digit                : [0-9];
fragment LETTER               : [A-Z];
fragment NEWLINE_EOF          : NEWLINE | EOF;
fragment NEWLINE              : '\r'? '\n';
